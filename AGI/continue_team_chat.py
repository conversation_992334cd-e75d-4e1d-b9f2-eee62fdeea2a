import requests
import os
from pathlib import Path

# Define all agents
AGENTS = {
    "Plannerbot-mistral": "mistral:latest",
    "Plannerbot-deepseek": "deepseek-llm:7b",
    "Coderbot-deepseek": "deepseek-coder:6.7b",
    "Coderbot-starcoder": "starcoder2:3b",
    "Coderbot-llama": "llama3.1:8b",
    "Coderbot-gemma": "gemma:latest",
    "Coderbot-wizard": "wizardcoder:33b"
}

CHAT_LOG = []

# Use Ollama API
def call_model(name, model, prompt):
    print(f"\n[{name}] Thinking...")
    response = requests.post(
        "http://localhost:11434/api/generate",
        json={"model": model, "prompt": prompt, "stream": False}
    )
    reply = response.json()["response"].strip()
    CHAT_LOG.append((name, reply))
    return reply

# Display conversation
def print_chat():
    os.system('clear')
    for name, message in CHAT_LOG:
        print(f"\n{name}:")
        print(f"{'-'*len(name)}")
        print(message)

# Run one round of responses from all agents
def agent_round(user_input=None):
    if user_input:
        CHAT_LOG.append(("User", user_input))

    current_context = "\n\n".join([f"{n}: {m}" for n, m in CHAT_LOG[-12:]])  # last 12 messages

    for name, model in AGENTS.items():
        prompt = f"""
You are {name}, part of a team of AI agents working on a project.

Here’s the latest conversation:
{current_context}

Your task is to respond with your thoughts, code suggestions, or questions based on what others said. Keep it short and focused. If you’re a coder, propose code or edits. If a planner, suggest structural or strategic improvements.
"""
        reply = call_model(name, model, prompt)

    print_chat()

# Main loop
if __name__ == "__main__":
    print("=== Team Agent Chat ===\n")
    while True:
        user_input = input("\nYou: ")
        agent_round(user_input)
