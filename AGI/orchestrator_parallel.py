import json, os, requests
from concurrent.futures import ThreadPoolExecutor

MODEL_MAP = {
    "Plannerbot-mistral": "mistral:latest",
    #"Plannerbot-deepseek": "deepseek-llm:7b",
    "Coderbot-deepseek": "deepseek-coder:6.7b",
    #"Coderbot-starcoder": "starcoder2:3b",
    #"Coderbot-llama": "llama3.1:8b",
    #"Coderbot-gemma": "gemma:latest",
    #"Coderbot-wizard": "wizardcoder:33b" deleted
}

def read_chat_log():
    with open("shared_context.json", "r") as f:
        return json.load(f)["chat_log"]

def write_chat_log(name, msg):
    chat = read_chat_log()
    chat.append({"name": name, "message": msg})
    with open("shared_context.json", "w") as f:
        json.dump({"chat_log": chat[-30:]}, f, indent=2)  # keep last 30

def get_prompt_for_model(model_name):
    with open(f"prompts/{model_name}.txt", "r") as f:
        return f.read()

def build_context(model_name):
    context = read_chat_log()
    context_text = "\n\n".join([f"{entry['name']}: {entry['message']}" for entry in context])
    user_prompt = get_prompt_for_model(model_name)
    return f"""
You are {model_name}. You're part of a team of AI agents. Read this conversation and respond:

{context_text}

Now, based on your unique skills, respond to the latest situation or tasks.

Your prompt: {user_prompt}
"""

def run_model(model_name, ollama_name):
    prompt = build_context(model_name)
    response = requests.post(
        "http://localhost:11434/api/generate",
        json={"model": ollama_name, "prompt": prompt, "stream": False}
    )
    reply = response.json()["response"]
    write_chat_log(model_name, reply.strip())
    print(f"\n[{model_name}] replied.")

def run_all_agents():
    with ThreadPoolExecutor() as executor:
        futures = []
        for name, model in MODEL_MAP.items():
            futures.append(executor.submit(run_model, name, model))
        for future in futures:
            future.result()

if __name__ == "__main__":
    print("=== Parallel Multi-Agent Runtime ===")
    while True:
        run_all_agents()
        os.system("clear")
        chat = read_chat_log()
        for entry in chat:
            print(f"\n{entry['name']}:\n{'-'*len(entry['name'])}\n{entry['message']}")
        input("\n[Enter] to run next round...")