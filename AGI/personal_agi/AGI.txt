personal_agi/
│
├── main.py                      # Entry point
├── config.py                   # Configs for models, memory, etc.
├── requirements.txt            # Python dependencies
│
├── input/
│   ├── mic_listener.py         # Uses Whisper to transcribe voice
│   ├── camera_capture.py       # Uses OpenCV to grab frames
│   └── keyboard_input.py       # CLI text input module
│
├── output/
│   ├── text_output.py          # Prints to terminal or GUI
│   ├── speech_output.py        # Uses pyttsx3 or Coqui TTS
│   └── visual_output.py        # Renders visual data (matplotlib/OpenCV)
│
├── memory/
│   ├── conscious_memory.py     # Short-term memory store
│   ├── long_term_memory.py     # SQLite or JSON memory DB
│   ├── live_logger.py          # Logs all inputs + thoughts
│   └── memory_filter.py        # Promotes/deletes from live to long-term
│
├── brain/
│   ├── language_model.py       # Wrapper around local LLM (GPT-J/Mistral)
│   ├── perception.py           # Combines vision/audio/text
│   ├── planner.py              # Breaks down complex goals
│   ├── reflector.py            # Self-checks and scores ideas
│   └── action_engine.py        # Executes code or replies
│
├── data/
│   ├── memory_log.sqlite       # Stored live memory
│   ├── knowledge_store.json    # Filtered long-term facts
│   └── embeddings/             # Vector DBs (FAISS or Chroma)
│
└── models/
    └── whisper/                # Whisper Tiny model files
    └── llama/                  # Mistral, GPT-J, etc.

#virtual environment
python -m venv personal_agi
source personal_agi/bin/activate

#install dependencies
pip install -r requirements.txt

#run python code
python main.py

#terminate virtual environment
deactivate