from input.keyboard_input import get_user_input
from memory.live_logger import log_input
from brain.language_model import generate_response
from output.text_output import speak, print_bot_info


print_bot_info()
while True:
    user_input  = get_user_input()
    if user_input.lower() in ["exit", "quit"]:
        break
    log_input(user_input)
    response    = generate_response(user_input)
    speak(response)