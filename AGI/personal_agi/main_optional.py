from input import mic_listener, camera_capture, keyboard_input
from output import text_output, speech_output, visual_output
from memory import conscious_memory, long_term_memory, live_logger, memory_filter
from brain import language_model, perception, planner, reflector, action_engine
import config

def main():
    # Initialize components
    mic = mic_listener.init()
    camera = camera_capture.init()
    keyboard = keyboard_input.init()
    text_output = text_output.init()
    speech_output = speech_output.init()
    visual_output = visual_output.init()
    conscious_memory = conscious_memory.init(config.MEMORY_LIMITS['SHORT'])
    long_term_memory = long_term_memory.init(config.MEMORY_LIMITS['LONG'])
    live_logger = live_logger.init()
    memory_filter = memory_filter.init()
    language_model = language_model.init()
    perception = perception.init(camera, mic, keyboard)
    planner = planner.init()
    reflector = reflector.init()
    action_engine = action_engine.init()
    
    # Main loop
    while True:
        # Get input
        inputs = perception.get_inputs()
        
        # Logging live memory
        live_logger.log(inputs)
        
        # Filtering out thoughts from consciousness
        conscious_memory, thoughts = memory_filter.filter(conscious_memory, inputs)
        
        if thoughts:
            # Reflecting on thoughts
            reflection = reflector.reflect(thoughts)
            
            # Planning actions based on reflection
            action = planner.plan(reflection)
            
            # Executing the planned action
            response = action_engine.execute(action)
            
            # Outputting responses
            text_output.print(response)
            speech_output.speak(response)
            visual_output.show(thoughts)
            
        # Filtering out thoughts from long-term memory
        long_term_memory, _ = memory_filter.filter(long_term_memory, inputs)
        
if __name__ == "__main__":
    main()