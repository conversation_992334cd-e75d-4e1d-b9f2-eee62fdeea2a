# memory/live_logger.py

import sqlite3
import datetime
import os

DB_PATH = "data/memory_log.sqlite"

def init_db():
    os.makedirs("data", exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('''
        CREATE TABLE IF NOT EXISTS live_memory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            type TEXT,
            content TEXT
        )
    ''')
    conn.commit()
    conn.close()

def log_input(text, input_type="user"):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute('INSERT INTO live_memory (timestamp, type, content) VALUES (?, ?, ?)',
              (datetime.datetime.now().isoformat(), input_type, text))
    conn.commit()
    conn.close()

# Call this once at the start of main.py
# init_db()