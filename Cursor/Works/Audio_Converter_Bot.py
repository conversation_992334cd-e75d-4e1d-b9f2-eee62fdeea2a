import os
import logging
import asyncio
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from aiogram.filters import Command
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton, FSInputFile
import soundfile as sf
import sounddevice as sd
import numpy as np
from config import AUDIO_CONVERTER_BOT_TOKEN, TEMP_DIRS, LOG_FORMAT, LOG_LEVEL

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)

# Initialize bot and dispatcher
bot = Bot(token=AUDIO_CONVERTER_BOT_TOKEN)
storage = MemoryStorage()
dp = Dispatcher()

# Define states
class AudioStates(StatesGroup):
    waiting_for_option = State()
    waiting_for_file = State()
    waiting_for_bitrate = State()

# Keyboard with options
options_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="🎵 WAV to MP3"), KeyboardButton(text="🎵 MP3 to WAV")],
        [KeyboardButton(text="🎵 WAV to FLAC"), KeyboardButton(text="🎵 FLAC to WAV")],
        [KeyboardButton(text="🎵 WAV to OGG"), KeyboardButton(text="🎵 OGG to WAV")]
    ],
    resize_keyboard=True
)

# Start command handler
@dp.message(Command("start"))
async def start(message: types.Message, state: FSMContext):
    await state.clear()
    await state.set_state(AudioStates.waiting_for_option)
    await message.answer(
        "🎵 Welcome to the Audio Converter Bot!\n\n"
        "Choose an option to proceed:",
        parse_mode="Markdown",
        reply_markup=options_keyboard
    )

# Handle user option selection
@dp.message(AudioStates.waiting_for_option)
async def handle_option(message: types.Message, state: FSMContext):
    option = message.text
    if option not in [btn.text for row in options_keyboard.keyboard for btn in row]:
        await message.answer("Please select a valid option from the keyboard.")
        return

    await state.update_data(option=option)
    await state.set_state(AudioStates.waiting_for_file)
    await message.answer(f"✅ Selected: *{option}*.\nNow, please upload the audio file.", parse_mode="Markdown")

# Handle file upload and conversion
@dp.message(AudioStates.waiting_for_file)
async def handle_audio_file(message: types.Message, state: FSMContext):
    if not message.audio and not message.document:
        await message.answer("Please upload an audio file.")
        return

    try:
        data = await state.get_data()
        option = data.get("option")

        # Create temp directories
        os.makedirs(TEMP_DIRS['audio']['files'], exist_ok=True)
        os.makedirs(TEMP_DIRS['audio']['converted'], exist_ok=True)

        # Download file
        file_id = message.audio.file_id if message.audio else message.document.file_id
        file_info = await bot.get_file(file_id)
        input_file = f"{TEMP_DIRS['audio']['files']}/{file_id}"
        await bot.download_file(file_info.file_path, input_file)

        # Convert based on option
        output_file = await convert_audio(input_file, option)
        
        if output_file:
            await message.answer_document(
                FSInputFile(output_file),
                caption="✅ Here's your converted audio file!"
            )
        else:
            await message.answer("❌ Failed to convert the audio file. Please try again.")

        # Cleanup
        try:
            os.remove(input_file)
            os.remove(output_file)
        except:
            pass

    except Exception as e:
        logging.error(f"Error processing audio: {e}")
        await message.answer("❌ An error occurred while processing your file. Please try again.")

    await start(message, state)

async def convert_audio(input_file, option):
    try:
        output_file = f"{TEMP_DIRS['audio']['converted']}/converted_audio"
        
        # Read input file
        data, samplerate = sf.read(input_file)
        
        # Determine output format based on option
        if "WAV to MP3" in option:
            output_file += ".mp3"
            sf.write(output_file, data, samplerate, format='MP3')
        elif "MP3 to WAV" in option:
            output_file += ".wav"
            sf.write(output_file, data, samplerate, format='WAV')
        elif "WAV to FLAC" in option:
            output_file += ".flac"
            sf.write(output_file, data, samplerate, format='FLAC')
        elif "FLAC to WAV" in option:
            output_file += ".wav"
            sf.write(output_file, data, samplerate, format='WAV')
        elif "WAV to OGG" in option:
            output_file += ".ogg"
            sf.write(output_file, data, samplerate, format='OGG')
        elif "OGG to WAV" in option:
            output_file += ".wav"
            sf.write(output_file, data, samplerate, format='WAV')
        else:
            return None

        return output_file
    except Exception as e:
        logging.error(f"Error converting audio: {e}")
        return None

# Run the bot
async def main():
    logging.info("🎵 Audio Converter Bot is running...")
    await dp.start_polling(bot)

if __name__ == "__main__":
    asyncio.run(main()) 