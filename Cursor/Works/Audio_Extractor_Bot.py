import os
import logging
import asyncio
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from aiogram.filters import Command
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton, FSInputFile
import ffmpeg
from config import AUDIO_EXTRACTOR_BOT_TOKEN, TEMP_DIRS, LOG_FORMAT, LOG_LEVEL

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)

# Initialize bot and dispatcher
bot = Bot(token=AUDIO_EXTRACTOR_BOT_TOKEN)
storage = MemoryStorage()
dp = Dispatcher()

# Define states
class AudioExtractorStates(StatesGroup):
    waiting_for_option = State()
    waiting_for_file = State()
    waiting_for_bitrate = State()

# Keyboard with extraction options
options_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="🎵 MP4 to MP3"), KeyboardButton(text="🎵 MP4 to WAV")],
        [KeyboardButton(text="🎵 MP4 to AAC"), KeyboardButton(text="🎵 MP4 to FLAC")],
        [KeyboardButton(text="🎵 MP4 to M4A"), KeyboardButton(text="🎵 MP4 to OGG")],
        [KeyboardButton(text="🎵 AVI to MP3"), KeyboardButton(text="🎵 AVI to WAV")],
        [KeyboardButton(text="🎵 MKV to MP3"), KeyboardButton(text="🎵 MKV to WAV")],
        [KeyboardButton(text="🎵 MOV to MP3"), KeyboardButton(text="🎵 MOV to WAV")]
    ],
    resize_keyboard=True
)

# Bitrate options keyboard
bitrate_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="64 kbps"), KeyboardButton(text="128 kbps")],
        [KeyboardButton(text="192 kbps"), KeyboardButton(text="256 kbps")],
        [KeyboardButton(text="320 kbps"), KeyboardButton(text="Lossless")]
    ],
    resize_keyboard=True
)

# Start command handler
@dp.message(Command("start"))
async def start(message: types.Message, state: FSMContext):
    await state.clear()
    await state.set_state(AudioExtractorStates.waiting_for_option)
    await message.answer(
        "🎵 *Welcome to the Audio Extractor Bot!*\n\n"
        "Choose an extraction option to proceed:",
        parse_mode="Markdown",
        reply_markup=options_keyboard
    )

# Handle user option selection
@dp.message(AudioExtractorStates.waiting_for_option)
async def handle_option(message: types.Message, state: FSMContext):
    option = message.text
    if option not in [btn.text for row in options_keyboard.keyboard for btn in row]:
        await message.answer("Please select a valid option from the keyboard.")
        return

    await state.update_data(option=option)
    
    # Check if bitrate selection is needed
    if any(format in option.lower() for format in ['mp3', 'aac']):
        await state.set_state(AudioExtractorStates.waiting_for_bitrate)
        await message.answer(
            "🎵 Please select the desired bitrate:",
            reply_markup=bitrate_keyboard
        )
    else:
        await state.set_state(AudioExtractorStates.waiting_for_file)
        await message.answer(
            f"✅ Selected: *{option}*.\nNow, please upload the video file to extract audio from.",
            parse_mode="Markdown"
        )

# Handle bitrate selection
@dp.message(AudioExtractorStates.waiting_for_bitrate)
async def handle_bitrate(message: types.Message, state: FSMContext):
    bitrate = message.text
    if bitrate not in [btn.text for row in bitrate_keyboard.keyboard for btn in row]:
        await message.answer("Please select a valid bitrate option.")
        return

    await state.update_data(bitrate=bitrate)
    await state.set_state(AudioExtractorStates.waiting_for_file)
    await message.answer(
        "✅ Now, please upload the video file to extract audio from.",
        reply_markup=types.ReplyKeyboardRemove()
    )

# Handle file extraction
@dp.message(AudioExtractorStates.waiting_for_file)
async def handle_file(message: types.Message, state: FSMContext):
    data = await state.get_data()
    option = data.get("option")
    bitrate = data.get("bitrate")

    if not option:
        await start(message, state)
        return

    try:
        # Create temp directories
        os.makedirs(TEMP_DIRS['audio_extractor']['files'], exist_ok=True)
        os.makedirs(TEMP_DIRS['audio_extractor']['converted'], exist_ok=True)

        # Download the file
        file_id = message.video.file_id if message.video else message.document.file_id
        file_info = await bot.get_file(file_id)
        file_path = f"{TEMP_DIRS['audio_extractor']['files']}/{file_id}"
        
        # Download the file
        await bot.download_file(file_info.file_path, file_path)

        # Process based on option
        if option == "🎵 MP4 to MP3":
            output_path = await extract_mp4_to_mp3(file_path, file_id, bitrate)
        elif option == "🎵 MP4 to WAV":
            output_path = await extract_mp4_to_wav(file_path, file_id)
        elif option == "🎵 MP4 to AAC":
            output_path = await extract_mp4_to_aac(file_path, file_id, bitrate)
        elif option == "🎵 MP4 to FLAC":
            output_path = await extract_mp4_to_flac(file_path, file_id)
        elif option == "🎵 MP4 to M4A":
            output_path = await extract_mp4_to_m4a(file_path, file_id)
        elif option == "🎵 MP4 to OGG":
            output_path = await extract_mp4_to_ogg(file_path, file_id)
        elif option == "🎵 AVI to MP3":
            output_path = await extract_avi_to_mp3(file_path, file_id, bitrate)
        elif option == "🎵 AVI to WAV":
            output_path = await extract_avi_to_wav(file_path, file_id)
        elif option == "🎵 MKV to MP3":
            output_path = await extract_mkv_to_mp3(file_path, file_id, bitrate)
        elif option == "🎵 MKV to WAV":
            output_path = await extract_mkv_to_wav(file_path, file_id)
        elif option == "🎵 MOV to MP3":
            output_path = await extract_mov_to_mp3(file_path, file_id, bitrate)
        elif option == "🎵 MOV to WAV":
            output_path = await extract_mov_to_wav(file_path, file_id)
        else:
            await message.answer("❌ Unsupported extraction option.")
            return

        if output_path:
            # Send the extracted audio file
            await message.answer_document(
                FSInputFile(output_path),
                caption="✅ Here's your extracted audio file!"
            )
        else:
            await message.answer("❌ Failed to extract audio from the video file. Please try again.")

    except Exception as e:
        logging.error(f"Error extracting audio from video: {e}")
        await message.answer("❌ An error occurred while extracting audio from the video file. Please try again.")
    finally:
        # Cleanup
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            if output_path and os.path.exists(output_path):
                os.remove(output_path)
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")
        
        await start(message, state)

# Extraction functions
async def extract_mp4_to_mp3(input_path, file_id, bitrate):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.mp3"
        if bitrate == "Lossless":
            bitrate = "320k"
        else:
            bitrate = bitrate.replace(" kbps", "k")
        
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='libmp3lame', audio_bitrate=bitrate)
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting MP3 from MP4: {e}")
        return None

async def extract_mp4_to_wav(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.wav"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='pcm_s16le')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting WAV from MP4: {e}")
        return None

async def extract_mp4_to_aac(input_path, file_id, bitrate):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.aac"
        if bitrate == "Lossless":
            bitrate = "320k"
        else:
            bitrate = bitrate.replace(" kbps", "k")
        
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='aac', audio_bitrate=bitrate)
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting AAC from MP4: {e}")
        return None

async def extract_mp4_to_flac(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.flac"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='flac')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting FLAC from MP4: {e}")
        return None

async def extract_mp4_to_m4a(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.m4a"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='aac')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting M4A from MP4: {e}")
        return None

async def extract_mp4_to_ogg(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.ogg"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='libvorbis')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting OGG from MP4: {e}")
        return None

async def extract_avi_to_mp3(input_path, file_id, bitrate):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.mp3"
        if bitrate == "Lossless":
            bitrate = "320k"
        else:
            bitrate = bitrate.replace(" kbps", "k")
        
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='libmp3lame', audio_bitrate=bitrate)
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting MP3 from AVI: {e}")
        return None

async def extract_avi_to_wav(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.wav"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='pcm_s16le')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting WAV from AVI: {e}")
        return None

async def extract_mkv_to_mp3(input_path, file_id, bitrate):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.mp3"
        if bitrate == "Lossless":
            bitrate = "320k"
        else:
            bitrate = bitrate.replace(" kbps", "k")
        
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='libmp3lame', audio_bitrate=bitrate)
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting MP3 from MKV: {e}")
        return None

async def extract_mkv_to_wav(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.wav"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='pcm_s16le')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting WAV from MKV: {e}")
        return None

async def extract_mov_to_mp3(input_path, file_id, bitrate):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.mp3"
        if bitrate == "Lossless":
            bitrate = "320k"
        else:
            bitrate = bitrate.replace(" kbps", "k")
        
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='libmp3lame', audio_bitrate=bitrate)
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting MP3 from MOV: {e}")
        return None

async def extract_mov_to_wav(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['audio_extractor']['converted']}/{file_id}.wav"
        stream = ffmpeg.input(input_path)
        stream = ffmpeg.output(stream, output_path, acodec='pcm_s16le')
        ffmpeg.run(stream, overwrite_output=True)
        return output_path
    except Exception as e:
        logging.error(f"Error extracting WAV from MOV: {e}")
        return None

# Run the bot
async def main():
    logging.info("🤖 Audio Extractor Bot is running...")
    
    # Register handlers
    dp.message.register(start, Command("start"))
    dp.message.register(handle_option, AudioExtractorStates.waiting_for_option)
    dp.message.register(handle_bitrate, AudioExtractorStates.waiting_for_bitrate)
    dp.message.register(handle_file, AudioExtractorStates.waiting_for_file)
    
    # Add fallback handler for unhandled messages
    @dp.message()
    async def handle_unknown(message: types.Message, state: FSMContext):
        current_state = await state.get_state()
        if current_state is None or current_state == "AudioExtractorStates:waiting_for_option":
            await message.answer("⚠️ Please select an extraction option from the menu below.")
            await start(message, state)
        elif current_state == "AudioExtractorStates:waiting_for_bitrate":
            await message.answer("🎵 Please select a bitrate option.")
        elif current_state == "AudioExtractorStates:waiting_for_file":
            await message.answer("🎵 Please upload a video file to extract audio from.")
        else:
            await message.answer("⚠️ Please select an extraction option from the menu below.")
            await start(message, state)
    
    dp.message.register(handle_unknown)
    
    await dp.start_polling(bot, allowed_updates=dp.resolve_used_update_types())

if __name__ == "__main__":
    asyncio.run(main()) 