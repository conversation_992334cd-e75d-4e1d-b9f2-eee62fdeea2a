import os
import logging
import asyncio
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, types
from aiogram.filters import Command
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import Reply<PERSON><PERSON>boardMarkup, KeyboardButton, FSInputFile
from docx2pdf import convert
from PIL import Image
import pytesseract
from pdf2image import convert_from_path
import pdf2docx
import docx2txt
import pandas as pd
from openpyxl import load_workbook
import csv
import json
import xml.etree.ElementTree as ET
import yaml
import toml
import markdown
import html2text
import re
from config import FILE_CONVERTER_BOT_TOKEN, TEMP_DIRS, LOG_FORMAT, LOG_LEVEL

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)

# Initialize bot and dispatcher
bot = Bot(token=FILE_CONVERTER_BOT_TOKEN)
storage = MemoryStorage()
dp = Dispatcher()

# Define states
class ConverterStates(StatesGroup):
    waiting_for_option = State()
    waiting_for_file = State()

# Keyboard with conversion options
options_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="📄 DOCX to PDF"), KeyboardButton(text="📄 PDF to DOCX")],
        [KeyboardButton(text="🖼 PNG to JPG"), KeyboardButton(text="🖼 JPG to PNG")],
        [KeyboardButton(text="📊 XLSX to CSV"), KeyboardButton(text="📊 CSV to XLSX")],
        [KeyboardButton(text="📝 TXT to PDF"), KeyboardButton(text="📝 PDF to TXT")],
        [KeyboardButton(text="🔍 Image to Text"), KeyboardButton(text="📄 PDF to Image")],
        [KeyboardButton(text="📋 JSON to YAML"), KeyboardButton(text="📋 YAML to JSON")],
        [KeyboardButton(text="📝 Markdown to HTML"), KeyboardButton(text="📝 HTML to Markdown")]
    ],
    resize_keyboard=True
)

# Start command handler
@dp.message(Command("start"))
async def start(message: types.Message, state: FSMContext):
    await state.clear()
    await state.set_state(ConverterStates.waiting_for_option)
    await message.answer(
        "📌 *Welcome to the File Converter Bot!*\n\n"
        "Choose a conversion option to proceed:",
        parse_mode="Markdown",
        reply_markup=options_keyboard
    )

# Handle user option selection
@dp.message(ConverterStates.waiting_for_option)
async def handle_option(message: types.Message, state: FSMContext):
    option = message.text
    if option not in [btn.text for row in options_keyboard.keyboard for btn in row]:
        await message.answer("Please select a valid option from the keyboard.")
        return

    await state.update_data(option=option)
    await state.set_state(ConverterStates.waiting_for_file)
    await message.answer(f"✅ Selected: *{option}*.\nNow, please upload the file to convert.", parse_mode="Markdown")

# Handle file conversion
@dp.message(ConverterStates.waiting_for_file)
async def handle_file(message: types.Message, state: FSMContext):
    data = await state.get_data()
    option = data.get("option")

    if not option:
        await start(message, state)
        return

    try:
        # Create temp directories
        os.makedirs(TEMP_DIRS['file_converter']['files'], exist_ok=True)
        os.makedirs(TEMP_DIRS['file_converter']['converted'], exist_ok=True)

        # Download the file
        file_id = message.document.file_id if message.document else message.photo[-1].file_id
        file_info = await bot.get_file(file_id)
        file_path = f"{TEMP_DIRS['file_converter']['files']}/{file_id}"
        
        # Get file extension
        file_extension = os.path.splitext(file_info.file_path)[1].lower()
        
        # Download the file
        await bot.download_file(file_info.file_path, file_path)

        # Process based on option
        if option == "📄 DOCX to PDF":
            output_path = await convert_docx_to_pdf(file_path, file_id)
        elif option == "📄 PDF to DOCX":
            output_path = await convert_pdf_to_docx(file_path, file_id)
        elif option == "🖼 PNG to JPG":
            output_path = await convert_png_to_jpg(file_path, file_id)
        elif option == "🖼 JPG to PNG":
            output_path = await convert_jpg_to_png(file_path, file_id)
        elif option == "📊 XLSX to CSV":
            output_path = await convert_xlsx_to_csv(file_path, file_id)
        elif option == "📊 CSV to XLSX":
            output_path = await convert_csv_to_xlsx(file_path, file_id)
        elif option == "📝 TXT to PDF":
            output_path = await convert_txt_to_pdf(file_path, file_id)
        elif option == "🔍 Image to Text":
            output_path = await convert_image_to_text(file_path, file_id)
        elif option == "📄 PDF to Image":
            output_path = await convert_pdf_to_image(file_path, file_id)
        elif option == "📋 JSON to YAML":
            output_path = await convert_json_to_yaml(file_path, file_id)
        elif option == "📋 YAML to JSON":
            output_path = await convert_yaml_to_json(file_path, file_id)
        elif option == "📝 Markdown to HTML":
            output_path = await convert_markdown_to_html(file_path, file_id)
        elif option == "📝 HTML to Markdown":
            output_path = await convert_html_to_markdown(file_path, file_id)
        else:
            await message.answer("❌ Unsupported conversion option.")
            return

        if output_path:
            # Send the converted file
            await message.answer_document(
                FSInputFile(output_path),
                caption="✅ Here's your converted file!"
            )
        else:
            await message.answer("❌ Failed to convert the file. Please try again.")

    except Exception as e:
        logging.error(f"Error converting file: {e}")
        await message.answer("❌ An error occurred while converting the file. Please try again.")
    finally:
        # Cleanup
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            if output_path and os.path.exists(output_path):
                os.remove(output_path)
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")
        
        await start(message, state)

# Conversion functions
async def convert_docx_to_pdf(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.pdf"
        convert(input_path, output_path)
        return output_path
    except Exception as e:
        logging.error(f"Error converting DOCX to PDF: {e}")
        return None

async def convert_pdf_to_docx(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.docx"
        pdf2docx.convert(input_path, output_path)
        return output_path
    except Exception as e:
        logging.error(f"Error converting PDF to DOCX: {e}")
        return None

async def convert_png_to_jpg(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.jpg"
        with Image.open(input_path) as img:
            if img.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                background.save(output_path)
            else:
                img.convert('RGB').save(output_path)
        return output_path
    except Exception as e:
        logging.error(f"Error converting PNG to JPG: {e}")
        return None

async def convert_jpg_to_png(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.png"
        with Image.open(input_path) as img:
            img.save(output_path, 'PNG')
        return output_path
    except Exception as e:
        logging.error(f"Error converting JPG to PNG: {e}")
        return None

async def convert_xlsx_to_csv(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.csv"
        df = pd.read_excel(input_path)
        df.to_csv(output_path, index=False)
        return output_path
    except Exception as e:
        logging.error(f"Error converting XLSX to CSV: {e}")
        return None

async def convert_csv_to_xlsx(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.xlsx"
        df = pd.read_csv(input_path)
        df.to_excel(output_path, index=False)
        return output_path
    except Exception as e:
        logging.error(f"Error converting CSV to XLSX: {e}")
        return None

async def convert_txt_to_pdf(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.pdf"
        with open(input_path, 'r', encoding='utf-8') as file:
            text = file.read()
        
        from fpdf import FPDF
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        pdf.multi_cell(0, 10, text)
        pdf.output(output_path)
        return output_path
    except Exception as e:
        logging.error(f"Error converting TXT to PDF: {e}")
        return None

async def convert_image_to_text(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.txt"
        text = pytesseract.image_to_string(Image.open(input_path))
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(text)
        return output_path
    except Exception as e:
        logging.error(f"Error converting image to text: {e}")
        return None

async def convert_pdf_to_image(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.jpg"
        images = convert_from_path(input_path, first_page=1, last_page=1)
        if images:
            images[0].save(output_path)
            return output_path
        return None
    except Exception as e:
        logging.error(f"Error converting PDF to image: {e}")
        return None

async def convert_json_to_yaml(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.yaml"
        with open(input_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        with open(output_path, 'w', encoding='utf-8') as file:
            yaml.dump(data, file)
        return output_path
    except Exception as e:
        logging.error(f"Error converting JSON to YAML: {e}")
        return None

async def convert_yaml_to_json(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.json"
        with open(input_path, 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
        with open(output_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2)
        return output_path
    except Exception as e:
        logging.error(f"Error converting YAML to JSON: {e}")
        return None

async def convert_markdown_to_html(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.html"
        with open(input_path, 'r', encoding='utf-8') as file:
            markdown_text = file.read()
        html = markdown.markdown(markdown_text)
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(html)
        return output_path
    except Exception as e:
        logging.error(f"Error converting Markdown to HTML: {e}")
        return None

async def convert_html_to_markdown(input_path, file_id):
    try:
        output_path = f"{TEMP_DIRS['file_converter']['converted']}/{file_id}.md"
        with open(input_path, 'r', encoding='utf-8') as file:
            html = file.read()
        h = html2text.HTML2Text()
        markdown_text = h.handle(html)
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(markdown_text)
        return output_path
    except Exception as e:
        logging.error(f"Error converting HTML to Markdown: {e}")
        return None

# Run the bot
async def main():
    logging.info("🤖 File Converter Bot is running...")
    
    # Register handlers
    dp.message.register(start, Command("start"))
    dp.message.register(handle_option, ConverterStates.waiting_for_option)
    dp.message.register(handle_file, ConverterStates.waiting_for_file)
    
    # Add fallback handler for unhandled messages
    @dp.message()
    async def handle_unknown(message: types.Message, state: FSMContext):
        current_state = await state.get_state()
        if current_state is None or current_state == "ConverterStates:waiting_for_option":
            await message.answer("⚠️ Please select a conversion option from the menu below.")
            await start(message, state)
        elif current_state == "ConverterStates:waiting_for_file":
            await message.answer("📄 Please upload a file to convert.")
        else:
            await message.answer("⚠️ Please select a conversion option from the menu below.")
            await start(message, state)
    
    dp.message.register(handle_unknown)
    
    await dp.start_polling(bot, allowed_updates=dp.resolve_used_update_types())

if __name__ == "__main__":
    asyncio.run(main()) 