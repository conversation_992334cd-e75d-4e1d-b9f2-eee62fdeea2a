import os
import logging
import asyncio
from aiogram import <PERSON><PERSON>, Di<PERSON>atch<PERSON>, types
from aiogram.filters import Command
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import <PERSON>ly<PERSON>eyboardMarkup, KeyboardButton, FSInputFile
from pypdf import PdfMerger, PdfReader, PdfWriter
from pdf2image import convert_from_path
from PIL import Image, ImageDraw, ImageFont
import pytesseract
import pdfplumber
from fpdf import FPDF
from config import PDF_BOT_TOKEN, TEMP_DIRS, LOG_FORMAT, LOG_LEVEL

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)

# Check dependencies
TESSERACT_INSTALLED = False
POPPLER_INSTALLED = False

try:
    pytesseract.get_tesseract_version()
    TESSERACT_INSTALLED = True
except Exception as e:
    logging.error(f"Tesseract is not properly installed: {e}")

try:
    from pdf2image import convert_from_path
    # Test poppler by attempting to convert a small PDF
    with open("test.pdf", "w") as f:
        f.write("%PDF-1.7")
    convert_from_path("test.pdf", dpi=72, first_page=1, last_page=1)
    os.remove("test.pdf")
    POPPLER_INSTALLED = True
except Exception as e:
    logging.error(f"Poppler is not properly installed: {e}")

# Initialize bot and dispatcher
bot = Bot(token=PDF_BOT_TOKEN)
storage = MemoryStorage()
dp = Dispatcher()

# Define states
class PDFStates(StatesGroup):
    waiting_for_option = State()
    waiting_for_file = State()
    collecting_images = State()
    collecting_pdfs = State()

# Keyboard with options
options_keyboard = ReplyKeyboardMarkup(
    keyboard=[
        [KeyboardButton(text="📜 Convert Text to PDF"), KeyboardButton(text="📷 Images to PDF")],
        [KeyboardButton(text="📂 Merge PDFs"), KeyboardButton(text="🔍 Extract Text from PDF")],
        [KeyboardButton(text="💦 Watermark PDF"), KeyboardButton(text="🔐 Encrypt PDF")],
        [KeyboardButton(text="🖼 Convert PDF to Images")]
    ],
    resize_keyboard=True
)

# Start command handler
@dp.message(Command("start"))
async def start(message: types.Message, state: FSMContext):
    await state.clear()
    await state.set_state(PDFStates.waiting_for_option)
    await message.answer(
        "📌 *Welcome to the PDF Utility Bot!*\n\n"
        "Choose an option to proceed:",
        parse_mode="Markdown",
        reply_markup=options_keyboard
    )

# Handle user option selection
@dp.message(PDFStates.waiting_for_option)
async def handle_option(message: types.Message, state: FSMContext):
    option = message.text
    if option not in [btn.text for row in options_keyboard.keyboard for btn in row]:
        await message.answer("Please select a valid option from the keyboard.")
        return

    await state.update_data(option=option)
    if option == "📷 Images to PDF":
        await state.set_state(PDFStates.collecting_images)
        await state.update_data(image_files=[])
    elif option == "📂 Merge PDFs":
        await state.set_state(PDFStates.collecting_pdfs)
        await state.update_data(pdf_files=[])
    else:
        await state.set_state(PDFStates.waiting_for_file)
    await message.answer(f"✅ Selected: *{option}*.\nNow, please upload the required file(s).", parse_mode="Markdown")

# Handle file and process accordingly
@dp.message(PDFStates.waiting_for_file)
async def handle_files(message: types.Message, state: FSMContext):
    data = await state.get_data()
    option = data.get("option")

    if not option:
        await start(message, state)
        return

    file_path = f"user_{message.from_user.id}_file"

    try:
        if option == "📜 Convert Text to PDF" and message.text:
            pdf_filename = await text_to_pdf(message.text, file_path)
            if pdf_filename:
                await message.answer_document(FSInputFile(pdf_filename), caption="📄 Here is your PDF!")
                os.remove(pdf_filename)  # Clean up the temporary file
            else:
                await message.answer("❌ Failed to convert text to PDF. Please try again.")
            await start(message, state)  # Return to start only after completion
        elif option == "🔍 Extract Text from PDF" and message.document:
            await extract_text_from_pdf(message, file_path, state)
        elif option == "🖼 Convert PDF to Images" and message.document:
            await convert_pdf_to_images(message, file_path, state)
            await start(message, state)
        elif option == "💦 Watermark PDF" and message.document:
            await add_watermark(message, file_path)
            await start(message, state)
        elif option == "🔐 Encrypt PDF" and message.document:
            await encrypt_pdf(message, state)
            return  # Don't return to start - wait for password
        else:
            await message.answer("Please upload the correct file type for the selected option.")
            return

    except Exception as e:
        logging.error(f"Error processing file: {e}")
        await message.answer("An error occurred while processing your file. Please try again.")
        await start(message, state)

# Handle image collection
@dp.message(PDFStates.collecting_images)
async def handle_image_collection(message: types.Message, state: FSMContext):
    if not message.photo:
        if message.text == "/convert":
            await convert_images_to_pdf(message, state)
        else:
            await message.answer("Please send images or use /convert to generate PDF.")
        return

    try:
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)

        data = await state.get_data()
        image_files = data.get("image_files", [])

        file_id = message.photo[-1].file_id
        file_info = await bot.get_file(file_id)
        local_image_path = f"{TEMP_DIRS['pdf']['files']}/{file_id}.jpg"

        await bot.download_file(file_info.file_path, local_image_path)
        image_files.append(local_image_path)

        await state.update_data(image_files=image_files)
        await message.answer(f"📸 Image received! You have {len(image_files)} image(s). Send more or type /convert to generate PDF.")

    except Exception as e:
        logging.error(f"Error processing image: {e}")
        await message.answer("❌ Failed to process image. Please try again.")

@dp.message(Command("convert"))
async def convert_images_to_pdf(message: types.Message, state: FSMContext):
    try:
        current_state = await state.get_state()
        if current_state != "PDFStates:collecting_images":
            return

        data = await state.get_data()
        image_files = data.get("image_files", [])

        if not image_files:
            await message.answer("❌ No images found. Please upload images first.")
            return

        pdf_filename = f"{TEMP_DIRS['pdf']['converted']}/user_{message.from_user.id}_images.pdf"
        images = [Image.open(img).convert("RGB") for img in image_files]

        images[0].save(pdf_filename, save_all=True, append_images=images[1:])
        await message.answer_document(FSInputFile(pdf_filename), caption="📄 Here is your PDF with all your images!")

        # Cleanup
        for img in image_files:
            try:
                os.remove(img)
            except:
                pass
        try:
            os.remove(pdf_filename)
        except:
            pass

        await state.clear()
        await start(message, state)  # Return to start only after PDF is generated

    except Exception as e:
        logging.error(f"Error creating PDF: {e}")
        await message.answer("❌ Failed to generate PDF. Please try again.")
        await state.clear()
        await start(message, state)

# PDF Utility Functions
async def text_to_pdf(text, file_path):
    try:
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)

        pdf.multi_cell(190, 10, text)

        pdf_filename = f"{TEMP_DIRS['pdf']['converted']}/{file_path}.pdf"
        pdf.output(pdf_filename)

        return pdf_filename
    except Exception as e:
        logging.error(f"Error converting text to PDF: {e}")
        return None

@dp.message(PDFStates.collecting_pdfs)
async def handle_pdf_collection(message: types.Message, state: FSMContext):
    if not message.document or not message.document.mime_type == "application/pdf":
        if message.text == "/merge":
            await process_merge_pdfs(message, state)
        else:
            await message.answer("Please send PDF files or use /merge to combine PDFs.")
        return

    try:
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)

        data = await state.get_data()
        pdf_files = data.get("pdf_files", [])

        file_id = message.document.file_id
        file_info = await bot.get_file(file_id)
        local_pdf_path = f"{TEMP_DIRS['pdf']['files']}/{file_id}.pdf"

        # Validate PDF before saving
        try:
            await bot.download_file(file_info.file_path, local_pdf_path)
            # Try to open the PDF to validate it
            PdfReader(local_pdf_path)
            pdf_files.append(local_pdf_path)
            await state.update_data(pdf_files=pdf_files)
            await message.answer(f"📄 PDF received! You have {len(pdf_files)} PDF(s). Send more or type /merge to combine.")
        except Exception as e:
            logging.error(f"Invalid PDF file: {e}")
            try:
                os.remove(local_pdf_path)
            except:
                pass
            await message.answer("❌ The file appears to be invalid or corrupted. Please send a valid PDF file.")

    except Exception as e:
        logging.error(f"Error saving PDF: {e}")
        await message.answer("❌ Failed to process PDF. Please try again.")

@dp.message(Command("merge"))
async def process_merge_pdfs(message: types.Message, state: FSMContext):
    merged_pdf_filename = None
    try:
        current_state = await state.get_state()
        if current_state != "PDFStates:collecting_pdfs":
            await start(message, state)
            return

        data = await state.get_data()
        pdf_files = data.get("pdf_files", [])

        if not pdf_files:
            await message.answer("❌ No PDFs found. Please upload PDFs first.")
            await start(message, state)
            return

        if len(pdf_files) < 2:
            await message.answer("❌ Upload at least 2 PDFs before merging.")
            return

        merged_pdf_filename = f"{TEMP_DIRS['pdf']['converted']}/user_{message.from_user.id}_merged.pdf"
        writer = PdfWriter()

        # Merge PDFs
        for pdf in pdf_files:
            if os.path.exists(pdf):
                try:
                    reader = PdfReader(pdf)
                    for page in reader.pages:
                        writer.add_page(page)
                except Exception as e:
                    logging.error(f"Error reading PDF {pdf}: {e}")
                    continue

        # Write merged PDF
        with open(merged_pdf_filename, "wb") as output_file:
            writer.write(output_file)

        # Send the merged PDF
        await message.answer_document(
            FSInputFile(merged_pdf_filename),
            caption="📄 Here is your merged PDF!"
        )

        # Clean up source PDFs
        for pdf in pdf_files:
            try:
                if os.path.exists(pdf):
                    os.remove(pdf)
            except Exception as e:
                logging.error(f"Error removing source PDF: {e}")

    except Exception as e:
        logging.error(f"Error in merge process: {e}")
        await message.answer("❌ Failed to merge PDFs. Please try again.")
    finally:
        # Clear state
        await state.clear()
        
        # Clean up merged PDF last
        if merged_pdf_filename and os.path.exists(merged_pdf_filename):
            try:
                os.remove(merged_pdf_filename)
            except Exception as e:
                logging.error(f"Error removing merged PDF: {e}")
        
        # Return to start menu
        await start(message, state)

async def extract_text_from_pdf(message, file_path, state: FSMContext):
    try:
        # Create temp directories
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)
        os.makedirs(TEMP_DIRS['pdf']['converted'], exist_ok=True)

        # Download the PDF
        file_id = message.document.file_id
        file_info = await bot.get_file(file_id)
        local_pdf_path = f"{TEMP_DIRS['pdf']['files']}/{file_id}.pdf"
        await bot.download_file(file_info.file_path, local_pdf_path)
        
        await message.answer("📄 Processing your PDF, please wait...")
        extracted_text = ""

        # Method 1: Try pdfplumber first (most reliable)
        try:
            with pdfplumber.open(local_pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            extracted_text += f"--- Page {page_num} ---\n{page_text.strip()}\n\n"
                    except Exception as e:
                        logging.error(f"pdfplumber extraction error on page {page_num}: {e}")
                        continue
        except Exception as e:
            logging.error(f"pdfplumber extraction error: {e}")

        # Method 2: Try PyPDF if pdfplumber didn't work
        if not extracted_text.strip():
            try:
                pdf_reader = PdfReader(local_pdf_path)
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            extracted_text += f"--- Page {page_num} ---\n{page_text.strip()}\n\n"
                    except Exception as e:
                        logging.error(f"PyPDF extraction error on page {page_num}: {e}")
                        continue
            except Exception as e:
                logging.error(f"PyPDF extraction error: {e}")

        # Method 3: Try OCR as last resort
        if not extracted_text.strip():
            try:
                # Convert PDF to images
                images = convert_from_path(local_pdf_path, dpi=300)
                total_pages = len(images)
                
                # Process each page with OCR
                for i, image in enumerate(images, 1):
                    try:
                        # Enhance image for better OCR
                        enhanced_image = image.convert('L')  # Convert to grayscale
                        enhanced_image = enhanced_image.point(lambda x: 0 if x < 128 else 255, '1')  # Increase contrast
                        
                        # Configure tesseract for better accuracy
                        custom_config = r'--oem 3 --psm 6 -l eng'  # Specify English language
                        page_text = pytesseract.image_to_string(enhanced_image, config=custom_config)
                        
                        if page_text and page_text.strip():
                            extracted_text += f"--- Page {i} ---\n{page_text.strip()}\n\n"
                    except Exception as e:
                        logging.error(f"OCR error on page {i}: {e}")
                        continue
            except Exception as e:
                logging.error(f"Error during OCR process: {e}")

        # If no text was extracted at all
        if not extracted_text.strip():
            await message.answer("❌ Could not extract text from this PDF. The file might be corrupted, password-protected, or contain unrecognizable text.")
            return

        # Clean up the extracted text
        extracted_text = extracted_text.replace('\n\n\n', '\n\n').strip()

        # Send the extracted text
        if len(extracted_text) > 4000:
            # Save as file if too long
            text_file_path = f"{TEMP_DIRS['pdf']['converted']}/extracted_text_{message.from_user.id}.txt"
            with open(text_file_path, "w", encoding="utf-8") as f:
                f.write(extracted_text)
            await message.answer_document(
                FSInputFile(text_file_path),
                caption="📄 The extracted text was too long, here it is as a file."
            )
            os.remove(text_file_path)
        else:
            # Send directly if short enough
            await message.answer(f"📜 Extracted Text:\n\n{extracted_text}")

    except Exception as e:
        logging.error(f"Error extracting text from PDF: {e}")
        await message.answer("❌ Failed to extract text from the PDF. Please try again.")
    finally:
        # Cleanup
        try:
            if os.path.exists(local_pdf_path):
                os.remove(local_pdf_path)
        except Exception as e:
            logging.error(f"Error cleaning up PDF file: {e}")
        await start(message, state)

async def convert_pdf_to_images(message, file_path, state: FSMContext):
    local_pdf_path = None
    try:
        # Create temp directories
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)
        os.makedirs(TEMP_DIRS['pdf']['converted'], exist_ok=True)

        # Download and save the PDF
        file_id = message.document.file_id
        file_info = await bot.get_file(file_id)
        local_pdf_path = f"{TEMP_DIRS['pdf']['files']}/{file_id}.pdf"

        await message.answer("📄 Converting PDF to images, please wait...")
        await bot.download_file(file_info.file_path, local_pdf_path)

        # Validate PDF first
        try:
            pdf = PdfReader(local_pdf_path)
            if len(pdf.pages) > 20:
                await message.answer("❌ PDF has too many pages. Please try a PDF with fewer pages (maximum 20).")
                return
        except Exception as e:
            logging.error(f"Error validating PDF: {e}")
            await message.answer("❌ Invalid or corrupted PDF file. Please try another file.")
            return

        # Convert PDF to images
        images = convert_from_path(
            local_pdf_path,
            dpi=150,
            fmt="jpeg"
        )

        if not images:
            await message.answer("❌ No images could be extracted from the PDF.")
            return

        # Process and send images one by one
        for i, img in enumerate(images):
            try:
                # Save image with reduced quality
                image_path = f"{TEMP_DIRS['pdf']['converted']}/page_{i+1}.jpg"
                img.save(image_path, "JPEG", quality=70)
                
                # Send image
                await message.answer_photo(
                    FSInputFile(image_path),
                    caption=f"Page {i+1}/{len(images)}"
                )
                
                # Remove image after sending
                os.remove(image_path)
                await asyncio.sleep(1)  # Small delay between sends
                
            except Exception as e:
                logging.error(f"Error processing page {i+1}: {e}")
                await message.answer(f"❌ Failed to process page {i+1}")
                continue

        await message.answer("✅ PDF conversion completed!")

    except Exception as e:
        logging.error(f"Error in PDF to image conversion: {e}")
        error_message = "❌ Failed to convert the PDF to images."
        if "poppler" in str(e).lower():
            error_message = "❌ PDF conversion tool is not properly installed. Please contact the administrator."
        elif "memory" in str(e).lower():
            error_message = "❌ The PDF file is too large to process. Please try a smaller file."
        await message.answer(error_message)

    finally:
        # Cleanup
        try:
            if local_pdf_path and os.path.exists(local_pdf_path):
                os.remove(local_pdf_path)
            # Clean temp_images directory
            for file in os.listdir(TEMP_DIRS['pdf']['converted']):
                if file.endswith(".jpg") or file.endswith(".jpeg"):
                    os.remove(os.path.join(TEMP_DIRS['pdf']['converted'], file))
        except Exception as e:
            logging.error(f"Error during cleanup: {e}")
        
        # Return to start menu
        await start(message, state)

async def add_watermark(message, file_path):
    try:
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)

        file_id = message.document.file_id
        file_info = await bot.get_file(file_id)
        file_path_telegram = file_info.file_path
        local_pdf_path = f"{TEMP_DIRS['pdf']['files']}/original_{file_id}.pdf"
        watermarked_pdf_path = f"{TEMP_DIRS['pdf']['converted']}/watermarked_{file_id}.pdf"

        await bot.download_file(file_path_telegram, local_pdf_path)

        pdf_reader = PdfReader(local_pdf_path)
        pdf_writer = PdfWriter()

        watermark_text = "CONFIDENTIAL"
        font_path = "/System/Library/Fonts/Supplemental/Arial.ttf"
        font_size = 36

        try:
            font = ImageFont.truetype(font_path, font_size)
        except Exception as e:
            logging.error(f"Error loading font: {e}")
            await message.answer("❌ Failed to load font for watermark. Try another method.")
            return

        for page_num, page in enumerate(pdf_reader.pages):
            img = convert_from_path(local_pdf_path, first_page=page_num + 1, last_page=page_num + 1, dpi=200)[0]
            draw = ImageDraw.Draw(img)

            try:
                text_width, text_height = draw.textbbox((0, 0), watermark_text, font=font)[2:]
            except Exception as e:
                logging.error(f"Error calculating watermark dimensions: {e}")
                await message.answer("❌ Error adding watermark text.")
                return

            x = (img.width - text_width) / 2
            y = (img.height - text_height) / 2

            draw.text((x, y), watermark_text, font=font, fill=(255, 0, 0, 100))

            img_pdf_path = f"{TEMP_DIRS['pdf']['files']}/page_{page_num + 1}.pdf"
            img.save(img_pdf_path, "PDF")
            pdf_writer.add_page(PdfReader(img_pdf_path).pages[0])
            os.remove(img_pdf_path)  # Clean up individual page PDFs

        with open(watermarked_pdf_path, "wb") as output_pdf:
            pdf_writer.write(output_pdf)

        await message.answer_document(FSInputFile(watermarked_pdf_path), caption="✅ Watermarked PDF is ready!")

        # Cleanup
        os.remove(local_pdf_path)
        os.remove(watermarked_pdf_path)

    except Exception as e:
        logging.error(f"Error adding watermark to PDF: {e}")
        await message.answer("❌ Failed to add watermark. Please try again.")

class EncryptState(StatesGroup):
    waiting_for_password = State()

@dp.message(PDFStates.waiting_for_file)
async def encrypt_pdf(message: types.Message, state: FSMContext):
    try:
        os.makedirs(TEMP_DIRS['pdf']['files'], exist_ok=True)

        file_id = message.document.file_id
        file_info = await bot.get_file(file_id)
        local_pdf_path = f"{TEMP_DIRS['pdf']['files']}/original_{file_id}.pdf"

        await bot.download_file(file_info.file_path, local_pdf_path)

        await state.update_data(pdf_path=local_pdf_path)
        await state.set_state(EncryptState.waiting_for_password)

        await message.answer("🔒 Please send me the password to encrypt the PDF.")

    except Exception as e:
        logging.error(f"Error processing PDF: {e}")
        await message.answer("❌ Failed to process PDF. Please try again.")

@dp.message(EncryptState.waiting_for_password)
async def receive_password(message: types.Message, state: FSMContext):
    data = await state.get_data()
    local_pdf_path = data.get("pdf_path")
    encrypted_pdf_path = f"{TEMP_DIRS['pdf']['converted']}/encrypted_{os.path.basename(local_pdf_path)}"

    password = message.text.strip()
    if not password:
        await message.answer("❌ Please enter a valid password.")
        return

    try:
        pdf_reader = PdfReader(local_pdf_path)
        pdf_writer = PdfWriter()

        for page in pdf_reader.pages:
            pdf_writer.add_page(page)

        pdf_writer.encrypt(password)

        with open(encrypted_pdf_path, "wb") as output_pdf:
            pdf_writer.write(output_pdf)

        await message.answer_document(
            FSInputFile(encrypted_pdf_path),
            caption=f"✅ Your PDF is encrypted!\n🔑 *Password:* `{password}`",
            parse_mode="Markdown"
        )

        # Cleanup
        os.remove(local_pdf_path)
        os.remove(encrypted_pdf_path)
        await state.clear()

    except Exception as e:
        logging.error(f"Error encrypting PDF: {e}")
        await message.answer("❌ Failed to encrypt the PDF. Please try again.")

# Run the bot
async def main():
    logging.info("🤖 PDF Utility Bot is running...")

    # Register handlers
    dp.message.register(start, Command("start"))
    dp.message.register(handle_option, PDFStates.waiting_for_option)
    dp.message.register(handle_files, PDFStates.waiting_for_file)
    dp.message.register(handle_image_collection, PDFStates.collecting_images)
    dp.message.register(handle_pdf_collection, PDFStates.collecting_pdfs)
    dp.message.register(convert_images_to_pdf, Command("convert"))
    dp.message.register(process_merge_pdfs, Command("merge"))
    dp.message.register(receive_password, EncryptState.waiting_for_password)
    
    # Add fallback handler for unhandled messages
    @dp.message()
    async def handle_unknown(message: types.Message, state: FSMContext):
        current_state = await state.get_state()
        if current_state is None or current_state == "PDFStates:waiting_for_option":
            await message.answer("⚠️ Please select an option from the menu below.")
            await start(message, state)
        elif current_state == "PDFStates:collecting_images":
            if not message.photo:
                await message.answer("📸 Please send images or use /convert to generate PDF.")
        elif current_state == "PDFStates:collecting_pdfs":
            if not message.document or not message.document.mime_type == "application/pdf":
                await message.answer("📄 Please send PDF files or use /merge to combine PDFs.")
        elif current_state == "EncryptState:waiting_for_password":
            await message.answer("🔒 Please enter a password to encrypt the PDF.")
        else:
            await message.answer("⚠️ Please select an option from the menu below.")
            await start(message, state)

    dp.message.register(handle_unknown)

    await dp.start_polling(bot, allowed_updates=dp.resolve_used_update_types())

if __name__ == "__main__":
    asyncio.run(main())