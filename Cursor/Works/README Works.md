# Telegram File Conversion Bots - Startup Guide

## Project Location
All files are located in:
```
/Users/<USER>/Documents/Cursor/Works/
```

## Project Structure
```
Works/
├── telegram_bots_envmac/           # Virtual environment
├── PDF_Text_image_bot_codes.py     # PDF Utility Bot
├── File_Converter_Bot.py           # File Converter Bot
├── Audio_Converter_Bot.py          # Audio Converter Bot
├── Audio_Extractor_Bot.py          # Audio Extractor Bot
├── config.py                       # Bot tokens and configuration
├── requirements.txt                # Python dependencies
├── run_pdf_bot.sh                  # Script to run PDF bot
├── run_file_converter_bot.sh       # Script to run File Converter bot
├── run_audio_converter_bot.sh      # Script to run Audio Converter bot
└── run_audio_extractor_bot.sh      # Script to run Audio Extractor bot
```

## Starting the Bots

### Method 1: Using Shell Scripts (Recommended)

1. Open Terminal and navigate to the Works directory:
```bash
cd /Users/<USER>/Documents/Cursor/Works
```

2. Make sure the scripts are executable:
```bash
chmod +x run_pdf_bot.sh run_file_converter_bot.sh run_audio_converter_bot.sh run_audio_extractor_bot.sh
```

chmod +x setup.sh

3. Run any bot using its script:
```bash
# Start PDF Bot
./run_pdf_bot.sh

# Start File Converter Bot
./run_file_converter_bot.sh

# Start Audio Converter Bot
./run_audio_converter_bot.sh

# Start Audio Extractor Bot
./run_audio_extractor_bot.sh
```

### Method 2: Manual Startup

1. Open Terminal and navigate to the Works directory:
```bash
cd /Users/<USER>/Documents/Cursor/Works
```

2. Activate the virtual environment:
```bash
source telegram_bots_envmac/bin/activate
```

3. Run any bot using the virtual environment's Python:
```bash
# Start PDF Bot
./telegram_bots_envmac/bin/python PDF_Text_image_bot_codes.py

# Start File Converter Bot
./telegram_bots_envmac/bin/python File_Converter_Bot.py

# Start Audio Converter Bot
./telegram_bots_envmac/bin/python Audio_Converter_Bot.py

# Start Audio Extractor Bot
./telegram_bots_envmac/bin/python Audio_Extractor_Bot.py
```

## Important Notes

1. Each bot needs to run in a separate terminal window
2. Make sure you're in the correct directory (`/Users/<USER>/Documents/Cursor/Works`) before running any commands
3. The virtual environment (`telegram_bots_envmac`) contains all required packages
4. Bot tokens are stored in `config.py`
5. Temporary files are automatically cleaned up after processing

## Troubleshooting

1. If a bot doesn't start:
   - Make sure you're in the correct directory
   - Verify the virtual environment is activated
   - Check if the bot token in config.py is correct

2. If you see "Permission denied":
   ```bash
   chmod +x run_*.sh
   ```

3. If you see "No such file or directory":
   - Make sure you're in the Works directory
   - Verify all files are present
   - Check if the virtual environment is properly set up

4. To check if virtual environment is active:
   - Look for `(telegram_bots_envmac)` at the start of your prompt
   - Or run: `which python` (should point to telegram_bots_envmac/bin/python)

## Quick Reference

Start all bots in separate terminals:
```bash
# Terminal 1
cd /Users/<USER>/Documents/Cursor/Works && ./run_pdf_bot.sh

# Terminal 2
cd /Users/<USER>/Documents/Cursor/Works && ./run_file_converter_bot.sh

# Terminal 3
cd /Users/<USER>/Documents/Cursor/Works && ./run_audio_converter_bot.sh

# Terminal 4
cd /Users/<USER>/Documents/Cursor/Works && ./run_audio_extractor_bot.sh
```

Stop any bot: Press `Ctrl+C` in its terminal window 