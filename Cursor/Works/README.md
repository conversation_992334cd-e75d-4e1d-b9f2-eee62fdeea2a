# Telegram File Conversion Bots

This repository contains four Telegram bots for file conversion:

1. PDF Utility Bot - For PDF-related operations (compression, merging, splitting, etc.)
2. File Converter Bot - For converting between various file formats (DOCX to PDF, PNG to JPG, etc.)
3. Audio Converter Bot - For converting between audio formats (MP3, WAV, AAC, etc.)
4. Audio Extractor Bot - For extracting audio from video files (MP4, AVI, MKV, etc.)

## Project Structure

```
Works/
├── PDF_Text_image_bot_codes.py    # PDF Utility Bot
├── File_Converter_Bot.py          # File Converter Bot
├── Audio_Converter_Bot.py         # Audio Converter Bot
├── Audio_Extractor_Bot.py         # Audio Extractor Bot
├── config.py                      # Configuration file
├── requirements.txt               # Python dependencies
└── .env                          # Environment variables (create this)
```

## Prerequisites

### System Requirements
- Python 3.8 or higher
- FFmpeg installed on your system
- Tesseract OCR installed on your system (for PDF text extraction)
- Poppler installed on your system (for PDF to image conversion)

### Installing System Dependencies

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install FFmpeg
brew install ffmpeg

# Install Tesseract OCR
brew install tesseract

# Install Poppler
brew install poppler
```

## Setup

2. Create and activate a virtual environment:

Install the required Python packages:
```bash
pip install -r requirements.txt
```
# Activate virtual environment
cd /Users/<USER>/Documents/Cursor/Works
source requirements/environments/telegram_bots_envmac/bin/activate

4. Create a `.env` file in the `Works` directory with your Telegram bot tokens:
```env
PDF_BOT_TOKEN=your_pdf_bot_token
FILE_CONVERTER_BOT_TOKEN=your_file_converter_bot_token
AUDIO_CONVERTER_BOT_TOKEN=your_audio_converter_bot_token
AUDIO_EXTRACTOR_BOT_TOKEN=your_audio_extractor_bot_token
```

## Running the Bots

Each bot can be run independently in a separate terminal window. Make sure your virtual environment is activated before running any bot.

### Terminal 1 - PDF Utility Bot
```bash
cd Works
python PDF_Text_image_bot_codes.py
```

### Terminal 2 - File Converter Bot
```bash
cd Works
python File_Converter_Bot.py
```

### Terminal 3 - Audio Converter Bot
```bash
cd Works
python Audio_Converter_Bot.py
```

### Terminal 4 - Audio Extractor Bot
```bash
cd Works
python Audio_Extractor_Bot.py
```

## Features

### PDF Utility Bot
- Convert text to PDF
- Convert images to PDF
- Merge multiple PDFs
- Extract text from PDF
- Add watermark to PDF
- Encrypt PDF with password
- Convert PDF to images

### File Converter Bot
- Convert DOCX to PDF and vice versa
- Convert PNG to JPG and vice versa
- Convert XLSX to CSV and vice versa
- Convert TXT to PDF
- Extract text from images
- Convert JSON to YAML and vice versa
- Convert Markdown to HTML and vice versa

### Audio Converter Bot
- Convert MP3 to WAV and vice versa
- Convert MP3 to AAC and vice versa
- Convert WAV to OGG and vice versa
- Convert MP3 to FLAC and vice versa
- Convert WAV to M4A and vice versa
- Convert MP3 to WMA and vice versa
- Adjustable bitrate options

### Audio Extractor Bot
- Extract audio from MP4 videos
- Extract audio from AVI videos
- Extract audio from MKV videos
- Extract audio from MOV videos
- Multiple output formats (MP3, WAV, AAC, FLAC, M4A, OGG)
- Adjustable bitrate options

## Usage

1. Start a bot by sending the `/start` command in Telegram
2. Choose the desired operation from the menu
3. Upload the file(s) to process
4. Wait for the bot to process the file(s)
5. Download the converted/processed file(s)

## Notes

- All bots use temporary directories for file processing
- Files are automatically cleaned up after processing
- Maximum file size limits apply based on Telegram's restrictions
- Some operations may take longer depending on file size and complexity
- Make sure you have enough disk space for temporary file processing
- Keep your bot tokens secure and never share them publicly

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   - Ensure FFmpeg is installed and added to system PATH
   - Try running `ffmpeg -version` in terminal to verify installation

2. **Tesseract not found**
   - Ensure Tesseract is installed and added to system PATH
   - Try running `tesseract --version` in terminal to verify installation

3. **Poppler not found**
   - Ensure Poppler is installed and added to system PATH
   - Try running `pdfinfo --version` in terminal to verify installation

4. **Bot not responding**
   - Check if the bot token is correct in `.env` file
   - Ensure the bot is running in a terminal
   - Check internet connection

5. **File processing errors**
   - Check file size limits
   - Ensure file format is supported
   - Try with a smaller file

### Logging

- All bots have logging enabled
- Check the terminal output for error messages
- Logs include information about file processing and errors