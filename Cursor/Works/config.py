import os

# Telegram Bot Tokens
PDF_BOT_TOKEN = "**********************************************"
FILE_CONVERTER_BOT_TOKEN = "7930583913:AAFnLUkiIC3CvjzkHJNT_tdtLGfdi1_zKEE"
AUDIO_CONVERTER_BOT_TOKEN = "7532211127:AAEXo1jR6Zx_9fVCf57MP6OFinAD1vE1C8s"
AUDIO_EXTRACTOR_BOT_TOKEN = "7807772843:AAHYHR_MXJwolM-ufa_oN57CRQRvEhXHlxQ"

# Directory Structure
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TEMP_DIR = os.path.join(BASE_DIR, "temp")

TEMP_DIRS = {
    'pdf': {
        'files': os.path.join(TEMP_DIR, "pdf", "files"),
        'converted': os.path.join(TEMP_DIR, "pdf", "converted")
    },
    'audio': {
        'files': os.path.join(TEMP_DIR, "audio", "files"),
        'converted': os.path.join(TEMP_DIR, "audio", "converted")
    },
    'file_converter': {
        'files': os.path.join(TEMP_DIR, "file_converter", "files"),
        'converted': os.path.join(TEMP_DIR, "file_converter", "converted")
    }
}

# Logging Configuration
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_LEVEL = "INFO"

# Create temp directories if they don't exist
for category in TEMP_DIRS.values():
    for directory in category.values():
        os.makedirs(directory, exist_ok=True) 