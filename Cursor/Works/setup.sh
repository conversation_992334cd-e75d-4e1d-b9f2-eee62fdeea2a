#!/bin/bash

echo "🚀 Starting setup for Telegram Bots..."

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "Homebrew is already installed. Updating..."
    brew update
fi

# Install Homebrew dependencies
echo "📦 Installing Homebrew dependencies..."
brew install python@3.13
brew install ffmpeg
brew install poppler
brew install tesseract
brew install libmagic
brew install portaudio
brew install libogg
brew install libvorbis
brew install pkg-config

# Install Xcode Command Line Tools if not already installed
if ! xcode-select -p &> /dev/null; then
    echo "Installing Xcode Command Line Tools..."
    xcode-select --install
fi

# Create and activate virtual environment
echo "🔧 Setting up Python virtual environment..."
python3 -m venv telegram_bots_envmac
source telegram_bots_envmac/bin/activate

# Install Python build dependencies
echo "📚 Installing Python build dependencies..."
pip install --upgrade pip
pip install wheel
pip install setuptools
pip install Cython

# Install Python dependencies
echo "📚 Installing Python packages..."
export CFLAGS="-I$(brew --prefix portaudio)/include"
export LDFLAGS="-L$(brew --prefix portaudio)/lib"
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p temp/pdf/files temp/pdf/converted
mkdir -p temp/audio/files temp/audio/converted
mkdir -p temp/file_converter/files temp/file_converter/converted

echo "✅ Setup completed! You can now run the bots using their respective run scripts:"
echo "- ./run_pdf_bot.sh"
echo "- ./run_file_converter_bot.sh"
echo "- ./run_audio_converter_bot.sh"
echo "- ./run_audio_extractor_bot.sh"

# Make the run scripts executable
chmod +x run_pdf_bot.sh run_file_converter_bot.sh run_audio_converter_bot.sh run_audio_extractor_bot.sh 