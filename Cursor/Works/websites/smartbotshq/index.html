<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SmartBotsHQ - Advanced AI Automation</title>
  <!-- Fonts & Styles -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;500;700&display=swap" rel="stylesheet">
  <!-- GSAP & ScrollTrigger for animations -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
  <style>
    /* General Reset */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: 'Rajdhani', sans-serif;
      color: #111;
      overflow-x: hidden;
      scroll-behavior: smooth;
      background-color: #000;
    }
    
    /* Background Animation */
    #particles-js {
      position: fixed;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: -1;
      background: linear-gradient(45deg, #0b132b, #3a506b, #1a237e);
    }
    
    /* Overlay for readability */
    .overlay-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.4);
      z-index: -1;
    }

    /* Navigation */
    .nav {
      position: fixed;
      top: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 2rem 4rem;
      z-index: 1000;
      transition: background 0.3s ease;
    }
    
    .nav.scrolled {
      background: rgba(0, 0, 0, 0.9);
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    }
    
    .logo {
      font-family: 'Orbitron', sans-serif;
      font-size: 2.2rem;
      font-weight: 900;
      color: #fff;
      text-decoration: none;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
    }
    
    .nav-link {
      color: #fff;
      text-decoration: none;
      font-size: 1.2rem;
      font-weight: 700;
      position: relative;
      padding: 0.5rem 0;
      transition: all 0.3s ease;
    }
    
    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background: cyan;
      transition: width 0.3s ease;
    }
    
    .nav-link:hover::after {
      width: 100%;
    }
    
    /* Content Sections */
    .section {
      min-height: 100vh;
      padding: 6rem 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    .content-container {
      max-width: 1400px;
      width: 100%;
      margin: 0 auto;
      position: relative;
      z-index: 10;
    }
    
    /* Hero Section */
    #hero {
      text-align: center;
      padding-top: 10rem;
    }
    
    .hero-title {
      font-family: 'Orbitron', sans-serif;
      font-size: 7rem;
      font-weight: 900;
      background: linear-gradient(to right, #5ee7df, #b490ca);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      text-transform: uppercase;
      margin-bottom: 1rem;
      line-height: 1.1;
      letter-spacing: -2px;
      position: relative;
    }
    
    .hero-title::before {
      content: attr(data-text);
      position: absolute;
      left: -5px;
      top: 0;
      color: cyan;
      opacity: 0.7;
      filter: blur(1.5px);
      animation: glitch 2s infinite alternate-reverse;
    }
    
    .hero-title::after {
      content: attr(data-text);
      position: absolute;
      left: 5px;
      top: 0;
      color: magenta;
      opacity: 0.7;
      filter: blur(1.5px);
      animation: glitch 3s infinite alternate-reverse;
    }
    
    @keyframes glitch {
      0%, 100% { transform: translate(0); }
      20% { transform: translate(-5px, 5px); }
      40% { transform: translate(5px, -5px); }
      60% { transform: translate(-5px, -5px); }
      80% { transform: translate(5px, 5px); }
    }
    
    .hero-subtitle {
      font-size: 2.5rem;
      color: #fff;
      margin-bottom: 3rem;
      opacity: 0;
      font-weight: 300;
    }
    
    .hero-cta {
      display: inline-block;
      padding: 1.2rem 3rem;
      background: linear-gradient(45deg, #00f2fe, #4facfe);
      color: #fff;
      font-size: 1.5rem;
      text-decoration: none;
      border-radius: 50px;
      font-weight: 700;
      box-shadow: 0 5px 15px rgba(0, 242, 254, 0.4);
      transition: all 0.3s ease;
      opacity: 0;
      transform: translateY(20px);
    }
    
    .hero-cta:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 242, 254, 0.6);
    }
    
    /* About Section */
    #about {
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
    }
    
    .section-title {
      font-family: 'Orbitron', sans-serif;
      font-size: 4rem;
      color: #fff;
      margin-bottom: 3rem;
      position: relative;
      display: inline-block;
    }
    
    .section-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(to right, #00f2fe, #4facfe);
    }
    
    .about-text {
      font-size: 2rem;
      color: #fff;
      max-width: 900px;
      margin: 0 auto 4rem;
      line-height: 1.6;
    }
    
    .timeline {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      margin-top: 4rem;
      width: 100%;
      max-width: 900px;
    }
    
    .timeline-title {
      font-size: 2.5rem;
      color: #fff;
      margin-bottom: 2rem;
    }
    
    .timeline-item {
      display: flex;
      align-items: center;
      gap: 2rem;
      background: rgba(255, 255, 255, 0.1);
      padding: 2rem;
      border-radius: 10px;
      transition: transform 0.3s ease;
    }
    
    .timeline-item:hover {
      transform: scale(1.03);
      background: rgba(255, 255, 255, 0.15);
    }
    
    .timeline-year {
      font-family: 'Orbitron', sans-serif;
      font-size: 2.5rem;
      color: #4facfe;
      min-width: 150px;
      text-align: center;
    }
    
    .timeline-content {
      flex: 1;
      text-align: left;
    }
    
    .timeline-content h3 {
      font-size: 2rem;
      color: #fff;
      margin-bottom: 0.5rem;
    }
    
    .timeline-content p {
      font-size: 1.6rem;
      color: #ccc;
    }
    
    /* Products Section */
    #products {
      background: rgba(0, 0, 0, 0.7);
    }
    
    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
      width: 100%;
    }
    
    .product-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 3rem 2rem;
      text-align: center;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
    }
    
    .product-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, rgba(0, 242, 254, 0.2), rgba(79, 172, 254, 0.2));
      transform: translateY(100%);
      transition: transform 0.4s ease;
      z-index: -1;
    }
    
    .product-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 10px 30px rgba(0, 242, 254, 0.3);
    }
    
    .product-card:hover::before {
      transform: translateY(0);
    }
    
    .product-icon {
      font-size: 3rem;
      color: #4facfe;
      margin-bottom: 2rem;
    }
    
    .product-card h3 {
      font-family: 'Orbitron', sans-serif;
      font-size: 2.2rem;
      color: #fff;
      margin-bottom: 1rem;
    }
    
    .product-card p {
      font-size: 1.6rem;
      color: #ccc;
      margin-bottom: 2rem;
    }
    
    .product-link {
      display: inline-block;
      padding: 0.8rem 2rem;
      background: transparent;
      color: #4facfe;
      font-size: 1.3rem;
      text-decoration: none;
      border-radius: 50px;
      border: 2px solid #4facfe;
      font-weight: 700;
      transition: all 0.3s ease;
    }
    
    .product-link:hover {
      background: #4facfe;
      color: #111;
    }
    
    /* Donation Section */
    #donate {
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
    }
    
    .donate-content {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .donate-text {
      font-size: 2rem;
      color: #fff;
      margin-bottom: 4rem;
      line-height: 1.6;
    }
    
    .donate-btn {
      display: inline-block;
      padding: 1.5rem 4rem;
      background: linear-gradient(45deg, #00f2fe, #4facfe);
      color: #fff;
      font-size: 1.8rem;
      text-decoration: none;
      border-radius: 50px;
      font-weight: 700;
      box-shadow: 0 5px 15px rgba(0, 242, 254, 0.4);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .donate-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, #4facfe, #00f2fe);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
      z-index: -1;
    }
    
    .donate-btn:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 242, 254, 0.6);
    }
    
    .donate-btn:hover::before {
      transform: translateX(0);
    }
    
    /* FAQ Section */
    #faqs {
      background: rgba(0, 0, 0, 0.7);
    }
    
    .faq-container {
      max-width: 900px;
      width: 100%;
      margin-top: 4rem;
    }
    
    .faq-item {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      margin-bottom: 2rem;
      overflow: hidden;
    }
    
    .faq-question {
      padding: 2rem;
      font-size: 1.8rem;
      color: #fff;
      cursor: pointer;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .faq-question::after {
      content: '+';
      font-size: 2.5rem;
      transition: transform 0.3s ease;
    }
    
    .faq-item.active .faq-question::after {
      transform: rotate(45deg);
    }
    
    .faq-answer {
      font-size: 1.6rem;
      color: #ccc;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.6s ease, padding 0.6s ease;
    }
    
    .faq-item.active .faq-answer {
      padding: 0 2rem 2rem;
      max-height: 500px;
    }
    
    /* Footer */
    footer {
      background: rgba(0, 0, 0, 0.9);
      padding: 4rem 2rem;
      text-align: center;
    }
    
    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 3rem;
    }
    
    .footer-logo {
      font-family: 'Orbitron', sans-serif;
      font-size: 2.5rem;
      color: #fff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
    }
    
    .footer-links {
      display: flex;
      gap: 3rem;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .footer-link {
      color: #ccc;
      text-decoration: none;
      font-size: 1.4rem;
      transition: color 0.3s ease;
    }
    
    .footer-link:hover {
      color: #4facfe;
    }
    
    .social-links {
      display: flex;
      gap: 2rem;
      margin: 2rem 0;
    }
    
    .social-link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      font-size: 1.5rem;
      transition: all 0.3s ease;
    }
    
    .social-link:hover {
      background: #4facfe;
      transform: translateY(-5px);
    }
    
    .copyright {
      font-size: 1.2rem;
      color: #666;
      margin-top: 2rem;
    }
    
    /* Responsive Adjustments */
    @media (max-width: 1200px) {
      .hero-title {
        font-size: 6rem;
      }
      
      .section-title {
        font-size: 3.5rem;
      }
    }
    
    @media (max-width: 992px) {
      .nav {
        padding: 1.5rem 2rem;
      }
      
      .hero-title {
        font-size: 5rem;
      }
      
      .hero-subtitle {
        font-size: 2rem;
      }
      
      .section-title {
        font-size: 3rem;
      }
      
      .about-text {
        font-size: 1.8rem;
      }
      
      .timeline-year {
        font-size: 2rem;
        min-width: 100px;
      }
    }
    
    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }
      
      .hero-title {
        font-size: 4rem;
      }
      
      .hero-subtitle {
        font-size: 1.8rem;
      }
      
      .section-title {
        font-size: 2.5rem;
      }
      
      .about-text {
        font-size: 1.6rem;
      }
      
      .timeline-item {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }
      
      .timeline-year {
        min-width: auto;
        margin-bottom: 1rem;
      }
      
      .products-grid {
        grid-template-columns: 1fr;
      }
    }
    
    @media (max-width: 480px) {
      .hero-title {
        font-size: 3rem;
      }
      
      .hero-subtitle {
        font-size: 1.5rem;
      }
      
      .hero-cta {
        font-size: 1.2rem;
        padding: 1rem 2rem;
      }
      
      .section-title {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Particle Background -->
  <div id="particles-js"></div>
  <div class="overlay-bg"></div>
  
  <!-- Navigation -->
  <nav class="nav">
    <a href="#hero" class="logo">SmartBotsHQ</a>
    <div class="nav-links">
      <a href="#about" class="nav-link">About</a>
      <a href="#products" class="nav-link">Products</a>
      <a href="#donate" class="nav-link">Support Us</a>
      <a href="#faqs" class="nav-link">FAQs</a>
    </div>
  </nav>
  
  <!-- Hero Section -->
  <section id="hero" class="section">
    <div class="content-container">
      <h1 class="hero-title" data-text="SmartBotsHQ">SmartBotsHQ</h1>
      <h2 class="hero-subtitle">Empowering Automation with Intelligence</h2>
      <a href="#products" class="hero-cta">Explore Our Bots</a>
    </div>
  </section>
  
  <!-- About Section -->
  <section id="about" class="section">
    <div class="content-container">
      <h2 class="section-title">Our Vision</h2>
      <p class="about-text">At SmartBotsHQ, we're revolutionizing automation by enabling businesses and individuals to achieve more with cutting-edge AI-driven solutions. From simple file conversions to complex enterprise automation, we're building the future of intelligent assistants.</p>
      
      <div class="timeline">
        <h3 class="timeline-title">🚀 Our Journey</h3>
        
        <div class="timeline-item">
          <div class="timeline-year">2023</div>
          <div class="timeline-content">
            <h3>Foundation</h3>
            <p>Launched our first suite of Telegram bots, providing simple but powerful file conversion capabilities to users worldwide.</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-year">2024</div>
          <div class="timeline-content">
            <h3>Expansion</h3>
            <p>Expanded our services to include intelligent stock trading insights and business updates, helping our users make smarter decisions.</p>
          </div>
        </div>
        
        <div class="timeline-item">
          <div class="timeline-year">2025</div>
          <div class="timeline-content">
            <h3>Innovation</h3>
            <p>Scaling up with advanced AI automation solutions for individuals and enterprises, transforming how people interact with technology.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Products Section -->
  <section id="products" class="section">
    <div class="content-container">
      <h2 class="section-title">Our AI-Powered Bots</h2>
      
      <div class="products-grid">
        <div class="product-card">
          <div class="product-icon">📄</div>
          <h3>PDF Converter</h3>
          <p>Transform PDF documents into editable formats, extract text, and merge multiple PDFs into a single document with our intelligent processing.</p>
          <a href="#" class="product-link">Learn More</a>
        </div>
        
        <div class="product-card">
          <div class="product-icon">🎵</div>
          <h3>Audio Converter</h3>
          <p>Convert between various audio formats with high-quality retention, adjust bitrates, and optimize your audio files with AI-enhanced processing.</p>
          <a href="#" class="product-link">Learn More</a>
        </div>
        
        <div class="product-card">
          <div class="product-icon">🎬</div>
          <h3>Video to Audio</h3>
          <p>Extract audio tracks from video files, perfect for creating podcasts from interviews or saving music from video content with crystal clear quality.</p>
          <a href="#" class="product-link">Learn More</a>
        </div>
        
        <div class="product-card">
          <div class="product-icon">📁</div>
          <h3>File Converter</h3>
          <p>Our universal file converter handles document, image, audio, and video formats, providing seamless cross-format conversions for all your needs.</p>
          <a href="#" class="product-link">Learn More</a>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Donation Section -->
  <section id="donate" class="section">
    <div class="content-container">
      <h2 class="section-title">Support Our Mission</h2>
      
      <div class="donate-content">
        <p class="donate-text">Your contributions help us continue developing and improving our AI-powered tools. Every donation directly supports our mission to make intelligent automation accessible to everyone.</p>
        <a href="#" class="donate-btn">Donate via RazorPay</a>
      </div>
    </div>
  </section>
  
  <!-- FAQ Section -->
  <section id="faqs" class="section">
    <div class="content-container">
      <h2 class="section-title">Frequently Asked Questions</h2>
      
      <div class="faq-container">
        <div class="faq-item">
          <div class="faq-question">What do SmartBotsHQ bots do?</div>
          <div class="faq-answer">Our bots automate a wide range of tasks, from simple file conversions to complex data processing. They're designed to save you time and enhance productivity with AI-powered capabilities.</div>
        </div>
        
        <div class="faq-item">
          <div class="faq-question">How do I access your Telegram bots?</div>
          <div class="faq-answer">You can find our bots by searching for them on Telegram. Simply start a chat with the bot of your choice and follow the instructions provided to begin using our services.</div>
        </div>
        
        <div class="faq-item">
          <div class="faq-question">Are your services free to use?</div>
          <div class="faq-answer">We offer both free and premium tiers for our services. Basic functionality is available at no cost, while more advanced features and higher usage limits are available through our premium plans.</div>
        </div>
        
        <div class="faq-item">
          <div class="faq-question">What enterprise solutions do you offer?</div>
          <div class="faq-answer">We provide customized AI automation solutions for businesses of all sizes, including custom bot development, workflow automation, data analysis, and integration with existing business systems.</div>
        </div>
        
        <div class="faq-item">
          <div class="faq-question">How secure are file transfers with your bots?</div>
          <div class="faq-answer">Security is our top priority. All file transfers are encrypted, and we do not store your files longer than necessary for processing. Our systems comply with modern security standards to protect your data.</div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <div class="footer-logo">SmartBotsHQ</div>
      
      <div class="social-links">
        <a href="#" class="social-link">T</a>
        <a href="#" class="social-link">F</a>
        <a href="#" class="social-link">I</a>
        <a href="#" class="social-link">L</a>
      </div>
      
      <div class="footer-links">
        <a href="#" class="footer-link">About</a>
        <a href="#" class="footer-link">Products</a>
        <a href="#" class="footer-link">Support</a>
        <a href="#" class="footer-link">Privacy Policy</a>
        <a href="#" class="footer-link">Terms of Service</a>
        <a href="#" class="footer-link">Contact</a>
      </div>
      
      <p class="copyright">© 2025 SmartBotsHQ. All rights reserved.</p>
    </div>
  </footer>
  
  <!-- Scripts -->
  <script>
    // Initialize particles.js
    particlesJS('particles-js', {
      particles: {
        number: {
          value: 80,
          density: {
            enable: true,
            value_area: 800
          }
        },
        color: {
          value: "#ffffff"
        },
        shape: {
          type: "circle",
          stroke: {
            width: 0,
            color: "#000000"
          },
        },
        opacity: {
          value: 0.5,
          random: true,
          anim: {
            enable: true,
            speed: 1,
            opacity_min: 0.1,
            sync: false
          }
        },
        size: {
          value: 3,
          random: true,
          anim: {
            enable: true,
            speed: 2,
            size_min: 0.1,
            sync: false
          }
        },
        line_linked: {
          enable: true,
          distance: 150,
          color: "#ffffff",
          opacity: 0.4,
          width: 1
        },
        move: {
          enable: true,
          speed: 1,
          direction: "none",
          random: true,
          straight: false,
          out_mode: "out",
          bounce: false,
        }
      },
      interactivity: {
        detect_on: "canvas",
        events: {
          onhover: {
            enable: true,
            mode: "grab"
          },
          onclick: {
            enable: true,
            mode: "push"
          },
          resize: true
        },
        modes: {
          grab: {
            distance: 140,
            line_linked: {
              opacity: 1
            }
          },
          push: {
            particles_nb: 4
          }
        }
      },
      retina_detect: true
    });
    
    // Initialize GSAP animations
    document.addEventListener("DOMContentLoaded", function() {
      // Navbar scroll effect
      window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
          document.querySelector('.nav').classList.add('scrolled');
        } else {
          document.querySelector('.nav').classList.remove('scrolled');
        }
      });
      
      // Hero animations
      gsap.to(".hero-subtitle", {
        opacity: 1,
        duration: 1,
        delay: 0.5
      });
      
      gsap.to(".hero-cta", {
        opacity: 1,
        y: 0,
        duration: 1,
        delay: 1
      });
      
      // ScrollTrigger setup
      gsap.registerPlugin(ScrollTrigger);
      
      // Timeline animation
      gsap.utils.toArray(".timeline-item").forEach((item, i) => {
        gsap.fromTo(item, 
          { opacity: 0, x: -50 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.8,
            scrollTrigger: {
              trigger: item,
              start: "top 80%",
              toggleActions: "play none none none"
            },
            delay: i * 0.2
          }
        );
      });
      
      // Product cards animation
      gsap.utils.toArray(".product-card").forEach((card, i) => {
        // Initial state
        gsap.set(card, {
            y: 50,
            opacity: 0,
            scale: 0.95
        });
  // Create the animation
  gsap.to(card, {
    y: 0,
    opacity: 1,
    scale: 1,
    duration: 0.8,
    ease: "back.out(1.2)",
    scrollTrigger: {
      trigger: card,
      start: "top bottom-=100",
      toggleActions: "play none none reverse"
    },
    delay: i * 0.1 // Stagger effect
  });
  
  // Hover animation
  card.addEventListener("mouseenter", () => {
    gsap.to(card, {
      y: -10,
      scale: 1.03,
      boxShadow: "0 20px 30px rgba(0, 0, 0, 0.1)",
      duration: 0.3,
      ease: "power2.out"
    });
  });
  
  card.addEventListener("mouseleave", () => {
    gsap.to(card, {
      y: 0,
      scale: 1,
      boxShadow: "0 10px 20px rgba(0, 0, 0, 0.05)",
      duration: 0.3,
      ease: "power2.out"
    });
  });
});