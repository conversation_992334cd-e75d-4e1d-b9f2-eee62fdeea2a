/* General Reset */
body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    color: white;
    overflow-x: hidden;
    scroll-behavior: smooth;
    font-size: 22px; /* Increased base font size */
}

/* Background Video */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
#bg-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Content Wrapper */
.content-wrapper {
    position: relative;
    z-index: 10;
}

/* Sections */
.page {
    min-height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px; /* Increased padding */
    text-align: center;
    transition: transform 0.3s ease-out;
}
.page h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 3.5rem; /* Increased */
    text-transform: uppercase;
}
.page p {
    font-size: 1.8rem; /* Increased */
    max-width: 1100px; /* Wider content area */
}

/* Hero Section */
.overlay h1 {
    font-size: 6rem; /* Increased */
    text-transform: uppercase;
    animation: neon-glow 1.5s infinite alternate;
}
@keyframes neon-glow {
    from { text-shadow: 0 0 20px cyan; }
    to { text-shadow: 0 0 40px cyan; }
}

/* Glitch Effect */
.glitch-text {
    position: relative;
    display: inline-block;
    font-size: 4rem; /* Increased */
}
.glitch-text::before, .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.8;
}
.glitch-text::before {
    animation: glitch1 1s infinite linear alternate-reverse;
    color: red;
}
.glitch-text::after {
    animation: glitch2 1s infinite linear alternate-reverse;
    color: blue;
}
@keyframes glitch1 { 0% { transform: translate(-3px, 0); } 100% { transform: translate(3px, 0); } }
@keyframes glitch2 { 0% { transform: translate(3px, 0); } 100% { transform: translate(-3px, 0); } }

/* Product Cards */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Bigger cards */
    gap: 40px;
}
.product-card {
    background: rgba(0, 0, 0, 0.6);
    padding: 40px; /* Increased padding */
    border-radius: 12px;
    font-size: 1.8rem; /* Increased */
    transition: transform 0.3s;
}
.product-card:hover {
    transform: translateY(-8px);
}

/* Donate Button */
.donate-btn {
    background: cyan;
    padding: 25px 50px; /* Bigger button */
    border: none;
    font-size: 1.8rem; /* Increased */
    color: black;
    cursor: pointer;
    border-radius: 10px;
    transition: background 0.3s;
}
.donate-btn:hover {
    background: white;
}

/* FAQ Accordion */
.faq-accordion {
    max-width: 1000px;
    margin: 0 auto;
}
.page h2, .page p {
    color: white !important;
}
.page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.page {
    display: block !important;
    opacity: 1 !important;
}
.page {
    min-height: 100vh;
    margin: 0;
    padding: 50px 0; /* Adjust as needed */
}
.page {
    margin: 0 !important;
    padding: 100px 0;
}