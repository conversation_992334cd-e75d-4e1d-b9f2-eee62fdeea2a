# CHANGELOG.md

# Changelog

All notable changes to the Anus AI project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project structure
- Core engine components
- Agent system framework
- Tool ecosystem foundation
- Model integration interfaces
- User interface components
- Documentation framework
- Project logo and branding

### Changed
- N/A

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- Resolved "ModuleNotFoundError: No module named 'main'" on Windows by moving `main.py` into the `anus` package and updating `setup.py` (Issue #13).

### Security
- N/A

## [0.1.0] - 2025-03-09
- Initial release
- Basic project structure and documentation
