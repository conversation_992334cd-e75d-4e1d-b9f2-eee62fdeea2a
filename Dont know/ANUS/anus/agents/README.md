# Anus Agents Module

This module contains the agent system components of the Anus AI framework, including:

- Single-Agent Mode
- Multi-Agent Collaboration Mode
- Agent Role Definition Framework
- Inter-Agent Communication Protocol

## Components

### base_agent.py
Base class for all agent types with common functionality.

### single_agent.py
Simplified agent implementation for straightforward tasks.

### multi_agent.py
Implementation of multi-agent collaboration system.

### roles.py
Predefined agent role templates and custom role creation capabilities.

### communication.py
Inter-agent communication protocol and message handling.

### registry.py
Agent registration and discovery system.
