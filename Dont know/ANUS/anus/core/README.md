# Anus Core Module

This module contains the core functionality of the Anus AI agent system, including:

- Agent Orchestration System
- Task Planning and Execution Framework
- Memory and Context Management
- Tool Integration Interface

## Components

### orchestrator.py
Manages the lifecycle of agents, handles agent creation, destruction, and resource allocation.

### planner.py
Breaks down complex tasks into manageable steps, assigns steps to appropriate agents or tools.

### memory.py
Maintains short-term and long-term memory, manages conversation history and context.

### tool_manager.py
Provides a standardized API for tool integration, tool discovery and registration system.

### config.py
Configuration management for the core module.

### utils.py
Utility functions used across the core module.
