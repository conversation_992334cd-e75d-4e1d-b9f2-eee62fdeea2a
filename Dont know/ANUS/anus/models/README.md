# Anus Models Module

This module contains the model integration components of the Anus AI framework, including:

- OpenAI API Support
- Open-Source Model Support
- Model Switching and Fallback Mechanisms
- Vision Model Integration

## Components

### base_model.py
Base class for all model integrations with common functionality.

### openai_model.py
Integration with OpenAI API models (GPT-4, etc.).

### open_source_model.py
Integration with open-source models (Llama, Mistral, etc.).

### vision_model.py
Integration with vision models for image understanding.

### model_manager.py
Model selection, switching, and fallback mechanisms.

### config.py
Configuration management for model integrations.
