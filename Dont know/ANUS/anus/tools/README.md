# Anus Tools Module

This module contains the tool ecosystem components of the Anus AI framework, including:

- Web Interaction Tools (Browser Automation)
- Information Retrieval Tools (Search, Wikipedia)
- Document Processing Tools (PDF, Word, Excel)
- Code Execution Environment
- Multimodal Processing (Images, Audio, Video)

## Components

### base_tool.py
Base class for all tools with common functionality and interface.

### web_tools.py
Browser automation using Playwright, web scraping, and data extraction.

### search_tools.py
Search engine integration, Wikipedia access, and information retrieval.

### document_tools.py
PDF parsing, Office document handling, and data extraction.

### code_tools.py
Secure Python execution sandbox and code analysis.

### multimodal_tools.py
Image, audio, and video processing capabilities.

### registry.py
Tool registration and discovery system.
