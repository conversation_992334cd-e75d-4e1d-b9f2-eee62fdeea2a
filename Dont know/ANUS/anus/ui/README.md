# Anus UI Module

This module contains the user interface components of the Anus AI framework, including:

- Command-Line Interface
- Web Interface (Optional)
- API for Integration with Other Systems

## Components

### cli.py
Command-line interface for interacting with the Anus AI agent.

### web_interface.py
Optional web-based user interface for the Anus AI agent.

### api.py
RESTful API for integration with external systems.

### utils.py
Utility functions for UI components.

### config.py
Configuration management for UI components.
