# Examples

This directory contains example use cases and demonstrations of the Anus AI agent framework.

## Basic Examples

- `simple_task.py`: Demonstrates basic usage of the Anus AI agent for simple tasks.
- `web_search.py`: Shows how to use the agent for web search and information retrieval.
- `code_generation.py`: Example of using the agent for code generation and execution.

## Advanced Examples

- `multi_agent_collaboration.py`: Demonstrates the multi-agent collaboration capabilities.
- `document_processing.py`: Shows how to process and analyze documents.
- `browser_automation.py`: Example of browser automation for web tasks.

## Tutorials

- `getting_started.md`: Step-by-step tutorial for getting started with Anus AI.
- `custom_agent.md`: Guide for creating custom agent roles.
- `tool_development.md`: Tutorial for developing custom tools.
