# Tests

This directory contains test cases for the Anus AI agent framework.

## Unit Tests

- `test_core/`: Tests for core module components
- `test_agents/`: Tests for agent system components
- `test_tools/`: Tests for tool ecosystem components
- `test_models/`: Tests for model integration components
- `test_ui/`: Tests for user interface components

## Integration Tests

- `test_integration/`: Tests for integration between different components
- `test_end_to_end/`: End-to-end tests for complete workflows

## Benchmark Tests

- `test_benchmarks/`: Performance and capability benchmark tests
- `test_comparison/`: Comparison tests against other AI agent frameworks
