{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3aed44e1-04d4-49f5-b657-bc76ca0534a2", "metadata": {"execution": {"iopub.execute_input": "2025-03-19T09:44:26.657125Z", "iopub.status.busy": "2025-03-19T09:44:26.656689Z", "iopub.status.idle": "2025-03-19T09:44:27.414915Z", "shell.execute_reply": "2025-03-19T09:44:27.413565Z", "shell.execute_reply.started": "2025-03-19T09:44:26.657093Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" * Serving Flask app '__main__'\n", " * Debug mode: on\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[31m\u001b[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\u001b[0m\n", " * Running on http://127.0.0.1:5000\n", "\u001b[33mPress CTRL+C to quit\u001b[0m\n", " * Restarting with stat\n", "0.00s - Debugger warning: It seems that frozen modules are being used, which may\n", "0.00s - make the debugger miss breakpoints. Please pass -Xfrozen_modules=off\n", "0.00s - to python to disable frozen modules.\n", "0.00s - Note: Debugging will proceed. Set PYDEVD_DISABLE_FILE_VALIDATION=1 to disable this validation.\n", "Traceback (most recent call last):\n", "  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "  File \"<frozen runpy>\", line 88, in _run_code\n", "  File \"/usr/local/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/usr/local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1074, in launch_instance\n", "    app.initialize(argv)\n", "  File \"/usr/local/lib/python3.11/site-packages/traitlets/config/application.py\", line 118, in inner\n", "    return method(app, *args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 692, in initialize\n", "    self.init_sockets()\n", "  File \"/usr/local/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 331, in init_sockets\n", "    self.shell_port = self._bind_socket(self.shell_socket, self.shell_port)\n", "                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 253, in _bind_socket\n", "    return self._try_bind_socket(s, port)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/usr/local/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 229, in _try_bind_socket\n", "    s.bind(\"tcp://%s:%i\" % (self.ip, port))\n", "  File \"/usr/local/lib/python3.11/site-packages/zmq/sugar/socket.py\", line 302, in bind\n", "    super().bind(addr)\n", "  File \"zmq/backend/cython/socket.pyx\", line 564, in zmq.backend.cython.socket.Socket.bind\n", "  File \"zmq/backend/cython/checkrc.pxd\", line 28, in zmq.backend.cython.checkrc._check_rc\n", "zmq.error.ZMQError: Address already in use (addr='tcp://127.0.0.1:53986')\n"]}, {"ename": "SystemExit", "evalue": "1", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\u001b[0;31m:\u001b[0m 1\n"]}], "source": ["from flask import Flask, render_template\n", "\n", "app = Flask(__name__)\n", "\n", "# Route for the home page\n", "@app.route('/')\n", "def home():\n", "    return render_template('home.html')\n", "\n", "# Route for the about page\n", "@app.route('/about')\n", "def about():\n", "    return render_template('about.html')\n", "\n", "# Route for the contact page\n", "@app.route('/contact')\n", "def contact():\n", "    return render_template('contact.html')\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8ea3b3df-e416-4580-a47d-1c11d559662e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}