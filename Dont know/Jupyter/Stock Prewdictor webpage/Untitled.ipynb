{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2c071ed9-a4de-4c12-8184-83ba57dc5980", "metadata": {"execution": {"iopub.execute_input": "2025-03-19T16:44:02.978242Z", "iopub.status.busy": "2025-03-19T16:44:02.977858Z", "iopub.status.idle": "2025-03-19T16:44:03.853723Z", "shell.execute_reply": "2025-03-19T16:44:03.853071Z", "shell.execute_reply.started": "2025-03-19T16:44:02.978209Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Suggested Levels for Tomorrow:\n", "Buy Above: 55.97\n", "Buy Target: 56.94\n", "Buy Stop Loss: 54.04\n", "Sell Below: 51.91\n", "Sell Target: 51.14\n", "Sell Stop Loss: 54.04\n"]}], "source": ["import pandas as pd\n", "\n", "def calculate_levels(open_price, high_price, low_price, close_price):\n", "    # Calculate pivot points\n", "    pivot = (high_price + low_price + close_price) / 3\n", "    resistance1 = (2 * pivot) - low_price\n", "    support1 = (2 * pivot) - high_price\n", "    resistance2 = pivot + (high_price - low_price)\n", "    support2 = pivot - (high_price - low_price)\n", "    \n", "    # Add small volatility buffer\n", "    buffer = (high_price - low_price) * 0.2\n", "    \n", "    buy_price = round(resistance1 + buffer, 2)\n", "    buy_target = round(resistance2, 2)\n", "    buy_sl = round(pivot, 2)\n", "    \n", "    sell_price = round(support1 - buffer, 2)\n", "    sell_target = round(support2, 2)\n", "    sell_sl = round(pivot, 2)\n", "    \n", "    return {\n", "        \"Buy Above\": buy_price,\n", "        \"Buy Target\": buy_target,\n", "        \"Buy Stop Loss\": buy_sl,\n", "        \"Sell Below\": sell_price,\n", "        \"Sell Target\": sell_target,\n", "        \"Sell Stop Loss\": sell_sl\n", "    }\n", "\n", "# Example input for OLAELEC based on your chart (approx values)\n", "open_price = 53.2\n", "high_price = 55.6\n", "low_price = 52.7\n", "close_price = 53.83\n", "\n", "levels = calculate_levels(open_price, high_price, low_price, close_price)\n", "print(\"Suggested Levels for Tomorrow:\")\n", "for k, v in levels.items():\n", "    print(f\"{k}: {v}\")"]}, {"cell_type": "code", "execution_count": null, "id": "12f32cb9-9c83-4a9e-bab7-01622489c9d6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}