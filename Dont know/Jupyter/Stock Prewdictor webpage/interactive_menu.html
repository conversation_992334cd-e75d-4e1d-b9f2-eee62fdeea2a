<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Menu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 50px;
        }
        .menu {
            margin: 20px;
        }
        .menu button {
            font-size: 18px;
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Welcome to My Interactive Page</h1>
    <p>Select an option from the menu below:</p>

    <div class="menu">
        <button onclick="alert('You selected Option 1!')">Option 1</button>
        <button onclick="alert('You selected Option 2!')">Option 2</button>
        <button onclick="alert('You selected Option 3!')">Option 3</button>
    </div>

    <script>
        // You can add more interactivity here if needed
    </script>
</body>
</html>
