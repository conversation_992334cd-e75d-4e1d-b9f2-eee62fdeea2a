{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import plotly.graph_objects as go\n", "import yfinance as yf\n", "import torch\n", "import torch.nn as nn\n", "from sklearn.preprocessing import MinMaxScaler\n", "import os\n", "import json\n", "from IPython.display import display, clear_output\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class LSTMModel(nn.Module):\n", "    def __init__(self, input_size=1, hidden_size=50, num_layers=2):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)\n", "        self.fc1 = nn.Linear(hidden_size, 25)\n", "        self.fc2 = nn.Linear(25, 1)\n", "        \n", "    def forward(self, x):\n", "        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)\n", "        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)\n", "        out, _ = self.lstm(x, (h0, c0))\n", "        out = self.fc1(out[:, -1, :])\n", "        out = self.fc2(out)\n", "        return out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def prepare_lstm_data(data, look_back=60):\n", "    \"\"\"Prepare data for LSTM model.\"\"\"\n", "    # Convert to numpy array if it's a pandas Series\n", "    if isinstance(data, pd.Series):\n", "        data = data.values\n", "    \n", "    # Normalize the data\n", "    data = data.reshape(-1, 1)\n", "    scaler = MinMaxScaler(feature_range=(0, 1))\n", "    data = scaler.fit_transform(data)\n", "    \n", "    X, y = [], []\n", "    for i in range(len(data) - look_back):\n", "        X.append(data[i:(i + look_back)])\n", "        y.append(data[i + look_back])\n", "    \n", "    return np.array(X), np.array(y), scaler\n", "\n", "def train_lstm_model(data):\n", "    \"\"\"Train and save an LSTM model.\"\"\"\n", "    try:\n", "        X, y, scaler = prepare_lstm_data(data)\n", "        X_tensor = torch.FloatTensor(X)\n", "        y_tensor = torch.FloatTensor(y).reshape(-1, 1)\n", "        \n", "        model = LSTMModel()\n", "        criterion = nn.MS<PERSON><PERSON>()\n", "        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "        \n", "        losses = []\n", "        for epoch in range(10):\n", "            model.train()\n", "            optimizer.zero_grad()\n", "            outputs = model(X_tensor)\n", "            loss = criterion(outputs, y_tensor)\n", "            loss.backward()\n", "            optimizer.step()\n", "            losses.append(loss.item())\n", "            print(f'Epoch {epoch+1}/10, Loss: {loss.item():.6f}')\n", "        \n", "        # Plot training loss\n", "        fig = go.Figure()\n", "        fig.add_trace(go.<PERSON>(y=losses, mode='lines', name='Training Loss'))\n", "        fig.update_layout(title='Training Loss Over Time', xaxis_title='Epoch', yaxis_title='Loss')\n", "        fig.show()\n", "        \n", "        # Save the model\n", "        torch.save(model, \"lstm_model.pth\")\n", "        return model, scaler\n", "    except Exception as e:\n", "        print(f\"Error training model: {str(e)}\")\n", "        return None, None\n", "\n", "def predict_with_lstm(model, data, scaler, look_back=60):\n", "    \"\"\"Predict future values using a trained LSTM model.\"\"\"\n", "    try:\n", "        # Convert to numpy array if it's a pandas Series\n", "        if isinstance(data, pd.Series):\n", "            data = data.values\n", "            \n", "        # Normalize the data using the same scaler\n", "        data = scaler.transform(data.reshape(-1, 1))\n", "        \n", "        X, _ = prepare_lstm_data(data, look_back)[:2]  # Only need X, not y or scaler\n", "        X_tensor = torch.FloatTensor(X)\n", "        model.eval()\n", "        with torch.no_grad():\n", "            outputs = model(X_tensor)\n", "        \n", "        # Inverse transform to get actual price predictions\n", "        predictions = scaler.inverse_transform(outputs.numpy())\n", "        return float(predictions[-1][0])\n", "    except Exception as e:\n", "        print(f\"Error making prediction: {str(e)}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# List of stocks to analyze\n", "tickers = [\"RELIANCE.BO\", \"TCS.BO\", \"INFY.BO\", \"HDFCBANK.BO\"]\n", "\n", "# Download historical data and train model\n", "print(\"Downloading historical data...\")\n", "historical_data = yf.download(tickers[0], period=\"1mo\", interval=\"1d\")[\"Close\"]\n", "\n", "print(\"\\nTraining LSTM model...\")\n", "model, scaler = train_lstm_model(historical_data)\n", "\n", "if model is not None:\n", "    print(\"\\nMaking predictions...\")\n", "    predictions = []\n", "    for ticker in tickers:\n", "        data = yf.download(ticker, period=\"1mo\", interval=\"1d\")\n", "        if not data.empty:\n", "            current_price = data[\"Close\"][-1]\n", "            pred_price = predict_with_lstm(model, data[\"Close\"], scaler)\n", "            if pred_price is not None:\n", "                predictions.append({\n", "                    \"Stock\": ticker,\n", "                    \"Current Price\": round(current_price, 2),\n", "                    \"Predicted Price\": round(pred_price, 2),\n", "                    \"Expected Change %\": round((pred_price - current_price) / current_price * 100, 2)\n", "                })\n", "\n", "    # Display predictions\n", "    df = pd.DataFrame(predictions)\n", "    display(df)\n", "\n", "    # Visualize predictions\n", "    fig = go.Figure()\n", "    for _, row in df.iterrows():\n", "        fig.add_trace(go.Bar(\n", "            name=row[\"Stock\"],\n", "            x=[\"Current Price\", \"Predicted Price\"],\n", "            y=[row[\"Current Price\"], row[\"Predicted Price\"]],\n", "            text=[f\"{row['Current Price']:.2f}\", f\"{row['Predicted Price']:.2f}\"],\n", "            textposition='auto',\n", "        ))\n", "\n", "    fig.update_layout(\n", "        title=\"Stock Prices: Current vs Predicted\",\n", "        xaxis_title=\"Price Type\",\n", "        yaxis_title=\"Price (INR)\",\n", "        barmode='group'\n", "    )\n", "    fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}