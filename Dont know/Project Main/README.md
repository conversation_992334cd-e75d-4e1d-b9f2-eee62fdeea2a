# Real-Time Stock Movement Predictor

A sophisticated stock market prediction system that monitors live market data, identifies top movers, and provides real-time browser notifications for potential bullish or bearish movements.

## Features

- Real-time monitoring of top market movers
- Advanced technical analysis using multiple indicators
- Machine learning-based prediction using GradientBoostingClassifier
- Browser-based notifications for high-confidence predictions
- Interactive dashboard with live updates
- Multiple price target levels based on Average True Range (ATR)
- Automatic model retraining
- Historical prediction tracking

## Technical Indicators Used

- RSI (Relative Strength Index)
- MACD (Moving Average Convergence Divergence)
- Bollinger Bands
- On-Balance Volume (OBV)
- Accumulation/Distribution Index (ADI)
- Average Directional Index (ADX)
- TRIX Indicator
- Custom momentum and volatility features

## Setup

1. Create a virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the Streamlit app:
```bash
streamlit run stock_predictor.py
```

The system provides:

1. Live Predictions Tab:
   - Real-time market scanning
   - Auto-refresh option (every 5 minutes)
   - Interactive prediction cards with price charts
   - Color-coded notifications (green for bullish, red for bearish)

2. Historical Predictions Tab:
   - Complete history of all predictions
   - Filter by direction (Bullish/Bearish)
   - Filter by confidence level
   - Detailed view of each prediction

## Dashboard Features

- Live market scanning with auto-refresh option
- Interactive candlestick charts for each prediction
- Color-coded prediction cards
- Price target levels with detailed breakdowns
- Historical tracking and filtering
- Confidence metrics for each prediction

## Important Notes

- The system requires active market hours to function properly
- Predictions are based on technical analysis and machine learning
- Always perform your own due diligence before making trading decisions
- Past performance does not guarantee future results

## License

MIT License
