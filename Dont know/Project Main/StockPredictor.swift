import Foundation
import PythonKit

class StockPredictor {
    private let sys = Python.import("sys")
    private let predictor: PythonObject
    
    init() throws {
        // Add the path to your Python script
        sys.path.append("/Users/<USER>/Project Main")
        
        // Import the Python module
        guard let module = try? Python.import("unified_stock_predictor") else {
            throw NSError(domain: "StockPredictor", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to import Python module"])
        }
        
        predictor = module
    }
    
    func analyzeStocks(symbols: [String]) async throws -> [[String: Any]] {
        let pythonResults = predictor.analyze_stocks(symbols)
        
        // Convert Python results to Swift dictionary
        var results: [[String: Any]] = []
        for result in pythonResults {
            let dict: [String: Any] = [
                "symbol": String(result.symbol) ?? "",
                "current_price": Double(result.current_price) ?? 0.0,
                "predicted_price": Double(result.predicted_price) ?? 0.0,
                "price_change": Double(result.price_change) ?? 0.0,
                "rsi": Double(result.rsi) ?? 0.0,
                "macd": Double(result.macd) ?? 0.0,
                "volume": Double(result.volume) ?? 0.0,
                "timestamp": String(result.timestamp) ?? ""
            ]
            results.append(dict)
        }
        
        return results
    }
}

// Example View Model
class StockPredictorViewModel: ObservableObject {
    @Published var predictions: [[String: Any]] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let predictor: StockPredictor
    
    init() throws {
        predictor = try StockPredictor()
    }
    
    func fetchPredictions(for symbols: [String]) {
        isLoading = true
        
        Task {
            do {
                let results = try await predictor.analyzeStocks(symbols: symbols)
                DispatchQueue.main.async {
                    self.predictions = results
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.error = error
                    self.isLoading = false
                }
            }
        }
    }
}
