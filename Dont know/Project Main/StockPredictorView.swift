import SwiftUI

struct StockPredictionView: View {
    @StateObject private var viewModel: StockPredictorViewModel
    @State private var symbols = "RELIANCE.NS,TCS.NS,INFY.NS"
    
    init() {
        do {
            _viewModel = StateObject(wrappedValue: try StockPredictorViewModel())
        } catch {
            fatalError("Failed to initialize StockPredictorViewModel: \(error)")
        }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                TextField("Enter stock symbols (comma-separated)", text: $symbols)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()
                
                Button(action: {
                    let symbolList = symbols.split(separator: ",").map { String($0.trimmingCharacters(in: .whitespaces)) }
                    viewModel.fetchPredictions(for: symbolList)
                }) {
                    Text("Analyze Stocks")
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                
                if viewModel.isLoading {
                    ProgressView()
                        .padding()
                } else {
                    List(viewModel.predictions, id: \.self) { prediction in
                        VStack(alignment: .leading) {
                            Text(prediction["symbol"] as? String ?? "")
                                .font(.headline)
                            
                            HStack {
                                Text("Current: ₹\(String(format: "%.2f", prediction["current_price"] as? Double ?? 0))")
                                Spacer()
                                Text("Predicted: ₹\(String(format: "%.2f", prediction["predicted_price"] as? Double ?? 0))")
                            }
                            .font(.subheadline)
                            
                            HStack {
                                Text("RSI: \(String(format: "%.1f", prediction["rsi"] as? Double ?? 0))")
                                Spacer()
                                Text("MACD: \(String(format: "%.2f", prediction["macd"] as? Double ?? 0))")
                            }
                            .font(.caption)
                            
                            Text("Change: \(String(format: "%.1f", prediction["price_change"] as? Double ?? 0))%")
                                .foregroundColor((prediction["price_change"] as? Double ?? 0) >= 0 ? .green : .red)
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("Stock Predictor")
            .alert("Error", isPresented: Binding(
                get: { viewModel.error != nil },
                set: { if !$0 { viewModel.error = nil } }
            )) {
                Text(viewModel.error?.localizedDescription ?? "Unknown error")
            }
        }
    }
}

#Preview {
    StockPredictionView()
}
