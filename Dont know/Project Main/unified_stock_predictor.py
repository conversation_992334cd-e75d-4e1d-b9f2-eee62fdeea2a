import pandas as pd
import numpy as np
from datetime import datetime
import yfinance as yf
from sklearn.preprocessing import MinMaxScaler
import torch
import torch.nn as nn
import ta
import json
import os
from concurrent.futures import ThreadPoolExecutor
import pytz

class LSTMModel(nn.Module):
    def __init__(self, input_size=1, hidden_size=50, num_layers=2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.fc1 = nn.Linear(hidden_size, 25)
        self.fc2 = nn.Linear(25, 1)
        
    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc1(out[:, -1, :])
        out = self.fc2(out)
        return out

class UnifiedStockPredictor:
    def __init__(self):
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.ist = pytz.timezone('Asia/Kolkata')
        self.predictions_file = "predictions_history.json"
        
    def fetch_stock_data(self, symbol, period="1mo", interval="1d"):
        """Fetch stock data for a single symbol"""
        try:
            stock = yf.Ticker(symbol)
            hist = stock.history(period=period, interval=interval)
            return hist
        except Exception as e:
            print(f"Error fetching data for {symbol}: {str(e)}")
            return None

    def prepare_features(self, df):
        """Prepare technical indicators and features"""
        # Price-based indicators
        df['RSI'] = ta.momentum.RSIIndicator(df['Close']).rsi()
        df['MACD'] = ta.trend.MACD(df['Close']).macd()
        df['MACD_Signal'] = ta.trend.MACD(df['Close']).macd_signal()
        df['BB_upper'], df['BB_middle'], df['BB_lower'] = ta.volatility.BollingerBands(df['Close']).bollinger_bands()
        
        # Volume-based indicators
        df['OBV'] = ta.volume.OnBalanceVolumeIndicator(df['Close'], df['Volume']).on_balance_volume()
        df['CMF'] = ta.volume.ChaikinMoneyFlowIndicator(df['High'], df['Low'], df['Close'], df['Volume']).chaikin_money_flow()
        
        # Custom features
        df['Price_Range'] = df['High'] - df['Low']
        df['Price_Change'] = df['Close'].pct_change()
        df['Volume_Change'] = df['Volume'].pct_change()
        
        return df.fillna(0)

    def prepare_lstm_data(self, data, look_back=60):
        """Prepare data for LSTM model"""
        if isinstance(data, pd.Series):
            data = data.values
        data = data.reshape(-1, 1)
        data_scaled = self.scaler.fit_transform(data)
        
        X, y = [], []
        for i in range(len(data_scaled) - look_back):
            X.append(data_scaled[i:(i + look_back)])
            y.append(data_scaled[i + look_back])
        
        return np.array(X), np.array(y)

    def train_model(self, data):
        """Train LSTM model"""
        X, y = self.prepare_lstm_data(data)
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y)
        
        self.model = LSTMModel()
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
        
        self.model.train()
        for epoch in range(10):
            optimizer.zero_grad()
            outputs = self.model(X_tensor)
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()
        
        # Save the model
        torch.save(self.model.state_dict(), 'lstm_model.pth')
        return self.model

    def predict(self, data, look_back=60):
        """Make predictions using the trained model"""
        if self.model is None:
            if os.path.exists('lstm_model.pth'):
                self.model = LSTMModel()
                self.model.load_state_dict(torch.load('lstm_model.pth'))
            else:
                self.train_model(data)
        
        X, _ = self.prepare_lstm_data(data, look_back)
        X_tensor = torch.FloatTensor(X)
        
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor)
        
        predictions = self.scaler.inverse_transform(predictions.numpy())
        return predictions

    def save_prediction(self, symbol, prediction, actual=None):
        """Save prediction to history"""
        if os.path.exists(self.predictions_file):
            with open(self.predictions_file, 'r') as f:
                history = json.load(f)
        else:
            history = []
        
        prediction_data = {
            'timestamp': datetime.now(self.ist).isoformat(),
            'symbol': symbol,
            'predicted_value': float(prediction),
            'actual_value': float(actual) if actual is not None else None
        }
        
        history.append(prediction_data)
        with open(self.predictions_file, 'w') as f:
            json.dump(history, f, indent=4)

    def get_prediction_history(self):
        """Load prediction history"""
        if os.path.exists(self.predictions_file):
            with open(self.predictions_file, 'r') as f:
                return json.load(f)
        return []

    def analyze_stock(self, symbol):
        """Complete analysis for a single stock"""
        data = self.fetch_stock_data(symbol)
        if data is None or data.empty:
            return None
        
        # Prepare features
        data_with_features = self.prepare_features(data)
        
        # Make prediction
        latest_prediction = self.predict(data['Close'])[-1][0]
        
        # Save prediction
        self.save_prediction(symbol, latest_prediction, data['Close'].iloc[-1])
        
        return {
            'symbol': symbol,
            'current_price': data['Close'].iloc[-1],
            'predicted_price': latest_prediction,
            'price_change': ((data['Close'].iloc[-1] - data['Close'].iloc[0]) / data['Close'].iloc[0]) * 100,
            'rsi': data_with_features['RSI'].iloc[-1],
            'macd': data_with_features['MACD'].iloc[-1],
            'volume': data['Volume'].iloc[-1],
            'timestamp': datetime.now(self.ist).isoformat()
        }

# Function to be called from Swift
def analyze_stocks(symbols):
    """Main function to be called from Swift using PythonKit"""
    predictor = UnifiedStockPredictor()
    results = []
    
    for symbol in symbols:
        result = predictor.analyze_stock(symbol)
        if result:
            results.append(result)
    
    return results

# Example usage (when running directly in Python)
if __name__ == "__main__":
    symbols = ['RELIANCE.NS', 'TCS.NS', 'INFY.NS']
    results = analyze_stocks(symbols)
    print(json.dumps(results, indent=2))
