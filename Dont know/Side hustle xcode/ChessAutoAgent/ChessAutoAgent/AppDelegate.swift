import Swift
import Cocoa

@NSApplicationMain
class AppDelegate: NSObject, NSApplicationDelegate {
    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Start automation logic after launch
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            ChessLogic.shared.run()
        }
    }

    func applicationWillTerminate(_ aNotification: Notification) {}
}
