import swift
import Foundation
import Cocoa

class ChessLogic {
    static let shared = ChessLogic()

    func run() {
        print("Capturing screen...")
        guard let image = captureScreen() else {
            print("Failed to capture screen")
            return
        }
        // Save or analyze image as needed

        // Example: click center of screen (for now)
        let screenSize = NSScreen.main?.frame.size ?? .zero
        let centerPoint = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
        clickAt(point: centerPoint)
        print("Clicked at center of screen")
    }

    private func captureScreen() -> NSImage? {
        guard let cgImage = CGDisplayCreateImage(CGMainDisplayID()) else { return nil }
        return NSImage(cgImage: cgImage, size: NSZeroSize)
    }

    private func clickAt(point: CGPoint) {
        let move = CGEvent(mouseEventSource: nil, mouseType: .mouseMoved, mouseCursorPosition: point, mouseButton: .left)
        let clickDown = CGEvent(mouseEventSource: nil, mouseType: .leftMouseDown, mouseCursorPosition: point, mouseButton: .left)
        let clickUp = CGEvent(mouseEventSource: nil, mouseType: .leftMouseUp, mouseCursorPosition: point, mouseButton: .left)

        move?.post(tap: .cghidEventTap)
        clickDown?.post(tap: .cghidEventTap)
        clickUp?.post(tap: .cghidEventTap)
    }
}
