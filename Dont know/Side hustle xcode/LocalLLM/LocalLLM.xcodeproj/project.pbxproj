// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		04F231022D9E99F4006ADB1D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04F230EB2D9E99F1006ADB1D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04F230F22D9E99F1006ADB1D;
			remoteInfo = LocalLLM;
		};
		04F2310C2D9E99F4006ADB1D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04F230EB2D9E99F1006ADB1D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04F230F22D9E99F1006ADB1D;
			remoteInfo = LocalLLM;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		04F230F32D9E99F1006ADB1D /* LocalLLM.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LocalLLM.app; sourceTree = BUILT_PRODUCTS_DIR; };
		04F231012D9E99F4006ADB1D /* LocalLLMTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LocalLLMTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		04F2310B2D9E99F4006ADB1D /* LocalLLMUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LocalLLMUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		04F230F52D9E99F1006ADB1D /* LocalLLM */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LocalLLM;
			sourceTree = "<group>";
		};
		04F231042D9E99F4006ADB1D /* LocalLLMTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LocalLLMTests;
			sourceTree = "<group>";
		};
		04F2310E2D9E99F4006ADB1D /* LocalLLMUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LocalLLMUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		04F230F02D9E99F1006ADB1D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F230FE2D9E99F4006ADB1D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F231082D9E99F4006ADB1D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04F230EA2D9E99F1006ADB1D = {
			isa = PBXGroup;
			children = (
				04F230F52D9E99F1006ADB1D /* LocalLLM */,
				04F231042D9E99F4006ADB1D /* LocalLLMTests */,
				04F2310E2D9E99F4006ADB1D /* LocalLLMUITests */,
				04F230F42D9E99F1006ADB1D /* Products */,
			);
			sourceTree = "<group>";
		};
		04F230F42D9E99F1006ADB1D /* Products */ = {
			isa = PBXGroup;
			children = (
				04F230F32D9E99F1006ADB1D /* LocalLLM.app */,
				04F231012D9E99F4006ADB1D /* LocalLLMTests.xctest */,
				04F2310B2D9E99F4006ADB1D /* LocalLLMUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		04F230F22D9E99F1006ADB1D /* LocalLLM */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04F231152D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLM" */;
			buildPhases = (
				04F230EF2D9E99F1006ADB1D /* Sources */,
				04F230F02D9E99F1006ADB1D /* Frameworks */,
				04F230F12D9E99F1006ADB1D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				04F230F52D9E99F1006ADB1D /* LocalLLM */,
			);
			name = LocalLLM;
			packageProductDependencies = (
			);
			productName = LocalLLM;
			productReference = 04F230F32D9E99F1006ADB1D /* LocalLLM.app */;
			productType = "com.apple.product-type.application";
		};
		04F231002D9E99F4006ADB1D /* LocalLLMTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04F231182D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLMTests" */;
			buildPhases = (
				04F230FD2D9E99F4006ADB1D /* Sources */,
				04F230FE2D9E99F4006ADB1D /* Frameworks */,
				04F230FF2D9E99F4006ADB1D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04F231032D9E99F4006ADB1D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04F231042D9E99F4006ADB1D /* LocalLLMTests */,
			);
			name = LocalLLMTests;
			packageProductDependencies = (
			);
			productName = LocalLLMTests;
			productReference = 04F231012D9E99F4006ADB1D /* LocalLLMTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		04F2310A2D9E99F4006ADB1D /* LocalLLMUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04F2311B2D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLMUITests" */;
			buildPhases = (
				04F231072D9E99F4006ADB1D /* Sources */,
				04F231082D9E99F4006ADB1D /* Frameworks */,
				04F231092D9E99F4006ADB1D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04F2310D2D9E99F4006ADB1D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04F2310E2D9E99F4006ADB1D /* LocalLLMUITests */,
			);
			name = LocalLLMUITests;
			packageProductDependencies = (
			);
			productName = LocalLLMUITests;
			productReference = 04F2310B2D9E99F4006ADB1D /* LocalLLMUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		04F230EB2D9E99F1006ADB1D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					04F230F22D9E99F1006ADB1D = {
						CreatedOnToolsVersion = 16.3;
					};
					04F231002D9E99F4006ADB1D = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 04F230F22D9E99F1006ADB1D;
					};
					04F2310A2D9E99F4006ADB1D = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 04F230F22D9E99F1006ADB1D;
					};
				};
			};
			buildConfigurationList = 04F230EE2D9E99F1006ADB1D /* Build configuration list for PBXProject "LocalLLM" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 04F230EA2D9E99F1006ADB1D;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 04F230F42D9E99F1006ADB1D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				04F230F22D9E99F1006ADB1D /* LocalLLM */,
				04F231002D9E99F4006ADB1D /* LocalLLMTests */,
				04F2310A2D9E99F4006ADB1D /* LocalLLMUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		04F230F12D9E99F1006ADB1D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F230FF2D9E99F4006ADB1D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F231092D9E99F4006ADB1D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		04F230EF2D9E99F1006ADB1D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F230FD2D9E99F4006ADB1D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04F231072D9E99F4006ADB1D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		04F231032D9E99F4006ADB1D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04F230F22D9E99F1006ADB1D /* LocalLLM */;
			targetProxy = 04F231022D9E99F4006ADB1D /* PBXContainerItemProxy */;
		};
		04F2310D2D9E99F4006ADB1D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04F230F22D9E99F1006ADB1D /* LocalLLM */;
			targetProxy = 04F2310C2D9E99F4006ADB1D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		04F231132D9E99F4006ADB1D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		04F231142D9E99F4006ADB1D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		04F231162D9E99F4006ADB1D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LocalLLM/LocalLLM.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LocalLLM/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = NO;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = NO;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = NO;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = NO;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 13.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLM;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		04F231172D9E99F4006ADB1D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LocalLLM/LocalLLM.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LocalLLM/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = NO;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = NO;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = NO;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = NO;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 13.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLM;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		04F231192D9E99F4006ADB1D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLMTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LocalLLM.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LocalLLM";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		04F2311A2D9E99F4006ADB1D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLMTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LocalLLM.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LocalLLM";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		04F2311C2D9E99F4006ADB1D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLMUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LocalLLM;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		04F2311D2D9E99F4006ADB1D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.LocalLLMUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LocalLLM;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		04F230EE2D9E99F1006ADB1D /* Build configuration list for PBXProject "LocalLLM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04F231132D9E99F4006ADB1D /* Debug */,
				04F231142D9E99F4006ADB1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04F231152D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04F231162D9E99F4006ADB1D /* Debug */,
				04F231172D9E99F4006ADB1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04F231182D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLMTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04F231192D9E99F4006ADB1D /* Debug */,
				04F2311A2D9E99F4006ADB1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04F2311B2D9E99F4006ADB1D /* Build configuration list for PBXNativeTarget "LocalLLMUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04F2311C2D9E99F4006ADB1D /* Debug */,
				04F2311D2D9E99F4006ADB1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 04F230EB2D9E99F1006ADB1D /* Project object */;
}
