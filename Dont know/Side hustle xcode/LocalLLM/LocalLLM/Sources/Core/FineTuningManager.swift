//
//  FineTuningManager.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//


import Foundation

/// Stores chat history for fine-tuning.
class FineTuningManager {
    static let shared = FineTuningManager()
    
    private init() {}

    private var interactionHistory: [(query: String, response: String)] = []
    
    /// Saves interaction for later fine-tuning.
    func addInteraction(query: String, response: String) {
        interactionHistory.append((query, response))
    }

    /// Displays stored interactions for debugging.
    func printHistory() {
        print("Fine-Tuning Data:", interactionHistory)
    }
}
