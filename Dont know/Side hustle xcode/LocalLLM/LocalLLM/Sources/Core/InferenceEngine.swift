//
//  InferenceService.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//

import Foundation

class InferenceEngine {
    static let shared = InferenceEngine()
    
    private var loadedModel: String?

    private init() {}

    /// Load a model for inference
    func loadModel(named modelName: String) -> Bool {
        self.loadedModel = modelName
        print("Model loaded: \(modelName)")
        return true
    }
    

    /// Run inference using Ollama CLI
    func runInference(on inputText: String) -> String {
        guard let modelName = loadedModel else {
            return "Error: No model loaded."
        }

        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/local/bin/ollama") // Ensure ollama is installed
        process.arguments = ["run", modelName, inputText]

        let outputPipe = Pipe()
        process.standardOutput = outputPipe
        process.standardError = outputPipe

        do {
            try process.run()
        } catch {
            return "Error: Unable to run model."
        }

        let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()
        return String(data: outputData, encoding: .utf8) ?? "Error: Invalid response."
    }
}
