//
//  ModelManager.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//

import Foundation
import AppKit // Required for macOS file handling

class ModelManager: ObservableObject {
    static let shared = ModelManager()

    @Published var availableModels: [String] = []

    private let ollamaPath = "/usr/local/bin/ollama" // Ensure this path is correct
    private let modelsDirectory = FileManager.default.homeDirectoryForCurrentUser.appendingPathComponent(".ollama/models")

    private init() {
        listModels()
    }

    // List all available models from Ollama
    func listModels() {
        let task = Process()
        task.launchPath = "/usr/bin/env"
        task.arguments = ["ollama", "list"]

        let pipe = Pipe()
        task.standardOutput = pipe
        task.launch()
        
        let data = pipe.fileHandleForReading.readDataToEndOfFile()
        task.waitUntilExit()
        
        if let output = String(data: data, encoding: .utf8) {
            let lines = output.split(separator: "\n").dropFirst()
            availableModels = lines.map { String($0.split(separator: " ")[0]) }
        }
    }

    // Load a model
    func loadModel(named model: String) -> Bool {
        let task = Process()
        task.launchPath = "/usr/bin/env"
        task.arguments = ["ollama", "pull", model]

        let pipe = Pipe()
        task.standardOutput = pipe
        task.launch()
        task.waitUntilExit()
        
        return task.terminationStatus == 0
    }

    // Upload a model (copy file to the correct location)
    func uploadModel(from url: URL) -> Bool {
        let destination = modelsDirectory.appendingPathComponent(url.lastPathComponent)
        
        do {
            try FileManager.default.copyItem(at: url, to: destination)
            listModels() // Refresh the model list
            return true
        } catch {
            print("Error uploading model: \(error)")
            return false
        }
    }

    // Delete a model
    func deleteModel(named model: String) -> Bool {
        let modelPath = modelsDirectory.appendingPathComponent(model)
        
        do {
            try FileManager.default.removeItem(at: modelPath)
            listModels() // Refresh the list
            return true
        } catch {
            print("Error deleting model: \(error)")
            return false
        }
    }
}
