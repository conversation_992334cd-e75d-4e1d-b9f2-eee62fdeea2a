//
//  ChatViewModel.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//

import SwiftUI

struct ChatView: View {
    @State private var selectedModel: String = ""
    @State private var userMessage: String = ""
    @State private var chatMessages: [(String, String)] = []

    var body: some View {
        VStack {
            ScrollView {
                VStack(alignment: .leading) {
                    ForEach(chatMessages, id: \.0) { message in
                        HStack {
                            Text("You: \(message.0)")
                                .padding()
                                .background(Color.blue.opacity(0.2))
                                .cornerRadius(8)
                            Spacer()
                        }

                        HStack {
                            Text("AI: \(message.1)")
                                .padding()
                                .background(Color.gray.opacity(0.2))
                                .cornerRadius(8)
                            Spacer()
                        }
                    }
                }
                .padding()
            }

            HStack {
                TextField("Type a message...", text: $userMessage)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(minHeight: 30)

                But<PERSON>("Send") {
                    sendMessage()
                }
                .padding()
            }
            .padding()
        }
    }

    private func sendMessage() {
        guard !selectedModel.isEmpty, !userMessage.isEmpty else { return }

        let response = generateResponse(from: userMessage, using: selectedModel)
        chatMessages.append((userMessage, response))
        userMessage = ""
    }

    private func generateResponse(from message: String, using model: String) -> String {
        let task = Process()
        task.launchPath = "/usr/bin/env"
        task.arguments = ["ollama", "run", model, message]

        let pipe = Pipe()
        task.standardOutput = pipe
        task.launch()
        task.waitUntilExit()

        let data = pipe.fileHandleForReading.readDataToEndOfFile()
        return String(data: data, encoding: .utf8) ?? "Error: No response"
    }
}
