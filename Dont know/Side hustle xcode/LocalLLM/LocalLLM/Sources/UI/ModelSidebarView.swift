//
//  ModelSidebarView.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//


import SwiftUI

struct ModelSidebarView: View {
    @Binding var selectedModel: String
    @ObservedObject private var modelManager = ModelManager.shared

    var body: some View {
        VStack(alignment: .leading) {
            Text("Models")
                .font(.headline)
                .padding(.top)

            List {
                ForEach(modelManager.availableModels, id: \.self) { model in
                    HStack {
                        Button(action: {
                            selectedModel = model
                            if modelManager.loadModel(named: model) {
                                print("\(model) loaded successfully.")
                            } else {
                                print("Failed to load \(model).")
                            }
                        }) {
                            Text(model)
                                .foregroundColor(selectedModel == model ? .blue : .primary)
                        }
                        
                        Spacer()

                        But<PERSON>(action: {
                            if modelManager.deleteModel(named: model) {
                                print("\(model) deleted.")
                            } else {
                                print("Failed to delete \(model).")
                            }
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                    }
                }
            }
            .onAppear {
                model<PERSON>anager.listModels()
            }
        }
        .frame(minWidth: 200, maxWidth: 250)
    }
}
