//
//  SettingsView.swift
//  LocalLLM
//
//  Created by <PERSON><PERSON><PERSON>  on 03/04/25.
//


import SwiftUI
import UniformTypeIdentifiers

struct SettingsView: View {
    @ObservedObject private var modelManager = ModelManager.shared
    @State private var uploadedModelName: String = ""
    
    var body: some View {
        VStack {
            Button("Upload Model") {
                showDocumentPicker()
            }
            .padding()
            .background(Color.green)
            .foregroundColor(.white)
            .cornerRadius(8)
            
            Text(uploadedModelName)
                .foregroundColor(.gray)
                .padding()
        }
        .padding()
    }
    
    // Open file picker
    private func showDocumentPicker() {
#if os(iOS)
        let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [.item])
        documentPicker.allowsMultipleSelection = false
        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
            scene.windows.first?.rootViewController?.present(documentPicker, animated: true)
        }
#elseif os(macOS)
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [UTType.item] // ✅ FIX: Use UTType.item directly
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK, let url = panel.url {
            handleFileSelection(url)
        }
        private func handleFileSelection(_ url: URL) {
            if modelManager.uploadModel(from: url) {
                uploadedModelName = "Uploaded: \(url.lastPathComponent)"
            } else {
                uploadedModelName = "Upload failed"
            }
        }
    }
}
