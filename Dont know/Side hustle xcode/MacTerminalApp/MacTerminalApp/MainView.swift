//
//  ContentView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//

import SwiftUI

struct MainView: View {
    var body: some View {
        VStack {
            HStack {
                ModelSidebarView()  // ✅ Ensure this file exists
                    .frame(minWidth: 200, maxWidth: 250)
                
                ChatView()  // ✅ Ensure this file exists
                    .frame(minWidth: 500, maxWidth: .infinity)
            }
            
            TerminalView()  // ✅ This should now work correctly
                .frame(height: 200)
                .background(Color.black)
        }
        .frame(minWidth: 800, minHeight: 600)
    }
}
