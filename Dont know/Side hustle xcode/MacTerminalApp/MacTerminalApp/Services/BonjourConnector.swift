//
//  BonjourConnector.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import Foundation
import Network

class BonjourConnector: ObservableObject {
    private var browser: NWBrowser?

    func startBrowsing() {
        let params = NWParameters.tcp
        params.includePeerToPeer = true
        let browser = NWBrowser(for: .bonjour(type: "MacTerminalApp._tcp", domain: nil), using: params)

        browser.browseResultsChangedHandler = { results, _ in
            for result in results {
                switch result.endpoint {
                case let .service(name: name, _, _, _):
                    print("Discovered service: \(name)")
                default:
                    break
                }
            }
        }

        browser.start(queue: .main)
        self.browser = browser
    }
}
