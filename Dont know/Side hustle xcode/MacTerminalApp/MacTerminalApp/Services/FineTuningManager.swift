//
//  FineTuningManager.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 05/04/25.
//


import Foundation

class FineTuningManager {
    static let shared = FineTuningManager()

    private var trainingData: [String] = []

    func addTrainingExample(_ example: String) {
        trainingData.append(example)
    }

    func saveTrainingData(to file: URL) {
        do {
            let data = trainingData.joined(separator: "\n").data(using: .utf8)
            try data?.write(to: file)
        } catch {
            print("Error saving training data: \(error)")
        }
    }
}
