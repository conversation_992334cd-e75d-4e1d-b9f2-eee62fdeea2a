//
//  InferenceEngine.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 05/04/25.
//


import Foundation

class InferenceEngine {
    static let shared = InferenceEngine()

    func runModel(_ model: String, withInput input: String, completion: @escaping (String) -> Void) {
        let process = Process()
        let pipe = Pipe()
        process.standardOutput = pipe
        process.launchPath = "/usr/local/bin/ollama"
        process.arguments = ["run", model, input]

        process.terminationHandler = { _ in
            let outputData = pipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: outputData, encoding: .utf8) ?? "Error in inference"
            DispatchQueue.main.async {
                completion(output)
            }
        }
        do {
            try process.run()
        } catch {
            print("Failed to run inference: \(error)")
            completion("Error in inference")
        }
    }
}
