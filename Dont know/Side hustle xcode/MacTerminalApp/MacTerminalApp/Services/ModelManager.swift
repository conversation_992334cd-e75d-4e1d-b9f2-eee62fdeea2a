//
//  ModelManager.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 05/04/25.
//


import Foundation

class ModelManager: ObservableObject {
    static let shared = ModelManager()
    
    @Published var models: [String] = [] // List of available models

    private let modelsPath = FileManager.default.homeDirectoryForCurrentUser.appendingPathComponent(".ollama/models")

    init() {
        loadModels()
    }

    func loadModels() {
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: modelsPath, includingPropertiesForKeys: nil)
            models = contents.map { $0.lastPathComponent }.filter { $0.hasSuffix(".gguf") || $0.hasSuffix(".bin") }
        } catch {
            print("Error loading models: \(error)")
        }
    }

    func deleteModel(named modelName: String) {
        let modelURL = modelsPath.appendingPathComponent(modelName)
        do {
            try FileManager.default.removeItem(at: modelURL)
            models.removeAll { $0 == modelN<PERSON> }
        } catch {
            print("Error deleting model: \(error)")
        }
    }

    func useModel(named modelName: String, input: String, completion: @escaping (String) -> Void) {
        let process = Process()
        let pipe = Pipe()
        process.standardOutput = pipe
        process.launchPath = "/usr/local/bin/ollama"
        process.arguments = ["run", modelName, input]

        process.terminationHandler = { _ in
            let outputData = pipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: outputData, encoding: .utf8) ?? "Error running model"
            DispatchQueue.main.async {
                completion(output)
            }
        }
        do {
            try process.run()
        } catch {
            print("Error running model: \(error)")
            completion("Error running model")
        }
    }
}
