//
//  ChatView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct ChatView: View {
    @State private var fileContents: String = "// Select a file to view its contents"

    var body: some View {
        TextEditor(text: $fileContents)
            .font(.system(.body, design: .monospaced))
            .onReceive(NotificationCenter.default.publisher(for: .fileSelected)) { notification in
                if let fileURL = notification.object as? URL {
                    loadFileContents(from: fileURL)
                }
            }
    }

    private func loadFileContents(from url: URL) {
        do {
            let contents = try String(contentsOf: url)
            DispatchQueue.main.async {
                fileContents = contents
            }
        } catch {
            fileContents = "Failed to load file."
        }
    }
}
