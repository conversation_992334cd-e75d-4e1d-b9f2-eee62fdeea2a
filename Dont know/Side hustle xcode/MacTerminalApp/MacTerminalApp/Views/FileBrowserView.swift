//
//  FileBrowserView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct FileBrowserView: View {
    @State private var files: [String] = []
    private let fileManager = FileManager.default
    private let homeDirectory = FileManager.default.homeDirectoryForCurrentUser

    var body: some View {
        List(files, id: \.self) { file in
            Button(action: { openFile(file) }) {
                Text(file)
            }
        }
        .onAppear(perform: loadFiles)
    }

    private func loadFiles() {
        do {
            let contents = try fileManager.contentsOfDirectory(atPath: homeDirectory.path)
            files = contents
        } catch {
            print("Error loading files: \(error)")
        }
    }

    private func openFile(_ file: String) {
        let fileURL = homeDirectory.appendingPathComponent(file)
        NotificationCenter.default.post(name: .fileSelected, object: fileURL)
    }
}

extension Notification.Name {
    static let fileSelected = Notification.Name("fileSelected")
}