//
//  ModelSidebarView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 05/04/25.
//


import SwiftUI

struct ModelSidebarView: View {
    @ObservedObject var modelManager = ModelManager.shared

    var body: some View {
        List {
            ForEach(modelManager.models, id: \.self) { model in
                Text(model)
                    .onTapGesture {
                        print("Selected model: \(model)")
                    }
            }
        }
        .onAppear {
            modelManager.loadModels()
        }
    }
}
