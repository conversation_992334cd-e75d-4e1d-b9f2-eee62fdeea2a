//
//  SidebarView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct SidebarView: View {
    @State private var files: [String] = []
    
    var body: some View {
        List(files, id: \.self) { file in
            Text(file)
                .onTapGesture { openFile(file) }
        }
        .frame(minWidth: 250)
        .onAppear(perform: loadFiles)
    }
    
    func loadFiles() {
        let fm = FileManager.default
        let path = NSHomeDirectory()
        do {
            files = try fm.contentsOfDirectory(atPath: path)
        } catch {
            print("Error loading files")
        }
    }
    
    func openFile(_ file: String) {
        print("Open \(file)")
    }
}