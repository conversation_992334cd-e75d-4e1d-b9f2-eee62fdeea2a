//
//  TerminalView.swift
//  MacTerminalApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct TerminalView: View {
    @State private var output: String = "Terminal Output\n"

    var body: some View {
        VStack {
            ScrollView {
                Text(output)
                    .font(.system(.body, design: .monospaced))
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .background(Color.black)
            .foregroundColor(.green)

            TextField("Enter command...", text: $output)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding()
        }
        .frame(height: 200)
    }
}
