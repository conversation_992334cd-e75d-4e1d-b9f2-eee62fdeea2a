// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0466FF3E2D0F1E140071DFE3 /* PythonKit in Frameworks */ = {isa = PBXBuildFile; productRef = 0466FF3D2D0F1E140071DFE3 /* PythonKit */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		044CDEEB2D0BFB6F0097D114 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 044CDED12D0BFB6C0097D114 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 044CDED82D0BFB6C0097D114;
			remoteInfo = "Side Hustle";
		};
		044CDEF52D0BFB6F0097D114 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 044CDED12D0BFB6C0097D114 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 044CDED82D0BFB6C0097D114;
			remoteInfo = "Side Hustle";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		044CDEEA2D0BFB6F0097D114 /* Side HustleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "Side HustleTests.xctest"; path = "/Users/<USER>/Side hustle xcode/Side Hustle/build/Debug/Side Hustle.app/Contents/PlugIns/Side HustleTests.xctest"; sourceTree = "<absolute>"; };
		044CDEF42D0BFB6F0097D114 /* Side HustleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "Side HustleUITests.xctest"; path = "/Users/<USER>/Side hustle xcode/Side Hustle/build/Debug/Side HustleUITests-Runner.app/Contents/PlugIns/Side HustleUITests.xctest"; sourceTree = "<absolute>"; };
		04CCAD072D80AFED009A7714 /* Side Hustle.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Side Hustle.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		04CCACF72D80A2F8009A7714 /* Exceptions for "Side Hustle" folder in "Side Hustle" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Models/StockNotification.swift,
				Resources/stock_data.json,
				Resources/trained_model.mlmodel,
				Views/StockDetailView.swift,
			);
			target = 044CDED82D0BFB6C0097D114 /* Side Hustle */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		044CDEDB2D0BFB6C0097D114 /* Side Hustle */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				04CCACF72D80A2F8009A7714 /* Exceptions for "Side Hustle" folder in "Side Hustle" target */,
			);
			path = "Side Hustle";
			sourceTree = "<group>";
		};
		044CDEED2D0BFB6F0097D114 /* Side HustleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Side HustleTests";
			sourceTree = "<group>";
		};
		044CDEF72D0BFB6F0097D114 /* Side HustleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Side HustleUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		044CDED62D0BFB6C0097D114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0466FF3E2D0F1E140071DFE3 /* PythonKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEE72D0BFB6F0097D114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEF12D0BFB6F0097D114 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		044CDED02D0BFB6C0097D114 = {
			isa = PBXGroup;
			children = (
				044CDEDB2D0BFB6C0097D114 /* Side Hustle */,
				044CDEED2D0BFB6F0097D114 /* Side HustleTests */,
				044CDEF72D0BFB6F0097D114 /* Side HustleUITests */,
				04CCAD072D80AFED009A7714 /* Side Hustle.app */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		044CDED82D0BFB6C0097D114 /* Side Hustle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 044CDEFE2D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side Hustle" */;
			buildPhases = (
				044CDED52D0BFB6C0097D114 /* Sources */,
				044CDED62D0BFB6C0097D114 /* Frameworks */,
				044CDED72D0BFB6C0097D114 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				044CDEDB2D0BFB6C0097D114 /* Side Hustle */,
			);
			name = "Side Hustle";
			packageProductDependencies = (
				0466FF3D2D0F1E140071DFE3 /* PythonKit */,
			);
			productName = "Side Hustle";
			productReference = 04CCAD072D80AFED009A7714 /* Side Hustle.app */;
			productType = "com.apple.product-type.application";
		};
		044CDEE92D0BFB6F0097D114 /* Side HustleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 044CDF012D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side HustleTests" */;
			buildPhases = (
				044CDEE62D0BFB6F0097D114 /* Sources */,
				044CDEE72D0BFB6F0097D114 /* Frameworks */,
				044CDEE82D0BFB6F0097D114 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				044CDEEC2D0BFB6F0097D114 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				044CDEED2D0BFB6F0097D114 /* Side HustleTests */,
			);
			name = "Side HustleTests";
			packageProductDependencies = (
			);
			productName = "Side HustleTests";
			productReference = 044CDEEA2D0BFB6F0097D114 /* Side HustleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		044CDEF32D0BFB6F0097D114 /* Side HustleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 044CDF042D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side HustleUITests" */;
			buildPhases = (
				044CDEF02D0BFB6F0097D114 /* Sources */,
				044CDEF12D0BFB6F0097D114 /* Frameworks */,
				044CDEF22D0BFB6F0097D114 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				044CDEF62D0BFB6F0097D114 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				044CDEF72D0BFB6F0097D114 /* Side HustleUITests */,
			);
			name = "Side HustleUITests";
			packageProductDependencies = (
			);
			productName = "Side HustleUITests";
			productReference = 044CDEF42D0BFB6F0097D114 /* Side HustleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		044CDED12D0BFB6C0097D114 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					044CDED82D0BFB6C0097D114 = {
						CreatedOnToolsVersion = 16.2;
					};
					044CDEE92D0BFB6F0097D114 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 044CDED82D0BFB6C0097D114;
					};
					044CDEF32D0BFB6F0097D114 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 044CDED82D0BFB6C0097D114;
					};
				};
			};
			buildConfigurationList = 044CDED42D0BFB6C0097D114 /* Build configuration list for PBXProject "Side Hustle" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 044CDED02D0BFB6C0097D114;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				0466FF3C2D0F1E140071DFE3 /* XCRemoteSwiftPackageReference "PythonKit" */,
				04CCAD042D80A45C009A7714 /* XCRemoteSwiftPackageReference "swift-system" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 044CDED02D0BFB6C0097D114;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				044CDED82D0BFB6C0097D114 /* Side Hustle */,
				044CDEE92D0BFB6F0097D114 /* Side HustleTests */,
				044CDEF32D0BFB6F0097D114 /* Side HustleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		044CDED72D0BFB6C0097D114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEE82D0BFB6F0097D114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEF22D0BFB6F0097D114 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		044CDED52D0BFB6C0097D114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEE62D0BFB6F0097D114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		044CDEF02D0BFB6F0097D114 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		044CDEEC2D0BFB6F0097D114 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 044CDED82D0BFB6C0097D114 /* Side Hustle */;
			targetProxy = 044CDEEB2D0BFB6F0097D114 /* PBXContainerItemProxy */;
		};
		044CDEF62D0BFB6F0097D114 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 044CDED82D0BFB6C0097D114 /* Side Hustle */;
			targetProxy = 044CDEF52D0BFB6F0097D114 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		044CDEFC2D0BFB6F0097D114 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		044CDEFD2D0BFB6F0097D114 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		044CDEFF2D0BFB6F0097D114 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Side Hustle/Side_Hustle.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-Hustle";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = macosx;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		044CDF002D0BFB6F0097D114 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "Side Hustle/Side_Hustle.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-Hustle";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = macosx;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		044CDF022D0BFB6F0097D114 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-HustleTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Side Hustle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Side Hustle";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		044CDF032D0BFB6F0097D114 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-HustleTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Side Hustle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Side Hustle";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		044CDF052D0BFB6F0097D114 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-HustleUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "Side Hustle";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		044CDF062D0BFB6F0097D114 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "Personal.Side-HustleUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "Side Hustle";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		044CDED42D0BFB6C0097D114 /* Build configuration list for PBXProject "Side Hustle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				044CDEFC2D0BFB6F0097D114 /* Debug */,
				044CDEFD2D0BFB6F0097D114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		044CDEFE2D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side Hustle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				044CDEFF2D0BFB6F0097D114 /* Debug */,
				044CDF002D0BFB6F0097D114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		044CDF012D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side HustleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				044CDF022D0BFB6F0097D114 /* Debug */,
				044CDF032D0BFB6F0097D114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		044CDF042D0BFB6F0097D114 /* Build configuration list for PBXNativeTarget "Side HustleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				044CDF052D0BFB6F0097D114 /* Debug */,
				044CDF062D0BFB6F0097D114 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		0466FF3C2D0F1E140071DFE3 /* XCRemoteSwiftPackageReference "PythonKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/pvieito/PythonKit";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		04CCAD042D80A45C009A7714 /* XCRemoteSwiftPackageReference "swift-system" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-system.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.4.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		0466FF3D2D0F1E140071DFE3 /* PythonKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0466FF3C2D0F1E140071DFE3 /* XCRemoteSwiftPackageReference "PythonKit" */;
			productName = PythonKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 044CDED12D0BFB6C0097D114 /* Project object */;
}
