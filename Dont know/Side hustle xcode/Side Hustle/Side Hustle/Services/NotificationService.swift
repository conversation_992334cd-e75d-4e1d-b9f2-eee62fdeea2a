import Foundation
import UserNotifications

class NotificationService {
    static let shared = NotificationService()
    
    private init() {
        requestNotificationPermission()
    }
    
    /// Requests user permission for notifications
    private func requestNotificationPermission() {
        let center = UNUserNotificationCenter.current()
        center.requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if let error = error {
                print("❌ Notification permission error: \(error)")
            } else if granted {
                print("✅ Notification permission granted!")
            } else {
                print("❌ Notification permission denied.")
            }
        }
    }
    
    /// Schedules a stock prediction notification
    func sendPredictionNotification(stockSymbol: String, trend: Double, predictedPrice: Double) {
        let content = UNMutableNotificationContent()
        content.title = "📈 Stock Prediction Update"
        content.body = "\(stockSymbol) is predicted to reach ₹\(predictedPrice) with a trend score of \(trend)."
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 2, repeats: false)
        let request = UNNotificationRequest(identifier: "\(stockSymbol)_prediction", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("❌ Error sending notification: \(error)")
            } else {
                print("✅ Notification scheduled for \(stockSymbol)")
            }
        }
    }
}

