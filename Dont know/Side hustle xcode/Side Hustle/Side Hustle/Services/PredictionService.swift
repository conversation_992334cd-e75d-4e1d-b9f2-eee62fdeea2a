import Foundation

class PredictionService {
    static let shared = PredictionService()

    func predictStockTrend(for stock: stock) -> StockPrediction {
        // Simulated ML prediction (replace with CoreML model)
        let probability = Double.random(in: 0...1)
        let isBullish = probability > 0.5
        return StockPrediction(stock: stock, isBullish: isBullish, probability: probability)
    }
}
