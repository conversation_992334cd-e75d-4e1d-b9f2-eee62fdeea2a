import Foundation
import Combine

class StockDataService {
    static let shared = StockDataService()  // Singleton instance

    private let apiKey = "MIIZ9WNJP2D20VS4"
    private let baseURL = "https://www.alphavantage.co/query?"
    
    @Published var stocks: [stock] = []
    private var cancellables = Set<AnyCancellable>()

    init() {
        fetchAllStocks()
    }

    // Fetches all NSE & BSE stocks
    func fetchAllStocks() {
        let stockSymbols = ["RELIANCE.BSE", "TCS.BSE", "INFY.BSE", "HDFC.BSE", "HINDUNILVR.BSE"]
        
        for symbol in stockSymbols {
            fetchStockData(for: symbol)
        }
    }

    // Fetch individual stock data
    func fetchStockData(for symbol: String) {
        let urlString = "\(baseURL)function=TIME_SERIES_INTRADAY&symbol=\(symbol)&interval=5min&apikey=\(apiKey)"
        
        guard let url = URL(string: urlString) else {
            print("❌ Invalid URL for \(symbol)")
            return
        }
        
        URLSession.shared.dataTaskPublisher(for: url)
            .map { $0.data }
            .decode(type: AlphaVantageResponse.self, decoder: JSONDecoder())
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                switch completion {
                case .failure(let error):
                    print("❌ Error fetching \(symbol): \(error.localizedDescription)")
                case .finished:
                    break
                }
            }, receiveValue: { [weak self] response in
                if let stock = response.toStock(symbol: symbol) {
                    self?.stocks.append(stock)   // ✅ Use `stock` instead of `Stock`
                    print("✅ Fetched stock: \(stock.name) - \(stock.momentum)%")
                }
            })
            .store(in: &cancellables)
    }
}

// MARK: - Alpha Vantage API Response Models
struct AlphaVantageResponse: Codable {
    let timeSeries: [String: StockData]

    enum CodingKeys: String, CodingKey {
        case timeSeries = "Time Series (5min)"
    }

    func toStock(symbol: String) -> Stock? {
        guard let latest = timeSeries.first else { return nil }
        return Stock(symbol: symbol, date: latest.key, price: Double(latest.value.close) ?? 0.0)
    }
}

struct StockData: Codable {
    let open: String
    let high: String
    let low: String
    let close: String
    let volume: String

    enum CodingKeys: String, CodingKey {
        case open = "1. open"
        case high = "2. high"
        case low = "3. low"
        case close = "4. close"
        case volume = "5. volume"
    }
}

// MARK: - Stock Model
struct Stock: Identifiable {
    let id = UUID()
    let symbol: String
    let name: String
    let date: String
    let price: Double
    var momentum: Double  // Determines bull/bear status

    init(symbol: String, date: String, price: Double) {
        self.symbol = symbol
        self.name = symbol.replacingOccurrences(of: ".BSE", with: "")
        self.date = date
        self.price = price
        self.momentum = Double.random(in: -2.5...2.5)  // Simulating momentum changes
    }
}
