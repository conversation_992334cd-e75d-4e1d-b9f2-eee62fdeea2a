import Foundation
import Combine

// Define the Stock struct conforming to Codable
struct Stock: Codable {
    let stockSymbol: String
    let predictedTrend: Double // Simulated trend (-1: bearish, 1: bullish)
    let predictedPrice: Double // Simulated price range
}

class PredictionViewModel: ObservableObject {
    @Published var predictedStocks: [Stock] = [] // Array of Stock objects
    private var cancellables = Set<AnyCancellable>()

    init() {
        fetchPredictions()
    }

    // Fetch or generate predictions
    func fetchPredictions() {
        // In a real app, replace this with a Machine Learning Model or API call
        let samplePredictions = StockDataService.shared.stocks.map { stock in
            Stock(
                stockSymbol: stock.symbol, // Correctly map the symbol property
                predictedTrend: Double.random(in: -1...1), // Simulated trend (-1 to 1)
                predictedPrice: Double.random(in: 10...100) // Simulated price range
            )
        }

        DispatchQueue.main.async {
            self.predictedStocks = samplePredictions
        }
    }
}

class StockDataService {
    static let shared = StockDataService()
    var stocks: [StockData] = [
        StockData(symbol: "AAPL"),
        StockData(symbol: "GOOG"),
        StockData(symbol: "TSLA")
    ]
}

// Define the StockData struct to represent basic stock information
struct StockData {
    let symbol: String
}
