import Foundation
import Combine

class StockViewModel: ObservableObject {
    @Published var stocks: [stock] = []
    @Published var predictions: [StockPrediction] = []
    
    private var stockService = StockDataService.shared
    private var predictionService = PredictionService.shared
    private var cancellables = Set<AnyCancellable>()

    init() {
        stockService.$stocks
            .receive(on: DispatchQueue.main)
            .sink { [weak self] stocks in
                self?.stocks = stocks
            }
            .store(in: &cancellables)
    }

    func fetchPredictions() {
        predictions = stocks.map { stock in
            predictionService.predictStockTrend(for: stock)
        }
    }
}
