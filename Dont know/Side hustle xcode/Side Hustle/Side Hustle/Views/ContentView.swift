import SwiftUI

struct ContentView: View {
    @StateObject private var stockViewModel = StockViewModel()
    
    var body: some View {
        NavigationView {
            VStack {
                List {
                    Section(header: Text("📈 Bullish Stocks").font(.headline)) {
                        ForEach($stockViewModel.bullishStocks) { stock in
                            StockRowView(stock: stock)
                        }
                    }
                    
                    Section(header: Text("📉 Bearish Stocks").font(.headline)) {
                        ForEach(stockViewModel.bearishStocks) { stock in
                            StockRowView(stock: stock)
                        }
                    }
                }
                .navigationTitle("Stock Watchlist")
                .toolbar {
                    Button(action: {
                        stockViewModel.fetchStockData()
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                }
            }
        }
        .onAppear {
            $stockViewModel.fetchStockData
        }
    }
}

// MARK: - Stock Row View
struct StockRowView: View {
    let stock: stock

    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(stock.name)
                    .font(.headline)
                Text(stock.symbol)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            Spacer()
            Text(String(format: "%.2f", stock.momentum) + "%")
                .foregroundColor(stock.momentum >= 0 ? .green : .red)
                .bold()
        }
        .padding(.vertical, 5)
    }
}
#Preview {
    ContentView()
}
