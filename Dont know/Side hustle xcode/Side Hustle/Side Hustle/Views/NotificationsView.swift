
import SwiftUI

struct NotificationsView: View {
    @ObservedObject var viewModel: StockViewModel

    var body: some View {
        NavigationView {
            VStack {
                Text("Stock Predictions Notifications")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                List($viewModel.notifications) { notification in
                    HStack {
                        VStack(alignment: .leading) {
                            Text(notification.stockName)
                                .font(.headline)
                            Text("Movement: \(notification.movement)")
                                .foregroundColor(notification.movement == "Bullish" ? .green : .red)
                        }
                        Spacer()
                        Text("Target: ₹\(String(format: "%.2f", notification.targetPrice))")
                            .fontWeight(.bold)
                    }
                    .padding()
                }
            }
            .onAppear {
                $viewModel.fetchNotifications
            }
        }
    }
}
