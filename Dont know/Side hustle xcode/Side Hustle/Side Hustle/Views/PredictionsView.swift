import SwiftUI

struct PredictionsView: View {
    @ObservedObject var viewModel: StockViewModel

    var body: some View {
        NavigationView {
            VStack {
                Text("Predicted Stock Movements")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                List(viewModel.predictions, id: \.id) { prediction in
                    HStack {
                        VStack(alignment: .leading) {
                            Text("\(prediction.stockName)")
                                .font(.headline)
                            Text("Prediction: \(prediction.movement)")
                                .foregroundColor(prediction.movement == "Bullish" ? .green : .red)
                        }
                        Spacer()
                        Text("Target: ₹\(String(format: "%.2f", prediction.targetPrice))")
                            .fontWeight(.bold)
                    }
                    .padding()
                }
            }
            .onAppear {
                viewModel.fetchPredictions()
            }
        }
    }
}
