import SwiftUI

struct StockDetailView: View {
    let stock: StockModel
    
    var body: some View {
        VStack {
            Text(stock.name)
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text(stock.symbol)
                .font(.title2)
                .foregroundColor(.gray)
            
            Spacer()
            
            VStack(spacing: 10) {
                HStack {
                    Text("Current Price:")
                    Spacer()
                    Text("₹\(String(format: "%.2f", stock.currentPrice))")
                        .fontWeight(.bold)
                }
                .padding()
                
                HStack {
                    Text("Momentum:")
                    Spacer()
                    Text("\(String(format: "%.2f", stock.momentum))%")
                        .foregroundColor(stock.momentum >= 0 ? .green : .red)
                        .fontWeight(.bold)
                }
                .padding()
                
                HStack {
                    Text("Predicted Movement:")
                    Spacer()
                    Text(stock.prediction)
                        .foregroundColor(stock.prediction == "Bullish" ? .green : .red)
                        .fontWeight(.bold)
                }
                .padding()
            }
            .background(RoundedRectangle(cornerRadius: 10).fill(Color(.systemGray6)))
            .padding()
            
            Spacer()
            
            Button(action: {
                print("Fetching live updates...")
            }) {
                Text("Refresh Data")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
            .padding()
        }
        .padding()
        .navigationTitle("Stock Details")
    }
}
