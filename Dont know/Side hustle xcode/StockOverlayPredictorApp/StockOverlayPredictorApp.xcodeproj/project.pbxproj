// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		04A717CC2D930A3900C4F2B4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04A717B22D930A3600C4F2B4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04A717B92D930A3600C4F2B4;
			remoteInfo = StockOverlayPredictorApp;
		};
		04A717D62D930A3900C4F2B4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04A717B22D930A3600C4F2B4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04A717B92D930A3600C4F2B4;
			remoteInfo = StockOverlayPredictorApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		04A717BA2D930A3600C4F2B4 /* StockOverlayPredictorApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = StockOverlayPredictorApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		04A717CB2D930A3900C4F2B4 /* StockOverlayPredictorAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = StockOverlayPredictorAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		04A717D52D930A3900C4F2B4 /* StockOverlayPredictorAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = StockOverlayPredictorAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		04A717BC2D930A3600C4F2B4 /* StockOverlayPredictorApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = StockOverlayPredictorApp;
			sourceTree = "<group>";
		};
		04A717CE2D930A3900C4F2B4 /* StockOverlayPredictorAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = StockOverlayPredictorAppTests;
			sourceTree = "<group>";
		};
		04A717D82D930A3900C4F2B4 /* StockOverlayPredictorAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = StockOverlayPredictorAppUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		04A717B72D930A3600C4F2B4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717C82D930A3900C4F2B4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717D22D930A3900C4F2B4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04A717B12D930A3600C4F2B4 = {
			isa = PBXGroup;
			children = (
				04A717BC2D930A3600C4F2B4 /* StockOverlayPredictorApp */,
				04A717CE2D930A3900C4F2B4 /* StockOverlayPredictorAppTests */,
				04A717D82D930A3900C4F2B4 /* StockOverlayPredictorAppUITests */,
				04A717BB2D930A3600C4F2B4 /* Products */,
			);
			sourceTree = "<group>";
		};
		04A717BB2D930A3600C4F2B4 /* Products */ = {
			isa = PBXGroup;
			children = (
				04A717BA2D930A3600C4F2B4 /* StockOverlayPredictorApp.app */,
				04A717CB2D930A3900C4F2B4 /* StockOverlayPredictorAppTests.xctest */,
				04A717D52D930A3900C4F2B4 /* StockOverlayPredictorAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		04A717B92D930A3600C4F2B4 /* StockOverlayPredictorApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04A717DF2D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorApp" */;
			buildPhases = (
				04A717B62D930A3600C4F2B4 /* Sources */,
				04A717B72D930A3600C4F2B4 /* Frameworks */,
				04A717B82D930A3600C4F2B4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				04A717BC2D930A3600C4F2B4 /* StockOverlayPredictorApp */,
			);
			name = StockOverlayPredictorApp;
			packageProductDependencies = (
			);
			productName = StockOverlayPredictorApp;
			productReference = 04A717BA2D930A3600C4F2B4 /* StockOverlayPredictorApp.app */;
			productType = "com.apple.product-type.application";
		};
		04A717CA2D930A3900C4F2B4 /* StockOverlayPredictorAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04A717E22D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorAppTests" */;
			buildPhases = (
				04A717C72D930A3900C4F2B4 /* Sources */,
				04A717C82D930A3900C4F2B4 /* Frameworks */,
				04A717C92D930A3900C4F2B4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04A717CD2D930A3900C4F2B4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04A717CE2D930A3900C4F2B4 /* StockOverlayPredictorAppTests */,
			);
			name = StockOverlayPredictorAppTests;
			packageProductDependencies = (
			);
			productName = StockOverlayPredictorAppTests;
			productReference = 04A717CB2D930A3900C4F2B4 /* StockOverlayPredictorAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		04A717D42D930A3900C4F2B4 /* StockOverlayPredictorAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04A717E52D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorAppUITests" */;
			buildPhases = (
				04A717D12D930A3900C4F2B4 /* Sources */,
				04A717D22D930A3900C4F2B4 /* Frameworks */,
				04A717D32D930A3900C4F2B4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04A717D72D930A3900C4F2B4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				04A717D82D930A3900C4F2B4 /* StockOverlayPredictorAppUITests */,
			);
			name = StockOverlayPredictorAppUITests;
			packageProductDependencies = (
			);
			productName = StockOverlayPredictorAppUITests;
			productReference = 04A717D52D930A3900C4F2B4 /* StockOverlayPredictorAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		04A717B22D930A3600C4F2B4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					04A717B92D930A3600C4F2B4 = {
						CreatedOnToolsVersion = 16.2;
					};
					04A717CA2D930A3900C4F2B4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 04A717B92D930A3600C4F2B4;
					};
					04A717D42D930A3900C4F2B4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 04A717B92D930A3600C4F2B4;
					};
				};
			};
			buildConfigurationList = 04A717B52D930A3600C4F2B4 /* Build configuration list for PBXProject "StockOverlayPredictorApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 04A717B12D930A3600C4F2B4;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 04A717BB2D930A3600C4F2B4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				04A717B92D930A3600C4F2B4 /* StockOverlayPredictorApp */,
				04A717CA2D930A3900C4F2B4 /* StockOverlayPredictorAppTests */,
				04A717D42D930A3900C4F2B4 /* StockOverlayPredictorAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		04A717B82D930A3600C4F2B4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717C92D930A3900C4F2B4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717D32D930A3900C4F2B4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		04A717B62D930A3600C4F2B4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717C72D930A3900C4F2B4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04A717D12D930A3900C4F2B4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		04A717CD2D930A3900C4F2B4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04A717B92D930A3600C4F2B4 /* StockOverlayPredictorApp */;
			targetProxy = 04A717CC2D930A3900C4F2B4 /* PBXContainerItemProxy */;
		};
		04A717D72D930A3900C4F2B4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04A717B92D930A3600C4F2B4 /* StockOverlayPredictorApp */;
			targetProxy = 04A717D62D930A3900C4F2B4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		04A717DD2D930A3900C4F2B4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		04A717DE2D930A3900C4F2B4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		04A717E02D930A3900C4F2B4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = StockOverlayPredictorApp/StockOverlayPredictorApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"StockOverlayPredictorApp/Preview Content\"";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		04A717E12D930A3900C4F2B4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = StockOverlayPredictorApp/StockOverlayPredictorApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"StockOverlayPredictorApp/Preview Content\"";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		04A717E32D930A3900C4F2B4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/StockOverlayPredictorApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/StockOverlayPredictorApp";
			};
			name = Debug;
		};
		04A717E42D930A3900C4F2B4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/StockOverlayPredictorApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/StockOverlayPredictorApp";
			};
			name = Release;
		};
		04A717E62D930A3900C4F2B4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = StockOverlayPredictorApp;
			};
			name = Debug;
		};
		04A717E72D930A3900C4F2B4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.StockOverlayPredictorAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = StockOverlayPredictorApp;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		04A717B52D930A3600C4F2B4 /* Build configuration list for PBXProject "StockOverlayPredictorApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04A717DD2D930A3900C4F2B4 /* Debug */,
				04A717DE2D930A3900C4F2B4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04A717DF2D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04A717E02D930A3900C4F2B4 /* Debug */,
				04A717E12D930A3900C4F2B4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04A717E22D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04A717E32D930A3900C4F2B4 /* Debug */,
				04A717E42D930A3900C4F2B4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04A717E52D930A3900C4F2B4 /* Build configuration list for PBXNativeTarget "StockOverlayPredictorAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04A717E62D930A3900C4F2B4 /* Debug */,
				04A717E72D930A3900C4F2B4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 04A717B22D930A3600C4F2B4 /* Project object */;
}
