//
//  AppDelegate.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Cocoa

class AppDelegate: NSObject, NSApplicationDelegate {
    var overlayManager: OverlayManager?
    
    func applicationDidFinishLaunching(_ aNotification: Notification) {
        checkScreenRecordingPermission()
    }
    
    func checkScreenRecordingPermission() {
        SCShareableContent.requestScreenRecordingPermission { granted in
            if granted {
                print("Screen recording permission granted.")
            } else {
                print("Screen recording permission denied.")
            }
        }
    }
}
