//
//  LLMClient.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Foundation

class LLMClient {
    func requestPrediction(from text: String, completion: @escaping (String?) -> Void) {
        var request = URLRequest(url: URL(string: "http://localhost:11434/api/generate")!)
        request.httpMethod = "POST"
        let body = ["model": "mistral", "prompt": text, "stream": false] as [String : Any]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body, options: [])
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        URLSession.shared.dataTask(with: request) { data, _, _ in
            if let data = data, let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any], let response = json["response"] as? String {
                completion(response)
            } else {
                completion(nil)
            }
        }.resume()
    }
}
