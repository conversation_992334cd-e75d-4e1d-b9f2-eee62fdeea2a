//
//  OCRProcessor.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Vision
import AppKit

class OCRProcessor {
    func recognizeText(in image: NSImage, completion: @escaping (String) -> Void) {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { return }
        
        let request = VNRecognizeTextRequest { (request, _) in
            let text = request.results?
                .compactMap { $0 as? VNRecognizedTextObservation }
                .flatMap { $0.topCandidates(1) }
                .map { $0.string }
                .joined(separator: "\n")
            completion(text ?? "")
        }
        
        request.recognitionLevel = .accurate
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        try? handler.perform([request])
    }
}
