//
//  OverlayManager.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Cocoa

class OverlayManager {
    private let screenCaptureManager = ScreenCaptureManager()
    private let ocrProcessor = OCRProcessor()
    private let llmClient = LLMClient()
    private var overlayWindow: OverlayWindow?
    private var timer: Timer?
    
    func startOverlay() {
        overlayWindow = OverlayWindow()
        overlayWindow?.makeKeyAndOrderFront(nil)
        
        timer = Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            self?.performScreenAnalysisAndUpdateOverlay()
        }
    }
    
    private func performScreenAnalysisAndUpdateOverlay() {
        guard let screenshot = screenCaptureManager.captureScreen() else { return }
        
        ocrProcessor.recognizeText(in: screenshot) { [weak self] text in
            self?.llmClient.requestPrediction(from: text) { prediction in
                DispatchQueue.main.async {
                    self?.overlayWindow?.updatePrediction(Prediction(text: prediction ?? "No prediction"))
                }
            }
        }
    }
}
