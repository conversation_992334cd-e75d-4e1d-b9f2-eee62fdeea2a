//
//  ScreenCaptureManager.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Cocoa
import ScreenCaptureKit

class ScreenCaptureManager {
    func captureScreen(completion: @escaping (NSImage?) -> Void) {
        let filter = SCContentFilter(content: SCShareableContent.excludingWindows([]), excludingApplications: [])
        let configuration = SCScreenshotConfiguration()
        
        SCScreenshotManager().captureScreenshot(contentFilter: filter, configuration: configuration) { result in
            switch result {
            case .success(let cgImage):
                let nsImage = NSImage(cgImage: cgImage, size: NSSize(width: cgImage.width, height: cgImage.height))
                completion(nsImage)
            case .failure(let error):
                print("Failed to capture screenshot: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }
}
