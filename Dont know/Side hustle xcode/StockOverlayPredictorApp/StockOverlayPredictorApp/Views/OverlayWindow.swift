//
//  OverlayWindow.swift
//  StockOverlayPredictorApp
//
//  Created by <PERSON><PERSON><PERSON>  on 25/03/25.
//


import Cocoa
import SwiftUI

class OverlayWindow: NSWindow {
    init() {
        super.init(contentRect: NSScreen.main?.frame ?? .zero,
                   styleMask: [.borderless, .fullSizeContentView],
                   backing: .buffered,
                   defer: false)
        
        self.backgroundColor = .clear
        self.isOpaque = false
        self.level = .floating
        self.ignoresMouseEvents = true
        self.collectionBehavior = [.canJoinAllSpaces, .fullScreenAuxiliary]
        
        self.contentView = NSHostingView(rootView: PredictionView(prediction: .constant(Prediction(text: "Initializing..."))))
    }
    
    func updatePrediction(_ prediction: Prediction) {
        (self.contentView as? NSHostingView<PredictionView>)?.rootView.prediction = prediction
    }
}
