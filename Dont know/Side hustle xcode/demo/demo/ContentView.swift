//
//  ContentView.swift
//  demo
//
//  Created by <PERSON><PERSON><PERSON>  on 19/03/25.
//

import SwiftUI

struct ContentView: View {
    var body: some View {
            TabView(selection: /*@START_MENU_TOKEN@*//*@PLACEHOLDER=Selection@*/.constant(1)/*@END_MENU_TOKEN@*/) {
                Text("Tab Content 1").tabItem { Text("Watchlist") }.tag(1)
                Text("Tab Content 2").tabItem { Text("Stocks") }.tag(2)
                Text("H").tabItem { Text("History") }.tag(3)
                
            }
        .padding()
    }
}

#Preview {
    ContentView()
}
