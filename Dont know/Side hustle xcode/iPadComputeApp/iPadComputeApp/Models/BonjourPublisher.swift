//
//  BonjourPublisher.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import Foundation
import Network

class BonjourPublisher {
    static let shared = BonjourPublisher() // Fix here

    private var service: NetService?

    func start() {
        service = NetService(domain: "local.", type: "_ipadcompute._tcp.", name: "iPadComputeNode", port: 8080)
        service?.publish()
    }
}
