//
//  SystemMonitor.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import Foundation
import SwiftUI

class SystemMonitor: ObservableObject {
    @Published var cpuUsage: Double = 0.0
    @Published var memoryUsage: Double = 0.0
    @Published var dataSent: Double = 0.0
    @Published var dataReceived: Double = 0.0

    func startMonitoring() {
        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            self.cpuUsage = Double.random(in: 10...90) // Simulate CPU usage
            self.memoryUsage = Double.random(in: 100...800) // Simulate RAM usage
        }
    }

    func updateDataSent(bytes: Int) {
        DispatchQueue.main.async {
            self.dataSent += Double(bytes) / 1024.0
        }
    }

    func updateDataReceived(bytes: Int) {
        DispatchQueue.main.async {
            self.dataReceived += Double(bytes) / 1024.0
        }
    }
}