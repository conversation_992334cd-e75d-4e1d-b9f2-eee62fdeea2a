//
//  DashboardView.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct DashboardView: View {
    @EnvironmentObject var taskReceiver: TaskReceiver
    @EnvironmentObject var systemMonitor: SystemMonitor

    var body: some View {
        VStack {
            Text("📡 Compute Node Status").font(.title).bold()

            HStack {
                Text("Connected to Mac:")
                Text(taskReceiver.isConnected ? "✅ YES" : "❌ NO")
                    .foregroundColor(taskReceiver.isConnected ? .green : .red)
            }

            Divider()
            
            HStack {
                Text("Data Sent: \(systemMonitor.dataSent) KB")
                Spacer()
                Text("Data Received: \(systemMonitor.dataReceived) KB")
            }.padding()
            
            Divider()
            
            VStack(alignment: .leading) {
                Text("📊 System Usage:")
                Text("CPU: \(systemMonitor.cpuUsage, specifier: "%.2f")%")
                Text("Memory: \(systemMonitor.memoryUsage, specifier: "%.2f") MB")
            }.padding()

            Spacer()
        }
        .padding()
        .onAppear {
            systemMonitor.startMonitoring()
        }
    }
}