//
//  NetworkStatsView.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct NetworkStatsView: View {
    @EnvironmentObject var systemMonitor: SystemMonitor

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Network Stats").font(.headline)

            HStack {
                Text("Sent:")
                Spacer()
                Text("\(systemMonitor.dataSent, specifier: "%.2f") KB")
            }

            HStack {
                Text("Received:")
                Spacer()
                Text("\(systemMonitor.dataReceived, specifier: "%.2f") KB")
            }

            Divider()

            Text("System Usage").font(.headline)
            Text("CPU: \(systemMonitor.cpuUsage, specifier: "%.1f")%")
            Text("Memory: \(systemMonitor.memoryUsage, specifier: "%.1f") MB")
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(10)
    }
}