//
//  TaskMonitorView.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import SwiftUI

struct TaskMonitorView: View {
    @EnvironmentObject var taskReceiver: TaskReceiver

    var body: some View {
        VStack {
            Text("🖥 Processing Queue").font(.headline)
            
            List(taskReceiver.tasks) { task in
                VStack(alignment: .leading) {
                    Text("Task ID: \(task.taskID)").bold()
                    Text("Type: \(task.type)")
                    Text("File: \(task.fileName)")
                }
            }
        }
        .padding()
    }
}