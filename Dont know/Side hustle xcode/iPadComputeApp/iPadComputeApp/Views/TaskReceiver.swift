//
//  TaskReceiver.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//


import Foundation
import Swifter

class TaskReceiver: ObservableObject {
    @Published var isConnected = false
    @Published var tasks: [TaskModel] = []
    
    private var server = HttpServer()

    init() {
        startServer()
    }

    func startServer() {
        server["/task"] = { request in
            self.isConnected = true
            
            // Ensure request.body is not empty
            guard !request.body.isEmpty else {
                return .badRequest(.text("No Data Received"))
            }
            
            do {
                let data = Data(request.body) // Convert [UInt8] to Data
                let task = try JSONDecoder().decode(TaskModel.self, from: data)
                
                DispatchQueue.main.async {
                    self.tasks.append(task)
                }
                
                // Simulate processing
                let result = HeavyTaskRunner.process(task: task)
                return .ok(.text(result))
            } catch {
                return .badRequest(.text("Invalid Task Format"))
            }
        }

        do {
            try server.start(8080)
        } catch {
            print("Server failed to start: \(error)")
        }
    }
}
