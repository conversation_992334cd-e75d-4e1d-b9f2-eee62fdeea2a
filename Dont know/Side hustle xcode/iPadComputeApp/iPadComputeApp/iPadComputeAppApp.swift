//
//  iPadComputeAppApp.swift
//  iPadComputeApp
//
//  Created by <PERSON><PERSON><PERSON>  on 04/04/25.
//

import SwiftUI

@main
struct iPadComputeApp: App {
    @StateObject private var taskReceiver = TaskReceiver()
    @StateObject private var systemMonitor = SystemMonitor()

    var body: some Scene {
        WindowGroup {
            DashboardView()
                .environmentObject(taskReceiver)
                .environmentObject(systemMonitor)
                .onAppear {
                    systemMonitor.startMonitoring()
                    BonjourPublisher.shared.start()
                }
        }
    }
}
