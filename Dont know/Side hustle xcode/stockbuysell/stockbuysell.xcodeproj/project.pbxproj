// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		043BA6B42D8B2A1200F0C77A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 043BA69B2D8B2A0F00F0C77A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 043BA6A22D8B2A0F00F0C77A;
			remoteInfo = stockbuysell;
		};
		043BA6BE2D8B2A1200F0C77A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 043BA69B2D8B2A0F00F0C77A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 043BA6A22D8B2A0F00F0C77A;
			remoteInfo = stockbuysell;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		043BA6A32D8B2A0F00F0C77A /* stockbuysell.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = stockbuysell.app; sourceTree = BUILT_PRODUCTS_DIR; };
		043BA6B32D8B2A1200F0C77A /* stockbuysellTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = stockbuysellTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		043BA6BD2D8B2A1200F0C77A /* stockbuysellUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = stockbuysellUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		043BA6A52D8B2A0F00F0C77A /* stockbuysell */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = stockbuysell;
			sourceTree = "<group>";
		};
		043BA6B62D8B2A1200F0C77A /* stockbuysellTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = stockbuysellTests;
			sourceTree = "<group>";
		};
		043BA6C02D8B2A1200F0C77A /* stockbuysellUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = stockbuysellUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		043BA6A02D8B2A0F00F0C77A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6B02D8B2A1200F0C77A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6BA2D8B2A1200F0C77A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		043BA69A2D8B2A0F00F0C77A = {
			isa = PBXGroup;
			children = (
				043BA6A52D8B2A0F00F0C77A /* stockbuysell */,
				043BA6B62D8B2A1200F0C77A /* stockbuysellTests */,
				043BA6C02D8B2A1200F0C77A /* stockbuysellUITests */,
				043BA6A42D8B2A0F00F0C77A /* Products */,
			);
			sourceTree = "<group>";
		};
		043BA6A42D8B2A0F00F0C77A /* Products */ = {
			isa = PBXGroup;
			children = (
				043BA6A32D8B2A0F00F0C77A /* stockbuysell.app */,
				043BA6B32D8B2A1200F0C77A /* stockbuysellTests.xctest */,
				043BA6BD2D8B2A1200F0C77A /* stockbuysellUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		043BA6A22D8B2A0F00F0C77A /* stockbuysell */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 043BA6C72D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysell" */;
			buildPhases = (
				043BA69F2D8B2A0F00F0C77A /* Sources */,
				043BA6A02D8B2A0F00F0C77A /* Frameworks */,
				043BA6A12D8B2A0F00F0C77A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				043BA6A52D8B2A0F00F0C77A /* stockbuysell */,
			);
			name = stockbuysell;
			packageProductDependencies = (
			);
			productName = stockbuysell;
			productReference = 043BA6A32D8B2A0F00F0C77A /* stockbuysell.app */;
			productType = "com.apple.product-type.application";
		};
		043BA6B22D8B2A1200F0C77A /* stockbuysellTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 043BA6CA2D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysellTests" */;
			buildPhases = (
				043BA6AF2D8B2A1200F0C77A /* Sources */,
				043BA6B02D8B2A1200F0C77A /* Frameworks */,
				043BA6B12D8B2A1200F0C77A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				043BA6B52D8B2A1200F0C77A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				043BA6B62D8B2A1200F0C77A /* stockbuysellTests */,
			);
			name = stockbuysellTests;
			packageProductDependencies = (
			);
			productName = stockbuysellTests;
			productReference = 043BA6B32D8B2A1200F0C77A /* stockbuysellTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		043BA6BC2D8B2A1200F0C77A /* stockbuysellUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 043BA6CD2D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysellUITests" */;
			buildPhases = (
				043BA6B92D8B2A1200F0C77A /* Sources */,
				043BA6BA2D8B2A1200F0C77A /* Frameworks */,
				043BA6BB2D8B2A1200F0C77A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				043BA6BF2D8B2A1200F0C77A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				043BA6C02D8B2A1200F0C77A /* stockbuysellUITests */,
			);
			name = stockbuysellUITests;
			packageProductDependencies = (
			);
			productName = stockbuysellUITests;
			productReference = 043BA6BD2D8B2A1200F0C77A /* stockbuysellUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		043BA69B2D8B2A0F00F0C77A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					043BA6A22D8B2A0F00F0C77A = {
						CreatedOnToolsVersion = 16.2;
					};
					043BA6B22D8B2A1200F0C77A = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 043BA6A22D8B2A0F00F0C77A;
					};
					043BA6BC2D8B2A1200F0C77A = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 043BA6A22D8B2A0F00F0C77A;
					};
				};
			};
			buildConfigurationList = 043BA69E2D8B2A0F00F0C77A /* Build configuration list for PBXProject "stockbuysell" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 043BA69A2D8B2A0F00F0C77A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 043BA6A42D8B2A0F00F0C77A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				043BA6A22D8B2A0F00F0C77A /* stockbuysell */,
				043BA6B22D8B2A1200F0C77A /* stockbuysellTests */,
				043BA6BC2D8B2A1200F0C77A /* stockbuysellUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		043BA6A12D8B2A0F00F0C77A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6B12D8B2A1200F0C77A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6BB2D8B2A1200F0C77A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		043BA69F2D8B2A0F00F0C77A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6AF2D8B2A1200F0C77A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		043BA6B92D8B2A1200F0C77A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		043BA6B52D8B2A1200F0C77A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 043BA6A22D8B2A0F00F0C77A /* stockbuysell */;
			targetProxy = 043BA6B42D8B2A1200F0C77A /* PBXContainerItemProxy */;
		};
		043BA6BF2D8B2A1200F0C77A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 043BA6A22D8B2A0F00F0C77A /* stockbuysell */;
			targetProxy = 043BA6BE2D8B2A1200F0C77A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		043BA6C52D8B2A1200F0C77A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		043BA6C62D8B2A1200F0C77A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		043BA6C82D8B2A1200F0C77A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"stockbuysell/Preview Content\"";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysell;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		043BA6C92D8B2A1200F0C77A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"stockbuysell/Preview Content\"";
				DEVELOPMENT_TEAM = SLT4AVDMJ9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysell;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		043BA6CB2D8B2A1200F0C77A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysellTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/stockbuysell.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/stockbuysell";
			};
			name = Debug;
		};
		043BA6CC2D8B2A1200F0C77A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysellTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/stockbuysell.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/stockbuysell";
			};
			name = Release;
		};
		043BA6CE2D8B2A1200F0C77A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysellUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = stockbuysell;
			};
			name = Debug;
		};
		043BA6CF2D8B2A1200F0C77A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = Personal.stockbuysellUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = stockbuysell;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		043BA69E2D8B2A0F00F0C77A /* Build configuration list for PBXProject "stockbuysell" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				043BA6C52D8B2A1200F0C77A /* Debug */,
				043BA6C62D8B2A1200F0C77A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		043BA6C72D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysell" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				043BA6C82D8B2A1200F0C77A /* Debug */,
				043BA6C92D8B2A1200F0C77A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		043BA6CA2D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysellTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				043BA6CB2D8B2A1200F0C77A /* Debug */,
				043BA6CC2D8B2A1200F0C77A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		043BA6CD2D8B2A1200F0C77A /* Build configuration list for PBXNativeTarget "stockbuysellUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				043BA6CE2D8B2A1200F0C77A /* Debug */,
				043BA6CF2D8B2A1200F0C77A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 043BA69B2D8B2A0F00F0C77A /* Project object */;
}
