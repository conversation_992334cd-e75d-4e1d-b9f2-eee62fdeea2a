<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "9FA41823-16BC-4FC2-8F01-52B0115AC12A"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "03654186-BE52-44D7-9443-4ECB38D73477"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "stockbuysell/NotificationsView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1"
            endingLineNumber = "1"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
