import SwiftUI

struct ChartsView: View {
    @ObservedObject var searchViewModel: SearchViewModel
    @ObservedObject var chartsViewModel: ChartsViewModel
    @State private var showingManualUpdateView = false

    var body: some View {
        NavigationView {
            VStack {
                List(searchViewModel.searchHistory, id: \.self) { symbol in
                    NavigationLink(destination: ChartDetailView(symbol: symbol, entries: chartsViewModel.ohlcData[symbol] ?? [])) {
                        Text(symbol)
                            .font(.headline)
                            .padding()
                    }
                }
                
                Button(action: {
                    showingManualUpdateView = true
                }) {
                    HStack {
                        Image(systemName: "pencil.circle.fill")
                        Text("Manual OHLC Update")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
            }
            .navigationTitle("Stock Charts")
            .sheet(isPresented: $showingManualUpdateView) {
                ManualOHLCUpdateView()
            }
        }
    }
}
