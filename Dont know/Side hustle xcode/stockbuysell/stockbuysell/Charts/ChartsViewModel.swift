//
//  ChartsViewModel.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation

class ChartsViewModel: ObservableObject {
    @Published var stockSymbols = ["AAPL", "GOOGL", "MSFT"] // Example symbols
    @Published var ohlcData: [String: [OHLCEntry]] = [:]

    private let apiService = APIService.shared

    func fetchInitialData() {
        for symbol in stockSymbols {
            fetchOHLCData(for: symbol)
        }
    }

    func fetchOHLCData(for symbol: String) {
        Task {
            do {
                let data = try await apiService.fetchOHLCData(for: symbol)
                DispatchQueue.main.async {
                    self.ohlcData[symbol] = data
                }
            } catch {
                print("Error fetching data for \(symbol): \(error)")
            }
        }
    }
}
