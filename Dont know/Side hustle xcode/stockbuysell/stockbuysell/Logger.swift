//
//  Logger.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation
import OSLog

class Logger: ObservableObject {
    static let shared = Logger()
    @Published private(set) var logs: [String] = []
    private let osLogger = os.Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.example.app", category: "general")

    private init() {}

    func log(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .short, timeStyle: .long)
        let logMessage = "[\(timestamp)] \(message)"
        logs.append(logMessage)
        osLogger.log("\(logMessage, privacy: .public)")
    }
}
