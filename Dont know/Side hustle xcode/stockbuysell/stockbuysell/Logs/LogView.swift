//
//  LogView.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//

import SwiftUI

struct LogView: View {
    @ObservedObject var viewModel: LogsViewModel

    var body: some View {
        NavigationView {
            List(viewModel.logs) { log in
                HStack {
                    Image(systemName: log.type == .success ? "checkmark.circle" : "exclamationmark.triangle")
                        .foregroundColor(log.type == .success ? .green : .red)
                    VStack(alignment: .leading) {
                        Text(log.message)
                            .font(.body)
                            .foregroundColor(log.type == .success ? .green : .red)
                        Text(log.timestamp, style: .time)
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }
            .navigationTitle("API Logs")
        }
    }
}
