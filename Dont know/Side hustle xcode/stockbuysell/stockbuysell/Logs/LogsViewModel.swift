//
//  LogsViewModel.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation

class LogsViewModel: ObservableObject {
    @Published var logs = [LogEntry]()
    @Published var unreadLogs = Set<UUID>() // Track unread log IDs

    var unreadLogsCount: Int {
        unreadLogs.count
    }

    func addLog(message: String, type: LogType) {
        let newLog = LogEntry(message: message, timestamp: Date(), type: type)
        DispatchQueue.main.async {
            self.logs.append(newLog)
            self.unreadLogs.insert(newLog.id) // Mark new log as unread
        }
    }

    func markAllAsRead() {
        DispatchQueue.main.async {
            self.unreadLogs.removeAll()
        }
    }

    func clearLogs() {
        DispatchQueue.main.async {
            self.logs.removeAll()
            self.unreadLogs.removeAll()
        }
    }
}
