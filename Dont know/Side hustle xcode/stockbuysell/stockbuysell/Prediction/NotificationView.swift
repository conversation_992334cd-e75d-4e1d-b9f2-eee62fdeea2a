//
//  NotificationsView.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//

import SwiftUI

struct NotificationView: View {
    @ObservedObject var viewModel: PredictionsViewModel

    var body: some View {
        NavigationView {
            VStack {
                // Top 3 Bullish Stocks
                Text("Top 3 Bullish Stocks")
                    .font(.headline)
                    .padding()

                List(viewModel.bullishStocks, id: \.symbol) { prediction in
                    HStack {
                        Text(prediction.symbol)
                            .font(.headline)
                        Spacer()
                        Text(String(format: "Buy Above %.2f", prediction.buyAbove))
                            .foregroundColor(.green)
                    }
                }

                Divider()

                // Top 3 Bearish Stocks
                Text("Top 3 Bearish Stocks")
                    .font(.headline)
                    .padding()

                List(viewModel.bearishStocks, id: \.symbol) { prediction in
                    HStack {
                        Text(prediction.symbol)
                            .font(.headline)
                        Spacer()
                        Text(String(format: "Sell Below %.2f", prediction.sellBelow))
                            .foregroundColor(.red)
                    }
                }
            }
            .navigationTitle("Predictions")
        }
    }
}
