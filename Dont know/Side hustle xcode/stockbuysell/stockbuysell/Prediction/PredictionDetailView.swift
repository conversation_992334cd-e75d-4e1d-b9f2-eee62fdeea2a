//
//  PredictionDetailView.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import SwiftUI

struct PredictionDetailView: View {
    let prediction: StockPrediction

    var body: some View {
        VStack(alignment: .leading) {
            Text("Prediction Details")
                .font(.title2)
                .padding()

            HStack {
                Text("Stock Symbol:")
                    .font(.headline)
                Spacer()
                Text(prediction.symbol)
                    .font(.body)
            }
            .padding()

            HStack {
                Text("Buy Above:")
                    .font(.headline)
                Spacer()
                Text(String(format: "%.2f", prediction.buyAbove))
                    .foregroundColor(.green)
                    .font(.body)
            }
            .padding()

            HStack {
                Text("Sell Below:")
                    .font(.headline)
                Spacer()
                Text(String(format: "%.2f", prediction.sellBelow))
                    .foregroundColor(.red)
                    .font(.body)
            }
            .padding()

            Spacer()
        }
        .navigationTitle(prediction.symbol)
        .padding()
    }
}
