//
//  PredictionsViewModel.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation

class PredictionsViewModel: ObservableObject {
    @Published var bullishStocks: [StockPrediction] = []
    @Published var bearishStocks: [StockPrediction] = []

    func generatePredictions(using stockData: [String: [OHLCEntry]]) {
        var predictions = [StockPrediction]()

        for (symbol, ohlcEntries) in stockData {
            if let latestEntry = ohlcEntries.first {
                let buyAbove = latestEntry.close * 1.02 // Example logic
                let sellBelow = latestEntry.close * 0.98 // Example logic

                predictions.append(StockPrediction(symbol: symbol, buyAbove: buyAbove, sellBelow: sellBelow))
            }
        }

        DispatchQueue.main.async {
            self.bullishStocks = predictions.sorted(by: { $0.buyAbove > $1.buyAbove }).prefix(3).map { $0 }
            self.bearishStocks = predictions.sorted(by: { $0.sellBelow < $1.sellBelow }).prefix(3).map { $0 }
        }
    }
}
