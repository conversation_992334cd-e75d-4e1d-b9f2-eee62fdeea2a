import SwiftUI

struct ContentView: View {
    @ObservedObject var viewModel: SearchViewModel
    @ObservedObject var logsViewModel: LogsViewModel // Add LogsViewModel to manage logs

    var body: some View {
        NavigationView {
            VStack {
                // Search Bar
                TextField("Enter Stock Symbol", text: $viewModel.searchText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()

                <PERSON><PERSON>(action: {
                    viewModel.searchStock()
                }) {
                    Text("Search")
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .padding()
                .disabled(viewModel.isLoading)

                if viewModel.isLoading {
                    ProgressView("Fetching Data...")
                        .padding()
                }

                // Search History
                List(viewModel.searchHistory, id: \.self) { symbol in
                    NavigationLink(destination: StockDetailView(symbol: symbol, ohlcData: viewModel.ohlcData[symbol] ?? [])) {
                        Text(symbol)
                            .font(.headline)
                            .padding()
                    }
                    .swipeActions {
                        But<PERSON>(role: .destructive) {
                            viewModel.removeFromHistory(symbol)
                        } label: {
                            Label("Delete", systemImage: "trash")
                        }
                    }
                }
            }
            .navigationTitle("Stock Search")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink(destination: LogView(viewModel: logsViewModel)) {
                        Image(systemName: "doc.text.magnifyingglass")
                            .badge(logsViewModel.unreadLogsCount) // Show badge for unread logs
                    }
                }
            }
        }
    }
}
