//
//  ManualOHLCUpdateView.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import SwiftUI

struct ManualOHLCUpdateView: View {
    @State private var symbol = ""
    @State private var openPrice = ""
    @State private var highPrice = ""
    @State private var lowPrice = ""
    @State private var closePrice = ""
    @State private var showAlert = false
    @State private var alertMessage = ""
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Stock Symbol")) {
                    TextField("Enter Stock Symbol", text: $symbol)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                Section(header: Text("OHLC Values")) {
                    TextField("Open Price", text: $openPrice)
                        .keyboardType(.decimalPad)
                    TextField("High Price", text: $highPrice)
                        .keyboardType(.decimalPad)
                    TextField("Low Price", text: $lowPrice)
                        .keyboardType(.decimalPad)
                    TextField("Close Price", text: $closePrice)
                        .keyboardType(.decimalPad)
                }

                Section {
                    Button(action: saveOHLCValues) {
                        Text("Save OHLC Values")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                }
            }
            .navigationTitle("Manual OHLC Update")
            .alert(isPresented: $showAlert) {
                Alert(title: Text("OHLC Update"), message: Text(alertMessage), dismissButton: .default(Text("OK")) {
                    if alertMessage.contains("Successfully") {
                        presentationMode.wrappedValue.dismiss()
                    }
                })
            }
        }
    }

    private func saveOHLCValues() {
        guard !symbol.isEmpty,
              let open = Double(openPrice),
              let high = Double(highPrice),
              let low = Double(lowPrice),
              let close = Double(closePrice) else {
            alertMessage = "Please fill in all fields with valid numbers."
            showAlert = true
            return
        }

        DatabaseManager.shared.saveManualOHLCData(symbol: symbol, date: Date(), open: open, high: high, low: low, close: close)
        
        alertMessage = "Successfully saved OHLC data for \(symbol)"
        showAlert = true
        
        // Clear the input fields
        symbol = ""
        openPrice = ""
        highPrice = ""
        lowPrice = ""
        closePrice = ""
    }
}
