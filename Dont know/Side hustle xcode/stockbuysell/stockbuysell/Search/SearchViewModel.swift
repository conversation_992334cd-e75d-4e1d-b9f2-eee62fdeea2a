//
//  SearchViewModel.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation

class SearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchHistory = [String]()
    @Published var ohlcData: [String: [OHLCEntry]] = [:]
    @Published var isLoading = false

    private let apiService = APIService.shared

    func searchStock() {
        let symbol = searchText.uppercased().trimmingCharacters(in: .whitespacesAndNewlines)

        guard !symbol.isEmpty else { return }

        // Prevent duplicate searches
        if !searchHistory.contains(symbol) {
            searchHistory.insert(symbol, at: 0)

            // Limit history to the last 20 searches
            if searchHistory.count > 20 {
                searchHistory.removeLast()
            }
        }

        isLoading = true

        Task {
            do {
                let data = try await apiService.fetchOHLCData(for: symbol)
                DispatchQueue.main.async {
                    self.ohlcData[symbol] = data
                    self.isLoading = false
                }
            } catch {
                print("Error fetching data for \(symbol): \(error)")
                DispatchQueue.main.async {
                    self.isLoading = false
                }
            }
        }

        // Clear the search text after searching
        searchText = ""
    }

    func removeFromHistory(_ symbol: String) {
        if let index = searchHistory.firstIndex(of: symbol) {
            searchHistory.remove(at: index)
            ohlcData[symbol] = nil // Remove associated data as well
        }
    }
}
