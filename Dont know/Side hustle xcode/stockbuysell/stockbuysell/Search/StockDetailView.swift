//
//  StockDetailView.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import SwiftUI
import Charts

struct StockDetailView: View {
    let symbol: String
    let ohlcData: [OHLCEntry]

    var body: some View {
        VStack(alignment: .leading) {
            Text("\(symbol) - Last 1 Month")
                .font(.headline)
                .padding()

            if ohlcData.isEmpty {
                Text("No data available.")
                    .foregroundColor(.red)
                    .padding()
            } else {
                Chart(ohlcData) { entry in
                    LineMark(
                        x: .value("Date", entry.date),
                        y: .value("Close", entry.close)
                    )
                    PointMark(
                        x: .value("Date", entry.date),
                        y: .value("Close", entry.close)
                    )
                }
                .frame(height: 300)
                .padding()

                List(ohlcData.prefix(30), id: \.id) { entry in
                    HStack {
                        Text(entry.date)
                            .font(.caption)
                        Spacer()
                        Text(String(format: "O %.2f H %.2f L %.2f C %.2f", entry.open, entry.high, entry.low, entry.close))
                            .font(.caption2)
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .navigationTitle(symbol)
    }
}
