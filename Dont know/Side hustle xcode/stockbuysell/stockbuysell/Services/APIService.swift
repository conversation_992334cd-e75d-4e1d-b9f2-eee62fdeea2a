//
//  APIService.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//


import Foundation

class APIService {
    static let shared = APIService()
    private let apiKey = "C638WM1RA1WZNE31"

    var logsViewModel: LogsViewModel?

    func fetchOHLCData(for symbol: String) async throws -> [OHLCEntry] {
        let urlString = "https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=\(symbol)&apikey=\(apiKey)"
        guard let url = URL(string: urlString) else {
            logsViewModel?.addLog(message: "Invalid URL for \(symbol)", type: .error)
            throw APIError.invalidURL
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                logsViewModel?.addLog(message: "Invalid response for \(symbol)", type: .error)
                throw APIError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200:
                logsViewModel?.addLog(message: "Successfully fetched data for \(symbol)", type: .success)
                return try parseOHLCResponse(data)
            case 401:
                logsViewModel?.addLog(message: "Unauthorized access for \(symbol)", type: .error)
                throw APIError.unauthorized
            case 429:
                logsViewModel?.addLog(message: "API rate limit exceeded for \(symbol)", type: .warning)
                throw APIError.rateLimitExceeded
            default:
                logsViewModel?.addLog(message: "Unexpected status code \(httpResponse.statusCode) for \(symbol)", type: .error)
                throw APIError.unexpectedStatusCode(httpResponse.statusCode)
            }
        } catch {
            if error is URLError {
                logsViewModel?.addLog(message: "Network error for \(symbol): \(error.localizedDescription)", type: .error)
            } else {
                logsViewModel?.addLog(message: "Unexpected error for \(symbol): \(error.localizedDescription)", type: .error)
            }
            throw error
        }
    }

    private func parseOHLCResponse(_ data: Data) throws -> [OHLCEntry] {
        guard let json = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
              let timeSeries = json["Time Series (Daily)"] as? [String: [String: String]] else {
            throw APIError.invalidResponse
        }

        var ohlcEntries = [OHLCEntry]()
        for (date, values) in timeSeries.sorted(by: { $0.key > $1.key }) { // Sort by date descending
            if let open = Double(values["1. open"] ?? ""),
               let high = Double(values["2. high"] ?? ""),
               let low = Double(values["3. low"] ?? ""),
               let close = Double(values["4. close"] ?? "") {
                ohlcEntries.append(OHLCEntry(date: date, open: open, high: high, low: low, close: close))
            }
        }
        return ohlcEntries
    }
}

enum APIError: Error {
    case invalidURL
    case invalidResponse
    case unauthorized
    case rateLimitExceeded
    case unexpectedStatusCode(Int)
}
