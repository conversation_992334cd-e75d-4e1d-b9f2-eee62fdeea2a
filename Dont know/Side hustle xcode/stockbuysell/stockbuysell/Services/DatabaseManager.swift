//
//  DatabaseManager.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//

import Foundation
import SQLite3

class DatabaseManager {
    static let shared = DatabaseManager()
    private var db: OpaquePointer?

    private init() {
        openDatabase()
        createTables()
    }

    private func openDatabase() {
        let fileURL = try! FileManager.default
            .urls(for: .documentDirectory, in: .userDomainMask)
            .first!
            .appendingPathComponent("StockData.sqlite")

        if sqlite3_open(fileURL.path, &db) != SQLITE_OK {
            print("Error opening database")
        }
    }

    private func createTables() {
        let createHistoryTableQuery = """
        CREATE TABLE IF NOT EXISTS SearchHistory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT UNIQUE NOT NULL
        );
        """
        
        let createStockDataTableQuery = """
        CREATE TABLE IF NOT EXISTS StockData (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT UNIQUE NOT NULL,
            ohlcData TEXT NOT NULL,
            lastUpdated DATE NOT NULL
        );
        """

        executeQuery(query: createHistoryTableQuery)
        executeQuery(query: createStockDataTableQuery)
    }

    private func executeQuery(query: String) {
        var statement: OpaquePointer?
        if sqlite3_prepare_v2(db, query, -1, &statement, nil) == SQLITE_OK {
            sqlite3_step(statement)
            sqlite3_finalize(statement)
        } else {
            print("Error executing query: \(query)")
        }
    }

    func saveSearchHistory(symbol: String) {
        let insertQuery = "INSERT OR IGNORE INTO SearchHistory (symbol) VALUES (?);"
        var statement: OpaquePointer?

        do {
            if sqlite3_prepare_v2(db, insertQuery, -1, &statement, nil) == SQLITE_OK {
                sqlite3_bind_text(statement, 1, symbol, -1, nil)

                if sqlite3_step(statement) != SQLITE_DONE {
                    throw DatabaseError.insertFailed(message: String(cString: sqlite3_errmsg(db)))
                }

                sqlite3_finalize(statement)
            } else {
                throw DatabaseError.prepareFailed(message: String(cString: sqlite3_errmsg(db)))
            }
        } catch let error as DatabaseError {
            print("Database Error:", error.localizedDescription)
        } catch {
            print("Unexpected Error:", error.localizedDescription)
        }
    }

    enum DatabaseError: Error {
        case insertFailed(message: String)
        case prepareFailed(message: String)

        var localizedDescription: String {
            switch self {
            case .insertFailed(let message): return "Insert failed with message \(message)"
            case .prepareFailed(let message): return "Prepare failed with message \(message)"
            }
        }
    }


    func fetchSearchHistory() -> [String] {
        let selectQuery = "SELECT symbol FROM SearchHistory;"
        var statement: OpaquePointer?
        
        var symbols = [String]()
        
        if sqlite3_prepare_v2(db, selectQuery, -1, &statement, nil) == SQLITE_OK {
            while sqlite3_step(statement) == SQLITE_ROW {
                if let cString = sqlite3_column_text(statement, 0) {
                    symbols.append(String(cString: cString))
                }
            }
            
            sqlite3_finalize(statement)
        }
        
        return symbols
    }

    func saveStockData(symbol: String, ohlcData: String) {
        let insertQuery = """
        INSERT OR REPLACE INTO StockData (symbol, ohlcData, lastUpdated)
        VALUES (?, ?, datetime('now'));
        """
        
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, insertQuery, -1, &statement, nil) == SQLITE_OK {
            sqlite3_bind_text(statement, 1, symbol, -1, nil)
            sqlite3_bind_text(statement, 2, ohlcData, -1, nil)
            
            if sqlite3_step(statement) == SQLITE_DONE {
                print("Successfully saved stock data for \(symbol)")
            } else {
                print("Failed to save stock data for \(symbol)")
            }
            
            sqlite3_finalize(statement)
        }
    }

    func fetchStockData(symbol: String) -> String? {
        let selectQuery = "SELECT ohlcData FROM StockData WHERE symbol = ?;"
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, selectQuery, -1, &statement, nil) == SQLITE_OK {
            sqlite3_bind_text(statement, 1, symbol, -1, nil)
            
            if sqlite3_step(statement) == SQLITE_ROW {
                if let cString = sqlite3_column_text(statement, 0) {
                    return String(cString: cString)
                }
            }
            
            sqlite3_finalize(statement)
        }
        
        return nil
    }
    
    func saveManualOHLCData(symbol: String, date: Date, open: Double, high: Double, low: Double, close: Double) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        let insertQuery = """
        INSERT OR REPLACE INTO StockData (symbol, date, open, high, low, close, is_manual)
        VALUES (?, ?, ?, ?, ?, ?, 1);
        """
        
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, insertQuery, -1, &statement, nil) == SQLITE_OK {
            sqlite3_bind_text(statement, 1, (symbol as NSString).utf8String, -1, nil)
            sqlite3_bind_text(statement, 2, (dateString as NSString).utf8String, -1, nil)
            sqlite3_bind_double(statement, 3, open)
            sqlite3_bind_double(statement, 4, high)
            sqlite3_bind_double(statement, 5, low)
            sqlite3_bind_double(statement, 6, close)
            
            if sqlite3_step(statement) == SQLITE_DONE {
                print("Successfully saved manual OHLC data for \(symbol)")
            } else {
                print("Failed to save manual OHLC data for \(symbol)")
            }
            
            sqlite3_finalize(statement)
        }
    }
}
