//
//  StockBuySellApp.swift
//  stockbuysell
//
//  Created by <PERSON><PERSON><PERSON>  on 20/03/25.
//

import SwiftUI

@main
struct StockBuySellApp: App {
    @StateObject private var chartsViewModel = ChartsViewModel()
    @StateObject private var searchViewModel = SearchViewModel()
    @StateObject private var predictionsViewModel = PredictionsViewModel()
    @StateObject private var logsViewModel = LogsViewModel()

    var body: some Scene {
        WindowGroup {
            TabView {
                // Charts Tab
                ChartsView(searchViewModel: searchViewModel, chartsViewModel: chartsViewModel)
                    .tabItem {
                        Label("Charts", systemImage: "chart.line.uptrend.xyaxis")
                    }

                // Search Tab
                ContentView(viewModel: searchViewModel, logsViewModel: logsViewModel) // Pass logsViewModel here
                    .tabItem {
                        Label("Search", systemImage: "magnifyingglass")
                    }

                // Predictions Tab
                NotificationView(viewModel: predictionsViewModel)
                    .tabItem {
                        Label("Alerts", systemImage: "bell.badge.fill")
                    }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink(destination: LogView(viewModel: logsViewModel)) {
                        Image(systemName: "doc.text.magnifyingglass")
                            .badge(logsViewModel.unreadLogsCount)
                    }
                }
            }
        }
    }
}
