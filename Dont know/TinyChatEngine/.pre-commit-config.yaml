exclude: "code_generator/tflite/.*"
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: trailing-whitespace
      - id: mixed-line-ending
        args: ["--fix=lf"]
      - id: end-of-file-fixer
      - id: check-merge-conflict
      - id: requirements-txt-fixer
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: debug-statements
      - id: check-toml
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.10
    hooks:
      - id: mdformat
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--sp", "pyproject.toml"]
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-comprehensions==3.7.0
          - flake8-docstrings==1.6.0
  - repo: local
    hooks:
      - id: pylint
        name: pylint
        entry: pylint
        language: system
        types: [python]
        require_serial: true
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.910-1
    hooks:
      - id: mypy
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v13.0.0
    hooks:
      - id: clang-format
