name: Comment Check Amalgamation
on:
  workflow_run:
    workflows: ["Check amalgamation"]
    types:
      - completed

permissions: {}

jobs:
  comment:
    if: ${{ github.event.workflow_run.conclusion == 'failure' }}
    runs-on: ubuntu-latest
    permissions:
      contents: read
      actions: read
      issues: read
      pull-requests: write
    steps:
      - name: 'Download artifact'
        uses: actions/github-script@98814c53be79b1d30f795b907e553d8679345975 # v6.4.0
        with:
          script: |
            var artifacts = await github.rest.actions.listWorkflowRunArtifacts({
               owner: context.repo.owner,
               repo: context.repo.repo,
               run_id: ${{github.event.workflow_run.id }},
            });
            var matchArtifact = artifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "pr"
            })[0];
            var download = await github.rest.actions.downloadArtifact({
               owner: context.repo.owner,
               repo: context.repo.repo,
               artifact_id: matchArtifact.id,
               archive_format: 'zip',
            });
            var fs = require('fs');
            fs.writeFileSync('${{github.workspace}}/pr.zip', Buffer.from(download.data));
      - run: unzip pr.zip

      - name: 'Comment on PR'
        uses: actions/github-script@98814c53be79b1d30f795b907e553d8679345975 # v6.4.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            var fs = require('fs');
            const author = fs.readFileSync('./author')
            const issue_number = Number(fs.readFileSync('./number'));
            const opts = github.rest.issues.listForRepo.endpoint.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              creator: author,
              state: 'all'
            })
            let first = true
            const issues = await github.paginate(opts)
            for (const issue of issues) {
              if (issue.number === issue_number) {
                continue
              }
              if (issue.pull_request) {
                first = false
                break
              }
            }
            await github.rest.issues.createComment({
              issue_number: issue_number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '## 🔴 Amalgamation check failed! 🔴\nThe source code has not been amalgamated.'
                    + (first ? ' @' + author + ' Please read and follow the [Contribution Guidelines]'
                               + '(https://github.com/nlohmann/json/blob/develop/.github/CONTRIBUTING.md#files-to-change).'
                             : '')
              })
