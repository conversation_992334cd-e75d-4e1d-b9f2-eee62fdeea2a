name: Ubuntu

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  workflow_dispatch:
  
permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  ci_test_clang:
    runs-on: ubuntu-latest
    container: silkeh/clang:dev
    steps:
      - name: Install git and unzip
        run: apt-get update ; apt-get install -y git unzip
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_clang

  ci_test_gcc:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_gcc

  ci_static_analysis:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    strategy:
      matrix:
        target: [ci_cppcheck, ci_test_valgrind, ci_test_amalgamation, ci_test_single_header, ci_single_binaries, ci_infer]
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_static_analysis_ubuntu:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [ci_cpplint, ci_reproducible_tests, ci_non_git_tests, ci_offline_testdata]
    steps:
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_static_analysis_clang:
    runs-on: ubuntu-latest
    container: silkeh/clang:dev
    strategy:
      matrix:
        target: [ci_clang_tidy, ci_test_clang_sanitizer, ci_clang_analyze]
    steps:
      - name: Install git, clang-tools, and unzip
        run: apt-get update ; apt-get install -y git clang-tools unzip
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_cmake_options:
    runs-on: ubuntu-latest
    container: ubuntu:focal
    strategy:
      matrix:
        target: [ci_cmake_flags, ci_test_diagnostics, ci_test_noexceptions, ci_test_noimplicitconversions, ci_test_legacycomparison, ci_test_noglobaludls]
    steps:
      - name: Install build-essential
        run: apt-get update ; apt-get install -y build-essential unzip wget git
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_test_coverage:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    permissions:
      contents: read
      checks: write
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_coverage
      - name: Archive coverage report
        uses: actions/upload-artifact@v3
        with:
          name: code-coverage-report
          path: ${{ github.workspace }}/build/html
      - name: Publish report to Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: ${{ github.workspace }}/build/json.info.filtered.noexcept

  ci_test_compilers_gcc:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: ['4', '5', '6', '7', '8', '9', '10', '11', '12', 'latest']
    container: gcc:${{ matrix.compiler }}
    steps:
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_default

  ci_test_compilers_clang:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: ['3.5', '3.6', '3.7', '3.8', '3.9', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15-bullseye', 'latest']
    container: silkeh/clang:${{ matrix.compiler }}
    steps:
      - name: Install unzip and git
        run: apt-get update ; apt-get install -y unzip git
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Set env FORCE_STDCPPFS_FLAG for clang 7 / 8 / 9 / 10
        run: echo "JSON_FORCED_GLOBAL_COMPILE_OPTIONS=-DJSON_HAS_FILESYSTEM=0;-DJSON_HAS_EXPERIMENTAL_FILESYSTEM=0" >> "$GITHUB_ENV"
        if: ${{ matrix.compiler == '7' || matrix.compiler == '8' || matrix.compiler == '9' || matrix.compiler == '10' }}
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_default

  ci_test_compilers:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    strategy:
      matrix:
        compiler: [g++-4.8]
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_${{ matrix.compiler }}

  ci_test_standards_gcc:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    strategy:
      matrix:
        standard: [11, 14, 17, 20, 23]
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_gcc_cxx${{ matrix.standard }}

  ci_test_standards_clang:
    runs-on: ubuntu-latest
    container: silkeh/clang:latest
    strategy:
      matrix:
        standard: [11, 14, 17, 20, 23]
    steps:
      - name: Install git and unzip
        run: apt-get update ; apt-get install -y git unzip
      - uses: actions/checkout@v3
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@latest
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_clang_cxx${{ matrix.standard }}

  ci_cuda_example:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_cuda_example

  ci_icpc:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.2.0
    steps:
      - uses: actions/checkout@v2
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: |
          . /opt/intel/oneapi/setvars.sh
          cmake --build build --target ci_icpc

  ci_reuse_compliance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
      - name: Install REUSE tool
        run:  python -m pip install reuse
      - name: Run REUSE lint
        run: reuse lint

  ci_test_documentation:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [ci_test_examples, ci_test_api_documentation]
    steps:
      - uses: actions/checkout@v3
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}
