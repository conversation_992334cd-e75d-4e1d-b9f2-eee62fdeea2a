include(Find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>StandardArgs)
set(${CMAKE_FIND_PACKAGE_NAME}_CONFIG ${CMAKE_CURRENT_LIST_FILE})
find_package_handle_standard_args(@PROJECT_NAME@ CONFIG_MODE)

if(NOT TARGET @PROJECT_NAME@::@NLOHMANN_JSON_TARGET_NAME@)
    include("${CMAKE_CURRENT_LIST_DIR}/@NLOHMANN_JSON_TARGETS_EXPORT_NAME@.cmake")
    if((NOT TARGET @NLOHMANN_JSON_TARGET_NAME@) AND
       (NOT @PROJECT_NAME@_FIND_VERSION OR
        @PROJECT_NAME@_FIND_VERSION VERSION_LESS 3.2.0))
        add_library(@NLOHMANN_JSON_TARGET_NAME@ INTERFACE IMPORTED)
        set_target_properties(@NLOHMANN_JSON_TARGET_NAME@ PROPERTIES
            INTERFACE_LINK_LIBRARIES @PROJECT_NAME@::@NLOHMANN_JSON_TARGET_NAME@
        )
    endif()
endif()
