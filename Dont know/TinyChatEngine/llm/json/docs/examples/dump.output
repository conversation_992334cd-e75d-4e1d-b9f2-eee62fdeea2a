objects:
{"one":1,"two":2}

{"one":1,"two":2}

{
"one": 1,
"two": 2
}

{
    "one": 1,
    "two": 2
}

{
	"one": 1,
	"two": 2
}

arrays:
[1,2,4,8,16]

[1,2,4,8,16]

[
1,
2,
4,
8,
16
]

[
    1,
    2,
    4,
    8,
    16
]

[
	1,
	2,
	4,
	8,
	16
]

strings:
"Hellö 😀!"
"Hell\u00f6 \ud83d\ude00!"
[json.exception.type_error.316] invalid UTF-8 byte at index 2: 0xA9
string with replaced invalid characters: "ä�ü"
string with ignored invalid characters: "äü"
