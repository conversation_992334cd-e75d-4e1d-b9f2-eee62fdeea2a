# <small>nlohmann::json_pointer::</small>pop_back

```cpp
void pop_back();
```

Remove last reference token.

## Exceptions

Throws [out_of_range.405](../../home/<USER>

## Complexity

Constant.

## Examples

??? example

    The example shows the usage of `pop_back`.
     
    ```cpp
    --8<-- "examples/json_pointer__pop_back.cpp"
    ```
    
    Output:
    
    ```json
    --8<-- "examples/json_pointer__pop_back.output"
    ```

## Version history

Added in version 3.6.0.
