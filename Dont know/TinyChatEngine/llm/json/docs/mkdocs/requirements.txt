Babel==2.11.0
certifi==2022.12.7
charset-normalizer==2.1.1
click==8.1.3
csscompressor==0.9.5
future==0.18.3
ghp-import==2.1.0
gitdb==4.0.10
GitPython==3.1.29
htmlmin==0.1.12
httplib2==0.21.0
idna==3.4
importlib-metadata==5.1.0
Jinja2==3.1.2
joblib==1.2.0
jsmin==3.0.1
livereload==2.6.3
lunr==0.6.2
Markdown==3.3.7            # we cannot install a more recent version yet as mkdocs 1.4.2 depends on markdown<3.4
markdown-include==0.8.0
MarkupSafe==2.1.1
mergedeep==1.3.4
mkdocs==1.4.2
mkdocs-git-revision-date-localized-plugin==1.1.0
mkdocs-material==8.5.11
mkdocs-material-extensions==1.1.1
mkdocs-minify-plugin==0.6.2
mkdocs-redirects==1.2.0
mkdocs-simple-hooks==0.1.5
nltk==3.8
packaging==22.0
plantuml==0.3.0
plantuml-markdown==3.7.3
Pygments==2.13.0
pymdown-extensions==9.9
pyparsing==3.0.9
python-dateutil==2.8.2
pytz==2022.7
PyYAML==6.0
pyyaml_env_tag==0.1
regex==2022.10.31
requests==2.28.1
six==1.16.0
smmap==5.0.0
tornado==6.2
tqdm==4.64.1
urllib3==1.26.13
watchdog==2.2.0
zipp==3.11.0
