.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "21"
mks.enable3d = "TRUE"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
hpet0.present = "TRUE"
nvram = "Ubuntu 64-bit 22.04.3 2.nvram"
virtualHW.productCompatibility = "hosted"
powerType.powerOff = "soft"
powerType.powerOn = "soft"
powerType.suspend = "soft"
powerType.reset = "soft"
displayName = "Ubuntu 64-bit 22.04.3"
usb.vbluetooth.startConnected = "TRUE"
guestOS = "ubuntu-64"
tools.syncTime = "TRUE"
tools.upgrade.policy = "upgradeAtPowerCycle"
sound.autoDetect = "TRUE"
sound.fileName = "-1"
sound.present = "TRUE"
numvcpus = "8"
cpuid.coresPerSocket = "1"
vcpu.hotadd = "TRUE"
memsize = "8192"
mem.hotadd = "TRUE"
scsi0.virtualDev = "lsilogic"
scsi0.present = "TRUE"
sata0.present = "TRUE"
scsi0:0.fileName = "Virtual Disk.vmdk"
scsi0:0.present = "TRUE"
sata0:1.deviceType = "cdrom-image"
sata0:1.fileName = "/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso"
sata0:1.present = "TRUE"
sata0:0.deviceType = "cdrom-image"
sata0:0.fileName = "autoinst.iso"
sata0:0.present = "TRUE"
usb.present = "TRUE"
ehci.present = "TRUE"
svga.graphicsMemoryKB = "8388608"
ethernet0.connectionType = "nat"
ethernet0.addressType = "generated"
ethernet0.virtualDev = "e1000"
ethernet0.linkStatePropagation.enable = "TRUE"
floppy0.fileType = "file"
floppy0.fileName = "autoinst.flp"
ethernet0.present = "TRUE"
extendedConfigFile = "Ubuntu 64-bit 22.04.3 2.vmxf"
floppy0.clientDevice = "FALSE"
vmxstats.filename = "Ubuntu 64-bit 22.04.3 2.scoreboard"
uuid.bios = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
uuid.location = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
pciBridge0.pciSlotNumber = "17"
pciBridge4.pciSlotNumber = "21"
pciBridge5.pciSlotNumber = "22"
pciBridge6.pciSlotNumber = "23"
pciBridge7.pciSlotNumber = "24"
scsi0.pciSlotNumber = "16"
usb.pciSlotNumber = "32"
ethernet0.pciSlotNumber = "33"
sound.pciSlotNumber = "34"
ehci.pciSlotNumber = "35"
sata0.pciSlotNumber = "36"
scsi0:0.redo = ""
svga.vramSize = "268435456"
vmotion.checkpointFBSize = "4194304"
vmotion.checkpointSVGAPrimarySize = "268435456"
vmotion.svga.mobMaxSize = "1073741824"
vmotion.svga.graphicsMemoryKB = "8388608"
vmotion.svga.supports3D = "1"
vmotion.svga.baseCapsLevel = "9"
vmotion.svga.maxPointSize = "189"
vmotion.svga.maxTextureSize = "16384"
vmotion.svga.maxVolumeExtent = "2048"
vmotion.svga.maxTextureAnisotropy = "16"
vmotion.svga.lineStipple = "0"
vmotion.svga.dxMaxConstantBuffers = "15"
vmotion.svga.dxProvokingVertex = "0"
vmotion.svga.sm41 = "1"
vmotion.svga.multisample2x = "1"
vmotion.svga.multisample4x = "1"
vmotion.svga.msFullQuality = "1"
vmotion.svga.logicOps = "1"
vmotion.svga.bc67 = "9"
vmotion.svga.sm5 = "1"
vmotion.svga.multisample8x = "1"
vmotion.svga.logicBlendOps = "0"
vmotion.svga.maxForcedSampleCount = "8"
vmotion.svga.gl43 = "1"
ethernet0.generatedAddress = "00:0c:29:99:78:11"
ethernet0.generatedAddressOffset = "0"
vmci0.id = "1620670481"
monitor.phys_bits_used = "45"
cleanShutdown = "TRUE"
softPowerOff = "TRUE"
usb:1.speed = "2"
usb:1.present = "TRUE"
usb:1.deviceType = "hub"
usb:1.port = "1"
usb:1.parent = "-1"
svga.guestBackedPrimaryAware = "TRUE"
guestInfo.detailed.data = "architecture='X86' bitness='64' distroAddlVersion='22.04.5 LTS (Jammy Jellyfish)' distroName='Ubuntu' distroVersion='22.04' familyName='Linux' kernelVersion='6.8.0-60-generic' prettyName='Ubuntu 22.04.5 LTS'"
keyboardAndMouseProfile = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
gui.fitGuestUsingNativeDisplayResolution = "TRUE"
chipset.useAcpiBattery = "TRUE"
chipset.useApmBattery = "TRUE"
toolsInstallManager.updateCounter = "1"
tools.capability.verifiedSamlToken = "TRUE"
checkpoint.vmState = ""
gui.lastPoweredViewMode = "fullscreen"
usb:0.present = "TRUE"
usb:0.deviceType = "hid"
usb:0.port = "0"
usb:0.parent = "-1"
ehci:0.present = "TRUE"
ehci:0.deviceType = "video"
ehci:0.port = "0"
ehci:0.parent = "-1"
