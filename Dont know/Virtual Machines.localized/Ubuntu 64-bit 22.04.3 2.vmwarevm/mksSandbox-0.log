2025-05-02T06:20:10.883Z In(05) mks Log for VMware Fusion pid=21172 version=13.5.2 build=build-23775688 option=Release
2025-05-02T06:20:10.883Z In(05) mks The host is x86_64.
2025-05-02T06:20:10.883Z In(05) mks Host codepage=UTF-8 encoding=UTF-8
2025-05-02T06:20:10.883Z In(05) mks Host is macOS 15.5 (24F5068b) Darwin 24.5.0
2025-05-02T06:20:10.883Z In(05) mks Host offset from UTC is +05:30.
2025-05-02T06:20:11.087Z In(05) main  MKSSandbox: MKS_UUID=52 97 1a 8e 70 37 c4 ff-f4 c9 4d ce 76 8d f2 5f
2025-05-02T06:20:11.087Z In(05) main  MKSRoleMain: Powering on MKS
2025-05-02T06:20:11.087Z In(05) main  MKS PowerOn
2025-05-02T06:20:11.087Z In(05) mks  VTHREAD 123145417494528 "mks" tid 178815
2025-05-02T06:20:11.087Z In(05) mks  MKS thread is alive
2025-05-02T06:20:11.087Z In(05) svga  VTHREAD 123145425895424 "svga" tid 178816
2025-05-02T06:20:11.087Z In(05) svga  SVGA thread is alive
2025-05-02T06:20:11.087Z In(05) mks  MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-05-02T06:20:11.090Z In(05) mks  MKS-RenderMain: PowerOn allowed MTLRenderer 
2025-05-02T06:20:11.090Z In(05) mks  MKS-RenderMain: ISB not enabled by config
2025-05-02T06:20:11.090Z In(05) mks  MKS-RenderMain: Collecting RenderOps caps from MTLRenderer
2025-05-02T06:20:11.090Z In(05) mks  MKS-RenderMain: Starting MTLRenderer
2025-05-02T06:20:11.105Z In(05) mks  Metal Device name: AMD Radeon Pro 560X
2025-05-02T06:20:11.105Z In(05) mks  Metal Device removable: FALSE
2025-05-02T06:20:11.105Z In(05) mks  Metal Device headless: FALSE
2025-05-02T06:20:11.105Z In(05) mks  Metal Device lowPower: FALSE
2025-05-02T06:20:11.105Z In(05) mks  Metal Device maxThreadsPerThreadgroup: 1024x1024x1024
2025-05-02T06:20:11.105Z In(05) mks  Metal Device recommendedMaxWorkingSetSize: 4294967296
2025-05-02T06:20:11.105Z In(05) mks  Metal Device depth24Stencil8PixelFormatSupported: TRUE
2025-05-02T06:20:11.105Z In(05) mks  Metal Device GPU family: 2
2025-05-02T06:20:11.105Z In(05) mks  Metal Device Apple GPU family: 0
2025-05-02T06:20:11.105Z In(05) mks  Metal RW Texture Tier: 2
2025-05-02T06:20:11.105Z In(05) mks  Metal Arg Buffer Support: Tier 2
2025-05-02T06:20:11.106Z In(05) mks  Metal Device PCI ID: 1002:67EF
2025-05-02T06:20:11.106Z In(05) mks  Metal GPU chip: Baffin (GCN4)
2025-05-02T06:20:11.663Z In(05) mks  Metal Device plugin: AMDMTLBronzeDriver 7.0.3 24657
2025-05-02T06:20:11.663Z In(05) mks  Metal Device: Pull Model Interpolation support: No
2025-05-02T06:20:11.663Z In(05) mks  Metal Shading Language: 2.3
2025-05-02T06:20:11.701Z In(05) mks  MTLRenderer: Intersecting with caps of device Intel(R) UHD Graphics 630
2025-05-02T06:20:11.702Z In(05) mks  Started Shim3D
2025-05-02T06:20:11.703Z In(05) mks  MKS-RenderMain: Started MTLRenderer
2025-05-02T06:20:11.703Z In(05) mks  MKS-RenderMain: Found Full Renderer: MTLRenderer
2025-05-02T06:20:11.703Z In(05) mks  MKS-RenderMain: maxTextureSize=16384
2025-05-02T06:20:11.703Z In(05) mks  MKSRemoteMgr: Set default display name: Ubuntu 64-bit 22.04.3
2025-05-02T06:20:11.703Z In(05) mks  MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-05-02T06:20:11.703Z In(05) mks  MKSRemoteMgr: Using default VNC keymap table "us"
2025-05-02T06:20:11.703Z In(05) main  MKSSandboxComm: Late PowerOn
2025-05-02T06:20:11.703Z In(05) main  hostCPUID vendor: GenuineIntel
2025-05-02T06:20:11.703Z In(05) main  hostCPUID family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:20:11.703Z In(05) main  hostCPUID codename: Coffee Lake-S/H
2025-05-02T06:20:11.703Z In(05) main  hostCPUID name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000000, 0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000001, 0: 0x000906ea 0x00100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000002, 0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000004, 0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000004, 2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11142120
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000006, 0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000007, 0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000a, 0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 3: 0x00000040 0x000003c0 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 4: 0x00000040 0x00000400 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000014, 1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000015, 0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 00000016, 0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000003, 0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000004, 0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:20:11.703Z In(05) main  hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:11.703Z In(05) main  PStrIntern expansion: nBkts=256
2025-05-02T06:20:11.703Z In(05) main  VMFeature_GetHostCaps: hostCaps:
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.sse3 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.pclmulqdq = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.mwait = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.vmx = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ssse3 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.fma = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.cmpxchg16b = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.pcid = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.sse41 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.sse42 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.movbe = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.popcnt = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.aes = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xsave = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.avx = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.f16c = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.rdrand = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ds = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ss = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.fsgsbase = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.bmi1 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.avx2 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.smep = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.bmi2 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.enfstrg = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.invpcid = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.rtm = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.rdseed = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.adx = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.smap = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.clflushopt = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.mdclear = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.stibp = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.fcmd = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ssbd = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xcr0_master_sse = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xcr0_master_bndregs = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xsaveopt = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xsavec = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xgetbv_ecx1 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.xsaves = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.lahf64 = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.abm = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.3dnprefetch = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.nx = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.pdpe1gb = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.rdtscp = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.lm = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.intel = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ibrs = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.ibpb = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.mpx = 1
2025-05-02T06:20:11.703Z In(05) main  Capability Found: cpuid.hle = 1
2025-05-02T06:20:11.703Z In(05) main  MKSRoleMain: PowerOn finished.
2025-05-02T06:20:11.703Z In(05) svga  ReplayFifo: mksReplay format version=22
2025-05-02T06:20:11.703Z In(05) svga  SVGA3dDevCaps: replayFifo host
2025-05-02T06:20:11.703Z In(05) svga    cap[  0]: 0x00000001 (3D)
2025-05-02T06:20:11.703Z In(05) svga    cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:20:11.703Z In(05) svga    cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:20:11.703Z In(05) svga    cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:20:11.703Z In(05) svga    cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:20:11.703Z In(05) svga    cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:20:11.703Z In(05) svga    cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:20:11.703Z In(05) svga    cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:20:11.703Z In(05) svga    cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:20:11.703Z In(05) svga    cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 17]: 511.000000 (MAX_POINT_SIZE)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:20:11.703Z In(05) svga    cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:20:11.704Z In(05) svga    cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:20:11.704Z In(05) svga    cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:20:11.704Z In(05) svga    cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:20:11.704Z In(05) svga    cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:20:11.704Z In(05) svga    cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:20:11.704Z In(05) svga    cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:20:11.704Z In(05) svga    cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:20:11.704Z In(05) svga    cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:20:11.704Z In(05) svga    cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:20:11.704Z In(05) svga    cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:20:11.704Z In(05) svga    cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:20:11.704Z In(05) svga    cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:20:11.704Z In(05) svga    cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:20:11.704Z In(05) svga    cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:20:11.704Z In(05) svga    cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:11.704Z In(05) svga    cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:20:11.704Z In(05) svga    cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:20:11.704Z In(05) svga    cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:20:11.704Z In(05) svga    cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:20:11.704Z In(05) svga    cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:20:11.704Z In(05) svga    cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:20:11.704Z In(05) svga    cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:20:11.704Z In(05) svga    cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:20:11.704Z In(05) svga    cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:20:11.704Z In(05) svga    cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:20:11.704Z In(05) svga    cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:20:11.704Z In(05) svga    cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:20:11.704Z In(05) svga    cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:20:11.704Z In(05) svga    cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:20:11.704Z In(05) svga    cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:20:11.704Z In(05) svga    cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:20:11.704Z In(05) svga    cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:20:11.704Z In(05) svga    cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:20:11.704Z In(05) svga    cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:20:11.704Z In(05) svga    cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:20:11.704Z In(05) svga    cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:20:11.704Z In(05) svga    cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:20:11.704Z In(05) svga    cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:20:11.704Z In(05) svga    cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:20:11.704Z In(05) svga    cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:20:11.704Z In(05) svga    cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:20:11.704Z In(05) svga    cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:20:11.704Z In(05) svga    cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:20:11.704Z In(05) svga    cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:20:11.704Z In(05) svga    cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:20:11.704Z In(05) svga    cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:20:11.704Z In(05) svga    cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:20:11.704Z In(05) svga    cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:20:11.704Z In(05) svga    cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:20:11.704Z In(05) svga    cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:20:11.704Z In(05) svga    cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[244]: 0x00000001 (SM41)
2025-05-02T06:20:11.704Z In(05) svga    cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:20:11.704Z In(05) svga    cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:20:11.704Z In(05) svga    cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:20:11.704Z In(05) svga    cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:20:11.704Z In(05) svga    cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:20:11.704Z In(05) svga    cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:20:11.704Z In(05) svga    cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:20:11.704Z In(05) svga    cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:20:11.704Z In(05) svga    cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:20:11.704Z In(05) svga    cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:20:11.704Z In(05) svga    cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:20:11.704Z In(05) svga    cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:20:11.704Z In(05) svga    cap[258]: 0x00000001 (SM5)
2025-05-02T06:20:11.704Z In(05) svga    cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:20:11.704Z In(05) svga    cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:20:11.704Z In(05) svga    cap[261]: 0x00000001 (GL43)
2025-05-02T06:20:11.707Z In(05) svga  MKSRoleReplay: MKSRoleReplay_Run: Starting.
2025-05-02T06:20:11.735Z In(05) main  MKSMacos: Starting CFRunLoop...
2025-05-02T06:20:28.212Z In(05) mks  MKSThread: Requesting MKS exit
2025-05-02T06:20:28.212Z In(05) svga  MKSSandboxComm: ISB closed the channel while reading
2025-05-02T06:20:28.212Z In(05) svga  MKSRoleReplay: MKSRoleReplay_Run: Finished.
2025-05-02T06:20:28.212Z In(05) main  MKSMacos: Finished CFRunLoop.
2025-05-02T06:20:28.213Z In(05) svga  MKSSandboxComm: Cleanup
2025-05-02T06:20:28.213Z In(05) main  Stopping MKS/SVGA threads
2025-05-02T06:20:28.213Z In(05) main  MKS/SVGA threads are stopped
2025-05-02T06:20:28.213Z In(05) main  MKSRoleMain: Powering off.
2025-05-02T06:20:28.213Z In(05) main  MKSRoleMain: Powering off MKS
2025-05-02T06:20:28.213Z In(05) mks  MKS-RenderMain: Stopping MTLRenderer
2025-05-02T06:20:28.213Z In(05) mks  MKS-RenderMain: Stopping MTLRenderer
2025-05-02T06:20:28.214Z In(05) mks  Stopped Shim3D
2025-05-02T06:20:28.228Z In(05) mks  MKS-RenderMain: Stopped MTLRenderer
2025-05-02T06:20:28.228Z In(05) mks  MKS PowerOff
