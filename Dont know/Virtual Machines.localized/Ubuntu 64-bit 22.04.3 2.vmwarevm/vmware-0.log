2025-05-02T06:20:10.461Z In(05) vmx Log for VMware Fusion pid=21172 version=13.5.2 build=build-23775688 option=Release
2025-05-02T06:20:10.461Z In(05) vmx The host is x86_64.
2025-05-02T06:20:10.461Z In(05) vmx Host codepage=UTF-8 encoding=UTF-8
2025-05-02T06:20:10.461Z In(05) vmx Host is macOS 15.5 (24F5068b) Darwin 24.5.0
2025-05-02T06:20:10.461Z In(05) vmx Host offset from UTC is +05:30.
2025-05-02T06:20:10.425Z In(05) vmx VTHREAD 140704386637184 "vmx" tid 178768
2025-05-02T06:20:10.431Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:20:10.431Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:20:10.431Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.431Z In(05) vmx Msg_Reset:
2025-05-02T06:20:10.431Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.431Z In(05) vmx ----------------------------------------
2025-05-02T06:20:10.431Z In(05) vmx ConfigDB: Failed to load /Library/Preferences/VMware Fusion/config
2025-05-02T06:20:10.431Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:20:10.431Z In(05) vmx Msg_Reset:
2025-05-02T06:20:10.431Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:20:10.431Z In(05) vmx ----------------------------------------
2025-05-02T06:20:10.431Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config
2025-05-02T06:20:10.431Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:20:10.431Z In(05) vmx Msg_Reset:
2025-05-02T06:20:10.431Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:20:10.431Z In(05) vmx ----------------------------------------
2025-05-02T06:20:10.431Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings
2025-05-02T06:20:10.431Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.431Z In(05) vmx Msg_Reset:
2025-05-02T06:20:10.431Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.431Z In(05) vmx ----------------------------------------
2025-05-02T06:20:10.431Z In(05) vmx ConfigDB: Failed to load ~/Library/Preferences/VMware Fusion/config
2025-05-02T06:20:10.452Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-05-02T06:20:10.452Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx PREF Optional preferences file not found at /Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:20:10.452Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/settings. Using default values.
2025-05-02T06:20:10.452Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/config. Using default values.
2025-05-02T06:20:10.452Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:10.452Z In(05) vmx PREF Optional preferences file not found at /Users/<USER>/Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:20:10.459Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: Client usage
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: Server usage
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:20:10.460Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:20:10.463Z In(05) vmx Hostname=Rohans-MacBook-Pro.local
2025-05-02T06:20:10.463Z In(05) vmx IP=127.0.0.1 (lo0)
2025-05-02T06:20:10.463Z In(05) vmx IP=::1 (lo0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::1 (lo0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::aede:48ff:fe00:1122 (en5)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::b414:79ff:fe37:ddd5 (ap1)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::10ad:8605:26fd:f9e1 (en0)
2025-05-02T06:20:10.463Z In(05) vmx IP=*********** (en0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::6c19:c3ff:fef1:4fa2 (awdl0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::6c19:c3ff:fef1:4fa2 (llw0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::71:ab8a:4db0:d5fa (utun0)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::32fe:1b8d:9696:9ee3 (utun1)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::3804:28be:b3d1:22a6 (utun2)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::ce81:b1c:bd2c:69e (utun3)
2025-05-02T06:20:10.463Z In(05) vmx IP=************ (bridge100)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c64 (bridge100)
2025-05-02T06:20:10.463Z In(05) vmx IP=************* (bridge101)
2025-05-02T06:20:10.463Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c65 (bridge101)
2025-05-02T06:20:10.463Z In(05) vmx System uptime 11276063600 us
2025-05-02T06:20:10.463Z In(05) vmx Command line: "/Applications/VMware Fusion.app/Contents/Library/vmware-vmx" "-E" "en" "-s" "vmx.stdio.keep=TRUE" "-#" "product=64;name=VMware Fusion;version=13.5.2;buildnumber=23775688;licensename=VMware Fusion for Mac OS;licenseversion=13.0;" "-@" "duplex=3;msgs=ui" "-D" "4" "/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx"
2025-05-02T06:20:10.463Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:20:10.464Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:20:10.610Z In(05) vmx Duplex socket: 3
2025-05-02T06:20:10.627Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 16
2025-05-02T06:20:10.627Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 19
2025-05-02T06:20:10.628Z In(05) vmx VigorTransport listening on fd 20
2025-05-02T06:20:10.628Z In(05) vmx Vigor_Init 1
2025-05-02T06:20:10.628Z In(05) vmx Connecting 'ui' to fd '3' with user '(null)'
2025-05-02T06:20:10.628Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-05-02T06:20:10.631Z In(05) vmx /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx: Setup symlink /var/run/vmware/7c69a851e62cb34e34c456da6ed7d4056046773c4790429821e01a8ae1e08e3b -> /var/run/vmware/501/11276210349_21172
2025-05-02T06:20:10.631Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-05-02T06:20:10.631Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-05-02T06:20:10.635Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:20:10.635Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:20:10.635Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:20:10.635Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-05-02T06:20:10.635Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-05-02T06:20:10.657Z In(05) vmx Services_Init: Successfully opened the services.
2025-05-02T06:20:10.658Z In(05) vmx FeatureCompat: No EVC masks.
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID codename: Coffee Lake-S/H
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000000, 0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000001, 0: 0x000906ea 0x00100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000002, 0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11142120
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000006, 0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000040 0x000003c0 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000040 0x00000400 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 00000016, 0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000003, 0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000004, 0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:20:10.658Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR       0x3a =                0x5
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x480 =   0xda040000000004
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x481 =       0x7f0000003f
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x482 = 0xfdf9fffe9500697a
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x483 =   0x737fff00236fff
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x484 =     0xb3ff000091ff
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x489 =           0x3767ff
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48b =   0x515cef000000a2
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48d =       0x7f00000016
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x48f =  0x1ffffff00036dfb
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x490 =    0x3ffff000011fb
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x491 =                  0
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x492 =                  0
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x10a =          0xa000c04
2025-05-02T06:20:10.660Z In(05) vmx Common: MSR      0x122 =                  0
2025-05-02T06:20:10.660Z In(05) vmx UTIL: Current file descriptor limit: soft 12800, hard 4294967295.
2025-05-02T06:20:10.660Z In(05) vmx TSC Hz estimates: vmmon 0, cpuinfo 0, cpufreq 0 sysctlfreq 2592000000. Using 2592000000 Hz
2025-05-02T06:20:10.660Z In(05) vmx PTSC: RefClockToPTSC 0 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:20:10.660Z In(05) vmx PTSC: RefClockToPTSC ((x * 2147483648) >> 31) + -29164334565214
2025-05-02T06:20:10.660Z In(05) vmx PTSC: tscOffset 0
2025-05-02T06:20:10.660Z In(05) vmx PTSC: using user-level reference clock
2025-05-02T06:20:10.660Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-05-02T06:20:10.660Z In(05) vmx PTSC: current PTSC=89550
2025-05-02T06:20:10.663Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 26
2025-05-02T06:20:10.664Z In(05) vmx Current Display Settings:
2025-05-02T06:20:10.664Z In(05) vmx    Display: 0 size: 1920x1200 pixelSize: 3840x2400 position: (0, 0) Primary
2025-05-02T06:20:10.666Z In(05) vmx [10401F000-10513F000): /Applications/VMware Fusion.app/Contents/Library/vmware-vmx
2025-05-02T06:20:10.692Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-05-02T06:20:10.693Z In(05) vmx changing directory to /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/.
2025-05-02T06:20:10.693Z In(05) vmx Config file: /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx
2025-05-02T06:20:10.693Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-05-02T06:20:10.693Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-05-02T06:20:10.697Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.004049 seconds.
2025-05-02T06:20:10.698Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.005518 seconds.
2025-05-02T06:20:10.712Z No(00) vmx ConfigDB: Setting floppy0.clientDevice = "FALSE"
2025-05-02T06:20:10.724Z Wa(03) vmx PowerOn
2025-05-02T06:20:10.724Z In(05) vmx VMX_PowerOn: VMX build 23775688, UI build 23775688
2025-05-02T06:20:10.724Z In(05) vmx Processor affinity not supported on this host OS
2025-05-02T06:20:10.729Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-05-02T06:20:10.731Z In(05) vmx VMXSTATS: Successfully created stats file 'Ubuntu 64-bit 22.04.3 2.scoreboard'
2025-05-02T06:20:10.732Z In(05) vmx VMXSTATS: Update Product Information: VMware Fusion	13.5.2	build-23775688	Release  TotalBlockSize: 48
2025-05-02T06:20:10.732Z In(05) vmx HOST sysname Darwin, nodename Rohans-MacBook-Pro.local, release 24.5.0, version Darwin Kernel Version 24.5.0: Tue Apr 22 20:35:39 PDT 2025; root:xnu-11417.121.6~4/RELEASE_X86_64, machine x86_64
2025-05-02T06:20:10.732Z In(05) vmx DICT --- GLOBAL SETTINGS /Applications/VMware Fusion.app/Contents/Library/settings
2025-05-02T06:20:10.732Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-05-02T06:20:10.732Z In(05) vmx DICT --- USER PREFERENCES /Users/<USER>/Library/Preferences/VMware Fusion/preferences
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.dataCollectionEnabled.epoch = ""
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.dataCollectionEnabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.defaultProfileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.maxProfiles = "4"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileKey = "5202618b-19c9-7a89-903f-4ee6655cb796"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileName = "Windows 10 Profile"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileType = "windows9"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableOSShortcuts = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableKeyMappings = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.selectedLanguage = ""
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.cmdKeyFilterType = "none"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.maxMappings = "30"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.mappingKey = "0"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.fromHost = "GUI Z"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.mappingKey = "1"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.fromHost = "GUI X"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.toGuest = "CONTROL X"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.mappingKey = "2"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.fromHost = "GUI C"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.toGuest = "CONTROL C"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.mappingKey = "3"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.fromHost = "GUI V"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.toGuest = "CONTROL V"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.mappingKey = "4"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.fromHost = "GUI P"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.toGuest = "CONTROL P"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.mappingKey = "5"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.fromHost = "GUI A"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.toGuest = "CONTROL A"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.mappingKey = "6"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.fromHost = "GUI S"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.toGuest = "CONTROL S"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.mappingKey = "7"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.fromHost = "GUI F"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.toGuest = "0x03d"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.mappingKey = "8"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.fromHost = "GUI W"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.mappingKey = "9"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.mappingKey = "10"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.fromHost = "0x11c"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.toGuest = "0x138"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.mappingKey = "11"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.fromHost = "GUI SHIFT H"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.toGuest = "GUI H"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.mappingKey = "12"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.toGuest = "GUI M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.mappingKey = "13"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.fromHost = "GUI SHIFT P"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.toGuest = "GUI P"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.mappingKey = "14"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.fromHost = "GUI SHIFT V"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.toGuest = "GUI V"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.mappingKey = "15"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.fromHost = "GUI SHIFT X"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.toGuest = "GUI X"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.mappingKey = "16"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.fromHost = "GUI SHIFT Z"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.toGuest = "GUI Z"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.mappingKey = "17"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.fromHost = "Mouse1 Control"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.toGuest = "Mouse2"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.mappingKey = "18"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.fromHost = "Mouse1 GUI"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.toGuest = "Mouse3"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.mappingKey = "19"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.fromHost = "GUI CONTROL"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.toGuest = "Ungrab"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.mappingKey = "20"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.fromHost = "GUI CONTROL F"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.toGuest = "Fullscreen"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.mappingKey = "21"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.fromHost = "GUI SHIFT U"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.toGuest = "Unity"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.mappingKey = "22"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.fromHost = "GUI CONTROL S"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.toGuest = "SingleWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.mappingKey = "23"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.fromHost = "GUI 0x029"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.toGuest = "CycleWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.mappingKey = "24"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.toGuest = "CycleWindowReverse"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.mappingKey = "25"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.fromHost = "GUI H"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.toGuest = "HideApplication"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.mappingKey = "26"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.fromHost = "GUI M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.toGuest = "MinimizeWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.mappingKey = "27"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.fromHost = "GUI Q"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.toGuest = "Quit"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.mappingKey = "28"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.toGuest = "ToggleHideMenu"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.mappingKey = "29"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.fromHost = "GUI E"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.toGuest = "Settings"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileKey = "521d2205-554c-06fa-8306-642f387be31a"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileName = "Mac Profile"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileType = "mac"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableOSShortcuts = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableKeyMappings = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.languageSpecificKeyMappingsEnabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.selectedLanguage = ""
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.cmdKeyFilterType = "none"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.maxMappings = "14"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.mappingKey = "0"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.fromHost = "Mouse1 Control"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.toGuest = "Mouse2"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.mappingKey = "1"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.fromHost = "Mouse1 GUI"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.toGuest = "Mouse3"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.mappingKey = "2"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.fromHost = "GUI CONTROL"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.toGuest = "Ungrab"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.mappingKey = "3"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.fromHost = "GUI CONTROL F"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.toGuest = "Fullscreen"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.mappingKey = "4"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.fromHost = "GUI CONTROL U"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.toGuest = "Unity"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.mappingKey = "5"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.fromHost = "GUI SHIFT U"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.toGuest = "Unity"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.mappingKey = "6"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.fromHost = "GUI CONTROL S"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.toGuest = "SingleWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.mappingKey = "7"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.fromHost = "GUI 0x029"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.toGuest = "CycleWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.mappingKey = "8"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.toGuest = "CycleWindowReverse"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.mappingKey = "9"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.fromHost = "GUI H"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.toGuest = "HideApplication"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.mappingKey = "10"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.fromHost = "GUI M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.toGuest = "MinimizeWindow"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.mappingKey = "11"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.fromHost = "GUI Q"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.toGuest = "Quit"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.mappingKey = "12"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.enabled = "TRUE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.toGuest = "ToggleHideMenu"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.mappingKey = "13"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.enabled = "FALSE"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.fromHost = "GUI E"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.toGuest = "Settings"
2025-05-02T06:20:10.732Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileKey = "52312ba5-d8c9-0a91-aaed-ebeb665caf10"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileName = "Windows 8 Profile"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileType = "windows8"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableOSShortcuts = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableKeyMappings = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.selectedLanguage = ""
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.cmdKeyFilterType = "none"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.maxMappings = "33"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.mappingKey = "0"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.fromHost = "GUI Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.mappingKey = "1"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.fromHost = "GUI X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.toGuest = "CONTROL X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.mappingKey = "2"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.fromHost = "GUI C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.toGuest = "CONTROL C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.mappingKey = "3"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.fromHost = "GUI V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.toGuest = "CONTROL V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.mappingKey = "4"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.fromHost = "GUI P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.toGuest = "CONTROL P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.mappingKey = "5"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.fromHost = "GUI A"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.toGuest = "CONTROL A"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.mappingKey = "6"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.fromHost = "GUI S"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.toGuest = "CONTROL S"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.mappingKey = "7"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.fromHost = "GUI F"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.toGuest = "0x03d"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.mappingKey = "8"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.fromHost = "GUI W"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.mappingKey = "9"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.mappingKey = "10"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.enabled = "FALSE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.fromHost = "0x11c"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.toGuest = "0x138"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.mappingKey = "11"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.fromHost = "GUI SHIFT C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.toGuest = "GUI C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.mappingKey = "12"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.fromHost = "GUI SHIFT H"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.toGuest = "GUI H"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.mappingKey = "13"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.fromHost = "GUI SHIFT M"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.toGuest = "GUI M"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.mappingKey = "14"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.fromHost = "GUI SHIFT P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.toGuest = "GUI P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.mappingKey = "15"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.fromHost = "GUI SHIFT F"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.toGuest = "GUI Q"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.mappingKey = "16"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.fromHost = "GUI SHIFT V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.toGuest = "GUI V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.mappingKey = "17"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.fromHost = "GUI SHIFT W"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.toGuest = "GUI W"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.mappingKey = "18"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.fromHost = "GUI SHIFT X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.toGuest = "GUI X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.mappingKey = "19"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.fromHost = "GUI SHIFT Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.toGuest = "GUI Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.mappingKey = "20"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.fromHost = "Mouse1 Control"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.toGuest = "Mouse2"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.mappingKey = "21"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.fromHost = "Mouse1 GUI"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.toGuest = "Mouse3"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.mappingKey = "22"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.fromHost = "GUI CONTROL"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.toGuest = "Ungrab"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.mappingKey = "23"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.fromHost = "GUI CONTROL F"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.toGuest = "Fullscreen"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.mappingKey = "24"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.fromHost = "GUI SHIFT U"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.toGuest = "Unity"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.mappingKey = "25"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.fromHost = "GUI CONTROL S"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.toGuest = "SingleWindow"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.mappingKey = "26"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.fromHost = "GUI 0x029"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.toGuest = "CycleWindow"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.mappingKey = "27"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.toGuest = "CycleWindowReverse"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.mappingKey = "28"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.fromHost = "GUI H"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.toGuest = "HideApplication"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.mappingKey = "29"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.fromHost = "GUI M"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.toGuest = "MinimizeWindow"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.mappingKey = "30"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.fromHost = "GUI Q"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.toGuest = "Quit"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.mappingKey = "31"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.toGuest = "ToggleHideMenu"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.mappingKey = "32"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.enabled = "FALSE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.fromHost = "GUI E"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.toGuest = "Settings"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileName = "Profile"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileType = "standard"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableOSShortcuts = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableKeyMappings = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.selectedLanguage = ""
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.cmdKeyFilterType = "none"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.maxMappings = "25"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.mappingKey = "0"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.fromHost = "GUI Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.mappingKey = "1"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.fromHost = "GUI X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.toGuest = "CONTROL X"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.mappingKey = "2"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.fromHost = "GUI C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.toGuest = "CONTROL C"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.mappingKey = "3"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.fromHost = "GUI V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.toGuest = "CONTROL V"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.mappingKey = "4"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.fromHost = "GUI P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.toGuest = "CONTROL P"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.mappingKey = "5"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.fromHost = "GUI A"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.toGuest = "CONTROL A"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.mappingKey = "6"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.fromHost = "GUI S"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.toGuest = "CONTROL S"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.mappingKey = "7"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.fromHost = "GUI F"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.toGuest = "0x03d"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.mappingKey = "8"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.fromHost = "GUI W"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.mappingKey = "9"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.mappingKey = "10"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.enabled = "FALSE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.fromHost = "0x11c"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.toGuest = "0x138"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.mappingKey = "11"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.fromHost = "Mouse1 Control"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.toGuest = "Mouse2"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.mappingKey = "12"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.fromHost = "Mouse1 GUI"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.toGuest = "Mouse3"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.mappingKey = "13"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.fromHost = "GUI CONTROL"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.toGuest = "Ungrab"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.mappingKey = "14"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.fromHost = "GUI CONTROL F"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.toGuest = "Fullscreen"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.mappingKey = "15"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.fromHost = "GUI CONTROL U"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.toGuest = "Unity"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.mappingKey = "16"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.enabled = "TRUE"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.fromHost = "GUI SHIFT U"
2025-05-02T06:20:10.733Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.toGuest = "Unity"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.mappingKey = "17"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.fromHost = "GUI CONTROL S"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.toGuest = "SingleWindow"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.mappingKey = "18"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.fromHost = "GUI 0x029"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.toGuest = "CycleWindow"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.mappingKey = "19"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.toGuest = "CycleWindowReverse"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.mappingKey = "20"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.fromHost = "GUI H"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.toGuest = "HideApplication"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.mappingKey = "21"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.fromHost = "GUI M"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.toGuest = "MinimizeWindow"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.mappingKey = "22"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.fromHost = "GUI Q"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.toGuest = "Quit"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.mappingKey = "23"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.enabled = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.fromHost = "GUI SHIFT M"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.toGuest = "ToggleHideMenu"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.mappingKey = "24"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.enabled = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.fromHost = "GUI E"
2025-05-02T06:20:10.734Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.toGuest = "Settings"
2025-05-02T06:20:10.734Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1746162507"
2025-05-02T06:20:10.734Z In(05) vmx DICT         vmWizard.guestKey = "ubuntu-64"
2025-05-02T06:20:10.734Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT  hint.confirmLaxOVFImport = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT    hint.dui.poweroff.soft = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT hint.loader.mitigations.wsAndFusion = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT         hint.vmui.restart = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT --- USER DEFAULTS /Users/<USER>/Library/Preferences/VMware Fusion/config
2025-05-02T06:20:10.734Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config
2025-05-02T06:20:10.734Z In(05) vmx DICT --- SITE DEFAULTS /Applications/VMware Fusion.app/Contents/Library/config
2025-05-02T06:20:10.734Z In(05) vmx DICT --- NONPERSISTENT
2025-05-02T06:20:10.734Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT --- COMMAND LINE
2025-05-02T06:20:10.734Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT --- RECORDING
2025-05-02T06:20:10.734Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT --- CONFIGURATION /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx 
2025-05-02T06:20:10.734Z In(05) vmx DICT            config.version = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT         virtualHW.version = "21"
2025-05-02T06:20:10.734Z In(05) vmx DICT              mks.enable3d = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-05-02T06:20:10.734Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-05-02T06:20:10.734Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-05-02T06:20:10.734Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-05-02T06:20:10.734Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT                     nvram = "Ubuntu 64-bit 22.04.3 2.nvram"
2025-05-02T06:20:10.734Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-05-02T06:20:10.734Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-05-02T06:20:10.734Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-05-02T06:20:10.734Z In(05) vmx DICT         powerType.suspend = "soft"
2025-05-02T06:20:10.734Z In(05) vmx DICT           powerType.reset = "soft"
2025-05-02T06:20:10.734Z In(05) vmx DICT               displayName = "Ubuntu 64-bit 22.04.3"
2025-05-02T06:20:10.734Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT                   guestOS = "ubuntu-64"
2025-05-02T06:20:10.734Z In(05) vmx DICT            tools.syncTime = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT      tools.upgrade.policy = "upgradeAtPowerCycle"
2025-05-02T06:20:10.734Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT            sound.fileName = "-1"
2025-05-02T06:20:10.734Z In(05) vmx DICT             sound.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT                  numvcpus = "4"
2025-05-02T06:20:10.734Z In(05) vmx DICT      cpuid.coresPerSocket = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT               vcpu.hotadd = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT                   memsize = "4096"
2025-05-02T06:20:10.734Z In(05) vmx DICT                mem.hotadd = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-05-02T06:20:10.734Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT             sata0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT          scsi0:0.fileName = "Virtual Disk.vmdk"
2025-05-02T06:20:10.734Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        sata0:1.deviceType = "cdrom-image"
2025-05-02T06:20:10.734Z In(05) vmx DICT          sata0:1.fileName = "/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso"
2025-05-02T06:20:10.734Z In(05) vmx DICT           sata0:1.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        sata0:0.deviceType = "cdrom-image"
2025-05-02T06:20:10.734Z In(05) vmx DICT          sata0:0.fileName = "autoinst.iso"
2025-05-02T06:20:10.734Z In(05) vmx DICT           sata0:0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT               usb.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT              ehci.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     svga.graphicsMemoryKB = "8388608"
2025-05-02T06:20:10.734Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2025-05-02T06:20:10.734Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-05-02T06:20:10.734Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2025-05-02T06:20:10.734Z In(05) vmx DICT ethernet0.linkStatePropagation.enable = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT          floppy0.fileType = "file"
2025-05-02T06:20:10.734Z In(05) vmx DICT          floppy0.fileName = "autoinst.flp"
2025-05-02T06:20:10.734Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        extendedConfigFile = "Ubuntu 64-bit 22.04.3 2.vmxf"
2025-05-02T06:20:10.734Z In(05) vmx DICT      floppy0.clientDevice = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT         vmxstats.filename = "Ubuntu 64-bit 22.04.3 2.scoreboard"
2025-05-02T06:20:10.734Z In(05) vmx DICT                 uuid.bios = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
2025-05-02T06:20:10.734Z In(05) vmx DICT             uuid.location = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
2025-05-02T06:20:10.734Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-05-02T06:20:10.734Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-05-02T06:20:10.734Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-05-02T06:20:10.734Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-05-02T06:20:10.734Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-05-02T06:20:10.734Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-05-02T06:20:10.734Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2025-05-02T06:20:10.734Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-05-02T06:20:10.734Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2025-05-02T06:20:10.734Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2025-05-02T06:20:10.734Z In(05) vmx DICT       sata0.pciSlotNumber = "36"
2025-05-02T06:20:10.734Z In(05) vmx DICT              scsi0:0.redo = ""
2025-05-02T06:20:10.734Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-05-02T06:20:10.734Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:20:10.734Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:20:10.734Z In(05) vmx DICT   vmotion.svga.supports3D = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.baseCapsLevel = "9"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.maxPointSize = "189"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.maxTextureSize = "16384"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.maxVolumeExtent = "2048"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.maxTextureAnisotropy = "16"
2025-05-02T06:20:10.734Z In(05) vmx DICT  vmotion.svga.lineStipple = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.dxMaxConstantBuffers = "15"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.dxProvokingVertex = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT         vmotion.svga.sm41 = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.multisample2x = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.multisample4x = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.msFullQuality = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT     vmotion.svga.logicOps = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT         vmotion.svga.bc67 = "9"
2025-05-02T06:20:10.734Z In(05) vmx DICT          vmotion.svga.sm5 = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.multisample8x = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.logicBlendOps = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT vmotion.svga.maxForcedSampleCount = "8"
2025-05-02T06:20:10.734Z In(05) vmx DICT         vmotion.svga.gl43 = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:99:78:11"
2025-05-02T06:20:10.734Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT                  vmci0.id = "1620670481"
2025-05-02T06:20:10.734Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2025-05-02T06:20:10.734Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT              softPowerOff = "FALSE"
2025-05-02T06:20:10.734Z In(05) vmx DICT               usb:1.speed = "2"
2025-05-02T06:20:10.734Z In(05) vmx DICT             usb:1.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT          usb:1.deviceType = "hub"
2025-05-02T06:20:10.734Z In(05) vmx DICT                usb:1.port = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT              usb:1.parent = "-1"
2025-05-02T06:20:10.734Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT   guestInfo.detailed.data = <not printed>
2025-05-02T06:20:10.734Z In(05) vmx DICT   keyboardAndMouseProfile = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:20:10.734Z In(05) vmx DICT gui.fitGuestUsingNativeDisplayResolution = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT    chipset.useAcpiBattery = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT     chipset.useApmBattery = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT toolsInstallManager.updateCounter = "1"
2025-05-02T06:20:10.734Z In(05) vmx DICT tools.capability.verifiedSamlToken = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT        checkpoint.vmState = "Ubuntu 64-bit 22.04.3 2-ecf6dafd.vmss"
2025-05-02T06:20:10.734Z In(05) vmx DICT             usb:0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT          usb:0.deviceType = "hid"
2025-05-02T06:20:10.734Z In(05) vmx DICT                usb:0.port = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT              usb:0.parent = "-1"
2025-05-02T06:20:10.734Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2025-05-02T06:20:10.734Z In(05) vmx DICT     gui.viewModeAtPowerOn = "fullscreen"
2025-05-02T06:20:10.734Z In(05) vmx DICT            ehci:0.present = "TRUE"
2025-05-02T06:20:10.734Z In(05) vmx DICT         ehci:0.deviceType = "video"
2025-05-02T06:20:10.734Z In(05) vmx DICT               ehci:0.port = "0"
2025-05-02T06:20:10.734Z In(05) vmx DICT             ehci:0.parent = "-1"
2025-05-02T06:20:10.734Z In(05) vmx DICT --- USER DEFAULTS ~/Library/Preferences/VMware Fusion/config 
2025-05-02T06:20:10.734Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config 
2025-05-02T06:20:10.734Z In(05) vmx DICT --- SITE DEFAULTS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config 
2025-05-02T06:20:10.734Z In(05) vmx DICT --- GLOBAL SETTINGS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings 
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:20:10.735Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:20:10.735Z In(05) vmx Powering on guestOS 'ubuntu-64' using the configuration for 'ubuntu-64'.
2025-05-02T06:20:10.735Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:20:10.735Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:20:10.736Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:20:10.736Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:20:10.737Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-05-02T06:20:10.737Z In(05) vmx Checkpointed in VMware Fusion, 13.5.2, build-23775688, Mac OS Host
2025-05-02T06:20:10.737Z In(05) vmx Resuming virtual machine from /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2-ecf6dafd.vmss with 4096 MB of memory.
2025-05-02T06:20:10.737Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:20:10.737Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:20:10.738Z In(05) vmx Monitor Mode: ULM
2025-05-02T06:20:10.738Z In(05) vmx MsgHint: msg.loader.mitigations.wsAndFusion
2025-05-02T06:20:10.738Z In(05)+ vmx You are running this virtual machine with side channel mitigations enabled. Side channel mitigations provide enhanced security but also lower performance.
2025-05-02T06:20:10.738Z In(05)+ vmx 
2025-05-02T06:20:10.738Z In(05)+ vmx To disable mitigations, change the side channel mitigations setting in the advanced panel of the virtual machine settings. Refer to VMware KB article 79832 at https://kb.vmware.com/s/article/79832 for more details.
2025-05-02T06:20:10.738Z In(05)+ vmx ---------------------------------------
2025-05-02T06:20:10.751Z In(05) vmx OvhdMem_PowerOn: initial admission: paged  4142968 nonpaged     4343 anonymous     9460
2025-05-02T06:20:10.751Z In(05) vmx VMMEM: Initial Reservation: 16237MB (MainMem=4096MB)
2025-05-02T06:20:10.751Z In(05) vmx numa: Hot-add is enabled and vNUMA hot-add is disabled, forcing UMA.
2025-05-02T06:20:10.751Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-05-02T06:20:10.751Z In(05) vmx llc: vLLC size: 1
2025-05-02T06:20:10.752Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 209715 (80% of min required size 262144)
2025-05-02T06:20:10.753Z In(05) PowerNotifyThread VTHREAD 123145314906112 "PowerNotifyThread" tid 178800
2025-05-02T06:20:10.753Z In(05) PowerNotifyThread PowerNotify thread is alive.
2025-05-02T06:20:10.753Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2025-05-02T06:20:10.753Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2025-05-02T06:20:10.753Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2025-05-02T06:20:10.753Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2025-05-02T06:20:10.753Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2025-05-02T06:20:10.753Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Fusion)
2025-05-02T06:20:10.754Z In(05) vthread-178801 VTHREAD 123145315442688 "vthread-178801" tid 178801
2025-05-02T06:20:10.754Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:20:10.754Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:20:10.754Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2025-05-02T06:20:10.755Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu-64' (refreshCount=1, lastCount=1).
2025-05-02T06:20:10.755Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:20:10.755Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:20:10.755Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:20:10.755Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:20:10.755Z In(05) deviceThread VTHREAD 123145315979264 "deviceThread" tid 178802
2025-05-02T06:20:10.756Z In(05) deviceThread Device thread is alive
2025-05-02T06:20:10.756Z In(05) vmx Host VT-x Capabilities:
2025-05-02T06:20:10.756Z In(05) vmx Basic VMX Information (0x00da040000000004)
2025-05-02T06:20:10.756Z In(05) vmx   VMCS revision ID                           4
2025-05-02T06:20:10.756Z In(05) vmx   VMCS region length                      1024
2025-05-02T06:20:10.756Z In(05) vmx   VMX physical-address width           natural
2025-05-02T06:20:10.756Z In(05) vmx   SMM dual-monitor mode                    yes
2025-05-02T06:20:10.756Z In(05) vmx   VMCS memory type                          WB
2025-05-02T06:20:10.756Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-05-02T06:20:10.756Z In(05) vmx   True VMX MSRs                            yes
2025-05-02T06:20:10.756Z In(05) vmx   Exception Injection ignores error code    no
2025-05-02T06:20:10.756Z In(05) vmx True Pin-Based VM-Execution Controls (0x0000007f00000016)
2025-05-02T06:20:10.756Z In(05) vmx   External-interrupt exiting               {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   NMI exiting                              {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Virtual NMIs                             {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Process posted interrupts                { 0 }
2025-05-02T06:20:10.756Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2025-05-02T06:20:10.756Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   HLT exiting                              {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   INVLPG exiting                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   MWAIT exiting                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   RDPMC exiting                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   RDTSC exiting                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   CR3-load exiting                         {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   CR3-store exiting                        {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Activate tertiary controls               { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   CR8-load exiting                         {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   CR8-store exiting                        {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Use TPR shadow                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   NMI-window exiting                       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Monitor trap flag                        {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   MONITOR exiting                          {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   PAUSE exiting                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Activate secondary controls              {0,1}
2025-05-02T06:20:10.756Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x00515cef000000a2)
2025-05-02T06:20:10.756Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Enable EPT                               { 1 }
2025-05-02T06:20:10.756Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Virtualize x2APIC mode                   { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Enable VPID                              { 1 }
2025-05-02T06:20:10.756Z In(05) vmx   WBINVD exiting                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Unrestricted guest                       { 1 }
2025-05-02T06:20:10.756Z In(05) vmx   APIC-register virtualization             { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Virtual-interrupt delivery               { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   RDRAND exiting                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Enable INVPCID                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Enable VM Functions                      { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Use VMCS shadowing                       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   ENCLS exiting                            { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   RDSEED exiting                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Enable PML                               { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   EPT-violation #VE                        { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Conceal VMX from PT                      { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   PASID translation                        { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   PT uses guest physical addresses         { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Use TSC scaling                          { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Bus lock exiting                         { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Notification VM exits                    { 0 }
2025-05-02T06:20:10.756Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2025-05-02T06:20:10.756Z In(05) vmx   LOADIWKEY exiting                          no
2025-05-02T06:20:10.756Z In(05) vmx   Enable HLAT                                no
2025-05-02T06:20:10.756Z In(05) vmx   Enable Paging-Write                        no
2025-05-02T06:20:10.756Z In(05) vmx   Enable Guest Paging Verification           no
2025-05-02T06:20:10.756Z In(05) vmx   Enable IPI Virtualization                  no
2025-05-02T06:20:10.756Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-05-02T06:20:10.756Z In(05) vmx True VM-Exit Controls (0x01ffffff00036dfb)
2025-05-02T06:20:10.756Z In(05) vmx   Save debug controls                      {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Host address-space size                  {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Save VMX-preemption timer                {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Clear IA32_BNDCFGS                       {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Clear IA32_RTIT MSR                      { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:20:10.756Z In(05) vmx True VM-Entry Controls (0x0003ffff000011fb)
2025-05-02T06:20:10.756Z In(05) vmx   Load debug controls                      {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Entry to SMM                             {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_BNDCFGS                        {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_RTIT MSR                       { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2025-05-02T06:20:10.756Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:20:10.756Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2025-05-02T06:20:10.756Z In(05) vmx   R=0/W=0/X=1                               yes
2025-05-02T06:20:10.756Z In(05) vmx   Page-walk length 3                        yes
2025-05-02T06:20:10.756Z In(05) vmx   EPT memory type WB                        yes
2025-05-02T06:20:10.756Z In(05) vmx   2MB super-page                            yes
2025-05-02T06:20:10.756Z In(05) vmx   1GB super-page                            yes
2025-05-02T06:20:10.756Z In(05) vmx   INVEPT support                            yes
2025-05-02T06:20:10.756Z In(05) vmx   Access & Dirty Bits                       yes
2025-05-02T06:20:10.756Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2025-05-02T06:20:10.756Z In(05) vmx   Supervisor shadow-stack control            no
2025-05-02T06:20:10.756Z In(05) vmx   Type 1 INVEPT                             yes
2025-05-02T06:20:10.756Z In(05) vmx   Type 2 INVEPT                             yes
2025-05-02T06:20:10.756Z In(05) vmx   INVVPID support                           yes
2025-05-02T06:20:10.756Z In(05) vmx   Type 0 INVVPID                            yes
2025-05-02T06:20:10.756Z In(05) vmx   Type 1 INVVPID                            yes
2025-05-02T06:20:10.756Z In(05) vmx   Type 2 INVVPID                            yes
2025-05-02T06:20:10.756Z In(05) vmx   Type 3 INVVPID                            yes
2025-05-02T06:20:10.756Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2025-05-02T06:20:10.756Z In(05) vmx   TSC to preemption timer ratio      7
2025-05-02T06:20:10.756Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-05-02T06:20:10.756Z In(05) vmx   Activity State HLT               yes
2025-05-02T06:20:10.756Z In(05) vmx   Activity State shutdown          yes
2025-05-02T06:20:10.756Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-05-02T06:20:10.756Z In(05) vmx   Processor trace in VMX           yes
2025-05-02T06:20:10.756Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2025-05-02T06:20:10.756Z In(05) vmx   CR3 targets supported              4
2025-05-02T06:20:10.756Z In(05) vmx   Maximum MSR list size            512
2025-05-02T06:20:10.756Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2025-05-02T06:20:10.756Z In(05) vmx   Allow all VMWRITEs               yes
2025-05-02T06:20:10.756Z In(05) vmx   Allow zero instruction length    yes
2025-05-02T06:20:10.756Z In(05) vmx   MSEG revision ID                   0
2025-05-02T06:20:10.756Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-05-02T06:20:10.756Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-05-02T06:20:10.756Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-05-02T06:20:10.756Z In(05) vmx   Variable          0x000000007fffffde
2025-05-02T06:20:10.756Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x00000000003767ff)
2025-05-02T06:20:10.756Z In(05) vmx   Fixed to 0        0xffffffffffc89800
2025-05-02T06:20:10.756Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-05-02T06:20:10.756Z In(05) vmx   Variable          0x00000000003747ff
2025-05-02T06:20:10.756Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2025-05-02T06:20:10.756Z In(05) vmx   Highest index                   0x17
2025-05-02T06:20:10.756Z In(05) vmx VM Functions (0x0000000000000000)
2025-05-02T06:20:10.756Z In(05) vmx KHZEstimate 2592000
2025-05-02T06:20:10.756Z In(05) vmx MHZEstimate 2592
2025-05-02T06:20:10.756Z In(05) vmx NumVCPUs 4
2025-05-02T06:20:10.757Z In(05) vmx AIOGNRC: numThreads=17 ide=0, scsi=1, passthru=0
2025-05-02T06:20:10.757Z In(05) vmx WORKER: Creating new group with maxThreads=17 (17)
2025-05-02T06:20:10.759Z In(05) vmx WORKER: Creating new group with maxThreads=1 (18)
2025-05-02T06:20:10.759Z In(05) vmx MainMem: CPT Host WZ=0 PF=4096 D=0
2025-05-02T06:20:10.759Z In(05) vmx MainMem: CPT PLS=1 PLR=0 BS=1 BlkP=32 Mult=4 W=50
2025-05-02T06:20:10.759Z In(05) vmx MStat: Creating Stat vm.uptime
2025-05-02T06:20:10.759Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-05-02T06:20:10.759Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-05-02T06:20:10.759Z In(05) vmx VMXAIOMGR: Using: simple=Generic
2025-05-02T06:20:10.761Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2025-05-02T06:20:10.761Z In(05) machPoll VTHREAD 123145316515840 "machPoll" tid 178803
2025-05-02T06:20:10.764Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2025-05-02T06:20:10.764Z In(05) vmx WORKER: Creating new group with maxThreads=14 (34)
2025-05-02T06:20:10.765Z In(05) vmx FeatureCompat: No VM masks.
2025-05-02T06:20:10.765Z In(05) vmx TimeTracker host to guest rate conversion 271981954 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:20:10.765Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -271981954
2025-05-02T06:20:10.765Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-02T06:20:10.765Z In(05) vmx TSC offsetting enabled.
2025-05-02T06:20:10.765Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-02T06:20:10.765Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-02T06:20:10.765Z In(05) vmx MKS PowerOn
2025-05-02T06:20:10.766Z In(05) mks VTHREAD 123145318625280 "mks" tid 178804
2025-05-02T06:20:10.767Z In(05) mks MKS thread is alive
2025-05-02T06:20:10.767Z In(05) svga VTHREAD 123145327026176 "svga" tid 178805
2025-05-02T06:20:10.767Z In(05) svga SVGA thread is alive
2025-05-02T06:20:10.767Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-05-02T06:20:10.799Z In(05) mks MKS MacOSMouse: Using event tap path.
2025-05-02T06:20:10.799Z In(05) mouse VTHREAD 123145327562752 "mouse" tid 178806
2025-05-02T06:20:10.800Z In(05) keyboard VTHREAD 123145328099328 "keyboard" tid 178807
2025-05-02T06:20:10.875Z In(05) keyboard MKS MacOSKeyboard: Adding device: (0) VID:05AC PID:027C Apple Inc. (Apple Internal Keyboard / Trackpad)
2025-05-02T06:20:10.880Z In(05) keyboard MKS MacOSKeyboard: Adding device: (1) VID:05AC PID:8600 <missing> (TouchBarUserDevice)
2025-05-02T06:20:10.880Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps MTLRenderer ISBRenderer 
2025-05-02T06:20:10.880Z In(05) mks MKS-RenderMain: ISB enabled by config
2025-05-02T06:20:10.880Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from ISBRenderer
2025-05-02T06:20:10.880Z In(05) mks MKS-RenderMain: Starting ISBRenderer
2025-05-02T06:20:10.880Z In(05) mks ISBRendererComm: ISBRendererComm DataChannel size=4294967296
2025-05-02T06:20:10.881Z In(05) mks LocalMKS: MKS_UUID=52 97 1a 8e 70 37 c4 ff-f4 c9 4d ce 76 8d f2 5f
2025-05-02T06:20:10.883Z In(05) mks LogRotation: Rotating to a new log file (keepOld 3) took 0.002145 seconds.
2025-05-02T06:20:10.883Z In(05) mks ISBRendererComm: mksSandbox command-line: /Applications/VMware Fusion.app/Contents/Library/mksSandbox --pipeInfo 3
2025-05-02T06:20:10.892Z In(05) mks ISBRendererComm: Spawned process with pid 21174
2025-05-02T06:20:11.703Z In(05) mks ISBRendererComm: Sandbox Renderer: MTLRenderer
2025-05-02T06:20:11.707Z In(05) mks MKS-RenderMain: Started ISBRenderer with (MTLRenderer)
2025-05-02T06:20:11.707Z In(05) mks MKS-RenderMain: Found Full Renderer: ISBRenderer (MTLRenderer)
2025-05-02T06:20:11.707Z In(05) mks MKS-RenderMain: maxTextureSize=16384
2025-05-02T06:20:11.708Z In(05) mks SOCKET 2 (71) creating new listening socket on port -1
2025-05-02T06:20:11.708Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-05-02T06:20:11.708Z In(05) mks MKSRemoteMgr: Set default display name: Ubuntu 64-bit 22.04.3
2025-05-02T06:20:11.708Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-05-02T06:20:11.708Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-05-02T06:20:11.708Z No(00) vmx PowerOnTiming: Module MKS took 943174 us
2025-05-02T06:20:11.708Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-05-02T06:20:11.709Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-05-02T06:20:11.709Z In(05) vmx Chipset version: 0x17
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2025-05-02T06:20:11.924Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2025-05-02T06:20:11.926Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2025-05-02T06:20:11.926Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2025-05-02T06:20:11.933Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-05-02T06:20:11.933Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-05-02T06:20:11.934Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2025-05-02T06:20:11.934Z In(05) vmx DISK: OPEN scsi0:0 '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' persistent R[]
2025-05-02T06:20:11.935Z In(05) vmx Current OS Release is 24.5.0
2025-05-02T06:20:11.937Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2025-05-02T06:20:11.937Z In(05) vmx DISK: Disk '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' has UUID '60 00 c2 90 cf 53 1e 92-6a 2b b7 7a 00 db 66 0e'
2025-05-02T06:20:11.937Z In(05) vmx DISK: OPEN '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' Geo (6527/255/63) BIOS Geo (0/0/0)
2025-05-02T06:20:11.938Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'scsi0:0' identified as Virtual SSD device.
2025-05-02T06:20:11.938Z In(05) vmx DISK: Opening disks took 4 ms.
2025-05-02T06:20:11.938Z In(05) vmx USBArbLib: USBArbLib initialized successfully, retryIntervalStart(5), retryIntervalMax(120), arbSocketName(/var/run/vmware/usbarb-socket), useLocking(yes), tryUpgrading(no).
2025-05-02T06:20:11.938Z In(05) vmx UsbEnum: Initializing UsbEnum library, disableLocking(no), allowBootableHid(yes).
2025-05-02T06:20:11.938Z In(05) vmx SOCKET creating new socket, connecting to /var/run/vmware/usbarb-socket
2025-05-02T06:20:11.938Z In(05) vmx USB: Initializing 'Virtual Hub' backend
2025-05-02T06:20:11.938Z In(05) vmx USB: Initializing 'Generic' backend
2025-05-02T06:20:11.938Z Wa(03) vmx USBArbLib: OUT SET_AUTO_CONNECT: Not connected to arbitrator, autoconnect(0) for client 'Ubuntu 64-bit 22.04.3', connectState(1).
2025-05-02T06:20:11.938Z In(05) vmx USB: Initializing 'Virtual HID' backend
2025-05-02T06:20:11.939Z In(05) vmx USB: Initializing 'Virtual Mass Storage' backend
2025-05-02T06:20:11.939Z In(05) vmx USB: Initializing 'Virtual RNG' backend
2025-05-02T06:20:11.939Z In(05) vmx USB: Initializing 'Virtual CCID' backend
2025-05-02T06:20:11.939Z In(05) vmx USB-CCID:  dlopened /System/Library/Frameworks/PCSC.framework/PCSC.
2025-05-02T06:20:11.963Z In(05) usbCCIDEnumCards VTHREAD 123145330245632 "usbCCIDEnumCards" tid 178923
2025-05-02T06:20:11.963Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread created.
2025-05-02T06:20:11.963Z In(05) vmx USB: Initializing 'Virtual Bluetooth' backend
2025-05-02T06:20:11.963Z In(05) vmx USB: Initializing 'Virtual Audio' backend
2025-05-02T06:20:11.963Z In(05) vmx USB: Initializing 'Virtual Video' backend
2025-05-02T06:20:11.967Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:20:11.967Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:20:11.967Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:20:11.967Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:20:11.968Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2025-05-02T06:20:11.968Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2025-05-02T06:20:11.968Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2025-05-02T06:20:11.968Z In(05) vmx DISKUTIL: scsi0:0 : capacity=104857600 logical sector size=512
2025-05-02T06:20:11.968Z In(05) vmx DISKUTIL: scsi0:0 : geometry=6527/255/63
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 4194304
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=4194304 bytes, max wh(1176, 885)
2025-05-02T06:20:11.969Z In(05) vmx SVGA-GFB: Max wh(1176, 885), number of displays: 10
2025-05-02T06:20:11.969Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2025-05-02T06:20:11.969Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:20:11.969Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:20:11.969Z In(05) vmx SVGA: mobMaxSize=1073741824
2025-05-02T06:20:11.969Z In(05) vmx SVGA: graphicsMemoryKB=8388608
2025-05-02T06:20:11.969Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 511
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 16384
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 9
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 9
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 189
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 16384
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 9
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host id: 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.supports3D bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.baseCapsLevel num 9
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.maxPointSize num 189
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.maxTextureSize num 16384
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.maxVolumeExtent num 2048
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.maxTextureAnisotropy num 16
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.lineStipple bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.dxProvokingVertex bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.sm41 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.multisample2x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.multisample4x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.msFullQuality bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.logicOps bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.bc67 num 9
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.sm5 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.multisample8x bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.logicBlendOps bool 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.maxForcedSampleCount num 8
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature    host svga.gl43 bool 1
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature evcHost id: 0
2025-05-02T06:20:11.969Z In(05) vmx SVGAFeature evcHost svga.supports3D bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.baseCapsLevel num 9
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.maxPointSize num 189
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.maxTextureSize num 16384
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.maxVolumeExtent num 2048
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.maxTextureAnisotropy num 16
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.lineStipple bool 0
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.dxProvokingVertex bool 0
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.sm41 bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.multisample2x bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.multisample4x bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.msFullQuality bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.logicOps bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.bc67 num 9
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.sm5 bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.multisample8x bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.logicBlendOps bool 0
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.maxForcedSampleCount num 8
2025-05-02T06:20:11.970Z In(05) vmx SVGAFeature evcHost svga.gl43 bool 1
2025-05-02T06:20:11.970Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:20:11.970Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:20:11.970Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:20:11.970Z In(05) vmx SVGA3dCaps: host, at power on (3d enabled)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:20:11.970Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:20:11.970Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:20:11.970Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:20:11.970Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:20:11.970Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:20:11.970Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:20:11.970Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:20:11.970Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:20:11.970Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:20:11.970Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:20:11.971Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:20:11.971Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:20:11.971Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:20:11.971Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:20:11.971Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:20:11.971Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:20:11.971Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:20:11.971Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:20:11.971Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:20:11.971Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:20:11.971Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:20:11.971Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:20:11.971Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:20:11.971Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:20:11.971Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:20:11.971Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:20:11.971Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:20:11.971Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:20:11.971Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:20:11.971Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:20:11.971Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:20:11.971Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:20:11.971Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:20:11.971Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:20:11.971Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:20:11.971Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:20:11.971Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:20:11.971Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:20:11.971Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:20:11.971Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:20:11.971Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:20:11.971Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:20:11.971Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config id: 0
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.supports3D bool 1
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.baseCapsLevel num 9
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.maxPointSize num 189
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.maxTextureSize num 16384
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.maxVolumeExtent num 2048
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.maxTextureAnisotropy num 16
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.lineStipple bool 0
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.dxProvokingVertex bool 0
2025-05-02T06:20:11.971Z In(05) vmx SVGAFeature config svga.sm41 bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.multisample2x bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.multisample4x bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.msFullQuality bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.logicOps bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.bc67 num 9
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.sm5 bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.multisample8x bool 1
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.logicBlendOps bool 0
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.maxForcedSampleCount num 8
2025-05-02T06:20:11.972Z In(05) vmx SVGAFeature config svga.gl43 bool 1
2025-05-02T06:20:11.973Z In(05) vmx USB: Initializing 'UHCI' host controller.
2025-05-02T06:20:11.974Z In(05) vmx USB: PowerOnCreateDevice 'usb:0' #0, found port 7FF48C064690.
2025-05-02T06:20:11.974Z No(00) vmx ConfigDB: Setting usb:0.present = "TRUE"
2025-05-02T06:20:11.974Z No(00) vmx ConfigDB: Setting usb:0.deviceType = "hid"
2025-05-02T06:20:11.974Z No(00) vmx ConfigDB: Setting usb:0.port = "0"
2025-05-02T06:20:11.974Z No(00) vmx ConfigDB: Setting usb:0.parent = "-1"
2025-05-02T06:20:11.980Z In(05) vmx USB: PowerOnCreateDevice 'usb:1' #1, found port 7FF48C0646C8.
2025-05-02T06:20:11.980Z No(00) vmx ConfigDB: Setting usb:1.speed = "2"
2025-05-02T06:20:11.981Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:99:78:11
2025-05-02T06:20:11.982Z In(05) vmx USB: Initializing 'EHCI' host controller.
2025-05-02T06:20:11.982Z In(05) vmx USB: PowerOnCreateDevice 'ehci:0' #0, found port 7FF48C064F90.
2025-05-02T06:20:11.982Z No(00) vmx ConfigDB: Setting ehci:0.present = "TRUE"
2025-05-02T06:20:11.982Z No(00) vmx ConfigDB: Setting ehci:0.deviceType = "video"
2025-05-02T06:20:11.982Z No(00) vmx ConfigDB: Setting ehci:0.port = "0"
2025-05-02T06:20:11.982Z No(00) vmx ConfigDB: Setting ehci:0.parent = "-1"
2025-05-02T06:20:11.988Z No(00) vmx ConfigDB: Setting vmci0.id = "1620670481"
2025-05-02T06:20:11.994Z In(05) vmx AHCI:Successfully created adapter 'sata0' as num 0
2025-05-02T06:20:11.994Z In(05) vmx SCSI DEVICE (sata0:0): Computed value of sata0:0.useBounceBuffers: default
2025-05-02T06:20:11.994Z In(05) vmx DISKUTIL: sata0:0 : capacity=0 logical sector size=2048
2025-05-02T06:20:11.994Z In(05) vmx DISKUTIL: sata0:0 : geometry=0/0/0
2025-05-02T06:20:11.994Z In(05) vmx AHCI:Creating ATAPI CDROM on SATA adapter.
2025-05-02T06:20:11.994Z In(05) vmx AHCI:Successfully created device: sata0:0
2025-05-02T06:20:11.994Z In(05) vmx SCSI DEVICE (sata0:1): Computed value of sata0:1.useBounceBuffers: default
2025-05-02T06:20:11.995Z In(05) vmx DISKUTIL: sata0:1 : capacity=0 logical sector size=2048
2025-05-02T06:20:11.995Z In(05) vmx DISKUTIL: sata0:1 : geometry=0/0/0
2025-05-02T06:20:11.995Z In(05) vmx AHCI:Creating ATAPI CDROM on SATA adapter.
2025-05-02T06:20:11.995Z In(05) vmx AHCI:Successfully created device: sata0:1
2025-05-02T06:20:11.997Z In(05) vmx WORKER: Creating new group with maxThreads=1 (35)
2025-05-02T06:20:11.997Z In(05) vmx DISKUTIL: scsi0:0 : max toolsVersion = 12389, type = 4
2025-05-02T06:20:11.997Z In(05) vmx TOOLS setting legacy tools version to '12389' type 4, manifest status is 9
2025-05-02T06:20:11.997Z In(05) worker-178801 ToolsVersionGetStatusWorkerThread: Tools status 8 derived from environment
2025-05-02T06:20:11.997Z In(05) vmx Tools: sending 'OS_Resume' (state = 4) state change request
2025-05-02T06:20:11.997Z In(05) vmx Tools: Delaying state change request to state 4.
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2025-05-02T06:20:11.997Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2025-05-02T06:20:11.998Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2025-05-02T06:20:11.998Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2025-05-02T06:20:11.998Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2025-05-02T06:20:11.998Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2025-05-02T06:20:11.998Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2025-05-02T06:20:11.999Z In(05) vmx CPT: Restoring checkpoint /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2-ecf6dafd.vmss
2025-05-02T06:20:12.000Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-05-02T06:20:12.000Z In(05) vmx PStrIntern expansion: nBkts=256
2025-05-02T06:20:12.000Z In(05) vmx Progress -1% (msg.checkpoint.restoreStatus)
2025-05-02T06:20:12.001Z In(05) vmx   restoring GuestVars
2025-05-02T06:20:12.016Z In(05) vmx   restoring A20
2025-05-02T06:20:12.016Z In(05) vmx   restoring UUIDVMX
2025-05-02T06:20:12.016Z In(05) vmx   restoring memory
2025-05-02T06:20:12.016Z In(05) vmx DUMPER: Item 'hotSetSize' [-1, -1] not found.
2025-05-02T06:20:12.016Z In(05) vmx MainMem: Opened paging file, 'UNNAMED' (swap).
2025-05-02T06:20:12.016Z In(05) vmx MainMem: Read full memory image (SF=0 L=0 SZ=1 RM=0 U=0 TM=0).
2025-05-02T06:20:12.019Z In(05) vmx Progress 0% (none)
2025-05-02T06:20:12.040Z In(05) vmx Progress 1% (none)
2025-05-02T06:20:12.062Z In(05) vmx Progress 2% (none)
2025-05-02T06:20:12.084Z In(05) vmx Progress 3% (none)
2025-05-02T06:20:12.110Z In(05) vmx Progress 4% (none)
2025-05-02T06:20:12.136Z In(05) vmx Progress 5% (none)
2025-05-02T06:20:12.161Z In(05) vmx Progress 6% (none)
2025-05-02T06:20:12.186Z In(05) vmx Progress 7% (none)
2025-05-02T06:20:12.215Z In(05) vmx Progress 8% (none)
2025-05-02T06:20:12.241Z In(05) vmx Progress 9% (none)
2025-05-02T06:20:12.268Z In(05) vmx Progress 10% (none)
2025-05-02T06:20:12.293Z In(05) vmx Progress 11% (none)
2025-05-02T06:20:12.327Z In(05) vmx Progress 12% (none)
2025-05-02T06:20:12.354Z In(05) vmx Progress 13% (none)
2025-05-02T06:20:12.379Z In(05) vmx Progress 14% (none)
2025-05-02T06:20:12.405Z In(05) vmx Progress 15% (none)
2025-05-02T06:20:12.434Z In(05) vmx Progress 16% (none)
2025-05-02T06:20:12.463Z In(05) vmx Progress 17% (none)
2025-05-02T06:20:12.489Z In(05) vmx Progress 18% (none)
2025-05-02T06:20:12.518Z In(05) vmx Progress 19% (none)
2025-05-02T06:20:12.544Z In(05) vmx Progress 20% (none)
2025-05-02T06:20:12.571Z In(05) vmx Progress 21% (none)
2025-05-02T06:20:12.598Z In(05) vmx Progress 22% (none)
2025-05-02T06:20:12.628Z In(05) vmx Progress 23% (none)
2025-05-02T06:20:12.654Z In(05) vmx Progress 24% (none)
2025-05-02T06:20:12.679Z In(05) vmx Progress 25% (none)
2025-05-02T06:20:12.706Z In(05) vmx Progress 26% (none)
2025-05-02T06:20:12.734Z In(05) vmx Progress 27% (none)
2025-05-02T06:20:12.760Z In(05) vmx Progress 28% (none)
2025-05-02T06:20:12.786Z In(05) vmx Progress 29% (none)
2025-05-02T06:20:12.812Z In(05) vmx Progress 30% (none)
2025-05-02T06:20:12.840Z In(05) vmx Progress 31% (none)
2025-05-02T06:20:12.865Z In(05) vmx Progress 32% (none)
2025-05-02T06:20:12.891Z In(05) vmx Progress 33% (none)
2025-05-02T06:20:12.918Z In(05) vmx Progress 34% (none)
2025-05-02T06:20:12.945Z In(05) vmx Progress 35% (none)
2025-05-02T06:20:12.971Z In(05) vmx Progress 36% (none)
2025-05-02T06:20:12.997Z In(05) vmx Progress 37% (none)
2025-05-02T06:20:13.027Z In(05) vmx Progress 38% (none)
2025-05-02T06:20:13.052Z In(05) vmx Progress 39% (none)
2025-05-02T06:20:13.078Z In(05) vmx Progress 40% (none)
2025-05-02T06:20:13.104Z In(05) vmx Progress 41% (none)
2025-05-02T06:20:13.132Z In(05) vmx Progress 42% (none)
2025-05-02T06:20:13.157Z In(05) vmx Progress 43% (none)
2025-05-02T06:20:13.183Z In(05) vmx Progress 44% (none)
2025-05-02T06:20:13.209Z In(05) vmx Progress 45% (none)
2025-05-02T06:20:13.237Z In(05) vmx Progress 46% (none)
2025-05-02T06:20:13.263Z In(05) vmx Progress 47% (none)
2025-05-02T06:20:13.288Z In(05) vmx Progress 48% (none)
2025-05-02T06:20:13.316Z In(05) vmx Progress 49% (none)
2025-05-02T06:20:13.344Z In(05) vmx Progress 50% (none)
2025-05-02T06:20:13.377Z In(05) vmx Progress 51% (none)
2025-05-02T06:20:13.412Z In(05) vmx Progress 52% (none)
2025-05-02T06:20:13.449Z In(05) vmx Progress 53% (none)
2025-05-02T06:20:13.480Z In(05) vmx Progress 54% (none)
2025-05-02T06:20:13.512Z In(05) vmx Progress 55% (none)
2025-05-02T06:20:13.543Z In(05) vmx Progress 56% (none)
2025-05-02T06:20:13.571Z In(05) vmx Progress 57% (none)
2025-05-02T06:20:13.598Z In(05) vmx Progress 58% (none)
2025-05-02T06:20:13.625Z In(05) vmx Progress 59% (none)
2025-05-02T06:20:13.651Z In(05) vmx Progress 60% (none)
2025-05-02T06:20:13.681Z In(05) vmx Progress 61% (none)
2025-05-02T06:20:13.707Z In(05) vmx Progress 62% (none)
2025-05-02T06:20:13.733Z In(05) vmx Progress 63% (none)
2025-05-02T06:20:13.762Z In(05) vmx Progress 64% (none)
2025-05-02T06:20:13.791Z In(05) vmx Progress 65% (none)
2025-05-02T06:20:13.846Z In(05) vmx Progress 66% (none)
2025-05-02T06:20:13.880Z In(05) vmx Progress 67% (none)
2025-05-02T06:20:13.932Z In(05) vmx Progress 68% (none)
2025-05-02T06:20:13.971Z In(05) vmx Progress 69% (none)
2025-05-02T06:20:14.024Z In(05) vmx Progress 70% (none)
2025-05-02T06:20:14.079Z In(05) vmx Progress 71% (none)
2025-05-02T06:20:14.146Z In(05) vmx Progress 72% (none)
2025-05-02T06:20:14.206Z In(05) vmx Progress 73% (none)
2025-05-02T06:20:14.265Z In(05) vmx Progress 74% (none)
2025-05-02T06:20:14.344Z In(05) vmx Progress 75% (none)
2025-05-02T06:20:14.395Z In(05) vmx Progress 76% (none)
2025-05-02T06:20:14.437Z In(05) vmx Progress 77% (none)
2025-05-02T06:20:14.484Z In(05) vmx Progress 78% (none)
2025-05-02T06:20:14.535Z In(05) vmx Progress 79% (none)
2025-05-02T06:20:14.583Z In(05) vmx Progress 80% (none)
2025-05-02T06:20:14.630Z In(05) vmx Progress 81% (none)
2025-05-02T06:20:14.677Z In(05) vmx Progress 82% (none)
2025-05-02T06:20:14.731Z In(05) vmx Progress 83% (none)
2025-05-02T06:20:14.776Z In(05) vmx Progress 84% (none)
2025-05-02T06:20:14.822Z In(05) vmx Progress 85% (none)
2025-05-02T06:20:14.868Z In(05) vmx Progress 86% (none)
2025-05-02T06:20:14.908Z In(05) vmx Progress 87% (none)
2025-05-02T06:20:14.953Z In(05) vmx Progress 88% (none)
2025-05-02T06:20:15.000Z In(05) vmx Progress 89% (none)
2025-05-02T06:20:15.042Z In(05) vmx Progress 90% (none)
2025-05-02T06:20:15.086Z In(05) vmx Progress 91% (none)
2025-05-02T06:20:15.125Z In(05) vmx Progress 92% (none)
2025-05-02T06:20:15.163Z In(05) vmx Progress 93% (none)
2025-05-02T06:20:15.203Z In(05) vmx Progress 94% (none)
2025-05-02T06:20:15.240Z In(05) vmx Progress 95% (none)
2025-05-02T06:20:15.284Z In(05) vmx Progress 96% (none)
2025-05-02T06:20:15.328Z In(05) vmx Progress 97% (none)
2025-05-02T06:20:15.381Z In(05) vmx Progress 98% (none)
2025-05-02T06:20:15.429Z In(05) vmx Progress 99% (none)
2025-05-02T06:20:15.459Z In(05) vmx MainMem: Read 4096MiB in 3442ms (1247.64 MB/s)
2025-05-02T06:20:15.459Z In(05) vmx   restoring MStats
2025-05-02T06:20:15.459Z In(05) vmx   restoring Snapshot
2025-05-02T06:20:15.459Z In(05) vmx   restoring pic
2025-05-02T06:20:15.459Z In(05) vmx   restoring scsi0:0
2025-05-02T06:20:15.459Z In(05) vmx   restoring sata0:1
2025-05-02T06:20:15.459Z In(05) vmx   restoring sata0:0
2025-05-02T06:20:15.459Z In(05) vmx   restoring FeatureCompat
2025-05-02T06:20:15.459Z In(05) vmx   restoring TimeTracker
2025-05-02T06:20:15.459Z In(05) vmx TimeTracker host to guest rate conversion 271981954 @ 2592000000Hz -> 33776581827942 @ 2592000000Hz
2025-05-02T06:20:15.459Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + 33776309845988
2025-05-02T06:20:15.459Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-02T06:20:15.459Z In(05) vmx TSC offsetting enabled.
2025-05-02T06:20:15.459Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-02T06:20:15.460Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-02T06:20:15.460Z In(05) vmx   restoring Backdoor
2025-05-02T06:20:15.460Z In(05) vmx   restoring PCI
2025-05-02T06:20:15.460Z In(05) vmx   restoring ExtCfgDevice
2025-05-02T06:20:15.460Z In(05) vmx   restoring Cs440bx
2025-05-02T06:20:15.460Z In(05) vmx DUMPER: Item 'gpe.status' [2, -1] not found.
2025-05-02T06:20:15.460Z In(05) vmx DUMPER: Item 'gpe.enable' [2, -1] not found.
2025-05-02T06:20:15.460Z In(05) vmx   restoring Floppy
2025-05-02T06:20:15.460Z In(05) vmx   restoring AcpiNotify
2025-05-02T06:20:15.460Z In(05) vmx   restoring vcpuHotPlug
2025-05-02T06:20:15.460Z In(05) vmx   restoring MemoryHotplug
2025-05-02T06:20:15.461Z In(05) vmx   restoring devHP
2025-05-02T06:20:15.461Z In(05) vmx   restoring ACPIWake
2025-05-02T06:20:15.461Z In(05) vmx   restoring OEMDevice
2025-05-02T06:20:15.461Z In(05) vmx   restoring HotButton
2025-05-02T06:20:15.461Z In(05) vmx   restoring Timer
2025-05-02T06:20:15.462Z In(05) vmx   restoring ACPI
2025-05-02T06:20:15.462Z In(05) vmx   restoring XPMode
2025-05-02T06:20:15.462Z In(05) vmx   restoring DMA
2025-05-02T06:20:15.462Z In(05) vmx   restoring BackdoorAPM
2025-05-02T06:20:15.462Z In(05) vmx   restoring smram
2025-05-02T06:20:15.463Z In(05) vmx   restoring backdoorAbsMouse
2025-05-02T06:20:15.463Z In(05) vmx   restoring Keyboard
2025-05-02T06:20:15.463Z In(05) vmx   restoring SIO
2025-05-02T06:20:15.464Z In(05) vmx   restoring monitorLate
2025-05-02T06:20:15.464Z In(05) vmx   restoring vcpuNUMA
2025-05-02T06:20:15.464Z In(05) vmx   restoring devices
2025-05-02T06:20:15.464Z In(05) vmx   restoring configdbFT
2025-05-02T06:20:15.464Z In(05) vmx   restoring DevicesPowerOn
2025-05-02T06:20:15.464Z In(05) vmx   restoring PCIBridge0
2025-05-02T06:20:15.464Z In(05) vmx   restoring PCIBridge4
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:1
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:2
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:3
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:4
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:5
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:6
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge4:7
2025-05-02T06:20:15.464Z In(05) vmx   restoring PCIBridge5
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:1
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:2
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:3
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:4
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:5
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:6
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge5:7
2025-05-02T06:20:15.464Z In(05) vmx   restoring PCIBridge6
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge6:1
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge6:2
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge6:3
2025-05-02T06:20:15.464Z In(05) vmx   restoring pciBridge6:4
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge6:5
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge6:6
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge6:7
2025-05-02T06:20:15.465Z In(05) vmx   restoring PCIBridge7
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:1
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:2
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:3
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:4
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:5
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:6
2025-05-02T06:20:15.465Z In(05) vmx   restoring pciBridge7:7
2025-05-02T06:20:15.465Z In(05) vmx   restoring Migrate
2025-05-02T06:20:15.465Z In(05) vmx   restoring vide
2025-05-02T06:20:15.465Z In(05) vmx DUMPER: Block item 'monbuf' [0, -1] not found.
2025-05-02T06:20:15.465Z In(05) vmx DUMPER: Block item 'monbuf' [1, -1] not found.
2025-05-02T06:20:15.465Z In(05) vmx   restoring SCSI0
2025-05-02T06:20:15.465Z In(05) vmx   restoring VGA
2025-05-02T06:20:15.465Z In(05) vmx   restoring SVGA
2025-05-02T06:20:15.465Z In(05) vmx SVGA: Guest reported SVGA driver: (2, 101187596, 34865152, 0)
2025-05-02T06:20:15.465Z In(05) vmx SVGA-GFB: Allocated gfbSize=4194304
2025-05-02T06:20:15.465Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "4194304"
2025-05-02T06:20:15.465Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:20:15.472Z In(05) vmx SVGA3dCaps: guest, saved in checkpoint
2025-05-02T06:20:15.472Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:20:15.472Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:20:15.472Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:20:15.472Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:20:15.472Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:20:15.472Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:20:15.472Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:20:15.472Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:20:15.472Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:20:15.472Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:20:15.472Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:20:15.472Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:20:15.472Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:20:15.472Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:20:15.472Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:20:15.472Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:20:15.472Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:20:15.473Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:20:15.473Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:20:15.473Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:20:15.473Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:20:15.473Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:20:15.473Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:20:15.473Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:20:15.473Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:20:15.473Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:20:15.473Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:20:15.473Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:20:15.473Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:20:15.473Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:20:15.473Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:20:15.473Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:20:15.473Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:20:15.473Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:20:15.473Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:20:15.473Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:20:15.473Z In(05) vmx SVGA3dCaps: guest, at resume
2025-05-02T06:20:15.473Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:20:15.473Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:20:15.474Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:20:15.474Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:20:15.474Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:20:15.474Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:20:15.474Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:20:15.474Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:20:15.474Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:20:15.474Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:20:15.474Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:20:15.474Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:20:15.474Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:20:15.474Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:20:15.474Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:20:15.474Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:20:15.474Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:20:15.474Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:20:15.474Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:20:15.474Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:20:15.474Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:20:15.474Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:20:15.474Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:20:15.474Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:20:15.474Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:20:15.474Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:20:15.474Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:20:15.475Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:20:15.475Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:20:15.475Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:20:15.475Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:20:15.475Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:20:15.475Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:20:15.475Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:20:15.475Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:20:15.475Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:20:15.475Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:20:15.475Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:20:15.475Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:20:15.475Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:20:15.475Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:20:15.475Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:20:15.475Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:20:15.475Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:20:15.475Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume id: 0
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.supports3D bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.baseCapsLevel num 9
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.maxPointSize num 189
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.maxTextureSize num 16384
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.maxVolumeExtent num 2048
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.maxTextureAnisotropy num 16
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.lineStipple bool 0
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.dxMaxConstantBuffers num 15
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.dxProvokingVertex bool 0
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.sm41 bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.multisample2x bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.multisample4x bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.msFullQuality bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.logicOps bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.bc67 num 9
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.sm5 bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.multisample8x bool 1
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.logicBlendOps bool 0
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.maxForcedSampleCount num 8
2025-05-02T06:20:15.475Z In(05) vmx SVGAFeature guest, at resume svga.gl43 bool 1
2025-05-02T06:20:15.475Z In(05) vmx   restoring usb
2025-05-02T06:20:15.475Z In(05) vmx DUMPER: Item 'lastFrnumChangeTime' [-1, -1] not found.
2025-05-02T06:20:15.475Z In(05) vmx   restoring usb:0
2025-05-02T06:20:15.475Z In(05) vmx   restoring usb:1
2025-05-02T06:20:15.475Z In(05) vmx   restoring Ethernet0
2025-05-02T06:20:15.475Z In(05) vmx   restoring sound
2025-05-02T06:20:15.476Z In(05) vmx   restoring hpet0
2025-05-02T06:20:15.476Z In(05) vmx   restoring ich7m.hpet
2025-05-02T06:20:15.476Z In(05) vmx   restoring ehci
2025-05-02T06:20:15.476Z In(05) vmx   restoring vmci0
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'VMCINumSubs' [-1, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx   restoring sata0
2025-05-02T06:20:15.476Z In(05) vmx   restoring vsock
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'remote.port' [0, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'qpair.context' [0, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'timeoutRemaining' [0, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'timeoutRemaining' [1, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'timeoutRemaining' [2, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx   restoring GuestMsg
2025-05-02T06:20:15.476Z In(05) vmx   restoring GuestRpc
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Item 'AsyncVmciSocket.numSendBuf' [-1, -1] not found.
2025-05-02T06:20:15.476Z In(05) vmx   restoring Tools
2025-05-02T06:20:15.476Z In(05) vmx   restoring Tools Install
2025-05-02T06:20:15.476Z In(05) vmx TOOLS INSTALL setting state to 0 on restore.
2025-05-02T06:20:15.476Z In(05) vmx   restoring GuestAppMonitor
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Requested 10 bytes, found 5 bytes.
2025-05-02T06:20:15.476Z In(05) vmx DUMPER: Requested 20 bytes, found 5 bytes.
2025-05-02T06:20:15.476Z In(05) vmx   restoring Hgfs
2025-05-02T06:20:15.476Z In(05) vmx   restoring MKSVMX
2025-05-02T06:20:15.477Z In(05) vmx   restoring ToolsDeployPkg
2025-05-02T06:20:15.477Z In(05) vmx DEPLOYPKG: ToolsDeployPkgCptRestore: state=0 err=0 (null msg)
2025-05-02T06:20:15.477Z In(05) vmx DEPLOYPKG: ToolsDeployPkgRestoreSuccessTasks: state=0 err=0, msg=null
2025-05-02T06:20:15.477Z In(05) vmx   restoring CMOS
2025-05-02T06:20:15.477Z In(05) vmx   restoring FlashRam
2025-05-02T06:20:15.477Z In(05) vmx Progress 101% (none)
2025-05-02T06:20:15.478Z In(05) vmx DUMPER: Updating header magic on restore.
2025-05-02T06:20:15.478Z In(05) vmx CPT: Deleting checkpoint state, '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2-ecf6dafd.vmss'.
2025-05-02T06:20:15.845Z In(05) vmx SNAPSHOT: SnapshotConfigInfoExpandVM: Unable to find 'Ubuntu 64-bit 22.04.3 2-ecf6dafd.vmss'.  Setting vmState to NULL.
2025-05-02T06:20:15.845Z No(00) vmx ConfigDB: Setting checkpoint.vmState = ""
2025-05-02T06:20:15.853Z No(00) vmx PowerOnTiming: Module CheckpointLate took 3854221 us
2025-05-02T06:20:15.855Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2025-05-02T06:20:15.855Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest codename: Coffee Lake-S/H
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000906ea 0x00010800 0xf7fa3203 0x0f8bfbff
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000906ea 0x00100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x009c27ab 0x00000000 0xbc000400
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x07300401 0x000000ff 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000000 0x00000001 0x00000201 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x0000000c 0x00000201 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000f 0x00000340 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x00278d00 0x000101d0 0x00000002 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:20:15.855Z In(05) vmx Minimum ucode level: 0x000000fa
2025-05-02T06:20:15.855Z In(05) vmx VPMC: events will use hybrid freeze.
2025-05-02T06:20:15.855Z In(05) vmx VPMC: gen counters: num 4 mask 0xffffffffffff
2025-05-02T06:20:15.855Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2025-05-02T06:20:15.855Z In(05) vmx VPMC: hardware counters: 0
2025-05-02T06:20:15.855Z In(05) vmx VPMC: perf capabilities: 0x2000
2025-05-02T06:20:15.855Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0xc
2025-05-02T06:20:15.856Z In(05) vmx SVGA: Registering IOSpace at 0x1070
2025-05-02T06:20:15.856Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2025-05-02T06:20:15.856Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=4194304
2025-05-02T06:20:15.856Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=4194304
2025-05-02T06:20:15.856Z In(05) vmx SVGA: Final Device caps : 0xfdffc3e2
2025-05-02T06:20:15.856Z In(05) vmx SVGA: Final Device caps2: 0x0005efff
2025-05-02T06:20:15.856Z In(05) vmx FeatureCompat: Capabilities:
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.sse3 = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.fma = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.pcid = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.sse41 = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.sse42 = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.movbe = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.popcnt = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.aes = 1
2025-05-02T06:20:15.856Z In(05) vmx Capability Found: cpuid.xsave = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.avx = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.f16c = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.rdrand = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.ss = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.avx2 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.smep = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.invpcid = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.rdseed = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.adx = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.smap = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.mdclear = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.stibp = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.fcmd = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.ssbd = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xsavec = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xsaves = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.abm = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.nx = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.lm = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.intel = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.ibrs = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.ibpb = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.mwait = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.vmx = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.ds = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: hv.capable = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.realmode = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.mbx = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.advexitinfo = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.eptad = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.ple = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.supports3d = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.basecapslevel = 9
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.maxpointsize = 0xbd
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.maxtexturesize = 0x4000
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.maxvolumeextent = 0x800
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.maxtextureanisotropy = 0x10
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.dxmaxconstantbuffers = 0xf
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.sm41 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.multisample2x = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.multisample4x = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.msfullquality = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.logicops = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.bc67 = 9
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.sm5 = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.multisample8x = 1
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.maxforcedsamplecount = 8
2025-05-02T06:20:15.857Z In(05) vmx Capability Found: svga0*svga.gl43 = 1
2025-05-02T06:20:15.857Z In(05) vmx FeatureCompat: Requirements:
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xgetbv_ecx1 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.supports3d - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.basecapslevel - Num:Min:9
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.maxpointsize - Num:Min:0xbd
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.maxtexturesize - Num:Min:0x4000
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.maxvolumeextent - Num:Min:0x800
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.maxtextureanisotropy - Num:Min:0x10
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.dxmaxconstantbuffers - Num:Min:0xf
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.sm41 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.multisample2x - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.multisample4x - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.msfullquality - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.logicops - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.bc67 - Num:Min:9
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.sm5 - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.multisample8x - Bool:Min:1
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.maxforcedsamplecount - Num:Min:8
2025-05-02T06:20:15.857Z In(05) vmx VM Features Required: svga*svga.gl43 - Bool:Min:1
2025-05-02T06:20:15.927Z In(05) ulm_exc VTHREAD 123145330782208 "ulm_exc" tid 178987
2025-05-02T06:20:15.927Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2025-05-02T06:20:15.929Z In(05) vmx 
2025-05-02T06:20:15.929Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2025-05-02T06:20:15.929Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem Total excluded                      :  1073664 1073664      - |      -      -      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem Actual maximum                      :         1073664        |             -
2025-05-02T06:20:15.929Z In(05)+ vmx 
2025-05-02T06:20:15.929Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       8      8      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       4      4      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |      0      0      -
2025-05-02T06:20:15.929Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   37888  37888      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Total paged                         :  4142968 4142968      - |   3517   3517      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Actual maximum                      :         4142968        |        4142968
2025-05-02T06:20:15.930Z In(05)+ vmx 
2025-05-02T06:20:15.930Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :      11     11      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1146   2241      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_AHCIREGS                   :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_LBR                        :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      89     89      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Total nonpaged                      :    2800   4343      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Actual maximum                      :           2800        |          4343
2025-05-02T06:20:15.930Z In(05)+ vmx 
2025-05-02T06:20:15.930Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     392    392      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :      16     16      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :     160    160      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_TC                          :    2052   2052      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       8      8      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_HV                          :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       4      4      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_VHV                         :      12     12      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_Numa                        :      42     42      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :     116    116      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     469    469      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2322   2322      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     270    270      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       9      9      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Total anonymous                     :    9403   9460      - |      0      0      -
2025-05-02T06:20:15.930Z In(05) vmx OvhdMem Actual maximum                      :           9403        |          9460
2025-05-02T06:20:15.930Z In(05)+ vmx 
2025-05-02T06:20:15.930Z In(05) vmx VMMEM: Precise Reservation: 16231MB (MainMem=4096MB)
2025-05-02T06:20:15.930Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2025-05-02T06:20:15.930Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2025-05-02T06:20:15.931Z No(00) vmx PowerOnTiming: ALL took 5201411 us
2025-05-02T06:20:15.931Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2025-05-02T06:20:15.931Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2025-05-02T06:20:15.931Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2025-05-02T06:20:15.932Z In(05) vcpu-0 VTHREAD 123145331318784 "vcpu-0" tid 178988
2025-05-02T06:20:15.934Z In(05) vcpu-1 VTHREAD 123145331855360 "vcpu-1" tid 178989
2025-05-02T06:20:15.934Z In(05) vcpu-2 VTHREAD 123145332391936 "vcpu-2" tid 178990
2025-05-02T06:20:15.934Z In(05) vcpu-3 VTHREAD 123145332928512 "vcpu-3" tid 178991
2025-05-02T06:20:15.935Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2025-05-02T06:20:15.936Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 4095 MB (100 %) Size:65535 MB (100 %)
2025-05-02T06:20:15.936Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x5, chipset.onlineStandby 0
2025-05-02T06:20:15.936Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:20:15.936Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:20:15.936Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:20:15.969Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2025-05-02T06:20:15.969Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: Starting isolated virtual interface, vmnet=vmnet8
2025-05-02T06:20:15.970Z In(05) vcpu-0 DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:20:15.987Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: uuid=A0BE8A3F-C259-4B67-984F-7D70F863C51A
2025-05-02T06:20:15.988Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: connect adapter to host: connect=yes
2025-05-02T06:20:15.988Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: host adapter address: ip=*************
2025-05-02T06:20:15.988Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: host adapter subnet: subnet=*************
2025-05-02T06:20:15.988Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: MTU size is not configured, vmnet=vmnet8
2025-05-02T06:20:15.995Z In(05) host-178993 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: starting interface, status=1000
2025-05-02T06:20:15.995Z In(05) host-178993 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: interface params:
2025-05-02T06:20:15.995Z In(05) host-178993 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      MTU             : 1500
2025-05-02T06:20:15.995Z In(05) host-178993 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      Max packet size : 1514
2025-05-02T06:20:15.995Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartInterface: Ethernet0: Virtual interface started successfully
2025-05-02T06:20:15.995Z In(05) vcpu-0 VNET: MACVNetMacosDSMonitorSubscribeDefaultNetIf: Starting monitoring global primary interface key
2025-05-02T06:20:15.997Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2025-05-02T06:20:15.997Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2025-05-02T06:20:15.998Z In(05) vcpu-0 FLOPPYLIB-IMAGE: Floppy geometry 80/2/18 detected from boot sector.
2025-05-02T06:20:15.999Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2025-05-02T06:20:16.003Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:20:16.004Z In(05) host-178993 VNET: MACVNetPortVirtApiPrimaryIfaceChanged: Global state callback for adapter: 0, primary if: en0
2025-05-02T06:20:16.007Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:20:16.009Z Wa(03) vcpu-0 USB: Disconnecting missing device on usb:1 port 0.
2025-05-02T06:20:16.009Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2025-05-02T06:20:16.009Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:20:16.009Z No(00) vcpu-0 ConfigDB: Unsetting all entries with prefix "ehci:0."
2025-05-02T06:20:16.022Z In(05) vcpu-0 USB: DevID(800000010e0f000b): Disconnecting device.
2025-05-02T06:20:16.023Z In(05) vcpu-0 USB: DevID(800000010e0f000b): Connecting device desc:name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5.
2025-05-02T06:20:16.023Z In(05) vcpu-0 AVCAPTURE: AVCapture_OpenDevice: Choosing default parameters to create video device.
2025-05-02T06:20:16.023Z In(05) vcpu-0 AVCAPTURE: -[AVCaptureAVFDevice open:genericDevice:]: Capture device not specified, selecting a default video source.
2025-05-02T06:20:16.107Z In(05) vcpu-0 AVCAPTURE: AVCapture_OpenDevice: Device created but supported camera settings not found.
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: VUsbVideoCreateDevice:Camera device connected to the host does not support any known camera settings
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Format 1:	 yuy2	 numFrames:2
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame:1
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: dimensions:640x480
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:20:16.107Z In(05) vcpu-0 30.000030	
2025-05-02T06:20:16.107Z In(05) vcpu-0 29.000049	
2025-05-02T06:20:16.107Z In(05) vcpu-0 28.000067	
2025-05-02T06:20:16.107Z In(05) vcpu-0 27.000027	
2025-05-02T06:20:16.107Z In(05) vcpu-0 26.000026	
2025-05-02T06:20:16.107Z In(05) vcpu-0 25.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 24.000038	
2025-05-02T06:20:16.107Z In(05) vcpu-0 23.000032	
2025-05-02T06:20:16.107Z In(05) vcpu-0 22.000022	
2025-05-02T06:20:16.107Z In(05) vcpu-0 21.000021	
2025-05-02T06:20:16.107Z In(05) vcpu-0 20.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 19.000029	
2025-05-02T06:20:16.107Z In(05) vcpu-0 18.000018	
2025-05-02T06:20:16.107Z In(05) vcpu-0 17.000009	
2025-05-02T06:20:16.107Z In(05) vcpu-0 16.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 15.000015	
2025-05-02T06:20:16.107Z In(05) vcpu-0 14.000014	
2025-05-02T06:20:16.107Z In(05) vcpu-0 13.000013	
2025-05-02T06:20:16.107Z In(05) vcpu-0 12.000005	
2025-05-02T06:20:16.107Z In(05) vcpu-0 11.000011	
2025-05-02T06:20:16.107Z In(05) vcpu-0 10.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 9.000001	
2025-05-02T06:20:16.107Z In(05) vcpu-0 8.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 7.000002	
2025-05-02T06:20:16.107Z In(05) vcpu-0 6.000002	
2025-05-02T06:20:16.107Z In(05) vcpu-0 5.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 4.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 3.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 2.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.033333	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.034483	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.035714	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.037037	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.038462	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.040000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.041667	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.043478	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.045455	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.047619	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.050000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.052632	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.055556	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.058824	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.062500	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.066667	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.071429	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.076923	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.083333	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.090909	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.100000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.111111	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.125000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.142857	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.166667	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.200000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.250000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.333333	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.500000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame:2
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: dimensions:1280x720
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:20:16.107Z In(05) vcpu-0 13.000013	
2025-05-02T06:20:16.107Z In(05) vcpu-0 12.000005	
2025-05-02T06:20:16.107Z In(05) vcpu-0 11.000011	
2025-05-02T06:20:16.107Z In(05) vcpu-0 10.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 9.000001	
2025-05-02T06:20:16.107Z In(05) vcpu-0 8.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 7.000002	
2025-05-02T06:20:16.107Z In(05) vcpu-0 6.000002	
2025-05-02T06:20:16.107Z In(05) vcpu-0 5.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 4.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 3.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 2.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.076923	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.083333	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.090909	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.100000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.111111	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.125000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.142857	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.166667	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.200000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.250000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.333333	
2025-05-02T06:20:16.107Z In(05) vcpu-0 0.500000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.107Z In(05) vcpu-0 
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Format 2:	 nv12	 numFrames:2
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame:1
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: dimensions:640x480
2025-05-02T06:20:16.107Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:20:16.107Z In(05) vcpu-0 30.000030	
2025-05-02T06:20:16.107Z In(05) vcpu-0 29.000049	
2025-05-02T06:20:16.107Z In(05) vcpu-0 28.000067	
2025-05-02T06:20:16.108Z In(05) vcpu-0 27.000027	
2025-05-02T06:20:16.108Z In(05) vcpu-0 26.000026	
2025-05-02T06:20:16.108Z In(05) vcpu-0 25.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 24.000038	
2025-05-02T06:20:16.108Z In(05) vcpu-0 23.000032	
2025-05-02T06:20:16.108Z In(05) vcpu-0 22.000022	
2025-05-02T06:20:16.108Z In(05) vcpu-0 21.000021	
2025-05-02T06:20:16.108Z In(05) vcpu-0 20.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 19.000029	
2025-05-02T06:20:16.108Z In(05) vcpu-0 18.000018	
2025-05-02T06:20:16.108Z In(05) vcpu-0 17.000009	
2025-05-02T06:20:16.108Z In(05) vcpu-0 16.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 15.000015	
2025-05-02T06:20:16.108Z In(05) vcpu-0 14.000014	
2025-05-02T06:20:16.108Z In(05) vcpu-0 13.000013	
2025-05-02T06:20:16.108Z In(05) vcpu-0 12.000005	
2025-05-02T06:20:16.108Z In(05) vcpu-0 11.000011	
2025-05-02T06:20:16.108Z In(05) vcpu-0 10.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 9.000001	
2025-05-02T06:20:16.108Z In(05) vcpu-0 8.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 7.000002	
2025-05-02T06:20:16.108Z In(05) vcpu-0 6.000002	
2025-05-02T06:20:16.108Z In(05) vcpu-0 5.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 4.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 3.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 2.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 
2025-05-02T06:20:16.108Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.033333	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.034483	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.035714	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.037037	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.038462	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.040000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.041667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.043478	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.045455	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.047619	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.050000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.052632	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.055556	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.058824	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.062500	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.066667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.071429	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.076923	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.083333	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.090909	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.100000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.111111	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.125000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.142857	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.166667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.200000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.250000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.333333	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.500000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 
2025-05-02T06:20:16.108Z In(05) vcpu-0 VUsbVideo: Frame:2
2025-05-02T06:20:16.108Z In(05) vcpu-0 VUsbVideo: dimensions:1280x720
2025-05-02T06:20:16.108Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:20:16.108Z In(05) vcpu-0 26.000026	
2025-05-02T06:20:16.108Z In(05) vcpu-0 25.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 24.000038	
2025-05-02T06:20:16.108Z In(05) vcpu-0 23.000032	
2025-05-02T06:20:16.108Z In(05) vcpu-0 22.000022	
2025-05-02T06:20:16.108Z In(05) vcpu-0 21.000021	
2025-05-02T06:20:16.108Z In(05) vcpu-0 20.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 19.000029	
2025-05-02T06:20:16.108Z In(05) vcpu-0 18.000018	
2025-05-02T06:20:16.108Z In(05) vcpu-0 17.000009	
2025-05-02T06:20:16.108Z In(05) vcpu-0 16.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 15.000015	
2025-05-02T06:20:16.108Z In(05) vcpu-0 14.000014	
2025-05-02T06:20:16.108Z In(05) vcpu-0 13.000013	
2025-05-02T06:20:16.108Z In(05) vcpu-0 12.000005	
2025-05-02T06:20:16.108Z In(05) vcpu-0 11.000011	
2025-05-02T06:20:16.108Z In(05) vcpu-0 10.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 9.000001	
2025-05-02T06:20:16.108Z In(05) vcpu-0 8.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 7.000002	
2025-05-02T06:20:16.108Z In(05) vcpu-0 6.000002	
2025-05-02T06:20:16.108Z In(05) vcpu-0 5.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 4.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 3.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 2.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 
2025-05-02T06:20:16.108Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.038462	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.040000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.041667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.043478	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.045455	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.047619	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.050000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.052632	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.055556	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.058824	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.062500	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.066667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.071429	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.076923	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.083333	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.090909	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.100000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.111111	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.125000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.142857	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.166667	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.200000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.250000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.333333	
2025-05-02T06:20:16.108Z In(05) vcpu-0 0.500000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 1.000000	
2025-05-02T06:20:16.108Z In(05) vcpu-0 
2025-05-02T06:20:16.108Z No(00) vcpu-0 ConfigDB: Setting ehci:0.present = "TRUE"
2025-05-02T06:20:16.108Z No(00) vcpu-0 ConfigDB: Setting ehci:0.deviceType = "video"
2025-05-02T06:20:16.108Z No(00) vcpu-0 ConfigDB: Setting ehci:0.port = "0"
2025-05-02T06:20:16.108Z No(00) vcpu-0 ConfigDB: Setting ehci:0.parent = "-1"
2025-05-02T06:20:16.118Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:20:16.119Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2025-05-02T06:20:16.120Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2025-05-02T06:20:16.120Z No(00) vcpu-0 Metrics lastUpdate (s): 11281
2025-05-02T06:20:16.120Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2025-05-02T06:20:16.120Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2025-05-02T06:20:16.120Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-02T06:20:16.120Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2025-05-02T06:20:16.120Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2025-05-02T06:20:16.121Z In(05) vmx USB: New set of 2 USB devices.
2025-05-02T06:20:16.122Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2025-05-02T06:20:16.122Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:20:16.122Z In(05) vcpu-3 CPT: vmstart
2025-05-02T06:20:16.122Z In(05) vcpu-0 CPT: vmstart
2025-05-02T06:20:16.122Z In(05) vcpu-2 CPT: vmstart
2025-05-02T06:20:16.122Z In(05) vcpu-1 CPT: vmstart
2025-05-02T06:20:16.123Z No(00) vmx ConfigDB: Setting floppy0.clientDevice = "FALSE"
2025-05-02T06:20:16.124Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2025-05-02T06:20:16.125Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2025-05-02T06:20:16.125Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu-64' (refreshCount=1, lastCount=1).
2025-05-02T06:20:16.125Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:20:16.125Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:20:16.125Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:20:16.125Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:20:16.125Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2025-05-02T06:20:16.125Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'unmanaged', 'unmanaged', install possible
2025-05-02T06:20:16.126Z In(05) vmx USB: New set of 6 USB devices.
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:QinHeng\ USB2.0-Serial vid:1a86 pid:7523 path:65/3/2 speed:full family:vendor arbRuntimeKey:4 version:5]
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:Realtek\ USB\ 10/100/1000\ LAN vid:0bda pid:8153 path:0/1/4 speed:super family:vendor,comm serialnum:000001 arbRuntimeKey:3 version:5]
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:Fresco\ Logic\ Generic\ Billboard\ Device vid:1d5c pid:7102 path:65/3/3/1 speed:full family:other arbRuntimeKey:2 version:5]
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:CMX\ USB\ PnP\ Audio\ Device vid:2000 pid:dd00 path:65/3/3/2/2 speed:full family:audio,hid serialnum:U2dc0z7ws53a79rn arbRuntimeKey:1 version:5]
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2025-05-02T06:20:16.126Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:20:16.149Z In(05) vthread-179014 VTHREAD 123145313832960 "vthread-179014" tid 179014
2025-05-02T06:20:16.149Z In(05) vthread-179015 VTHREAD 123145333465088 "vthread-179015" tid 179015
2025-05-02T06:20:16.149Z In(05) vthread-179016 VTHREAD 123145334001664 "vthread-179016" tid 179016
2025-05-02T06:20:16.161Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:20:16.162Z Wa(03) vcpu-1 GuestRpc: application toolbox, changing channel 65535 -> 0
2025-05-02T06:20:16.162Z In(05) vcpu-1 GuestRpc: Channel 0, guest application toolbox.
2025-05-02T06:20:16.162Z In(05) vcpu-1 Tools: [AppStatus] Last heartbeat value 0 (never received)
2025-05-02T06:20:16.162Z In(05) vcpu-1 TOOLS: appName=toolbox, oldStatus=0, status=1, guestInitiated=0.
2025-05-02T06:20:16.171Z In(05) mks MKSControlMgr: connected
2025-05-02T06:20:16.178Z In(05) vmx Tools: Changing running status: 0 => 2.
2025-05-02T06:20:16.178Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 0 (never received)
2025-05-02T06:20:16.178Z In(05) vmx Tools: Removing Tools inactivity timer.
2025-05-02T06:20:16.181Z In(05) vmx TOOLS Received tools.set.versiontype rpc call, version = 12389, type = 4
2025-05-02T06:20:16.181Z In(05) vmx TOOLS Setting toolsVersionStatus = TOOLS_STATUS_UNMANAGED
2025-05-02T06:20:16.181Z In(05) vmx Tools_SetVersionAndType did nothing; new tools version (12389) and type (4) match old Tools version and type
2025-05-02T06:20:16.182Z In(05) vmx Guest: Executing script for state change 'OS_Resume'.
2025-05-02T06:20:16.190Z In(05) vcpu-0 Tools: State change '4' progress: last event 0, event 1, success 1.
2025-05-02T06:20:16.645Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2025-05-02T06:20:16.645Z In(05) vmx USB: DevID(600000010e0f0008): Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5.
2025-05-02T06:20:16.645Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:20:16.646Z In(05) vmx USB: New set of 6 USB devices.
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:QinHeng\ USB2.0-Serial vid:1a86 pid:7523 path:65/3/2 speed:full family:vendor arbRuntimeKey:4 version:5]
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:Realtek\ USB\ 10/100/1000\ LAN vid:0bda pid:8153 path:0/1/4 speed:super family:vendor,comm serialnum:000001 arbRuntimeKey:3 version:5]
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:Fresco\ Logic\ Generic\ Billboard\ Device vid:1d5c pid:7102 path:65/3/3/1 speed:full family:other arbRuntimeKey:2 version:5]
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:CMX\ USB\ PnP\ Audio\ Device vid:2000 pid:dd00 path:65/3/3/2/2 speed:full family:audio,hid serialnum:U2dc0z7ws53a79rn arbRuntimeKey:1 version:5]
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth virtPath:usb:2 deviceType:virtual-bluetooth info:0000001 version:5], connected to usb:1 port 0.
2025-05-02T06:20:16.646Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:20:16.648Z Wa(03) vcpu-2 GuestRpc: application toolbox-dnd, changing channel 65535 -> 1
2025-05-02T06:20:16.648Z In(05) vcpu-2 GuestRpc: Channel 1, guest application toolbox-dnd.
2025-05-02T06:20:16.648Z In(05) vcpu-2 TOOLS: appName=toolbox-dnd, oldStatus=0, status=1, guestInitiated=0.
2025-05-02T06:20:16.655Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:20:16.655Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:20:16.656Z In(05) vthread-179030 VTHREAD 123145335074816 "vthread-179030" tid 179030
2025-05-02T06:20:16.656Z In(05) vthread-179031 VTHREAD 123145335611392 "vthread-179031" tid 179031
2025-05-02T06:20:16.717Z In(05) vthread-179042 VTHREAD 123145336684544 "vthread-179042" tid 179042
2025-05-02T06:20:16.769Z In(05) vthread-179045 VTHREAD 123145337221120 "vthread-179045" tid 179045
2025-05-02T06:20:16.912Z In(05) vcpu-2 DDB: "longContentID" = "761732d5d3cb45dd0d0fccf83afbe4cc" (was "bb0debb1998772818c48925846819d21")
2025-05-02T06:20:16.934Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:20:16.934Z In(05) mks SWBWindow: Window 17 Defined: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1920, 1200) pixelScale=2, flags=0xF
2025-05-02T06:20:16.934Z In(05) windowThread-17 VTHREAD 123145337757696 "windowThread-17" tid 179050
2025-05-02T06:20:16.934Z In(05) mks MKS-HWinMux: Started MacOS presentation backend.
2025-05-02T06:20:16.934Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:20:16.934Z In(05) mks SWBWindow: Window 18 Defined: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x1A
2025-05-02T06:20:16.934Z In(05) windowThread-18 VTHREAD 123145338294272 "windowThread-18" tid 179051
2025-05-02T06:20:16.958Z In(05) vmx Guest: Script exit code: 0, success = 1
2025-05-02T06:20:16.959Z In(05) vmx TOOLS state change 4 returned status 1
2025-05-02T06:20:16.959Z In(05) vmx Tools: State change '4' progress: last event 1, event 2, success 1.
2025-05-02T06:20:16.959Z In(05) vmx Tools: State change '4' progress: last event 1, event 4, success 1.
2025-05-02T06:20:16.959Z In(05) vmx Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-02T06:20:16.959Z In(05) vmx Tools: Changing running status: 2 => 1.
2025-05-02T06:20:16.959Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2025-05-02T06:20:17.015Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:20:21.662Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2025-05-02T06:20:22.684Z In(05) vthread-179116 VTHREAD 123145314369536 "vthread-179116" tid 179116
2025-05-02T06:20:24.168Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:20:24.168Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:20:24.176Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:20:24.195Z In(05) vmx GuestRpc: Got error for channel 1 connection 6: Remote disconnected
2025-05-02T06:20:24.195Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 1
2025-05-02T06:20:24.195Z In(05) vmx GuestRpc: Closing channel 1 connection 6
2025-05-02T06:20:24.199Z In(05) vcpu-0 GuestRpc: Reinitializing Channel 1(toolbox-dnd)
2025-05-02T06:20:24.199Z In(05) vcpu-0 TOOLS: appName=toolbox-dnd, oldStatus=1, status=0, guestInitiated=1.
2025-05-02T06:20:24.457Z In(05) vthread-179131 VTHREAD 123145338830848 "vthread-179131" tid 179131
2025-05-02T06:20:24.535Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:20:24.536Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:20:24.537Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:20:24.563Z In(05) mks SWBWindow: Window 18 Destroyed: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x12
2025-05-02T06:20:24.564Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:20:24.599Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:20:24.599Z In(05) mks SWBWindow: Window 19 Defined: src screenId=1, src xywh(0, 0, 1280, 800) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x1A
2025-05-02T06:20:24.599Z In(05) windowThread-19 VTHREAD 123145338294272 "windowThread-19" tid 179134
2025-05-02T06:20:24.607Z In(05) vcpu-3 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:20:24.607Z In(05) vcpu-3 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:20:24.607Z In(05) vcpu-3 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:20:24.659Z In(05) DAC1 VTHREAD 123145340440576 "DAC1" tid 179171
2025-05-02T06:20:24.678Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:20:24.678Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:20:24.678Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:20:24.863Z In(05) ADC VTHREAD 123145341513728 "ADC" tid 179183
2025-05-02T06:20:25.299Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:20:25.299Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:20:25.300Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:20:25.317Z In(05) mks SWBWindow: Window 19 Destroyed: src screenId=1, src xywh(0, 0, 1280, 800) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x12
2025-05-02T06:20:25.317Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:20:25.366Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:20:25.366Z In(05) mks SWBWindow: Window 20 Defined: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x1A
2025-05-02T06:20:25.366Z In(05) windowThread-20 VTHREAD 123145338294272 "windowThread-20" tid 179204
2025-05-02T06:20:27.815Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:20:27.821Z In(05) vcpu-1 GuestRpc: Reinitializing Channel 0(toolbox)
2025-05-02T06:20:27.821Z In(05) vcpu-1 Tools: [AppStatus] Last heartbeat value 12 (last received 0s ago)
2025-05-02T06:20:27.821Z In(05) vcpu-1 TOOLS: appName=toolbox, oldStatus=1, status=0, guestInitiated=1.
2025-05-02T06:20:27.825Z In(05) vmx GuestRpc: Got error for channel 0 connection 5: Remote disconnected
2025-05-02T06:20:27.825Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 0
2025-05-02T06:20:27.825Z In(05) vmx GuestRpc: Closing channel 0 connection 5
2025-05-02T06:20:27.829Z In(05) vmx GuestRpc: Got RPCI vsocket connection 9, assigned to channel 0.
2025-05-02T06:20:27.829Z In(05) vmx Guest: [vgauthservice] VGAuthService END SERVICE
2025-05-02T06:20:27.829Z In(05) vmx GuestRpc: Closing channel 0 connection 9
2025-05-02T06:20:27.989Z In(05) vcpu-1 VMMouse: CMD Disable
2025-05-02T06:20:27.989Z In(05) vcpu-1 VMMouse: Disabling VMMouse mode
2025-05-02T06:20:28.081Z In(05) vcpu-0 PIIX4: PM Soft Off.  Good-bye.
2025-05-02T06:20:28.081Z In(05) vcpu-0 Chipset: The guest has requested that the virtual machine be powered off.
2025-05-02T06:20:28.081Z No(00) vcpu-0 ConfigDB: Setting softPowerOff = "TRUE"
2025-05-02T06:20:28.085Z In(05) vcpu-0 VMX: Issuing power-off request...
2025-05-02T06:20:28.085Z In(05) vmx Stopping VCPU threads...
2025-05-02T06:20:28.085Z In(05) vmx MKSThread: Requesting MKS exit
2025-05-02T06:20:28.085Z In(05) vmx Stopping MKS/SVGA threads
2025-05-02T06:20:28.101Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:20:28.101Z In(05) svga SVGA thread is exiting the main loop
2025-05-02T06:20:28.110Z In(05) mks SWBWindow: Window 17 Destroyed: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1920, 1200) pixelScale=2, flags=0xF
2025-05-02T06:20:28.110Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:20:28.110Z In(05) mks SWBWindow: Window 20 Destroyed: src screenId=1, src xywh(0, 0, 3840, 2400) dest xywh(0, 0, 1464, 915) pixelScale=1, flags=0x1A
2025-05-02T06:20:28.110Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2025-05-02T06:20:28.110Z In(05) vmx MKS/SVGA threads are stopped
2025-05-02T06:20:28.111Z In(05) vmx USB: DevID(600000010e0f0008): Disconnecting device.
2025-05-02T06:20:28.111Z In(05) vmx USB: DevID(800000010e0f000b): Disconnecting device.
2025-05-02T06:20:28.113Z In(05) vmx 
2025-05-02T06:20:28.113Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2025-05-02T06:20:28.113Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem Total excluded                      :  1073664 1073664      - |      -      -      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem Actual maximum                      :         1073664        |             -
2025-05-02T06:20:28.113Z In(05)+ vmx 
2025-05-02T06:20:28.113Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       8      8      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       4      4      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    467    467      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:20:28.113Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      2      2      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      7      7      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      4      4      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    211    211      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |     13     13      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      4      4      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |     10     10      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      4      4      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      4      4      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |     14     14      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |     55     55      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |     53     53      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0      6      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      1      1      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |     17     17      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      8      8      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   37888  37888      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Total paged                         :  4142968 4142968      - |   3814   3820      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Actual maximum                      :         4142968        |        4142968
2025-05-02T06:20:28.114Z In(05)+ vmx 
2025-05-02T06:20:28.114Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :      11     11      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1146   2241      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_AHCIREGS                   :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_LBR                        :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      89     89      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Total nonpaged                      :    2800   4343      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Actual maximum                      :           2800        |          4343
2025-05-02T06:20:28.114Z In(05)+ vmx 
2025-05-02T06:20:28.114Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     392    392      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :      16     16      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :     160    160      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_TC                          :    2052   2052      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       8      8      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_HV                          :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       4      4      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_VHV                         :      12     12      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_Numa                        :      42     42      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :     116    116      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     469    469      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2322   2322      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     270    270      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       9      9      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Total anonymous                     :    9403   9460      - |      0      0      -
2025-05-02T06:20:28.114Z In(05) vmx OvhdMem Actual maximum                      :           9403        |          9460
2025-05-02T06:20:28.114Z In(05)+ vmx 
2025-05-02T06:20:28.114Z In(05) vmx VMMEM: Maximum Reservation: 16237MB (MainMem=4096MB)
2025-05-02T06:20:28.114Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:20:28.115Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:20:28.115Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2025-05-02T06:20:28.115Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-05-02T06:20:28.115Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-05-02T06:20:28.115Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2025-05-02T06:20:28.115Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-05-02T06:20:28.115Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-05-02T06:20:28.174Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2025-05-02T06:20:28.174Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x0
2025-05-02T06:20:28.174Z In(05) vmx Tools: Changing running status: 1 => 0.
2025-05-02T06:20:28.174Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 12 (last received 0s ago)
2025-05-02T06:20:28.174Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:20:28.175Z In(05) vmx Tools: [AppStatus] Last heartbeat value 12 (last received 0s ago)
2025-05-02T06:20:28.175Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2025-05-02T06:20:28.176Z In(05) vmx VNET: MACVNetMacosDSMonitorUnsubscribeDefaultNetIf: Stopping global primary interface key monitoring
2025-05-02T06:20:28.206Z In(05) host-179041 VNET: MacosVmnetVirtApiStopHandler: Ethernet0: stopping virtual interface, status=1000
2025-05-02T06:20:28.207Z In(05) vmx USB: DevID(10e0f0002): Disconnecting device.
2025-05-02T06:20:28.207Z In(05) vmx USB: DevID(200000040e0f0003): Disconnecting device.
2025-05-02T06:20:28.208Z In(05) sensor SensorLib: Sensor thread exiting.
2025-05-02T06:20:28.208Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread exiting.
2025-05-02T06:20:28.211Z In(05) mks MKSControlMgr: disconnected
2025-05-02T06:20:28.212Z In(05) mks MKS-RenderMain: Stopping MTLRenderer
2025-05-02T06:20:28.212Z In(05) mks MKS-RenderMain: Stopping ISBRenderer (MTLRenderer)
2025-05-02T06:20:28.257Z In(05) mks MKS-RenderMain: Stopped ISBRenderer: (MTLRenderer)
2025-05-02T06:20:28.258Z In(05) mks MKS PowerOff
2025-05-02T06:20:28.258Z In(05) svga SVGA thread is exiting
2025-05-02T06:20:28.258Z In(05) mks MKS thread is exiting
2025-05-02T06:20:28.258Z Wa(03) vmx 
2025-05-02T06:20:28.258Z In(05) vmx scsi0:0: numIOs = 5738 numMergedIOs = 690 numSplitIOs = 39 ( 5.3%)
2025-05-02T06:20:28.258Z In(05) vmx Closing disk 'scsi0:0'
2025-05-02T06:20:28.366Z In(05) deviceThread Device thread is exiting
2025-05-02T06:20:28.366Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2025-05-02T06:20:28.367Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-05-02T06:20:28.367Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-05-02T06:20:28.367Z In(05) vmx WORKER: asyncOps=5988 maxActiveOps=9 maxPending=7 maxCompleted=9
2025-05-02T06:20:28.367Z In(05) PowerNotifyThread PowerNotify thread exiting.
2025-05-02T06:20:28.368Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:20:28.368Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:20:28.368Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 10948D000.
2025-05-02T06:20:28.369Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2025-05-02T06:20:28.370Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:20:28.370Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:20:28.370Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:20:28.370Z In(05) vmx VMX idle exit
2025-05-02T06:20:28.370Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 18
2025-05-02T06:20:28.764Z In(05) vmx Services_Exit: Closed the services.
2025-05-02T06:20:28.765Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-05-02T06:20:28.765Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-05-02T06:20:28.768Z In(05) vmx Flushing VMX VMDB connections
2025-05-02T06:20:28.768Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-05-02T06:20:28.768Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-05-02T06:20:28.768Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-05-02T06:20:28.768Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 14
2025-05-02T06:20:28.768Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 15
2025-05-02T06:20:28.772Z In(05) vmx VMX exit (0).
2025-05-02T06:20:28.772Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-05-02T06:20:28.772Z In(05) vmx AIOMGR-S : stat o=5 r=1105 w=1 i=0 br=4300818272 bw=12
