2025-05-02T06:21:29.100Z In(05) vmx Log for VMware Fusion pid=21232 version=13.5.2 build=build-23775688 option=Release
2025-05-02T06:21:29.100Z In(05) vmx The host is x86_64.
2025-05-02T06:21:29.100Z In(05) vmx Host codepage=UTF-8 encoding=UTF-8
2025-05-02T06:21:29.100Z In(05) vmx Host is macOS 15.5 (24F5068b) Darwin 24.5.0
2025-05-02T06:21:29.100Z In(05) vmx Host offset from UTC is +05:30.
2025-05-02T06:21:29.074Z In(05) vmx VTHREAD 140704386637184 "vmx" tid 181664
2025-05-02T06:21:29.078Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:21:29.078Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:21:29.078Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.078Z In(05) vmx Msg_Reset:
2025-05-02T06:21:29.078Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.078Z In(05) vmx ----------------------------------------
2025-05-02T06:21:29.078Z In(05) vmx ConfigDB: Failed to load /Library/Preferences/VMware Fusion/config
2025-05-02T06:21:29.078Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:21:29.078Z In(05) vmx Msg_Reset:
2025-05-02T06:21:29.078Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:21:29.078Z In(05) vmx ----------------------------------------
2025-05-02T06:21:29.078Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config
2025-05-02T06:21:29.078Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:21:29.078Z In(05) vmx Msg_Reset:
2025-05-02T06:21:29.078Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:21:29.078Z In(05) vmx ----------------------------------------
2025-05-02T06:21:29.078Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings
2025-05-02T06:21:29.079Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.079Z In(05) vmx Msg_Reset:
2025-05-02T06:21:29.079Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.079Z In(05) vmx ----------------------------------------
2025-05-02T06:21:29.079Z In(05) vmx ConfigDB: Failed to load ~/Library/Preferences/VMware Fusion/config
2025-05-02T06:21:29.090Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-05-02T06:21:29.090Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx PREF Optional preferences file not found at /Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:21:29.090Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/settings. Using default values.
2025-05-02T06:21:29.090Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/config. Using default values.
2025-05-02T06:21:29.090Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:29.090Z In(05) vmx PREF Optional preferences file not found at /Users/<USER>/Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:21:29.097Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: Client usage
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: Server usage
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:21:29.099Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:21:29.102Z In(05) vmx Hostname=Rohans-MacBook-Pro.local
2025-05-02T06:21:29.103Z In(05) vmx IP=127.0.0.1 (lo0)
2025-05-02T06:21:29.103Z In(05) vmx IP=::1 (lo0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::1 (lo0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::aede:48ff:fe00:1122 (en5)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::b414:79ff:fe37:ddd5 (ap1)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::10ad:8605:26fd:f9e1 (en0)
2025-05-02T06:21:29.103Z In(05) vmx IP=*********** (en0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::c0a2:1eff:fe3c:d3 (awdl0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::c0a2:1eff:fe3c:d3 (llw0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::71:ab8a:4db0:d5fa (utun0)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::32fe:1b8d:9696:9ee3 (utun1)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::3804:28be:b3d1:22a6 (utun2)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::ce81:b1c:bd2c:69e (utun3)
2025-05-02T06:21:29.103Z In(05) vmx IP=************ (bridge100)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c64 (bridge100)
2025-05-02T06:21:29.103Z In(05) vmx IP=************* (bridge101)
2025-05-02T06:21:29.103Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c65 (bridge101)
2025-05-02T06:21:29.103Z In(05) vmx System uptime 11354700675 us
2025-05-02T06:21:29.103Z In(05) vmx Command line: "/Applications/VMware Fusion.app/Contents/Library/vmware-vmx" "-E" "en" "-s" "vmx.stdio.keep=TRUE" "-#" "product=64;name=VMware Fusion;version=13.5.2;buildnumber=23775688;licensename=VMware Fusion for Mac OS;licenseversion=13.0;" "-@" "duplex=3;msgs=ui" "-D" "4" "/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx"
2025-05-02T06:21:29.103Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:21:29.103Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:21:29.229Z In(05) vmx Duplex socket: 3
2025-05-02T06:21:29.248Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 16
2025-05-02T06:21:29.248Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 19
2025-05-02T06:21:29.248Z In(05) vmx VigorTransport listening on fd 20
2025-05-02T06:21:29.248Z In(05) vmx Vigor_Init 1
2025-05-02T06:21:29.248Z In(05) vmx Connecting 'ui' to fd '3' with user '(null)'
2025-05-02T06:21:29.249Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-05-02T06:21:29.250Z In(05) vmx /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx: Setup symlink /var/run/vmware/7c69a851e62cb34e34c456da6ed7d4056046773c4790429821e01a8ae1e08e3b -> /var/run/vmware/501/11354826766_21232
2025-05-02T06:21:29.251Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-05-02T06:21:29.251Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-05-02T06:21:29.255Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:21:29.255Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:21:29.255Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:21:29.255Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-05-02T06:21:29.255Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-05-02T06:21:29.340Z In(05) vmx Services_Init: Successfully opened the services.
2025-05-02T06:21:29.340Z In(05) vmx FeatureCompat: No EVC masks.
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID codename: Coffee Lake-S/H
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000000, 0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000001, 0: 0x000906ea 0x00100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000002, 0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11142120
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000006, 0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000040 0x000003c0 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000040 0x00000400 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.340Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 00000016, 0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000003, 0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000004, 0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:21:29.341Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR       0x3a =                0x5
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x480 =   0xda040000000004
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x481 =       0x7f0000003f
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x482 = 0xfdf9fffe9500697a
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x483 =   0x737fff00236fff
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x484 =     0xb3ff000091ff
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x489 =           0x3767ff
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48b =   0x515cef000000a2
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48d =       0x7f00000016
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x48f =  0x1ffffff00036dfb
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x490 =    0x3ffff000011fb
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x491 =                  0
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x492 =                  0
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x10a =          0xa000c04
2025-05-02T06:21:29.342Z In(05) vmx Common: MSR      0x122 =                  0
2025-05-02T06:21:29.342Z In(05) vmx UTIL: Current file descriptor limit: soft 12800, hard 4294967295.
2025-05-02T06:21:29.342Z In(05) vmx TSC Hz estimates: vmmon 0, cpuinfo 0, cpufreq 0 sysctlfreq 2592000000. Using 2592000000 Hz
2025-05-02T06:21:29.342Z In(05) vmx PTSC: RefClockToPTSC 0 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:21:29.342Z In(05) vmx PTSC: RefClockToPTSC ((x * 2147483648) >> 31) + -29368272492598
2025-05-02T06:21:29.342Z In(05) vmx PTSC: tscOffset 0
2025-05-02T06:21:29.342Z In(05) vmx PTSC: using user-level reference clock
2025-05-02T06:21:29.342Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-05-02T06:21:29.342Z In(05) vmx PTSC: current PTSC=102486
2025-05-02T06:21:29.345Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 26
2025-05-02T06:21:29.345Z In(05) vmx Current Display Settings:
2025-05-02T06:21:29.345Z In(05) vmx    Display: 0 size: 1920x1200 pixelSize: 3840x2400 position: (0, 0) Primary
2025-05-02T06:21:29.347Z In(05) vmx [107918000-108A38000): /Applications/VMware Fusion.app/Contents/Library/vmware-vmx
2025-05-02T06:21:29.368Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-05-02T06:21:29.369Z In(05) vmx changing directory to /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/.
2025-05-02T06:21:29.369Z In(05) vmx Config file: /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx
2025-05-02T06:21:29.369Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:21:29.369Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-05-02T06:21:29.372Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.002644 seconds.
2025-05-02T06:21:29.372Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.003123 seconds.
2025-05-02T06:21:29.384Z No(00) vmx ConfigDB: Setting floppy0.clientDevice = "FALSE"
2025-05-02T06:21:29.396Z Wa(03) vmx PowerOn
2025-05-02T06:21:29.396Z In(05) vmx VMX_PowerOn: VMX build 23775688, UI build 23775688
2025-05-02T06:21:29.396Z In(05) vmx Processor affinity not supported on this host OS
2025-05-02T06:21:29.401Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-05-02T06:21:29.402Z In(05) vmx VMXSTATS: Successfully created stats file 'Ubuntu 64-bit 22.04.3 2.scoreboard'
2025-05-02T06:21:29.403Z In(05) vmx VMXSTATS: Update Product Information: VMware Fusion	13.5.2	build-23775688	Release  TotalBlockSize: 48
2025-05-02T06:21:29.403Z In(05) vmx HOST sysname Darwin, nodename Rohans-MacBook-Pro.local, release 24.5.0, version Darwin Kernel Version 24.5.0: Tue Apr 22 20:35:39 PDT 2025; root:xnu-11417.121.6~4/RELEASE_X86_64, machine x86_64
2025-05-02T06:21:29.403Z In(05) vmx DICT --- GLOBAL SETTINGS /Applications/VMware Fusion.app/Contents/Library/settings
2025-05-02T06:21:29.403Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-05-02T06:21:29.403Z In(05) vmx DICT --- USER PREFERENCES /Users/<USER>/Library/Preferences/VMware Fusion/preferences
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.dataCollectionEnabled.epoch = ""
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.dataCollectionEnabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.defaultProfileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.maxProfiles = "4"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileKey = "5202618b-19c9-7a89-903f-4ee6655cb796"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileName = "Windows 10 Profile"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileType = "windows9"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableOSShortcuts = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableKeyMappings = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.selectedLanguage = ""
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.cmdKeyFilterType = "none"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.maxMappings = "30"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.mappingKey = "0"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.fromHost = "GUI Z"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.mappingKey = "1"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.fromHost = "GUI X"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.toGuest = "CONTROL X"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.mappingKey = "2"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.fromHost = "GUI C"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.toGuest = "CONTROL C"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.mappingKey = "3"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.fromHost = "GUI V"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.toGuest = "CONTROL V"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.mappingKey = "4"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.fromHost = "GUI P"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.toGuest = "CONTROL P"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.mappingKey = "5"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.fromHost = "GUI A"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.toGuest = "CONTROL A"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.mappingKey = "6"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.fromHost = "GUI S"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.toGuest = "CONTROL S"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.mappingKey = "7"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.fromHost = "GUI F"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.toGuest = "0x03d"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.mappingKey = "8"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.fromHost = "GUI W"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.mappingKey = "9"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.mappingKey = "10"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.fromHost = "0x11c"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.toGuest = "0x138"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.mappingKey = "11"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.fromHost = "GUI SHIFT H"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.toGuest = "GUI H"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.mappingKey = "12"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.toGuest = "GUI M"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.mappingKey = "13"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.fromHost = "GUI SHIFT P"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.toGuest = "GUI P"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.mappingKey = "14"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.fromHost = "GUI SHIFT V"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.toGuest = "GUI V"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.mappingKey = "15"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.fromHost = "GUI SHIFT X"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.toGuest = "GUI X"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.mappingKey = "16"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.fromHost = "GUI SHIFT Z"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.toGuest = "GUI Z"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.mappingKey = "17"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.fromHost = "Mouse1 Control"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.toGuest = "Mouse2"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.mappingKey = "18"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.fromHost = "Mouse1 GUI"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.toGuest = "Mouse3"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.mappingKey = "19"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.fromHost = "GUI CONTROL"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.toGuest = "Ungrab"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.mappingKey = "20"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.fromHost = "GUI CONTROL F"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.toGuest = "Fullscreen"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.mappingKey = "21"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.fromHost = "GUI SHIFT U"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.toGuest = "Unity"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.mappingKey = "22"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.fromHost = "GUI CONTROL S"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.toGuest = "SingleWindow"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.mappingKey = "23"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.fromHost = "GUI 0x029"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.toGuest = "CycleWindow"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.mappingKey = "24"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.toGuest = "CycleWindowReverse"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.mappingKey = "25"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.fromHost = "GUI H"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.toGuest = "HideApplication"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.mappingKey = "26"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.fromHost = "GUI M"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.toGuest = "MinimizeWindow"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.mappingKey = "27"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.fromHost = "GUI Q"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.toGuest = "Quit"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.mappingKey = "28"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.toGuest = "ToggleHideMenu"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.mappingKey = "29"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.fromHost = "GUI E"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.toGuest = "Settings"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileKey = "521d2205-554c-06fa-8306-642f387be31a"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileName = "Mac Profile"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileType = "mac"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableOSShortcuts = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableKeyMappings = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.languageSpecificKeyMappingsEnabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.selectedLanguage = ""
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.cmdKeyFilterType = "none"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.maxMappings = "14"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.mappingKey = "0"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.fromHost = "Mouse1 Control"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.toGuest = "Mouse2"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.mappingKey = "1"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.fromHost = "Mouse1 GUI"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.toGuest = "Mouse3"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.mappingKey = "2"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.fromHost = "GUI CONTROL"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.toGuest = "Ungrab"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.mappingKey = "3"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.fromHost = "GUI CONTROL F"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.toGuest = "Fullscreen"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.mappingKey = "4"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.fromHost = "GUI CONTROL U"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.toGuest = "Unity"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.mappingKey = "5"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.fromHost = "GUI SHIFT U"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.toGuest = "Unity"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.mappingKey = "6"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.enabled = "TRUE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.fromHost = "GUI CONTROL S"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.toGuest = "SingleWindow"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.mappingKey = "7"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.fromHost = "GUI 0x029"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.toGuest = "CycleWindow"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.mappingKey = "8"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.enabled = "FALSE"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:21:29.403Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.toGuest = "CycleWindowReverse"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.mappingKey = "9"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.fromHost = "GUI H"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.toGuest = "HideApplication"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.mappingKey = "10"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.fromHost = "GUI M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.toGuest = "MinimizeWindow"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.mappingKey = "11"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.fromHost = "GUI Q"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.toGuest = "Quit"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.mappingKey = "12"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.toGuest = "ToggleHideMenu"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.mappingKey = "13"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.fromHost = "GUI E"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.toGuest = "Settings"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileKey = "52312ba5-d8c9-0a91-aaed-ebeb665caf10"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileName = "Windows 8 Profile"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileType = "windows8"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableOSShortcuts = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableKeyMappings = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.selectedLanguage = ""
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.cmdKeyFilterType = "none"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.maxMappings = "33"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.mappingKey = "0"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.fromHost = "GUI Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.mappingKey = "1"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.fromHost = "GUI X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.toGuest = "CONTROL X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.mappingKey = "2"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.fromHost = "GUI C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.toGuest = "CONTROL C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.mappingKey = "3"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.fromHost = "GUI V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.toGuest = "CONTROL V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.mappingKey = "4"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.fromHost = "GUI P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.toGuest = "CONTROL P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.mappingKey = "5"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.fromHost = "GUI A"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.toGuest = "CONTROL A"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.mappingKey = "6"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.fromHost = "GUI S"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.toGuest = "CONTROL S"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.mappingKey = "7"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.fromHost = "GUI F"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.toGuest = "0x03d"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.mappingKey = "8"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.fromHost = "GUI W"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.mappingKey = "9"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.mappingKey = "10"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.fromHost = "0x11c"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.toGuest = "0x138"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.mappingKey = "11"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.fromHost = "GUI SHIFT C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.toGuest = "GUI C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.mappingKey = "12"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.fromHost = "GUI SHIFT H"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.toGuest = "GUI H"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.mappingKey = "13"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.fromHost = "GUI SHIFT M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.toGuest = "GUI M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.mappingKey = "14"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.fromHost = "GUI SHIFT P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.toGuest = "GUI P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.mappingKey = "15"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.fromHost = "GUI SHIFT F"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.toGuest = "GUI Q"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.mappingKey = "16"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.fromHost = "GUI SHIFT V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.toGuest = "GUI V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.mappingKey = "17"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.fromHost = "GUI SHIFT W"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.toGuest = "GUI W"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.mappingKey = "18"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.fromHost = "GUI SHIFT X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.toGuest = "GUI X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.mappingKey = "19"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.fromHost = "GUI SHIFT Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.toGuest = "GUI Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.mappingKey = "20"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.fromHost = "Mouse1 Control"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.toGuest = "Mouse2"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.mappingKey = "21"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.fromHost = "Mouse1 GUI"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.toGuest = "Mouse3"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.mappingKey = "22"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.fromHost = "GUI CONTROL"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.toGuest = "Ungrab"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.mappingKey = "23"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.fromHost = "GUI CONTROL F"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.toGuest = "Fullscreen"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.mappingKey = "24"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.fromHost = "GUI SHIFT U"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.toGuest = "Unity"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.mappingKey = "25"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.fromHost = "GUI CONTROL S"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.toGuest = "SingleWindow"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.mappingKey = "26"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.fromHost = "GUI 0x029"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.toGuest = "CycleWindow"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.mappingKey = "27"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.toGuest = "CycleWindowReverse"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.mappingKey = "28"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.fromHost = "GUI H"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.toGuest = "HideApplication"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.mappingKey = "29"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.fromHost = "GUI M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.toGuest = "MinimizeWindow"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.mappingKey = "30"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.fromHost = "GUI Q"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.toGuest = "Quit"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.mappingKey = "31"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.toGuest = "ToggleHideMenu"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.mappingKey = "32"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.fromHost = "GUI E"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.toGuest = "Settings"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileName = "Profile"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileType = "standard"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableOSShortcuts = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableKeyMappings = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.selectedLanguage = ""
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.cmdKeyFilterType = "none"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.maxMappings = "25"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.mappingKey = "0"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.fromHost = "GUI Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.mappingKey = "1"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.fromHost = "GUI X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.toGuest = "CONTROL X"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.mappingKey = "2"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.fromHost = "GUI C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.toGuest = "CONTROL C"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.mappingKey = "3"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.fromHost = "GUI V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.toGuest = "CONTROL V"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.mappingKey = "4"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.fromHost = "GUI P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.toGuest = "CONTROL P"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.mappingKey = "5"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.fromHost = "GUI A"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.toGuest = "CONTROL A"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.mappingKey = "6"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.fromHost = "GUI S"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.toGuest = "CONTROL S"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.mappingKey = "7"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.fromHost = "GUI F"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.toGuest = "0x03d"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.mappingKey = "8"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.fromHost = "GUI W"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.mappingKey = "9"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.mappingKey = "10"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.enabled = "FALSE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.fromHost = "0x11c"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.toGuest = "0x138"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.mappingKey = "11"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.enabled = "TRUE"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.fromHost = "Mouse1 Control"
2025-05-02T06:21:29.404Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.toGuest = "Mouse2"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.mappingKey = "12"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.fromHost = "Mouse1 GUI"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.toGuest = "Mouse3"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.mappingKey = "13"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.fromHost = "GUI CONTROL"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.toGuest = "Ungrab"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.mappingKey = "14"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.fromHost = "GUI CONTROL F"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.toGuest = "Fullscreen"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.mappingKey = "15"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.fromHost = "GUI CONTROL U"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.toGuest = "Unity"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.mappingKey = "16"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.fromHost = "GUI SHIFT U"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.toGuest = "Unity"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.mappingKey = "17"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.fromHost = "GUI CONTROL S"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.toGuest = "SingleWindow"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.mappingKey = "18"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.fromHost = "GUI 0x029"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.toGuest = "CycleWindow"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.mappingKey = "19"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.toGuest = "CycleWindowReverse"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.mappingKey = "20"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.fromHost = "GUI H"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.toGuest = "HideApplication"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.mappingKey = "21"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.fromHost = "GUI M"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.toGuest = "MinimizeWindow"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.mappingKey = "22"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.fromHost = "GUI Q"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.toGuest = "Quit"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.mappingKey = "23"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.enabled = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.fromHost = "GUI SHIFT M"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.toGuest = "ToggleHideMenu"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.mappingKey = "24"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.enabled = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.fromHost = "GUI E"
2025-05-02T06:21:29.405Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.toGuest = "Settings"
2025-05-02T06:21:29.405Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1746162507"
2025-05-02T06:21:29.405Z In(05) vmx DICT         vmWizard.guestKey = "ubuntu-64"
2025-05-02T06:21:29.405Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT  hint.confirmLaxOVFImport = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT    hint.dui.poweroff.soft = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT hint.loader.mitigations.wsAndFusion = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT         hint.vmui.restart = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT --- USER DEFAULTS /Users/<USER>/Library/Preferences/VMware Fusion/config
2025-05-02T06:21:29.405Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config
2025-05-02T06:21:29.405Z In(05) vmx DICT --- SITE DEFAULTS /Applications/VMware Fusion.app/Contents/Library/config
2025-05-02T06:21:29.405Z In(05) vmx DICT --- NONPERSISTENT
2025-05-02T06:21:29.405Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT --- COMMAND LINE
2025-05-02T06:21:29.405Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT --- RECORDING
2025-05-02T06:21:29.405Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT --- CONFIGURATION /Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Ubuntu 64-bit 22.04.3 2.vmx 
2025-05-02T06:21:29.405Z In(05) vmx DICT            config.version = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT         virtualHW.version = "21"
2025-05-02T06:21:29.405Z In(05) vmx DICT              mks.enable3d = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-05-02T06:21:29.405Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-05-02T06:21:29.405Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-05-02T06:21:29.405Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-05-02T06:21:29.405Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT                     nvram = "Ubuntu 64-bit 22.04.3 2.nvram"
2025-05-02T06:21:29.405Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-05-02T06:21:29.405Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-05-02T06:21:29.405Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-05-02T06:21:29.405Z In(05) vmx DICT         powerType.suspend = "soft"
2025-05-02T06:21:29.405Z In(05) vmx DICT           powerType.reset = "soft"
2025-05-02T06:21:29.405Z In(05) vmx DICT               displayName = "Ubuntu 64-bit 22.04.3"
2025-05-02T06:21:29.405Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT                   guestOS = "ubuntu-64"
2025-05-02T06:21:29.405Z In(05) vmx DICT            tools.syncTime = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT      tools.upgrade.policy = "upgradeAtPowerCycle"
2025-05-02T06:21:29.405Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT            sound.fileName = "-1"
2025-05-02T06:21:29.405Z In(05) vmx DICT             sound.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT                  numvcpus = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT      cpuid.coresPerSocket = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT               vcpu.hotadd = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT                   memsize = "8192"
2025-05-02T06:21:29.405Z In(05) vmx DICT                mem.hotadd = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-05-02T06:21:29.405Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT             sata0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT          scsi0:0.fileName = "Virtual Disk.vmdk"
2025-05-02T06:21:29.405Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        sata0:1.deviceType = "cdrom-image"
2025-05-02T06:21:29.405Z In(05) vmx DICT          sata0:1.fileName = "/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso"
2025-05-02T06:21:29.405Z In(05) vmx DICT           sata0:1.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        sata0:0.deviceType = "cdrom-image"
2025-05-02T06:21:29.405Z In(05) vmx DICT          sata0:0.fileName = "autoinst.iso"
2025-05-02T06:21:29.405Z In(05) vmx DICT           sata0:0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT               usb.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT              ehci.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     svga.graphicsMemoryKB = "8388608"
2025-05-02T06:21:29.405Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2025-05-02T06:21:29.405Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-05-02T06:21:29.405Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2025-05-02T06:21:29.405Z In(05) vmx DICT ethernet0.linkStatePropagation.enable = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT          floppy0.fileType = "file"
2025-05-02T06:21:29.405Z In(05) vmx DICT          floppy0.fileName = "autoinst.flp"
2025-05-02T06:21:29.405Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        extendedConfigFile = "Ubuntu 64-bit 22.04.3 2.vmxf"
2025-05-02T06:21:29.405Z In(05) vmx DICT      floppy0.clientDevice = "FALSE"
2025-05-02T06:21:29.405Z In(05) vmx DICT         vmxstats.filename = "Ubuntu 64-bit 22.04.3 2.scoreboard"
2025-05-02T06:21:29.405Z In(05) vmx DICT                 uuid.bios = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
2025-05-02T06:21:29.405Z In(05) vmx DICT             uuid.location = "56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11"
2025-05-02T06:21:29.405Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-05-02T06:21:29.405Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-05-02T06:21:29.405Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-05-02T06:21:29.405Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-05-02T06:21:29.405Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-05-02T06:21:29.405Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-05-02T06:21:29.405Z In(05) vmx DICT         usb.pciSlotNumber = "32"
2025-05-02T06:21:29.405Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-05-02T06:21:29.405Z In(05) vmx DICT       sound.pciSlotNumber = "34"
2025-05-02T06:21:29.405Z In(05) vmx DICT        ehci.pciSlotNumber = "35"
2025-05-02T06:21:29.405Z In(05) vmx DICT       sata0.pciSlotNumber = "36"
2025-05-02T06:21:29.405Z In(05) vmx DICT              scsi0:0.redo = ""
2025-05-02T06:21:29.405Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-05-02T06:21:29.405Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:21:29.405Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:21:29.405Z In(05) vmx DICT   vmotion.svga.supports3D = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.baseCapsLevel = "9"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.maxPointSize = "189"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.maxTextureSize = "16384"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.maxVolumeExtent = "2048"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.maxTextureAnisotropy = "16"
2025-05-02T06:21:29.405Z In(05) vmx DICT  vmotion.svga.lineStipple = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.dxMaxConstantBuffers = "15"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.dxProvokingVertex = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT         vmotion.svga.sm41 = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.multisample2x = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.multisample4x = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.msFullQuality = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT     vmotion.svga.logicOps = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT         vmotion.svga.bc67 = "9"
2025-05-02T06:21:29.405Z In(05) vmx DICT          vmotion.svga.sm5 = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.multisample8x = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.logicBlendOps = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT vmotion.svga.maxForcedSampleCount = "8"
2025-05-02T06:21:29.405Z In(05) vmx DICT         vmotion.svga.gl43 = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:99:78:11"
2025-05-02T06:21:29.405Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT                  vmci0.id = "1620670481"
2025-05-02T06:21:29.405Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2025-05-02T06:21:29.405Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT              softPowerOff = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT               usb:1.speed = "2"
2025-05-02T06:21:29.405Z In(05) vmx DICT             usb:1.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT          usb:1.deviceType = "hub"
2025-05-02T06:21:29.405Z In(05) vmx DICT                usb:1.port = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT              usb:1.parent = "-1"
2025-05-02T06:21:29.405Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT   guestInfo.detailed.data = <not printed>
2025-05-02T06:21:29.405Z In(05) vmx DICT   keyboardAndMouseProfile = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:21:29.405Z In(05) vmx DICT gui.fitGuestUsingNativeDisplayResolution = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT    chipset.useAcpiBattery = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT     chipset.useApmBattery = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT toolsInstallManager.updateCounter = "1"
2025-05-02T06:21:29.405Z In(05) vmx DICT tools.capability.verifiedSamlToken = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT        checkpoint.vmState = ""
2025-05-02T06:21:29.405Z In(05) vmx DICT             usb:0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT          usb:0.deviceType = "hid"
2025-05-02T06:21:29.405Z In(05) vmx DICT                usb:0.port = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT              usb:0.parent = "-1"
2025-05-02T06:21:29.405Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2025-05-02T06:21:29.405Z In(05) vmx DICT     gui.viewModeAtPowerOn = "fullscreen"
2025-05-02T06:21:29.405Z In(05) vmx DICT            ehci:0.present = "TRUE"
2025-05-02T06:21:29.405Z In(05) vmx DICT         ehci:0.deviceType = "video"
2025-05-02T06:21:29.405Z In(05) vmx DICT               ehci:0.port = "0"
2025-05-02T06:21:29.405Z In(05) vmx DICT             ehci:0.parent = "-1"
2025-05-02T06:21:29.406Z In(05) vmx DICT --- USER DEFAULTS ~/Library/Preferences/VMware Fusion/config 
2025-05-02T06:21:29.406Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config 
2025-05-02T06:21:29.406Z In(05) vmx DICT --- SITE DEFAULTS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config 
2025-05-02T06:21:29.406Z In(05) vmx DICT --- GLOBAL SETTINGS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings 
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:21:29.406Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:21:29.406Z In(05) vmx Powering on guestOS 'ubuntu-64' using the configuration for 'ubuntu-64'.
2025-05-02T06:21:29.406Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:21:29.406Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:21:29.407Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:21:29.407Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:21:29.407Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:21:29.407Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:21:29.408Z In(05) vmx Monitor Mode: ULM
2025-05-02T06:21:29.408Z In(05) vmx MsgHint: msg.loader.mitigations.wsAndFusion
2025-05-02T06:21:29.408Z In(05)+ vmx You are running this virtual machine with side channel mitigations enabled. Side channel mitigations provide enhanced security but also lower performance.
2025-05-02T06:21:29.408Z In(05)+ vmx 
2025-05-02T06:21:29.408Z In(05)+ vmx To disable mitigations, change the side channel mitigations setting in the advanced panel of the virtual machine settings. Refer to VMware KB article 79832 at https://kb.vmware.com/s/article/79832 for more details.
2025-05-02T06:21:29.408Z In(05)+ vmx ---------------------------------------
2025-05-02T06:21:29.421Z In(05) vmx OvhdMem_PowerOn: initial admission: paged  4145028 nonpaged     5487 anonymous    15788
2025-05-02T06:21:29.421Z In(05) vmx VMMEM: Initial Reservation: 16274MB (MainMem=8192MB)
2025-05-02T06:21:29.421Z In(05) vmx numa: Hot-add is enabled and vNUMA hot-add is disabled, forcing UMA.
2025-05-02T06:21:29.422Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-05-02T06:21:29.422Z In(05) vmx llc: vLLC size: 1
2025-05-02T06:21:29.422Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 419430 (80% of min required size 524288)
2025-05-02T06:21:29.422Z In(05) PowerNotifyThread VTHREAD 123145554243584 "PowerNotifyThread" tid 181700
2025-05-02T06:21:29.422Z In(05) PowerNotifyThread PowerNotify thread is alive.
2025-05-02T06:21:29.422Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2025-05-02T06:21:29.422Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2025-05-02T06:21:29.422Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2025-05-02T06:21:29.422Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2025-05-02T06:21:29.422Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2025-05-02T06:21:29.422Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Fusion)
2025-05-02T06:21:29.423Z In(05) vthread-181701 VTHREAD 123145554780160 "vthread-181701" tid 181701
2025-05-02T06:21:29.423Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:21:29.423Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:21:29.423Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2025-05-02T06:21:29.424Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu-64' (refreshCount=1, lastCount=1).
2025-05-02T06:21:29.424Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:21:29.424Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:21:29.424Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:21:29.424Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:21:29.425Z In(05) deviceThread VTHREAD 123145555316736 "deviceThread" tid 181702
2025-05-02T06:21:29.425Z In(05) deviceThread Device thread is alive
2025-05-02T06:21:29.425Z In(05) vmx Host VT-x Capabilities:
2025-05-02T06:21:29.425Z In(05) vmx Basic VMX Information (0x00da040000000004)
2025-05-02T06:21:29.425Z In(05) vmx   VMCS revision ID                           4
2025-05-02T06:21:29.425Z In(05) vmx   VMCS region length                      1024
2025-05-02T06:21:29.425Z In(05) vmx   VMX physical-address width           natural
2025-05-02T06:21:29.425Z In(05) vmx   SMM dual-monitor mode                    yes
2025-05-02T06:21:29.425Z In(05) vmx   VMCS memory type                          WB
2025-05-02T06:21:29.425Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-05-02T06:21:29.425Z In(05) vmx   True VMX MSRs                            yes
2025-05-02T06:21:29.425Z In(05) vmx   Exception Injection ignores error code    no
2025-05-02T06:21:29.425Z In(05) vmx True Pin-Based VM-Execution Controls (0x0000007f00000016)
2025-05-02T06:21:29.425Z In(05) vmx   External-interrupt exiting               {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   NMI exiting                              {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Virtual NMIs                             {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Process posted interrupts                { 0 }
2025-05-02T06:21:29.425Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2025-05-02T06:21:29.425Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   HLT exiting                              {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   INVLPG exiting                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   MWAIT exiting                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   RDPMC exiting                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   RDTSC exiting                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   CR3-load exiting                         {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   CR3-store exiting                        {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Activate tertiary controls               { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   CR8-load exiting                         {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   CR8-store exiting                        {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Use TPR shadow                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   NMI-window exiting                       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Monitor trap flag                        {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   MONITOR exiting                          {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   PAUSE exiting                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Activate secondary controls              {0,1}
2025-05-02T06:21:29.425Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x00515cef000000a2)
2025-05-02T06:21:29.425Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Enable EPT                               { 1 }
2025-05-02T06:21:29.425Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Virtualize x2APIC mode                   { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Enable VPID                              { 1 }
2025-05-02T06:21:29.425Z In(05) vmx   WBINVD exiting                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Unrestricted guest                       { 1 }
2025-05-02T06:21:29.425Z In(05) vmx   APIC-register virtualization             { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Virtual-interrupt delivery               { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   RDRAND exiting                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Enable INVPCID                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Enable VM Functions                      { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Use VMCS shadowing                       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   ENCLS exiting                            { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   RDSEED exiting                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Enable PML                               { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   EPT-violation #VE                        { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Conceal VMX from PT                      { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   PASID translation                        { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   PT uses guest physical addresses         { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Use TSC scaling                          { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Bus lock exiting                         { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Notification VM exits                    { 0 }
2025-05-02T06:21:29.425Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2025-05-02T06:21:29.425Z In(05) vmx   LOADIWKEY exiting                          no
2025-05-02T06:21:29.425Z In(05) vmx   Enable HLAT                                no
2025-05-02T06:21:29.425Z In(05) vmx   Enable Paging-Write                        no
2025-05-02T06:21:29.425Z In(05) vmx   Enable Guest Paging Verification           no
2025-05-02T06:21:29.425Z In(05) vmx   Enable IPI Virtualization                  no
2025-05-02T06:21:29.425Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-05-02T06:21:29.425Z In(05) vmx True VM-Exit Controls (0x01ffffff00036dfb)
2025-05-02T06:21:29.425Z In(05) vmx   Save debug controls                      {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Host address-space size                  {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Save VMX-preemption timer                {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Clear IA32_BNDCFGS                       {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Clear IA32_RTIT MSR                      { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:21:29.425Z In(05) vmx True VM-Entry Controls (0x0003ffff000011fb)
2025-05-02T06:21:29.425Z In(05) vmx   Load debug controls                      {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Entry to SMM                             {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_BNDCFGS                        {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_RTIT MSR                       { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2025-05-02T06:21:29.425Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:21:29.425Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2025-05-02T06:21:29.425Z In(05) vmx   R=0/W=0/X=1                               yes
2025-05-02T06:21:29.425Z In(05) vmx   Page-walk length 3                        yes
2025-05-02T06:21:29.425Z In(05) vmx   EPT memory type WB                        yes
2025-05-02T06:21:29.425Z In(05) vmx   2MB super-page                            yes
2025-05-02T06:21:29.425Z In(05) vmx   1GB super-page                            yes
2025-05-02T06:21:29.425Z In(05) vmx   INVEPT support                            yes
2025-05-02T06:21:29.425Z In(05) vmx   Access & Dirty Bits                       yes
2025-05-02T06:21:29.425Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2025-05-02T06:21:29.425Z In(05) vmx   Supervisor shadow-stack control            no
2025-05-02T06:21:29.425Z In(05) vmx   Type 1 INVEPT                             yes
2025-05-02T06:21:29.425Z In(05) vmx   Type 2 INVEPT                             yes
2025-05-02T06:21:29.425Z In(05) vmx   INVVPID support                           yes
2025-05-02T06:21:29.425Z In(05) vmx   Type 0 INVVPID                            yes
2025-05-02T06:21:29.425Z In(05) vmx   Type 1 INVVPID                            yes
2025-05-02T06:21:29.425Z In(05) vmx   Type 2 INVVPID                            yes
2025-05-02T06:21:29.425Z In(05) vmx   Type 3 INVVPID                            yes
2025-05-02T06:21:29.425Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2025-05-02T06:21:29.425Z In(05) vmx   TSC to preemption timer ratio      7
2025-05-02T06:21:29.425Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-05-02T06:21:29.425Z In(05) vmx   Activity State HLT               yes
2025-05-02T06:21:29.425Z In(05) vmx   Activity State shutdown          yes
2025-05-02T06:21:29.425Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-05-02T06:21:29.425Z In(05) vmx   Processor trace in VMX           yes
2025-05-02T06:21:29.425Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2025-05-02T06:21:29.425Z In(05) vmx   CR3 targets supported              4
2025-05-02T06:21:29.425Z In(05) vmx   Maximum MSR list size            512
2025-05-02T06:21:29.425Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2025-05-02T06:21:29.425Z In(05) vmx   Allow all VMWRITEs               yes
2025-05-02T06:21:29.426Z In(05) vmx   Allow zero instruction length    yes
2025-05-02T06:21:29.426Z In(05) vmx   MSEG revision ID                   0
2025-05-02T06:21:29.426Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-05-02T06:21:29.426Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-05-02T06:21:29.426Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-05-02T06:21:29.426Z In(05) vmx   Variable          0x000000007fffffde
2025-05-02T06:21:29.426Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x00000000003767ff)
2025-05-02T06:21:29.426Z In(05) vmx   Fixed to 0        0xffffffffffc89800
2025-05-02T06:21:29.426Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-05-02T06:21:29.426Z In(05) vmx   Variable          0x00000000003747ff
2025-05-02T06:21:29.426Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2025-05-02T06:21:29.426Z In(05) vmx   Highest index                   0x17
2025-05-02T06:21:29.426Z In(05) vmx VM Functions (0x0000000000000000)
2025-05-02T06:21:29.426Z In(05) vmx KHZEstimate 2592000
2025-05-02T06:21:29.426Z In(05) vmx MHZEstimate 2592
2025-05-02T06:21:29.426Z In(05) vmx NumVCPUs 8
2025-05-02T06:21:29.426Z In(05) vmx AIOGNRC: numThreads=17 ide=0, scsi=1, passthru=0
2025-05-02T06:21:29.426Z In(05) vmx WORKER: Creating new group with maxThreads=17 (17)
2025-05-02T06:21:29.430Z In(05) vmx WORKER: Creating new group with maxThreads=1 (18)
2025-05-02T06:21:29.430Z In(05) vmx MainMem: CPT Host WZ=0 PF=8192 D=0
2025-05-02T06:21:29.430Z In(05) vmx MainMem: CPT PLS=1 PLR=0 BS=1 BlkP=32 Mult=4 W=50
2025-05-02T06:21:29.430Z In(05) vmx MainMem: Opened paging file, 'UNNAMED' (swap).
2025-05-02T06:21:29.430Z In(05) vmx MStat: Creating Stat vm.uptime
2025-05-02T06:21:29.430Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-05-02T06:21:29.430Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-05-02T06:21:29.430Z In(05) vmx VMXAIOMGR: Using: simple=Generic
2025-05-02T06:21:29.432Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2025-05-02T06:21:29.432Z In(05) machPoll VTHREAD 123145555853312 "machPoll" tid 181703
2025-05-02T06:21:29.434Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2025-05-02T06:21:29.434Z In(05) vmx WORKER: Creating new group with maxThreads=14 (34)
2025-05-02T06:21:29.435Z In(05) vmx FeatureCompat: No VM masks.
2025-05-02T06:21:29.435Z In(05) vmx TimeTracker host to guest rate conversion 241716210 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:21:29.435Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -241716210
2025-05-02T06:21:29.435Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-02T06:21:29.435Z In(05) vmx TSC offsetting enabled.
2025-05-02T06:21:29.435Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-02T06:21:29.435Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-02T06:21:29.435Z In(05) vmx MKS PowerOn
2025-05-02T06:21:29.437Z In(05) mks VTHREAD 123145557962752 "mks" tid 181704
2025-05-02T06:21:29.437Z In(05) mks MKS thread is alive
2025-05-02T06:21:29.437Z In(05) svga VTHREAD 123145566363648 "svga" tid 181705
2025-05-02T06:21:29.437Z In(05) svga SVGA thread is alive
2025-05-02T06:21:29.438Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-05-02T06:21:29.469Z In(05) mks MKS MacOSMouse: Using event tap path.
2025-05-02T06:21:29.469Z In(05) mouse VTHREAD 123145566900224 "mouse" tid 181709
2025-05-02T06:21:29.469Z In(05) keyboard VTHREAD 123145567436800 "keyboard" tid 181710
2025-05-02T06:21:29.532Z In(05) keyboard MKS MacOSKeyboard: Adding device: (0) VID:05AC PID:027C Apple Inc. (Apple Internal Keyboard / Trackpad)
2025-05-02T06:21:29.536Z In(05) keyboard MKS MacOSKeyboard: Adding device: (1) VID:05AC PID:8600 <missing> (TouchBarUserDevice)
2025-05-02T06:21:29.536Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps MTLRenderer ISBRenderer 
2025-05-02T06:21:29.536Z In(05) mks MKS-RenderMain: ISB enabled by config
2025-05-02T06:21:29.536Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from ISBRenderer
2025-05-02T06:21:29.536Z In(05) mks MKS-RenderMain: Starting ISBRenderer
2025-05-02T06:21:29.536Z In(05) mks ISBRendererComm: ISBRendererComm DataChannel size=4294967296
2025-05-02T06:21:29.537Z In(05) mks LocalMKS: MKS_UUID=52 09 ca 06 a0 94 eb 21-dd 19 cf 18 16 11 79 42
2025-05-02T06:21:29.538Z In(05) mks LogRotation: Rotating to a new log file (keepOld 3) took 0.001256 seconds.
2025-05-02T06:21:29.539Z In(05) mks ISBRendererComm: mksSandbox command-line: /Applications/VMware Fusion.app/Contents/Library/mksSandbox --pipeInfo 3
2025-05-02T06:21:29.554Z In(05) mks ISBRendererComm: Spawned process with pid 21234
2025-05-02T06:21:30.327Z In(05) mks ISBRendererComm: Sandbox Renderer: MTLRenderer
2025-05-02T06:21:30.332Z In(05) mks MKS-RenderMain: Started ISBRenderer with (MTLRenderer)
2025-05-02T06:21:30.332Z In(05) mks MKS-RenderMain: Found Full Renderer: ISBRenderer (MTLRenderer)
2025-05-02T06:21:30.332Z In(05) mks MKS-RenderMain: maxTextureSize=16384
2025-05-02T06:21:30.333Z In(05) mks SOCKET 2 (87) creating new listening socket on port -1
2025-05-02T06:21:30.333Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-05-02T06:21:30.334Z In(05) mks MKSRemoteMgr: Set default display name: Ubuntu 64-bit 22.04.3
2025-05-02T06:21:30.334Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-05-02T06:21:30.334Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-05-02T06:21:30.334Z No(00) vmx PowerOnTiming: Module MKS took 898140 us
2025-05-02T06:21:30.334Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-05-02T06:21:30.334Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-05-02T06:21:30.334Z In(05) vmx Chipset version: 0x17
2025-05-02T06:21:30.517Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-05-02T06:21:30.517Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2025-05-02T06:21:30.517Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2025-05-02T06:21:30.517Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2025-05-02T06:21:30.518Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-05-02T06:21:30.518Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2025-05-02T06:21:30.518Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2025-05-02T06:21:30.518Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2025-05-02T06:21:30.519Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2025-05-02T06:21:30.519Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2025-05-02T06:21:30.526Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-05-02T06:21:30.526Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-05-02T06:21:30.527Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2025-05-02T06:21:30.527Z In(05) vmx DISK: OPEN scsi0:0 '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' persistent R[]
2025-05-02T06:21:30.529Z In(05) vmx Current OS Release is 24.5.0
2025-05-02T06:21:30.534Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2025-05-02T06:21:30.534Z In(05) vmx DISK: Disk '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' has UUID '60 00 c2 90 cf 53 1e 92-6a 2b b7 7a 00 db 66 0e'
2025-05-02T06:21:30.534Z In(05) vmx DISK: OPEN '/Users/<USER>/Virtual Machines.localized/Ubuntu 64-bit 22.04.3 2.vmwarevm/Virtual Disk.vmdk' Geo (117487/255/63) BIOS Geo (0/0/0)
2025-05-02T06:21:30.535Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'scsi0:0' identified as Virtual SSD device.
2025-05-02T06:21:30.535Z In(05) vmx DISK: Opening disks took 8 ms.
2025-05-02T06:21:30.535Z In(05) vmx USBArbLib: USBArbLib initialized successfully, retryIntervalStart(5), retryIntervalMax(120), arbSocketName(/var/run/vmware/usbarb-socket), useLocking(yes), tryUpgrading(no).
2025-05-02T06:21:30.535Z In(05) vmx UsbEnum: Initializing UsbEnum library, disableLocking(no), allowBootableHid(yes).
2025-05-02T06:21:30.535Z In(05) vmx SOCKET creating new socket, connecting to /var/run/vmware/usbarb-socket
2025-05-02T06:21:30.535Z In(05) vmx USB: Initializing 'Virtual Hub' backend
2025-05-02T06:21:30.535Z In(05) vmx USB: Initializing 'Generic' backend
2025-05-02T06:21:30.536Z Wa(03) vmx USBArbLib: OUT SET_AUTO_CONNECT: Not connected to arbitrator, autoconnect(0) for client 'Ubuntu 64-bit 22.04.3', connectState(1).
2025-05-02T06:21:30.536Z In(05) vmx USB: Initializing 'Virtual HID' backend
2025-05-02T06:21:30.536Z In(05) vmx USB: Initializing 'Virtual Mass Storage' backend
2025-05-02T06:21:30.536Z In(05) vmx USB: Initializing 'Virtual RNG' backend
2025-05-02T06:21:30.536Z In(05) vmx USB: Initializing 'Virtual CCID' backend
2025-05-02T06:21:30.536Z In(05) vmx USB-CCID:  dlopened /System/Library/Frameworks/PCSC.framework/PCSC.
2025-05-02T06:21:30.554Z In(05) usbCCIDEnumCards VTHREAD 123145569583104 "usbCCIDEnumCards" tid 181807
2025-05-02T06:21:30.554Z In(05) vmx USB: Initializing 'Virtual Bluetooth' backend
2025-05-02T06:21:30.554Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread created.
2025-05-02T06:21:30.554Z In(05) vmx USB: Initializing 'Virtual Audio' backend
2025-05-02T06:21:30.554Z In(05) vmx USB: Initializing 'Virtual Video' backend
2025-05-02T06:21:30.558Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:21:30.558Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:21:30.558Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:21:30.558Z Wa(03) vmx USBGM: Skipping disk exclusion for volume: '/dev/disk1s1', type: 'PCI-Express'.
2025-05-02T06:21:30.560Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2025-05-02T06:21:30.560Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2025-05-02T06:21:30.560Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2025-05-02T06:21:30.561Z In(05) vmx DISKUTIL: scsi0:0 : capacity=1887436800 logical sector size=512
2025-05-02T06:21:30.561Z In(05) vmx DISKUTIL: scsi0:0 : geometry=117487/255/63
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 4194304
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=4194304 bytes, max wh(1176, 885)
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Max wh(1176, 885), number of displays: 10
2025-05-02T06:21:30.561Z In(05) vmx SVGA-GFB: Allocated gfbSize=4194304
2025-05-02T06:21:30.561Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "4194304"
2025-05-02T06:21:30.561Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:21:30.561Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2025-05-02T06:21:30.561Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:21:30.561Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:21:30.561Z In(05) vmx SVGA: mobMaxSize=1073741824
2025-05-02T06:21:30.561Z In(05) vmx SVGA: graphicsMemoryKB=8388608
2025-05-02T06:21:30.561Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 511
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 16384
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 9
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 1
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:21:30.561Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 189
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 16384
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host id: 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.supports3D bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.baseCapsLevel num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.maxPointSize num 189
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.maxTextureSize num 16384
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.maxVolumeExtent num 2048
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.maxTextureAnisotropy num 16
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.lineStipple bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.dxMaxConstantBuffers num 15
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.dxProvokingVertex bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.sm41 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.multisample2x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.multisample4x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.msFullQuality bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.logicOps bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.bc67 num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.sm5 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.multisample8x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.logicBlendOps bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.maxForcedSampleCount num 8
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature    host svga.gl43 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost id: 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.supports3D bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.baseCapsLevel num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.maxPointSize num 189
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.maxTextureSize num 16384
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.maxVolumeExtent num 2048
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.maxTextureAnisotropy num 16
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.lineStipple bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.dxMaxConstantBuffers num 15
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.dxProvokingVertex bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.sm41 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.multisample2x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.multisample4x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.msFullQuality bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.logicOps bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.bc67 num 9
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.sm5 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.multisample8x bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.logicBlendOps bool 0
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.maxForcedSampleCount num 8
2025-05-02T06:21:30.562Z In(05) vmx SVGAFeature evcHost svga.gl43 bool 1
2025-05-02T06:21:30.562Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:21:30.562Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:21:30.562Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:21:30.562Z In(05) vmx SVGA3dCaps: host, at power on (3d enabled)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:21:30.562Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:21:30.563Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:21:30.563Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:21:30.563Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:21:30.563Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:21:30.563Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:21:30.563Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:21:30.563Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:21:30.563Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:21:30.563Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:21:30.563Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:21:30.563Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:21:30.563Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:21:30.563Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:21:30.563Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:21:30.563Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:21:30.563Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:21:30.563Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:21:30.563Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:21:30.563Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:21:30.563Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:21:30.563Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:21:30.563Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:21:30.563Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:21:30.563Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:21:30.563Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:21:30.563Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:21:30.563Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:21:30.563Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:21:30.563Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:21:30.563Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:21:30.563Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:21:30.563Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:21:30.564Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:21:30.564Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.supports3D = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.baseCapsLevel = "9"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.maxPointSize = "189"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.maxTextureSize = "16384"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.maxVolumeExtent = "2048"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.maxTextureAnisotropy = "16"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.lineStipple = "0"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.dxMaxConstantBuffers = "15"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.dxProvokingVertex = "0"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.sm41 = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample2x = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample4x = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.msFullQuality = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.logicOps = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.bc67 = "9"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.sm5 = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.multisample8x = "1"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.logicBlendOps = "0"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.maxForcedSampleCount = "8"
2025-05-02T06:21:30.564Z No(00) vmx ConfigDB: Setting vmotion.svga.gl43 = "1"
2025-05-02T06:21:30.564Z In(05) vmx SVGA3dCaps: guest, compatibility level: 10
2025-05-02T06:21:30.564Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:21:30.564Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:21:30.564Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:21:30.564Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:21:30.564Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:21:30.564Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:21:30.564Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:21:30.564Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:21:30.564Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:21:30.564Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:21:30.564Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:21:30.564Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:21:30.564Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:21:30.564Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:21:30.564Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:21:30.564Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:21:30.564Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:21:30.565Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:21:30.565Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:21:30.565Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:21:30.565Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:21:30.565Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:21:30.565Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:21:30.565Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:21:30.565Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:21:30.565Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:21:30.565Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:21:30.565Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:21:30.565Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:21:30.565Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:21:30.565Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:21:30.565Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:21:30.565Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:21:30.565Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:21:30.565Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:21:30.565Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:21:30.565Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:21:30.565Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:21:30.565Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:21:30.565Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:21:30.565Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     9 (    9,     9)
2025-05-02T06:21:30.565Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:21:30.565Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:    10 (    1,    10)
2025-05-02T06:21:30.567Z In(05) vmx USB: Initializing 'UHCI' host controller.
2025-05-02T06:21:30.567Z In(05) vmx USB: PowerOnCreateDevice 'usb:0' #0, found port 7FA1D7887A90.
2025-05-02T06:21:30.567Z No(00) vmx ConfigDB: Setting usb:0.present = "TRUE"
2025-05-02T06:21:30.567Z No(00) vmx ConfigDB: Setting usb:0.deviceType = "hid"
2025-05-02T06:21:30.567Z No(00) vmx ConfigDB: Setting usb:0.port = "0"
2025-05-02T06:21:30.567Z No(00) vmx ConfigDB: Setting usb:0.parent = "-1"
2025-05-02T06:21:30.572Z In(05) vmx USB: PowerOnCreateDevice 'usb:1' #1, found port 7FA1D7887AC8.
2025-05-02T06:21:30.572Z No(00) vmx ConfigDB: Setting usb:1.speed = "2"
2025-05-02T06:21:30.574Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:99:78:11
2025-05-02T06:21:30.575Z In(05) vmx USB: Initializing 'EHCI' host controller.
2025-05-02T06:21:30.575Z In(05) vmx USB: PowerOnCreateDevice 'ehci:0' #0, found port 7FA1D6054D90.
2025-05-02T06:21:30.575Z No(00) vmx ConfigDB: Setting ehci:0.present = "TRUE"
2025-05-02T06:21:30.575Z No(00) vmx ConfigDB: Setting ehci:0.deviceType = "video"
2025-05-02T06:21:30.575Z No(00) vmx ConfigDB: Setting ehci:0.port = "0"
2025-05-02T06:21:30.575Z No(00) vmx ConfigDB: Setting ehci:0.parent = "-1"
2025-05-02T06:21:30.580Z No(00) vmx ConfigDB: Setting vmci0.id = "1620670481"
2025-05-02T06:21:30.585Z In(05) vmx AHCI:Successfully created adapter 'sata0' as num 0
2025-05-02T06:21:30.585Z In(05) vmx SCSI DEVICE (sata0:0): Computed value of sata0:0.useBounceBuffers: default
2025-05-02T06:21:30.585Z In(05) vmx DISKUTIL: sata0:0 : capacity=0 logical sector size=2048
2025-05-02T06:21:30.585Z In(05) vmx DISKUTIL: sata0:0 : geometry=0/0/0
2025-05-02T06:21:30.585Z In(05) vmx AHCI:Creating ATAPI CDROM on SATA adapter.
2025-05-02T06:21:30.585Z In(05) vmx AHCI:Successfully created device: sata0:0
2025-05-02T06:21:30.585Z In(05) vmx SCSI DEVICE (sata0:1): Computed value of sata0:1.useBounceBuffers: default
2025-05-02T06:21:30.585Z In(05) vmx DISKUTIL: sata0:1 : capacity=0 logical sector size=2048
2025-05-02T06:21:30.585Z In(05) vmx DISKUTIL: sata0:1 : geometry=0/0/0
2025-05-02T06:21:30.585Z In(05) vmx AHCI:Creating ATAPI CDROM on SATA adapter.
2025-05-02T06:21:30.585Z In(05) vmx AHCI:Successfully created device: sata0:1
2025-05-02T06:21:30.588Z In(05) vmx WORKER: Creating new group with maxThreads=1 (35)
2025-05-02T06:21:30.588Z In(05) vmx DISKUTIL: scsi0:0 : max toolsVersion = 12389, type = 4
2025-05-02T06:21:30.588Z In(05) vmx TOOLS setting legacy tools version to '12389' type 4, manifest status is 9
2025-05-02T06:21:30.588Z In(05) worker-181701 ToolsVersionGetStatusWorkerThread: Tools status 8 derived from environment
2025-05-02T06:21:30.588Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2025-05-02T06:21:30.588Z In(05) vmx Tools: Delaying state change request to state 3.
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2025-05-02T06:21:30.588Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2025-05-02T06:21:30.588Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2025-05-02T06:21:30.588Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2025-05-02T06:21:30.589Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2025-05-02T06:21:30.589Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2025-05-02T06:21:30.589Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2025-05-02T06:21:30.592Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2025-05-02T06:21:30.592Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest codename: Coffee Lake-S/H
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000906ea 0x00010800 0xf7fa3203 0x0f8bfbff
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000906ea 0x00100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x009c27ab 0x00000000 0xbc000400
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x07300401 0x000000ff 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000000 0x00000001 0x00000201 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x0000000c 0x00000201 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000f 0x00000340 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000014,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000014,  0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000014,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000014,  1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x00278d00 0x000101d0 0x00000002 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.592Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:21:30.593Z In(05) vmx Minimum ucode level: 0x000000fa
2025-05-02T06:21:30.593Z In(05) vmx VPMC: events will use hybrid freeze.
2025-05-02T06:21:30.593Z In(05) vmx VPMC: gen counters: num 4 mask 0xffffffffffff
2025-05-02T06:21:30.593Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2025-05-02T06:21:30.593Z In(05) vmx VPMC: hardware counters: 0
2025-05-02T06:21:30.593Z In(05) vmx VPMC: perf capabilities: 0x2000
2025-05-02T06:21:30.593Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0xc
2025-05-02T06:21:30.593Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2025-05-02T06:21:30.593Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=4194304
2025-05-02T06:21:30.593Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=4194304
2025-05-02T06:21:30.593Z In(05) vmx SVGA: Final Device caps : 0xfdffc3e2
2025-05-02T06:21:30.593Z In(05) vmx SVGA: Final Device caps2: 0x0005efff
2025-05-02T06:21:30.593Z In(05) vmx PStrIntern expansion: nBkts=256
2025-05-02T06:21:30.594Z In(05) vmx FeatureCompat: Capabilities:
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.sse3 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.mwait = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.vmx = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.fma = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.pcid = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.sse41 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.sse42 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.movbe = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.popcnt = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.aes = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xsave = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.avx = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.f16c = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.rdrand = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ds = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ss = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.avx2 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.smep = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.invpcid = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.rdseed = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.adx = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.smap = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.mdclear = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.stibp = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.fcmd = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ssbd = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xsavec = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.xsaves = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.abm = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.nx = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.lm = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.intel = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ibrs = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: cpuid.ibpb = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: hv.capable = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.realmode = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.mbx = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.advexitinfo = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.eptad = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.ple = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.supports3d = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.basecapslevel = 9
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.maxpointsize = 0xbd
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.maxtexturesize = 0x4000
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.maxvolumeextent = 0x800
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.maxtextureanisotropy = 0x10
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.dxmaxconstantbuffers = 0xf
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.sm41 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.multisample2x = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.multisample4x = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.msfullquality = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.logicops = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.bc67 = 9
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.sm5 = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.multisample8x = 1
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.maxforcedsamplecount = 8
2025-05-02T06:21:30.594Z In(05) vmx Capability Found: svga0*svga.gl43 = 1
2025-05-02T06:21:30.594Z In(05) vmx FeatureCompat: Requirements:
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2025-05-02T06:21:30.594Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xgetbv_ecx1 - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.supports3d - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.basecapslevel - Num:Min:9
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.maxpointsize - Num:Min:0xbd
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.maxtexturesize - Num:Min:0x4000
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.maxvolumeextent - Num:Min:0x800
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.maxtextureanisotropy - Num:Min:0x10
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.dxmaxconstantbuffers - Num:Min:0xf
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.sm41 - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.multisample2x - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.multisample4x - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.msfullquality - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.logicops - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.bc67 - Num:Min:9
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.sm5 - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.multisample8x - Bool:Min:1
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.maxforcedsamplecount - Num:Min:8
2025-05-02T06:21:30.595Z In(05) vmx VM Features Required: svga*svga.gl43 - Bool:Min:1
2025-05-02T06:21:30.652Z In(05) ulm_exc VTHREAD 123145570119680 "ulm_exc" tid 181808
2025-05-02T06:21:30.652Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2025-05-02T06:21:30.653Z In(05) vmx 
2025-05-02T06:21:30.653Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2025-05-02T06:21:30.653Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-05-02T06:21:30.653Z In(05)+ vmx 
2025-05-02T06:21:30.653Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :      16     16      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       8      8      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-02T06:21:30.653Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   39936  39936      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Total paged                         :  4145028 4145028      - |   3517   3517      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Actual maximum                      :         4145028        |        4145028
2025-05-02T06:21:30.654Z In(05)+ vmx 
2025-05-02T06:21:30.654Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :      11     11      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      69     69      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    2170   3265      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_AHCIREGS                   :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_LBR                        :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_MonWired                   :     161    161      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Total nonpaged                      :    3944   5487      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Actual maximum                      :           3944        |          5487
2025-05-02T06:21:30.654Z In(05)+ vmx 
2025-05-02T06:21:30.654Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     784    784      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2114   2171      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :      32     32      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :     320    320      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_TC                          :    4104   4104      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :      16     16      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_HV                          :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       8      8      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_VHV                         :      24     24      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_Numa                        :      66     66      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :     232    232      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     887    887      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4386   4386      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     278    278      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :      13     13      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Total anonymous                     :   15731  15788      - |      0      0      -
2025-05-02T06:21:30.654Z In(05) vmx OvhdMem Actual maximum                      :          15731        |         15788
2025-05-02T06:21:30.654Z In(05)+ vmx 
2025-05-02T06:21:30.654Z In(05) vmx VMMEM: Precise Reservation: 16268MB (MainMem=8192MB)
2025-05-02T06:21:30.654Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2025-05-02T06:21:30.654Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2025-05-02T06:21:30.655Z No(00) vmx PowerOnTiming: ALL took 1254167 us
2025-05-02T06:21:30.655Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2025-05-02T06:21:30.655Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2025-05-02T06:21:30.655Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2025-05-02T06:21:30.655Z In(05) vcpu-0 VTHREAD 123145570656256 "vcpu-0" tid 181809
2025-05-02T06:21:30.656Z In(05) vmx ToolsISO: Refreshing imageName for 'ubuntu-64' (refreshCount=1, lastCount=1).
2025-05-02T06:21:30.656Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:21:30.656Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:21:30.656Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-02T06:21:30.656Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'ubuntu-64' guest.
2025-05-02T06:21:30.657Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2025-05-02T06:21:30.657Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'unmanaged', 'unmanaged', install possible
2025-05-02T06:21:30.657Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-1 VTHREAD 123145571192832 "vcpu-1" tid 181810
2025-05-02T06:21:30.658Z In(05) vcpu-2 VTHREAD 123145571729408 "vcpu-2" tid 181811
2025-05-02T06:21:30.658Z In(05) vcpu-1 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-3 VTHREAD 123145572265984 "vcpu-3" tid 181812
2025-05-02T06:21:30.658Z In(05) vcpu-2 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-4 VTHREAD 123145572802560 "vcpu-4" tid 181813
2025-05-02T06:21:30.658Z In(05) vcpu-3 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-5 VTHREAD 123145573339136 "vcpu-5" tid 181814
2025-05-02T06:21:30.658Z In(05) vcpu-4 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-5 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-6 VTHREAD 123145573875712 "vcpu-6" tid 181815
2025-05-02T06:21:30.658Z In(05) vcpu-7 VTHREAD 123145574412288 "vcpu-7" tid 181816
2025-05-02T06:21:30.658Z In(05) vcpu-6 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.658Z In(05) vcpu-7 CPU reset: hard (mode Emulation)
2025-05-02T06:21:30.659Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2025-05-02T06:21:30.659Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2025-05-02T06:21:30.659Z No(00) vcpu-0 ConfigDB: Unsetting all entries with prefix "usb:0."
2025-05-02T06:21:30.665Z In(05) vcpu-0 USB: DevID(2000000400000000): Disconnecting device.
2025-05-02T06:21:30.665Z In(05) vcpu-0 USB: DevID(200000050e0f0003): Connecting device desc:name:VMware\ Virtual\ USB\ Mouse vid:0e0f pid:0003 speed:full family:hid deviceType:virtual-hid info:0000005 version:5.
2025-05-02T06:21:30.665Z No(00) vcpu-0 ConfigDB: Setting usb:0.present = "TRUE"
2025-05-02T06:21:30.665Z No(00) vcpu-0 ConfigDB: Setting usb:0.deviceType = "hid"
2025-05-02T06:21:30.665Z No(00) vcpu-0 ConfigDB: Setting usb:0.port = "0"
2025-05-02T06:21:30.665Z No(00) vcpu-0 ConfigDB: Setting usb:0.parent = "-1"
2025-05-02T06:21:30.671Z No(00) vcpu-0 ConfigDB: Unsetting all entries with prefix "ehci:0."
2025-05-02T06:21:30.676Z In(05) vcpu-0 USB: DevID(800000010e0f000b): Disconnecting device.
2025-05-02T06:21:30.676Z In(05) vcpu-0 USB: DevID(800000010e0f000b): Connecting device desc:name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5.
2025-05-02T06:21:30.676Z In(05) vcpu-0 AVCAPTURE: AVCapture_OpenDevice: Choosing default parameters to create video device.
2025-05-02T06:21:30.676Z In(05) vcpu-0 AVCAPTURE: -[AVCaptureAVFDevice open:genericDevice:]: Capture device not specified, selecting a default video source.
2025-05-02T06:21:30.719Z In(05) vcpu-0 AVCAPTURE: AVCapture_OpenDevice: Device created but supported camera settings not found.
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: VUsbVideoCreateDevice:Camera device connected to the host does not support any known camera settings
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: Format 1:	 yuy2	 numFrames:2
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: Frame:1
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: dimensions:640x480
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:21:30.719Z In(05) vcpu-0 30.000030	
2025-05-02T06:21:30.719Z In(05) vcpu-0 29.000049	
2025-05-02T06:21:30.719Z In(05) vcpu-0 28.000067	
2025-05-02T06:21:30.719Z In(05) vcpu-0 27.000027	
2025-05-02T06:21:30.719Z In(05) vcpu-0 26.000026	
2025-05-02T06:21:30.719Z In(05) vcpu-0 25.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 24.000038	
2025-05-02T06:21:30.719Z In(05) vcpu-0 23.000032	
2025-05-02T06:21:30.719Z In(05) vcpu-0 22.000022	
2025-05-02T06:21:30.719Z In(05) vcpu-0 21.000021	
2025-05-02T06:21:30.719Z In(05) vcpu-0 20.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 19.000029	
2025-05-02T06:21:30.719Z In(05) vcpu-0 18.000018	
2025-05-02T06:21:30.719Z In(05) vcpu-0 17.000009	
2025-05-02T06:21:30.719Z In(05) vcpu-0 16.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 15.000015	
2025-05-02T06:21:30.719Z In(05) vcpu-0 14.000014	
2025-05-02T06:21:30.719Z In(05) vcpu-0 13.000013	
2025-05-02T06:21:30.719Z In(05) vcpu-0 12.000005	
2025-05-02T06:21:30.719Z In(05) vcpu-0 11.000011	
2025-05-02T06:21:30.719Z In(05) vcpu-0 10.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 9.000001	
2025-05-02T06:21:30.719Z In(05) vcpu-0 8.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 7.000002	
2025-05-02T06:21:30.719Z In(05) vcpu-0 6.000002	
2025-05-02T06:21:30.719Z In(05) vcpu-0 5.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 4.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 3.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 2.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 
2025-05-02T06:21:30.719Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.033333	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.034483	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.035714	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.037037	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.038462	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.040000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.041667	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.043478	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.045455	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.047619	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.050000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.052632	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.055556	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.058824	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.062500	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.066667	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.071429	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.076923	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.083333	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.090909	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.100000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.111111	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.125000	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.142857	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.166667	
2025-05-02T06:21:30.719Z In(05) vcpu-0 0.200000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.250000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.333333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.500000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame:2
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: dimensions:1280x720
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:21:30.720Z In(05) vcpu-0 13.000013	
2025-05-02T06:21:30.720Z In(05) vcpu-0 12.000005	
2025-05-02T06:21:30.720Z In(05) vcpu-0 11.000011	
2025-05-02T06:21:30.720Z In(05) vcpu-0 10.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 9.000001	
2025-05-02T06:21:30.720Z In(05) vcpu-0 8.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 7.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 6.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 5.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 4.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 3.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 2.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.076923	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.083333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.090909	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.100000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.111111	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.125000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.142857	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.166667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.200000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.250000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.333333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.500000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Format 2:	 nv12	 numFrames:2
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame:1
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: dimensions:640x480
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:21:30.720Z In(05) vcpu-0 30.000030	
2025-05-02T06:21:30.720Z In(05) vcpu-0 29.000049	
2025-05-02T06:21:30.720Z In(05) vcpu-0 28.000067	
2025-05-02T06:21:30.720Z In(05) vcpu-0 27.000027	
2025-05-02T06:21:30.720Z In(05) vcpu-0 26.000026	
2025-05-02T06:21:30.720Z In(05) vcpu-0 25.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 24.000038	
2025-05-02T06:21:30.720Z In(05) vcpu-0 23.000032	
2025-05-02T06:21:30.720Z In(05) vcpu-0 22.000022	
2025-05-02T06:21:30.720Z In(05) vcpu-0 21.000021	
2025-05-02T06:21:30.720Z In(05) vcpu-0 20.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 19.000029	
2025-05-02T06:21:30.720Z In(05) vcpu-0 18.000018	
2025-05-02T06:21:30.720Z In(05) vcpu-0 17.000009	
2025-05-02T06:21:30.720Z In(05) vcpu-0 16.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 15.000015	
2025-05-02T06:21:30.720Z In(05) vcpu-0 14.000014	
2025-05-02T06:21:30.720Z In(05) vcpu-0 13.000013	
2025-05-02T06:21:30.720Z In(05) vcpu-0 12.000005	
2025-05-02T06:21:30.720Z In(05) vcpu-0 11.000011	
2025-05-02T06:21:30.720Z In(05) vcpu-0 10.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 9.000001	
2025-05-02T06:21:30.720Z In(05) vcpu-0 8.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 7.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 6.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 5.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 4.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 3.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 2.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.033333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.034483	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.035714	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.037037	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.038462	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.040000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.041667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.043478	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.045455	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.047619	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.050000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.052632	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.055556	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.058824	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.062500	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.066667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.071429	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.076923	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.083333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.090909	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.100000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.111111	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.125000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.142857	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.166667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.200000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.250000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.333333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.500000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame:2
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: dimensions:1280x720
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame rates:
2025-05-02T06:21:30.720Z In(05) vcpu-0 26.000026	
2025-05-02T06:21:30.720Z In(05) vcpu-0 25.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 24.000038	
2025-05-02T06:21:30.720Z In(05) vcpu-0 23.000032	
2025-05-02T06:21:30.720Z In(05) vcpu-0 22.000022	
2025-05-02T06:21:30.720Z In(05) vcpu-0 21.000021	
2025-05-02T06:21:30.720Z In(05) vcpu-0 20.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 19.000029	
2025-05-02T06:21:30.720Z In(05) vcpu-0 18.000018	
2025-05-02T06:21:30.720Z In(05) vcpu-0 17.000009	
2025-05-02T06:21:30.720Z In(05) vcpu-0 16.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 15.000015	
2025-05-02T06:21:30.720Z In(05) vcpu-0 14.000014	
2025-05-02T06:21:30.720Z In(05) vcpu-0 13.000013	
2025-05-02T06:21:30.720Z In(05) vcpu-0 12.000005	
2025-05-02T06:21:30.720Z In(05) vcpu-0 11.000011	
2025-05-02T06:21:30.720Z In(05) vcpu-0 10.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 9.000001	
2025-05-02T06:21:30.720Z In(05) vcpu-0 8.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 7.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 6.000002	
2025-05-02T06:21:30.720Z In(05) vcpu-0 5.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 4.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 3.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 2.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z In(05) vcpu-0 VUsbVideo: Frame durations:
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.038462	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.040000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.041667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.043478	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.045455	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.047619	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.050000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.052632	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.055556	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.058824	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.062500	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.066667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.071429	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.076923	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.083333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.090909	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.100000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.111111	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.125000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.142857	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.166667	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.200000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.250000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.333333	
2025-05-02T06:21:30.720Z In(05) vcpu-0 0.500000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 1.000000	
2025-05-02T06:21:30.720Z In(05) vcpu-0 
2025-05-02T06:21:30.720Z No(00) vcpu-0 ConfigDB: Setting ehci:0.present = "TRUE"
2025-05-02T06:21:30.720Z No(00) vcpu-0 ConfigDB: Setting ehci:0.deviceType = "video"
2025-05-02T06:21:30.720Z No(00) vcpu-0 ConfigDB: Setting ehci:0.port = "0"
2025-05-02T06:21:30.720Z No(00) vcpu-0 ConfigDB: Setting ehci:0.parent = "-1"
2025-05-02T06:21:30.727Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 8191 MB (100 %) Size:131071 MB (100 %)
2025-05-02T06:21:30.727Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2025-05-02T06:21:30.727Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:21:30.727Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:21:30.727Z In(05) vcpu-0 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:21:30.741Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2025-05-02T06:21:30.741Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: Starting isolated virtual interface, vmnet=vmnet8
2025-05-02T06:21:30.741Z In(05) vcpu-0 DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:21:30.750Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: uuid=A0BE8A3F-C259-4B67-984F-7D70F863C51A
2025-05-02T06:21:30.750Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: connect adapter to host: connect=yes
2025-05-02T06:21:30.750Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: host adapter address: ip=*************
2025-05-02T06:21:30.750Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: host adapter subnet: subnet=*************
2025-05-02T06:21:30.750Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartIsolatedInterface: Ethernet0: MTU size is not configured, vmnet=vmnet8
2025-05-02T06:21:30.754Z In(05) host-181676 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: starting interface, status=1000
2025-05-02T06:21:30.754Z In(05) host-181676 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: interface params:
2025-05-02T06:21:30.754Z In(05) host-181676 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      MTU             : 1500
2025-05-02T06:21:30.754Z In(05) host-181676 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      Max packet size : 1514
2025-05-02T06:21:30.754Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartInterface: Ethernet0: Virtual interface started successfully
2025-05-02T06:21:30.754Z In(05) vcpu-0 VNET: MACVNetMacosDSMonitorSubscribeDefaultNetIf: Starting monitoring global primary interface key
2025-05-02T06:21:30.755Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2025-05-02T06:21:30.755Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2025-05-02T06:21:30.756Z In(05) vcpu-0 FLOPPYLIB-IMAGE: Floppy geometry 80/2/18 detected from boot sector.
2025-05-02T06:21:30.756Z In(05) vcpu-0 CDROM: Connecting sata0:0 to 'autoinst.iso'. type=2 remote=0
2025-05-02T06:21:30.759Z In(05) host-181676 VNET: MACVNetPortVirtApiPrimaryIfaceChanged: Global state callback for adapter: 0, primary if: en0
2025-05-02T06:21:30.762Z In(05) vcpu-0 DMG_Open: Not an unencrypted .dmg file (footer signature 0x00000000).
2025-05-02T06:21:30.762Z In(05) vcpu-0 DMG_Close: max cached entries 8
2025-05-02T06:21:30.762Z In(05) vcpu-0 CDROM: Checking initial physical media state...
2025-05-02T06:21:30.762Z In(05) vcpu-0 CDROM:  initial physical CDROM state is 1 (sense)
2025-05-02T06:21:30.763Z In(05) vcpu-0 CDROM: Connecting sata0:1 to '/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso'. type=2 remote=0
2025-05-02T06:21:30.766Z In(05) vcpu-0 AIOGNRC: Failed to open '/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso' : Could not find the file (60003) (0x1).
2025-05-02T06:21:30.766Z In(05) vcpu-0 CDROM-IMG:  image open for '/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso' failed: Could not find the file (60003).
2025-05-02T06:21:30.766Z In(05) vcpu-0 CDROM-IMG: Failed to connect '/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso'.
2025-05-02T06:21:30.766Z In(05) vcpu-0 CDROM: Failed to connect CDROM device '/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso'.
2025-05-02T06:21:30.767Z In(05) vcpu-0 Msg_Post: Warning
2025-05-02T06:21:30.767Z In(05) vcpu-0 [msg.cdromImage.cantOpen] Cannot connect file "/Volumes/New Volume/ubuntu-22.04.3-desktop-amd64.iso.iso" as a CD-ROM image: Could not find the file
2025-05-02T06:21:30.767Z In(05) vcpu-0 [msg.device.startdisconnected] Virtual device 'sata0:1' will start disconnected.
2025-05-02T06:21:30.767Z In(05) vcpu-0 ----------------------------------------
2025-05-02T06:21:30.806Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2025-05-02T06:21:30.810Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:21:30.814Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2025-05-02T06:21:30.815Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2025-05-02T06:21:30.815Z No(00) vcpu-0 Metrics lastUpdate (s): 11356
2025-05-02T06:21:30.815Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2025-05-02T06:21:30.815Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2025-05-02T06:21:30.815Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-02T06:21:30.815Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2025-05-02T06:21:30.815Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2025-05-02T06:21:30.815Z In(05) vmx USB: New set of 6 USB devices.
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:QinHeng\ USB2.0-Serial vid:1a86 pid:7523 path:65/3/2 speed:full family:vendor arbRuntimeKey:4 version:5]
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:Realtek\ USB\ 10/100/1000\ LAN vid:0bda pid:8153 path:0/1/4 speed:super family:vendor,comm serialnum:000001 arbRuntimeKey:3 version:5]
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:Fresco\ Logic\ Generic\ Billboard\ Device vid:1d5c pid:7102 path:65/3/3/1 speed:full family:other arbRuntimeKey:2 version:5]
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:CMX\ USB\ PnP\ Audio\ Device vid:2000 pid:dd00 path:65/3/3/2/2 speed:full family:audio,hid serialnum:U2dc0z7ws53a79rn arbRuntimeKey:1 version:5]
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5]
2025-05-02T06:21:30.815Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:21:30.816Z No(00) vmx ConfigDB: Setting floppy0.clientDevice = "FALSE"
2025-05-02T06:21:30.819Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2025-05-02T06:21:30.819Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2025-05-02T06:21:30.860Z In(05) vmx USB: DevID(600000010e0f0008): Connecting device desc:name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth deviceType:virtual-bluetooth info:0000001 version:5.
2025-05-02T06:21:30.861Z In(05) vmx USB: New set of 6 USB devices.
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:QinHeng\ USB2.0-Serial vid:1a86 pid:7523 path:65/3/2 speed:full family:vendor arbRuntimeKey:4 version:5]
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:Realtek\ USB\ 10/100/1000\ LAN vid:0bda pid:8153 path:0/1/4 speed:super family:vendor,comm serialnum:000001 arbRuntimeKey:3 version:5]
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:Fresco\ Logic\ Generic\ Billboard\ Device vid:1d5c pid:7102 path:65/3/3/1 speed:full family:other arbRuntimeKey:2 version:5]
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:CMX\ USB\ PnP\ Audio\ Device vid:2000 pid:dd00 path:65/3/3/2/2 speed:full family:audio,hid serialnum:U2dc0z7ws53a79rn arbRuntimeKey:1 version:5]
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth virtPath:usb:2 deviceType:virtual-bluetooth info:0000001 version:5], connected to usb:1 port 0.
2025-05-02T06:21:30.861Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:21:30.897Z In(05) mks MKSControlMgr: connected
2025-05-02T06:21:30.928Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2025-05-02T06:21:31.074Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 640, 480) flags=0x3
2025-05-02T06:21:31.102Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:31.102Z In(05) mks SWBWindow: Window 21 Defined: src screenId=0, src xywh(0, 0, 640, 480) dest xywh(160, 0, 1600, 1200) pixelScale=2, flags=0xF
2025-05-02T06:21:31.103Z In(05) windowThread-21 VTHREAD 123145575485440 "windowThread-21" tid 181895
2025-05-02T06:21:31.103Z In(05) mks MKS-HWinMux: Started MacOS presentation backend.
2025-05-02T06:21:31.103Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:31.103Z In(05) mks SWBWindow: Window 22 Defined: src screenId=0, src xywh(0, 0, 640, 480) dest xywh(1, 0, 1301, 976) pixelScale=1, flags=0x1A
2025-05-02T06:21:31.103Z In(05) windowThread-22 VTHREAD 123145576022016 "windowThread-22" tid 181896
2025-05-02T06:21:31.533Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0) and 0xfe000000(0)
2025-05-02T06:21:31.533Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.537Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:31.537Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.554Z In(05) vmx USB: New set of 6 USB devices.
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:QinHeng\ USB2.0-Serial vid:1a86 pid:7523 path:65/3/2 speed:full family:vendor arbRuntimeKey:4 version:5]
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:Realtek\ USB\ 10/100/1000\ LAN vid:0bda pid:8153 path:0/1/4 speed:super family:vendor,comm serialnum:000001 arbRuntimeKey:3 version:5]
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:Fresco\ Logic\ Generic\ Billboard\ Device vid:1d5c pid:7102 path:65/3/3/1 speed:full family:other arbRuntimeKey:2 version:5]
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:CMX\ USB\ PnP\ Audio\ Device vid:2000 pid:dd00 path:65/3/3/2/2 speed:full family:audio,hid serialnum:U2dc0z7ws53a79rn arbRuntimeKey:1 version:5]
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:Virtual\ Bluetooth\ Adapter vid:0e0f pid:0008 speed:full family:wireless,bluetooth virtPath:usb:2 deviceType:virtual-bluetooth info:0000001 version:5], connected to usb:1 port 0.
2025-05-02T06:21:31.554Z In(05) vmx USB: Found device [name:VMware\ Virtual\ USB\ Video\ Device:default vid:0e0f pid:000b speed:high family:video virtPath:ehci:0 deviceType:virtual-video info:0000001 version:5], connected to ehci port 0.
2025-05-02T06:21:31.698Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:31.698Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.703Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:31.703Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.703Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2025-05-02T06:21:31.703Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:31.703Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.752Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.753Z In(05) vcpu-2 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.755Z In(05) vcpu-3 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.756Z In(05) vcpu-4 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.757Z In(05) vcpu-5 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.758Z In(05) vcpu-6 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.759Z In(05) vcpu-7 CPU reset: soft (mode Emulation)
2025-05-02T06:21:31.792Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.824Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:31.965Z In(05) vcpu-0 AHCI: Tried to enable/disable IO space.
2025-05-02T06:21:32.016Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=117487/255/63
2025-05-02T06:21:32.016Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=1887436800 logical sector size=512
2025-05-02T06:21:32.389Z In(05) vcpu-0 AHCI: Tried to enable/disable IO space.
2025-05-02T06:21:32.390Z In(05) vcpu-0 AHCI-VMM:HBA reset issued on sata0.
2025-05-02T06:21:32.401Z In(05) vcpu-0 AHCI-USER: Already in check condition 02 3a 00
2025-05-02T06:21:32.401Z In(05) vcpu-0 AHCI-USER: Already in check condition 02 3a 00
2025-05-02T06:21:32.421Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.423Z In(05) vcpu-2 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.425Z In(05) vcpu-3 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.426Z In(05) vcpu-4 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.428Z In(05) vcpu-5 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.430Z In(05) vcpu-6 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.431Z In(05) vcpu-7 CPU reset: soft (mode Emulation)
2025-05-02T06:21:32.459Z In(05) vcpu-0 BIOS-UUID is 56 4d a6 90 3f 54 15 44-c3 e4 39 6b 60 99 78 11
2025-05-02T06:21:32.578Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 480) flags=0x3
2025-05-02T06:21:32.599Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:21:32.600Z In(05) mks SWBWindow: Window 22 Destroyed: src screenId=0, src xywh(0, 0, 640, 480) dest xywh(1, 0, 1301, 976) pixelScale=1, flags=0x1A
2025-05-02T06:21:32.623Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:32.706Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:32.706Z In(05) mks SWBWindow: Window 24 Defined: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:21:32.706Z In(05) windowThread-24 VTHREAD 123145576022016 "windowThread-24" tid 181902
2025-05-02T06:21:33.555Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:21:33.555Z In(05) svga SVGA enabling SVGA
2025-05-02T06:21:33.555Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:21:33.557Z In(05) svga SVGA-ScreenMgr: Screen type changed to RegisterMode
2025-05-02T06:21:33.557Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 640, 480) flags=0x2
2025-05-02T06:21:33.557Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:33.558Z Wa(03) windowThread-24 MKSUIDrawnMKSClient_SendUIDrawnMKSCommand, could not create Mach port to send UI command.
2025-05-02T06:21:33.559Z In(05) mks SWBWindow: Window 24 Destroyed: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:21:33.559Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:33.567Z In(05) vcpu-0 DDB: "longContentID" = "b91d6b84a4356862ff580bdc7d992ed0" (was "552cd9266b88fd8c05540bf56f9c1bcb")
2025-05-02T06:21:33.569Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:33.569Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:33.569Z In(05) mks SWBWindow: Window 25 Defined: src screenId=1, src xywh(0, 0, 640, 480) dest xywh(1, 0, 1301, 976) pixelScale=1, flags=0x1A
2025-05-02T06:21:33.569Z In(05) windowThread-25 VTHREAD 123145576022016 "windowThread-25" tid 181913
2025-05-02T06:21:35.596Z In(05) svga SVGA disabling SVGA
2025-05-02T06:21:35.596Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 640, 480) flags=0x2
2025-05-02T06:21:35.597Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:35.618Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:21:35.619Z In(05) mks SWBWindow: Window 25 Destroyed: src screenId=1, src xywh(0, 0, 640, 480) dest xywh(1, 0, 1301, 976) pixelScale=1, flags=0x12
2025-05-02T06:21:35.620Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:35.643Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:35.643Z In(05) mks SWBWindow: Window 26 Defined: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:21:35.643Z In(05) windowThread-26 VTHREAD 123145553170432 "windowThread-26" tid 181932
2025-05-02T06:21:36.101Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2025-05-02T06:21:36.285Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.287Z In(05) vcpu-2 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.289Z In(05) vcpu-3 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.292Z In(05) vcpu-4 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.294Z In(05) vcpu-5 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.295Z In(05) vcpu-6 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.297Z In(05) vcpu-7 CPU reset: soft (mode Emulation)
2025-05-02T06:21:36.723Z In(05) vcpu-4 SVGA: Unregistering IOSpace at 0x1070
2025-05-02T06:21:36.723Z In(05) vcpu-4 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:36.723Z In(05) vcpu-4 SVGA: FIFO is already mapped
2025-05-02T06:21:36.724Z In(05) vcpu-4 SVGA: Registering IOSpace at 0x1070
2025-05-02T06:21:36.724Z In(05) vcpu-4 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:21:36.724Z In(05) vcpu-4 SVGA: FIFO is already mapped
2025-05-02T06:21:38.239Z In(05) vcpu-3 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.240Z In(05) vcpu-6 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.240Z In(05) vcpu-5 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.241Z In(05) vcpu-0 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.241Z In(05) vcpu-4 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.247Z In(05) vcpu-1 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.248Z In(05) vcpu-7 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.281Z In(05) vcpu-2 Guest MSR write (0x49: 0x1)
2025-05-02T06:21:38.744Z In(05) vcpu-3 AHCI-VMM:HBA reset issued on sata0.
2025-05-02T06:21:38.744Z In(05) vcpu-3 AHCI-VMM:Write to reserved or READ ONLY global register
2025-05-02T06:21:38.744Z In(05) vcpu-3 AHCI-VMM:Write to reserved or READ ONLY global register
2025-05-02T06:21:38.758Z In(05) vcpu-4 SCSI0: RESET BUS
2025-05-02T06:21:38.805Z In(05) vcpu-7 VMMouse: CMD Read ID
2025-05-02T06:21:38.888Z In(05) vcpu-5 AHCI-VMM: sata0:0 Port COMRESET requested.
2025-05-02T06:21:38.888Z In(05) vcpu-5 AHCI-VMM: sata0:0: Moved to COMRESET state.
2025-05-02T06:21:38.888Z In(05) vcpu-7 AHCI-VMM: sata0:1 Port COMRESET requested.
2025-05-02T06:21:38.888Z In(05) vcpu-7 AHCI-VMM: sata0:1: Moved to COMRESET state.
2025-05-02T06:21:38.888Z In(05) vcpu-1 AHCI-VMM: sata0:2 Port COMRESET requested.
2025-05-02T06:21:38.888Z In(05) vcpu-1 AHCI-VMM: sata0:2: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.889Z In(05) vcpu-1 AHCI-VMM: sata0:2: Moved to COMRESET state.
2025-05-02T06:21:38.889Z In(05) vcpu-5 AHCI-VMM: sata0:3 Port COMRESET requested.
2025-05-02T06:21:38.889Z In(05) vcpu-5 AHCI-VMM: sata0:3: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.889Z In(05) vcpu-5 AHCI-VMM: sata0:3: Moved to COMRESET state.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:4 Port COMRESET requested.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:4: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:4: Moved to COMRESET state.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:5 Port COMRESET requested.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:5: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.889Z In(05) vcpu-4 AHCI-VMM: sata0:5: Moved to COMRESET state.
2025-05-02T06:21:38.889Z In(05) vcpu-7 AHCI-VMM: sata0:1: Exiting COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-5 AHCI-VMM: sata0:0: Exiting COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-6 AHCI-VMM: sata0:6 Port COMRESET requested.
2025-05-02T06:21:38.890Z In(05) vcpu-6 AHCI-VMM: sata0:6: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.890Z In(05) vcpu-6 AHCI-VMM: sata0:6: Moved to COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-4 AHCI-VMM: sata0:7 Port COMRESET requested.
2025-05-02T06:21:38.890Z In(05) vcpu-4 AHCI-VMM: sata0:7: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.890Z In(05) vcpu-4 AHCI-VMM: sata0:7: Moved to COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-1 AHCI-VMM: sata0:2: Exiting COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-5 AHCI-VMM: sata0:3: Exiting COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-4 AHCI-VMM: sata0:4: Exiting COMRESET state.
2025-05-02T06:21:38.890Z In(05) vcpu-3 AHCI-VMM: sata0:8 Port COMRESET requested.
2025-05-02T06:21:38.890Z In(05) vcpu-3 AHCI-VMM: sata0:8: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.890Z In(05) vcpu-3 AHCI-VMM: sata0:8: Moved to COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:9 Port COMRESET requested.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:9: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:9: Moved to COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:5: Exiting COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:10 Port COMRESET requested.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:10: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:10: Moved to COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-6 AHCI-VMM: sata0:6: Exiting COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:11 Port COMRESET requested.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:11: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:11: Moved to COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:12 Port COMRESET requested.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:12: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.891Z In(05) vcpu-1 AHCI-VMM: sata0:12: Moved to COMRESET state.
2025-05-02T06:21:38.891Z In(05) vcpu-4 AHCI-VMM: sata0:7: Exiting COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-6 AHCI-VMM: sata0:13 Port COMRESET requested.
2025-05-02T06:21:38.892Z In(05) vcpu-6 AHCI-VMM: sata0:13: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.892Z In(05) vcpu-6 AHCI-VMM: sata0:13: Moved to COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-3 AHCI-VMM: sata0:8: Exiting COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-7 AHCI-VMM: sata0:14 Port COMRESET requested.
2025-05-02T06:21:38.892Z In(05) vcpu-7 AHCI-VMM: sata0:14: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.892Z In(05) vcpu-7 AHCI-VMM: sata0:14: Moved to COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-4 AHCI-VMM: sata0:9: Exiting COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-3 AHCI-VMM: sata0:15 Port COMRESET requested.
2025-05-02T06:21:38.892Z In(05) vcpu-3 AHCI-VMM: sata0:15: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.892Z In(05) vcpu-3 AHCI-VMM: sata0:15: Moved to COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-1 AHCI-VMM: sata0:10: Exiting COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-1 AHCI-VMM: sata0:16 Port COMRESET requested.
2025-05-02T06:21:38.892Z In(05) vcpu-1 AHCI-VMM: sata0:16: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.892Z In(05) vcpu-1 AHCI-VMM: sata0:16: Moved to COMRESET state.
2025-05-02T06:21:38.892Z In(05) vcpu-4 AHCI-VMM: sata0:11: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:17 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:17: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:17: Moved to COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:12: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:18 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:18: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:18: Moved to COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-6 AHCI-VMM: sata0:13: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:19 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:19: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:19: Moved to COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:20 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:20: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:20: Moved to COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:14: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:21 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:21: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-4 AHCI-VMM: sata0:21: Moved to COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-3 AHCI-VMM: sata0:15: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-1 AHCI-VMM: sata0:16: Exiting COMRESET state.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:22 Port COMRESET requested.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:22: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.893Z In(05) vcpu-7 AHCI-VMM: sata0:22: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:23 Port COMRESET requested.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:23: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:23: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-7 AHCI-VMM: sata0:17: Exiting COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-7 AHCI-VMM: sata0:24 Port COMRESET requested.
2025-05-02T06:21:38.894Z In(05) vcpu-7 AHCI-VMM: sata0:24: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.894Z In(05) vcpu-7 AHCI-VMM: sata0:24: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:18: Exiting COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-4 AHCI-VMM: sata0:19: Exiting COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:25 Port COMRESET requested.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:25: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:25: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-3 AHCI-VMM: sata0:26 Port COMRESET requested.
2025-05-02T06:21:38.894Z In(05) vcpu-3 AHCI-VMM: sata0:26: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.894Z In(05) vcpu-3 AHCI-VMM: sata0:26: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:20: Exiting COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:27 Port COMRESET requested.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:27: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.894Z In(05) vcpu-1 AHCI-VMM: sata0:27: Moved to COMRESET state.
2025-05-02T06:21:38.894Z In(05) vcpu-4 AHCI-VMM: sata0:21: Exiting COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-7 AHCI-VMM: sata0:28 Port COMRESET requested.
2025-05-02T06:21:38.895Z In(05) vcpu-7 AHCI-VMM: sata0:28: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.895Z In(05) vcpu-7 AHCI-VMM: sata0:28: Moved to COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-7 AHCI-VMM: sata0:22: Exiting COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-4 AHCI-VMM: sata0:29 Port COMRESET requested.
2025-05-02T06:21:38.895Z In(05) vcpu-4 AHCI-VMM: sata0:29: Write to PxSCTL.DET while no device present.
2025-05-02T06:21:38.895Z In(05) vcpu-4 AHCI-VMM: sata0:29: Moved to COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-1 AHCI-VMM: sata0:23: Exiting COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-7 AHCI-VMM: sata0:24: Exiting COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-1 AHCI-VMM: sata0:25: Exiting COMRESET state.
2025-05-02T06:21:38.895Z In(05) vcpu-3 AHCI-VMM: sata0:26: Exiting COMRESET state.
2025-05-02T06:21:38.896Z In(05) vcpu-1 AHCI-VMM: sata0:27: Exiting COMRESET state.
2025-05-02T06:21:38.896Z In(05) vcpu-7 AHCI-VMM: sata0:28: Exiting COMRESET state.
2025-05-02T06:21:38.896Z In(05) vcpu-4 AHCI-VMM: sata0:29: Exiting COMRESET state.
2025-05-02T06:21:38.910Z In(05) vcpu-4 SCSI0: RESET BUS
2025-05-02T06:21:38.928Z In(05) vcpu-4 DISKUTIL: scsi0:0 : capacity=1887436800 logical sector size=512
2025-05-02T06:21:38.928Z In(05) vcpu-4 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2025-05-02T06:21:38.930Z In(05) vcpu-4 DISKUTIL: scsi0:0 : capacity=1887436800 logical sector size=512
2025-05-02T06:21:38.930Z In(05) vcpu-4 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2025-05-02T06:21:39.207Z In(05) vcpu-2 AHCI-USER: Already in check condition 02 3a 00
2025-05-02T06:21:39.211Z In(05) vcpu-5 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:21:39.279Z In(05) vcpu-5 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:21:39.285Z In(05) vthread-181976 VTHREAD 123145553707008 "vthread-181976" tid 181976
2025-05-02T06:21:39.303Z In(05) vthread-181978 VTHREAD 123145576022016 "vthread-181978" tid 181978
2025-05-02T06:21:39.538Z In(05) vthread-181986 VTHREAD 123145576558592 "vthread-181986" tid 181986
2025-05-02T06:21:39.725Z In(05) vthread-181987 VTHREAD 123145577095168 "vthread-181987" tid 181987
2025-05-02T06:21:39.750Z In(05) vthread-181988 VTHREAD 123145577631744 "vthread-181988" tid 181988
2025-05-02T06:21:39.751Z In(05) vthread-181989 VTHREAD 123145578168320 "vthread-181989" tid 181989
2025-05-02T06:21:39.751Z In(05) vthread-181990 VTHREAD 123145578704896 "vthread-181990" tid 181990
2025-05-02T06:21:40.053Z In(05) svga SVGA hiding SVGA
2025-05-02T06:21:40.055Z In(05) vcpu-7 Guest: vmwgfx: Module Version: 2.20.0 (kernel: 6.8.0-60-generic)
2025-05-02T06:21:40.055Z In(05) vcpu-7 SVGA: Guest reported SVGA driver: (2, 101187596, 34865152, 0)
2025-05-02T06:21:40.059Z In(05) svga SVGA enabling SVGA
2025-05-02T06:21:40.059Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:21:40.059Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 640, 480) flags=0x2
2025-05-02T06:21:40.060Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:40.085Z In(05) mks SWBWindow: Window 26 Destroyed: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x12
2025-05-02T06:21:40.085Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:40.087Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:40.087Z In(05) mks SWBWindow: Window 27 Defined: src screenId=1, src xywh(0, 0, 640, 480) dest xywh(1, 0, 1301, 976) pixelScale=1, flags=0x1A
2025-05-02T06:21:40.087Z In(05) windowThread-27 VTHREAD 123145553170432 "windowThread-27" tid 182004
2025-05-02T06:21:40.089Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 640, 480) flags=0x2
2025-05-02T06:21:40.089Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2025-05-02T06:21:40.089Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:21:40.091Z In(05) mks SWBWindow: No more window events will be logged.
2025-05-02T06:21:40.102Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:40.116Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:40.116Z In(05) windowThread-28 VTHREAD 123145553170432 "windowThread-28" tid 182007
2025-05-02T06:21:40.337Z In(05) vthread-182009 VTHREAD 123145579778048 "vthread-182009" tid 182009
2025-05-02T06:21:40.337Z In(05) vthread-182010 VTHREAD 123145580314624 "vthread-182010" tid 182010
2025-05-02T06:21:40.342Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2025-05-02T06:21:40.342Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2025-05-02T06:21:40.342Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2025-05-02T06:21:40.342Z In(05) vcpu-0 Tools: Removing Tools inactivity timer.
2025-05-02T06:21:40.807Z In(05) vcpu-6 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:40.807Z In(05) vcpu-6 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:40.836Z In(05) vcpu-2 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:21:40.954Z In(05) vcpu-4 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:40.954Z In(05) vcpu-4 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:41.116Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:41.235Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:100, wIndex:100
2025-05-02T06:21:41.236Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.238Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.238Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.239Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:200, wIndex:100
2025-05-02T06:21:41.239Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.240Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.240Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.241Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:300, wIndex:100
2025-05-02T06:21:41.241Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.242Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.242Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.243Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:400, wIndex:100
2025-05-02T06:21:41.243Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.246Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.246Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.247Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:500, wIndex:100
2025-05-02T06:21:41.247Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.248Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.248Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.252Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:600, wIndex:100
2025-05-02T06:21:41.252Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.253Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.253Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.257Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:700, wIndex:100
2025-05-02T06:21:41.257Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.258Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.258Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.259Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:900, wIndex:100
2025-05-02T06:21:41.259Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.260Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.260Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.261Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:a00, wIndex:100
2025-05-02T06:21:41.261Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.262Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.262Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.263Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:b00, wIndex:100
2025-05-02T06:21:41.263Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.264Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.264Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.266Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:c00, wIndex:100
2025-05-02T06:21:41.266Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.267Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.267Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.268Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:d00, wIndex:100
2025-05-02T06:21:41.268Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.269Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.269Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.270Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:e00, wIndex:100
2025-05-02T06:21:41.270Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.272Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.272Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.273Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:f00, wIndex:100
2025-05-02T06:21:41.273Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.274Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.274Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.277Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:1000, wIndex:100
2025-05-02T06:21:41.277Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.279Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.279Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.280Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:800, wIndex:100
2025-05-02T06:21:41.280Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.281Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.281Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.282Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:86, wValue:1100, wIndex:100
2025-05-02T06:21:41.282Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.283Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:41.283Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:41.647Z In(05) vmx GuestRpc: Got RPCI vsocket connection 7, assigned to channel 0.
2025-05-02T06:21:41.647Z In(05) vmx Guest: [vgauthservice] VGAuthService build-22544099 starting up
2025-05-02T06:21:41.647Z In(05) vmx GuestRpc: Closing channel 0 connection 7
2025-05-02T06:21:41.685Z In(05) vmx GuestRpc: Got RPCI vsocket connection 8, assigned to channel 0.
2025-05-02T06:21:41.685Z In(05) vmx Guest: [vgauthservice] SAML_Init: Using xmlsec1 1.2.33 for XML signature support
2025-05-02T06:21:41.685Z In(05) vmx GuestRpc: Closing channel 0 connection 8
2025-05-02T06:21:41.804Z In(05) vmx GuestRpc: Got RPCI vsocket connection 9, assigned to channel 0.
2025-05-02T06:21:41.871Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:21:41.871Z In(05) vmx Guest: toolbox: Version: 12.3.5.46049 (build-22544099)
2025-05-02T06:21:41.871Z Wa(03) vcpu-2 GuestRpc: application toolbox, changing channel 65535 -> 0
2025-05-02T06:21:41.871Z In(05) vcpu-2 GuestRpc: Channel 0, guest application toolbox.
2025-05-02T06:21:41.871Z In(05) vcpu-2 Tools: [AppStatus] Last heartbeat value 1 (last received 1s ago)
2025-05-02T06:21:41.871Z In(05) vcpu-2 TOOLS: appName=toolbox, oldStatus=0, status=1, guestInitiated=0.
2025-05-02T06:21:41.882Z In(05) vmx Tools: Changing running status: 1 => 2.
2025-05-02T06:21:41.882Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 1 (last received 1s ago)
2025-05-02T06:21:41.886Z In(05) vmx ToolsSetDisplayTopology: Sending rpcMsg = DisplayTopology_Set 1 , 0 0 3840 2400
2025-05-02T06:21:41.888Z In(05) vmx TOOLS Received tools.set.versiontype rpc call, version = 12389, type = 4
2025-05-02T06:21:41.888Z In(05) vmx TOOLS Setting toolsVersionStatus = TOOLS_STATUS_UNMANAGED
2025-05-02T06:21:41.888Z In(05) vmx Tools_SetVersionAndType did nothing; new tools version (12389) and type (4) match old Tools version and type
2025-05-02T06:21:41.889Z In(05) vmx Guest: Executing script for state change 'OS_PowerOn'.
2025-05-02T06:21:41.892Z In(05) vcpu-2 Tools: State change '3' progress: last event 0, event 1, success 1.
2025-05-02T06:21:42.075Z Wa(03) vcpu-5 ES1371HostSetMixer attempting to set reg=0x1a and vol=0x0
2025-05-02T06:21:42.234Z Wa(03) vcpu-6 ES1371HostSetMixer attempting to set reg=0x1a and vol=0x202
2025-05-02T06:21:42.423Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:21:42.424Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:21:42.425Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:42.479Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-02T06:21:42.480Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:42.480Z In(05) windowThread-29 VTHREAD 123145553170432 "windowThread-29" tid 182156
2025-05-02T06:21:42.686Z In(05) vmx Guest: Script exit code: 0, success = 1
2025-05-02T06:21:42.686Z In(05) vmx TOOLS state change 3 returned status 1
2025-05-02T06:21:42.686Z In(05) vmx Tools: State change '3' progress: last event 1, event 2, success 1.
2025-05-02T06:21:42.686Z In(05) vmx Tools: State change '3' progress: last event 1, event 4, success 1.
2025-05-02T06:21:42.686Z In(05) vmx Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-02T06:21:42.686Z In(05) vmx Tools: Changing running status: 2 => 1.
2025-05-02T06:21:42.686Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 2 (last received 0s ago)
2025-05-02T06:21:43.447Z In(05) vcpu-7 VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:87, wValue:200, wIndex:100
2025-05-02T06:21:43.447Z In(05) vcpu-7 VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:43.448Z In(05) vmx VUsbVideo: Unhandled or invalid class specific request bmRequestType:a1, bmRequest:81, wValue:200, wIndex:0
2025-05-02T06:21:43.448Z In(05) vmx VUsbVideo: Unhandled URB: type=0 endpt=0 stream=0 datalen=9 numPackets=1
2025-05-02T06:21:43.482Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:21:43.482Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:21:43.482Z In(05) vcpu-1 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:21:43.532Z In(05) DAC1 VTHREAD 123145581924352 "DAC1" tid 182216
2025-05-02T06:21:43.542Z In(05) vcpu-5 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:21:43.542Z In(05) vcpu-5 SoundUtilMacos_ExtractAudioDeviceStr: Sound output device: BuiltInSpeakerDevice.
2025-05-02T06:21:43.542Z In(05) vcpu-5 SoundUtilMacos_ExtractAudioDeviceStr: Sound input device: BuiltInMicrophoneDevice.
2025-05-02T06:21:43.735Z In(05) ADC VTHREAD 123145582997504 "ADC" tid 182229
2025-05-02T06:21:44.958Z In(05) vcpu-6 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:21:44.959Z In(05) vcpu-6 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:21:47.332Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:47.332Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 40
2025-05-02T06:21:47.332Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 32
2025-05-02T06:21:47.334Z In(05) vcpu-1 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:47.341Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:47.361Z In(05) vcpu-3 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:47.361Z In(05) vcpu-3 CDROM: Emulate GET CONFIGURATION RT 0 starting feature 0
2025-05-02T06:21:47.378Z In(05) vcpu-3 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:21:47.400Z In(05) vthread-182318 VTHREAD 123145583534080 "vthread-182318" tid 182318
2025-05-02T06:21:47.403Z In(05) vthread-182319 VTHREAD 123145584070656 "vthread-182319" tid 182319
2025-05-02T06:21:47.403Z In(05) vthread-182320 VTHREAD 123145584607232 "vthread-182320" tid 182320
2025-05-02T06:21:47.410Z In(05) vthread-182321 VTHREAD 123145585143808 "vthread-182321" tid 182321
2025-05-02T06:21:48.023Z In(05) vmx GuestRpc: Got RPCI vsocket connection 10, assigned to channel 1.
2025-05-02T06:21:48.582Z In(05) vcpu-5 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:21:48.582Z In(05) vcpu-5 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:21:48.844Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:21:50.667Z In(05) vcpu-6 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:21:50.667Z In(05) vcpu-6 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:21:52.177Z In(05) vcpu-6 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:21:52.177Z In(05) vcpu-6 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:21:52.858Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:21:52.859Z In(05) vmx Guest: toolbox-dnd: Version: 12.3.5.46049 (build-22544099)
2025-05-02T06:21:52.860Z Wa(03) vcpu-3 GuestRpc: application toolbox-dnd, changing channel 65535 -> 1
2025-05-02T06:21:52.860Z In(05) vcpu-3 GuestRpc: Channel 1, guest application toolbox-dnd.
2025-05-02T06:21:52.860Z In(05) vcpu-3 TOOLS: appName=toolbox-dnd, oldStatus=0, status=1, guestInitiated=0.
2025-05-02T06:21:52.984Z In(05) vmx DnDCP: dndGuestVersion from vmdb failed, setting to 4
2025-05-02T06:21:52.984Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:21:52.989Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:21:52.989Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:21:53.170Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:21:53.917Z In(05) vcpu-7 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:22:11.870Z In(05) vmx GuestInfo: HostinfoDetailedDataHeader version: 1
2025-05-02T06:22:11.871Z No(00) vmx ConfigDB: Setting guestInfo.detailed.data = <not printed>
2025-05-02T06:22:11.884Z No(00) vmx ConfigDB: Setting floppy0.clientDevice = "FALSE"
2025-05-02T06:22:43.316Z In(05) vthread-183236 VTHREAD 123145552633856 "vthread-183236" tid 183236
2025-05-02T06:22:43.316Z In(05) vthread-183237 VTHREAD 123145579241472 "vthread-183237" tid 183237
2025-05-02T06:23:31.714Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:23:44.037Z In(05) vcpu-7 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:23:44.037Z In(05) vcpu-7 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:23:58.907Z In(05) vcpu-5 Guest: Mesa: SVGA3D; build: RELEASE;  LLVM;
2025-05-02T06:23:58.908Z In(05) vcpu-5 Guest: Mesa: 23.2.1-1ubuntu3.1~22.04.3
2025-05-02T06:24:37.707Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:24:41.295Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:24:42.048Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:24:42.048Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:24:46.749Z In(05) vcpu-5 HGFileCopyCreateSessionCB: Successfully created the session.
2025-05-02T06:24:48.472Z In(05) vcpu-5 HGFileCopyFinishedWriteFileCB: Error writing guest file (12)
2025-05-02T06:24:48.472Z In(05) vcpu-5 Msg_Post: Error
2025-05-02T06:24:48.472Z In(05) vcpu-5 [msg.literal] Cannot write file to virtual machine.
2025-05-02T06:24:48.472Z In(05)+ vcpu-5 Canceling the file copy operation.
2025-05-02T06:24:48.472Z In(05) vcpu-5 ----------------------------------------
2025-05-02T06:24:51.458Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:16, format:2.
2025-05-02T06:25:20.979Z In(05) vcpu-4 CDROM sata0:0: CMD 0x52 (*UNKNOWN (0x52)*) FAILED (key 0x5 asc 0x20 ascq 0)
2025-05-02T06:26:05.193Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:07.399Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:07.407Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:26:07.407Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:26:17.635Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:19.430Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:26:19.430Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:26:19.451Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:22.261Z In(05) vcpu-6 HGFileCopyCreateSessionCB: Successfully created the session.
2025-05-02T06:26:28.228Z In(05) vthread-186013 VTHREAD 123145585680384 "vthread-186013" tid 186013
2025-05-02T06:26:39.467Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:41.468Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:26:41.480Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:26:41.481Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:26:57.708Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:27:00.087Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:27:00.783Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:27:00.783Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:27:03.853Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:27:05.449Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:27:06.142Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:87, format:2.
2025-05-02T06:27:06.142Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:20, format:7.
2025-05-02T06:27:16.220Z In(05) vcpu-1 HGFileCopyCreateSessionCB: Successfully created the session.
2025-05-02T06:27:16.229Z In(05) vcpu-1 HGFileCopyFinishedWriteFileCB: Error writing guest file (12)
2025-05-02T06:27:16.230Z In(05) vcpu-1 Msg_Post: Error
2025-05-02T06:27:16.230Z In(05) vcpu-1 [msg.literal] Cannot write file to virtual machine.
2025-05-02T06:27:16.230Z In(05)+ vcpu-1 Canceling the file copy operation.
2025-05-02T06:27:16.230Z In(05) vcpu-1 ----------------------------------------
2025-05-02T06:27:17.386Z In(05) vmx CPClipboard_SetItem: Set CPClipboard struct with data of size:16, format:2.
2025-05-02T06:28:06.925Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:07.812Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:53.265Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:28:53.266Z In(05) vmx DnDCP: set guest controllers to version 4
2025-05-02T06:28:53.267Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:28:53.268Z In(05) vcpu-7 GuestRpc: Reinitializing Channel 1(toolbox-dnd)
2025-05-02T06:28:53.268Z In(05) vcpu-7 TOOLS: appName=toolbox-dnd, oldStatus=1, status=0, guestInitiated=1.
2025-05-02T06:28:53.269Z In(05) vmx GuestRpc: Got error for channel 1 connection 10: Remote disconnected
2025-05-02T06:28:53.269Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 1
2025-05-02T06:28:53.269Z In(05) vmx GuestRpc: Closing channel 1 connection 10
2025-05-02T06:28:53.431Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:28:53.433Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:53.454Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:28:53.456Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:28:53.472Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:53.472Z In(05) windowThread-30 VTHREAD 123145553170432 "windowThread-30" tid 187918
2025-05-02T06:28:53.764Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:28:53.765Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:28:53.765Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:53.787Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:28:53.850Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:53.850Z In(05) windowThread-31 VTHREAD 123145553170432 "windowThread-31" tid 187924
2025-05-02T06:28:56.494Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 3840, 2400) flags=0x2
2025-05-02T06:28:56.495Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:28:56.496Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:56.501Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:28:56.514Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:28:56.514Z In(05) windowThread-32 VTHREAD 123145553170432 "windowThread-32" tid 187970
2025-05-02T06:28:56.907Z In(05) vmx GuestRpc: Got RPCI vsocket connection 11, assigned to channel 1.
2025-05-02T06:28:56.908Z In(05) vmx Guest: [vgauthservice] VGAuthService END SERVICE
2025-05-02T06:28:56.908Z In(05) vmx GuestRpc: Closing channel 1 connection 11
2025-05-02T06:28:56.908Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:28:56.917Z In(05) vcpu-5 GuestRpc: Reinitializing Channel 0(toolbox)
2025-05-02T06:28:56.917Z In(05) vcpu-5 Tools: [AppStatus] Last heartbeat value 434 (last received 0s ago)
2025-05-02T06:28:56.917Z In(05) vcpu-5 TOOLS: appName=toolbox, oldStatus=1, status=0, guestInitiated=1.
2025-05-02T06:28:56.931Z In(05) vmx GuestRpc: Got error for channel 0 connection 9: Remote disconnected
2025-05-02T06:28:56.931Z In(05) vmx GuestRpc: GuestRpcResetVsockChannel: channel 0
2025-05-02T06:28:56.931Z In(05) vmx GuestRpc: Closing channel 0 connection 9
2025-05-02T06:28:56.990Z In(05) vcpu-2 VMMouse: CMD Disable
2025-05-02T06:28:56.990Z In(05) vcpu-2 VMMouse: Disabling VMMouse mode
2025-05-02T06:28:57.058Z In(05) vcpu-0 PIIX4: PM Soft Off.  Good-bye.
2025-05-02T06:28:57.058Z In(05) vcpu-0 Chipset: The guest has requested that the virtual machine be powered off.
2025-05-02T06:28:57.058Z No(00) vcpu-0 ConfigDB: Setting softPowerOff = "TRUE"
2025-05-02T06:28:57.062Z In(05) vcpu-0 VMX: Issuing power-off request...
2025-05-02T06:28:57.062Z In(05) vmx Stopping VCPU threads...
2025-05-02T06:28:57.063Z In(05) vmx MKSThread: Requesting MKS exit
2025-05-02T06:28:57.063Z In(05) vmx Stopping MKS/SVGA threads
2025-05-02T06:28:57.089Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 800) flags=0x2
2025-05-02T06:28:57.089Z In(05) svga SVGA thread is exiting the main loop
2025-05-02T06:28:57.091Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:28:57.092Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2025-05-02T06:28:57.092Z In(05) vmx MKS/SVGA threads are stopped
2025-05-02T06:28:57.092Z In(05) vmx USB: DevID(600000010e0f0008): Disconnecting device.
2025-05-02T06:28:57.092Z In(05) vmx USB: DevID(800000010e0f000b): Disconnecting device.
2025-05-02T06:28:57.093Z In(05) vmx USB: DevID(200000050e0f0003): Disconnecting device.
2025-05-02T06:28:57.095Z In(05) vmx 
2025-05-02T06:28:57.095Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2025-05-02T06:28:57.095Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-05-02T06:28:57.095Z In(05)+ vmx 
2025-05-02T06:28:57.095Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :      16     16      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    467    467      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      2      2      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      8      8      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      4      4      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    211    211      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |     13     13      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      4      4      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |     10     10      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      4      4      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      4      4      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |     14     14      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |     55     55      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |     53     53      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0     10      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      1      1      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |     17     17      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      8      8      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   39936  39936      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Total paged                         :  4145028 4145028      - |   3815   3825      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Actual maximum                      :         4145028        |        4145028
2025-05-02T06:28:57.095Z In(05)+ vmx 
2025-05-02T06:28:57.095Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :      11     11      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      69     69      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    2170   3265      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_AHCIBIOS                   :      16     16      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_AHCIREGS                   :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_LBR                        :       8      8      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_MonWired                   :     161    161      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Total nonpaged                      :    3944   5487      - |      0      0      -
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem Actual maximum                      :           3944        |          5487
2025-05-02T06:28:57.095Z In(05)+ vmx 
2025-05-02T06:28:57.095Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:28:57.095Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     784    784      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2114   2171      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :      32     32      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       8      8      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :     320    320      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_TC                          :    4104   4104      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :      16     16      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_HV                          :       8      8      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       8      8      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_VHV                         :      24     24      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_Numa                        :      66     66      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :     232    232      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     887    887      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4386   4386      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     278    278      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :      13     13      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem Total anonymous                     :   15731  15788      - |      0      0      -
2025-05-02T06:28:57.096Z In(05) vmx OvhdMem Actual maximum                      :          15731        |         15788
2025-05-02T06:28:57.096Z In(05)+ vmx 
2025-05-02T06:28:57.096Z In(05) vmx VMMEM: Maximum Reservation: 16274MB (MainMem=8192MB)
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:28:57.096Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:28:57.096Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2025-05-02T06:28:57.096Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-05-02T06:28:57.096Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-05-02T06:28:57.096Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2025-05-02T06:28:57.096Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox' while GuestRpc is powering off.
2025-05-02T06:28:57.096Z In(05) vmx GuestRpc: Attempt to send TCLO msg for 'toolbox-dnd' while GuestRpc is powering off.
2025-05-02T06:28:57.151Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2025-05-02T06:28:57.151Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x0
2025-05-02T06:28:57.151Z In(05) vmx Tools: Changing running status: 1 => 0.
2025-05-02T06:28:57.151Z In(05) vmx Tools: [RunningStatus] Last heartbeat value 434 (last received 0s ago)
2025-05-02T06:28:57.151Z Wa(03) vmx Guest attempted to revert hostVerifiedSamlToken capability; ignoring.
2025-05-02T06:28:57.152Z In(05) vmx Tools: [AppStatus] Last heartbeat value 434 (last received 0s ago)
2025-05-02T06:28:57.152Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2025-05-02T06:28:57.152Z In(05) vmx VNET: MACVNetMacosDSMonitorUnsubscribeDefaultNetIf: Stopping global primary interface key monitoring
2025-05-02T06:28:57.172Z In(05) host-187859 VNET: MacosVmnetVirtApiStopHandler: Ethernet0: stopping virtual interface, status=1000
2025-05-02T06:28:57.172Z In(05) vmx USB: DevID(10e0f0002): Disconnecting device.
2025-05-02T06:28:57.173Z In(05) sensor SensorLib: Sensor thread exiting.
2025-05-02T06:28:57.173Z In(05) usbCCIDEnumCards USB-CCID: Card enum thread exiting.
2025-05-02T06:28:57.177Z In(05) mks MKSControlMgr: disconnected
2025-05-02T06:28:57.178Z In(05) mks MKS-RenderMain: Stopping MTLRenderer
2025-05-02T06:28:57.178Z In(05) mks MKS-RenderMain: Stopping ISBRenderer (MTLRenderer)
2025-05-02T06:28:57.217Z In(05) mks MKS-RenderMain: Stopped ISBRenderer: (MTLRenderer)
2025-05-02T06:28:57.218Z In(05) mks MKS PowerOff
2025-05-02T06:28:57.218Z In(05) svga SVGA thread is exiting
2025-05-02T06:28:57.218Z In(05) mks MKS thread is exiting
2025-05-02T06:28:57.218Z Wa(03) vmx 
2025-05-02T06:28:57.218Z In(05) vmx scsi0:0: numIOs = 33536 numMergedIOs = 10179 numSplitIOs = 878 ( 7.9%)
2025-05-02T06:28:57.218Z In(05) vmx Closing disk 'scsi0:0'
2025-05-02T06:28:57.335Z In(05) deviceThread Device thread is exiting
2025-05-02T06:28:57.335Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2025-05-02T06:28:57.335Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-05-02T06:28:57.335Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-05-02T06:28:57.336Z In(05) vmx WORKER: asyncOps=35793 maxActiveOps=17 maxPending=33 maxCompleted=29
2025-05-02T06:28:57.336Z In(05) PowerNotifyThread PowerNotify thread exiting.
2025-05-02T06:28:57.337Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:28:57.338Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:28:57.338Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 10CD86000.
2025-05-02T06:28:57.338Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2025-05-02T06:28:57.340Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:28:57.340Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:28:57.340Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:28:57.341Z In(05) vmx VMX idle exit
2025-05-02T06:28:57.341Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 12
2025-05-02T06:28:57.609Z In(05) vmx Services_Exit: Closed the services.
2025-05-02T06:28:57.610Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-05-02T06:28:57.610Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-05-02T06:28:57.611Z In(05) vmx Flushing VMX VMDB connections
2025-05-02T06:28:57.611Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-05-02T06:28:57.611Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-05-02T06:28:57.612Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-05-02T06:28:57.612Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 16
2025-05-02T06:28:57.612Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 15
2025-05-02T06:28:57.616Z In(05) vmx VMX exit (0).
2025-05-02T06:28:57.616Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-05-02T06:28:57.616Z In(05) vmx AIOMGR-S : stat o=2 r=18 w=1 i=0 br=57840 bw=128
