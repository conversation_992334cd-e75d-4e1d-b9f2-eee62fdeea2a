.encoding = "UTF-8"
displayname = "cloudera-quickstart-vm-5.13.0-0-virtualbox"
guestos = "other"
virtualhw.version = "21"
config.version = "8"
numvcpus = "2"
memsize = "8192"
cpuid.numSMT = "1"
numa.vcpu.coresPerNode = "0"
pciBridge0.present = "TRUE"
pciBridge4.present = "TRUE"
pciBridge4.virtualDev = "pcieRootPort"
pciBridge4.functions = "8"
pciBridge5.present = "TRUE"
pciBridge5.virtualDev = "pcieRootPort"
pciBridge5.functions = "8"
pciBridge6.present = "TRUE"
pciBridge6.virtualDev = "pcieRootPort"
pciBridge6.functions = "8"
pciBridge7.present = "TRUE"
pciBridge7.virtualDev = "pcieRootPort"
pciBridge7.functions = "8"
vmci0.present = "TRUE"
ide0:0.present = "TRUE"
ide0:0.deviceType = "disk"
ide0:0.fileName = "cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk"
ide0:0.allowguestconnectioncontrol = "false"
ide0:0.mode = "persistent"
ide1:0.clientDevice = "FALSE"
ide1:0.present = "TRUE"
ide1:0.deviceType = "atapi-cdrom"
ide1:0.autodetect = "TRUE"
ide1:0.startConnected = "TRUE"
ide1:0.allowguestconnectioncontrol = "true"
ethernet0.present = "TRUE"
ethernet0.virtualDev = "e1000"
ethernet0.connectionType = "bridged"
ethernet0.startConnected = "TRUE"
ethernet0.addressType = "generated"
ethernet0.wakeonpcktrcv = "true"
ethernet0.allowguestconnectioncontrol = "true"
toolscripts.afterpoweron = "true"
toolscripts.afterresume = "true"
toolscripts.beforepoweroff = "true"
toolscripts.beforesuspend = "true"
extendedConfigFile = "cloudera-quickstart-vm-5.13.0-0-virtualbox.vmxf"
virtualHW.productCompatibility = "hosted"
floppy0.present = "FALSE"
vmxstats.filename = "cloudera-quickstart-vm-5.13.0-0-virtualbox.scoreboard"
numa.autosize.cookie = "20012"
numa.autosize.vcpu.maxPerVirtualNode = "2"
uuid.bios = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
uuid.location = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
pciBridge0.pciSlotNumber = "17"
pciBridge4.pciSlotNumber = "21"
pciBridge5.pciSlotNumber = "22"
pciBridge6.pciSlotNumber = "23"
pciBridge7.pciSlotNumber = "24"
ethernet0.pciSlotNumber = "32"
ide0:0.redo = ""
svga.vramSize = "268435456"
vmotion.checkpointFBSize = "134217728"
vmotion.checkpointSVGAPrimarySize = "268435456"
vmotion.svga.mobMaxSize = "1073741824"
vmotion.svga.graphicsMemoryKB = "8388608"
ethernet0.generatedAddress = "00:0c:29:47:6e:7b"
ethernet0.generatedAddressOffset = "0"
vmci0.id = "-783847813"
monitor.phys_bits_used = "45"
cleanShutdown = "TRUE"
softPowerOff = "TRUE"
checkpoint.vmState = ""
tools.syncTime = "FALSE"
cpuid.coresPerSocket = "1"
vhv.enable = "TRUE"
vvtd.enable = "TRUE"
mks.enable3d = "TRUE"
svga.graphicsMemoryKB = "8388608"
gui.fitGuestUsingNativeDisplayResolution = "FALSE"
keyboardAndMouseProfile = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
tools.upgrade.policy = "useGlobal"
vmotion.svga.supports3D = "1"
vmotion.svga.baseCapsLevel = "9"
vmotion.svga.maxPointSize = "189"
vmotion.svga.maxTextureSize = "16384"
vmotion.svga.maxVolumeExtent = "2048"
vmotion.svga.maxTextureAnisotropy = "16"
vmotion.svga.lineStipple = "0"
vmotion.svga.dxMaxConstantBuffers = "15"
vmotion.svga.dxProvokingVertex = "0"
vmotion.svga.sm41 = "1"
vmotion.svga.multisample2x = "1"
vmotion.svga.multisample4x = "1"
vmotion.svga.msFullQuality = "1"
vmotion.svga.logicOps = "1"
vmotion.svga.bc67 = "9"
vmotion.svga.sm5 = "1"
vmotion.svga.multisample8x = "1"
vmotion.svga.logicBlendOps = "0"
vmotion.svga.maxForcedSampleCount = "8"
vmotion.svga.gl43 = "1"
