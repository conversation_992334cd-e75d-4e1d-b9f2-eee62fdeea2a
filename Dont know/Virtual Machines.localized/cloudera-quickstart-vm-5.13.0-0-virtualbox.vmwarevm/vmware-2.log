2024-06-11T05:30:23.487Z In(05) vmx Log for VMware Fusion pid=1907 version=13.5.2 build=build-23775688 option=Release
2024-06-11T05:30:23.487Z In(05) vmx The host is x86_64.
2024-06-11T05:30:23.487Z In(05) vmx Host codepage=UTF-8 encoding=UTF-8
2024-06-11T05:30:23.487Z In(05) vmx Host is macOS 15.0 (24A5264n) Darwin 24.0.0
2024-06-11T05:30:23.487Z In(05) vmx Host offset from UTC is +05:30.
2024-06-11T05:30:23.451Z In(05) vmx VTHREAD 140704288948032 "vmx" tid 33289
2024-06-11T05:30:23.457Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2024-06-11T05:30:23.458Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2024-06-11T05:30:23.458Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.458Z In(05) vmx Msg_Reset:
2024-06-11T05:30:23.458Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.458Z In(05) vmx ----------------------------------------
2024-06-11T05:30:23.458Z In(05) vmx ConfigDB: Failed to load /Library/Preferences/VMware Fusion/config
2024-06-11T05:30:23.458Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2024-06-11T05:30:23.458Z In(05) vmx Msg_Reset:
2024-06-11T05:30:23.458Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2024-06-11T05:30:23.458Z In(05) vmx ----------------------------------------
2024-06-11T05:30:23.458Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config
2024-06-11T05:30:23.458Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2024-06-11T05:30:23.458Z In(05) vmx Msg_Reset:
2024-06-11T05:30:23.458Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2024-06-11T05:30:23.458Z In(05) vmx ----------------------------------------
2024-06-11T05:30:23.458Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings
2024-06-11T05:30:23.458Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.458Z In(05) vmx Msg_Reset:
2024-06-11T05:30:23.458Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.458Z In(05) vmx ----------------------------------------
2024-06-11T05:30:23.458Z In(05) vmx ConfigDB: Failed to load ~/Library/Preferences/VMware Fusion/config
2024-06-11T05:30:23.473Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2024-06-11T05:30:23.474Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx PREF Optional preferences file not found at /Library/Preferences/VMware Fusion/config. Using default values.
2024-06-11T05:30:23.474Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/settings. Using default values.
2024-06-11T05:30:23.474Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/config. Using default values.
2024-06-11T05:30:23.474Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2024-06-11T05:30:23.474Z In(05) vmx PREF Optional preferences file not found at /Users/<USER>/Library/Preferences/VMware Fusion/config. Using default values.
2024-06-11T05:30:23.484Z In(05) vmx lib/ssl: OpenSSL using default provider
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: Client usage
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol list tls1.2
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: Server usage
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol list tls1.2
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2024-06-11T05:30:23.486Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-06-11T05:30:23.490Z In(05) vmx Hostname=Rohans-MacBook-Pro.local
2024-06-11T05:30:23.490Z In(05) vmx IP=127.0.0.1 (lo0)
2024-06-11T05:30:23.490Z In(05) vmx IP=::1 (lo0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::1 (lo0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::aede:48ff:fe00:1122 (en5)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::14b8:8dff:fe35:e07c (ap1)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::18aa:aacb:cdd2:a7f6 (en0)
2024-06-11T05:30:23.490Z In(05) vmx IP=************** (en0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::6858:daff:fe91:524 (awdl0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::6858:daff:fe91:524 (llw0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::526c:5490:8a94:76fd (utun1)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::a9b7:5be:ffc7:ee36 (utun2)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::ce81:b1c:bd2c:69e (utun3)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::30ad:85d5:7534:25ba (utun0)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::e6cf:4a55:dd5f:3715 (utun4)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::a35a:fe66:c4f5:5ab3 (utun5)
2024-06-11T05:30:23.490Z In(05) vmx IP=************ (bridge100)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c64 (bridge100)
2024-06-11T05:30:23.490Z In(05) vmx IP=************* (bridge101)
2024-06-11T05:30:23.490Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c65 (bridge101)
2024-06-11T05:30:23.490Z In(05) vmx System uptime ********** us
2024-06-11T05:30:23.491Z In(05) vmx Command line: "/Applications/VMware Fusion.app/Contents/Library/vmware-vmx" "-E" "en" "-s" "vmx.stdio.keep=TRUE" "-#" "product=64;name=VMware Fusion;version=13.5.2;buildnumber=23775688;licensename=VMware Fusion for Mac OS;licenseversion=13.0;" "-@" "duplex=3;msgs=ui" "-D" "4" "/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx"
2024-06-11T05:30:23.491Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2024-06-11T05:30:23.491Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2024-06-11T05:30:23.660Z In(05) vmx Duplex socket: 3
2024-06-11T05:30:23.689Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 16
2024-06-11T05:30:23.689Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 19
2024-06-11T05:30:23.690Z In(05) vmx VigorTransport listening on fd 20
2024-06-11T05:30:23.690Z In(05) vmx Vigor_Init 1
2024-06-11T05:30:23.690Z In(05) vmx Connecting 'ui' to fd '3' with user '(null)'
2024-06-11T05:30:23.690Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2024-06-11T05:30:23.693Z In(05) vmx /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx: Setup symlink /var/run/vmware/2c91642b75451fe8cbda98ac11d1dd98659dbddf6411ce9de3ebb434dd8daba8 -> /var/run/vmware/501/1003415954_1907
2024-06-11T05:30:23.693Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2024-06-11T05:30:23.694Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2024-06-11T05:30:23.700Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-06-11T05:30:23.700Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-06-11T05:30:23.700Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-06-11T05:30:23.700Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2024-06-11T05:30:23.700Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2024-06-11T05:30:23.723Z In(05) vmx Services_Init: Successfully opened the services.
2024-06-11T05:30:23.724Z In(05) vmx FeatureCompat: No EVC masks.
2024-06-11T05:30:23.724Z In(05) vmx hostCPUID vendor: GenuineIntel
2024-06-11T05:30:23.724Z In(05) vmx hostCPUID family: 0x6 model: 0x9e stepping: 0xa
2024-06-11T05:30:23.724Z In(05) vmx hostCPUID codename: Coffee Lake-S/H
2024-06-11T05:30:23.724Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000000, 0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000001, 0: 0x000906ea 0x04100800 0x7ffafbff 0xbfebfbff
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000002, 0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11142120
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000006, 0: 0x000026f7 0x00000002 0x00000009 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300404 0x00000000 0x00000000 0x00000603
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000004
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000004
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000004
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 0: 0x0000001f 0x00000340 0x00000440 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000340 0x00000100 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000040 0x000003c0 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000040 0x00000400 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x000000d8 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 00000016, 0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000003, 0: 0x37692029 0x3538382d 0x43204830 0x40205550
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000004, 0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-06-11T05:30:23.725Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR       0x3a =                0x5
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x480 =   0xda040000000004
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x481 =       0x7f0000003f
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x482 = 0xfdf9fffe9500697a
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x483 =   0x737fff00236fff
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x484 =     0xb3ff000091ff
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x486 =         0x80000021
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x488 =             0x2000
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x489 =           0x3767ff
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48a =               0x2e
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48b =   0x515cef000000a2
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48d =       0x7f00000016
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x48f =  0x1ffffff00036dfb
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x490 =    0x3ffff000011fb
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x491 =                  0
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x492 =                  0
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR 0xc0010114 =                  0
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR       0xce =         0x80000000
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x10a =          0x2000c04
2024-06-11T05:30:23.726Z In(05) vmx Common: MSR      0x122 =                  0
2024-06-11T05:30:23.726Z In(05) vmx UTIL: Current file descriptor limit: soft 12800, hard 4294967295.
2024-06-11T05:30:23.726Z In(05) vmx TSC Hz estimates: vmmon 0, cpuinfo 0, cpufreq 0 sysctlfreq 2592000000. Using 2592000000 Hz
2024-06-11T05:30:23.726Z In(05) vmx PTSC: RefClockToPTSC 0 @ 2592000000Hz -> 0 @ 2592000000Hz
2024-06-11T05:30:23.726Z In(05) vmx PTSC: RefClockToPTSC ((x * 2147483648) >> 31) + -2546969699160
2024-06-11T05:30:23.726Z In(05) vmx PTSC: tscOffset 0
2024-06-11T05:30:23.726Z In(05) vmx PTSC: using user-level reference clock
2024-06-11T05:30:23.726Z In(05) vmx PTSC: hardware TSCs are synchronized.
2024-06-11T05:30:23.726Z In(05) vmx PTSC: current PTSC=144838
2024-06-11T05:30:23.730Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 26
2024-06-11T05:30:23.731Z In(05) vmx Current Display Settings:
2024-06-11T05:30:23.731Z In(05) vmx    Display: 0 size: 1920x1200 pixelSize: 3840x2400 position: (0, 0) Primary
2024-06-11T05:30:23.733Z In(05) vmx [106057000-107177000): /Applications/VMware Fusion.app/Contents/Library/vmware-vmx
2024-06-11T05:30:23.766Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2024-06-11T05:30:23.766Z In(05) vmx changing directory to /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/.
2024-06-11T05:30:23.767Z In(05) vmx Config file: /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx
2024-06-11T05:30:23.767Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-06-11T05:30:23.767Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2024-06-11T05:30:23.767Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.000238 seconds.
2024-06-11T05:30:23.768Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.001159 seconds.
2024-06-11T05:30:23.786Z Wa(03) vmx PowerOn
2024-06-11T05:30:23.786Z In(05) vmx VMX_PowerOn: VMX build 23775688, UI build 23775688
2024-06-11T05:30:23.786Z In(05) vmx Processor affinity not supported on this host OS
2024-06-11T05:30:23.792Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2024-06-11T05:30:23.794Z In(05) vmx VMXSTATS: Successfully created stats file 'cloudera-quickstart-vm-5.13.0-0-virtualbox.scoreboard'
2024-06-11T05:30:23.794Z In(05) vmx VMXSTATS: Update Product Information: VMware Fusion	13.5.2	build-23775688	Release  TotalBlockSize: 48
2024-06-11T05:30:23.794Z In(05) vmx HOST sysname Darwin, nodename Rohans-MacBook-Pro.local, release 24.0.0, version Darwin Kernel Version 24.0.0: Thu May 30 21:30:05 PDT 2024; root:xnu-11215.0.31.511.2~1/RELEASE_X86_64, machine x86_64
2024-06-11T05:30:23.794Z In(05) vmx DICT --- GLOBAL SETTINGS /Applications/VMware Fusion.app/Contents/Library/settings
2024-06-11T05:30:23.794Z In(05) vmx DICT --- NON PERSISTENT (null)
2024-06-11T05:30:23.794Z In(05) vmx DICT --- USER PREFERENCES /Users/<USER>/Library/Preferences/VMware Fusion/preferences
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.dataCollectionEnabled.epoch = ""
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.dataCollectionEnabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.defaultProfileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.maxProfiles = "4"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileKey = "5202618b-19c9-7a89-903f-4ee6655cb796"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileName = "Windows 10 Profile"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileType = "windows9"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableOSShortcuts = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableKeyMappings = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.languageSpecificKeyMappingsEnabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.selectedLanguage = ""
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.cmdKeyFilterType = "none"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.maxMappings = "30"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.mappingKey = "0"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.fromHost = "GUI Z"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.toGuest = "CONTROL Z"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.mappingKey = "1"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.fromHost = "GUI X"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.toGuest = "CONTROL X"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.mappingKey = "2"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.fromHost = "GUI C"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.toGuest = "CONTROL C"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.mappingKey = "3"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.fromHost = "GUI V"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.toGuest = "CONTROL V"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.mappingKey = "4"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.fromHost = "GUI P"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.toGuest = "CONTROL P"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.mappingKey = "5"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.fromHost = "GUI A"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.toGuest = "CONTROL A"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.mappingKey = "6"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.fromHost = "GUI S"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.toGuest = "CONTROL S"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.mappingKey = "7"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.fromHost = "GUI F"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.toGuest = "0x03d"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.mappingKey = "8"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.fromHost = "GUI W"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.toGuest = "ALT 0x03e"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.mappingKey = "9"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.fromHost = "CONTROL ALT 0xe"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.toGuest = "CONTROL ALT 0x153"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.mappingKey = "10"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.enabled = "FALSE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.fromHost = "0x11c"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.toGuest = "0x138"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.mappingKey = "11"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.fromHost = "GUI SHIFT H"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.toGuest = "GUI H"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.mappingKey = "12"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.fromHost = "GUI SHIFT M"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.toGuest = "GUI M"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.mappingKey = "13"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.fromHost = "GUI SHIFT P"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.toGuest = "GUI P"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.mappingKey = "14"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.fromHost = "GUI SHIFT V"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.toGuest = "GUI V"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.mappingKey = "15"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.fromHost = "GUI SHIFT X"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.toGuest = "GUI X"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.mappingKey = "16"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.fromHost = "GUI SHIFT Z"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.toGuest = "GUI Z"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.mappingKey = "17"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.fromHost = "Mouse1 Control"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.toGuest = "Mouse2"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.mappingKey = "18"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.fromHost = "Mouse1 GUI"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.toGuest = "Mouse3"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.mappingKey = "19"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.fromHost = "GUI CONTROL"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.toGuest = "Ungrab"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.mappingKey = "20"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.fromHost = "GUI CONTROL F"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.toGuest = "Fullscreen"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.mappingKey = "21"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.fromHost = "GUI SHIFT U"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.toGuest = "Unity"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.mappingKey = "22"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.fromHost = "GUI CONTROL S"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.toGuest = "SingleWindow"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.mappingKey = "23"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.fromHost = "GUI 0x029"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.toGuest = "CycleWindow"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.mappingKey = "24"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.fromHost = "GUI SHIFT 0x029"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.toGuest = "CycleWindowReverse"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.mappingKey = "25"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.fromHost = "GUI H"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.toGuest = "HideApplication"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.mappingKey = "26"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.enabled = "TRUE"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.fromHost = "GUI M"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.toGuest = "MinimizeWindow"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.mappingKey = "27"
2024-06-11T05:30:23.794Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.fromHost = "GUI Q"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.toGuest = "Quit"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.mappingKey = "28"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.fromHost = "GUI ALT SHIFT M"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.toGuest = "ToggleHideMenu"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.mappingKey = "29"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.fromHost = "GUI E"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.toGuest = "Settings"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileKey = "521d2205-554c-06fa-8306-642f387be31a"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileName = "Mac Profile"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileType = "mac"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableOSShortcuts = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableKeyMappings = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.languageSpecificKeyMappingsEnabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.selectedLanguage = ""
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.cmdKeyFilterType = "none"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.maxMappings = "14"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.mappingKey = "0"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.fromHost = "Mouse1 Control"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.toGuest = "Mouse2"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.mappingKey = "1"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.fromHost = "Mouse1 GUI"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.toGuest = "Mouse3"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.mappingKey = "2"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.fromHost = "GUI CONTROL"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.toGuest = "Ungrab"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.mappingKey = "3"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.fromHost = "GUI CONTROL F"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.toGuest = "Fullscreen"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.mappingKey = "4"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.fromHost = "GUI CONTROL U"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.toGuest = "Unity"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.mappingKey = "5"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.fromHost = "GUI SHIFT U"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.toGuest = "Unity"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.mappingKey = "6"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.fromHost = "GUI CONTROL S"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.toGuest = "SingleWindow"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.mappingKey = "7"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.fromHost = "GUI 0x029"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.toGuest = "CycleWindow"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.mappingKey = "8"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.fromHost = "GUI SHIFT 0x029"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.toGuest = "CycleWindowReverse"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.mappingKey = "9"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.fromHost = "GUI H"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.toGuest = "HideApplication"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.mappingKey = "10"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.fromHost = "GUI M"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.toGuest = "MinimizeWindow"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.mappingKey = "11"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.fromHost = "GUI Q"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.toGuest = "Quit"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.mappingKey = "12"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.fromHost = "GUI SHIFT M"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.toGuest = "ToggleHideMenu"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.mappingKey = "13"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.fromHost = "GUI E"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.toGuest = "Settings"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileKey = "52312ba5-d8c9-0a91-aaed-ebeb665caf10"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileName = "Windows 8 Profile"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileType = "windows8"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableOSShortcuts = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableKeyMappings = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.languageSpecificKeyMappingsEnabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.selectedLanguage = ""
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.cmdKeyFilterType = "none"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.maxMappings = "33"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.mappingKey = "0"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.fromHost = "GUI Z"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.toGuest = "CONTROL Z"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.mappingKey = "1"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.fromHost = "GUI X"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.toGuest = "CONTROL X"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.mappingKey = "2"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.fromHost = "GUI C"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.toGuest = "CONTROL C"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.mappingKey = "3"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.fromHost = "GUI V"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.toGuest = "CONTROL V"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.mappingKey = "4"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.fromHost = "GUI P"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.toGuest = "CONTROL P"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.mappingKey = "5"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.fromHost = "GUI A"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.toGuest = "CONTROL A"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.mappingKey = "6"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.fromHost = "GUI S"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.toGuest = "CONTROL S"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.mappingKey = "7"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.fromHost = "GUI F"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.toGuest = "0x03d"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.mappingKey = "8"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.fromHost = "GUI W"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.toGuest = "ALT 0x03e"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.mappingKey = "9"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.fromHost = "CONTROL ALT 0xe"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.toGuest = "CONTROL ALT 0x153"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.mappingKey = "10"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.enabled = "FALSE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.fromHost = "0x11c"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.toGuest = "0x138"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.mappingKey = "11"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.fromHost = "GUI SHIFT C"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.toGuest = "GUI C"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.mappingKey = "12"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.fromHost = "GUI SHIFT H"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.toGuest = "GUI H"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.mappingKey = "13"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.fromHost = "GUI SHIFT M"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.toGuest = "GUI M"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.mappingKey = "14"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.enabled = "TRUE"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.fromHost = "GUI SHIFT P"
2024-06-11T05:30:23.795Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.toGuest = "GUI P"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.mappingKey = "15"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.fromHost = "GUI SHIFT F"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.toGuest = "GUI Q"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.mappingKey = "16"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.fromHost = "GUI SHIFT V"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.toGuest = "GUI V"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.mappingKey = "17"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.fromHost = "GUI SHIFT W"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.toGuest = "GUI W"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.mappingKey = "18"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.fromHost = "GUI SHIFT X"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.toGuest = "GUI X"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.mappingKey = "19"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.fromHost = "GUI SHIFT Z"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.toGuest = "GUI Z"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.mappingKey = "20"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.fromHost = "Mouse1 Control"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.toGuest = "Mouse2"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.mappingKey = "21"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.fromHost = "Mouse1 GUI"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.toGuest = "Mouse3"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.mappingKey = "22"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.fromHost = "GUI CONTROL"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.toGuest = "Ungrab"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.mappingKey = "23"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.fromHost = "GUI CONTROL F"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.toGuest = "Fullscreen"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.mappingKey = "24"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.fromHost = "GUI SHIFT U"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.toGuest = "Unity"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.mappingKey = "25"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.fromHost = "GUI CONTROL S"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.toGuest = "SingleWindow"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.mappingKey = "26"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.fromHost = "GUI 0x029"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.toGuest = "CycleWindow"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.mappingKey = "27"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.fromHost = "GUI SHIFT 0x029"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.toGuest = "CycleWindowReverse"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.mappingKey = "28"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.fromHost = "GUI H"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.toGuest = "HideApplication"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.mappingKey = "29"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.fromHost = "GUI M"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.toGuest = "MinimizeWindow"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.mappingKey = "30"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.fromHost = "GUI Q"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.toGuest = "Quit"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.mappingKey = "31"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.fromHost = "GUI ALT SHIFT M"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.toGuest = "ToggleHideMenu"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.mappingKey = "32"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.enabled = "FALSE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.fromHost = "GUI E"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.toGuest = "Settings"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileName = "Profile"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileType = "standard"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableOSShortcuts = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableKeyMappings = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.languageSpecificKeyMappingsEnabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.selectedLanguage = ""
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.cmdKeyFilterType = "none"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.maxMappings = "25"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.mappingKey = "0"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.fromHost = "GUI Z"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.toGuest = "CONTROL Z"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.mappingKey = "1"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.fromHost = "GUI X"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.toGuest = "CONTROL X"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.mappingKey = "2"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.fromHost = "GUI C"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.toGuest = "CONTROL C"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.mappingKey = "3"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.fromHost = "GUI V"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.toGuest = "CONTROL V"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.mappingKey = "4"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.fromHost = "GUI P"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.toGuest = "CONTROL P"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.mappingKey = "5"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.fromHost = "GUI A"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.toGuest = "CONTROL A"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.mappingKey = "6"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.fromHost = "GUI S"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.toGuest = "CONTROL S"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.mappingKey = "7"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.fromHost = "GUI F"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.toGuest = "0x03d"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.mappingKey = "8"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.fromHost = "GUI W"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.toGuest = "ALT 0x03e"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.mappingKey = "9"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.fromHost = "CONTROL ALT 0xe"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.toGuest = "CONTROL ALT 0x153"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.mappingKey = "10"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.enabled = "FALSE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.fromHost = "0x11c"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.toGuest = "0x138"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.mappingKey = "11"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.fromHost = "Mouse1 Control"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.toGuest = "Mouse2"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.mappingKey = "12"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.fromHost = "Mouse1 GUI"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.toGuest = "Mouse3"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.mappingKey = "13"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.fromHost = "GUI CONTROL"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.toGuest = "Ungrab"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.mappingKey = "14"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.fromHost = "GUI CONTROL F"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.toGuest = "Fullscreen"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.mappingKey = "15"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.enabled = "TRUE"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.fromHost = "GUI CONTROL U"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.toGuest = "Unity"
2024-06-11T05:30:23.796Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.mappingKey = "16"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.fromHost = "GUI SHIFT U"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.toGuest = "Unity"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.mappingKey = "17"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.fromHost = "GUI CONTROL S"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.toGuest = "SingleWindow"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.mappingKey = "18"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.fromHost = "GUI 0x029"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.toGuest = "CycleWindow"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.mappingKey = "19"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.fromHost = "GUI SHIFT 0x029"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.toGuest = "CycleWindowReverse"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.mappingKey = "20"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.fromHost = "GUI H"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.toGuest = "HideApplication"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.mappingKey = "21"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.fromHost = "GUI M"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.toGuest = "MinimizeWindow"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.mappingKey = "22"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.fromHost = "GUI Q"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.toGuest = "Quit"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.mappingKey = "23"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.enabled = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.fromHost = "GUI SHIFT M"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.toGuest = "ToggleHideMenu"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.mappingKey = "24"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.enabled = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.fromHost = "GUI E"
2024-06-11T05:30:23.797Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.toGuest = "Settings"
2024-06-11T05:30:23.797Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1718040130"
2024-06-11T05:30:23.797Z In(05) vmx DICT         vmWizard.guestKey = "ubuntu-64"
2024-06-11T05:30:23.797Z In(05) vmx DICT             hints.hideAll = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT  hint.confirmLaxOVFImport = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT hint.loader.mitigations.wsAndFusion = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT         hint.vmui.restart = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT --- USER DEFAULTS /Users/<USER>/Library/Preferences/VMware Fusion/config
2024-06-11T05:30:23.797Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config
2024-06-11T05:30:23.797Z In(05) vmx DICT --- SITE DEFAULTS /Applications/VMware Fusion.app/Contents/Library/config
2024-06-11T05:30:23.797Z In(05) vmx DICT --- NONPERSISTENT
2024-06-11T05:30:23.797Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT             gui.available = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT --- COMMAND LINE
2024-06-11T05:30:23.797Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT             gui.available = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT --- RECORDING
2024-06-11T05:30:23.797Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT             gui.available = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT --- CONFIGURATION /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx 
2024-06-11T05:30:23.797Z In(05) vmx DICT               displayname = "cloudera-quickstart-vm-5.13.0-0-virtualbox"
2024-06-11T05:30:23.797Z In(05) vmx DICT                   guestos = "other"
2024-06-11T05:30:23.797Z In(05) vmx DICT         virtualhw.version = "21"
2024-06-11T05:30:23.797Z In(05) vmx DICT            config.version = "8"
2024-06-11T05:30:23.797Z In(05) vmx DICT                  numvcpus = "1"
2024-06-11T05:30:23.797Z In(05) vmx DICT                   memsize = "4096"
2024-06-11T05:30:23.797Z In(05) vmx DICT              cpuid.numSMT = "1"
2024-06-11T05:30:23.797Z In(05) vmx DICT    numa.vcpu.coresPerNode = "0"
2024-06-11T05:30:23.797Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2024-06-11T05:30:23.797Z In(05) vmx DICT      pciBridge4.functions = "8"
2024-06-11T05:30:23.797Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2024-06-11T05:30:23.797Z In(05) vmx DICT      pciBridge5.functions = "8"
2024-06-11T05:30:23.797Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2024-06-11T05:30:23.797Z In(05) vmx DICT      pciBridge6.functions = "8"
2024-06-11T05:30:23.797Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2024-06-11T05:30:23.797Z In(05) vmx DICT      pciBridge7.functions = "8"
2024-06-11T05:30:23.797Z In(05) vmx DICT             vmci0.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT            ide0:0.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT         ide0:0.deviceType = "disk"
2024-06-11T05:30:23.797Z In(05) vmx DICT           ide0:0.fileName = "cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk"
2024-06-11T05:30:23.797Z In(05) vmx DICT ide0:0.allowguestconnectioncontrol = "false"
2024-06-11T05:30:23.797Z In(05) vmx DICT               ide0:0.mode = "persistent"
2024-06-11T05:30:23.797Z In(05) vmx DICT       ide1:0.clientDevice = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT            ide1:0.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT         ide1:0.deviceType = "atapi-cdrom"
2024-06-11T05:30:23.797Z In(05) vmx DICT         ide1:0.autodetect = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     ide1:0.startConnected = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT ide1:0.allowguestconnectioncontrol = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT         ethernet0.present = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2024-06-11T05:30:23.797Z In(05) vmx DICT  ethernet0.connectionType = "bridged"
2024-06-11T05:30:23.797Z In(05) vmx DICT  ethernet0.startConnected = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT     ethernet0.addressType = "generated"
2024-06-11T05:30:23.797Z In(05) vmx DICT   ethernet0.wakeonpcktrcv = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT ethernet0.allowguestconnectioncontrol = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT  toolscripts.afterpoweron = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT   toolscripts.afterresume = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT toolscripts.beforepoweroff = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT toolscripts.beforesuspend = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT        extendedConfigFile = "cloudera-quickstart-vm-5.13.0-0-virtualbox.vmxf"
2024-06-11T05:30:23.797Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2024-06-11T05:30:23.797Z In(05) vmx DICT           floppy0.present = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT         vmxstats.filename = "cloudera-quickstart-vm-5.13.0-0-virtualbox.scoreboard"
2024-06-11T05:30:23.797Z In(05) vmx DICT      numa.autosize.cookie = "10012"
2024-06-11T05:30:23.797Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "1"
2024-06-11T05:30:23.797Z In(05) vmx DICT cpuid.coresPerSocket.cookie = "1"
2024-06-11T05:30:23.797Z In(05) vmx DICT                 uuid.bios = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
2024-06-11T05:30:23.797Z In(05) vmx DICT             uuid.location = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
2024-06-11T05:30:23.797Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2024-06-11T05:30:23.797Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2024-06-11T05:30:23.797Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2024-06-11T05:30:23.797Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2024-06-11T05:30:23.797Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2024-06-11T05:30:23.797Z In(05) vmx DICT   ethernet0.pciSlotNumber = "32"
2024-06-11T05:30:23.797Z In(05) vmx DICT               ide0:0.redo = ""
2024-06-11T05:30:23.797Z In(05) vmx DICT             svga.vramSize = "268435456"
2024-06-11T05:30:23.797Z In(05) vmx DICT  vmotion.checkpointFBSize = "134217728"
2024-06-11T05:30:23.797Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2024-06-11T05:30:23.797Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "268435456"
2024-06-11T05:30:23.797Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "262144"
2024-06-11T05:30:23.797Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:47:6e:7b"
2024-06-11T05:30:23.797Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2024-06-11T05:30:23.797Z In(05) vmx DICT                  vmci0.id = "-783847813"
2024-06-11T05:30:23.797Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2024-06-11T05:30:23.797Z In(05) vmx DICT             cleanShutdown = "TRUE"
2024-06-11T05:30:23.797Z In(05) vmx DICT              softPowerOff = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT        checkpoint.vmState = "cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss"
2024-06-11T05:30:23.797Z In(05) vmx DICT     gui.viewModeAtPowerOn = "fullscreen"
2024-06-11T05:30:23.797Z In(05) vmx DICT            tools.syncTime = "FALSE"
2024-06-11T05:30:23.797Z In(05) vmx DICT --- USER DEFAULTS ~/Library/Preferences/VMware Fusion/config 
2024-06-11T05:30:23.797Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config 
2024-06-11T05:30:23.797Z In(05) vmx DICT --- SITE DEFAULTS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config 
2024-06-11T05:30:23.797Z In(05) vmx DICT --- GLOBAL SETTINGS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings 
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2024-06-11T05:30:23.798Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2024-06-11T05:30:23.798Z In(05) vmx Powering on guestOS 'other' using the configuration for 'other'.
2024-06-11T05:30:23.799Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2024-06-11T05:30:23.799Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2024-06-11T05:30:23.799Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2024-06-11T05:30:23.800Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2024-06-11T05:30:23.800Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2024-06-11T05:30:23.802Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2024-06-11T05:30:23.802Z In(05) vmx Checkpointed in VMware Fusion, 13.5.2, build-23775688, Mac OS Host
2024-06-11T05:30:23.802Z In(05) vmx Resuming virtual machine from /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss with 4096 MB of memory.
2024-06-11T05:30:23.802Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-06-11T05:30:23.802Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2024-06-11T05:30:23.804Z In(05) vmx Monitor Mode: ULM
2024-06-11T05:30:23.804Z In(05) vmx MsgHint: msg.loader.mitigations.wsAndFusion
2024-06-11T05:30:23.804Z In(05)+ vmx You are running this virtual machine with side channel mitigations enabled. Side channel mitigations provide enhanced security but also lower performance.
2024-06-11T05:30:23.804Z In(05)+ vmx 
2024-06-11T05:30:23.804Z In(05)+ vmx To disable mitigations, change the side channel mitigations setting in the advanced panel of the virtual machine settings. Refer to VMware KB article 79832 at https://kb.vmware.com/s/article/79832 for more details.
2024-06-11T05:30:23.804Z In(05)+ vmx ---------------------------------------
2024-06-11T05:30:23.820Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   673930 nonpaged    35958 anonymous     7450
2024-06-11T05:30:23.820Z In(05) vmx VMMEM: Initial Reservation: 2802MB (MainMem=4096MB)
2024-06-11T05:30:23.820Z In(05) vmx numa: Resuming from checkpoint using VPD = 1
2024-06-11T05:30:23.820Z In(05) vmx llc: maximum vcpus per LLC: 1
2024-06-11T05:30:23.820Z In(05) vmx llc: vLLC size: 1
2024-06-11T05:30:23.820Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 6553 (80% of min required size 8192)
2024-06-11T05:30:23.821Z In(05) PowerNotifyThread VTHREAD 123145351131136 "PowerNotifyThread" tid 33316
2024-06-11T05:30:23.821Z In(05) PowerNotifyThread PowerNotify thread is alive.
2024-06-11T05:30:23.821Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2024-06-11T05:30:23.821Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2024-06-11T05:30:23.821Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2024-06-11T05:30:23.821Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2024-06-11T05:30:23.821Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2024-06-11T05:30:23.821Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Fusion)
2024-06-11T05:30:23.822Z In(05) vthread-33317 VTHREAD 123145351667712 "vthread-33317" tid 33317
2024-06-11T05:30:23.822Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-06-11T05:30:23.822Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-06-11T05:30:23.822Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2024-06-11T05:30:23.823Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2024-06-11T05:30:23.823Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2024-06-11T05:30:23.824Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2024-06-11T05:30:23.824Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2024-06-11T05:30:23.824Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2024-06-11T05:30:23.824Z In(05) deviceThread VTHREAD 123145352204288 "deviceThread" tid 33318
2024-06-11T05:30:23.824Z In(05) deviceThread Device thread is alive
2024-06-11T05:30:23.824Z In(05) vmx Host VT-x Capabilities:
2024-06-11T05:30:23.824Z In(05) vmx Basic VMX Information (0x00da040000000004)
2024-06-11T05:30:23.825Z In(05) vmx   VMCS revision ID                           4
2024-06-11T05:30:23.825Z In(05) vmx   VMCS region length                      1024
2024-06-11T05:30:23.825Z In(05) vmx   VMX physical-address width           natural
2024-06-11T05:30:23.825Z In(05) vmx   SMM dual-monitor mode                    yes
2024-06-11T05:30:23.825Z In(05) vmx   VMCS memory type                          WB
2024-06-11T05:30:23.825Z In(05) vmx   Advanced INS/OUTS info                   yes
2024-06-11T05:30:23.825Z In(05) vmx   True VMX MSRs                            yes
2024-06-11T05:30:23.825Z In(05) vmx   Exception Injection ignores error code    no
2024-06-11T05:30:23.825Z In(05) vmx True Pin-Based VM-Execution Controls (0x0000007f00000016)
2024-06-11T05:30:23.825Z In(05) vmx   External-interrupt exiting               {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   NMI exiting                              {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Virtual NMIs                             {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Process posted interrupts                { 0 }
2024-06-11T05:30:23.825Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2024-06-11T05:30:23.825Z In(05) vmx   Interrupt-window exiting                 {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Use TSC offsetting                       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   HLT exiting                              {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   INVLPG exiting                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   MWAIT exiting                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   RDPMC exiting                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   RDTSC exiting                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   CR3-load exiting                         {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   CR3-store exiting                        {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Activate tertiary controls               { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   CR8-load exiting                         {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   CR8-store exiting                        {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Use TPR shadow                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   NMI-window exiting                       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   MOV-DR exiting                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Unconditional I/O exiting                {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Use I/O bitmaps                          {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Monitor trap flag                        {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Use MSR bitmaps                          {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   MONITOR exiting                          {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   PAUSE exiting                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Activate secondary controls              {0,1}
2024-06-11T05:30:23.825Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x00515cef000000a2)
2024-06-11T05:30:23.825Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Enable EPT                               { 1 }
2024-06-11T05:30:23.825Z In(05) vmx   Descriptor-table exiting                 {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Enable RDTSCP                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Virtualize x2APIC mode                   { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Enable VPID                              { 1 }
2024-06-11T05:30:23.825Z In(05) vmx   WBINVD exiting                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Unrestricted guest                       { 1 }
2024-06-11T05:30:23.825Z In(05) vmx   APIC-register virtualization             { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Virtual-interrupt delivery               { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   RDRAND exiting                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Enable INVPCID                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Enable VM Functions                      { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Use VMCS shadowing                       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   ENCLS exiting                            { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   RDSEED exiting                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Enable PML                               { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   EPT-violation #VE                        { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Conceal VMX from PT                      { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   PASID translation                        { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   PT uses guest physical addresses         { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Use TSC scaling                          { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Bus lock exiting                         { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Notification VM exits                    { 0 }
2024-06-11T05:30:23.825Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2024-06-11T05:30:23.825Z In(05) vmx   LOADIWKEY exiting                          no
2024-06-11T05:30:23.825Z In(05) vmx   Enable HLAT                                no
2024-06-11T05:30:23.825Z In(05) vmx   Enable Paging-Write                        no
2024-06-11T05:30:23.825Z In(05) vmx   Enable Guest Paging Verification           no
2024-06-11T05:30:23.825Z In(05) vmx   Enable IPI Virtualization                  no
2024-06-11T05:30:23.825Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2024-06-11T05:30:23.825Z In(05) vmx True VM-Exit Controls (0x01ffffff00036dfb)
2024-06-11T05:30:23.825Z In(05) vmx   Save debug controls                      {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Host address-space size                  {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Save IA32_PAT                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Save IA32_EFER                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Save VMX-preemption timer                {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Clear IA32_BNDCFGS                       {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Clear IA32_RTIT MSR                      { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load CET state                           { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load PKRS                                { 0 }
2024-06-11T05:30:23.825Z In(05) vmx True VM-Entry Controls (0x0003ffff000011fb)
2024-06-11T05:30:23.825Z In(05) vmx   Load debug controls                      {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   IA-32e mode guest                        {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Entry to SMM                             {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_BNDCFGS                        {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_RTIT MSR                       { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load CET state                           { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2024-06-11T05:30:23.825Z In(05) vmx   Load PKRS                                { 0 }
2024-06-11T05:30:23.825Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2024-06-11T05:30:23.825Z In(05) vmx   R=0/W=0/X=1                               yes
2024-06-11T05:30:23.825Z In(05) vmx   Page-walk length 3                        yes
2024-06-11T05:30:23.825Z In(05) vmx   EPT memory type WB                        yes
2024-06-11T05:30:23.825Z In(05) vmx   2MB super-page                            yes
2024-06-11T05:30:23.825Z In(05) vmx   1GB super-page                            yes
2024-06-11T05:30:23.825Z In(05) vmx   INVEPT support                            yes
2024-06-11T05:30:23.825Z In(05) vmx   Access & Dirty Bits                       yes
2024-06-11T05:30:23.825Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2024-06-11T05:30:23.825Z In(05) vmx   Supervisor shadow-stack control            no
2024-06-11T05:30:23.825Z In(05) vmx   Type 1 INVEPT                             yes
2024-06-11T05:30:23.825Z In(05) vmx   Type 2 INVEPT                             yes
2024-06-11T05:30:23.825Z In(05) vmx   INVVPID support                           yes
2024-06-11T05:30:23.825Z In(05) vmx   Type 0 INVVPID                            yes
2024-06-11T05:30:23.825Z In(05) vmx   Type 1 INVVPID                            yes
2024-06-11T05:30:23.825Z In(05) vmx   Type 2 INVVPID                            yes
2024-06-11T05:30:23.825Z In(05) vmx   Type 3 INVVPID                            yes
2024-06-11T05:30:23.825Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2024-06-11T05:30:23.825Z In(05) vmx   TSC to preemption timer ratio      7
2024-06-11T05:30:23.825Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2024-06-11T05:30:23.825Z In(05) vmx   Activity State HLT               yes
2024-06-11T05:30:23.825Z In(05) vmx   Activity State shutdown          yes
2024-06-11T05:30:23.825Z In(05) vmx   Activity State wait-for-SIPI     yes
2024-06-11T05:30:23.825Z In(05) vmx   Processor trace in VMX           yes
2024-06-11T05:30:23.825Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2024-06-11T05:30:23.825Z In(05) vmx   CR3 targets supported              4
2024-06-11T05:30:23.825Z In(05) vmx   Maximum MSR list size            512
2024-06-11T05:30:23.825Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2024-06-11T05:30:23.825Z In(05) vmx   Allow all VMWRITEs               yes
2024-06-11T05:30:23.825Z In(05) vmx   Allow zero instruction length    yes
2024-06-11T05:30:23.825Z In(05) vmx   MSEG revision ID                   0
2024-06-11T05:30:23.825Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2024-06-11T05:30:23.826Z In(05) vmx   Fixed to 0        0xffffffff00000000
2024-06-11T05:30:23.826Z In(05) vmx   Fixed to 1        0x0000000080000021
2024-06-11T05:30:23.826Z In(05) vmx   Variable          0x000000007fffffde
2024-06-11T05:30:23.826Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x00000000003767ff)
2024-06-11T05:30:23.826Z In(05) vmx   Fixed to 0        0xffffffffffc89800
2024-06-11T05:30:23.826Z In(05) vmx   Fixed to 1        0x0000000000002000
2024-06-11T05:30:23.826Z In(05) vmx   Variable          0x00000000003747ff
2024-06-11T05:30:23.826Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2024-06-11T05:30:23.826Z In(05) vmx   Highest index                   0x17
2024-06-11T05:30:23.826Z In(05) vmx VM Functions (0x0000000000000000)
2024-06-11T05:30:23.826Z In(05) vmx KHZEstimate 2592000
2024-06-11T05:30:23.826Z In(05) vmx MHZEstimate 2592
2024-06-11T05:30:23.826Z In(05) vmx NumVCPUs 1
2024-06-11T05:30:23.826Z In(05) vmx Msg_Question:
2024-06-11T05:30:23.826Z In(05) vmx [msg.uuid.altered] This virtual machine might have been moved or copied.
2024-06-11T05:30:23.826Z In(05)+ vmx In order to configure certain management and networking features, VMware Fusion needs to know if this virtual machine was moved or copied. 
2024-06-11T05:30:23.826Z In(05)+ vmx 
2024-06-11T05:30:23.826Z In(05)+ vmx If you don't know, answer "I Co_pied It".
2024-06-11T05:30:23.826Z In(05)+ vmx 
2024-06-11T05:30:23.826Z In(05) vmx ----------------------------------------
2024-06-11T05:30:30.353Z In(05) vmx MsgQuestion: msg.uuid.altered reply=2
2024-06-11T05:30:30.353Z In(05) vmx UUID: Writing uuid.bios value: '56 4d 38 bf d9 c8 69 cf-d9 66 5d 88 ef 87 23 aa'
2024-06-11T05:30:30.353Z No(00) vmx ConfigDB: Setting uuid.bios = "56 4d 38 bf d9 c8 69 cf-d9 66 5d 88 ef 87 23 aa"
2024-06-11T05:30:30.353Z In(05) vmx UUID: Writing uuid.location value: '56 4d 38 bf d9 c8 69 cf-d9 66 5d 88 ef 87 23 aa'
2024-06-11T05:30:30.353Z No(00) vmx ConfigDB: Setting uuid.location = "56 4d 38 bf d9 c8 69 cf-d9 66 5d 88 ef 87 23 aa"
2024-06-11T05:30:30.355Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2024-06-11T05:30:30.355Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2024-06-11T05:30:30.355Z No(00) vmx PowerOnTiming: Module UUIDVMX took 6528657 us
2024-06-11T05:30:30.355Z In(05) vmx AIOGNRC: numThreads=4 ide=1, scsi=0, passthru=1
2024-06-11T05:30:30.355Z In(05) vmx WORKER: Creating new group with maxThreads=4 (4)
2024-06-11T05:30:30.358Z In(05) vmx WORKER: Creating new group with maxThreads=1 (5)
2024-06-11T05:30:30.358Z In(05) vmx MainMem: CPT Host WZ=0 PF=4096 D=0
2024-06-11T05:30:30.358Z In(05) vmx MainMem: CPT PLS=1 PLR=0 BS=1 BlkP=32 Mult=4 W=50
2024-06-11T05:30:30.358Z In(05) vmx MStat: Creating Stat vm.uptime
2024-06-11T05:30:30.358Z In(05) vmx MStat: Creating Stat vm.suspendTime
2024-06-11T05:30:30.358Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2024-06-11T05:30:30.358Z In(05) vmx VMXAIOMGR: Using: simple=Generic
2024-06-11T05:30:30.361Z In(05) vmx WORKER: Creating new group with maxThreads=1 (6)
2024-06-11T05:30:30.361Z In(05) machPoll VTHREAD 123145348984832 "machPoll" tid 33413
2024-06-11T05:30:30.364Z In(05) vmx WORKER: Creating new group with maxThreads=1 (7)
2024-06-11T05:30:30.364Z In(05) vmx WORKER: Creating new group with maxThreads=14 (21)
2024-06-11T05:30:30.365Z In(05) vmx FeatureCompat: No VM masks.
2024-06-11T05:30:30.365Z In(05) vmx TimeTracker host to guest rate conversion 17208413004 @ 2592000000Hz -> 0 @ 2592000000Hz
2024-06-11T05:30:30.365Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -17208413004
2024-06-11T05:30:30.365Z In(05) vmx Disabling TSC scaling since host does not support it.
2024-06-11T05:30:30.365Z In(05) vmx TSC offsetting enabled.
2024-06-11T05:30:30.365Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-06-11T05:30:30.365Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-06-11T05:30:30.366Z In(05) vmx MKS PowerOn
2024-06-11T05:30:30.368Z In(05) mks VTHREAD 123145354313728 "mks" tid 33415
2024-06-11T05:30:30.368Z In(05) mks MKS thread is alive
2024-06-11T05:30:30.368Z In(05) svga VTHREAD 123145358520320 "svga" tid 33416
2024-06-11T05:30:30.368Z In(05) svga SVGA thread is alive
2024-06-11T05:30:30.369Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-06-11T05:30:30.412Z In(05) mks MKS MacOSMouse: Using event tap path.
2024-06-11T05:30:30.412Z In(05) mouse VTHREAD 123145350057984 "mouse" tid 33419
2024-06-11T05:30:30.412Z In(05) keyboard VTHREAD 123145359056896 "keyboard" tid 33420
2024-06-11T05:30:30.593Z In(05) keyboard MKS MacOSKeyboard: Adding device: (0) VID:05AC PID:8600 <missing> (TouchBarUserDevice)
2024-06-11T05:30:30.595Z In(05) keyboard MKS MacOSKeyboard: Adding device: (1) VID:05AC PID:027C Apple Inc. (Apple Internal Keyboard / Trackpad)
2024-06-11T05:30:30.596Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps MTLRenderer ISBRenderer 
2024-06-11T05:30:30.596Z In(05) mks MKS-RenderMain: ISB enabled by config
2024-06-11T05:30:30.596Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MTLRenderer
2024-06-11T05:30:30.596Z In(05) mks MKS-RenderMain: Starting MTLRenderer
2024-06-11T05:30:30.609Z In(05) mks Metal Device name: AMD Radeon Pro 560X
2024-06-11T05:30:30.609Z In(05) mks Metal Device removable: FALSE
2024-06-11T05:30:30.609Z In(05) mks Metal Device headless: FALSE
2024-06-11T05:30:30.609Z In(05) mks Metal Device lowPower: FALSE
2024-06-11T05:30:30.609Z In(05) mks Metal Device maxThreadsPerThreadgroup: 1024x1024x1024
2024-06-11T05:30:30.609Z In(05) mks Metal Device recommendedMaxWorkingSetSize: 4294967296
2024-06-11T05:30:30.609Z In(05) mks Metal Device depth24Stencil8PixelFormatSupported: TRUE
2024-06-11T05:30:30.609Z In(05) mks Metal Device GPU family: 2
2024-06-11T05:30:30.609Z In(05) mks Metal Device Apple GPU family: 0
2024-06-11T05:30:30.609Z In(05) mks Metal RW Texture Tier: 2
2024-06-11T05:30:30.609Z In(05) mks Metal Arg Buffer Support: Tier 2
2024-06-11T05:30:30.610Z In(05) mks Metal Device PCI ID: 1002:67EF
2024-06-11T05:30:30.610Z In(05) mks Metal GPU chip: Baffin (GCN4)
2024-06-11T05:30:30.797Z In(05) mks Metal Device plugin: AMDMTLBronzeDriver 6.1.12 24649
2024-06-11T05:30:30.797Z In(05) mks Metal Device: Pull Model Interpolation support: No
2024-06-11T05:30:30.797Z In(05) mks Metal Shading Language: 2.3
2024-06-11T05:30:38.236Z In(05) mks MTLRenderer: Intersecting with caps of device Intel(R) UHD Graphics 630
2024-06-11T05:30:38.238Z In(05) mks Started Shim3D
2024-06-11T05:30:38.238Z In(05) mks MKS-RenderMain: Started MTLRenderer
2024-06-11T05:30:38.238Z In(05) mks MKS-RenderMain: ISB required for 3d: clamping down renderer caps
2024-06-11T05:30:38.238Z In(05) mks MKS-RenderMain: Found Full Renderer: MTLRenderer
2024-06-11T05:30:38.238Z In(05) mks MKS-RenderMain: maxTextureSize=16384
2024-06-11T05:30:38.239Z In(05) mks SOCKET 1 (60) creating new listening socket on port -1
2024-06-11T05:30:38.239Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-06-11T05:30:38.240Z In(05) mks MKSRemoteMgr: Set default display name: cloudera-quickstart-vm-5.13.0-0-virtualbox
2024-06-11T05:30:38.240Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-06-11T05:30:38.240Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2024-06-11T05:30:38.240Z No(00) vmx PowerOnTiming: Module MKS took 7873715 us
2024-06-11T05:30:38.240Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2024-06-11T05:30:38.240Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2024-06-11T05:30:38.241Z In(05) vmx Chipset version: 0x13
2024-06-11T05:30:38.495Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2024-06-11T05:30:38.495Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2024-06-11T05:30:38.495Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2024-06-11T05:30:38.495Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2024-06-11T05:30:38.496Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2024-06-11T05:30:38.496Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2024-06-11T05:30:38.496Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2024-06-11T05:30:38.496Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2024-06-11T05:30:38.509Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2024-06-11T05:30:38.509Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2024-06-11T05:30:38.509Z No(00) vmx ConfigDB: Setting ide0:0.redo = ""
2024-06-11T05:30:38.509Z In(05) vmx DISK: OPEN ide0:0 '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' persistent R[]
2024-06-11T05:30:38.512Z In(05) vmx Current OS Release is 24.0.0
2024-06-11T05:30:38.513Z In(05) vmx DISK: Disk '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' has UUID '60 00 c2 9d f9 58 79 c5-4d 5f 84 b7 6f e5 19 87'
2024-06-11T05:30:38.513Z In(05) vmx DISK: OPEN '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' Geo (133152/16/63) BIOS Geo (8354/255/63)
2024-06-11T05:30:38.514Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'ide0:0' identified as Virtual SSD device.
2024-06-11T05:30:38.514Z In(05) vmx DISK: Opening disks took 5 ms.
2024-06-11T05:30:38.514Z Wa(03) vmx USB: No USB controllers found.
2024-06-11T05:30:38.518Z In(05) vmx DISKUTIL: ide0:0 : capacity=134217728 logical sector size=512
2024-06-11T05:30:38.518Z In(05) vmx SCSI DEVICE (ide0:0): Computed value of ide0:0.useBounceBuffers: default
2024-06-11T05:30:38.518Z In(05) vmx DISKUTIL: ide0:0 : capacity=134217728 logical sector size=512
2024-06-11T05:30:38.518Z In(05) vmx DISKUTIL: ide0:0 : geometry=8354/255/63
2024-06-11T05:30:38.518Z In(05) vmx SCSI DEVICE (ide1:0): Computed value of ide1:0.useBounceBuffers: default
2024-06-11T05:30:38.518Z In(05) vmx DISKUTIL: ide1:0 : capacity=0 logical sector size=2048
2024-06-11T05:30:38.518Z In(05) vmx DISKUTIL: ide1:0 : geometry=0/0/0
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 134217728
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2024-06-11T05:30:38.519Z In(05) vmx SVGA-GFB: Max wh(6688, 5016), number of displays: 10
2024-06-11T05:30:38.519Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2024-06-11T05:30:38.519Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "268435456"
2024-06-11T05:30:38.519Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "262144"
2024-06-11T05:30:38.519Z In(05) vmx SVGA: mobMaxSize=268435456
2024-06-11T05:30:38.519Z In(05) vmx SVGA: graphicsMemoryKB=262144
2024-06-11T05:30:38.519Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 511
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 16384
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 2048
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 16
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 15
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 9
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 8
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 1
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2024-06-11T05:30:38.519Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2024-06-11T05:30:38.520Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2024-06-11T05:30:38.520Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2024-06-11T05:30:38.520Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2024-06-11T05:30:38.520Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     4)
2024-06-11T05:30:38.520Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2024-06-11T05:30:38.522Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddress = "00:0c:29:87:23:aa"
2024-06-11T05:30:38.522Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddressOffset = "0"
2024-06-11T05:30:38.522Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:87:23:aa
2024-06-11T05:30:38.524Z No(00) vmx ConfigDB: Setting vmci0.id = "-783847813"
2024-06-11T05:30:38.533Z In(05) vmx WORKER: Creating new group with maxThreads=1 (22)
2024-06-11T05:30:38.534Z In(05) vmx DISKUTIL: (null) : max toolsVersion = 0, type = 0
2024-06-11T05:30:38.534Z In(05) vmx DISKUTIL: Offline toolsVersion = 0, type = 0
2024-06-11T05:30:38.534Z In(05) vmx TOOLS setting legacy tools version to '0' type 0, manifest status is 9
2024-06-11T05:30:38.534Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2024-06-11T05:30:38.534Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2024-06-11T05:30:38.535Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2024-06-11T05:30:38.535Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2024-06-11T05:30:38.535Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2024-06-11T05:30:38.535Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2024-06-11T05:30:38.536Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2024-06-11T05:30:38.536Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2024-06-11T05:30:38.536Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2024-06-11T05:30:38.536Z In(05) vmx Tools: sending 'OS_Resume' (state = 4) state change request
2024-06-11T05:30:38.536Z In(05) vmx Tools: Delaying state change request to state 4.
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2024-06-11T05:30:38.536Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2024-06-11T05:30:38.536Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2024-06-11T05:30:38.536Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2024-06-11T05:30:38.537Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2024-06-11T05:30:38.537Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2024-06-11T05:30:38.537Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2024-06-11T05:30:38.539Z In(05) vmx CPT: Restoring checkpoint /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss
2024-06-11T05:30:38.540Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2024-06-11T05:30:38.541Z In(05) vmx PStrIntern expansion: nBkts=256
2024-06-11T05:30:38.542Z In(05) vmx Progress -1% (msg.checkpoint.restoreStatus)
2024-06-11T05:30:38.542Z In(05) vmx   restoring GuestVars
2024-06-11T05:30:38.542Z In(05) vmx   restoring A20
2024-06-11T05:30:38.542Z In(05) vmx   restoring UUIDVMX
2024-06-11T05:30:38.542Z In(05) vmx   restoring memory
2024-06-11T05:30:38.542Z In(05) vmx DUMPER: Item 'hotSetSize' [-1, -1] not found.
2024-06-11T05:30:38.542Z In(05) vmx MainMem: Opened paging file, 'UNNAMED' (swap).
2024-06-11T05:30:38.543Z In(05) vmx MainMem: Read full memory image (SF=0 L=0 SZ=1 RM=0 U=0 TM=0).
2024-06-11T05:30:38.546Z In(05) vmx Progress 0% (none)
2024-06-11T05:30:38.573Z In(05) vmx Progress 1% (none)
2024-06-11T05:30:38.602Z In(05) vmx Progress 2% (none)
2024-06-11T05:30:38.628Z In(05) vmx Progress 3% (none)
2024-06-11T05:30:38.658Z In(05) vmx Progress 4% (none)
2024-06-11T05:30:38.684Z In(05) vmx Progress 5% (none)
2024-06-11T05:30:38.711Z In(05) vmx Progress 6% (none)
2024-06-11T05:30:38.737Z In(05) vmx Progress 7% (none)
2024-06-11T05:30:38.765Z In(05) vmx Progress 8% (none)
2024-06-11T05:30:38.793Z In(05) vmx Progress 9% (none)
2024-06-11T05:30:38.818Z In(05) vmx Progress 10% (none)
2024-06-11T05:30:38.846Z In(05) vmx Progress 11% (none)
2024-06-11T05:30:38.872Z In(05) vmx Progress 12% (none)
2024-06-11T05:30:38.901Z In(05) vmx Progress 13% (none)
2024-06-11T05:30:38.927Z In(05) vmx Progress 14% (none)
2024-06-11T05:30:38.957Z In(05) vmx Progress 15% (none)
2024-06-11T05:30:38.986Z In(05) vmx Progress 16% (none)
2024-06-11T05:30:39.014Z In(05) vmx Progress 17% (none)
2024-06-11T05:30:39.043Z In(05) vmx Progress 18% (none)
2024-06-11T05:30:39.070Z In(05) vmx Progress 19% (none)
2024-06-11T05:30:39.100Z In(05) vmx Progress 20% (none)
2024-06-11T05:30:39.128Z In(05) vmx Progress 21% (none)
2024-06-11T05:30:39.158Z In(05) vmx Progress 22% (none)
2024-06-11T05:30:39.186Z In(05) vmx Progress 23% (none)
2024-06-11T05:30:39.215Z In(05) vmx Progress 24% (none)
2024-06-11T05:30:39.245Z In(05) vmx Progress 25% (none)
2024-06-11T05:30:39.272Z In(05) vmx Progress 26% (none)
2024-06-11T05:30:39.301Z In(05) vmx Progress 27% (none)
2024-06-11T05:30:39.328Z In(05) vmx Progress 28% (none)
2024-06-11T05:30:39.358Z In(05) vmx Progress 29% (none)
2024-06-11T05:30:39.385Z In(05) vmx Progress 30% (none)
2024-06-11T05:30:39.414Z In(05) vmx Progress 31% (none)
2024-06-11T05:30:39.444Z In(05) vmx Progress 32% (none)
2024-06-11T05:30:39.472Z In(05) vmx Progress 33% (none)
2024-06-11T05:30:39.502Z In(05) vmx Progress 34% (none)
2024-06-11T05:30:39.529Z In(05) vmx Progress 35% (none)
2024-06-11T05:30:39.559Z In(05) vmx Progress 36% (none)
2024-06-11T05:30:39.586Z In(05) vmx Progress 37% (none)
2024-06-11T05:30:39.615Z In(05) vmx Progress 38% (none)
2024-06-11T05:30:39.642Z In(05) vmx Progress 39% (none)
2024-06-11T05:30:39.673Z In(05) vmx Progress 40% (none)
2024-06-11T05:30:39.702Z In(05) vmx Progress 41% (none)
2024-06-11T05:30:39.730Z In(05) vmx Progress 42% (none)
2024-06-11T05:30:39.760Z In(05) vmx Progress 43% (none)
2024-06-11T05:30:39.788Z In(05) vmx Progress 44% (none)
2024-06-11T05:30:39.821Z In(05) vmx Progress 45% (none)
2024-06-11T05:30:39.850Z In(05) vmx Progress 46% (none)
2024-06-11T05:30:39.885Z In(05) vmx Progress 47% (none)
2024-06-11T05:30:39.923Z In(05) vmx Progress 48% (none)
2024-06-11T05:30:39.955Z In(05) vmx Progress 49% (none)
2024-06-11T05:30:39.988Z In(05) vmx Progress 50% (none)
2024-06-11T05:30:40.020Z In(05) vmx Progress 51% (none)
2024-06-11T05:30:40.051Z In(05) vmx Progress 52% (none)
2024-06-11T05:30:40.083Z In(05) vmx Progress 53% (none)
2024-06-11T05:30:40.119Z In(05) vmx Progress 54% (none)
2024-06-11T05:30:40.152Z In(05) vmx Progress 55% (none)
2024-06-11T05:30:40.191Z In(05) vmx Progress 56% (none)
2024-06-11T05:30:40.232Z In(05) vmx Progress 57% (none)
2024-06-11T05:30:40.273Z In(05) vmx Progress 58% (none)
2024-06-11T05:30:40.310Z In(05) vmx Progress 59% (none)
2024-06-11T05:30:40.345Z In(05) vmx Progress 60% (none)
2024-06-11T05:30:40.379Z In(05) vmx Progress 61% (none)
2024-06-11T05:30:40.413Z In(05) vmx Progress 62% (none)
2024-06-11T05:30:40.451Z In(05) vmx Progress 63% (none)
2024-06-11T05:30:40.493Z In(05) vmx Progress 64% (none)
2024-06-11T05:30:40.534Z In(05) vmx Progress 65% (none)
2024-06-11T05:30:40.577Z In(05) vmx Progress 66% (none)
2024-06-11T05:30:40.616Z In(05) vmx Progress 67% (none)
2024-06-11T05:30:40.661Z In(05) vmx Progress 68% (none)
2024-06-11T05:30:40.703Z In(05) vmx Progress 69% (none)
2024-06-11T05:30:40.756Z In(05) vmx Progress 70% (none)
2024-06-11T05:30:40.799Z In(05) vmx Progress 71% (none)
2024-06-11T05:30:40.847Z In(05) vmx Progress 72% (none)
2024-06-11T05:30:40.895Z In(05) vmx Progress 73% (none)
2024-06-11T05:30:40.931Z In(05) vmx Progress 74% (none)
2024-06-11T05:30:40.976Z In(05) vmx Progress 75% (none)
2024-06-11T05:30:41.017Z In(05) vmx Progress 76% (none)
2024-06-11T05:30:41.062Z In(05) vmx Progress 77% (none)
2024-06-11T05:30:41.103Z In(05) vmx Progress 78% (none)
2024-06-11T05:30:41.151Z In(05) vmx Progress 79% (none)
2024-06-11T05:30:41.195Z In(05) vmx Progress 80% (none)
2024-06-11T05:30:41.240Z In(05) vmx Progress 81% (none)
2024-06-11T05:30:41.290Z In(05) vmx Progress 82% (none)
2024-06-11T05:30:41.333Z In(05) vmx Progress 83% (none)
2024-06-11T05:30:41.379Z In(05) vmx Progress 84% (none)
2024-06-11T05:30:41.420Z In(05) vmx Progress 85% (none)
2024-06-11T05:30:41.464Z In(05) vmx Progress 86% (none)
2024-06-11T05:30:41.511Z In(05) vmx Progress 87% (none)
2024-06-11T05:30:41.569Z In(05) vmx Progress 88% (none)
2024-06-11T05:30:41.640Z In(05) vmx Progress 89% (none)
2024-06-11T05:30:41.683Z In(05) vmx Progress 90% (none)
2024-06-11T05:30:41.721Z In(05) vmx Progress 91% (none)
2024-06-11T05:30:41.759Z In(05) vmx Progress 92% (none)
2024-06-11T05:30:41.799Z In(05) vmx Progress 93% (none)
2024-06-11T05:30:41.834Z In(05) vmx Progress 94% (none)
2024-06-11T05:30:41.874Z In(05) vmx Progress 95% (none)
2024-06-11T05:30:41.915Z In(05) vmx Progress 96% (none)
2024-06-11T05:30:41.945Z In(05) vmx MainMem: Read 4096MiB in 3402ms (1262.21 MB/s)
2024-06-11T05:30:41.946Z In(05) vmx   restoring MStats
2024-06-11T05:30:41.946Z In(05) vmx   restoring Snapshot
2024-06-11T05:30:41.947Z In(05) vmx   restoring pic
2024-06-11T05:30:41.947Z In(05) vmx   restoring ide0:0
2024-06-11T05:30:41.947Z In(05) vmx   restoring ide1:0
2024-06-11T05:30:41.947Z In(05) vmx   restoring FeatureCompat
2024-06-11T05:30:41.947Z In(05) vmx   restoring TimeTracker
2024-06-11T05:30:41.947Z In(05) vmx TimeTracker host to guest rate conversion 17208413004 @ 2592000000Hz -> 774358403678 @ 2592000000Hz
2024-06-11T05:30:41.947Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + 757149990674
2024-06-11T05:30:41.947Z In(05) vmx Disabling TSC scaling since host does not support it.
2024-06-11T05:30:41.947Z In(05) vmx TSC offsetting enabled.
2024-06-11T05:30:41.947Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-06-11T05:30:41.947Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-06-11T05:30:41.947Z In(05) vmx   restoring Backdoor
2024-06-11T05:30:41.947Z In(05) vmx   restoring PCI
2024-06-11T05:30:41.947Z In(05) vmx   restoring ExtCfgDevice
2024-06-11T05:30:41.947Z In(05) vmx   restoring Cs440bx
2024-06-11T05:30:41.948Z In(05) vmx DUMPER: Item 'gpe.status' [2, -1] not found.
2024-06-11T05:30:41.948Z In(05) vmx DUMPER: Item 'gpe.enable' [2, -1] not found.
2024-06-11T05:30:41.948Z In(05) vmx   restoring AcpiNotify
2024-06-11T05:30:41.948Z In(05) vmx   restoring vcpuHotPlug
2024-06-11T05:30:41.949Z In(05) vmx   restoring MemoryHotplug
2024-06-11T05:30:41.949Z In(05) vmx   restoring devHP
2024-06-11T05:30:41.949Z In(05) vmx   restoring ACPIWake
2024-06-11T05:30:41.949Z In(05) vmx   restoring OEMDevice
2024-06-11T05:30:41.949Z In(05) vmx   restoring HotButton
2024-06-11T05:30:41.949Z In(05) vmx   restoring Timer
2024-06-11T05:30:41.949Z In(05) vmx   restoring ACPI
2024-06-11T05:30:41.949Z In(05) vmx   restoring XPMode
2024-06-11T05:30:41.949Z In(05) vmx   restoring DMA
2024-06-11T05:30:41.949Z In(05) vmx   restoring BackdoorAPM
2024-06-11T05:30:41.949Z In(05) vmx   restoring smram
2024-06-11T05:30:41.949Z In(05) vmx   restoring backdoorAbsMouse
2024-06-11T05:30:41.949Z In(05) vmx   restoring Keyboard
2024-06-11T05:30:41.949Z In(05) vmx   restoring SIO
2024-06-11T05:30:41.949Z In(05) vmx   restoring monitorLate
2024-06-11T05:30:41.949Z In(05) vmx   restoring vcpuNUMA
2024-06-11T05:30:41.949Z In(05) vmx   restoring devices
2024-06-11T05:30:41.949Z In(05) vmx   restoring configdbFT
2024-06-11T05:30:41.949Z In(05) vmx   restoring DevicesPowerOn
2024-06-11T05:30:41.949Z In(05) vmx   restoring PCIBridge0
2024-06-11T05:30:41.949Z In(05) vmx   restoring PCIBridge4
2024-06-11T05:30:41.949Z In(05) vmx   restoring pciBridge4:1
2024-06-11T05:30:41.949Z In(05) vmx   restoring pciBridge4:2
2024-06-11T05:30:41.949Z In(05) vmx   restoring pciBridge4:3
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge4:4
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge4:5
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge4:6
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge4:7
2024-06-11T05:30:41.950Z In(05) vmx   restoring PCIBridge5
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:1
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:2
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:3
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:4
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:5
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:6
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge5:7
2024-06-11T05:30:41.950Z In(05) vmx   restoring PCIBridge6
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:1
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:2
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:3
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:4
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:5
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:6
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge6:7
2024-06-11T05:30:41.950Z In(05) vmx   restoring PCIBridge7
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge7:1
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge7:2
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge7:3
2024-06-11T05:30:41.950Z In(05) vmx   restoring pciBridge7:4
2024-06-11T05:30:41.951Z In(05) vmx   restoring pciBridge7:5
2024-06-11T05:30:41.951Z In(05) vmx   restoring pciBridge7:6
2024-06-11T05:30:41.951Z In(05) vmx   restoring pciBridge7:7
2024-06-11T05:30:41.951Z In(05) vmx   restoring Migrate
2024-06-11T05:30:41.951Z In(05) vmx   restoring vide
2024-06-11T05:30:41.951Z In(05) vmx DUMPER: Block item 'monbuf' [0, -1] not found.
2024-06-11T05:30:41.951Z In(05) vmx DUMPER: Block item 'monbuf' [1, -1] not found.
2024-06-11T05:30:41.951Z In(05) vmx   restoring VGA
2024-06-11T05:30:41.952Z In(05) vmx   restoring SVGA
2024-06-11T05:30:41.952Z In(05) vmx SVGA: Guest reported SVGA driver: (0, 0, 0, 0)
2024-06-11T05:30:41.952Z In(05) vmx SVGA-GFB: Allocated gfbSize=134217728
2024-06-11T05:30:41.952Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "134217728"
2024-06-11T05:30:41.952Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2024-06-11T05:30:42.056Z In(05) vmx Progress 97% (none)
2024-06-11T05:30:42.079Z In(05) vmx Progress 98% (none)
2024-06-11T05:30:42.101Z In(05) vmx Progress 99% (none)
2024-06-11T05:30:42.121Z In(05) vmx SVGA3dCaps: guest, saved in checkpoint
2024-06-11T05:30:42.121Z In(05) vmx   cap[  0]: 0x00000000 (3D)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  1]: 0x00000000 (MAX_LIGHTS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  2]: 0x00000000 (MAX_TEXTURES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  3]: 0x00000000 (MAX_CLIP_PLANES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  4]: 0x00000000 (VERTEX_SHADER_VERSION)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  5]: 0x00000000 (VERTEX_SHADER)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  6]: 0x00000000 (FRAGMENT_SHADER_VERSION)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  7]: 0x00000000 (FRAGMENT_SHADER)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  8]: 0x00000000 (MAX_RENDER_TARGETS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[  9]: 0x00000000 (S23E8_TEXTURES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 10]: 0x00000000 (S10E5_TEXTURES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 11]: 0x00000000 (MAX_FIXED_VERTEXBLEND)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 12]: 0x00000000 (D16_BUFFER_FORMAT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 13]: 0x00000000 (D24S8_BUFFER_FORMAT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 14]: 0x00000000 (D24X8_BUFFER_FORMAT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 15]: 0x00000000 (QUERY_TYPES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 16]: 0x00000000 (TEXTURE_GRADIENT_SAMPLING)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 17]:   0.000000 (MAX_POINT_SIZE)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 18]: 0x00000000 (MAX_SHADER_TEXTURES)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 21]: 0x00000000 (MAX_VOLUME_EXTENT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 22]: 0x00000000 (MAX_TEXTURE_REPEAT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 23]: 0x00000000 (MAX_TEXTURE_ASPECT_RATIO)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 24]: 0x00000000 (MAX_TEXTURE_ANISOTROPY)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 25]: 0x00000000 (MAX_PRIMITIVE_COUNT)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 26]: 0x00000000 (MAX_VERTEX_INDEX)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 27]: 0x00000000 (MAX_VERTEX_SHADER_INSTRUCTIONS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 28]: 0x00000000 (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 29]: 0x00000000 (MAX_VERTEX_SHADER_TEMPS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 30]: 0x00000000 (MAX_FRAGMENT_SHADER_TEMPS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 31]: 0x00000000 (TEXTURE_OPS)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 32]: 0x00000000 (SURFACEFMT_X8R8G8B8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 33]: 0x00000000 (SURFACEFMT_A8R8G8B8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 34]: 0x00000000 (SURFACEFMT_A2R10G10B10)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 35]: 0x00000000 (SURFACEFMT_X1R5G5B5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 36]: 0x00000000 (SURFACEFMT_A1R5G5B5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 37]: 0x00000000 (SURFACEFMT_A4R4G4B4)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 38]: 0x00000000 (SURFACEFMT_R5G6B5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 39]: 0x00000000 (SURFACEFMT_LUMINANCE16)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 40]: 0x00000000 (SURFACEFMT_LUMINANCE8_ALPHA8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 41]: 0x00000000 (SURFACEFMT_ALPHA8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 42]: 0x00000000 (SURFACEFMT_LUMINANCE8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 43]: 0x00000000 (SURFACEFMT_Z_D16)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 44]: 0x00000000 (SURFACEFMT_Z_D24S8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 45]: 0x00000000 (SURFACEFMT_Z_D24X8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 46]: 0x00000000 (SURFACEFMT_DXT1)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 47]: 0x00000000 (SURFACEFMT_DXT2)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 48]: 0x00000000 (SURFACEFMT_DXT3)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 49]: 0x00000000 (SURFACEFMT_DXT4)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 50]: 0x00000000 (SURFACEFMT_DXT5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 51]: 0x00000000 (SURFACEFMT_BUMPX8L8V8U8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 52]: 0x00000000 (SURFACEFMT_A2W10V10U10)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 53]: 0x00000000 (SURFACEFMT_BUMPU8V8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 54]: 0x00000000 (SURFACEFMT_Q8W8V8U8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 55]: 0x00000000 (SURFACEFMT_CxV8U8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 56]: 0x00000000 (SURFACEFMT_R_S10E5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 57]: 0x00000000 (SURFACEFMT_R_S23E8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 58]: 0x00000000 (SURFACEFMT_RG_S10E5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 59]: 0x00000000 (SURFACEFMT_RG_S23E8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 60]: 0x00000000 (SURFACEFMT_ARGB_S10E5)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 61]: 0x00000000 (SURFACEFMT_ARGB_S23E8)
2024-06-11T05:30:42.121Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 63]: 0x00000000 (MAX_VERTEX_SHADER_TEXTURES)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 64]: 0x00000000 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 65]: 0x00000000 (SURFACEFMT_V16U16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 66]: 0x00000000 (SURFACEFMT_G16R16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 67]: 0x00000000 (SURFACEFMT_A16B16G16R16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 68]: 0x00000000 (SURFACEFMT_UYVY)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 69]: 0x00000000 (SURFACEFMT_YUY2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 74]: 0x00000000 (AUTOGENMIPMAPS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 75]: 0x00000000 (SURFACEFMT_NV12)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 77]: 0x00000000 (MAX_CONTEXT_IDS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 78]: 0x00000000 (MAX_SURFACE_IDS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 79]: 0x00000000 (SURFACEFMT_Z_DF16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 80]: 0x00000000 (SURFACEFMT_Z_DF24)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 81]: 0x00000000 (SURFACEFMT_Z_D24S8_INT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 82]: 0x00000000 (SURFACEFMT_ATI1)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 83]: 0x00000000 (SURFACEFMT_ATI2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 87]: 0x00000000 (LINE_AA)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 89]:   0.000000 (MAX_LINE_WIDTH)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 90]:   0.000000 (MAX_AA_LINE_WIDTH)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 91]: 0x00000000 (SURFACEFMT_YV12)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 93]: 0x00000000 (TS_COLOR_KEY)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 95]: 0x00000000 (DXCONTEXT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 97]: 0x00000000 (DX_MAX_VERTEXBUFFERS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 98]: 0x00000000 (DX_MAX_CONSTANT_BUFFERS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2024-06-11T05:30:42.122Z In(05) vmx   cap[100]: 0x00000000 (DXFMT_X8R8G8B8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[101]: 0x00000000 (DXFMT_A8R8G8B8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[102]: 0x00000000 (DXFMT_R5G6B5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[103]: 0x00000000 (DXFMT_X1R5G5B5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[104]: 0x00000000 (DXFMT_A1R5G5B5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[105]: 0x00000000 (DXFMT_A4R4G4B4)
2024-06-11T05:30:42.122Z In(05) vmx   cap[106]: 0x00000000 (DXFMT_Z_D32)
2024-06-11T05:30:42.122Z In(05) vmx   cap[107]: 0x00000000 (DXFMT_Z_D16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[108]: 0x00000000 (DXFMT_Z_D24S8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[109]: 0x00000000 (DXFMT_Z_D15S1)
2024-06-11T05:30:42.122Z In(05) vmx   cap[110]: 0x00000000 (DXFMT_LUMINANCE8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[111]: 0x00000000 (DXFMT_LUMINANCE4_ALPHA4)
2024-06-11T05:30:42.122Z In(05) vmx   cap[112]: 0x00000000 (DXFMT_LUMINANCE16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[113]: 0x00000000 (DXFMT_LUMINANCE8_ALPHA8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[114]: 0x00000000 (DXFMT_DXT1)
2024-06-11T05:30:42.122Z In(05) vmx   cap[115]: 0x00000000 (DXFMT_DXT2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[116]: 0x00000000 (DXFMT_DXT3)
2024-06-11T05:30:42.122Z In(05) vmx   cap[117]: 0x00000000 (DXFMT_DXT4)
2024-06-11T05:30:42.122Z In(05) vmx   cap[118]: 0x00000000 (DXFMT_DXT5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[119]: 0x00000000 (DXFMT_BUMPU8V8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[121]: 0x00000000 (DXFMT_BUMPX8L8V8U8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2024-06-11T05:30:42.122Z In(05) vmx   cap[123]: 0x00000000 (DXFMT_ARGB_S10E5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[124]: 0x00000000 (DXFMT_ARGB_S23E8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[125]: 0x00000000 (DXFMT_A2R10G10B10)
2024-06-11T05:30:42.122Z In(05) vmx   cap[126]: 0x00000000 (DXFMT_V8U8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[127]: 0x00000000 (DXFMT_Q8W8V8U8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[128]: 0x00000000 (DXFMT_CxV8U8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[129]: 0x00000000 (DXFMT_X8L8V8U8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[130]: 0x00000000 (DXFMT_A2W10V10U10)
2024-06-11T05:30:42.122Z In(05) vmx   cap[131]: 0x00000000 (DXFMT_ALPHA8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[132]: 0x00000000 (DXFMT_R_S10E5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[133]: 0x00000000 (DXFMT_R_S23E8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[134]: 0x00000000 (DXFMT_RG_S10E5)
2024-06-11T05:30:42.122Z In(05) vmx   cap[135]: 0x00000000 (DXFMT_RG_S23E8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[136]: 0x00000000 (DXFMT_BUFFER)
2024-06-11T05:30:42.122Z In(05) vmx   cap[137]: 0x00000000 (DXFMT_Z_D24X8)
2024-06-11T05:30:42.122Z In(05) vmx   cap[138]: 0x00000000 (DXFMT_V16U16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[139]: 0x00000000 (DXFMT_G16R16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[140]: 0x00000000 (DXFMT_A16B16G16R16)
2024-06-11T05:30:42.122Z In(05) vmx   cap[141]: 0x00000000 (DXFMT_UYVY)
2024-06-11T05:30:42.122Z In(05) vmx   cap[142]: 0x00000000 (DXFMT_YUY2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[143]: 0x00000000 (DXFMT_NV12)
2024-06-11T05:30:42.122Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2024-06-11T05:30:42.122Z In(05) vmx   cap[145]: 0x00000000 (DXFMT_R32G32B32A32_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[146]: 0x00000000 (DXFMT_R32G32B32A32_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[147]: 0x00000000 (DXFMT_R32G32B32A32_SINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[148]: 0x00000000 (DXFMT_R32G32B32_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[149]: 0x00000000 (DXFMT_R32G32B32_FLOAT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[150]: 0x00000000 (DXFMT_R32G32B32_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[151]: 0x00000000 (DXFMT_R32G32B32_SINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[152]: 0x00000000 (DXFMT_R16G16B16A16_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[153]: 0x00000000 (DXFMT_R16G16B16A16_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[154]: 0x00000000 (DXFMT_R16G16B16A16_SNORM)
2024-06-11T05:30:42.122Z In(05) vmx   cap[155]: 0x00000000 (DXFMT_R16G16B16A16_SINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[156]: 0x00000000 (DXFMT_R32G32_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[157]: 0x00000000 (DXFMT_R32G32_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[158]: 0x00000000 (DXFMT_R32G32_SINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[159]: 0x00000000 (DXFMT_R32G8X24_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[160]: 0x00000000 (DXFMT_D32_FLOAT_S8X24_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[161]: 0x00000000 (DXFMT_R32_FLOAT_X8X24)
2024-06-11T05:30:42.122Z In(05) vmx   cap[162]: 0x00000000 (DXFMT_X32_G8X24_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[163]: 0x00000000 (DXFMT_R10G10B10A2_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[164]: 0x00000000 (DXFMT_R10G10B10A2_UINT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[165]: 0x00000000 (DXFMT_R11G11B10_FLOAT)
2024-06-11T05:30:42.122Z In(05) vmx   cap[166]: 0x00000000 (DXFMT_R8G8B8A8_TYPELESS)
2024-06-11T05:30:42.122Z In(05) vmx   cap[167]: 0x00000000 (DXFMT_R8G8B8A8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[168]: 0x00000000 (DXFMT_R8G8B8A8_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[169]: 0x00000000 (DXFMT_R8G8B8A8_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[170]: 0x00000000 (DXFMT_R8G8B8A8_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[171]: 0x00000000 (DXFMT_R16G16_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[172]: 0x00000000 (DXFMT_R16G16_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[173]: 0x00000000 (DXFMT_R16G16_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[174]: 0x00000000 (DXFMT_R32_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[175]: 0x00000000 (DXFMT_D32_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[176]: 0x00000000 (DXFMT_R32_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[177]: 0x00000000 (DXFMT_R32_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[178]: 0x00000000 (DXFMT_R24G8_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[179]: 0x00000000 (DXFMT_D24_UNORM_S8_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[180]: 0x00000000 (DXFMT_R24_UNORM_X8)
2024-06-11T05:30:42.123Z In(05) vmx   cap[181]: 0x00000000 (DXFMT_X24_G8_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[182]: 0x00000000 (DXFMT_R8G8_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[183]: 0x00000000 (DXFMT_R8G8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[184]: 0x00000000 (DXFMT_R8G8_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[185]: 0x00000000 (DXFMT_R8G8_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[186]: 0x00000000 (DXFMT_R16_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[187]: 0x00000000 (DXFMT_R16_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[188]: 0x00000000 (DXFMT_R16_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[189]: 0x00000000 (DXFMT_R16_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[190]: 0x00000000 (DXFMT_R16_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[191]: 0x00000000 (DXFMT_R8_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[192]: 0x00000000 (DXFMT_R8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[193]: 0x00000000 (DXFMT_R8_UINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[194]: 0x00000000 (DXFMT_R8_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[195]: 0x00000000 (DXFMT_R8_SINT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[196]: 0x00000000 (DXFMT_P8)
2024-06-11T05:30:42.123Z In(05) vmx   cap[197]: 0x00000000 (DXFMT_R9G9B9E5_SHAREDEXP)
2024-06-11T05:30:42.123Z In(05) vmx   cap[198]: 0x00000000 (DXFMT_R8G8_B8G8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[199]: 0x00000000 (DXFMT_G8R8_G8B8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[200]: 0x00000000 (DXFMT_BC1_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[201]: 0x00000000 (DXFMT_BC1_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[202]: 0x00000000 (DXFMT_BC2_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[203]: 0x00000000 (DXFMT_BC2_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[204]: 0x00000000 (DXFMT_BC3_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[205]: 0x00000000 (DXFMT_BC3_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[206]: 0x00000000 (DXFMT_BC4_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[207]: 0x00000000 (DXFMT_ATI1)
2024-06-11T05:30:42.123Z In(05) vmx   cap[208]: 0x00000000 (DXFMT_BC4_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[209]: 0x00000000 (DXFMT_BC5_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[210]: 0x00000000 (DXFMT_ATI2)
2024-06-11T05:30:42.123Z In(05) vmx   cap[211]: 0x00000000 (DXFMT_BC5_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[212]: 0x00000000 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[213]: 0x00000000 (DXFMT_B8G8R8A8_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[214]: 0x00000000 (DXFMT_B8G8R8A8_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[215]: 0x00000000 (DXFMT_B8G8R8X8_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[216]: 0x00000000 (DXFMT_B8G8R8X8_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[217]: 0x00000000 (DXFMT_Z_DF16)
2024-06-11T05:30:42.123Z In(05) vmx   cap[218]: 0x00000000 (DXFMT_Z_DF24)
2024-06-11T05:30:42.123Z In(05) vmx   cap[219]: 0x00000000 (DXFMT_Z_D24S8_INT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[220]: 0x00000000 (DXFMT_YV12)
2024-06-11T05:30:42.123Z In(05) vmx   cap[221]: 0x00000000 (DXFMT_R32G32B32A32_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[222]: 0x00000000 (DXFMT_R16G16B16A16_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[223]: 0x00000000 (DXFMT_R16G16B16A16_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[224]: 0x00000000 (DXFMT_R32G32_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[225]: 0x00000000 (DXFMT_R10G10B10A2_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[226]: 0x00000000 (DXFMT_R8G8B8A8_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[227]: 0x00000000 (DXFMT_R16G16_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[228]: 0x00000000 (DXFMT_R16G16_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[229]: 0x00000000 (DXFMT_R16G16_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[230]: 0x00000000 (DXFMT_R32_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[231]: 0x00000000 (DXFMT_R8G8_SNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[232]: 0x00000000 (DXFMT_R16_FLOAT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[233]: 0x00000000 (DXFMT_D16_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[234]: 0x00000000 (DXFMT_A8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[235]: 0x00000000 (DXFMT_BC1_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[236]: 0x00000000 (DXFMT_BC2_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[237]: 0x00000000 (DXFMT_BC3_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[238]: 0x00000000 (DXFMT_B5G6R5_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[239]: 0x00000000 (DXFMT_B5G5R5A1_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[240]: 0x00000000 (DXFMT_B8G8R8A8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[241]: 0x00000000 (DXFMT_B8G8R8X8_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[242]: 0x00000000 (DXFMT_BC4_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[243]: 0x00000000 (DXFMT_BC5_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[244]: 0x00000000 (SM41)
2024-06-11T05:30:42.123Z In(05) vmx   cap[245]: 0x00000000 (MULTISAMPLE_2X)
2024-06-11T05:30:42.123Z In(05) vmx   cap[246]: 0x00000000 (MULTISAMPLE_4X)
2024-06-11T05:30:42.123Z In(05) vmx   cap[247]: 0x00000000 (MS_FULL_QUALITY)
2024-06-11T05:30:42.123Z In(05) vmx   cap[248]: 0x00000000 (LOGICOPS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2024-06-11T05:30:42.123Z In(05) vmx   cap[251]: 0x00000000 (DXFMT_BC6H_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[252]: 0x00000000 (DXFMT_BC6H_UF16)
2024-06-11T05:30:42.123Z In(05) vmx   cap[253]: 0x00000000 (DXFMT_BC6H_SF16)
2024-06-11T05:30:42.123Z In(05) vmx   cap[254]: 0x00000000 (DXFMT_BC7_TYPELESS)
2024-06-11T05:30:42.123Z In(05) vmx   cap[255]: 0x00000000 (DXFMT_BC7_UNORM)
2024-06-11T05:30:42.123Z In(05) vmx   cap[256]: 0x00000000 (DXFMT_BC7_UNORM_SRGB)
2024-06-11T05:30:42.123Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2024-06-11T05:30:42.123Z In(05) vmx   cap[258]: 0x00000000 (SM5)
2024-06-11T05:30:42.123Z In(05) vmx   cap[259]: 0x00000000 (MULTISAMPLE_8X)
2024-06-11T05:30:42.123Z In(05) vmx   cap[260]: 0x00000000 (MAX_FORCED_SAMPLE_COUNT)
2024-06-11T05:30:42.123Z In(05) vmx   cap[261]: 0x00000000 (GL43)
2024-06-11T05:30:42.123Z In(05) vmx SVGA3dCaps: guest, at resume
2024-06-11T05:30:42.123Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2024-06-11T05:30:42.123Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2024-06-11T05:30:42.124Z In(05) vmx DUMPER: Item 'screens.arraySize' [-1, -1] not found.
2024-06-11T05:30:42.124Z In(05) vmx SVGA-ScreenMgr: Screen type changed to RegisterMode
2024-06-11T05:30:42.124Z In(05) vmx SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 720) flags=0x2
2024-06-11T05:30:42.124Z In(05) vmx DUMPER: Item 'CursorType' [-1, -1] not found.
2024-06-11T05:30:42.124Z In(05) vmx SVGA: Disabling 3d support
2024-06-11T05:30:42.124Z In(05) vmx   restoring Ethernet0
2024-06-11T05:30:42.124Z In(05) vmx   restoring vmci0
2024-06-11T05:30:42.124Z In(05) vmx DUMPER: Item 'VMCINumSubs' [-1, -1] not found.
2024-06-11T05:30:42.124Z In(05) vmx   restoring vsock
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Item 'remote.port' [0, -1] not found.
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Item 'qpair.context' [0, -1] not found.
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Item 'timeoutRemaining' [0, -1] not found.
2024-06-11T05:30:42.125Z In(05) vmx   restoring GuestMsg
2024-06-11T05:30:42.125Z In(05) vmx   restoring GuestRpc
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Item 'AsyncVmciSocket.numSendBuf' [-1, -1] not found.
2024-06-11T05:30:42.125Z In(05) vmx   restoring Tools
2024-06-11T05:30:42.125Z In(05) vmx   restoring Tools Install
2024-06-11T05:30:42.125Z In(05) vmx TOOLS INSTALL setting state to 0 on restore.
2024-06-11T05:30:42.125Z In(05) vmx   restoring GuestAppMonitor
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Requested 10 bytes, found 5 bytes.
2024-06-11T05:30:42.125Z In(05) vmx DUMPER: Requested 20 bytes, found 5 bytes.
2024-06-11T05:30:42.125Z In(05) vmx   restoring Hgfs
2024-06-11T05:30:42.125Z In(05) vmx   restoring MKSVMX
2024-06-11T05:30:42.125Z In(05) vmx   restoring ToolsDeployPkg
2024-06-11T05:30:42.125Z In(05) vmx DEPLOYPKG: ToolsDeployPkgCptRestore: state=0 err=0 (null msg)
2024-06-11T05:30:42.125Z In(05) vmx DEPLOYPKG: ToolsDeployPkgRestoreSuccessTasks: state=0 err=0, msg=null
2024-06-11T05:30:42.125Z In(05) vmx   restoring CMOS
2024-06-11T05:30:42.125Z In(05) vmx   restoring FlashRam
2024-06-11T05:30:42.125Z In(05) vmx Progress 101% (none)
2024-06-11T05:30:42.126Z In(05) vmx DUMPER: Updating header magic on restore.
2024-06-11T05:30:42.126Z In(05) vmx CPT: Deleting checkpoint state, '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss'.
2024-06-11T05:30:42.397Z In(05) vmx SNAPSHOT: SnapshotConfigInfoExpandVM: Unable to find 'cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss'.  Setting vmState to NULL.
2024-06-11T05:30:42.397Z No(00) vmx ConfigDB: Setting checkpoint.vmState = ""
2024-06-11T05:30:42.406Z No(00) vmx PowerOnTiming: Module CheckpointLate took 3866921 us
2024-06-11T05:30:42.407Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2024-06-11T05:30:42.407Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x9e stepping: 0xa
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest codename: Coffee Lake-S/H
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000906ea 0x00010800 0xf7fa3203 0x0f8bfbff
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000906ea 0x04100800 0x7ffafbff 0xbfebfbff
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x00c0003f 0x000003ff 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x02c0003f 0x00002fff 0x00000006
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x000026f7 0x00000002 0x00000009 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x009c27ab 0x00000000 0xbc000400
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x07300401 0x000000ff 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x07300404 0x00000000 0x00000000 0x00000603
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000004
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000000 0x00000001 0x00000201 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x0000000c 0x00000201 0x00000004
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x0000001f 0x00000340 0x00000440 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000f 0x00000340 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000340 0x00000100 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x000000d8 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2024-06-11T05:30:42.407Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x00278d00 0x000101d0 0x00000002 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x37692029 0x3538382d 0x43204830 0x40205550
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-06-11T05:30:42.408Z In(05) vmx Minimum ucode level: 0x000000f6
2024-06-11T05:30:42.408Z In(05) vmx VPMC: events will use hybrid freeze.
2024-06-11T05:30:42.408Z In(05) vmx VPMC: gen counters: num 4 mask 0xffffffffffff
2024-06-11T05:30:42.408Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2024-06-11T05:30:42.408Z In(05) vmx VPMC: hardware counters: 0
2024-06-11T05:30:42.408Z In(05) vmx VPMC: perf capabilities: 0x2000
2024-06-11T05:30:42.408Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0xc
2024-06-11T05:30:42.408Z In(05) vmx SVGA: Registering IOSpace at 0x1070
2024-06-11T05:30:42.408Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2024-06-11T05:30:42.408Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=134217728
2024-06-11T05:30:42.408Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=134217728
2024-06-11T05:30:42.408Z In(05) vmx SVGA: Final Device caps : 0xfdff83e2
2024-06-11T05:30:42.408Z In(05) vmx SVGA: Final Device caps2: 0x0005efff
2024-06-11T05:30:42.409Z In(05) vmx FeatureCompat: Capabilities:
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.sse3 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.fma = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.pcid = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.sse41 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.sse42 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.movbe = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.popcnt = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.aes = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xsave = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.avx = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.f16c = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.rdrand = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ss = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.avx2 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.smep = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.invpcid = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.rdseed = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.adx = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.smap = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.mdclear = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.stibp = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.fcmd = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ssbd = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xsavec = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xsaves = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.abm = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.nx = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.lm = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.intel = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ibrs = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ibpb = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.mwait = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.vmx = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.ds = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: hv.capable = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.realmode = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.mbx = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.advexitinfo = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.eptad = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.ple = 1
2024-06-11T05:30:42.409Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2024-06-11T05:30:42.409Z In(05) vmx FeatureCompat: Requirements:
2024-06-11T05:30:42.409Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2024-06-11T05:30:42.409Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2024-06-11T05:30:42.409Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xgetbv_ecx1 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2024-06-11T05:30:42.410Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2024-06-11T05:30:42.516Z In(05) ulm_exc VTHREAD 123145360666624 "ulm_exc" tid 33595
2024-06-11T05:30:42.517Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2024-06-11T05:30:42.518Z In(05) vmx 
2024-06-11T05:30:42.518Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2024-06-11T05:30:42.518Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Total excluded                      :  1073664 1073664      - |      -      -      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Actual maximum                      :         1073664        |             -
2024-06-11T05:30:42.518Z In(05)+ vmx 
2024-06-11T05:30:42.518Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  196608 196608      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     258    258      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35328  35328      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Total paged                         :  673930 673930      - |    506    506      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Actual maximum                      :         673930        |        673930
2024-06-11T05:30:42.518Z In(05)+ vmx 
2024-06-11T05:30:42.518Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      38     38      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1168   2272      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_LBR                        :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Total nonpaged                      :   34406  35958      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem Actual maximum                      :          34406        |         35958
2024-06-11T05:30:42.518Z In(05)+ vmx 
2024-06-11T05:30:42.518Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      98     98      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1121   1178      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      40     40      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      0      0      -
2024-06-11T05:30:42.518Z In(05) vmx OvhdMem OvhdMon_HV                          :       1      1      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       1      1      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      29     29      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     475    475      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2378   2378      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem Total anonymous                     :    7393   7450      - |      0      0      -
2024-06-11T05:30:42.519Z In(05) vmx OvhdMem Actual maximum                      :           7393        |          7450
2024-06-11T05:30:42.519Z In(05)+ vmx 
2024-06-11T05:30:42.519Z In(05) vmx VMMEM: Precise Reservation: 2795MB (MainMem=4096MB)
2024-06-11T05:30:42.519Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2024-06-11T05:30:42.519Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2024-06-11T05:30:42.520Z No(00) vmx PowerOnTiming: ALL took 18727861 us
2024-06-11T05:30:42.520Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2024-06-11T05:30:42.520Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2024-06-11T05:30:42.520Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2024-06-11T05:30:42.521Z In(05) vcpu-0 VTHREAD 123145361203200 "vcpu-0" tid 33596
2024-06-11T05:30:42.524Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2024-06-11T05:30:42.524Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 4095 MB (100 %) Size:4095 MB (100 %)
2024-06-11T05:30:42.524Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x5, chipset.onlineStandby 0
2024-06-11T05:30:42.536Z In(05) vcpu-0 VNET: MacosVmnetVirtApiGetInterfaceForBridging: Using iface: en0 for bridging
2024-06-11T05:30:42.536Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartBridgedInterface: Ethernet0: Starting virtual interface in bridge mode, preferred=(null), selected=en0
2024-06-11T05:30:42.565Z In(05) host-33418 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: starting interface, status=1000
2024-06-11T05:30:42.566Z In(05) host-33418 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: interface params:
2024-06-11T05:30:42.566Z In(05) host-33418 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      MTU             : 1500
2024-06-11T05:30:42.566Z In(05) host-33418 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      Max packet size : 1514
2024-06-11T05:30:42.566Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartInterface: Ethernet0: Virtual interface started successfully
2024-06-11T05:30:42.566Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2024-06-11T05:30:42.567Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.accelerometer"
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.ambientLight"
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.compass"
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.gyrometer"
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.inclinometer"
2024-06-11T05:30:42.568Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.orientation"
2024-06-11T05:30:42.570Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2024-06-11T05:30:42.572Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2024-06-11T05:30:42.622Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-06-11T05:30:42.623Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2024-06-11T05:30:42.623Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2024-06-11T05:30:42.623Z No(00) vcpu-0 Metrics lastUpdate (s): 1022
2024-06-11T05:30:42.623Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2024-06-11T05:30:42.623Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2024-06-11T05:30:42.623Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-06-11T05:30:42.623Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2024-06-11T05:30:42.623Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2024-06-11T05:30:42.625Z In(05) vcpu-0 CPT: vmstart
2024-06-11T05:30:42.633Z In(05) mks MKSControlMgr: connected
2024-06-11T05:30:43.789Z No(00) vmx ConfigDB: Setting gui.lastPoweredViewMode = "fullscreen"
2024-06-11T05:30:43.809Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2024-06-11T05:30:43.809Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2024-06-11T05:30:43.815Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#1a/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#29/in/
2024-06-11T05:30:43.844Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:30:43.844Z In(05) mks SWBWindow: Window 0 Defined: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 60, 1920, 1080) pixelScale=1, flags=0xF
2024-06-11T05:30:43.844Z In(05) windowThread-0 VTHREAD 123145362276352 "windowThread-0" tid 33665
2024-06-11T05:30:43.844Z In(05) mks MKS-HWinMux: Started MacOS presentation backend.
2024-06-11T05:30:43.887Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2024-06-11T05:30:43.887Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2024-06-11T05:30:43.897Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2024-06-11T05:30:44.352Z In(05) vcpu-0 DDB: "longContentID" = "aa77cc640bbf579b7839f22fec297c4f" (was "7777ded961871ad76cebdf60aab4536a")
2024-06-11T05:30:44.357Z In(05) vthread-33668 VTHREAD 123145362812928 "vthread-33668" tid 33668
2024-06-11T05:30:44.357Z In(05) vthread-33669 VTHREAD 123145363349504 "vthread-33669" tid 33669
2024-06-11T05:30:44.357Z In(05) vthread-33670 VTHREAD 123145363886080 "vthread-33670" tid 33670
2024-06-11T05:31:11.705Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:33:24.008Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:33:43.973Z In(05) vmx GuestRpcSendTimedOut: message to toolbox timed out.
2024-06-11T05:33:43.974Z In(05) vmx Vix: [guestCommands.c:1943]: Error VIX_E_TOOLS_NOT_RUNNING in VMAutomationTranslateGuestRpcError(): VMware Tools are not running in the guest
2024-06-11T05:36:41.319Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:40:42.647Z In(05) vmx GuestRpcSendTimedOut: message to toolbox timed out.
2024-06-11T05:40:42.648Z Wa(03) vmx ToolsTimeSyncRpcCompletionRoutine: Timeout while sending guestrpc
2024-06-11T05:40:42.696Z In(05) vmx Tools: No activity for 10 minutes, resetting Tools version.
2024-06-11T05:40:42.696Z In(05) vmx Tools_SetVersionAndType did nothing; new tools version (0) and type (0) match old Tools version and type
2024-06-11T05:43:35.232Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:43:49.511Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:44:50.271Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:44:52.043Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:45:45.508Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:45:45.941Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:45:46.625Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:45:56.741Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:00.291Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:05.891Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:07.526Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:12.308Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:13.558Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:46:41.891Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:10.145Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:16.206Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:16.931Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:23.606Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:24.390Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:27.072Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:28.006Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:30.927Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:32.824Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:40.939Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:42.223Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:48.656Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:49.428Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:50.522Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:51.372Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:48:59.339Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:51:17.959Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:51:30.936Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-06-11T05:52:22.609Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2024-06-11T05:52:51.314Z Wa(03) vmx VMX: attempted to do a soft suspend while not in the correct state. Ignored...
2024-06-11T05:52:51.314Z In(05) vmx Vix: [vmxCommands.c:839]: VMAutomation_SuspendImpl: SoftSuspend failed.
2024-06-11T05:52:51.314Z In(05) vmx SUSPEND: Start suspend (flags=0)
2024-06-11T05:52:51.347Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-06-11T05:52:51.387Z In(05) vcpu-0 Closing all the disks of the VM.
2024-06-11T05:52:51.387Z In(05) vcpu-0 Closing disk 'ide0:0'
2024-06-11T05:52:51.391Z In(05) vcpu-0 Progress -1% (msg.checkpoint.saveStatus)
2024-06-11T05:52:51.391Z In(05) vcpu-0 Checkpointed in VMware Fusion, 13.5.2, build-23775688, Mac OS Host
2024-06-11T05:52:51.393Z In(05) vcpu-0 MainMem: Write full memory image (SF=0 L=0 SZ=1 RM=1 U=0 TM=0).
2024-06-11T05:52:51.397Z In(05) vcpu-0 Progress 0% (none)
2024-06-11T05:52:51.462Z In(05) vcpu-0 Progress 1% (none)
2024-06-11T05:52:51.525Z In(05) vcpu-0 Progress 2% (none)
2024-06-11T05:52:51.576Z In(05) vcpu-0 Progress 3% (none)
2024-06-11T05:52:51.640Z In(05) vcpu-0 Progress 4% (none)
2024-06-11T05:52:51.708Z In(05) vcpu-0 Progress 5% (none)
2024-06-11T05:52:51.796Z In(05) vcpu-0 Progress 6% (none)
2024-06-11T05:52:51.838Z In(05) vcpu-0 Progress 7% (none)
2024-06-11T05:52:51.884Z In(05) vcpu-0 Progress 8% (none)
2024-06-11T05:52:51.935Z In(05) vcpu-0 Progress 9% (none)
2024-06-11T05:52:51.982Z In(05) vcpu-0 Progress 10% (none)
2024-06-11T05:52:52.025Z In(05) vcpu-0 Progress 11% (none)
2024-06-11T05:52:52.066Z In(05) vcpu-0 Progress 12% (none)
2024-06-11T05:52:52.099Z In(05) vcpu-0 Progress 13% (none)
2024-06-11T05:52:52.127Z In(05) vcpu-0 Progress 14% (none)
2024-06-11T05:52:52.173Z In(05) vcpu-0 Progress 15% (none)
2024-06-11T05:52:52.211Z In(05) vcpu-0 Progress 16% (none)
2024-06-11T05:52:52.254Z In(05) vcpu-0 Progress 17% (none)
2024-06-11T05:52:52.308Z In(05) vcpu-0 Progress 18% (none)
2024-06-11T05:52:52.355Z In(05) vcpu-0 Progress 19% (none)
2024-06-11T05:52:52.403Z In(05) vcpu-0 Progress 20% (none)
2024-06-11T05:52:52.458Z In(05) vcpu-0 Progress 21% (none)
2024-06-11T05:52:52.528Z In(05) vcpu-0 Progress 22% (none)
2024-06-11T05:52:52.596Z In(05) vcpu-0 Progress 23% (none)
2024-06-11T05:52:52.679Z In(05) vcpu-0 Progress 24% (none)
2024-06-11T05:52:52.727Z In(05) vcpu-0 Progress 25% (none)
2024-06-11T05:52:52.793Z In(05) vcpu-0 Progress 26% (none)
2024-06-11T05:52:52.855Z In(05) vcpu-0 Progress 27% (none)
2024-06-11T05:52:52.919Z In(05) vcpu-0 Progress 28% (none)
2024-06-11T05:52:52.977Z In(05) vcpu-0 Progress 29% (none)
2024-06-11T05:52:53.048Z In(05) vcpu-0 Progress 30% (none)
2024-06-11T05:52:53.119Z In(05) vcpu-0 Progress 31% (none)
2024-06-11T05:52:53.184Z In(05) vcpu-0 Progress 32% (none)
2024-06-11T05:52:53.249Z In(05) vcpu-0 Progress 33% (none)
2024-06-11T05:52:53.311Z In(05) vcpu-0 Progress 34% (none)
2024-06-11T05:52:53.366Z In(05) vcpu-0 Progress 35% (none)
2024-06-11T05:52:53.431Z In(05) vcpu-0 Progress 36% (none)
2024-06-11T05:52:53.481Z In(05) vcpu-0 Progress 37% (none)
2024-06-11T05:52:53.553Z In(05) vcpu-0 Progress 38% (none)
2024-06-11T05:52:53.615Z In(05) vcpu-0 Progress 39% (none)
2024-06-11T05:52:53.682Z In(05) vcpu-0 Progress 40% (none)
2024-06-11T05:52:53.738Z In(05) vcpu-0 Progress 41% (none)
2024-06-11T05:52:53.801Z In(05) vcpu-0 Progress 42% (none)
2024-06-11T05:52:53.861Z In(05) vcpu-0 Progress 43% (none)
2024-06-11T05:52:53.916Z In(05) vcpu-0 Progress 44% (none)
2024-06-11T05:52:53.983Z In(05) vcpu-0 Progress 45% (none)
2024-06-11T05:52:54.046Z In(05) vcpu-0 Progress 46% (none)
2024-06-11T05:52:54.108Z In(05) vcpu-0 Progress 47% (none)
2024-06-11T05:52:54.166Z In(05) vcpu-0 Progress 48% (none)
2024-06-11T05:52:54.225Z In(05) vcpu-0 Progress 49% (none)
2024-06-11T05:52:54.287Z In(05) vcpu-0 Progress 50% (none)
2024-06-11T05:52:54.349Z In(05) vcpu-0 Progress 51% (none)
2024-06-11T05:52:54.410Z In(05) vcpu-0 Progress 52% (none)
2024-06-11T05:52:54.464Z In(05) vcpu-0 Progress 53% (none)
2024-06-11T05:52:54.528Z In(05) vcpu-0 Progress 54% (none)
2024-06-11T05:52:54.584Z In(05) vcpu-0 Progress 55% (none)
2024-06-11T05:52:54.662Z In(05) vcpu-0 Progress 56% (none)
2024-06-11T05:52:54.714Z In(05) vcpu-0 Progress 57% (none)
2024-06-11T05:52:54.764Z In(05) vcpu-0 Progress 58% (none)
2024-06-11T05:52:54.823Z In(05) vcpu-0 Progress 59% (none)
2024-06-11T05:52:54.882Z In(05) vcpu-0 Progress 60% (none)
2024-06-11T05:52:54.933Z In(05) vcpu-0 Progress 61% (none)
2024-06-11T05:52:54.987Z In(05) vcpu-0 Progress 62% (none)
2024-06-11T05:52:55.057Z In(05) vcpu-0 Progress 63% (none)
2024-06-11T05:52:55.114Z In(05) vcpu-0 Progress 64% (none)
2024-06-11T05:52:55.157Z In(05) vcpu-0 Progress 65% (none)
2024-06-11T05:52:55.204Z In(05) vcpu-0 Progress 66% (none)
2024-06-11T05:52:55.243Z In(05) vcpu-0 Progress 67% (none)
2024-06-11T05:52:55.287Z In(05) vcpu-0 Progress 68% (none)
2024-06-11T05:52:55.328Z In(05) vcpu-0 Progress 69% (none)
2024-06-11T05:52:55.373Z In(05) vcpu-0 Progress 70% (none)
2024-06-11T05:52:55.418Z In(05) vcpu-0 Progress 71% (none)
2024-06-11T05:52:55.481Z In(05) vcpu-0 Progress 72% (none)
2024-06-11T05:52:55.530Z In(05) vcpu-0 Progress 73% (none)
2024-06-11T05:52:55.589Z In(05) vcpu-0 Progress 74% (none)
2024-06-11T05:52:55.633Z In(05) vcpu-0 Progress 75% (none)
2024-06-11T05:52:55.668Z In(05) vcpu-0 Progress 76% (none)
2024-06-11T05:52:55.708Z In(05) vcpu-0 Progress 77% (none)
2024-06-11T05:52:55.753Z In(05) vcpu-0 Progress 78% (none)
2024-06-11T05:52:55.791Z In(05) vcpu-0 Progress 79% (none)
2024-06-11T05:52:55.823Z In(05) vcpu-0 Progress 80% (none)
2024-06-11T05:52:55.868Z In(05) vcpu-0 Progress 81% (none)
2024-06-11T05:52:55.915Z In(05) vcpu-0 Progress 82% (none)
2024-06-11T05:52:55.982Z In(05) vcpu-0 Progress 83% (none)
2024-06-11T05:52:56.043Z In(05) vcpu-0 Progress 84% (none)
2024-06-11T05:52:56.091Z In(05) vcpu-0 Progress 85% (none)
2024-06-11T05:52:56.141Z In(05) vcpu-0 Progress 86% (none)
2024-06-11T05:52:56.173Z In(05) vcpu-0 Progress 87% (none)
2024-06-11T05:52:56.222Z In(05) vcpu-0 Progress 88% (none)
2024-06-11T05:52:56.270Z In(05) vcpu-0 Progress 89% (none)
2024-06-11T05:52:56.330Z In(05) vcpu-0 Progress 90% (none)
2024-06-11T05:52:56.381Z In(05) vcpu-0 Progress 91% (none)
2024-06-11T05:52:56.431Z In(05) vcpu-0 Progress 92% (none)
2024-06-11T05:52:56.488Z In(05) vcpu-0 Progress 93% (none)
2024-06-11T05:52:56.534Z In(05) vcpu-0 Progress 94% (none)
2024-06-11T05:52:56.583Z In(05) vcpu-0 Progress 95% (none)
2024-06-11T05:52:56.632Z In(05) vcpu-0 Progress 96% (none)
2024-06-11T05:52:56.676Z In(05) vcpu-0 MainMem: Wrote 4096MiB in 5282ms (812.98 MB/s)
2024-06-11T05:52:56.677Z No(00) vcpu-0 CheckpointTiming save: memory took 5284799 us
2024-06-11T05:52:56.686Z In(05) vcpu-0 SVGA: Guest reported SVGA driver: (0, 0, 0, 0)
2024-06-11T05:52:56.686Z In(05) vcpu-0 Progress 97% (none)
2024-06-11T05:52:56.725Z In(05) vcpu-0 Progress 98% (none)
2024-06-11T05:52:56.763Z In(05) vcpu-0 Progress 99% (none)
2024-06-11T05:52:56.805Z In(05) vcpu-0 Progress 100% (none)
2024-06-11T05:52:56.806Z In(05) vcpu-0 Tools: [AppStatus] Last heartbeat value 0 (never received)
2024-06-11T05:52:56.806Z In(05) vcpu-0 TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2024-06-11T05:52:56.811Z In(05) vcpu-0 DEPLOYPKG: ToolsDeployPkgCptSave: state=0 err=0 (null msg)
2024-06-11T05:52:56.811Z In(05) vcpu-0 Progress 101% (none)
2024-06-11T05:52:56.814Z No(00) vcpu-0 ConfigDB: Setting checkpoint.vmState = "cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss"
2024-06-11T05:52:56.818Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-06-11T05:52:56.818Z In(05) vcpu-0 SUSPEND: Completed suspend: 'Operation completed successfully' (0)
2024-06-11T05:52:56.818Z No(00) vcpu-0 CheckpointTiming save: VMX took 5430838 us
2024-06-11T05:52:56.818Z No(00) vcpu-0 CheckpointTiming save: ALL took 5430872 us
2024-06-11T05:52:56.818Z In(05) vmx Stopping VCPU threads...
2024-06-11T05:52:56.818Z In(05) vmx MKSThread: Requesting MKS exit
2024-06-11T05:52:56.818Z In(05) vmx Stopping MKS/SVGA threads
2024-06-11T05:52:56.818Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 720) flags=0x2
2024-06-11T05:52:56.819Z In(05) svga SVGA thread is exiting the main loop
2024-06-11T05:52:56.819Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 60, 1920, 1080) pixelScale=1, flags=0xF
2024-06-11T05:52:56.819Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-06-11T05:52:56.819Z In(05) vmx MKS/SVGA threads are stopped
2024-06-11T05:52:56.822Z In(05) vmx 
2024-06-11T05:52:56.822Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2024-06-11T05:52:56.822Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Total excluded                      :  1073664 1073664      - |      -      -      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Actual maximum                      :         1073664        |             -
2024-06-11T05:52:56.822Z In(05)+ vmx 
2024-06-11T05:52:56.822Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       2      2      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  196608 196608      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      1      1      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     258    258      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35328  35328      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Total paged                         :  673930 673930      - |    508    508      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Actual maximum                      :         673930        |        673930
2024-06-11T05:52:56.822Z In(05)+ vmx 
2024-06-11T05:52:56.822Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      38     38      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1168   2272      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_LBR                        :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      35     35      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Total nonpaged                      :   34406  35958      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem Actual maximum                      :          34406        |         35958
2024-06-11T05:52:56.822Z In(05)+ vmx 
2024-06-11T05:52:56.822Z In(05) vmx                                                       reserved      |          used
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_Alloc                       :      98     98      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1121   1178      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       4      4      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      40     40      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_TC                          :     513    513      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       2      2      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_HV                          :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       1      1      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_VHV                         :       3      3      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_Numa                        :      24     24      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      29     29      - |      0      0      -
2024-06-11T05:52:56.822Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     475    475      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2378   2378      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     264    264      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       6      6      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem Total anonymous                     :    7393   7450      - |      0      0      -
2024-06-11T05:52:56.823Z In(05) vmx OvhdMem Actual maximum                      :           7393        |          7450
2024-06-11T05:52:56.823Z In(05)+ vmx 
2024-06-11T05:52:56.823Z In(05) vmx VMMEM: Maximum Reservation: 2802MB (MainMem=4096MB)
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2024-06-11T05:52:56.823Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-06-11T05:52:56.823Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2024-06-11T05:52:56.823Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2024-06-11T05:52:56.892Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2024-06-11T05:52:56.892Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x600001344000
2024-06-11T05:52:57.144Z In(05) host-57798 VNET: MacosVmnetVirtApiStopHandler: Ethernet0: stopping virtual interface, status=1000
2024-06-11T05:52:57.153Z In(05) mks MKSControlMgr: disconnected
2024-06-11T05:52:57.153Z In(05) mks MKS-RenderMain: Stopping MTLRenderer
2024-06-11T05:52:57.153Z In(05) mks MKS-RenderMain: Stopping MTLRenderer
2024-06-11T05:52:57.158Z In(05) mks Stopped Shim3D
2024-06-11T05:52:57.170Z In(05) mks MKS-RenderMain: Stopped MTLRenderer
2024-06-11T05:52:57.172Z In(05) mks MKS PowerOff
2024-06-11T05:52:57.172Z In(05) svga SVGA thread is exiting
2024-06-11T05:52:57.172Z In(05) mks MKS thread is exiting
2024-06-11T05:52:57.172Z Wa(03) vmx 
2024-06-11T05:52:57.304Z In(05) deviceThread Device thread is exiting
2024-06-11T05:52:57.304Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2024-06-11T05:52:57.305Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2024-06-11T05:52:57.305Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2024-06-11T05:52:57.305Z In(05) vmx WORKER: asyncOps=12220 maxActiveOps=4 maxPending=4 maxCompleted=3
2024-06-11T05:52:57.305Z In(05) PowerNotifyThread PowerNotify thread exiting.
2024-06-11T05:52:57.307Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2024-06-11T05:52:57.307Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-06-11T05:52:57.307Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 10B38E000.
2024-06-11T05:52:57.307Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2024-06-11T05:52:57.309Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-06-11T05:52:57.309Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-06-11T05:52:57.309Z In(05) vmx Transitioned vmx/execState/val to suspended
2024-06-11T05:52:57.310Z In(05) vmx VMX idle exit
2024-06-11T05:52:57.310Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 18
2024-06-11T05:52:57.585Z In(05) vmx Services_Exit: Closed the services.
2024-06-11T05:52:57.586Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2024-06-11T05:52:57.586Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2024-06-11T05:52:57.588Z In(05) vmx Flushing VMX VMDB connections
2024-06-11T05:52:57.589Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2024-06-11T05:52:57.589Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2024-06-11T05:52:57.589Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2024-06-11T05:52:57.589Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 14
2024-06-11T05:52:57.589Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 15
2024-06-11T05:52:57.596Z In(05) vmx VMX exit (0).
2024-06-11T05:52:57.596Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2024-06-11T05:52:57.596Z In(05) vmx AIOMGR-S : stat o=9 r=1210 w=1192 i=0 br=4430404896 bw=4430443277
