2025-05-02T06:29:05.296Z In(05) vmx Log for VMware Fusion pid=21392 version=13.5.2 build=build-23775688 option=Release
2025-05-02T06:29:05.296Z In(05) vmx The host is x86_64.
2025-05-02T06:29:05.296Z In(05) vmx Host codepage=UTF-8 encoding=UTF-8
2025-05-02T06:29:05.296Z In(05) vmx Host is macOS 15.5 (24F5068b) Darwin 24.5.0
2025-05-02T06:29:05.296Z In(05) vmx Host offset from UTC is +05:30.
2025-05-02T06:29:05.267Z In(05) vmx VTHREAD 140704386637184 "vmx" tid 188252
2025-05-02T06:29:05.273Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:29:05.273Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:29:05.273Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.273Z In(05) vmx Msg_Reset:
2025-05-02T06:29:05.273Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.273Z In(05) vmx ----------------------------------------
2025-05-02T06:29:05.273Z In(05) vmx ConfigDB: Failed to load /Library/Preferences/VMware Fusion/config
2025-05-02T06:29:05.273Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:29:05.273Z In(05) vmx Msg_Reset:
2025-05-02T06:29:05.273Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/config": Not a directory.
2025-05-02T06:29:05.273Z In(05) vmx ----------------------------------------
2025-05-02T06:29:05.273Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config
2025-05-02T06:29:05.273Z In(05) vmx DictionaryLoad: Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:29:05.273Z In(05) vmx Msg_Reset:
2025-05-02T06:29:05.273Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings": Not a directory.
2025-05-02T06:29:05.273Z In(05) vmx ----------------------------------------
2025-05-02T06:29:05.273Z In(05) vmx ConfigDB: Failed to load /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings
2025-05-02T06:29:05.273Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.273Z In(05) vmx Msg_Reset:
2025-05-02T06:29:05.273Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.273Z In(05) vmx ----------------------------------------
2025-05-02T06:29:05.273Z In(05) vmx ConfigDB: Failed to load ~/Library/Preferences/VMware Fusion/config
2025-05-02T06:29:05.288Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-05-02T06:29:05.289Z In(05) vmx DictionaryLoad: Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx PREF Optional preferences file not found at /Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:29:05.289Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/settings": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/settings. Using default values.
2025-05-02T06:29:05.289Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Applications/VMware Fusion.app/Contents/Library/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx PREF Optional preferences file not found at /Applications/VMware Fusion.app/Contents/Library/config. Using default values.
2025-05-02T06:29:05.289Z In(05) vmx DictionaryLoad: Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "/Users/<USER>/Library/Preferences/VMware Fusion/config": No such file or directory.
2025-05-02T06:29:05.289Z In(05) vmx PREF Optional preferences file not found at /Users/<USER>/Library/Preferences/VMware Fusion/config. Using default values.
2025-05-02T06:29:05.294Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: Client usage
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:29:05.295Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: Server usage
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-02T06:29:05.296Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-02T06:29:05.299Z In(05) vmx Hostname=Rohans-MacBook-Pro.local
2025-05-02T06:29:05.300Z In(05) vmx IP=127.0.0.1 (lo0)
2025-05-02T06:29:05.300Z In(05) vmx IP=::1 (lo0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::1 (lo0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::aede:48ff:fe00:1122 (en5)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::b414:79ff:fe37:ddd5 (ap1)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::10ad:8605:26fd:f9e1 (en0)
2025-05-02T06:29:05.300Z In(05) vmx IP=*********** (en0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::c079:cdff:fe17:df07 (awdl0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::c079:cdff:fe17:df07 (llw0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::71:ab8a:4db0:d5fa (utun0)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::32fe:1b8d:9696:9ee3 (utun1)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::3804:28be:b3d1:22a6 (utun2)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::ce81:b1c:bd2c:69e (utun3)
2025-05-02T06:29:05.300Z In(05) vmx IP=************ (bridge100)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c64 (bridge100)
2025-05-02T06:29:05.300Z In(05) vmx IP=************* (bridge101)
2025-05-02T06:29:05.300Z In(05) vmx IP=fe80::38f9:d3ff:fe77:4c65 (bridge101)
2025-05-02T06:29:05.300Z In(05) vmx System uptime 11810885463 us
2025-05-02T06:29:05.300Z In(05) vmx Command line: "/Applications/VMware Fusion.app/Contents/Library/vmware-vmx" "-E" "en" "-s" "vmx.stdio.keep=TRUE" "-#" "product=64;name=VMware Fusion;version=13.5.2;buildnumber=23775688;licensename=VMware Fusion for Mac OS;licenseversion=13.0;" "-@" "duplex=3;msgs=ui" "-D" "4" "/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx"
2025-05-02T06:29:05.300Z In(05) vmx Msg_SetLocaleEx: HostLocale=UTF-8 UserLocale=en
2025-05-02T06:29:05.300Z In(05) vmx DictionaryLoad: Cannot open file "/Applications/VMware Fusion.app/Contents/Library/messages/en/vmware.vmsg": No such file or directory.
2025-05-02T06:29:05.431Z In(05) vmx Duplex socket: 3
2025-05-02T06:29:05.447Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 16
2025-05-02T06:29:05.447Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 19
2025-05-02T06:29:05.447Z In(05) vmx VigorTransport listening on fd 20
2025-05-02T06:29:05.447Z In(05) vmx Vigor_Init 1
2025-05-02T06:29:05.448Z In(05) vmx Connecting 'ui' to fd '3' with user '(null)'
2025-05-02T06:29:05.448Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-05-02T06:29:05.449Z In(05) vmx /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx: Setup symlink /var/run/vmware/2c91642b75451fe8cbda98ac11d1dd98659dbddf6411ce9de3ebb434dd8daba8 -> /var/run/vmware/501/11811016439_21392
2025-05-02T06:29:05.449Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2025-05-02T06:29:05.450Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2025-05-02T06:29:05.454Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:29:05.454Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:29:05.454Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:29:05.454Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-05-02T06:29:05.454Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-05-02T06:29:05.473Z In(05) vmx Services_Init: Successfully opened the services.
2025-05-02T06:29:05.474Z In(05) vmx FeatureCompat: No EVC masks.
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID codename: Coffee Lake-S/H
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000000, 0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000001, 0: 0x000906ea 0x02100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000002, 0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x11142120
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000006, 0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000007, 0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000002
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000002
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000002
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000040 0x000003c0 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000040 0x00000400 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000000f 0x00000007 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f3fff 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 00000016, 0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000002, 0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000003, 0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000004, 0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:29:05.474Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR       0x3a =                0x5
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x480 =   0xda040000000004
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x481 =       0x7f0000003f
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x482 = 0xfdf9fffe9500697a
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x483 =   0x737fff00236fff
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x484 =     0xb3ff000091ff
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x486 =         0x80000021
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x488 =             0x2000
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x489 =           0x3767ff
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48a =               0x2e
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48b =   0x515cef000000a2
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48c =      0xf0106734141
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48d =       0x7f00000016
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48e = 0xfff9fffe04006172
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x48f =  0x1ffffff00036dfb
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x490 =    0x3ffff000011fb
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x491 =                  0
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x492 =                  0
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x10a =          0xa000c04
2025-05-02T06:29:05.476Z In(05) vmx Common: MSR      0x122 =                  0
2025-05-02T06:29:05.476Z In(05) vmx UTIL: Current file descriptor limit: soft 12800, hard 4294967295.
2025-05-02T06:29:05.476Z In(05) vmx TSC Hz estimates: vmmon 0, cpuinfo 0, cpufreq 0 sysctlfreq 2592000000. Using 2592000000 Hz
2025-05-02T06:29:05.476Z In(05) vmx PTSC: RefClockToPTSC 0 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:29:05.476Z In(05) vmx PTSC: RefClockToPTSC ((x * 2147483648) >> 31) + -30550541369538
2025-05-02T06:29:05.476Z In(05) vmx PTSC: tscOffset 0
2025-05-02T06:29:05.476Z In(05) vmx PTSC: using user-level reference clock
2025-05-02T06:29:05.476Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-05-02T06:29:05.476Z In(05) vmx PTSC: current PTSC=85308
2025-05-02T06:29:05.479Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 26
2025-05-02T06:29:05.480Z In(05) vmx Current Display Settings:
2025-05-02T06:29:05.480Z In(05) vmx    Display: 0 size: 1920x1200 pixelSize: 3840x2400 position: (0, 0) Primary
2025-05-02T06:29:05.481Z In(05) vmx [101641000-102761000): /Applications/VMware Fusion.app/Contents/Library/vmware-vmx
2025-05-02T06:29:05.506Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-05-02T06:29:05.506Z In(05) vmx changing directory to /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/.
2025-05-02T06:29:05.506Z In(05) vmx Config file: /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx
2025-05-02T06:29:05.507Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1875, success=1 additionalError=0
2025-05-02T06:29:05.507Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-05-02T06:29:05.509Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.002122 seconds.
2025-05-02T06:29:05.510Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.002946 seconds.
2025-05-02T06:29:05.523Z Wa(03) vmx PowerOn
2025-05-02T06:29:05.523Z In(05) vmx VMX_PowerOn: VMX build 23775688, UI build 23775688
2025-05-02T06:29:05.523Z In(05) vmx Processor affinity not supported on this host OS
2025-05-02T06:29:05.527Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-05-02T06:29:05.530Z In(05) vmx VMXSTATS: Successfully created stats file 'cloudera-quickstart-vm-5.13.0-0-virtualbox.scoreboard'
2025-05-02T06:29:05.530Z In(05) vmx VMXSTATS: Update Product Information: VMware Fusion	13.5.2	build-23775688	Release  TotalBlockSize: 48
2025-05-02T06:29:05.530Z In(05) vmx HOST sysname Darwin, nodename Rohans-MacBook-Pro.local, release 24.5.0, version Darwin Kernel Version 24.5.0: Tue Apr 22 20:35:39 PDT 2025; root:xnu-11417.121.6~4/RELEASE_X86_64, machine x86_64
2025-05-02T06:29:05.530Z In(05) vmx DICT --- GLOBAL SETTINGS /Applications/VMware Fusion.app/Contents/Library/settings
2025-05-02T06:29:05.530Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-05-02T06:29:05.530Z In(05) vmx DICT --- USER PREFERENCES /Users/<USER>/Library/Preferences/VMware Fusion/preferences
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.dataCollectionEnabled.epoch = ""
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.dataCollectionEnabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.defaultProfileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.maxProfiles = "4"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileKey = "5202618b-19c9-7a89-903f-4ee6655cb796"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileName = "Windows 10 Profile"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.profileType = "windows9"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableOSShortcuts = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.enableKeyMappings = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.selectedLanguage = ""
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.cmdKeyFilterType = "none"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.maxMappings = "30"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.mappingKey = "0"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.fromHost = "GUI Z"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.mappingKey = "1"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.fromHost = "GUI X"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping1.toGuest = "CONTROL X"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.mappingKey = "2"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.fromHost = "GUI C"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping2.toGuest = "CONTROL C"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.mappingKey = "3"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.fromHost = "GUI V"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping3.toGuest = "CONTROL V"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.mappingKey = "4"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.fromHost = "GUI P"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping4.toGuest = "CONTROL P"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.mappingKey = "5"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.fromHost = "GUI A"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping5.toGuest = "CONTROL A"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.mappingKey = "6"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.fromHost = "GUI S"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping6.toGuest = "CONTROL S"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.mappingKey = "7"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.fromHost = "GUI F"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping7.toGuest = "0x03d"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.mappingKey = "8"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.fromHost = "GUI W"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.mappingKey = "9"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.mappingKey = "10"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.fromHost = "0x11c"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping10.toGuest = "0x138"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.mappingKey = "11"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.fromHost = "GUI SHIFT H"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping11.toGuest = "GUI H"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.mappingKey = "12"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping12.toGuest = "GUI M"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.mappingKey = "13"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.fromHost = "GUI SHIFT P"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping13.toGuest = "GUI P"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.mappingKey = "14"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.fromHost = "GUI SHIFT V"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping14.toGuest = "GUI V"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.mappingKey = "15"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.fromHost = "GUI SHIFT X"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping15.toGuest = "GUI X"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.mappingKey = "16"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.fromHost = "GUI SHIFT Z"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping16.toGuest = "GUI Z"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.mappingKey = "17"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.fromHost = "Mouse1 Control"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping17.toGuest = "Mouse2"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.mappingKey = "18"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.fromHost = "Mouse1 GUI"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping18.toGuest = "Mouse3"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.mappingKey = "19"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.fromHost = "GUI CONTROL"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping19.toGuest = "Ungrab"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.mappingKey = "20"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.fromHost = "GUI CONTROL F"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping20.toGuest = "Fullscreen"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.mappingKey = "21"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.fromHost = "GUI SHIFT U"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping21.toGuest = "Unity"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.mappingKey = "22"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.fromHost = "GUI CONTROL S"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping22.toGuest = "SingleWindow"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.mappingKey = "23"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.fromHost = "GUI 0x029"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping23.toGuest = "CycleWindow"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.mappingKey = "24"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping24.toGuest = "CycleWindowReverse"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.mappingKey = "25"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.fromHost = "GUI H"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping25.toGuest = "HideApplication"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.mappingKey = "26"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.fromHost = "GUI M"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping26.toGuest = "MinimizeWindow"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.mappingKey = "27"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.fromHost = "GUI Q"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping27.toGuest = "Quit"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.mappingKey = "28"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping28.toGuest = "ToggleHideMenu"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.mappingKey = "29"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.fromHost = "GUI E"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile0.mapping29.toGuest = "Settings"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileKey = "521d2205-554c-06fa-8306-642f387be31a"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileName = "Mac Profile"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.profileType = "mac"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableOSShortcuts = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.enableKeyMappings = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.languageSpecificKeyMappingsEnabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.selectedLanguage = ""
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.cmdKeyFilterType = "none"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.maxMappings = "14"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.mappingKey = "0"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.fromHost = "Mouse1 Control"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping0.toGuest = "Mouse2"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.mappingKey = "1"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.fromHost = "Mouse1 GUI"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping1.toGuest = "Mouse3"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.mappingKey = "2"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.fromHost = "GUI CONTROL"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping2.toGuest = "Ungrab"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.mappingKey = "3"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.fromHost = "GUI CONTROL F"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping3.toGuest = "Fullscreen"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.mappingKey = "4"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.fromHost = "GUI CONTROL U"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping4.toGuest = "Unity"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.mappingKey = "5"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.enabled = "FALSE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.fromHost = "GUI SHIFT U"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping5.toGuest = "Unity"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.mappingKey = "6"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.enabled = "TRUE"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.fromHost = "GUI CONTROL S"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping6.toGuest = "SingleWindow"
2025-05-02T06:29:05.530Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.mappingKey = "7"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.fromHost = "GUI 0x029"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping7.toGuest = "CycleWindow"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.mappingKey = "8"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping8.toGuest = "CycleWindowReverse"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.mappingKey = "9"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.fromHost = "GUI H"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping9.toGuest = "HideApplication"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.mappingKey = "10"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.fromHost = "GUI M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping10.toGuest = "MinimizeWindow"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.mappingKey = "11"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.fromHost = "GUI Q"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping11.toGuest = "Quit"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.mappingKey = "12"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.fromHost = "GUI SHIFT M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping12.toGuest = "ToggleHideMenu"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.mappingKey = "13"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.fromHost = "GUI E"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile1.mapping13.toGuest = "Settings"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileKey = "52312ba5-d8c9-0a91-aaed-ebeb665caf10"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileName = "Windows 8 Profile"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.profileType = "windows8"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableOSShortcuts = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.enableKeyMappings = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.selectedLanguage = ""
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.cmdKeyFilterType = "none"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.maxMappings = "33"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.mappingKey = "0"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.fromHost = "GUI Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.mappingKey = "1"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.fromHost = "GUI X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping1.toGuest = "CONTROL X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.mappingKey = "2"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.fromHost = "GUI C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping2.toGuest = "CONTROL C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.mappingKey = "3"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.fromHost = "GUI V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping3.toGuest = "CONTROL V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.mappingKey = "4"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.fromHost = "GUI P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping4.toGuest = "CONTROL P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.mappingKey = "5"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.fromHost = "GUI A"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping5.toGuest = "CONTROL A"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.mappingKey = "6"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.fromHost = "GUI S"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping6.toGuest = "CONTROL S"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.mappingKey = "7"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.fromHost = "GUI F"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping7.toGuest = "0x03d"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.mappingKey = "8"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.fromHost = "GUI W"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.mappingKey = "9"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.mappingKey = "10"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.fromHost = "0x11c"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping10.toGuest = "0x138"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.mappingKey = "11"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.fromHost = "GUI SHIFT C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping11.toGuest = "GUI C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.mappingKey = "12"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.fromHost = "GUI SHIFT H"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping12.toGuest = "GUI H"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.mappingKey = "13"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.fromHost = "GUI SHIFT M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping13.toGuest = "GUI M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.mappingKey = "14"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.fromHost = "GUI SHIFT P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping14.toGuest = "GUI P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.mappingKey = "15"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.fromHost = "GUI SHIFT F"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping15.toGuest = "GUI Q"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.mappingKey = "16"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.fromHost = "GUI SHIFT V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping16.toGuest = "GUI V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.mappingKey = "17"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.fromHost = "GUI SHIFT W"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping17.toGuest = "GUI W"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.mappingKey = "18"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.fromHost = "GUI SHIFT X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping18.toGuest = "GUI X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.mappingKey = "19"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.fromHost = "GUI SHIFT Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping19.toGuest = "GUI Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.mappingKey = "20"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.fromHost = "Mouse1 Control"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping20.toGuest = "Mouse2"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.mappingKey = "21"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.fromHost = "Mouse1 GUI"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping21.toGuest = "Mouse3"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.mappingKey = "22"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.fromHost = "GUI CONTROL"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping22.toGuest = "Ungrab"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.mappingKey = "23"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.fromHost = "GUI CONTROL F"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping23.toGuest = "Fullscreen"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.mappingKey = "24"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.fromHost = "GUI SHIFT U"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping24.toGuest = "Unity"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.mappingKey = "25"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.fromHost = "GUI CONTROL S"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping25.toGuest = "SingleWindow"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.mappingKey = "26"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.fromHost = "GUI 0x029"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping26.toGuest = "CycleWindow"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.mappingKey = "27"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping27.toGuest = "CycleWindowReverse"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.mappingKey = "28"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.fromHost = "GUI H"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping28.toGuest = "HideApplication"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.mappingKey = "29"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.fromHost = "GUI M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping29.toGuest = "MinimizeWindow"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.mappingKey = "30"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.fromHost = "GUI Q"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping30.toGuest = "Quit"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.mappingKey = "31"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.fromHost = "GUI ALT SHIFT M"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping31.toGuest = "ToggleHideMenu"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.mappingKey = "32"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.enabled = "FALSE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.fromHost = "GUI E"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile2.mapping32.toGuest = "Settings"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileKey = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileName = "Profile"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.profileType = "standard"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableOSShortcuts = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.enableKeyMappings = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.languageSpecificKeyMappingsEnabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.selectedLanguage = ""
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.cmdKeyFilterType = "none"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.maxMappings = "25"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.mappingKey = "0"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.fromHost = "GUI Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping0.toGuest = "CONTROL Z"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.mappingKey = "1"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.fromHost = "GUI X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping1.toGuest = "CONTROL X"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.mappingKey = "2"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.fromHost = "GUI C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping2.toGuest = "CONTROL C"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.mappingKey = "3"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.fromHost = "GUI V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping3.toGuest = "CONTROL V"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.mappingKey = "4"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.fromHost = "GUI P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping4.toGuest = "CONTROL P"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.mappingKey = "5"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.enabled = "TRUE"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.fromHost = "GUI A"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping5.toGuest = "CONTROL A"
2025-05-02T06:29:05.531Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.mappingKey = "6"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.fromHost = "GUI S"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping6.toGuest = "CONTROL S"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.mappingKey = "7"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.fromHost = "GUI F"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping7.toGuest = "0x03d"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.mappingKey = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.fromHost = "GUI W"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping8.toGuest = "ALT 0x03e"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.mappingKey = "9"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.fromHost = "CONTROL ALT 0xe"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping9.toGuest = "CONTROL ALT 0x153"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.mappingKey = "10"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.enabled = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.fromHost = "0x11c"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping10.toGuest = "0x138"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.mappingKey = "11"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.fromHost = "Mouse1 Control"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping11.toGuest = "Mouse2"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.mappingKey = "12"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.fromHost = "Mouse1 GUI"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping12.toGuest = "Mouse3"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.mappingKey = "13"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.fromHost = "GUI CONTROL"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping13.toGuest = "Ungrab"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.mappingKey = "14"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.fromHost = "GUI CONTROL F"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping14.toGuest = "Fullscreen"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.mappingKey = "15"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.fromHost = "GUI CONTROL U"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping15.toGuest = "Unity"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.mappingKey = "16"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.fromHost = "GUI SHIFT U"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping16.toGuest = "Unity"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.mappingKey = "17"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.fromHost = "GUI CONTROL S"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping17.toGuest = "SingleWindow"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.mappingKey = "18"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.fromHost = "GUI 0x029"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping18.toGuest = "CycleWindow"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.mappingKey = "19"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.fromHost = "GUI SHIFT 0x029"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping19.toGuest = "CycleWindowReverse"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.mappingKey = "20"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.fromHost = "GUI H"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping20.toGuest = "HideApplication"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.mappingKey = "21"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.fromHost = "GUI M"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping21.toGuest = "MinimizeWindow"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.mappingKey = "22"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.fromHost = "GUI Q"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping22.toGuest = "Quit"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.mappingKey = "23"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.enabled = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.fromHost = "GUI SHIFT M"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping23.toGuest = "ToggleHideMenu"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.mappingKey = "24"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.enabled = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.fromHost = "GUI E"
2025-05-02T06:29:05.532Z In(05) vmx DICT pref.keyboardAndMouse.profile3.mapping24.toGuest = "Settings"
2025-05-02T06:29:05.532Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1746162507"
2025-05-02T06:29:05.532Z In(05) vmx DICT         vmWizard.guestKey = "ubuntu-64"
2025-05-02T06:29:05.532Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT  hint.confirmLaxOVFImport = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT    hint.dui.poweroff.soft = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT hint.loader.mitigations.wsAndFusion = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT         hint.vmui.restart = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT --- USER DEFAULTS /Users/<USER>/Library/Preferences/VMware Fusion/config
2025-05-02T06:29:05.532Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config
2025-05-02T06:29:05.532Z In(05) vmx DICT --- SITE DEFAULTS /Applications/VMware Fusion.app/Contents/Library/config
2025-05-02T06:29:05.532Z In(05) vmx DICT --- NONPERSISTENT
2025-05-02T06:29:05.532Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT --- COMMAND LINE
2025-05-02T06:29:05.532Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT --- RECORDING
2025-05-02T06:29:05.532Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT --- CONFIGURATION /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmx 
2025-05-02T06:29:05.532Z In(05) vmx DICT               displayname = "cloudera-quickstart-vm-5.13.0-0-virtualbox"
2025-05-02T06:29:05.532Z In(05) vmx DICT                   guestos = "other"
2025-05-02T06:29:05.532Z In(05) vmx DICT         virtualhw.version = "21"
2025-05-02T06:29:05.532Z In(05) vmx DICT            config.version = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT                  numvcpus = "2"
2025-05-02T06:29:05.532Z In(05) vmx DICT                   memsize = "8192"
2025-05-02T06:29:05.532Z In(05) vmx DICT              cpuid.numSMT = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT    numa.vcpu.coresPerNode = "0"
2025-05-02T06:29:05.532Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-05-02T06:29:05.532Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-05-02T06:29:05.532Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-05-02T06:29:05.532Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-05-02T06:29:05.532Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT            ide0:0.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT         ide0:0.deviceType = "disk"
2025-05-02T06:29:05.532Z In(05) vmx DICT           ide0:0.fileName = "cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk"
2025-05-02T06:29:05.532Z In(05) vmx DICT ide0:0.allowguestconnectioncontrol = "false"
2025-05-02T06:29:05.532Z In(05) vmx DICT               ide0:0.mode = "persistent"
2025-05-02T06:29:05.532Z In(05) vmx DICT       ide1:0.clientDevice = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT            ide1:0.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT         ide1:0.deviceType = "atapi-cdrom"
2025-05-02T06:29:05.532Z In(05) vmx DICT         ide1:0.autodetect = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     ide1:0.startConnected = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT ide1:0.allowguestconnectioncontrol = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2025-05-02T06:29:05.532Z In(05) vmx DICT  ethernet0.connectionType = "bridged"
2025-05-02T06:29:05.532Z In(05) vmx DICT  ethernet0.startConnected = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-05-02T06:29:05.532Z In(05) vmx DICT   ethernet0.wakeonpcktrcv = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT ethernet0.allowguestconnectioncontrol = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT  toolscripts.afterpoweron = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT   toolscripts.afterresume = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT toolscripts.beforepoweroff = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT toolscripts.beforesuspend = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT        extendedConfigFile = "cloudera-quickstart-vm-5.13.0-0-virtualbox.vmxf"
2025-05-02T06:29:05.532Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-05-02T06:29:05.532Z In(05) vmx DICT           floppy0.present = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT         vmxstats.filename = "cloudera-quickstart-vm-5.13.0-0-virtualbox.scoreboard"
2025-05-02T06:29:05.532Z In(05) vmx DICT      numa.autosize.cookie = "20012"
2025-05-02T06:29:05.532Z In(05) vmx DICT numa.autosize.vcpu.maxPerVirtualNode = "2"
2025-05-02T06:29:05.532Z In(05) vmx DICT                 uuid.bios = "56 4d 38 bf d9 c8 69 cf-d9 66 5d 88 ef 87 23 aa"
2025-05-02T06:29:05.532Z In(05) vmx DICT             uuid.location = "56 4d 16 7b 3f da 0e 48-17 37 7c bb 8d 49 a3 6b"
2025-05-02T06:29:05.532Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-05-02T06:29:05.532Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-05-02T06:29:05.532Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-05-02T06:29:05.532Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-05-02T06:29:05.532Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-05-02T06:29:05.532Z In(05) vmx DICT   ethernet0.pciSlotNumber = "32"
2025-05-02T06:29:05.532Z In(05) vmx DICT               ide0:0.redo = ""
2025-05-02T06:29:05.532Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-05-02T06:29:05.532Z In(05) vmx DICT  vmotion.checkpointFBSize = "134217728"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:29:05.532Z In(05) vmx DICT   vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:29:05.532Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:87:23:aa"
2025-05-02T06:29:05.532Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-05-02T06:29:05.532Z In(05) vmx DICT                  vmci0.id = "-783847813"
2025-05-02T06:29:05.532Z In(05) vmx DICT    monitor.phys_bits_used = "45"
2025-05-02T06:29:05.532Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT              softPowerOff = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT        checkpoint.vmState = "cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss"
2025-05-02T06:29:05.532Z In(05) vmx DICT            tools.syncTime = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT      cpuid.coresPerSocket = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT                vhv.enable = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT               vvtd.enable = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT              mks.enable3d = "TRUE"
2025-05-02T06:29:05.532Z In(05) vmx DICT     svga.graphicsMemoryKB = "8388608"
2025-05-02T06:29:05.532Z In(05) vmx DICT gui.fitGuestUsingNativeDisplayResolution = "FALSE"
2025-05-02T06:29:05.532Z In(05) vmx DICT   keyboardAndMouseProfile = "5260d4b9-7782-3924-ca37-baaf8f8ed75f"
2025-05-02T06:29:05.532Z In(05) vmx DICT   gui.lastPoweredViewMode = "fullscreen"
2025-05-02T06:29:05.532Z In(05) vmx DICT      tools.upgrade.policy = "useGlobal"
2025-05-02T06:29:05.532Z In(05) vmx DICT   vmotion.svga.supports3D = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.baseCapsLevel = "9"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.maxPointSize = "189"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.maxTextureSize = "16384"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.maxVolumeExtent = "2048"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.maxTextureAnisotropy = "16"
2025-05-02T06:29:05.532Z In(05) vmx DICT  vmotion.svga.lineStipple = "0"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.dxMaxConstantBuffers = "15"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.dxProvokingVertex = "0"
2025-05-02T06:29:05.532Z In(05) vmx DICT         vmotion.svga.sm41 = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.multisample2x = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.multisample4x = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.msFullQuality = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT     vmotion.svga.logicOps = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT         vmotion.svga.bc67 = "9"
2025-05-02T06:29:05.532Z In(05) vmx DICT          vmotion.svga.sm5 = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.multisample8x = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.logicBlendOps = "0"
2025-05-02T06:29:05.532Z In(05) vmx DICT vmotion.svga.maxForcedSampleCount = "8"
2025-05-02T06:29:05.532Z In(05) vmx DICT         vmotion.svga.gl43 = "1"
2025-05-02T06:29:05.532Z In(05) vmx DICT     gui.viewModeAtPowerOn = "fullscreen"
2025-05-02T06:29:05.532Z In(05) vmx DICT --- USER DEFAULTS ~/Library/Preferences/VMware Fusion/config 
2025-05-02T06:29:05.532Z In(05) vmx DICT --- HOST DEFAULTS /Library/Preferences/VMware Fusion/config 
2025-05-02T06:29:05.532Z In(05) vmx DICT --- SITE DEFAULTS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/config 
2025-05-02T06:29:05.533Z In(05) vmx DICT --- GLOBAL SETTINGS /dev/null/Non-existing DEFAULT_LIBDIRECTORY/settings 
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.diskLibDataVmdkOpenTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.diskLibDataVmdkGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 5 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 6 stats: vmx.diskLibDigestVmdkOpenTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 7 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 8 stats: vmx.diskLibDigestVmdkGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 9 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 10 stats: vmx.diskLibDigestFileDataGrowTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 11 stats: vmx.digestLibOpenIntTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 12 stats: vmx.digestLibOpenIntTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 13 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 14 stats: vmx.diskLibDataVmdkCloseTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 15 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 16 stats: vmx.diskLibDigestVmdkCloseTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 17 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 18 stats: vmx.diskLibVmdkCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 19 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 20 stats: vmx.diskLibChildVmdkCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 21 stats: vmx.snapshotCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 22 stats: vmx.snapshotCreateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 23 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 24 stats: vmx.snapshotCreateQuiescedTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 25 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 26 stats: vmx.snapshotCreateMemoryTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 27 stats: vmx.snapshotDeleteTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 28 stats: vmx.snapshotDeleteTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 29 stats: vmx.snapshotConsolidateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 30 stats: vmx.snapshotConsolidateTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 31 stats: vmx.checkpointStunTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 32 stats: vmx.checkpointStunTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 33 stats: vmx.setPolicyTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 34 stats: vmx.setPolicyTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 35 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 36 stats: vmx.filtlibApplyDiskConfigTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 37 stats: vmx.diskLibGetInfoTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 38 stats: vmx.diskLibGetInfoTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 39 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:29:05.533Z In(05) vmx VMXSTATS: Registering 40 stats: vmx.diskLibDigestGetInfoTime
2025-05-02T06:29:05.533Z In(05) vmx Powering on guestOS 'other' using the configuration for 'other'.
2025-05-02T06:29:05.534Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:29:05.534Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:29:05.534Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2025-05-02T06:29:05.534Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2025-05-02T06:29:05.534Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2025-05-02T06:29:05.535Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-05-02T06:29:05.535Z In(05) vmx Checkpointed in VMware Fusion, 13.5.2, build-23775688, Mac OS Host
2025-05-02T06:29:05.536Z In(05) vmx Resuming virtual machine from /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss with 8192 MB of memory.
2025-05-02T06:29:05.536Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:29:05.536Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:29:05.537Z In(05) vmx Monitor Mode: ULM
2025-05-02T06:29:05.537Z In(05) vmx MsgHint: msg.loader.mitigations.wsAndFusion
2025-05-02T06:29:05.537Z In(05)+ vmx You are running this virtual machine with side channel mitigations enabled. Side channel mitigations provide enhanced security but also lower performance.
2025-05-02T06:29:05.537Z In(05)+ vmx 
2025-05-02T06:29:05.537Z In(05)+ vmx To disable mitigations, change the side channel mitigations setting in the advanced panel of the virtual machine settings. Refer to VMware KB article 79832 at https://kb.vmware.com/s/article/79832 for more details.
2025-05-02T06:29:05.537Z In(05)+ vmx ---------------------------------------
2025-05-02T06:29:05.550Z In(05) vmx OvhdMem_PowerOn: initial admission: paged  4141938 nonpaged    37036 anonymous    22556
2025-05-02T06:29:05.550Z In(05) vmx VMMEM: Initial Reservation: 16412MB (MainMem=8192MB)
2025-05-02T06:29:05.551Z In(05) vmx numa: Resuming from checkpoint using VPD = 2
2025-05-02T06:29:05.551Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-05-02T06:29:05.551Z In(05) vmx llc: vLLC size: 1
2025-05-02T06:29:05.551Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 6553 (80% of min required size 8192)
2025-05-02T06:29:05.551Z In(05) PowerNotifyThread VTHREAD 123145323180032 "PowerNotifyThread" tid 188283
2025-05-02T06:29:05.551Z In(05) PowerNotifyThread PowerNotify thread is alive.
2025-05-02T06:29:05.552Z In(05) vmx VMXSTATS: Registering 41 stats: vmx.logBytesDropped
2025-05-02T06:29:05.552Z In(05) vmx VMXSTATS: Registering 42 stats: vmx.logMsgsDropped
2025-05-02T06:29:05.552Z In(05) vmx VMXSTATS: Registering 43 stats: vmx.logBytesLogged
2025-05-02T06:29:05.552Z In(05) vmx VMXSTATS: Registering 44 stats: vmx.logWriteMinMaxTime
2025-05-02T06:29:05.552Z In(05) vmx VMXSTATS: Registering 45 stats: vmx.logWriteAvgTime
2025-05-02T06:29:05.552Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Fusion)
2025-05-02T06:29:05.552Z In(05) vthread-188284 VTHREAD 123145323716608 "vthread-188284" tid 188284
2025-05-02T06:29:05.552Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:29:05.552Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:29:05.553Z In(05) vmx Host PA size: 39 bits. Guest PA size: 45 bits.
2025-05-02T06:29:05.553Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:29:05.553Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:29:05.554Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2025-05-02T06:29:05.554Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2025-05-02T06:29:05.554Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2025-05-02T06:29:05.554Z In(05) deviceThread VTHREAD 123145324253184 "deviceThread" tid 188286
2025-05-02T06:29:05.554Z In(05) deviceThread Device thread is alive
2025-05-02T06:29:05.554Z In(05) vmx Host VT-x Capabilities:
2025-05-02T06:29:05.554Z In(05) vmx Basic VMX Information (0x00da040000000004)
2025-05-02T06:29:05.554Z In(05) vmx   VMCS revision ID                           4
2025-05-02T06:29:05.554Z In(05) vmx   VMCS region length                      1024
2025-05-02T06:29:05.554Z In(05) vmx   VMX physical-address width           natural
2025-05-02T06:29:05.554Z In(05) vmx   SMM dual-monitor mode                    yes
2025-05-02T06:29:05.554Z In(05) vmx   VMCS memory type                          WB
2025-05-02T06:29:05.554Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-05-02T06:29:05.554Z In(05) vmx   True VMX MSRs                            yes
2025-05-02T06:29:05.554Z In(05) vmx   Exception Injection ignores error code    no
2025-05-02T06:29:05.554Z In(05) vmx True Pin-Based VM-Execution Controls (0x0000007f00000016)
2025-05-02T06:29:05.554Z In(05) vmx   External-interrupt exiting               {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   NMI exiting                              {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Virtual NMIs                             {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Process posted interrupts                { 0 }
2025-05-02T06:29:05.554Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfff9fffe04006172)
2025-05-02T06:29:05.554Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   HLT exiting                              {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   INVLPG exiting                           {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   MWAIT exiting                            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   RDPMC exiting                            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   RDTSC exiting                            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   CR3-load exiting                         {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   CR3-store exiting                        {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Activate tertiary controls               { 0 }
2025-05-02T06:29:05.554Z In(05) vmx   CR8-load exiting                         {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   CR8-store exiting                        {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Use TPR shadow                           {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   NMI-window exiting                       {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Monitor trap flag                        {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   MONITOR exiting                          {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   PAUSE exiting                            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Activate secondary controls              {0,1}
2025-05-02T06:29:05.554Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x00515cef000000a2)
2025-05-02T06:29:05.554Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Enable EPT                               { 1 }
2025-05-02T06:29:05.554Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-05-02T06:29:05.554Z In(05) vmx   Virtualize x2APIC mode                   { 0 }
2025-05-02T06:29:05.554Z In(05) vmx   Enable VPID                              { 1 }
2025-05-02T06:29:05.554Z In(05) vmx   WBINVD exiting                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Unrestricted guest                       { 1 }
2025-05-02T06:29:05.555Z In(05) vmx   APIC-register virtualization             { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Virtual-interrupt delivery               { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   RDRAND exiting                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Enable INVPCID                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Enable VM Functions                      { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Use VMCS shadowing                       {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   ENCLS exiting                            { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   RDSEED exiting                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Enable PML                               { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   EPT-violation #VE                        { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Conceal VMX from PT                      { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   PASID translation                        { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   PT uses guest physical addresses         { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Use TSC scaling                          { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Bus lock exiting                         { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Notification VM exits                    { 0 }
2025-05-02T06:29:05.555Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2025-05-02T06:29:05.555Z In(05) vmx   LOADIWKEY exiting                          no
2025-05-02T06:29:05.555Z In(05) vmx   Enable HLAT                                no
2025-05-02T06:29:05.555Z In(05) vmx   Enable Paging-Write                        no
2025-05-02T06:29:05.555Z In(05) vmx   Enable Guest Paging Verification           no
2025-05-02T06:29:05.555Z In(05) vmx   Enable IPI Virtualization                  no
2025-05-02T06:29:05.555Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-05-02T06:29:05.555Z In(05) vmx True VM-Exit Controls (0x01ffffff00036dfb)
2025-05-02T06:29:05.555Z In(05) vmx   Save debug controls                      {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Host address-space size                  {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Save VMX-preemption timer                {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Clear IA32_BNDCFGS                       {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Clear IA32_RTIT MSR                      { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:29:05.555Z In(05) vmx True VM-Entry Controls (0x0003ffff000011fb)
2025-05-02T06:29:05.555Z In(05) vmx   Load debug controls                      {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Entry to SMM                             {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_BNDCFGS                        {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_RTIT MSR                       { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2025-05-02T06:29:05.555Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:29:05.555Z In(05) vmx VPID and EPT Capabilities (0x00000f0106734141)
2025-05-02T06:29:05.555Z In(05) vmx   R=0/W=0/X=1                               yes
2025-05-02T06:29:05.555Z In(05) vmx   Page-walk length 3                        yes
2025-05-02T06:29:05.555Z In(05) vmx   EPT memory type WB                        yes
2025-05-02T06:29:05.555Z In(05) vmx   2MB super-page                            yes
2025-05-02T06:29:05.555Z In(05) vmx   1GB super-page                            yes
2025-05-02T06:29:05.555Z In(05) vmx   INVEPT support                            yes
2025-05-02T06:29:05.555Z In(05) vmx   Access & Dirty Bits                       yes
2025-05-02T06:29:05.555Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2025-05-02T06:29:05.555Z In(05) vmx   Supervisor shadow-stack control            no
2025-05-02T06:29:05.555Z In(05) vmx   Type 1 INVEPT                             yes
2025-05-02T06:29:05.555Z In(05) vmx   Type 2 INVEPT                             yes
2025-05-02T06:29:05.555Z In(05) vmx   INVVPID support                           yes
2025-05-02T06:29:05.555Z In(05) vmx   Type 0 INVVPID                            yes
2025-05-02T06:29:05.555Z In(05) vmx   Type 1 INVVPID                            yes
2025-05-02T06:29:05.555Z In(05) vmx   Type 2 INVVPID                            yes
2025-05-02T06:29:05.555Z In(05) vmx   Type 3 INVVPID                            yes
2025-05-02T06:29:05.555Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2025-05-02T06:29:05.555Z In(05) vmx   TSC to preemption timer ratio      7
2025-05-02T06:29:05.555Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-05-02T06:29:05.555Z In(05) vmx   Activity State HLT               yes
2025-05-02T06:29:05.555Z In(05) vmx   Activity State shutdown          yes
2025-05-02T06:29:05.555Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-05-02T06:29:05.555Z In(05) vmx   Processor trace in VMX           yes
2025-05-02T06:29:05.555Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2025-05-02T06:29:05.555Z In(05) vmx   CR3 targets supported              4
2025-05-02T06:29:05.555Z In(05) vmx   Maximum MSR list size            512
2025-05-02T06:29:05.555Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2025-05-02T06:29:05.555Z In(05) vmx   Allow all VMWRITEs               yes
2025-05-02T06:29:05.555Z In(05) vmx   Allow zero instruction length    yes
2025-05-02T06:29:05.555Z In(05) vmx   MSEG revision ID                   0
2025-05-02T06:29:05.555Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-05-02T06:29:05.555Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-05-02T06:29:05.555Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-05-02T06:29:05.555Z In(05) vmx   Variable          0x000000007fffffde
2025-05-02T06:29:05.555Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x00000000003767ff)
2025-05-02T06:29:05.555Z In(05) vmx   Fixed to 0        0xffffffffffc89800
2025-05-02T06:29:05.555Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-05-02T06:29:05.555Z In(05) vmx   Variable          0x00000000003747ff
2025-05-02T06:29:05.555Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2025-05-02T06:29:05.555Z In(05) vmx   Highest index                   0x17
2025-05-02T06:29:05.555Z In(05) vmx VM Functions (0x0000000000000000)
2025-05-02T06:29:05.555Z In(05) vmx KHZEstimate 2592000
2025-05-02T06:29:05.555Z In(05) vmx MHZEstimate 2592
2025-05-02T06:29:05.555Z In(05) vmx NumVCPUs 2
2025-05-02T06:29:05.556Z In(05) vmx Msg_Question:
2025-05-02T06:29:05.556Z In(05) vmx [msg.uuid.altered] This virtual machine might have been moved or copied.
2025-05-02T06:29:05.556Z In(05)+ vmx In order to configure certain management and networking features, VMware Fusion needs to know if this virtual machine was moved or copied. 
2025-05-02T06:29:05.556Z In(05)+ vmx 
2025-05-02T06:29:05.556Z In(05)+ vmx If you don't know, answer "I Co_pied It".
2025-05-02T06:29:05.556Z In(05)+ vmx 
2025-05-02T06:29:05.556Z In(05) vmx ----------------------------------------
2025-05-02T06:29:07.416Z In(05) vmx MsgQuestion: msg.uuid.altered reply=2
2025-05-02T06:29:07.416Z In(05) vmx UUID: Writing uuid.bios value: '56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b'
2025-05-02T06:29:07.416Z No(00) vmx ConfigDB: Setting uuid.bios = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
2025-05-02T06:29:07.416Z In(05) vmx UUID: Writing uuid.location value: '56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b'
2025-05-02T06:29:07.416Z No(00) vmx ConfigDB: Setting uuid.location = "56 4d 87 8e 6e 77 a6 cc-e5 d7 43 cc d1 47 6e 7b"
2025-05-02T06:29:07.417Z In(05) vmx VMXSTATS: Registering 46 stats: vmx.configWriteMinMaxTime
2025-05-02T06:29:07.417Z In(05) vmx VMXSTATS: Registering 47 stats: vmx.configWriteAvgTime
2025-05-02T06:29:07.417Z No(00) vmx PowerOnTiming: Module UUIDVMX took 1861725 us
2025-05-02T06:29:07.418Z In(05) vmx AIOGNRC: numThreads=4 ide=1, scsi=0, passthru=1
2025-05-02T06:29:07.418Z In(05) vmx WORKER: Creating new group with maxThreads=4 (4)
2025-05-02T06:29:07.422Z In(05) vmx WORKER: Creating new group with maxThreads=1 (5)
2025-05-02T06:29:07.422Z In(05) vmx MainMem: CPT Host WZ=0 PF=8192 D=0
2025-05-02T06:29:07.422Z In(05) vmx MainMem: CPT PLS=1 PLR=0 BS=1 BlkP=32 Mult=4 W=50
2025-05-02T06:29:07.422Z In(05) vmx MStat: Creating Stat vm.uptime
2025-05-02T06:29:07.422Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-05-02T06:29:07.422Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-05-02T06:29:07.422Z In(05) vmx VMXAIOMGR: Using: simple=Generic
2025-05-02T06:29:07.424Z In(05) vmx WORKER: Creating new group with maxThreads=1 (6)
2025-05-02T06:29:07.425Z In(05) machPoll VTHREAD 123145324789760 "machPoll" tid 188340
2025-05-02T06:29:07.427Z In(05) vmx WORKER: Creating new group with maxThreads=1 (7)
2025-05-02T06:29:07.427Z In(05) vmx WORKER: Creating new group with maxThreads=14 (21)
2025-05-02T06:29:07.428Z In(05) vmx FeatureCompat: No VM masks.
2025-05-02T06:29:07.428Z In(05) vmx TimeTracker host to guest rate conversion 5057982200 @ 2592000000Hz -> 0 @ 2592000000Hz
2025-05-02T06:29:07.428Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -5057982200
2025-05-02T06:29:07.428Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-02T06:29:07.428Z In(05) vmx TSC offsetting enabled.
2025-05-02T06:29:07.428Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-02T06:29:07.428Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-02T06:29:07.428Z In(05) vmx MKS PowerOn
2025-05-02T06:29:07.429Z In(05) mks VTHREAD 123145326899200 "mks" tid 188341
2025-05-02T06:29:07.430Z In(05) mks MKS thread is alive
2025-05-02T06:29:07.430Z In(05) svga VTHREAD 123145335300096 "svga" tid 188342
2025-05-02T06:29:07.430Z In(05) svga SVGA thread is alive
2025-05-02T06:29:07.430Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-05-02T06:29:07.465Z In(05) mks MKS MacOSMouse: Using event tap path.
2025-05-02T06:29:07.466Z In(05) mouse VTHREAD 123145335836672 "mouse" tid 188344
2025-05-02T06:29:07.466Z In(05) keyboard VTHREAD 123145336373248 "keyboard" tid 188345
2025-05-02T06:29:07.527Z In(05) keyboard MKS MacOSKeyboard: Adding device: (0) VID:05AC PID:027C Apple Inc. (Apple Internal Keyboard / Trackpad)
2025-05-02T06:29:07.531Z In(05) keyboard MKS MacOSKeyboard: Adding device: (1) VID:05AC PID:8600 <missing> (TouchBarUserDevice)
2025-05-02T06:29:07.531Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps MTLRenderer ISBRenderer 
2025-05-02T06:29:07.531Z In(05) mks MKS-RenderMain: ISB enabled by config
2025-05-02T06:29:07.532Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from ISBRenderer
2025-05-02T06:29:07.532Z In(05) mks MKS-RenderMain: Starting ISBRenderer
2025-05-02T06:29:07.532Z In(05) mks ISBRendererComm: ISBRendererComm DataChannel size=4294967296
2025-05-02T06:29:07.532Z In(05) mks LocalMKS: MKS_UUID=52 c2 c3 0a 96 98 fe 6f-83 7b 15 6a a4 6e b6 8a
2025-05-02T06:29:07.532Z In(05) mks LogRotation: Rotating to a new log file (keepOld 3) took 0.000159 seconds.
2025-05-02T06:29:07.533Z In(05) mks ISBRendererComm: mksSandbox command-line: /Applications/VMware Fusion.app/Contents/Library/mksSandbox --pipeInfo 3
2025-05-02T06:29:07.540Z In(05) mks ISBRendererComm: Spawned process with pid 21396
2025-05-02T06:29:08.177Z In(05) mks ISBRendererComm: Sandbox Renderer: MTLRenderer
2025-05-02T06:29:08.181Z In(05) mks MKS-RenderMain: Started ISBRenderer with (MTLRenderer)
2025-05-02T06:29:08.182Z In(05) mks MKS-RenderMain: Found Full Renderer: ISBRenderer (MTLRenderer)
2025-05-02T06:29:08.182Z In(05) mks MKS-RenderMain: maxTextureSize=16384
2025-05-02T06:29:08.182Z In(05) mks SOCKET 2 (63) creating new listening socket on port -1
2025-05-02T06:29:08.182Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-05-02T06:29:08.182Z In(05) mks MKSRemoteMgr: Set default display name: cloudera-quickstart-vm-5.13.0-0-virtualbox
2025-05-02T06:29:08.182Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-05-02T06:29:08.182Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-05-02T06:29:08.182Z No(00) vmx PowerOnTiming: Module MKS took 754207 us
2025-05-02T06:29:08.183Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-05-02T06:29:08.183Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-05-02T06:29:08.183Z In(05) vmx Chipset version: 0x13
2025-05-02T06:29:08.362Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-05-02T06:29:08.362Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2025-05-02T06:29:08.362Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2025-05-02T06:29:08.362Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2025-05-02T06:29:08.363Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-05-02T06:29:08.363Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2025-05-02T06:29:08.363Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2025-05-02T06:29:08.363Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2025-05-02T06:29:08.371Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-05-02T06:29:08.371Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-05-02T06:29:08.371Z No(00) vmx ConfigDB: Setting ide0:0.redo = ""
2025-05-02T06:29:08.371Z In(05) vmx DISK: OPEN ide0:0 '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' persistent R[]
2025-05-02T06:29:08.373Z In(05) vmx Current OS Release is 24.5.0
2025-05-02T06:29:08.373Z In(05) vmx DISK: Disk '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' has UUID '60 00 c2 9d f9 58 79 c5-4d 5f 84 b7 6f e5 19 87'
2025-05-02T06:29:08.373Z In(05) vmx DISK: OPEN '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-disk1.vmdk' Geo (133152/16/63) BIOS Geo (8354/255/63)
2025-05-02T06:29:08.374Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'ide0:0' identified as Virtual SSD device.
2025-05-02T06:29:08.374Z In(05) vmx DISK: Opening disks took 2 ms.
2025-05-02T06:29:08.374Z Wa(03) vmx USB: No USB controllers found.
2025-05-02T06:29:08.376Z In(05) vmx DISKUTIL: ide0:0 : capacity=134217728 logical sector size=512
2025-05-02T06:29:08.376Z In(05) vmx SCSI DEVICE (ide0:0): Computed value of ide0:0.useBounceBuffers: default
2025-05-02T06:29:08.376Z In(05) vmx DISKUTIL: ide0:0 : capacity=134217728 logical sector size=512
2025-05-02T06:29:08.376Z In(05) vmx DISKUTIL: ide0:0 : geometry=8354/255/63
2025-05-02T06:29:08.376Z In(05) vmx SCSI DEVICE (ide1:0): Computed value of ide1:0.useBounceBuffers: default
2025-05-02T06:29:08.376Z In(05) vmx DISKUTIL: ide1:0 : capacity=0 logical sector size=2048
2025-05-02T06:29:08.376Z In(05) vmx DISKUTIL: ide1:0 : geometry=0/0/0
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 134217728
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2025-05-02T06:29:08.377Z In(05) vmx SVGA-GFB: Max wh(6688, 5016), number of displays: 10
2025-05-02T06:29:08.377Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2025-05-02T06:29:08.377Z No(00) vmx ConfigDB: Setting vmotion.svga.mobMaxSize = "1073741824"
2025-05-02T06:29:08.377Z No(00) vmx ConfigDB: Setting vmotion.svga.graphicsMemoryKB = "8388608"
2025-05-02T06:29:08.377Z In(05) vmx SVGA: mobMaxSize=1073741824
2025-05-02T06:29:08.377Z In(05) vmx SVGA: graphicsMemoryKB=8388608
2025-05-02T06:29:08.377Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 511
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 16384
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 189
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 16384
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 2048
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 16
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 8
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host id: 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.supports3D bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.baseCapsLevel num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.maxPointSize num 189
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.maxTextureSize num 16384
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.maxVolumeExtent num 2048
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.maxTextureAnisotropy num 16
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.lineStipple bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.dxProvokingVertex bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.sm41 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.multisample2x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.multisample4x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.msFullQuality bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.logicOps bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.bc67 num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.sm5 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.multisample8x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.logicBlendOps bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.maxForcedSampleCount num 8
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature    host svga.gl43 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost id: 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.supports3D bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.baseCapsLevel num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.maxPointSize num 189
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.maxTextureSize num 16384
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.maxVolumeExtent num 2048
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.maxTextureAnisotropy num 16
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.lineStipple bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.dxProvokingVertex bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.sm41 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.multisample2x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.multisample4x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.msFullQuality bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.logicOps bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.bc67 num 9
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.sm5 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.multisample8x bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.logicBlendOps bool 0
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.maxForcedSampleCount num 8
2025-05-02T06:29:08.377Z In(05) vmx SVGAFeature evcHost svga.gl43 bool 1
2025-05-02T06:29:08.377Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:29:08.378Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:29:08.378Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:29:08.378Z In(05) vmx SVGA3dCaps: host, at power on (3d enabled)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:29:08.378Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:29:08.378Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:29:08.378Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:29:08.378Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:29:08.378Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:29:08.378Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:29:08.378Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:29:08.378Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:29:08.378Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:29:08.378Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:29:08.378Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:29:08.378Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:29:08.378Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:29:08.378Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:29:08.378Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:29:08.378Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:29:08.378Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:29:08.378Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:29:08.378Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:29:08.378Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:29:08.378Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:29:08.378Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:29:08.378Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:29:08.379Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:29:08.379Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:29:08.379Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:29:08.379Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:29:08.379Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:29:08.379Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:29:08.379Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:29:08.379Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:29:08.379Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:29:08.379Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:29:08.379Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:29:08.379Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:29:08.379Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:29:08.379Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:29:08.379Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:29:08.379Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:29:08.379Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:29:08.379Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:29:08.379Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:29:08.379Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:29:08.379Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:29:08.379Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     9 (    9,     9)
2025-05-02T06:29:08.379Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     9 (    9,     9)
2025-05-02T06:29:08.379Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     1 (    1,    10)
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config id: 0
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.supports3D bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.baseCapsLevel num 9
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.maxPointSize num 189
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.maxTextureSize num 16384
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.maxVolumeExtent num 2048
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.maxTextureAnisotropy num 16
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.lineStipple bool 0
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.dxProvokingVertex bool 0
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.sm41 bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.multisample2x bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.multisample4x bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.msFullQuality bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.logicOps bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.bc67 num 9
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.sm5 bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.multisample8x bool 1
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.logicBlendOps bool 0
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.maxForcedSampleCount num 8
2025-05-02T06:29:08.379Z In(05) vmx SVGAFeature config svga.gl43 bool 1
2025-05-02T06:29:08.382Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddress = "00:0c:29:47:6e:7b"
2025-05-02T06:29:08.382Z No(00) vmx ConfigDB: Setting ethernet0.generatedAddressOffset = "0"
2025-05-02T06:29:08.382Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:47:6e:7b
2025-05-02T06:29:08.383Z No(00) vmx ConfigDB: Setting vmci0.id = "-783847813"
2025-05-02T06:29:08.390Z In(05) vmx WORKER: Creating new group with maxThreads=1 (22)
2025-05-02T06:29:08.390Z In(05) vmx DISKUTIL: (null) : max toolsVersion = 0, type = 0
2025-05-02T06:29:08.390Z In(05) vmx DISKUTIL: Offline toolsVersion = 0, type = 0
2025-05-02T06:29:08.390Z In(05) vmx TOOLS setting legacy tools version to '0' type 0, manifest status is 9
2025-05-02T06:29:08.390Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:29:08.390Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:29:08.390Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2025-05-02T06:29:08.390Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2025-05-02T06:29:08.390Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2025-05-02T06:29:08.390Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2025-05-02T06:29:08.390Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2025-05-02T06:29:08.391Z In(05) vmx ToolsISO: open of /Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig failed: Could not find the file
2025-05-02T06:29:08.391Z In(05) vmx ToolsISO: Unable to read signature file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt.sig', ignoring.
2025-05-02T06:29:08.391Z In(05) vmx ToolsISO: Guest 'other' not found in Tools image map file '/Applications/VMware Fusion.app/Contents/Library/isoimages/x86_x64/isoimages_manifest.txt'
2025-05-02T06:29:08.391Z In(05) vmx ToolsISO: Updated cached value for imageName to '(null)'.
2025-05-02T06:29:08.391Z In(05) vmx GuestOS: GuestOS_GetToolsImageName: Failed to get Tools ISO image path.
2025-05-02T06:29:08.391Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2025-05-02T06:29:08.391Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2025-05-02T06:29:08.391Z In(05) vmx Tools: sending 'OS_Resume' (state = 4) state change request
2025-05-02T06:29:08.391Z In(05) vmx Tools: Delaying state change request to state 4.
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2025-05-02T06:29:08.391Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2025-05-02T06:29:08.391Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2025-05-02T06:29:08.391Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2025-05-02T06:29:08.392Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2025-05-02T06:29:08.392Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2025-05-02T06:29:08.392Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2025-05-02T06:29:08.394Z In(05) vmx CPT: Restoring checkpoint /Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss
2025-05-02T06:29:08.394Z In(05) vmx DUMPER: Restoring checkpoint version 8.
2025-05-02T06:29:08.395Z In(05) vmx PStrIntern expansion: nBkts=256
2025-05-02T06:29:08.395Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0x8
2025-05-02T06:29:08.395Z In(05) vmx Progress -1% (msg.checkpoint.restoreStatus)
2025-05-02T06:29:08.396Z In(05) vmx   restoring GuestVars
2025-05-02T06:29:08.396Z In(05) vmx   restoring A20
2025-05-02T06:29:08.396Z In(05) vmx   restoring VVTD
2025-05-02T06:29:08.396Z In(05) vmx   restoring UUIDVMX
2025-05-02T06:29:08.396Z In(05) vmx   restoring memory
2025-05-02T06:29:08.396Z In(05) vmx DUMPER: Item 'hotSetSize' [-1, -1] not found.
2025-05-02T06:29:08.396Z In(05) vmx MainMem: Opened paging file, 'UNNAMED' (swap).
2025-05-02T06:29:08.396Z In(05) vmx MainMem: Read full memory image (SF=0 L=0 SZ=1 RM=0 U=0 TM=0).
2025-05-02T06:29:08.399Z In(05) vmx Progress 0% (none)
2025-05-02T06:29:08.441Z In(05) vmx Progress 1% (none)
2025-05-02T06:29:08.488Z In(05) vmx Progress 2% (none)
2025-05-02T06:29:08.532Z In(05) vmx Progress 3% (none)
2025-05-02T06:29:08.575Z In(05) vmx Progress 4% (none)
2025-05-02T06:29:08.625Z In(05) vmx Progress 5% (none)
2025-05-02T06:29:08.675Z In(05) vmx Progress 6% (none)
2025-05-02T06:29:08.726Z In(05) vmx Progress 7% (none)
2025-05-02T06:29:08.780Z In(05) vmx Progress 8% (none)
2025-05-02T06:29:08.831Z In(05) vmx Progress 9% (none)
2025-05-02T06:29:08.883Z In(05) vmx Progress 10% (none)
2025-05-02T06:29:08.932Z In(05) vmx Progress 11% (none)
2025-05-02T06:29:08.984Z In(05) vmx Progress 12% (none)
2025-05-02T06:29:09.037Z In(05) vmx Progress 13% (none)
2025-05-02T06:29:09.086Z In(05) vmx Progress 14% (none)
2025-05-02T06:29:09.138Z In(05) vmx Progress 15% (none)
2025-05-02T06:29:09.186Z In(05) vmx Progress 16% (none)
2025-05-02T06:29:09.236Z In(05) vmx Progress 17% (none)
2025-05-02T06:29:09.288Z In(05) vmx Progress 18% (none)
2025-05-02T06:29:09.337Z In(05) vmx Progress 19% (none)
2025-05-02T06:29:09.387Z In(05) vmx Progress 20% (none)
2025-05-02T06:29:09.432Z In(05) vmx Progress 21% (none)
2025-05-02T06:29:09.480Z In(05) vmx Progress 22% (none)
2025-05-02T06:29:09.525Z In(05) vmx Progress 23% (none)
2025-05-02T06:29:09.567Z In(05) vmx Progress 24% (none)
2025-05-02T06:29:09.610Z In(05) vmx Progress 25% (none)
2025-05-02T06:29:09.652Z In(05) vmx Progress 26% (none)
2025-05-02T06:29:09.695Z In(05) vmx Progress 27% (none)
2025-05-02T06:29:09.741Z In(05) vmx Progress 28% (none)
2025-05-02T06:29:09.784Z In(05) vmx Progress 29% (none)
2025-05-02T06:29:09.828Z In(05) vmx Progress 30% (none)
2025-05-02T06:29:09.870Z In(05) vmx Progress 31% (none)
2025-05-02T06:29:09.916Z In(05) vmx Progress 32% (none)
2025-05-02T06:29:09.966Z In(05) vmx Progress 33% (none)
2025-05-02T06:29:10.019Z In(05) vmx Progress 34% (none)
2025-05-02T06:29:10.074Z In(05) vmx Progress 35% (none)
2025-05-02T06:29:10.189Z In(05) vmx Progress 36% (none)
2025-05-02T06:29:10.257Z In(05) vmx Progress 37% (none)
2025-05-02T06:29:10.340Z In(05) vmx Progress 38% (none)
2025-05-02T06:29:10.438Z In(05) vmx Progress 39% (none)
2025-05-02T06:29:10.622Z In(05) vmx Progress 40% (none)
2025-05-02T06:29:10.734Z In(05) vmx Progress 41% (none)
2025-05-02T06:29:10.843Z In(05) vmx Progress 42% (none)
2025-05-02T06:29:10.908Z In(05) vmx Progress 43% (none)
2025-05-02T06:29:10.970Z In(05) vmx Progress 44% (none)
2025-05-02T06:29:11.034Z In(05) vmx Progress 45% (none)
2025-05-02T06:29:11.106Z In(05) vmx Progress 46% (none)
2025-05-02T06:29:11.184Z In(05) vmx Progress 47% (none)
2025-05-02T06:29:11.264Z In(05) vmx Progress 48% (none)
2025-05-02T06:29:11.337Z In(05) vmx Progress 49% (none)
2025-05-02T06:29:11.417Z In(05) vmx Progress 50% (none)
2025-05-02T06:29:11.489Z In(05) vmx Progress 51% (none)
2025-05-02T06:29:11.559Z In(05) vmx Progress 52% (none)
2025-05-02T06:29:11.633Z In(05) vmx Progress 53% (none)
2025-05-02T06:29:11.708Z In(05) vmx Progress 54% (none)
2025-05-02T06:29:11.785Z In(05) vmx Progress 55% (none)
2025-05-02T06:29:11.856Z In(05) vmx Progress 56% (none)
2025-05-02T06:29:11.930Z In(05) vmx Progress 57% (none)
2025-05-02T06:29:12.013Z In(05) vmx Progress 58% (none)
2025-05-02T06:29:12.095Z In(05) vmx Progress 59% (none)
2025-05-02T06:29:12.175Z In(05) vmx Progress 60% (none)
2025-05-02T06:29:12.242Z In(05) vmx Progress 61% (none)
2025-05-02T06:29:12.313Z In(05) vmx Progress 62% (none)
2025-05-02T06:29:12.389Z In(05) vmx Progress 63% (none)
2025-05-02T06:29:12.459Z In(05) vmx Progress 64% (none)
2025-05-02T06:29:12.540Z In(05) vmx Progress 65% (none)
2025-05-02T06:29:12.621Z In(05) vmx Progress 66% (none)
2025-05-02T06:29:12.698Z In(05) vmx Progress 67% (none)
2025-05-02T06:29:12.775Z In(05) vmx Progress 68% (none)
2025-05-02T06:29:12.854Z In(05) vmx Progress 69% (none)
2025-05-02T06:29:12.937Z In(05) vmx Progress 70% (none)
2025-05-02T06:29:13.019Z In(05) vmx Progress 71% (none)
2025-05-02T06:29:13.097Z In(05) vmx Progress 72% (none)
2025-05-02T06:29:13.179Z In(05) vmx Progress 73% (none)
2025-05-02T06:29:13.258Z In(05) vmx Progress 74% (none)
2025-05-02T06:29:13.344Z In(05) vmx Progress 75% (none)
2025-05-02T06:29:13.427Z In(05) vmx Progress 76% (none)
2025-05-02T06:29:13.506Z In(05) vmx Progress 77% (none)
2025-05-02T06:29:13.589Z In(05) vmx Progress 78% (none)
2025-05-02T06:29:13.672Z In(05) vmx Progress 79% (none)
2025-05-02T06:29:13.754Z In(05) vmx Progress 80% (none)
2025-05-02T06:29:13.840Z In(05) vmx Progress 81% (none)
2025-05-02T06:29:13.923Z In(05) vmx Progress 82% (none)
2025-05-02T06:29:14.016Z In(05) vmx Progress 83% (none)
2025-05-02T06:29:14.106Z In(05) vmx Progress 84% (none)
2025-05-02T06:29:14.198Z In(05) vmx Progress 85% (none)
2025-05-02T06:29:14.283Z In(05) vmx Progress 86% (none)
2025-05-02T06:29:14.374Z In(05) vmx Progress 87% (none)
2025-05-02T06:29:14.470Z In(05) vmx Progress 88% (none)
2025-05-02T06:29:14.559Z In(05) vmx Progress 89% (none)
2025-05-02T06:29:14.661Z In(05) vmx Progress 90% (none)
2025-05-02T06:29:14.756Z In(05) vmx Progress 91% (none)
2025-05-02T06:29:14.851Z In(05) vmx Progress 92% (none)
2025-05-02T06:29:14.951Z In(05) vmx Progress 93% (none)
2025-05-02T06:29:15.042Z In(05) vmx Progress 94% (none)
2025-05-02T06:29:15.129Z In(05) vmx Progress 95% (none)
2025-05-02T06:29:15.210Z In(05) vmx Progress 96% (none)
2025-05-02T06:29:15.285Z In(05) vmx Progress 97% (none)
2025-05-02T06:29:15.363Z In(05) vmx Progress 98% (none)
2025-05-02T06:29:15.393Z In(05) vmx MainMem: Read 8192MiB in 6996ms (1227.72 MB/s)
2025-05-02T06:29:15.393Z In(05) vmx   restoring MStats
2025-05-02T06:29:15.393Z In(05) vmx   restoring Snapshot
2025-05-02T06:29:15.396Z In(05) vmx   restoring pic
2025-05-02T06:29:15.396Z In(05) vmx   restoring ide0:0
2025-05-02T06:29:15.396Z In(05) vmx   restoring ide1:0
2025-05-02T06:29:15.396Z In(05) vmx   restoring FeatureCompat
2025-05-02T06:29:15.396Z In(05) vmx   restoring TimeTracker
2025-05-02T06:29:15.396Z In(05) vmx TimeTracker host to guest rate conversion 5057982200 @ 2592000000Hz -> 16261473889236 @ 2592000000Hz
2025-05-02T06:29:15.396Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + 16256415907036
2025-05-02T06:29:15.396Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-02T06:29:15.396Z In(05) vmx TSC offsetting enabled.
2025-05-02T06:29:15.396Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-02T06:29:15.396Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-02T06:29:15.396Z In(05) vmx   restoring Backdoor
2025-05-02T06:29:15.396Z In(05) vmx   restoring PCI
2025-05-02T06:29:15.396Z In(05) vmx   restoring ExtCfgDevice
2025-05-02T06:29:15.396Z In(05) vmx   restoring Cs440bx
2025-05-02T06:29:15.397Z In(05) vmx DUMPER: Item 'gpe.status' [2, -1] not found.
2025-05-02T06:29:15.397Z In(05) vmx DUMPER: Item 'gpe.enable' [2, -1] not found.
2025-05-02T06:29:15.397Z In(05) vmx   restoring AcpiNotify
2025-05-02T06:29:15.397Z In(05) vmx   restoring vcpuHotPlug
2025-05-02T06:29:15.397Z In(05) vmx   restoring MemoryHotplug
2025-05-02T06:29:15.397Z In(05) vmx   restoring devHP
2025-05-02T06:29:15.397Z In(05) vmx   restoring ACPIWake
2025-05-02T06:29:15.397Z In(05) vmx   restoring OEMDevice
2025-05-02T06:29:15.397Z In(05) vmx   restoring HotButton
2025-05-02T06:29:15.398Z In(05) vmx   restoring Timer
2025-05-02T06:29:15.398Z In(05) vmx   restoring ACPI
2025-05-02T06:29:15.398Z In(05) vmx   restoring XPMode
2025-05-02T06:29:15.398Z In(05) vmx   restoring DMA
2025-05-02T06:29:15.398Z In(05) vmx   restoring BackdoorAPM
2025-05-02T06:29:15.398Z In(05) vmx   restoring smram
2025-05-02T06:29:15.398Z In(05) vmx   restoring backdoorAbsMouse
2025-05-02T06:29:15.398Z In(05) vmx   restoring Keyboard
2025-05-02T06:29:15.398Z In(05) vmx   restoring SIO
2025-05-02T06:29:15.398Z In(05) vmx   restoring monitorLate
2025-05-02T06:29:15.398Z In(05) vmx   restoring vcpuNUMA
2025-05-02T06:29:15.398Z In(05) vmx   restoring devices
2025-05-02T06:29:15.398Z In(05) vmx   restoring configdbFT
2025-05-02T06:29:15.398Z In(05) vmx   restoring DevicesPowerOn
2025-05-02T06:29:15.398Z In(05) vmx   restoring PCIBridge0
2025-05-02T06:29:15.398Z In(05) vmx   restoring PCIBridge4
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:1
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:2
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:3
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:4
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:5
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:6
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge4:7
2025-05-02T06:29:15.398Z In(05) vmx   restoring PCIBridge5
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge5:1
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge5:2
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge5:3
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge5:4
2025-05-02T06:29:15.398Z In(05) vmx   restoring pciBridge5:5
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge5:6
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge5:7
2025-05-02T06:29:15.399Z In(05) vmx   restoring PCIBridge6
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:1
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:2
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:3
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:4
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:5
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:6
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge6:7
2025-05-02T06:29:15.399Z In(05) vmx   restoring PCIBridge7
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:1
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:2
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:3
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:4
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:5
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:6
2025-05-02T06:29:15.399Z In(05) vmx   restoring pciBridge7:7
2025-05-02T06:29:15.400Z In(05) vmx   restoring Migrate
2025-05-02T06:29:15.400Z In(05) vmx   restoring vide
2025-05-02T06:29:15.401Z In(05) vmx DUMPER: Block item 'monbuf' [0, -1] not found.
2025-05-02T06:29:15.402Z In(05) vmx DUMPER: Block item 'monbuf' [1, -1] not found.
2025-05-02T06:29:15.402Z In(05) vmx   restoring VGA
2025-05-02T06:29:15.402Z In(05) vmx   restoring SVGA
2025-05-02T06:29:15.403Z In(05) vmx SVGA: Guest reported SVGA driver: (0, 0, 0, 0)
2025-05-02T06:29:15.403Z In(05) vmx SVGA-GFB: Allocated gfbSize=134217728
2025-05-02T06:29:15.403Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "134217728"
2025-05-02T06:29:15.403Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-02T06:29:15.512Z In(05) vmx Progress 99% (none)
2025-05-02T06:29:15.562Z In(05) vmx SVGA3dCaps: guest, saved in checkpoint
2025-05-02T06:29:15.562Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 62]: 0x00000000 (MISSING62)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 70]: 0x00000000 (DEAD4)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 71]: 0x00000000 (DEAD5)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 72]: 0x00000000 (DEAD7)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 73]: 0x00000000 (DEAD6)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 76]: 0x00000000 (DEAD10)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 84]: 0x00000000 (DEAD1)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 85]: 0x00000000 (DEAD8)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 86]: 0x00000000 (DEAD9)
2025-05-02T06:29:15.562Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 88]: 0x00000000 (LINE_STIPPLE)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 92]: 0x00000000 (DEAD3)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 94]: 0x00000000 (DEAD2)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 96]: 0x00000000 (DEAD11)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[ 99]: 0x00000000 (DX_PROVOKING_VERTEX)
2025-05-02T06:29:15.563Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:29:15.563Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:29:15.563Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:29:15.563Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:29:15.563Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:29:15.563Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:29:15.563Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:29:15.563Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:29:15.563Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:29:15.563Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:29:15.563Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[120]: 0x00000000 (DXFMT_BUMPL6V5U5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[122]: 0x00000000 (DXFMT_FORMAT_DEAD1)
2025-05-02T06:29:15.563Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:29:15.563Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:29:15.563Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:29:15.563Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:29:15.563Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:29:15.563Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:29:15.563Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:29:15.563Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:29:15.563Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:29:15.563Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:29:15.563Z In(05) vmx   cap[144]: 0x00000000 (DXFMT_FORMAT_DEAD2)
2025-05-02T06:29:15.563Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:29:15.563Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:29:15.563Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:29:15.563Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:29:15.563Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:29:15.563Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:29:15.563Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:29:15.564Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:29:15.564Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:29:15.564Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:29:15.564Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:29:15.564Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:29:15.564Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:29:15.564Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:29:15.564Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:29:15.564Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[249]: 0x00000000 (LOGIC_BLENDOPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[250]: 0x00000000 (DEAD12)
2025-05-02T06:29:15.564Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:29:15.564Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:29:15.564Z In(05) vmx   cap[257]: 0x00000000 (DEAD13)
2025-05-02T06:29:15.564Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:29:15.564Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:29:15.564Z In(05) vmx SVGA3dCaps: guest, at resume
2025-05-02T06:29:15.564Z In(05) vmx   cap[  0]: 0x00000001 (3D)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  1]: 0x00000008 (MAX_LIGHTS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  2]: 0x00000008 (MAX_TEXTURES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  3]: 0x00000008 (MAX_CLIP_PLANES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  4]: 0x00000007 (VERTEX_SHADER_VERSION)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  5]: 0x00000001 (VERTEX_SHADER)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  6]: 0x0000000d (FRAGMENT_SHADER_VERSION)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  7]: 0x00000001 (FRAGMENT_SHADER)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  8]: 0x00000008 (MAX_RENDER_TARGETS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[  9]: 0x00000001 (S23E8_TEXTURES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 10]: 0x00000001 (S10E5_TEXTURES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 11]: 0x00000004 (MAX_FIXED_VERTEXBLEND)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 12]: 0x00000001 (D16_BUFFER_FORMAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 13]: 0x00000001 (D24S8_BUFFER_FORMAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 14]: 0x00000001 (D24X8_BUFFER_FORMAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 15]: 0x00000001 (QUERY_TYPES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 16]: 0x00000001 (TEXTURE_GRADIENT_SAMPLING)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 17]: 189.000000 (MAX_POINT_SIZE)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 18]: 0x00000014 (MAX_SHADER_TEXTURES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 19]: 0x00004000 (MAX_TEXTURE_WIDTH)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 20]: 0x00004000 (MAX_TEXTURE_HEIGHT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 21]: 0x00000800 (MAX_VOLUME_EXTENT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 22]: 0x00004000 (MAX_TEXTURE_REPEAT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 23]: 0x00004000 (MAX_TEXTURE_ASPECT_RATIO)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 24]: 0x00000010 (MAX_TEXTURE_ANISOTROPY)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 25]: 0x001fffff (MAX_PRIMITIVE_COUNT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 26]: 0x000fffff (MAX_VERTEX_INDEX)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 27]: 0x0000ffff (MAX_VERTEX_SHADER_INSTRUCTIONS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 28]: 0x0000ffff (MAX_FRAGMENT_SHADER_INSTRUCTIONS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 29]: 0x00000020 (MAX_VERTEX_SHADER_TEMPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 30]: 0x00000020 (MAX_FRAGMENT_SHADER_TEMPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 31]: 0x03ffffff (TEXTURE_OPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 32]: 0x0018ec1f (SURFACEFMT_X8R8G8B8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 33]: 0x0018e11f (SURFACEFMT_A8R8G8B8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 34]: 0x0008601f (SURFACEFMT_A2R10G10B10)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 35]: 0x0008601f (SURFACEFMT_X1R5G5B5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 36]: 0x0008611f (SURFACEFMT_A1R5G5B5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 37]: 0x0000611f (SURFACEFMT_A4R4G4B4)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 38]: 0x0018ec1f (SURFACEFMT_R5G6B5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 39]: 0x0000601f (SURFACEFMT_LUMINANCE16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 40]: 0x00006007 (SURFACEFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 41]: 0x0000601f (SURFACEFMT_ALPHA8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 42]: 0x0000601f (SURFACEFMT_LUMINANCE8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 43]: 0x000040c5 (SURFACEFMT_Z_D16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 44]: 0x000040c5 (SURFACEFMT_Z_D24S8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 45]: 0x000040c5 (SURFACEFMT_Z_D24X8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 46]: 0x0000e005 (SURFACEFMT_DXT1)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 47]: 0x0000e005 (SURFACEFMT_DXT2)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 48]: 0x0000e005 (SURFACEFMT_DXT3)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 49]: 0x0000e005 (SURFACEFMT_DXT4)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 50]: 0x0000e005 (SURFACEFMT_DXT5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 51]: 0x00014005 (SURFACEFMT_BUMPX8L8V8U8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 52]: 0x00014007 (SURFACEFMT_A2W10V10U10)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 53]: 0x00014007 (SURFACEFMT_BUMPU8V8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 54]: 0x00014005 (SURFACEFMT_Q8W8V8U8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 55]: 0x00014001 (SURFACEFMT_CxV8U8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 56]: 0x0080601f (SURFACEFMT_R_S10E5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 57]: 0x0080601f (SURFACEFMT_R_S23E8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 58]: 0x0080601f (SURFACEFMT_RG_S10E5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 59]: 0x0080601f (SURFACEFMT_RG_S23E8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 60]: 0x0080601f (SURFACEFMT_ARGB_S10E5)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 61]: 0x0080601f (SURFACEFMT_ARGB_S23E8)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 63]: 0x00000004 (MAX_VERTEX_SHADER_TEXTURES)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 64]: 0x00000008 (MAX_SIMULTANEOUS_RENDER_TARGETS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 65]: 0x00014007 (SURFACEFMT_V16U16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 66]: 0x0000601f (SURFACEFMT_G16R16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 67]: 0x0000601f (SURFACEFMT_A16B16G16R16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 68]: 0x01246000 (SURFACEFMT_UYVY)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 69]: 0x01246000 (SURFACEFMT_YUY2)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 74]: 0x00000001 (AUTOGENMIPMAPS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 75]: 0x01246000 (SURFACEFMT_NV12)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 77]: 0x00000100 (MAX_CONTEXT_IDS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 78]: 0x00008000 (MAX_SURFACE_IDS)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 79]: 0x000040c5 (SURFACEFMT_Z_DF16)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 80]: 0x000040c5 (SURFACEFMT_Z_DF24)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 81]: 0x000040c5 (SURFACEFMT_Z_D24S8_INT)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 82]: 0x00006005 (SURFACEFMT_ATI1)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 83]: 0x00006005 (SURFACEFMT_ATI2)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 87]: 0x00000001 (LINE_AA)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 89]:  10.000000 (MAX_LINE_WIDTH)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 90]:  10.000000 (MAX_AA_LINE_WIDTH)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 91]: 0x01246000 (SURFACEFMT_YV12)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-02T06:29:15.564Z In(05) vmx   cap[ 95]: 0x00000001 (DXCONTEXT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[ 97]: 0x00000010 (DX_MAX_VERTEXBUFFERS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[ 98]: 0x0000000f (DX_MAX_CONSTANT_BUFFERS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[100]: 0x000002f7 (DXFMT_X8R8G8B8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[101]: 0x000003f7 (DXFMT_A8R8G8B8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[102]: 0x000002f7 (DXFMT_R5G6B5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[103]: 0x000000f7 (DXFMT_X1R5G5B5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[104]: 0x000000f7 (DXFMT_A1R5G5B5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[105]: 0x000000f7 (DXFMT_A4R4G4B4)
2025-05-02T06:29:15.565Z In(05) vmx   cap[106]: 0x00000009 (DXFMT_Z_D32)
2025-05-02T06:29:15.565Z In(05) vmx   cap[107]: 0x0000026b (DXFMT_Z_D16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[108]: 0x0000026b (DXFMT_Z_D24S8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[109]: 0x0000000b (DXFMT_Z_D15S1)
2025-05-02T06:29:15.565Z In(05) vmx   cap[110]: 0x000000f7 (DXFMT_LUMINANCE8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[111]: 0x000000e3 (DXFMT_LUMINANCE4_ALPHA4)
2025-05-02T06:29:15.565Z In(05) vmx   cap[112]: 0x000000f7 (DXFMT_LUMINANCE16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[113]: 0x000000e3 (DXFMT_LUMINANCE8_ALPHA8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[114]: 0x00000063 (DXFMT_DXT1)
2025-05-02T06:29:15.565Z In(05) vmx   cap[115]: 0x00000063 (DXFMT_DXT2)
2025-05-02T06:29:15.565Z In(05) vmx   cap[116]: 0x00000063 (DXFMT_DXT3)
2025-05-02T06:29:15.565Z In(05) vmx   cap[117]: 0x00000063 (DXFMT_DXT4)
2025-05-02T06:29:15.565Z In(05) vmx   cap[118]: 0x00000063 (DXFMT_DXT5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[119]: 0x000000e3 (DXFMT_BUMPU8V8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[121]: 0x00000063 (DXFMT_BUMPX8L8V8U8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[123]: 0x000003f7 (DXFMT_ARGB_S10E5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[124]: 0x000003f7 (DXFMT_ARGB_S23E8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[125]: 0x000003f7 (DXFMT_A2R10G10B10)
2025-05-02T06:29:15.565Z In(05) vmx   cap[126]: 0x000000e3 (DXFMT_V8U8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[127]: 0x00000063 (DXFMT_Q8W8V8U8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[128]: 0x00000063 (DXFMT_CxV8U8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[129]: 0x000000e3 (DXFMT_X8L8V8U8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[130]: 0x000000e3 (DXFMT_A2W10V10U10)
2025-05-02T06:29:15.565Z In(05) vmx   cap[131]: 0x000000f7 (DXFMT_ALPHA8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[132]: 0x000003f7 (DXFMT_R_S10E5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[133]: 0x000003f7 (DXFMT_R_S23E8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[134]: 0x000003f7 (DXFMT_RG_S10E5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[135]: 0x000003f7 (DXFMT_RG_S23E8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[136]: 0x00000001 (DXFMT_BUFFER)
2025-05-02T06:29:15.565Z In(05) vmx   cap[137]: 0x0000026b (DXFMT_Z_D24X8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[138]: 0x000001e3 (DXFMT_V16U16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[139]: 0x000003f7 (DXFMT_G16R16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[140]: 0x000001f7 (DXFMT_A16B16G16R16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[141]: 0x00000001 (DXFMT_UYVY)
2025-05-02T06:29:15.565Z In(05) vmx   cap[142]: 0x00000041 (DXFMT_YUY2)
2025-05-02T06:29:15.565Z In(05) vmx   cap[143]: 0x00000041 (DXFMT_NV12)
2025-05-02T06:29:15.565Z In(05) vmx   cap[145]: 0x000002e1 (DXFMT_R32G32B32A32_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[146]: 0x000003e7 (DXFMT_R32G32B32A32_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[147]: 0x000003e7 (DXFMT_R32G32B32A32_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[148]: 0x000000e1 (DXFMT_R32G32B32_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[149]: 0x000001e3 (DXFMT_R32G32B32_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[150]: 0x000001e3 (DXFMT_R32G32B32_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[151]: 0x000001e3 (DXFMT_R32G32B32_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[152]: 0x000002e1 (DXFMT_R16G16B16A16_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[153]: 0x000003e7 (DXFMT_R16G16B16A16_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[154]: 0x000003f7 (DXFMT_R16G16B16A16_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[155]: 0x000003e7 (DXFMT_R16G16B16A16_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[156]: 0x000002e1 (DXFMT_R32G32_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[157]: 0x000003e7 (DXFMT_R32G32_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[158]: 0x000003e7 (DXFMT_R32G32_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[159]: 0x00000261 (DXFMT_R32G8X24_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[160]: 0x00000269 (DXFMT_D32_FLOAT_S8X24_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[161]: 0x00000063 (DXFMT_R32_FLOAT_X8X24)
2025-05-02T06:29:15.565Z In(05) vmx   cap[162]: 0x00000063 (DXFMT_X32_G8X24_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[163]: 0x000002e1 (DXFMT_R10G10B10A2_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[164]: 0x000003e7 (DXFMT_R10G10B10A2_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[165]: 0x000003f7 (DXFMT_R11G11B10_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[166]: 0x000002e1 (DXFMT_R8G8B8A8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[167]: 0x000003f7 (DXFMT_R8G8B8A8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[168]: 0x000002f7 (DXFMT_R8G8B8A8_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[169]: 0x000003e7 (DXFMT_R8G8B8A8_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[170]: 0x000003e7 (DXFMT_R8G8B8A8_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[171]: 0x000002e1 (DXFMT_R16G16_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[172]: 0x000003e7 (DXFMT_R16G16_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[173]: 0x000003e7 (DXFMT_R16G16_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[174]: 0x000002e1 (DXFMT_R32_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[175]: 0x00000269 (DXFMT_D32_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[176]: 0x000003e7 (DXFMT_R32_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[177]: 0x000003e7 (DXFMT_R32_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[178]: 0x00000261 (DXFMT_R24G8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[179]: 0x00000269 (DXFMT_D24_UNORM_S8_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[180]: 0x00000063 (DXFMT_R24_UNORM_X8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[181]: 0x00000063 (DXFMT_X24_G8_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[182]: 0x000002e1 (DXFMT_R8G8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[183]: 0x000003f7 (DXFMT_R8G8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[184]: 0x000003e7 (DXFMT_R8G8_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[185]: 0x000003e7 (DXFMT_R8G8_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[186]: 0x000002e1 (DXFMT_R16_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[187]: 0x000003f7 (DXFMT_R16_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[188]: 0x000003e7 (DXFMT_R16_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[189]: 0x000003f7 (DXFMT_R16_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[190]: 0x000003e7 (DXFMT_R16_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[191]: 0x000002e1 (DXFMT_R8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[192]: 0x000003f7 (DXFMT_R8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[193]: 0x000003e7 (DXFMT_R8_UINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[194]: 0x000003f7 (DXFMT_R8_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[195]: 0x000003e7 (DXFMT_R8_SINT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[196]: 0x00000001 (DXFMT_P8)
2025-05-02T06:29:15.565Z In(05) vmx   cap[197]: 0x000000e3 (DXFMT_R9G9B9E5_SHAREDEXP)
2025-05-02T06:29:15.565Z In(05) vmx   cap[198]: 0x000000e3 (DXFMT_R8G8_B8G8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[199]: 0x000000e3 (DXFMT_G8R8_G8B8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[200]: 0x000000e1 (DXFMT_BC1_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[201]: 0x000000e3 (DXFMT_BC1_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[202]: 0x000000e1 (DXFMT_BC2_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[203]: 0x000000e3 (DXFMT_BC2_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[204]: 0x000000e1 (DXFMT_BC3_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[205]: 0x000000e3 (DXFMT_BC3_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[206]: 0x000000e1 (DXFMT_BC4_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[207]: 0x00000063 (DXFMT_ATI1)
2025-05-02T06:29:15.565Z In(05) vmx   cap[208]: 0x000000e3 (DXFMT_BC4_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[209]: 0x000000e1 (DXFMT_BC5_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[210]: 0x00000063 (DXFMT_ATI2)
2025-05-02T06:29:15.565Z In(05) vmx   cap[211]: 0x000000e3 (DXFMT_BC5_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[212]: 0x00000045 (DXFMT_R10G10B10_XR_BIAS_A2_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[213]: 0x000002e1 (DXFMT_B8G8R8A8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[214]: 0x000002f7 (DXFMT_B8G8R8A8_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[215]: 0x000002e1 (DXFMT_B8G8R8X8_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[216]: 0x000002f7 (DXFMT_B8G8R8X8_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[217]: 0x0000006b (DXFMT_Z_DF16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[218]: 0x0000006b (DXFMT_Z_DF24)
2025-05-02T06:29:15.565Z In(05) vmx   cap[219]: 0x0000006b (DXFMT_Z_D24S8_INT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[220]: 0x00000001 (DXFMT_YV12)
2025-05-02T06:29:15.565Z In(05) vmx   cap[221]: 0x000003f7 (DXFMT_R32G32B32A32_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[222]: 0x000003f7 (DXFMT_R16G16B16A16_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[223]: 0x000003f7 (DXFMT_R16G16B16A16_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[224]: 0x000003f7 (DXFMT_R32G32_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[225]: 0x000003f7 (DXFMT_R10G10B10A2_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[226]: 0x000003f7 (DXFMT_R8G8B8A8_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[227]: 0x000003f7 (DXFMT_R16G16_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[228]: 0x000003f7 (DXFMT_R16G16_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[229]: 0x000003f7 (DXFMT_R16G16_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[230]: 0x000003f7 (DXFMT_R32_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[231]: 0x000003f7 (DXFMT_R8G8_SNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[232]: 0x000003f7 (DXFMT_R16_FLOAT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[233]: 0x00000269 (DXFMT_D16_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[234]: 0x000002f7 (DXFMT_A8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[235]: 0x000000e3 (DXFMT_BC1_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[236]: 0x000000e3 (DXFMT_BC2_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[237]: 0x000000e3 (DXFMT_BC3_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[238]: 0x000002f7 (DXFMT_B5G6R5_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[239]: 0x000002f7 (DXFMT_B5G5R5A1_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[240]: 0x000003f7 (DXFMT_B8G8R8A8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[241]: 0x000003f7 (DXFMT_B8G8R8X8_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[242]: 0x000000e3 (DXFMT_BC4_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[243]: 0x000000e3 (DXFMT_BC5_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[244]: 0x00000001 (SM41)
2025-05-02T06:29:15.565Z In(05) vmx   cap[245]: 0x00000001 (MULTISAMPLE_2X)
2025-05-02T06:29:15.565Z In(05) vmx   cap[246]: 0x00000001 (MULTISAMPLE_4X)
2025-05-02T06:29:15.565Z In(05) vmx   cap[247]: 0x00000001 (MS_FULL_QUALITY)
2025-05-02T06:29:15.565Z In(05) vmx   cap[248]: 0x00000001 (LOGICOPS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[251]: 0x000000e1 (DXFMT_BC6H_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[252]: 0x000000e3 (DXFMT_BC6H_UF16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[253]: 0x000000e3 (DXFMT_BC6H_SF16)
2025-05-02T06:29:15.565Z In(05) vmx   cap[254]: 0x000000e1 (DXFMT_BC7_TYPELESS)
2025-05-02T06:29:15.565Z In(05) vmx   cap[255]: 0x000000e3 (DXFMT_BC7_UNORM)
2025-05-02T06:29:15.565Z In(05) vmx   cap[256]: 0x000000e3 (DXFMT_BC7_UNORM_SRGB)
2025-05-02T06:29:15.565Z In(05) vmx   cap[258]: 0x00000001 (SM5)
2025-05-02T06:29:15.565Z In(05) vmx   cap[259]: 0x00000001 (MULTISAMPLE_8X)
2025-05-02T06:29:15.565Z In(05) vmx   cap[260]: 0x00000008 (MAX_FORCED_SAMPLE_COUNT)
2025-05-02T06:29:15.565Z In(05) vmx   cap[261]: 0x00000001 (GL43)
2025-05-02T06:29:15.565Z In(05) vmx SVGAFeature guest, at resume id: 0
2025-05-02T06:29:15.565Z In(05) vmx SVGAFeature guest, at resume svga.supports3D bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.baseCapsLevel num 9
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.maxPointSize num 189
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.maxTextureSize num 16384
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.maxVolumeExtent num 2048
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.maxTextureAnisotropy num 16
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.lineStipple bool 0
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.dxMaxConstantBuffers num 15
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.dxProvokingVertex bool 0
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.sm41 bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.multisample2x bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.multisample4x bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.msFullQuality bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.logicOps bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.bc67 num 9
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.sm5 bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.multisample8x bool 1
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.logicBlendOps bool 0
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.maxForcedSampleCount num 8
2025-05-02T06:29:15.566Z In(05) vmx SVGAFeature guest, at resume svga.gl43 bool 1
2025-05-02T06:29:15.567Z In(05) vmx DUMPER: Item 'screens.arraySize' [-1, -1] not found.
2025-05-02T06:29:15.567Z In(05) vmx SVGA-ScreenMgr: Screen type changed to RegisterMode
2025-05-02T06:29:15.567Z In(05) vmx SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 720) flags=0x2
2025-05-02T06:29:15.568Z In(05) vmx DUMPER: Item 'CursorType' [-1, -1] not found.
2025-05-02T06:29:15.568Z In(05) vmx   restoring Ethernet0
2025-05-02T06:29:15.570Z In(05) vmx   restoring vmci0
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Item 'VMCINumSubs' [-1, -1] not found.
2025-05-02T06:29:15.570Z In(05) vmx   restoring vsock
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Item 'remote.port' [0, -1] not found.
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Item 'qpair.context' [0, -1] not found.
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Item 'timeoutRemaining' [0, -1] not found.
2025-05-02T06:29:15.570Z In(05) vmx   restoring GuestMsg
2025-05-02T06:29:15.570Z In(05) vmx   restoring GuestRpc
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Item 'AsyncVmciSocket.numSendBuf' [-1, -1] not found.
2025-05-02T06:29:15.570Z In(05) vmx   restoring Tools
2025-05-02T06:29:15.570Z In(05) vmx   restoring Tools Install
2025-05-02T06:29:15.570Z In(05) vmx TOOLS INSTALL setting state to 0 on restore.
2025-05-02T06:29:15.570Z In(05) vmx   restoring GuestAppMonitor
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Requested 10 bytes, found 5 bytes.
2025-05-02T06:29:15.570Z In(05) vmx DUMPER: Requested 20 bytes, found 5 bytes.
2025-05-02T06:29:15.570Z In(05) vmx   restoring Hgfs
2025-05-02T06:29:15.571Z In(05) vmx   restoring MKSVMX
2025-05-02T06:29:15.571Z In(05) vmx   restoring ToolsDeployPkg
2025-05-02T06:29:15.571Z In(05) vmx DEPLOYPKG: ToolsDeployPkgCptRestore: state=0 err=0 (null msg)
2025-05-02T06:29:15.571Z In(05) vmx DEPLOYPKG: ToolsDeployPkgRestoreSuccessTasks: state=0 err=0, msg=null
2025-05-02T06:29:15.571Z In(05) vmx   restoring CMOS
2025-05-02T06:29:15.571Z In(05) vmx   restoring FlashRam
2025-05-02T06:29:15.571Z In(05) vmx Progress 101% (none)
2025-05-02T06:29:15.572Z In(05) vmx DUMPER: Updating header magic on restore.
2025-05-02T06:29:15.572Z In(05) vmx CPT: Deleting checkpoint state, '/Users/<USER>/Virtual Machines.localized/cloudera-quickstart-vm-5.13.0-0-virtualbox.vmwarevm/cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss'.
2025-05-02T06:29:15.867Z In(05) vmx SNAPSHOT: SnapshotConfigInfoExpandVM: Unable to find 'cloudera-quickstart-vm-5.13.0-0-virtualbox-3ddfda5e.vmss'.  Setting vmState to NULL.
2025-05-02T06:29:15.867Z No(00) vmx ConfigDB: Setting checkpoint.vmState = ""
2025-05-02T06:29:15.874Z No(00) vmx PowerOnTiming: Module CheckpointLate took 7480285 us
2025-05-02T06:29:15.875Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "45"
2025-05-02T06:29:15.875Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x9e stepping: 0xa
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest codename: Coffee Lake-S/H
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest name: Intel(R) Core(TM) i7-8850H CPU @ 2.60GHz
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000016 0x756e6547 0x6c65746e 0x49656e69
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000906ea 0x00010800 0xf7fa3223 0x0f8bfbff
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000906ea 0x02100800 0x7ffafbff 0xbfebfbff
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x76036301 0x00f0b5ff 0x00000000 0x00c30000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x00000121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x00000122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x00000143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x00c0003f 0x000003ff 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x00000163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x02c0003f 0x00002fff 0x00000006
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x000026f7 0x00000002 0x00000009 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x009c27ab 0x00000000 0xbc000400
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000000 0x029c6fbf 0x40000000 0xbc002e00
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x07300401 0x000000ff 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x07300404 0x00000000 0x00000000 0x00000603
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000002
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000000 0x00000001 0x00000201 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x0000000c 0x00000201 0x00000002
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x0000001f 0x00000340 0x00000440 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000f 0x00000340 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000340 0x00000100 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x000000d8 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x00000a28 0x000010cc 0x00000064 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x00278d00 0x000101d0 0x00000002 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x65746e49 0x2952286c 0x726f4320 0x4d542865
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x37692029 0x3538382d 0x43204830 0x40205550
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x362e3220 0x7a484730 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x01006040 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302d 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-02T06:29:15.875Z In(05) vmx Minimum ucode level: 0x000000fa
2025-05-02T06:29:15.875Z In(05) vmx VPMC: events will use hybrid freeze.
2025-05-02T06:29:15.875Z In(05) vmx VPMC: gen counters: num 4 mask 0xffffffffffff
2025-05-02T06:29:15.875Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2025-05-02T06:29:15.875Z In(05) vmx VPMC: hardware counters: 0
2025-05-02T06:29:15.875Z In(05) vmx VPMC: perf capabilities: 0x2000
2025-05-02T06:29:15.876Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0xc
2025-05-02T06:29:15.876Z In(05) vmx SVGA: Registering IOSpace at 0x1070
2025-05-02T06:29:15.876Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2025-05-02T06:29:15.876Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=134217728
2025-05-02T06:29:15.876Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=134217728
2025-05-02T06:29:15.876Z In(05) vmx SVGA: Final Device caps : 0xfdffc3e2
2025-05-02T06:29:15.876Z In(05) vmx SVGA: Final Device caps2: 0x0005efff
2025-05-02T06:29:15.877Z In(05) vmx FeatureCompat: Capabilities:
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.sse3 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.vmx = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.fma = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.pcid = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.sse41 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.sse42 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.movbe = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.popcnt = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.aes = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xsave = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.avx = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.f16c = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.rdrand = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ss = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.avx2 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.smep = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.invpcid = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.rdseed = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.adx = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.smap = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.mdclear = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.stibp = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.fcmd = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ssbd = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xsavec = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xsaves = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.abm = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.nx = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.lm = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.intel = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ibrs = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ibpb = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.mwait = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.ds = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: hv.capable = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.realmode = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.ple = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.mbx = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.advexitinfo = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: vt.eptad = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.supports3d = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.basecapslevel = 9
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.maxpointsize = 0xbd
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.maxtexturesize = 0x4000
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.maxvolumeextent = 0x800
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.maxtextureanisotropy = 0x10
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.dxmaxconstantbuffers = 0xf
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.sm41 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.multisample2x = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.multisample4x = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.msfullquality = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.logicops = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.bc67 = 9
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.sm5 = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.multisample8x = 1
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.maxforcedsamplecount = 8
2025-05-02T06:29:15.877Z In(05) vmx Capability Found: svga0*svga.gl43 = 1
2025-05-02T06:29:15.877Z In(05) vmx FeatureCompat: Requirements:
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.vmx - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xgetbv_ecx1 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2025-05-02T06:29:15.877Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: hv.capable - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: vt.realmode - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: vt.ple - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: vt.zeroinstlen - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.supports3d - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.basecapslevel - Num:Min:9
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.maxpointsize - Num:Min:0xbd
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.maxtexturesize - Num:Min:0x4000
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.maxvolumeextent - Num:Min:0x800
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.maxtextureanisotropy - Num:Min:0x10
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.dxmaxconstantbuffers - Num:Min:0xf
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.sm41 - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.multisample2x - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.multisample4x - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.msfullquality - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.logicops - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.bc67 - Num:Min:9
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.sm5 - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.multisample8x - Bool:Min:1
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.maxforcedsamplecount - Num:Min:8
2025-05-02T06:29:15.878Z In(05) vmx VM Features Required: svga*svga.gl43 - Bool:Min:1
2025-05-02T06:29:15.937Z In(05) ulm_exc VTHREAD 123145321570304 "ulm_exc" tid 188451
2025-05-02T06:29:15.938Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2025-05-02T06:29:15.939Z In(05) vmx Guest VT-x Capabilities:
2025-05-02T06:29:15.939Z In(05) vmx Basic VMX Information (0x00d8100000000001)
2025-05-02T06:29:15.939Z In(05) vmx   VMCS revision ID                           1
2025-05-02T06:29:15.939Z In(05) vmx   VMCS region length                      4096
2025-05-02T06:29:15.939Z In(05) vmx   VMX physical-address width           natural
2025-05-02T06:29:15.939Z In(05) vmx   SMM dual-monitor mode                     no
2025-05-02T06:29:15.939Z In(05) vmx   VMCS memory type                          WB
2025-05-02T06:29:15.939Z In(05) vmx   Advanced INS/OUTS info                   yes
2025-05-02T06:29:15.939Z In(05) vmx   True VMX MSRs                            yes
2025-05-02T06:29:15.939Z In(05) vmx   Exception Injection ignores error code    no
2025-05-02T06:29:15.939Z In(05) vmx True Pin-Based VM-Execution Controls (0x0000003f00000016)
2025-05-02T06:29:15.939Z In(05) vmx   External-interrupt exiting               {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   NMI exiting                              {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Virtual NMIs                             {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Activate VMX-preemption timer            { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Process posted interrupts                { 0 }
2025-05-02T06:29:15.939Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xf7f9fffe04006172)
2025-05-02T06:29:15.939Z In(05) vmx   Interrupt-window exiting                 {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Use TSC offsetting                       {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   HLT exiting                              {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   INVLPG exiting                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   MWAIT exiting                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   RDPMC exiting                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   RDTSC exiting                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   CR3-load exiting                         {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   CR3-store exiting                        {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Activate tertiary controls               { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   CR8-load exiting                         {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   CR8-store exiting                        {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Use TPR shadow                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   NMI-window exiting                       {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   MOV-DR exiting                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Unconditional I/O exiting                {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Use I/O bitmaps                          {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Monitor trap flag                        { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Use MSR bitmaps                          {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   MONITOR exiting                          {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   PAUSE exiting                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Activate secondary controls              {0,1}
2025-05-02T06:29:15.939Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x00111cfe00000000)
2025-05-02T06:29:15.939Z In(05) vmx   Virtualize APIC accesses                 { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Enable EPT                               {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Descriptor-table exiting                 {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Enable RDTSCP                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Enable VPID                              {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   WBINVD exiting                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Unrestricted guest                       {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   APIC-register virtualization             { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Virtual-interrupt delivery               { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   RDRAND exiting                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Enable INVPCID                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Enable VM Functions                      { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Use VMCS shadowing                       { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   ENCLS exiting                            { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   RDSEED exiting                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Enable PML                               { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   EPT-violation #VE                        { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Conceal VMX from PT                      { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   PASID translation                        { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Mode-based execute control for EPT       { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   PT uses guest physical addresses         { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Use TSC scaling                          { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Enable UMWAIT and TPAUSE                 { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Bus lock exiting                         { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Notification VM exits                    { 0 }
2025-05-02T06:29:15.939Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000000)
2025-05-02T06:29:15.939Z In(05) vmx   LOADIWKEY exiting                          no
2025-05-02T06:29:15.939Z In(05) vmx   Enable HLAT                                no
2025-05-02T06:29:15.939Z In(05) vmx   Enable Paging-Write                        no
2025-05-02T06:29:15.939Z In(05) vmx   Enable Guest Paging Verification           no
2025-05-02T06:29:15.939Z In(05) vmx   Enable IPI Virtualization                  no
2025-05-02T06:29:15.939Z In(05) vmx   Enable Virtual MSR_SPEC_CTRL               no
2025-05-02T06:29:15.939Z In(05) vmx True VM-Exit Controls (0x003fefff00036dfb)
2025-05-02T06:29:15.939Z In(05) vmx   Save debug controls                      {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Host address-space size                  {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Save IA32_PAT                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Save IA32_EFER                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Save VMX-preemption timer                { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Conceal VMX from processor trace         { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Clear IA32_RTIT MSR                      { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Clear IA32_LBR_CTL MSR                   { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:29:15.939Z In(05) vmx True VM-Entry Controls (0x0000d3ff000011fb)
2025-05-02T06:29:15.939Z In(05) vmx   Load debug controls                      {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   IA-32e mode guest                        {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Entry to SMM                             { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Deactivate dual-monitor mode             { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_PAT                            {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_EFER                           {0,1}
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Conceal VMX from processor trace         { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_RTIT MSR                       { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load CET state                           { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load IA32_LBR_CTL MSR                    { 0 }
2025-05-02T06:29:15.939Z In(05) vmx   Load PKRS                                { 0 }
2025-05-02T06:29:15.939Z In(05) vmx VPID and EPT Capabilities (0x00000f0106114041)
2025-05-02T06:29:15.939Z In(05) vmx   R=0/W=0/X=1                               yes
2025-05-02T06:29:15.939Z In(05) vmx   Page-walk length 3                        yes
2025-05-02T06:29:15.939Z In(05) vmx   EPT memory type WB                        yes
2025-05-02T06:29:15.939Z In(05) vmx   2MB super-page                            yes
2025-05-02T06:29:15.939Z In(05) vmx   1GB super-page                             no
2025-05-02T06:29:15.939Z In(05) vmx   INVEPT support                            yes
2025-05-02T06:29:15.939Z In(05) vmx   Access & Dirty Bits                        no
2025-05-02T06:29:15.939Z In(05) vmx   Advanced VM exit information for EPT violations    no
2025-05-02T06:29:15.939Z In(05) vmx   Supervisor shadow-stack control            no
2025-05-02T06:29:15.939Z In(05) vmx   Type 1 INVEPT                             yes
2025-05-02T06:29:15.939Z In(05) vmx   Type 2 INVEPT                             yes
2025-05-02T06:29:15.939Z In(05) vmx   INVVPID support                           yes
2025-05-02T06:29:15.939Z In(05) vmx   Type 0 INVVPID                            yes
2025-05-02T06:29:15.939Z In(05) vmx   Type 1 INVVPID                            yes
2025-05-02T06:29:15.939Z In(05) vmx   Type 2 INVVPID                            yes
2025-05-02T06:29:15.939Z In(05) vmx   Type 3 INVVPID                            yes
2025-05-02T06:29:15.939Z In(05) vmx Miscellaneous VMX Data (0x00000000400401e0)
2025-05-02T06:29:15.939Z In(05) vmx   TSC to preemption timer ratio      0
2025-05-02T06:29:15.939Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2025-05-02T06:29:15.939Z In(05) vmx   Activity State HLT               yes
2025-05-02T06:29:15.939Z In(05) vmx   Activity State shutdown          yes
2025-05-02T06:29:15.939Z In(05) vmx   Activity State wait-for-SIPI     yes
2025-05-02T06:29:15.939Z In(05) vmx   Processor trace in VMX            no
2025-05-02T06:29:15.939Z In(05) vmx   RDMSR SMBASE MSR in SMM           no
2025-05-02T06:29:15.939Z In(05) vmx   CR3 targets supported              4
2025-05-02T06:29:15.939Z In(05) vmx   Maximum MSR list size            512
2025-05-02T06:29:15.939Z In(05) vmx   VMXOFF holdoff of SMIs            no
2025-05-02T06:29:15.939Z In(05) vmx   Allow all VMWRITEs                no
2025-05-02T06:29:15.939Z In(05) vmx   Allow zero instruction length    yes
2025-05-02T06:29:15.939Z In(05) vmx   MSEG revision ID                   0
2025-05-02T06:29:15.939Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2025-05-02T06:29:15.939Z In(05) vmx   Fixed to 0        0xffffffff00000000
2025-05-02T06:29:15.939Z In(05) vmx   Fixed to 1        0x0000000080000021
2025-05-02T06:29:15.939Z In(05) vmx   Variable          0x000000007fffffde
2025-05-02T06:29:15.939Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x00000000003727ff)
2025-05-02T06:29:15.939Z In(05) vmx   Fixed to 0        0xffffffffffc8d800
2025-05-02T06:29:15.939Z In(05) vmx   Fixed to 1        0x0000000000002000
2025-05-02T06:29:15.939Z In(05) vmx   Variable          0x00000000003707ff
2025-05-02T06:29:15.939Z In(05) vmx VMCS Enumeration (0x000000000000005a)
2025-05-02T06:29:15.939Z In(05) vmx   Highest index                   0x2d
2025-05-02T06:29:15.939Z In(05) vmx VM Functions (0x0000000000000000)
2025-05-02T06:29:15.939Z In(05) vmx 
2025-05-02T06:29:15.939Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2025-05-02T06:29:15.939Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-05-02T06:29:15.939Z In(05)+ vmx 
2025-05-02T06:29:15.939Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      0      0      -
2025-05-02T06:29:15.939Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   36864  36864      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Total paged                         :  4141938 4141938      - |   3517   3517      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Actual maximum                      :         4141938        |        4141938
2025-05-02T06:29:15.940Z In(05)+ vmx 
2025-05-02T06:29:15.940Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      70     70      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    2193   3296      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Total nonpaged                      :   35485  37036      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Actual maximum                      :          35485        |         37036
2025-05-02T06:29:15.940Z In(05)+ vmx 
2025-05-02T06:29:15.940Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2145   2202      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VNPTShadow                  :    6427   6427      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VNPTShadowCache             :      53     53      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VNPTBackmap                 :    4413   4413      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     887    887      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4436   4436      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Total anonymous                     :   22499  22556      - |      0      0      -
2025-05-02T06:29:15.940Z In(05) vmx OvhdMem Actual maximum                      :          22499        |         22556
2025-05-02T06:29:15.940Z In(05)+ vmx 
2025-05-02T06:29:15.940Z In(05) vmx VMMEM: Precise Reservation: 16405MB (MainMem=8192MB)
2025-05-02T06:29:15.940Z In(05) vmx VMXSTATS: Registering 48 stats: vmx.overheadMemSize
2025-05-02T06:29:15.940Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2025-05-02T06:29:15.941Z No(00) vmx PowerOnTiming: ALL took 10413440 us
2025-05-02T06:29:15.941Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2025-05-02T06:29:15.941Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2025-05-02T06:29:15.941Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2025-05-02T06:29:15.942Z In(05) vcpu-0 VTHREAD 123145322106880 "vcpu-0" tid 188452
2025-05-02T06:29:15.944Z In(05) vcpu-1 VTHREAD 123145337982976 "vcpu-1" tid 188453
2025-05-02T06:29:15.945Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2025-05-02T06:29:15.945Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 8191 MB (100 %) Size:8191 MB (100 %)
2025-05-02T06:29:15.945Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x5, chipset.onlineStandby 0
2025-05-02T06:29:15.958Z In(05) vcpu-0 VNET: MacosVmnetVirtApiGetInterfaceForBridging: Using iface: en0 for bridging
2025-05-02T06:29:15.958Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartBridgedInterface: Ethernet0: Starting virtual interface in bridge mode, preferred=(null), selected=en0
2025-05-02T06:29:15.980Z In(05) host-188436 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: starting interface, status=1000
2025-05-02T06:29:15.980Z In(05) host-188436 VNET: MacosVmnetVirtApiStartHandler: Ethernet0: interface params:
2025-05-02T06:29:15.980Z In(05) host-188436 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      MTU             : 1500
2025-05-02T06:29:15.980Z In(05) host-188436 VNET: MacosVmnetVirtApiStartHandler: Ethernet0:      Max packet size : 1514
2025-05-02T06:29:15.980Z In(05) vcpu-0 VNET: MacosVmnetVirtApiStartInterface: Ethernet0: Virtual interface started successfully
2025-05-02T06:29:15.980Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2025-05-02T06:29:15.982Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.accelerometer"
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.ambientLight"
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.compass"
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.gyrometer"
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.inclinometer"
2025-05-02T06:29:15.983Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.orientation"
2025-05-02T06:29:15.985Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-02T06:29:15.988Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-02T06:29:15.991Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-02T06:29:15.991Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 49 stats: vmx.vigor.opsTotal
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 50 stats: vmx.vigor.opsPerS
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 51 stats: vmx.vigor.queriesPerS
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 52 stats: vmx.poll.itersPerS
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 53 stats: vmx.userRpc.opsPerS
2025-05-02T06:29:15.992Z In(05) vcpu-0 VMXSTATS: Registering 54 stats: vmx.metrics.lastUpdate
2025-05-02T06:29:15.992Z No(00) vcpu-0 Metrics lastUpdate (s): 11821
2025-05-02T06:29:15.992Z In(05) vcpu-0 Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2025-05-02T06:29:15.992Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2025-05-02T06:29:15.992Z In(05) vcpu-0 Vix: [mainDispatch.c:4129]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-02T06:29:15.992Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2025-05-02T06:29:15.992Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2025-05-02T06:29:15.993Z In(05) vcpu-0 CPT: vmstart
2025-05-02T06:29:15.994Z In(05) vcpu-1 CPT: vmstart
2025-05-02T06:29:16.008Z In(05) mks MKSControlMgr: connected
2025-05-02T06:29:16.228Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:29:16.228Z In(05) mks SWBWindow: Window 0 Defined: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 60, 1920, 1080) pixelScale=1, flags=0xF
2025-05-02T06:29:16.229Z In(05) windowThread-0 VTHREAD 123145338519552 "windowThread-0" tid 188580
2025-05-02T06:29:16.229Z In(05) mks MKS-HWinMux: Started MacOS presentation backend.
2025-05-02T06:29:16.229Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:29:16.229Z In(05) mks SWBWindow: Window 1 Defined: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 0, 1464, 823) pixelScale=1, flags=0x1A
2025-05-02T06:29:16.229Z In(05) windowThread-1 VTHREAD 123145339056128 "windowThread-1" tid 188581
2025-05-02T06:29:17.632Z In(05) vcpu-0 DDB: "longContentID" = "39c0d910a2b7fa8097999c558b61e70e" (was "7986cc9376e37202dc7ff5f818947dd3")
2025-05-02T06:29:29.312Z In(05) vthread-188910 VTHREAD 123145339592704 "vthread-188910" tid 188910
2025-05-02T06:29:36.225Z In(05) svga SVGA disabling SVGA
2025-05-02T06:29:36.225Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 720) flags=0x2
2025-05-02T06:29:36.226Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:29:36.227Z Wa(03) windowThread-1 MKSUIDrawnMKSClient_SendUIDrawnMKSCommand, could not create Mach port to send UI command.
2025-05-02T06:29:36.236Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:29:36.254Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 0, 1464, 823) pixelScale=1, flags=0x1A
2025-05-02T06:29:36.254Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-02T06:29:36.261Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:29:36.261Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:29:36.261Z In(05) mks SWBWindow: Window 2 Defined: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:29:36.261Z In(05) windowThread-2 VTHREAD 123145339056128 "windowThread-2" tid 188977
2025-05-02T06:29:36.736Z In(05) svga SVGA enabling SVGA
2025-05-02T06:29:36.736Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:29:36.746Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 1280, 720) flags=0x2
2025-05-02T06:29:36.746Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:29:36.763Z In(05) mks SWBWindow: Window 2 Destroyed: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:29:36.763Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-02T06:29:36.769Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:29:36.769Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:29:36.769Z In(05) mks SWBWindow: Window 3 Defined: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 0, 1464, 823) pixelScale=1, flags=0x1A
2025-05-02T06:29:36.769Z In(05) windowThread-3 VTHREAD 123145339056128 "windowThread-3" tid 188981
2025-05-02T06:29:40.603Z In(05) vthread-189020 VTHREAD 123145340665856 "vthread-189020" tid 189020
2025-05-02T06:29:40.604Z In(05) vthread-189021 VTHREAD 123145341202432 "vthread-189021" tid 189021
2025-05-02T06:30:00.056Z In(05) svga SVGA disabling SVGA
2025-05-02T06:30:00.056Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 1280, 720) flags=0x2
2025-05-02T06:30:00.057Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:30:00.058Z Wa(03) windowThread-3 MKSUIDrawnMKSClient_SendUIDrawnMKSCommand, could not create Mach port to send UI command.
2025-05-02T06:30:00.068Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:30:00.090Z In(05) mks SWBWindow: Window 3 Destroyed: src screenId=1, src xywh(0, 0, 1280, 720) dest xywh(0, 0, 1464, 823) pixelScale=1, flags=0x1A
2025-05-02T06:30:00.090Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-02T06:30:00.099Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:30:00.099Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:30:00.099Z In(05) mks SWBWindow: Window 4 Defined: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 1, 1464, 813) pixelScale=1, flags=0x1A
2025-05-02T06:30:00.099Z In(05) windowThread-4 VTHREAD 123145321033728 "windowThread-4" tid 189198
2025-05-02T06:30:06.324Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2025-05-02T06:30:06.324Z In(05) mks SWBVmdb: Destroy SWB Window Id #0 because an invalid MKSWindow definition is received from UI over VMDB.
2025-05-02T06:30:06.324Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=0, src xywh(0, 0, 720, 400) dest xywh(0, 0, 1672, 928) pixelScale=1, flags=0xF
2025-05-02T06:30:06.324Z In(05) mks MKSCursorPosition: Destroying the current grab window
2025-05-02T06:30:06.341Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:30:06.360Z No(00) vmx ConfigDB: Unsetting "gui.viewModeAtPowerOn"
2025-05-02T06:30:06.382Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-02T06:30:06.382Z In(05) mks SWBWindow: Window 5 Defined: src screenId=-1, src xywh(0, 0, 720, 400) dest xywh(0, 0, 1672, 928) pixelScale=1, flags=0x7
2025-05-02T06:30:06.382Z In(05) windowThread-5 VTHREAD 123145338519552 "windowThread-5" tid 189298
2025-05-02T06:30:06.407Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.407Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.417Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#1a/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#1f8/in/
2025-05-02T06:30:06.426Z No(00) vmx ConfigDB: Unsetting "gui.lastPoweredViewMode"
2025-05-02T06:30:06.443Z In(05) mks SWBWindow: No more window events will be logged.
2025-05-02T06:30:06.461Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.461Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.472Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#1b/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#1fd/in/
2025-05-02T06:30:06.496Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.497Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.506Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#1c/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#201/in/
2025-05-02T06:30:06.527Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.527Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.536Z In(05) vmx VMXVmdb_SetCfgState: cfgReqPath=/vm/#_VMX/vmx/cfgState/req/#1d/, remDevPath=/vm/#_VMX/vmx/vigor/setCfgStateReq/#205/in/
2025-05-02T06:30:06.553Z In(05) mks SWBWindow: Number of MKSWindows changed: 2 rendering MKSWindow(s) of total 2.
2025-05-02T06:30:06.556Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.556Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.570Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2025-05-02T06:30:06.584Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.584Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.593Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2025-05-02T06:30:06.607Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.608Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.623Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2025-05-02T06:30:06.636Z In(05) vmx TOOLS received request in VMX to set option 'synctime' -> '0'
2025-05-02T06:30:06.636Z No(00) vmx ConfigDB: Setting tools.syncTime = "FALSE"
2025-05-02T06:30:06.650Z No(00) vmx ConfigDB: Setting tools.upgrade.policy = "useGlobal"
2025-05-02T06:30:12.177Z In(05) vcpu-0 PIIX4: PM Soft Off.  Good-bye.
2025-05-02T06:30:12.177Z In(05) vcpu-0 Chipset: The guest has requested that the virtual machine be powered off.
2025-05-02T06:30:12.177Z No(00) vcpu-0 ConfigDB: Setting softPowerOff = "TRUE"
2025-05-02T06:30:12.181Z In(05) vcpu-0 VMX: Issuing power-off request...
2025-05-02T06:30:12.181Z In(05) vmx Stopping VCPU threads...
2025-05-02T06:30:12.183Z In(05) vmx MKSThread: Requesting MKS exit
2025-05-02T06:30:12.183Z In(05) vmx Stopping MKS/SVGA threads
2025-05-02T06:30:12.183Z In(05) svga SVGA thread is exiting the main loop
2025-05-02T06:30:12.183Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-02T06:30:12.183Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2025-05-02T06:30:12.183Z In(05) mks SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2025-05-02T06:30:12.184Z In(05) vmx MKS/SVGA threads are stopped
2025-05-02T06:30:12.187Z In(05) vmx 
2025-05-02T06:30:12.187Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2025-05-02T06:30:12.187Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  2097152 2097152      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem Total excluded                      :  2122240 2122240      - |      -      -      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem Actual maximum                      :         2122240        |             -
2025-05-02T06:30:12.187Z In(05)+ vmx 
2025-05-02T06:30:12.187Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :  1048576 1048576      - |      0      0      -
2025-05-02T06:30:12.187Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :    2944   2944      - |    385    385      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :     196    196      - |    196    196      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaShaderTable            :    1000   1000      - |    193    193      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaShaderText             :    2304   2304      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaContextCache           :    1405   1405      - |      2      2      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXRTViewTable          :      31     31      - |      7      7      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXDSViewTable          :      16     16      - |      4      4      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXSRViewTable          :    1021   1021      - |    193    193      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXUAViewTable          :      70     70      - |     13     13      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXBlendStateTable      :      31     31      - |      4      4      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXElementLayoutTable   :     199    199      - |      4      4      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXDepthStencilTable    :      19     19      - |      4      4      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXRasterizerStateTable :      19     19      - |      4      4      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXSamplerTable         :     141    141      - |     13     13      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXContextTable         :    1472   1472      - |      1      1      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderTable          :     241    241      - |     49     49      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXShaderText           :    4352   4352      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXStreamOutTable       :     516    516      - |      1      1      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaDXQueryTable           :     132    132      - |     13     13      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaVAProcessorTable       :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaVADecoderTable         :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :    1026   1026      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :   10752  10752      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :    3494   3494      - |   2540   2540      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  2711552 2711552      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksLLVM                 :   12288  12288      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   36864  36864      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem Total paged                         :  4141938 4141938      - |   3637   3637      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem Actual maximum                      :         4141938        |        4141938
2025-05-02T06:30:12.188Z In(05)+ vmx 
2025-05-02T06:30:12.188Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      70     70      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    2193   3296      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :   32768  32768      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem Total nonpaged                      :   35485  37036      - |      0      0      -
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem Actual maximum                      :          35485        |         37036
2025-05-02T06:30:12.188Z In(05)+ vmx 
2025-05-02T06:30:12.188Z In(05) vmx                                                       reserved      |          used
2025-05-02T06:30:12.188Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    2145   2202      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      32     32      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       8      8      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       3      3      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VNPTShadow                  :    6427   6427      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VNPTShadowCache             :      53     53      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VNPTBackmap                 :    4413   4413      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     887    887      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    4436   4436      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem Total anonymous                     :   22499  22556      - |      0      0      -
2025-05-02T06:30:12.189Z In(05) vmx OvhdMem Actual maximum                      :          22499        |         22556
2025-05-02T06:30:12.189Z In(05)+ vmx 
2025-05-02T06:30:12.189Z In(05) vmx VMMEM: Maximum Reservation: 16412MB (MainMem=8192MB)
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.189Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) HIST [0, 0]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-02T06:30:12.190Z In(05) vmx MemSched: (null) P50 1 P70 1 P90 1 MIN 0 MAX 0
2025-05-02T06:30:12.190Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2025-05-02T06:30:12.190Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2025-05-02T06:30:12.272Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2025-05-02T06:30:12.273Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x600000943640
2025-05-02T06:30:12.273Z In(05) vmx Tools: [AppStatus] Last heartbeat value 0 (never received)
2025-05-02T06:30:12.273Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2025-05-02T06:30:12.591Z In(05) host-188271 VNET: MacosVmnetVirtApiStopHandler: Ethernet0: stopping virtual interface, status=1000
2025-05-02T06:30:12.598Z In(05) mks MKSControlMgr: disconnected
2025-05-02T06:30:12.598Z In(05) mks MKS-RenderMain: Stopping MTLRenderer
2025-05-02T06:30:12.598Z In(05) mks MKS-RenderMain: Stopping ISBRenderer (MTLRenderer)
2025-05-02T06:30:12.623Z In(05) mks MKS-RenderMain: Stopped ISBRenderer: (MTLRenderer)
2025-05-02T06:30:12.624Z In(05) mks MKS PowerOff
2025-05-02T06:30:12.624Z In(05) svga SVGA thread is exiting
2025-05-02T06:30:12.624Z In(05) mks MKS thread is exiting
2025-05-02T06:30:12.624Z Wa(03) vmx 
2025-05-02T06:30:12.625Z In(05) vmx ide0:0: numIOs = 1035 numMergedIOs = 104 numSplitIOs = 8 ( 7.1%)
2025-05-02T06:30:12.625Z In(05) vmx Closing disk 'ide0:0'
2025-05-02T06:30:12.755Z In(05) deviceThread Device thread is exiting
2025-05-02T06:30:12.755Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2025-05-02T06:30:12.756Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-05-02T06:30:12.756Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-05-02T06:30:12.756Z In(05) vmx WORKER: asyncOps=1116 maxActiveOps=3 maxPending=4 maxCompleted=2
2025-05-02T06:30:12.756Z In(05) PowerNotifyThread PowerNotify thread exiting.
2025-05-02T06:30:12.757Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-02T06:30:12.758Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:30:12.759Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 106A68000.
2025-05-02T06:30:12.759Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2025-05-02T06:30:12.761Z In(05) vmx Vix: [mainDispatch.c:4212]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-02T06:30:12.761Z In(05) vmx Vix: [mainDispatch.c:4230]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-02T06:30:12.761Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-02T06:30:12.761Z In(05) vmx VMX idle exit
2025-05-02T06:30:12.761Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 18
2025-05-02T06:30:13.689Z In(05) vmx Services_Exit: Closed the services.
2025-05-02T06:30:13.690Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2025-05-02T06:30:13.690Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-05-02T06:30:13.693Z In(05) vmx Flushing VMX VMDB connections
2025-05-02T06:30:13.693Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-05-02T06:30:13.693Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-05-02T06:30:13.693Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-05-02T06:30:13.693Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 13
2025-05-02T06:30:13.693Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 15
2025-05-02T06:30:13.699Z In(05) vmx VMX exit (0).
2025-05-02T06:30:13.699Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-05-02T06:30:13.700Z In(05) vmx AIOMGR-S : stat o=5 r=2235 w=6 i=0 br=8725382714 bw=164
