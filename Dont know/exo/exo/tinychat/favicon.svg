<svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 -10 150 70" shape-rendering="crispEdges">
  <g id="logo">
    <!-- t -->
    <polygon points="10,40 10,20 0,20 0,10 10,10 10,0 20,0 20,10 30,10 30,20 20,20 20,30 30,30 30,40" />
    <!-- i -->
    <polygon points="40,40 40,20 50,20 50,40" />
    <polygon points="40,10 40,0 50,0 50,10" />
    <!-- n -->
    <polygon points="60,40 60,10 80,10 80,40 90,40 90,20 70,20 70,40" />
    <!-- y -->
    <polygon points="100,50 100,40 130,40 130,10 120,10 120,20 110,20 110,10 100,10 100,30 120,30 120,50" />
  </g>
  <style>
  @media (prefers-color-scheme: dark) {
    #logo {
      fill: #fff;
    }
  }
  @media (prefers-color-scheme: light) {
    #logo {
      fill: #000;
    }
  }
  </style>
</svg>
