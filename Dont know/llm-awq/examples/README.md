# AWQ Examples

Here we provide two AWQ examples, applying to:
- [Vicuna-7B](https://github.com/lm-sys/FastChat), a chatbot with instruction-tuning
- [LLaVA-13B](https://github.com/lm-sys/FastChat), a visual LM for multi-modal applications like visual reasoning.
- [A simple conversion script](https://github.com/mit-han-lab/llm-awq/tree/main/examples/convert_to_hf.py) to convert llm-awq weights into HF format. 

Here are some example output from the two demos. You should able to observe memory saving when running the demos in 4-bit. Please check the notebooks for details. 

![overview](../figures/example_vis.jpg)
