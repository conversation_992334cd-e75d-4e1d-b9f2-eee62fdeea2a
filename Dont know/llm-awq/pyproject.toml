[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "awq"
version = "0.1.0"
description = "An efficient and accurate low-bit weight quantization(INT3/4) method for LLMs."
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
]
dependencies = [
    "accelerate==0.34.2", "sentencepiece", "tokenizers>=0.12.1",
    "torch==2.3.0", "torchvision==0.18.0",
    "transformers==4.46.0", 
    "lm_eval==0.3.0", "texttable",
    "toml", "attributedict",
    "protobuf",
    "gradio==3.35.2", "gradio_client==0.2.9",
    "fastapi", "uvicorn",
    "pydantic==1.10.19"
]

[tool.setuptools.packages.find]
exclude = ["results*", "scripts*", "examples*"]

[tool.wheel]
exclude = ["results*", "scripts*", "examples*"]
