# Installation
> `npm install --save @types/node-fetch`

# Summary
This package contains type definitions for node-fetch (https://github.com/bitinn/node-fetch).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-fetch.

### Additional Details
 * Last updated: Mon, 11 Nov 2024 18:36:31 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [form-data](https://npmjs.com/package/form-data)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/to<PERSON>werner), [<PERSON><PERSON>](https://github.com/nikcorg), [<PERSON><PERSON>](https://github.com/vinaybedre), [<PERSON>](https://github.com/kyranet), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/JasonLi914), [<PERSON>](https://github.com/southpolesteve), [<PERSON><PERSON>](https://github.com/ExE-Boss), [<PERSON>](https://github.com/alexandrusavin), [<PERSON>](https://github.com/OmgImAlexis), [Jakub Kisielewski](https://github.com/kbkk), and [David Glasser](https://github.com/glasser).
