# DOMException
An implementation of the DOMException class from NodeJS

This package implements the [`DOMException`](https://developer.mozilla.org/en-US/docs/Web/API/DOMException) class, from NodeJS itself.
NodeJS has DOMException built in, but it's not globally available, and you can't require/import it from somewhere.

The only possible way is to use some web-ish tools that have been introduced into NodeJS that throws an error and catch the constructor.
This way you will have the same class that NodeJS has and you can check if the error is a instance of DOMException.
The instanceof check would not have worked with a custom class such as the DOMexception provided by domenic which also is much larger in size.

```js
import DOMException from 'node-domexception'

hello().catch(err => {
  if (err instanceof DOMException) {
    ...
  }
})

const e1 = new DOMException("Something went wrong", "BadThingsError");
console.assert(e1.name === "BadThingsError");
console.assert(e1.code === 0);

const e2 = new DOMException("Another exciting error message", "NoModificationAllowedError");
console.assert(e2.name === "NoModificationAllowedError");
console.assert(e2.code === 7);

console.assert(DOMException.INUSE_ATTRIBUTE_ERR === 10);
```

## APIs

This package exposes two flavors of the `DOMException` interface depending on the imported module.

### `domexception` module

This module default-exports the `DOMException` interface constructor.

### `domexception/webidl2js-wrapper` module

This module exports the `DOMException` [interface wrapper API](https://github.com/jsdom/webidl2js#for-interfaces) generated by [webidl2js](https://github.com/jsdom/webidl2js).
