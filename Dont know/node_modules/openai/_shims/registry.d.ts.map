{"version": 3, "file": "registry.d.ts", "sourceRoot": "", "sources": ["../src/_shims/registry.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,EAAE,KAAK,cAAc,EAAE,MAAM,SAAS,CAAC;AAE9C,MAAM,WAAW,KAAK;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,GAAG,CAAC;IACX,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,GAAG,CAAC;IACd,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,GAAG,CAAC;IACd,IAAI,EAAE,GAAG,CAAC;IACV,IAAI,EAAE,GAAG,CAAC;IACV,cAAc,EAAE,GAAG,CAAC;IACpB,0BAA0B,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EACtD,IAAI,EAAE,KAAK,CAAC,UAAU,CAAC,EACvB,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC,KACpB,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,eAAe,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,GAAG,CAAC;IACtC,YAAY,EACR,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAC3E,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7D,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,OAAO,CAAC;CACzC;AAED,eAAO,IAAI,IAAI,SAAQ,CAAC;AACxB,eAAO,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,SAAqB,CAAC;AACvD,eAAO,IAAI,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,SAAqB,CAAC;AACzD,eAAO,IAAI,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,SAAqB,CAAC;AAC7D,eAAO,IAAI,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,SAAqB,CAAC;AAC/D,eAAO,IAAI,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,SAAqB,CAAC;AAC7D,eAAO,IAAI,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,SAAqB,CAAC;AAC/D,eAAO,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,SAAqB,CAAC;AACvD,eAAO,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,SAAqB,CAAC;AACvD,eAAO,IAAI,cAAc,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAG,SAAqB,CAAC;AAC3E,eAAO,IAAI,0BAA0B,EAAE,KAAK,CAAC,4BAA4B,CAAC,GAAG,SAAqB,CAAC;AACnG,eAAO,IAAI,eAAe,EAAE,KAAK,CAAC,iBAAiB,CAAC,GAAG,SAAqB,CAAC;AAC7E,eAAO,IAAI,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,GAAG,SAAqB,CAAC;AACvE,eAAO,IAAI,cAAc,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAG,SAAqB,CAAC;AAE3E,wBAAgB,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,GAAE;IAAE,IAAI,EAAE,OAAO,CAAA;CAAoB,QAuBlF"}