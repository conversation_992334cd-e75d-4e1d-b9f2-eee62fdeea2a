{"version": 3, "file": "nativeEnum.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/nativeEnum.ts"], "names": [], "mappings": "AAOA,MAAM,UAAU,kBAAkB,CAAC,GAAqB;IACtD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE;QAChE,OAAO,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,CAAE,CAAC,KAAK,QAAQ,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAE,CAAC,CAAC;IAEnE,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAuB,EAAE,EAAE,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtG,OAAO;QACL,IAAI,EACF,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;YACxB,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;gBAC3B,QAAQ;gBACV,CAAC,CAAC,QAAQ;YACZ,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC;QACxB,IAAI,EAAE,YAAY;KACnB,CAAC;AACJ,CAAC"}