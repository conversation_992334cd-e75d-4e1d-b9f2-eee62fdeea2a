{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/number.ts"], "names": [], "mappings": ";;;AACA,uDAA6F;AAa7F,SAAgB,cAAc,CAAC,GAAiB,EAAE,IAAU;IAC1D,MAAM,GAAG,GAA0B;QACjC,IAAI,EAAE,QAAQ;KACf,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,MAAM;QAAE,OAAO,GAAG,CAAC;IAE5B,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;QAC9B,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,KAAK;gBACR,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;gBACrB,IAAA,+BAAe,EAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE;oBACjC,IAAI,KAAK,CAAC,SAAS,EAAE;wBACnB,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAC7E;yBAAM;wBACL,IAAA,yCAAyB,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACtF;iBACF;qBAAM;oBACL,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;wBACpB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;qBACpC;oBACD,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBAC7E;gBACD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE;oBACjC,IAAI,KAAK,CAAC,SAAS,EAAE;wBACnB,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAC7E;yBAAM;wBACL,IAAA,yCAAyB,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACtF;iBACF;qBAAM;oBACL,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;wBACpB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;qBACpC;oBACD,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBAC7E;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAA,yCAAyB,EAAC,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC/E,MAAM;SACT;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AA/CD,wCA+CC"}