{"version": 3, "file": "object.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/object.ts"], "names": [], "mappings": "OACO,EAAmB,QAAQ,EAAE;AAGpC,SAAS,0BAA0B,CAAC,GAAiB,EAAE,IAAU;IAC/D,IAAI,IAAI,CAAC,wBAAwB,KAAK,QAAQ,EAAE;QAC9C,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;YAC9C,GAAG,CAAC,WAAW,KAAK,QAAQ;YAC9B,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC1B,GAAG,IAAI;gBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC;aAC3D,CAAC,IAAI,IAAI,CAAC;KAChB;SAAM;QACL,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;YAC9C,GAAG,CAAC,WAAW,KAAK,aAAa;YACnC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC1B,GAAG,IAAI;gBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC;aAC3D,CAAC,IAAI,IAAI,CAAC;KAChB;AACH,CAAC;AASD,MAAM,UAAU,cAAc,CAAC,GAAiB,EAAE,IAAU;IAC1D,MAAM,MAAM,GAA0B;QACpC,IAAI,EAAE,QAAQ;QACd,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,CACnC,CACE,GAGC,EACD,CAAC,QAAQ,EAAE,OAAO,CAAC,EACnB,EAAE;YACF,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS;gBAAE,OAAO,GAAG,CAAC;YACpE,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE;gBACvC,GAAG,IAAI;gBACP,WAAW,EAAE,YAAY;gBACzB,YAAY;aACb,CAAC,CAAC;YACH,IAAI,SAAS,KAAK,SAAS;gBAAE,OAAO,GAAG,CAAC;YACxC,IAAI,IAAI,CAAC,gBAAgB,IAAI,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;gBAC1E,OAAO,CAAC,IAAI,CACV,kBAAkB,YAAY,CAAC,IAAI,CACjC,GAAG,CACJ,8PAA8P,CAChQ,CAAC;aACH;YACD,OAAO;gBACL,UAAU,EAAE;oBACV,GAAG,GAAG,CAAC,UAAU;oBACjB,CAAC,QAAQ,CAAC,EAAE,SAAS;iBACtB;gBACD,QAAQ,EACN,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;aAC9F,CAAC;QACJ,CAAC,EACD,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CACjC;QACD,oBAAoB,EAAE,0BAA0B,CAAC,GAAG,EAAE,IAAI,CAAC;KAC5D,CAAC;IACF,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,MAAM;QAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;IACrD,OAAO,MAAM,CAAC;AAChB,CAAC"}