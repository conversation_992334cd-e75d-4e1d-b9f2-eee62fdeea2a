{"version": 3, "file": "jsonschema.d.ts", "sourceRoot": "", "sources": ["../src/lib/jsonschema.ts"], "names": [], "mappings": "AASA;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAC1B,CAAC,EAAE,GAAG,MAAM,CAAC,GACb,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,SAAS,GACT,QAAQ,GACR,OAAO,GACP,MAAM,CAAC;AAEX;;;GAGG;AACH,MAAM,MAAM,cAAc,GACtB,MAAM,GACN,MAAM,GACN,OAAO,GACP,gBAAgB,GAChB,eAAe,GACf,IAAI,CAAC;AAGT,MAAM,WAAW,gBAAgB;IAC/B,CAAC,GAAG,EAAE,MAAM,GAAG,cAAc,CAAC;CAC/B;AAID,MAAM,WAAW,eAAgB,SAAQ,KAAK,CAAC,cAAc,CAAC;CAAG;AAEjE;;;;;;;;;;GAUG;AACH,MAAM,MAAM,iBAAiB,GAAG,MAAM,CAAC;AAEvC;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,UAAU,GAAG,OAAO,CAAC;AACxD,MAAM,WAAW,UAAU;IACzB,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,IAAI,CAAC,EAAE,kBAAkB,GAAG,kBAAkB,EAAE,GAAG,SAAS,CAAC;IAC7D,IAAI,CAAC,EAAE,cAAc,EAAE,GAAG,SAAS,CAAC;IACpC,KAAK,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;IAEnC;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAChC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACtC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7B,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEtC;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC/B,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC/B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;OAEG;IACH,KAAK,CAAC,EAAE,oBAAoB,GAAG,oBAAoB,EAAE,GAAG,SAAS,CAAC;IAClE,eAAe,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IACnD,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAClC,QAAQ,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IAE5C;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC,QAAQ,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;IAChC,UAAU,CAAC,EACP;QACE,CAAC,GAAG,EAAE,MAAM,GAAG,oBAAoB,CAAC;KACrC,GACD,SAAS,CAAC;IACd,iBAAiB,CAAC,EACd;QACE,CAAC,GAAG,EAAE,MAAM,GAAG,oBAAoB,CAAC;KACrC,GACD,SAAS,CAAC;IACd,oBAAoB,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IACxD,aAAa,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IAEjD;;OAEG;IACH,EAAE,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IACtC,IAAI,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IACxC,IAAI,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IAExC;;OAEG;IACH,KAAK,CAAC,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC;IAC3C,KAAK,CAAC,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC;IAC3C,KAAK,CAAC,EAAE,oBAAoB,EAAE,GAAG,SAAS,CAAC;IAC3C,GAAG,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IAEvC;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,OAAO,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;IACrC,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC/B,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAChC,QAAQ,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;CACvC"}