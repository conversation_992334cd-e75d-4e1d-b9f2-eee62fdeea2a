export { Audio, type AudioModel, type AudioResponseFormat } from "./audio.js";
export { Speech, type SpeechModel, type SpeechCreateParams } from "./speech.js";
export { Transcriptions, type Transcription, type TranscriptionInclude, type TranscriptionSegment, type TranscriptionStreamEvent, type TranscriptionTextDeltaEvent, type TranscriptionTextDoneEvent, type TranscriptionVerbose, type TranscriptionWord, type TranscriptionCreateResponse, type TranscriptionCreateParams, type TranscriptionCreateParamsNonStreaming, type TranscriptionCreateParamsStreaming, } from "./transcriptions.js";
export { Translations, type Translation, type TranslationVerbose, type TranslationCreateResponse, type TranslationCreateParams, } from "./translations.js";
//# sourceMappingURL=index.d.ts.map