Path: devdocs > README.md

Path: devdocs > README.md Path: devdocs > README.md Path: devdocs > README.md Path: devdocs > README.md # Programming Languages Documentation This directory contains documentation for various programming languages, organized for use with the OS's context retrieval and RAG (Retrieval-Augmented Generation) systems. ## Directory Structure Each programming language has its own directory containing: - `knowledge.json` - Structured knowledge about the language for the RAG system - `metadata.json` - Information about the documentation (name, version, etc.) - `entries/` - JSON files containing documentation entries - `pages/` - HTML files containing the actual documentation content ## Available Languages The following programming languages are currently available: - JavaScript - Modern ECMAScript language documentation - Python - Python language and standard library documentation - C++ - C++ language and STL documentation - Go - Go language and standard library documentation - Rust - Rust language and standard library documentation - Java - Java language and standard library documentation ## Usage with RAG System The documentation in this directory is designed to be indexed by the OS's RAG system, allowing: 1. Context-aware code assistance 2. Documentation lookup during development 3. Code generation with accurate API references 4. Intelligent code completion ## Updating Documentation To update the documentation, you can use the `download_devdocs.py` script in the OS root directory: ```bash # From the OS root directory python3 download_devdocs.py --docs javascript,python,cpp,go,rust,java ``` ## Adding Custom Knowledge You can add custom knowledge to the `knowledge.json` files in each language directory. The knowledge is structured as follows: ```json { "domain": "language_name", "concepts": { "Concept Name": { "description": "Description of the concept", "examples": ["Example code or usage"], "related_concepts": ["Related concept 1", "Related concept 2"], "first_added": "ISO date", "last_updated": "ISO date" } }, "examples": [], "last_updated": "ISO date" } ``` This structure allows the RAG system to retrieve relevant information based on the user's queries.