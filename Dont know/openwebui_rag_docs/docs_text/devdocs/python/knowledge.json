{"domain": "python", "concepts": {"Python Language": {"description": "Python is an interpreted, high-level, general-purpose programming language. Its design philosophy emphasizes code readability with its use of significant whitespace.", "examples": ["# Variable assignment\nx = 10\ny = 20\n\n# Function definition\ndef add(a, b):\n    return a + b\n\n# List comprehension\nsquares = [x**2 for x in range(10)]"], "related_concepts": ["Python Standard Library", "PEP 8", "Virtual Environments", "<PERSON><PERSON>"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Python Functions": {"description": "Functions are a fundamental building block of Python programs. They allow you to organize code into reusable blocks, making your programs more modular and easier to maintain.", "examples": ["# Basic function\ndef greet(name):\n    return f'Hello, {name}!'\n\n# Function with default parameter\ndef greet_with_default(name='World'):\n    return f'Hello, {name}!'\n\n# Lambda function\nsquare = lambda x: x**2"], "related_concepts": ["Decorators", "Generators", "Lambda Functions", "Closures"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Python Data Structures": {"description": "Python has a rich set of built-in data structures, including lists, dictionaries, sets, and tuples. These provide powerful ways to organize and manipulate data.", "examples": ["# List\nfruits = ['apple', 'banana', 'cherry']\n\n# Dictionary\nperson = {\n    'name': '<PERSON>',\n    'age': 30,\n    'city': 'New York'\n}\n\n# Set\nunique_numbers = {1, 2, 3, 4, 5}\n\n# Tuple\ncoordinates = (10, 20)"], "related_concepts": ["List Comprehensions", "Dictionary Comprehensions", "Collections Module", "Itertools"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}