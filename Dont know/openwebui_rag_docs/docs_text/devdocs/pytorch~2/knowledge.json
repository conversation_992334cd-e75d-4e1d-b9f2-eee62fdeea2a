{"domain": "pytorch", "concepts": {"PyTorch Overview": {"description": "PyTorch is an open-source machine learning library based on the Torch library, used for applications such as computer vision and natural language processing. It is primarily developed by Facebook's AI Research lab (FAIR).", "examples": ["# Basic PyTorch example\nimport torch\n\n# Create a tensor\nx = torch.tensor([[1, 2], [3, 4]])\nprint(x)\n\n# Create a simple neural network\nimport torch.nn as nn\nimport torch.nn.functional as F\n\nclass Net(nn.Module):\n    def __init__(self):\n        super(Net, self).__init__()\n        self.fc1 = nn.Linear(10, 5)\n        self.fc2 = nn.Linear(5, 2)\n        \n    def forward(self, x):\n        x = F.relu(self.fc1(x))\n        x = self.fc2(x)\n        return x\n        \nmodel = Net()"], "related_concepts": ["Deep Learning", "Neural Networks", "Tensors", "Autograd"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "PyTorch Tensors": {"description": "Tensors are multi-dimensional arrays similar to NumPy's ndarrays, but with the ability to run on GPUs and other hardware accelerators. They are the fundamental data structure in PyTorch and the building block for all operations.", "examples": ["# Creating tensors in PyTorch\nimport torch\n\n# From Python list\ntensor_a = torch.tensor([1, 2, 3])\n\n# With specific data type\ntensor_b = torch.tensor([1.0, 2.0], dtype=torch.float64)\n\n# Zeros and ones\ntensor_c = torch.zeros(2, 3)  # 2x3 tensor of zeros\ntensor_d = torch.ones(2, 3)   # 2x3 tensor of ones\n\n# Random tensors\ntensor_e = torch.rand(2, 3)   # Random values from uniform distribution [0, 1)\n\n# Moving tensors to GPU (if available)\nif torch.cuda.is_available():\n    tensor_a = tensor_a.cuda()\n    # Or using device syntax\n    device = torch.device(\"cuda:0\")\n    tensor_b = tensor_b.to(device)"], "related_concepts": ["<PERSON>um<PERSON><PERSON>", "GPU Acceleration", "Tensor Operations", "Data Types"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "PyTorch Autograd": {"description": "Autograd is PyTorch's automatic differentiation engine that powers neural network training. It keeps track of operations performed on tensors and automatically computes gradients during the backward pass.", "examples": ["# Autograd example\nimport torch\n\n# Create tensors with requires_grad=True\nx = torch.tensor([2.0], requires_grad=True)\ny = torch.tensor([3.0], requires_grad=True)\n\n# Perform operations\nz = x**2 + y**3\n\n# Compute gradients\nz.backward()\n\n# Access gradients\nprint(f\"dz/dx: {x.grad}\")  # Should be 2*x = 4\nprint(f\"dz/dy: {y.grad}\")  # Should be 3*y^2 = 27"], "related_concepts": ["Backpropagation", "Computational Graphs", "Chain Rule", "<PERSON><PERSON><PERSON> Descent"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "PyTorch Neural Networks": {"description": "PyTorch provides the torch.nn module which contains classes and functions to build neural networks. It includes common layers, loss functions, and optimization algorithms.", "examples": ["# Neural network training example\nimport torch\nimport torch.nn as nn\nimport torch.optim as optim\n\n# Define a simple model\nclass SimpleModel(nn.Module):\n    def __init__(self):\n        super(SimpleModel, self).__init__()\n        self.fc1 = nn.Linear(10, 5)\n        self.fc2 = nn.Linear(5, 1)\n        \n    def forward(self, x):\n        x = torch.relu(self.fc1(x))\n        x = self.fc2(x)\n        return x\n\n# Create model, loss function, and optimizer\nmodel = SimpleModel()\ncriterion = nn.MSELoss()\noptimizer = optim.SGD(model.parameters(), lr=0.01)\n\n# Training loop (simplified)\nfor epoch in range(100):\n    # Generate dummy data\n    inputs = torch.randn(20, 10)\n    targets = torch.randn(20, 1)\n    \n    # Forward pass\n    outputs = model(inputs)\n    loss = criterion(outputs, targets)\n    \n    # Backward pass and optimize\n    optimizer.zero_grad()\n    loss.backward()\n    optimizer.step()\n    \n    if epoch % 10 == 0:\n        print(f'Epoch {epoch}, Loss: {loss.item():.4f}')"], "related_concepts": ["Layers", "Loss Functions", "Optimizers", "Training Loop"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}