{"domain": "tensorflow", "concepts": {"TensorFlow Overview": {"description": "TensorFlow is an open-source machine learning framework developed by Google. It provides a comprehensive ecosystem of tools, libraries, and community resources that lets researchers push the state-of-the-art in ML and developers easily build and deploy ML-powered applications.", "examples": ["# Basic TensorFlow example\nimport tensorflow as tf\n\n# Create a simple model\nmodel = tf.keras.Sequential([\n    tf.keras.layers.Dense(128, activation='relu', input_shape=(784,)),\n    tf.keras.layers.Dropout(0.2),\n    tf.keras.layers.Dense(10, activation='softmax')\n])\n\n# Compile the model\nmodel.compile(optimizer='adam',\n              loss='sparse_categorical_crossentropy',\n              metrics=['accuracy'])"], "related_concepts": ["Machine Learning", "Deep Learning", "Neural Networks", "<PERSON><PERSON>"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "TensorFlow Tensors": {"description": "Tensors are multi-dimensional arrays with a uniform type (called a dtype). You can see tensors as a generalization of vectors and matrices to potentially higher dimensions. TensorFlow uses tensors as the fundamental data structure.", "examples": ["# Creating tensors in TensorFlow\nimport tensorflow as tf\n\n# Create a constant tensor\ntensor_1 = tf.constant([[1, 2], [3, 4]])\n\n# Create a tensor filled with zeros\ntensor_2 = tf.zeros([2, 3])\n\n# Create a tensor filled with ones\ntensor_3 = tf.ones([3, 2])\n\n# Create a tensor with random values\ntensor_4 = tf.random.normal([3, 2], mean=0, stddev=1)"], "related_concepts": ["<PERSON>um<PERSON><PERSON>", "Variables", "Gradients", "Computational Graph"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "TensorFlow Keras": {"description": "Keras is TensorFlow's high-level API for building and training deep learning models. It provides essential abstractions and building blocks for developing and shipping machine learning solutions with high iteration velocity.", "examples": ["# Building a model with Keras\nimport tensorflow as tf\nfrom tensorflow import keras\n\n# Load the MNIST dataset\n(x_train, y_train), (x_test, y_test) = keras.datasets.mnist.load_data()\n\n# Preprocess the data\nx_train = x_train.reshape(-1, 28*28).astype('float32') / 255.0\nx_test = x_test.reshape(-1, 28*28).astype('float32') / 255.0\n\n# Build the model\nmodel = keras.Sequential([\n    keras.layers.Dense(128, activation='relu', input_shape=(784,)),\n    keras.layers.Dense(64, activation='relu'),\n    keras.layers.Dense(10, activation='softmax')\n])\n\n# Compile the model\nmodel.compile(optimizer='adam',\n              loss='sparse_categorical_crossentropy',\n              metrics=['accuracy'])\n\n# Train the model\nmodel.fit(x_train, y_train, epochs=5, batch_size=32, validation_split=0.2)"], "related_concepts": ["Sequential Model", "Functional API", "Layers", "Optimizers", "Loss Functions"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "TensorFlow SavedModel": {"description": "SavedModel is TensorFlow's recommended format for saving and sharing trained models. It contains a complete TensorFlow program, including weights and computation, and is portable across different environments.", "examples": ["# Saving and loading a model with SavedModel\nimport tensorflow as tf\n\n# Create and train a model\nmodel = tf.keras.Sequential([...])\nmodel.compile(...)\nmodel.fit(...)\n\n# Save the model\ntf.saved_model.save(model, 'path/to/saved_model')\n\n# Load the model\nloaded_model = tf.saved_model.load('path/to/saved_model')\n\n# For Keras models, you can also use:\nsaved_model = tf.keras.models.load_model('path/to/saved_model')"], "related_concepts": ["Model Serialization", "TensorFlow Serving", "TensorFlow Lite", "TensorFlow.js"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}