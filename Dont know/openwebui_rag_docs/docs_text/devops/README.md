Path: devops > README.md

Path: devops > README.md Path: devops > README.md Path: devops > README.md Path: devops > README.md # DevOps Tools Documentation This directory contains documentation for DevOps and infrastructure tools, organized for use with the OS's context retrieval and RAG (Retrieval-Augmented Generation) systems. ## Available Tools - `docker/` - Docker containerization platform - `kubernetes/` - Kubernetes container orchestration - `homebrew/` - Homebrew package manager - `npm/` - Node Package Manager - `yarn/` - Yarn package manager ## Structure Each tool directory contains: - `knowledge.json` - Structured knowledge about the tool for the RAG system - `metadata.json` - Information about the documentation (name, version, etc.) - `entries/` - JSON files containing documentation entries - `pages/` - HTML files containing the actual documentation content ## Usage with RAG System The documentation in this directory is designed to be indexed by the OS's RAG system, allowing: 1. Context-aware DevOps assistance 2. Documentation lookup during development 3. Command generation with accurate syntax 4. Intelligent completion for configuration files ## Adding New Tools To add documentation for additional DevOps tools: ```bash # From the OS root directory python3 download_devdocs.py --docs tool1,tool2,tool3 --output-dir continuous_learning/knowledge_base/prog_lang/devops ```