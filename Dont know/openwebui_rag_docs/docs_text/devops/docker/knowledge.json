{"domain": "docker", "concepts": {"Docker Overview": {"description": "Docker is a platform for developing, shipping, and running applications in containers. Containers are lightweight, portable, and self-sufficient environments that include everything needed to run an application.", "examples": ["# Basic Docker commands\n\n# Pull an image from Docker Hub\ndocker pull nginx\n\n# Run a container\ndocker run -d -p 80:80 --name my-nginx nginx\n\n# List running containers\ndocker ps\n\n# Stop a container\ndocker stop my-nginx\n\n# Remove a container\ndocker rm my-nginx"], "related_concepts": ["Containers", "Virtualization", "Microservices", "DevOps"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Docker Images": {"description": "Docker images are read-only templates used to create Docker containers. They contain the application code, libraries, dependencies, tools, and other files needed for an application to run.", "examples": ["# Working with Docker images\n\n# List images\ndocker images\n\n# Build an image from a Dockerfile\ndocker build -t my-app:1.0 .\n\n# Tag an image\ndocker tag my-app:1.0 username/my-app:1.0\n\n# Push an image to Docker Hub\ndocker push username/my-app:1.0\n\n# Remove an image\ndocker rmi my-app:1.0"], "related_concepts": ["Dockerfile", "Layers", "Registry", "<PERSON><PERSON>"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Dockerfile": {"description": "A Dockerfile is a text document that contains all the commands a user could call on the command line to assemble an image. Using docker build, users can create an automated build that executes several command-line instructions in succession.", "examples": ["# Example Dockerfile for a Node.js application\n\n# Use an official Node.js runtime as the base image\nFROM node:14\n\n# Set the working directory in the container\nWORKDIR /app\n\n# Copy package.json and package-lock.json\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm install\n\n# Copy the rest of the application code\nCOPY . .\n\n# Expose port 3000\nEXPOSE 3000\n\n# Define the command to run the application\nCMD [\"npm\", \"start\"]"], "related_concepts": ["Docker Images", "Build Context", "Multi-stage Builds", "Best Practices"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Docker Compose": {"description": "Docker Compose is a tool for defining and running multi-container Docker applications. With Compose, you use a YAML file to configure your application's services, networks, and volumes.", "examples": ["# Example docker-compose.yml file\n\nversion: '3'\n\nservices:\n  web:\n    build: ./web\n    ports:\n      - \"5000:5000\"\n    depends_on:\n      - db\n    environment:\n      - DATABASE_URL=************************************/app\n  \n  db:\n    image: postgres:13\n    volumes:\n      - postgres_data:/var/lib/postgresql/data\n    environment:\n      - POSTGRES_USER=postgres\n      - POSTGRES_PASSWORD=postgres\n      - POSTGRES_DB=app\n\nvolumes:\n  postgres_data:"], "related_concepts": ["Multi-container Applications", "Service Definition", "Networks", "Volumes"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}