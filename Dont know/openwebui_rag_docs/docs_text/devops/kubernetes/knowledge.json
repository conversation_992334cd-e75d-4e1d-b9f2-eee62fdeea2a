{"domain": "kubernetes", "concepts": {"Kubernetes Overview": {"description": "Kubernetes is an open-source container orchestration platform that automates the deployment, scaling, and management of containerized applications. It groups containers that make up an application into logical units for easy management and discovery.", "examples": ["# Example Kubernetes deployment YAML\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: nginx-deployment\n  labels:\n    app: nginx\nspec:\n  replicas: 3\n  selector:\n    matchLabels:\n      app: nginx\n  template:\n    metadata:\n      labels:\n        app: nginx\n    spec:\n      containers:\n      - name: nginx\n        image: nginx:1.14.2\n        ports:\n        - containerPort: 80"], "related_concepts": ["Containers", "<PERSON>er", "Microservices", "Container Orchestration"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Kubernetes Pods": {"description": "Pods are the smallest deployable units of computing that you can create and manage in Kubernetes. A Pod is a group of one or more containers, with shared storage and network resources, and a specification for how to run the containers.", "examples": ["# Example Pod YAML\napiVersion: v1\nkind: Pod\nmetadata:\n  name: nginx\n  labels:\n    app: nginx\nspec:\n  containers:\n  - name: nginx\n    image: nginx:1.14.2\n    ports:\n    - containerPort: 80"], "related_concepts": ["Containers", "Deployments", "ReplicaSets", "Services"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Kubernetes Services": {"description": "A Kubernetes Service is an abstraction which defines a logical set of Pods and a policy by which to access them. Services enable a loose coupling between dependent Pods. A Service is defined using YAML or JSON, like all Kubernetes objects.", "examples": ["# Example Service YAML\napiVersion: v1\nkind: Service\nmetadata:\n  name: my-service\nspec:\n  selector:\n    app: MyApp\n  ports:\n  - protocol: TCP\n    port: 80\n    targetPort: 9376"], "related_concepts": ["Pods", "ClusterIP", "NodePort", "LoadBalancer", "Ingress"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Kubernetes Deployments": {"description": "A Deployment provides declarative updates for Pods and ReplicaSets. You describe a desired state in a Deployment, and the Deployment Controller changes the actual state to the desired state at a controlled rate.", "examples": ["# Example Deployment with rolling update strategy\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: nginx-deployment\n  labels:\n    app: nginx\nspec:\n  replicas: 3\n  strategy:\n    type: RollingUpdate\n    rollingUpdate:\n      maxSurge: 1\n      maxUnavailable: 1\n  selector:\n    matchLabels:\n      app: nginx\n  template:\n    metadata:\n      labels:\n        app: nginx\n    spec:\n      containers:\n      - name: nginx\n        image: nginx:1.14.2\n        ports:\n        - containerPort: 80"], "related_concepts": ["Pods", "ReplicaSets", "Rolling Updates", "Rollbacks"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}