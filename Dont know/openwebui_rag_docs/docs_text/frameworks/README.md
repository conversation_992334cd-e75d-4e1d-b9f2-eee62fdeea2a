Path: frameworks > README.md

Path: frameworks > README.md Path: frameworks > README.md Path: frameworks > README.md Path: frameworks > README.md # Application Frameworks Documentation This directory contains documentation for application frameworks and databases, organized for use with the OS's context retrieval and RAG (Retrieval-Augmented Generation) systems. ## Available Frameworks - `node/` - Node.js runtime environment - `flask/` - Flask web framework for Python - `sqlite/` - SQLite database engine ## Structure Each framework directory contains: - `knowledge.json` - Structured knowledge about the framework for the RAG system - `metadata.json` - Information about the documentation (name, version, etc.) - `entries/` - JSON files containing documentation entries - `pages/` - HTML files containing the actual documentation content ## Usage with RAG System The documentation in this directory is designed to be indexed by the OS's RAG system, allowing: 1. Context-aware application development assistance 2. Documentation lookup during development 3. Code generation with accurate API references 4. Intelligent code completion for framework-specific code ## Adding New Frameworks To add documentation for additional frameworks: ```bash # From the OS root directory python3 download_devdocs.py --docs framework1,framework2,framework3 --output-dir continuous_learning/knowledge_base/prog_lang/frameworks ```