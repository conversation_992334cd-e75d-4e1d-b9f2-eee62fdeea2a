{"domain": "node", "concepts": {"Node.js Overview": {"description": "Node.js is an open-source, cross-platform JavaScript runtime environment that executes JavaScript code outside a web browser. Node.js lets developers use JavaScript to write command line tools and for server-side scripting.", "examples": ["// Simple HTTP server with Node.js\nconst http = require('http');\n\nconst server = http.createServer((req, res) => {\n  res.statusCode = 200;\n  res.setHeader('Content-Type', 'text/plain');\n  res.end('Hello World\\n');\n});\n\nserver.listen(3000, '127.0.0.1', () => {\n  console.log('Server running at http://127.0.0.1:3000/');\n});"], "related_concepts": ["JavaScript", "V8 Engine", "Event Loop", "npm", "Express.js"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Node.js Modules": {"description": "In Node.js, modules are the fundamental building blocks for organizing code. Node.js has a simple module loading system using the CommonJS module format. Each file is treated as a separate module.", "examples": ["// math.js - Module definition\nfunction add(a, b) {\n  return a + b;\n}\n\nfunction subtract(a, b) {\n  return a - b;\n}\n\nmodule.exports = { add, subtract };\n\n// app.js - Module usage\nconst math = require('./math');\nconsole.log(math.add(5, 3));      // 8\nconsole.log(math.subtract(5, 3)); // 2"], "related_concepts": ["CommonJS", "ES Modules", "require", "module.exports", "package.json"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Node.js Event Loop": {"description": "The event loop is what allows Node.js to perform non-blocking I/O operations despite JavaScript being single-threaded. It works by offloading operations to the system kernel whenever possible and handling callbacks when operations complete.", "examples": ["// Example demonstrating the event loop\nconsole.log('Start');\n\nsetTimeout(() => {\n  console.log('Timeout callback executed');\n}, 0);\n\nPromise.resolve().then(() => {\n  console.log('Promise resolved');\n});\n\nconsole.log('End');\n\n// Output:\n// Start\n// End\n// Promise resolved\n// Timeout callback executed"], "related_concepts": ["Asynchronous Programming", "Callbacks", "Promises", "async/await", "libuv"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "Node.js File System": {"description": "Node.js includes the fs module which provides an API for interacting with the file system. It provides both synchronous and asynchronous methods for file operations like reading, writing, updating, deleting, and renaming files.", "examples": ["// File system operations in Node.js\nconst fs = require('fs');\n\n// Synchronous read\ntry {\n  const data = fs.readFileSync('file.txt', 'utf8');\n  console.log(data);\n} catch (err) {\n  console.error(err);\n}\n\n// Asynchronous read\nfs.readFile('file.txt', 'utf8', (err, data) => {\n  if (err) {\n    console.error(err);\n    return;\n  }\n  console.log(data);\n});\n\n// Using promises with fs\nconst fsPromises = require('fs').promises;\n\nasync function readFile() {\n  try {\n    const data = await fsPromises.readFile('file.txt', 'utf8');\n    console.log(data);\n  } catch (err) {\n    console.error(err);\n  }\n}\n\nreadFile();"], "related_concepts": ["Streams", "Buffers", "Path Module", "File Descriptors", "Directory Operations"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}