Path: languages > README.md

Path: languages > README.md Path: languages > README.md Path: languages > README.md Path: languages > README.md # Programming Languages Documentation This directory contains documentation for core programming languages, organized for use with the OS's context retrieval and RAG (Retrieval-Augmented Generation) systems. ## Available Languages - `python/` - Python language documentation, including full Python 3.13 docs - `javascript/` - JavaScript language documentation - `cpp/` - C++ language documentation - `go/` - Go language documentation - `rust/` - Rust language documentation - `java/` - Java language documentation - `kotlin/` - Kotlin language documentation ## Structure Each language directory contains: - `knowledge.json` - Structured knowledge about the language for the RAG system - `metadata.json` - Information about the documentation (name, version, etc.) - `entries/` - JSON files containing documentation entries - `pages/` - HTML files containing the actual documentation content Some languages (like Python) also include a `full-docs` directory with comprehensive documentation. ## Usage with RAG System The documentation in this directory is designed to be indexed by the OS's RAG system, allowing: 1. Context-aware code assistance 2. Documentation lookup during development 3. Code generation with accurate API references 4. Intelligent code completion ## Adding New Languages To add documentation for additional programming languages: ```bash # From the OS root directory python3 download_devdocs.py --docs language1,language2,language3 --output-dir continuous_learning/knowledge_base/prog_lang/languages ```