{"domain": "javascript", "concepts": {"JavaScript Language": {"description": "JavaScript is a high-level, interpreted programming language that conforms to the ECMAScript specification. It is a language that is also characterized as dynamic, weakly typed, prototype-based and multi-paradigm.", "examples": ["// Variable declaration\nlet x = 10;\nconst y = 20;\n\n// Function declaration\nfunction add(a, b) {\n  return a + b;\n}\n\n// Arrow function\nconst multiply = (a, b) => a * b;"], "related_concepts": ["ECMAScript", "DOM", "Web APIs", "Node.js"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "JavaScript Functions": {"description": "Functions are one of the fundamental building blocks in JavaScript. A function is a JavaScript procedure—a set of statements that performs a task or calculates a value.", "examples": ["// Function declaration\nfunction greet(name) {\n  return `Hello, ${name}!`;\n}\n\n// Function expression\nconst sayGoodbye = function(name) {\n  return `Goodbye, ${name}!`;\n};\n\n// Arrow function\nconst square = x => x * x;"], "related_concepts": ["Closures", "Callbacks", "Promises", "Async/Await"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}, "JavaScript Objects": {"description": "In JavaScript, objects are king. If you understand objects, you understand JavaScript. In JavaScript, almost everything is an object.", "examples": ["// Object literal\nconst person = {\n  firstName: '<PERSON>',\n  lastName: '<PERSON><PERSON>',\n  age: 30,\n  fullName: function() {\n    return this.firstName + ' ' + this.lastName;\n  }\n};\n\n// Accessing properties\nconsole.log(person.firstName); // John\nconsole.log(person['lastName']); // Doe\n\n// Calling methods\nconsole.log(person.fullName()); // <PERSON>"], "related_concepts": ["Prototypes", "Classes", "Inheritance", "JSON"], "first_added": "2025-05-03T12:00:00.000Z", "last_updated": "2025-05-03T12:00:00.000Z"}}, "examples": [], "last_updated": "2025-05-03T12:00:00.000Z"}