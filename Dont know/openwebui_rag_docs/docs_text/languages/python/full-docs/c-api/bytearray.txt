Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > bytearray.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > bytearray.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > bytearray.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > bytearray.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > bytearray.txt Byte Array Objects ****************** type PyByteArrayObject This subtype of "PyObject" represents a Python bytearray object. PyTypeObject PyByteArray_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python bytearray type; it is the same object as "bytearray" in the Python layer. Type check macros ================= int PyByteArray_Check(PyObject *o) Return true if the object *o* is a bytearray object or an instance of a subtype of the bytearray type. This function always succeeds. int PyByteArray_CheckExact(PyObject *o) Return true if the object *o* is a bytearray object, but not an instance of a subtype of the bytearray type. This function always succeeds. Direct API functions ==================== PyObject *PyByteArray_FromObject(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* Return a new bytearray object from any object, *o*, that implements the buffer protocol. On failure, return "NULL" with an exception set. PyObject *PyByteArray_FromStringAndSize(const char *string, Py_ssize_t len) *Return value: New reference.** Part of the Stable ABI.* Create a new bytearray object from *string* and its length, *len*. On failure, return "NULL" with an exception set. PyObject *PyByteArray_Concat(PyObject *a, PyObject *b) *Return value: New reference.** Part of the Stable ABI.* Concat bytearrays *a* and *b* and return a new bytearray with the result. On failure, return "NULL" with an exception set. Py_ssize_t PyByteArray_Size(PyObject *bytearray) * Part of the Stable ABI.* Return the size of *bytearray* after checking for a "NULL" pointer. char *PyByteArray_AsString(PyObject *bytearray) * Part of the Stable ABI.* Return the contents of *bytearray* as a char array after checking for a "NULL" pointer. The returned array always has an extra null byte appended. int PyByteArray_Resize(PyObject *bytearray, Py_ssize_t len) * Part of the Stable ABI.* Resize the internal buffer of *bytearray* to *len*. Macros ====== These macros trade safety for speed and they don't check pointers. char *PyByteArray_AS_STRING(PyObject *bytearray) Similar to "PyByteArray_AsString()", but without error checking. Py_ssize_t PyByteArray_GET_SIZE(PyObject *bytearray) Similar to "PyByteArray_Size()", but without error checking.