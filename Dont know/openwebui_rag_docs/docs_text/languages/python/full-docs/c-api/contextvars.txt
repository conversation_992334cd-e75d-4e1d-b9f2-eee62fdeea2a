Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > contextvars.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > contextvars.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > contextvars.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > contextvars.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > contextvars.txt Context Variables Objects ************************* Added in version 3.7. Changed in version 3.7.1: Note: In Python 3.7.1 the signatures of all context variables C APIs were **changed** to use "PyObject" pointers instead of "PyContext", "PyContextVar", and "PyContextToken", e.g.: // in 3.7.0: PyContext *PyContext_New(void); // in 3.7.1+: PyObject *PyContext_New(void); See bpo-34762 for more details. This section details the public C API for the "contextvars" module. type PyContext The C structure used to represent a "contextvars.Context" object. type PyContextVar The C structure used to represent a "contextvars.ContextVar" object. type PyContextToken The C structure used to represent a "contextvars.Token" object. PyTypeObject PyContext_Type The type object representing the *context* type. PyTypeObject PyContextVar_Type The type object representing the *context variable* type. PyTypeObject PyContextToken_Type The type object representing the *context variable token* type. Type-check macros: int PyContext_CheckExact(PyObject *o) Return true if *o* is of type "PyContext_Type". *o* must not be "NULL". This function always succeeds. int PyContextVar_CheckExact(PyObject *o) Return true if *o* is of type "PyContextVar_Type". *o* must not be "NULL". This function always succeeds. int PyContextToken_CheckExact(PyObject *o) Return true if *o* is of type "PyContextToken_Type". *o* must not be "NULL". This function always succeeds. Context object management functions: PyObject *PyContext_New(void) *Return value: New reference.* Create a new empty context object. Returns "NULL" if an error has occurred. PyObject *PyContext_Copy(PyObject *ctx) *Return value: New reference.* Create a shallow copy of the passed *ctx* context object. Returns "NULL" if an error has occurred. PyObject *PyContext_CopyCurrent(void) *Return value: New reference.* Create a shallow copy of the current thread context. Returns "NULL" if an error has occurred. int PyContext_Enter(PyObject *ctx) Set *ctx* as the current context for the current thread. Returns "0" on success, and "-1" on error. int PyContext_Exit(PyObject *ctx) Deactivate the *ctx* context and restore the previous context as the current context for the current thread. Returns "0" on success, and "-1" on error. Context variable functions: PyObject *PyContextVar_New(const char *name, PyObject *def) *Return value: New reference.* Create a new "ContextVar" object. The *name* parameter is used for introspection and debug purposes. The *def* parameter specifies a default value for the context variable, or "NULL" for no default. If an error has occurred, this function returns "NULL". int PyContextVar_Get(PyObject *var, PyObject *default_value, PyObject **value) Get the value of a context variable. Returns "-1" if an error has occurred during lookup, and "0" if no error occurred, whether or not a value was found. If the context variable was found, *value* will be a pointer to it. If the context variable was *not* found, *value* will point to: * *default_value*, if not "NULL"; * the default value of *var*, if not "NULL"; * "NULL" Except for "NULL", the function returns a new reference. PyObject *PyContextVar_Set(PyObject *var, PyObject *value) *Return value: New reference.* Set the value of *var* to *value* in the current context. Returns a new token object for this change, or "NULL" if an error has occurred. int PyContextVar_Reset(PyObject *var, PyObject *token) Reset the state of the *var* context variable to that it was in before "PyContextVar_Set()" that returned the *token* was called. This function returns "0" on success and "-1" on error.