Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > datetime.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > datetime.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > datetime.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > datetime.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > datetime.txt DateTime Objects **************** Various date and time objects are supplied by the "datetime" module. Before using any of these functions, the header file "datetime.h" must be included in your source (note that this is not included by "Python.h"), and the macro "PyDateTime_IMPORT" must be invoked, usually as part of the module initialisation function. The macro puts a pointer to a C structure into a static variable, "PyDateTimeAPI", that is used by the following macros. type PyDateTime_Date This subtype of "PyObject" represents a Python date object. type PyDateTime_DateTime This subtype of "PyObject" represents a Python datetime object. type PyDateTime_Time This subtype of "PyObject" represents a Python time object. type PyDateTime_Delta This subtype of "PyObject" represents the difference between two datetime values. PyTypeObject PyDateTime_DateType This instance of "PyTypeObject" represents the Python date type; it is the same object as "datetime.date" in the Python layer. PyTypeObject PyDateTime_DateTimeType This instance of "PyTypeObject" represents the Python datetime type; it is the same object as "datetime.datetime" in the Python layer. PyTypeObject PyDateTime_TimeType This instance of "PyTypeObject" represents the Python time type; it is the same object as "datetime.time" in the Python layer. PyTypeObject PyDateTime_DeltaType This instance of "PyTypeObject" represents Python type for the difference between two datetime values; it is the same object as "datetime.timedelta" in the Python layer. PyTypeObject PyDateTime_TZInfoType This instance of "PyTypeObject" represents the Python time zone info type; it is the same object as "datetime.tzinfo" in the Python layer. Macro for access to the UTC singleton: PyObject *PyDateTime_TimeZone_UTC Returns the time zone singleton representing UTC, the same object as "datetime.timezone.utc". Added in version 3.7. Type-check macros: int PyDate_Check(PyObject *ob) Return true if *ob* is of type "PyDateTime_DateType" or a subtype of "PyDateTime_DateType". *ob* must not be "NULL". This function always succeeds. int PyDate_CheckExact(PyObject *ob) Return true if *ob* is of type "PyDateTime_DateType". *ob* must not be "NULL". This function always succeeds. int PyDateTime_Check(PyObject *ob) Return true if *ob* is of type "PyDateTime_DateTimeType" or a subtype of "PyDateTime_DateTimeType". *ob* must not be "NULL". This function always succeeds. int PyDateTime_CheckExact(PyObject *ob) Return true if *ob* is of type "PyDateTime_DateTimeType". *ob* must not be "NULL". This function always succeeds. int PyTime_Check(PyObject *ob) Return true if *ob* is of type "PyDateTime_TimeType" or a subtype of "PyDateTime_TimeType". *ob* must not be "NULL". This function always succeeds. int PyTime_CheckExact(PyObject *ob) Return true if *ob* is of type "PyDateTime_TimeType". *ob* must not be "NULL". This function always succeeds. int PyDelta_Check(PyObject *ob) Return true if *ob* is of type "PyDateTime_DeltaType" or a subtype of "PyDateTime_DeltaType". *ob* must not be "NULL". This function always succeeds. int PyDelta_CheckExact(PyObject *ob) Return true if *ob* is of type "PyDateTime_DeltaType". *ob* must not be "NULL". This function always succeeds. int PyTZInfo_Check(PyObject *ob) Return true if *ob* is of type "PyDateTime_TZInfoType" or a subtype of "PyDateTime_TZInfoType". *ob* must not be "NULL". This function always succeeds. int PyTZInfo_CheckExact(PyObject *ob) Return true if *ob* is of type "PyDateTime_TZInfoType". *ob* must not be "NULL". This function always succeeds. Macros to create objects: PyObject *PyDate_FromDate(int year, int month, int day) *Return value: New reference.* Return a "datetime.date" object with the specified year, month and day. PyObject *PyDateTime_FromDateAndTime(int year, int month, int day, int hour, int minute, int second, int usecond) *Return value: New reference.* Return a "datetime.datetime" object with the specified year, month, day, hour, minute, second and microsecond. PyObject *PyDateTime_FromDateAndTimeAndFold(int year, int month, int day, int hour, int minute, int second, int usecond, int fold) *Return value: New reference.* Return a "datetime.datetime" object with the specified year, month, day, hour, minute, second, microsecond and fold. Added in version 3.6. PyObject *PyTime_FromTime(int hour, int minute, int second, int usecond) *Return value: New reference.* Return a "datetime.time" object with the specified hour, minute, second and microsecond. PyObject *PyTime_FromTimeAndFold(int hour, int minute, int second, int usecond, int fold) *Return value: New reference.* Return a "datetime.time" object with the specified hour, minute, second, microsecond and fold. Added in version 3.6. PyObject *PyDelta_FromDSU(int days, int seconds, int useconds) *Return value: New reference.* Return a "datetime.timedelta" object representing the given number of days, seconds and microseconds. Normalization is performed so that the resulting number of microseconds and seconds lie in the ranges documented for "datetime.timedelta" objects. PyObject *PyTimeZone_FromOffset(PyObject *offset) *Return value: New reference.* Return a "datetime.timezone" object with an unnamed fixed offset represented by the *offset* argument. Added in version 3.7. PyObject *PyTimeZone_FromOffsetAndName(PyObject *offset, PyObject *name) *Return value: New reference.* Return a "datetime.timezone" object with a fixed offset represented by the *offset* argument and with tzname *name*. Added in version 3.7. Macros to extract fields from date objects. The argument must be an instance of "PyDateTime_Date", including subclasses (such as "PyDateTime_DateTime"). The argument must not be "NULL", and the type is not checked: int PyDateTime_GET_YEAR(PyDateTime_Date *o) Return the year, as a positive int. int PyDateTime_GET_MONTH(PyDateTime_Date *o) Return the month, as an int from 1 through 12. int PyDateTime_GET_DAY(PyDateTime_Date *o) Return the day, as an int from 1 through 31. Macros to extract fields from datetime objects. The argument must be an instance of "PyDateTime_DateTime", including subclasses. The argument must not be "NULL", and the type is not checked: int PyDateTime_DATE_GET_HOUR(PyDateTime_DateTime *o) Return the hour, as an int from 0 through 23. int PyDateTime_DATE_GET_MINUTE(PyDateTime_DateTime *o) Return the minute, as an int from 0 through 59. int PyDateTime_DATE_GET_SECOND(PyDateTime_DateTime *o) Return the second, as an int from 0 through 59. int PyDateTime_DATE_GET_MICROSECOND(PyDateTime_DateTime *o) Return the microsecond, as an int from 0 through 999999. int PyDateTime_DATE_GET_FOLD(PyDateTime_DateTime *o) Return the fold, as an int from 0 through 1. Added in version 3.6. PyObject *PyDateTime_DATE_GET_TZINFO(PyDateTime_DateTime *o) Return the tzinfo (which may be "None"). Added in version 3.10. Macros to extract fields from time objects. The argument must be an instance of "PyDateTime_Time", including subclasses. The argument must not be "NULL", and the type is not checked: int PyDateTime_TIME_GET_HOUR(PyDateTime_Time *o) Return the hour, as an int from 0 through 23. int PyDateTime_TIME_GET_MINUTE(PyDateTime_Time *o) Return the minute, as an int from 0 through 59. int PyDateTime_TIME_GET_SECOND(PyDateTime_Time *o) Return the second, as an int from 0 through 59. int PyDateTime_TIME_GET_MICROSECOND(PyDateTime_Time *o) Return the microsecond, as an int from 0 through 999999. int PyDateTime_TIME_GET_FOLD(PyDateTime_Time *o) Return the fold, as an int from 0 through 1. Added in version 3.6. PyObject *PyDateTime_TIME_GET_TZINFO(PyDateTime_Time *o) Return the tzinfo (which may be "None"). Added in version 3.10. Macros to extract fields from time delta objects. The argument must be an instance of "PyDateTime_Delta", including subclasses. The argument must not be "NULL", and the type is not checked: int PyDateTime_DELTA_GET_DAYS(PyDateTime_Delta *o) Return the number of days, as an int from -999999999 to 999999999. Added in version 3.3. int PyDateTime_DELTA_GET_SECONDS(PyDateTime_Delta *o) Return the number of seconds, as an int from 0 through 86399. Added in version 3.3. int PyDateTime_DELTA_GET_MICROSECONDS(PyDateTime_Delta *o) Return the number of microseconds, as an int from 0 through 999999. Added in version 3.3. Macros for the convenience of modules implementing the DB API: PyObject *PyDateTime_FromTimestamp(PyObject *args) *Return value: New reference.* Create and return a new "datetime.datetime" object given an argument tuple suitable for passing to "datetime.datetime.fromtimestamp()". PyObject *PyDate_FromTimestamp(PyObject *args) *Return value: New reference.* Create and return a new "datetime.date" object given an argument tuple suitable for passing to "datetime.date.fromtimestamp()".