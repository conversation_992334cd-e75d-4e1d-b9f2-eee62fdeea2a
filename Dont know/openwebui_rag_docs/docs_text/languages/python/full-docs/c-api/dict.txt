Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > dict.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > dict.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > dict.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > dict.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > dict.txt Dictionary Objects ****************** type PyDictObject This subtype of "PyObject" represents a Python dictionary object. PyTypeObject PyDict_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python dictionary type. This is the same object as "dict" in the Python layer. int PyDict_Check(PyObject *p) Return true if *p* is a dict object or an instance of a subtype of the dict type. This function always succeeds. int PyDict_CheckExact(PyObject *p) Return true if *p* is a dict object, but not an instance of a subtype of the dict type. This function always succeeds. PyObject *PyDict_New() *Return value: New reference.** Part of the Stable ABI.* Return a new empty dictionary, or "NULL" on failure. PyObject *PyDictProxy_New(PyObject *mapping) *Return value: New reference.** Part of the Stable ABI.* Return a "types.MappingProxyType" object for a mapping which enforces read-only behavior. This is normally used to create a view to prevent modification of the dictionary for non-dynamic class types. void PyDict_Clear(PyObject *p) * Part of the Stable ABI.* Empty an existing dictionary of all key-value pairs. int PyDict_Contains(PyObject *p, PyObject *key) * Part of the Stable ABI.* Determine if dictionary *p* contains *key*. If an item in *p* is matches *key*, return "1", otherwise return "0". On error, return "-1". This is equivalent to the Python expression "key in p". int PyDict_ContainsString(PyObject *p, const char *key) This is the same as "PyDict_Contains()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Added in version 3.13. PyObject *PyDict_Copy(PyObject *p) *Return value: New reference.** Part of the Stable ABI.* Return a new dictionary that contains the same key-value pairs as *p*. int PyDict_SetItem(PyObject *p, PyObject *key, PyObject *val) * Part of the Stable ABI.* Insert *val* into the dictionary *p* with a key of *key*. *key* must be *hashable*; if it isn't, "TypeError" will be raised. Return "0" on success or "-1" on failure. This function *does not* steal a reference to *val*. int PyDict_SetItemString(PyObject *p, const char *key, PyObject *val) * Part of the Stable ABI.* This is the same as "PyDict_SetItem()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. int PyDict_DelItem(PyObject *p, PyObject *key) * Part of the Stable ABI.* Remove the entry in dictionary *p* with key *key*. *key* must be *hashable*; if it isn't, "TypeError" is raised. If *key* is not in the dictionary, "KeyError" is raised. Return "0" on success or "-1" on failure. int PyDict_DelItemString(PyObject *p, const char *key) * Part of the Stable ABI.* This is the same as "PyDict_DelItem()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. int PyDict_GetItemRef(PyObject *p, PyObject *key, PyObject **result) * Part of the Stable ABI since version 3.13.* Return a new *strong reference* to the object from dictionary *p* which has a key *key*: * If the key is present, set **result* to a new *strong reference* to the value and return "1". * If the key is missing, set **result* to "NULL" and return "0". * On error, raise an exception and return "-1". Added in version 3.13. See also the "PyObject_GetItem()" function. PyObject *PyDict_GetItem(PyObject *p, PyObject *key) *Return value: Borrowed reference.** Part of the Stable ABI.* Return a *borrowed reference* to the object from dictionary *p* which has a key *key*. Return "NULL" if the key *key* is missing *without* setting an exception. Note: Exceptions that occur while this calls "__hash__()" and "__eq__()" methods are silently ignored. Prefer the "PyDict_GetItemWithError()" function instead. Changed in version 3.10: Calling this API without *GIL* held had been allowed for historical reason. It is no longer allowed. PyObject *PyDict_GetItemWithError(PyObject *p, PyObject *key) *Return value: Borrowed reference.** Part of the Stable ABI.* Variant of "PyDict_GetItem()" that does not suppress exceptions. Return "NULL" **with** an exception set if an exception occurred. Return "NULL" **without** an exception set if the key wasn't present. PyObject *PyDict_GetItemString(PyObject *p, const char *key) *Return value: Borrowed reference.** Part of the Stable ABI.* This is the same as "PyDict_GetItem()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Note: Exceptions that occur while this calls "__hash__()" and "__eq__()" methods or while creating the temporary "str" object are silently ignored. Prefer using the "PyDict_GetItemWithError()" function with your own "PyUnicode_FromString()" *key* instead. int PyDict_GetItemStringRef(PyObject *p, const char *key, PyObject **result) * Part of the Stable ABI since version 3.13.* Similar to "PyDict_GetItemRef()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Added in version 3.13. PyObject *PyDict_SetDefault(PyObject *p, PyObject *key, PyObject *defaultobj) *Return value: Borrowed reference.* This is the same as the Python-level "dict.setdefault()". If present, it returns the value corresponding to *key* from the dictionary *p*. If the key is not in the dict, it is inserted with value *defaultobj* and *defaultobj* is returned. This function evaluates the hash function of *key* only once, instead of evaluating it independently for the lookup and the insertion. Added in version 3.4. int PyDict_SetDefaultRef(PyObject *p, PyObject *key, PyObject *default_value, PyObject **result) Inserts *default_value* into the dictionary *p* with a key of *key* if the key is not already present in the dictionary. If *result* is not "NULL", then **result* is set to a *strong reference* to either *default_value*, if the key was not present, or the existing value, if *key* was already present in the dictionary. Returns "1" if the key was present and *default_value* was not inserted, or "0" if the key was not present and *default_value* was inserted. On failure, returns "-1", sets an exception, and sets "*result" to "NULL". For clarity: if you have a strong reference to *default_value* before calling this function, then after it returns, you hold a strong reference to both *default_value* and **result* (if it's not "NULL"). These may refer to the same object: in that case you hold two separate references to it. Added in version 3.13. int PyDict_Pop(PyObject *p, PyObject *key, PyObject **result) Remove *key* from dictionary *p* and optionally return the removed value. Do not raise "KeyError" if the key missing. * If the key is present, set **result* to a new reference to the removed value if *result* is not "NULL", and return "1". * If the key is missing, set **result* to "NULL" if *result* is not "NULL", and return "0". * On error, raise an exception and return "-1". Similar to "dict.pop()", but without the default value and not raising "KeyError" if the key missing. Added in version 3.13. int PyDict_PopString(PyObject *p, const char *key, PyObject **result) Similar to "PyDict_Pop()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Added in version 3.13. PyObject *PyDict_Items(PyObject *p) *Return value: New reference.** Part of the Stable ABI.* Return a "PyListObject" containing all the items from the dictionary. PyObject *PyDict_Keys(PyObject *p) *Return value: New reference.** Part of the Stable ABI.* Return a "PyListObject" containing all the keys from the dictionary. PyObject *PyDict_Values(PyObject *p) *Return value: New reference.** Part of the Stable ABI.* Return a "PyListObject" containing all the values from the dictionary *p*. Py_ssize_t PyDict_Size(PyObject *p) * Part of the Stable ABI.* Return the number of items in the dictionary. This is equivalent to "len(p)" on a dictionary. int PyDict_Next(PyObject *p, Py_ssize_t *ppos, PyObject **pkey, PyObject **pvalue) * Part of the Stable ABI.* Iterate over all key-value pairs in the dictionary *p*. The "Py_ssize_t" referred to by *ppos* must be initialized to "0" prior to the first call to this function to start the iteration; the function returns true for each pair in the dictionary, and false once all pairs have been reported. The parameters *pkey* and *pvalue* should either point to PyObject* variables that will be filled in with each key and value, respectively, or may be "NULL". Any references returned through them are borrowed. *ppos* should not be altered during iteration. Its value represents offsets within the internal dictionary structure, and since the structure is sparse, the offsets are not consecutive. For example: PyObject *key, *value; Py_ssize_t pos = 0; while (PyDict_Next(self->dict, &pos, &key, &value)) { /* do something interesting with the values... */ ... } The dictionary *p* should not be mutated during iteration. It is safe to modify the values of the keys as you iterate over the dictionary, but only so long as the set of keys does not change. For example: PyObject *key, *value; Py_ssize_t pos = 0; while (PyDict_Next(self->dict, &pos, &key, &value)) { long i = PyLong_AsLong(value); if (i == -1 && PyErr_Occurred()) { return -1; } PyObject *o = PyLong_FromLong(i + 1); if (o == NULL) return -1; if (PyDict_SetItem(self->dict, key, o) < 0) { Py_DECREF(o); return -1; } Py_DECREF(o); } The function is not thread-safe in the *free-threaded* build without external synchronization. You can use "Py_BEGIN_CRITICAL_SECTION" to lock the dictionary while iterating over it: Py_BEGIN_CRITICAL_SECTION(self->dict); while (PyDict_Next(self->dict, &pos, &key, &value)) { ... } Py_END_CRITICAL_SECTION(); int PyDict_Merge(PyObject *a, PyObject *b, int override) * Part of the Stable ABI.* Iterate over mapping object *b* adding key-value pairs to dictionary *a*. *b* may be a dictionary, or any object supporting "PyMapping_Keys()" and "PyObject_GetItem()". If *override* is true, existing pairs in *a* will be replaced if a matching key is found in *b*, otherwise pairs will only be added if there is not a matching key in *a*. Return "0" on success or "-1" if an exception was raised. int PyDict_Update(PyObject *a, PyObject *b) * Part of the Stable ABI.* This is the same as "PyDict_Merge(a, b, 1)" in C, and is similar to "a.update(b)" in Python except that "PyDict_Update()" doesn't fall back to the iterating over a sequence of key value pairs if the second argument has no "keys" attribute. Return "0" on success or "-1" if an exception was raised. int PyDict_MergeFromSeq2(PyObject *a, PyObject *seq2, int override) * Part of the Stable ABI.* Update or merge into dictionary *a*, from the key-value pairs in *seq2*. *seq2* must be an iterable object producing iterable objects of length 2, viewed as key-value pairs. In case of duplicate keys, the last wins if *override* is true, else the first wins. Return "0" on success or "-1" if an exception was raised. Equivalent Python (except for the return value): def PyDict_MergeFromSeq2(a, seq2, override): for key, value in seq2: if override or key not in a: a[key] = value int PyDict_AddWatcher(PyDict_WatchCallback callback) Register *callback* as a dictionary watcher. Return a non-negative integer id which must be passed to future calls to "PyDict_Watch()". In case of error (e.g. no more watcher IDs available), return "-1" and set an exception. Added in version 3.12. int PyDict_ClearWatcher(int watcher_id) Clear watcher identified by *watcher_id* previously returned from "PyDict_AddWatcher()". Return "0" on success, "-1" on error (e.g. if the given *watcher_id* was never registered.) Added in version 3.12. int PyDict_Watch(int watcher_id, PyObject *dict) Mark dictionary *dict* as watched. The callback granted *watcher_id* by "PyDict_AddWatcher()" will be called when *dict* is modified or deallocated. Return "0" on success or "-1" on error. Added in version 3.12. int PyDict_Unwatch(int watcher_id, PyObject *dict) Mark dictionary *dict* as no longer watched. The callback granted *watcher_id* by "PyDict_AddWatcher()" will no longer be called when *dict* is modified or deallocated. The dict must previously have been watched by this watcher. Return "0" on success or "-1" on error. Added in version 3.12. type PyDict_WatchEvent Enumeration of possible dictionary watcher events: "PyDict_EVENT_ADDED", "PyDict_EVENT_MODIFIED", "PyDict_EVENT_DELETED", "PyDict_EVENT_CLONED", "PyDict_EVENT_CLEARED", or "PyDict_EVENT_DEALLOCATED". Added in version 3.12. typedef int (*PyDict_WatchCallback)(PyDict_WatchEvent event, PyObject *dict, PyObject *key, PyObject *new_value) Type of a dict watcher callback function. If *event* is "PyDict_EVENT_CLEARED" or "PyDict_EVENT_DEALLOCATED", both *key* and *new_value* will be "NULL". If *event* is "PyDict_EVENT_ADDED" or "PyDict_EVENT_MODIFIED", *new_value* will be the new value for *key*. If *event* is "PyDict_EVENT_DELETED", *key* is being deleted from the dictionary and *new_value* will be "NULL". "PyDict_EVENT_CLONED" occurs when *dict* was previously empty and another dict is merged into it. To maintain efficiency of this operation, per-key "PyDict_EVENT_ADDED" events are not issued in this case; instead a single "PyDict_EVENT_CLONED" is issued, and *key* will be the source dictionary. The callback may inspect but must not modify *dict*; doing so could have unpredictable effects, including infinite recursion. Do not trigger Python code execution in the callback, as it could modify the dict as a side effect. If *event* is "PyDict_EVENT_DEALLOCATED", taking a new reference in the callback to the about-to-be-destroyed dictionary will resurrect it and prevent it from being freed at this time. When the resurrected object is destroyed later, any watcher callbacks active at that time will be called again. Callbacks occur before the notified modification to *dict* takes place, so the prior state of *dict* can be inspected. If the callback sets an exception, it must return "-1"; this exception will be printed as an unraisable exception using "PyErr_WriteUnraisable()". Otherwise it should return "0". There may already be a pending exception set on entry to the callback. In this case, the callback should return "0" with the same exception still set. This means the callback may not call any other API that can set an exception unless it saves and clears the exception state first, and restores it before returning. Added in version 3.12.