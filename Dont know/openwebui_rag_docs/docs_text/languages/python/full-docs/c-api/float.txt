Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > float.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > float.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > float.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > float.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > float.txt Floating-Point Objects ********************** type PyFloatObject This subtype of "PyObject" represents a Python floating-point object. PyTypeObject PyFloat_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python floating- point type. This is the same object as "float" in the Python layer. int PyFloat_Check(PyObject *p) Return true if its argument is a "PyFloatObject" or a subtype of "PyFloatObject". This function always succeeds. int PyFloat_CheckExact(PyObject *p) Return true if its argument is a "PyFloatObject", but not a subtype of "PyFloatObject". This function always succeeds. PyObject *PyFloat_FromString(PyObject *str) *Return value: New reference.** Part of the Stable ABI.* Create a "PyFloatObject" object based on the string value in *str*, or "NULL" on failure. PyObject *PyFloat_FromDouble(double v) *Return value: New reference.** Part of the Stable ABI.* Create a "PyFloatObject" object from *v*, or "NULL" on failure. double PyFloat_AsDouble(PyObject *pyfloat) * Part of the Stable ABI.* Return a C double representation of the contents of *pyfloat*. If *pyfloat* is not a Python floating-point object but has a "__float__()" method, this method will first be called to convert *pyfloat* into a float. If "__float__()" is not defined then it falls back to "__index__()". This method returns "-1.0" upon failure, so one should call "PyErr_Occurred()" to check for errors. Changed in version 3.8: Use "__index__()" if available. double PyFloat_AS_DOUBLE(PyObject *pyfloat) Return a C double representation of the contents of *pyfloat*, but without error checking. PyObject *PyFloat_GetInfo(void) *Return value: New reference.** Part of the Stable ABI.* Return a structseq instance which contains information about the precision, minimum and maximum values of a float. It's a thin wrapper around the header file "float.h". double PyFloat_GetMax() * Part of the Stable ABI.* Return the maximum representable finite float *DBL_MAX* as C double. double PyFloat_GetMin() * Part of the Stable ABI.* Return the minimum normalized positive float *DBL_MIN* as C double. Pack and Unpack functions ========================= The pack and unpack functions provide an efficient platform- independent way to store floating-point values as byte strings. The Pack routines produce a bytes string from a C double, and the Unpack routines produce a C double from such a bytes string. The suffix (2, 4 or 8) specifies the number of bytes in the bytes string. On platforms that appear to use IEEE 754 formats these functions work by copying bits. On other platforms, the 2-byte format is identical to the IEEE 754 binary16 half-precision format, the 4-byte format (32-bit) is identical to the IEEE 754 binary32 single precision format, and the 8-byte format to the IEEE 754 binary64 double precision format, although the packing of INFs and NaNs (if such things exist on the platform) isn't handled correctly, and attempting to unpack a bytes string containing an IEEE INF or NaN will raise an exception. On non-IEEE platforms with more precision, or larger dynamic range, than IEEE 754 supports, not all values can be packed; on non-IEEE platforms with less precision, or smaller dynamic range, not all values can be unpacked. What happens in such cases is partly accidental (alas). Added in version 3.11. Pack functions -------------- The pack routines write 2, 4 or 8 bytes, starting at *p*. *le* is an int argument, non-zero if you want the bytes string in little-endian format (exponent last, at "p+1", "p+3", or "p+6" "p+7"), zero if you want big-endian format (exponent first, at *p*). The "PY_BIG_ENDIAN" constant can be used to use the native endian: it is equal to "1" on big endian processor, or "0" on little endian processor. Return value: "0" if all is OK, "-1" if error (and an exception is set, most likely "OverflowError"). There are two problems on non-IEEE platforms: * What this does is undefined if *x* is a NaN or infinity. * "-0.0" and "+0.0" produce the same bytes string. int PyFloat_Pack2(double x, unsigned char *p, int le) Pack a C double as the IEEE 754 binary16 half-precision format. int PyFloat_Pack4(double x, unsigned char *p, int le) Pack a C double as the IEEE 754 binary32 single precision format. int PyFloat_Pack8(double x, unsigned char *p, int le) Pack a C double as the IEEE 754 binary64 double precision format. Unpack functions ---------------- The unpack routines read 2, 4 or 8 bytes, starting at *p*. *le* is an int argument, non-zero if the bytes string is in little-endian format (exponent last, at "p+1", "p+3" or "p+6" and "p+7"), zero if big- endian (exponent first, at *p*). The "PY_BIG_ENDIAN" constant can be used to use the native endian: it is equal to "1" on big endian processor, or "0" on little endian processor. Return value: The unpacked double. On error, this is "-1.0" and "PyErr_Occurred()" is true (and an exception is set, most likely "OverflowError"). Note that on a non-IEEE platform this will refuse to unpack a bytes string that represents a NaN or infinity. double PyFloat_Unpack2(const unsigned char *p, int le) Unpack the IEEE 754 binary16 half-precision format as a C double. double PyFloat_Unpack4(const unsigned char *p, int le) Unpack the IEEE 754 binary32 single precision format as a C double. double PyFloat_Unpack8(const unsigned char *p, int le) Unpack the IEEE 754 binary64 double precision format as a C double.