Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > frame.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > frame.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > frame.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > frame.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > frame.txt Frame Objects ************* type PyFrameObject * Part of the Limited API (as an opaque struct).* The C structure of the objects used to describe frame objects. There are no public members in this structure. Changed in version 3.11: The members of this structure were removed from the public C API. Refer to the What's New entry for details. The "PyEval_GetFrame()" and "PyThreadState_GetFrame()" functions can be used to get a frame object. See also Reflection. PyTypeObject PyFrame_Type The type of frame objects. It is the same object as "types.FrameType" in the Python layer. Changed in version 3.11: Previously, this type was only available after including "<frameobject.h>". int PyFrame_Check(PyObject *obj) Return non-zero if *obj* is a frame object. Changed in version 3.11: Previously, this function was only available after including "<frameobject.h>". PyFrameObject *PyFrame_GetBack(PyFrameObject *frame) *Return value: New reference.* Get the *frame* next outer frame. Return a *strong reference*, or "NULL" if *frame* has no outer frame. Added in version 3.9. PyObject *PyFrame_GetBuiltins(PyFrameObject *frame) *Return value: New reference.* Get the *frame*'s "f_builtins" attribute. Return a *strong reference*. The result cannot be "NULL". Added in version 3.11. PyCodeObject *PyFrame_GetCode(PyFrameObject *frame) *Return value: New reference.** Part of the Stable ABI since version 3.10.* Get the *frame* code. Return a *strong reference*. The result (frame code) cannot be "NULL". Added in version 3.9. PyObject *PyFrame_GetGenerator(PyFrameObject *frame) *Return value: New reference.* Get the generator, coroutine, or async generator that owns this frame, or "NULL" if this frame is not owned by a generator. Does not raise an exception, even if the return value is "NULL". Return a *strong reference*, or "NULL". Added in version 3.11. PyObject *PyFrame_GetGlobals(PyFrameObject *frame) *Return value: New reference.* Get the *frame*'s "f_globals" attribute. Return a *strong reference*. The result cannot be "NULL". Added in version 3.11. int PyFrame_GetLasti(PyFrameObject *frame) Get the *frame*'s "f_lasti" attribute. Returns -1 if "frame.f_lasti" is "None". Added in version 3.11. PyObject *PyFrame_GetVar(PyFrameObject *frame, PyObject *name) *Return value: New reference.* Get the variable *name* of *frame*. * Return a *strong reference* to the variable value on success. * Raise "NameError" and return "NULL" if the variable does not exist. * Raise an exception and return "NULL" on error. *name* type must be a "str". Added in version 3.12. PyObject *PyFrame_GetVarString(PyFrameObject *frame, const char *name) *Return value: New reference.* Similar to "PyFrame_GetVar()", but the variable name is a C string encoded in UTF-8. Added in version 3.12. PyObject *PyFrame_GetLocals(PyFrameObject *frame) *Return value: New reference.* Get the *frame*'s "f_locals" attribute. If the frame refers to an *optimized scope*, this returns a write-through proxy object that allows modifying the locals. In all other cases (classes, modules, "exec()", "eval()") it returns the mapping representing the frame locals directly (as described for "locals()"). Return a *strong reference*. Added in version 3.11. Changed in version 3.13: As part of **PEP 667**, return an instance of "PyFrameLocalsProxy_Type". int PyFrame_GetLineNumber(PyFrameObject *frame) * Part of the Stable ABI since version 3.10.* Return the line number that *frame* is currently executing. Frame Locals Proxies ==================== Added in version 3.13. The "f_locals" attribute on a frame object is an instance of a "frame- locals proxy". The proxy object exposes a write-through view of the underlying locals dictionary for the frame. This ensures that the variables exposed by "f_locals" are always up to date with the live local variables in the frame itself. See **PEP 667** for more information. PyTypeObject PyFrameLocalsProxy_Type The type of frame "locals()" proxy objects. int PyFrameLocalsProxy_Check(PyObject *obj) Return non-zero if *obj* is a frame "locals()" proxy. Internal Frames =============== Unless using **PEP 523**, you will not need this. struct _PyInterpreterFrame The interpreter's internal frame representation. Added in version 3.11. PyObject *PyUnstable_InterpreterFrame_GetCode(struct _PyInterpreterFrame *frame); *This is Unstable API. It may change without warning in minor releases.* Return a *strong reference* to the code object for the frame. Added in version 3.12. int PyUnstable_InterpreterFrame_GetLasti(struct _PyInterpreterFrame *frame); *This is Unstable API. It may change without warning in minor releases.* Return the byte offset into the last executed instruction. Added in version 3.12. int PyUnstable_InterpreterFrame_GetLine(struct _PyInterpreterFrame *frame); *This is Unstable API. It may change without warning in minor releases.* Return the currently executing line number, or -1 if there is no line number. Added in version 3.12.