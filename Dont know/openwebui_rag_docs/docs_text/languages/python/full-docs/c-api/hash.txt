Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > hash.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > hash.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > hash.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > hash.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > hash.txt PyHash API ********** See also the "PyTypeObject.tp_hash" member and Hashing of numeric types. type Py_hash_t Hash value type: signed integer. Added in version 3.2. type Py_uhash_t Hash value type: unsigned integer. Added in version 3.2. PyHASH_MODULUS The Mersenne prime "P = 2**n -1", used for numeric hash scheme. Added in version 3.13. PyHASH_BITS The exponent "n" of "P" in "PyHASH_MODULUS". Added in version 3.13. PyHASH_MULTIPLIER Prime multiplier used in string and various other hashes. Added in version 3.13. PyHASH_INF The hash value returned for a positive infinity. Added in version 3.13. PyHASH_IMAG The multiplier used for the imaginary part of a complex number. Added in version 3.13. type PyHash_FuncDef Hash function definition used by "PyHash_GetFuncDef()". const char *name Hash function name (UTF-8 encoded string). const int hash_bits Internal size of the hash value in bits. const int seed_bits Size of seed input in bits. Added in version 3.4. PyHash_FuncDef *PyHash_GetFuncDef(void) Get the hash function definition. See also: **PEP 456** "Secure and interchangeable hash algorithm". Added in version 3.4. Py_hash_t Py_HashPointer(const void *ptr) Hash a pointer value: process the pointer value as an integer (cast it to "uintptr_t" internally). The pointer is not dereferenced. The function cannot fail: it cannot return "-1". Added in version 3.13. Py_hash_t PyObject_GenericHash(PyObject *obj) Generic hashing function that is meant to be put into a type object's "tp_hash" slot. Its result only depends on the object's identity. **CPython implementation detail:** In CPython, it is equivalent to "Py_HashPointer()". Added in version 3.13.