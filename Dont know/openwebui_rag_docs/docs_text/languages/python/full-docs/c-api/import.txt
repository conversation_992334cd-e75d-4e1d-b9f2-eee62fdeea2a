Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > import.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > import.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > import.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > import.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > import.txt Importing Modules ***************** PyObject *PyImport_ImportModule(const char *name) *Return value: New reference.** Part of the Stable ABI.* This is a wrapper around "PyImport_Import()" which takes a const char* as an argument instead of a PyObject*. PyObject *PyImport_ImportModuleNoBlock(const char *name) *Return value: New reference.** Part of the Stable ABI.* This function is a deprecated alias of "PyImport_ImportModule()". Changed in version 3.3: This function used to fail immediately when the import lock was held by another thread. In Python 3.3 though, the locking scheme switched to per-module locks for most purposes, so this function's special behaviour isn't needed anymore. Deprecated since version 3.13, will be removed in version 3.15: Use "PyImport_ImportModule()" instead. PyObject *PyImport_ImportModuleEx(const char *name, PyObject *globals, PyObject *locals, PyObject *fromlist) *Return value: New reference.* Import a module. This is best described by referring to the built- in Python function "__import__()". The return value is a new reference to the imported module or top- level package, or "NULL" with an exception set on failure. Like for "__import__()", the return value when a submodule of a package was requested is normally the top-level package, unless a non-empty *fromlist* was given. Failing imports remove incomplete module objects, like with "PyImport_ImportModule()". PyObject *PyImport_ImportModuleLevelObject(PyObject *name, PyObject *globals, PyObject *locals, PyObject *fromlist, int level) *Return value: New reference.** Part of the Stable ABI since version 3.7.* Import a module. This is best described by referring to the built- in Python function "__import__()", as the standard "__import__()" function calls this function directly. The return value is a new reference to the imported module or top- level package, or "NULL" with an exception set on failure. Like for "__import__()", the return value when a submodule of a package was requested is normally the top-level package, unless a non-empty *fromlist* was given. Added in version 3.3. PyObject *PyImport_ImportModuleLevel(const char *name, PyObject *globals, PyObject *locals, PyObject *fromlist, int level) *Return value: New reference.** Part of the Stable ABI.* Similar to "PyImport_ImportModuleLevelObject()", but the name is a UTF-8 encoded string instead of a Unicode object. Changed in version 3.3: Negative values for *level* are no longer accepted. PyObject *PyImport_Import(PyObject *name) *Return value: New reference.** Part of the Stable ABI.* This is a higher-level interface that calls the current "import hook function" (with an explicit *level* of 0, meaning absolute import). It invokes the "__import__()" function from the "__builtins__" of the current globals. This means that the import is done using whatever import hooks are installed in the current environment. This function always uses absolute imports. PyObject *PyImport_ReloadModule(PyObject *m) *Return value: New reference.** Part of the Stable ABI.* Reload a module. Return a new reference to the reloaded module, or "NULL" with an exception set on failure (the module still exists in this case). PyObject *PyImport_AddModuleRef(const char *name) *Return value: New reference.** Part of the Stable ABI since version 3.13.* Return the module object corresponding to a module name. The *name* argument may be of the form "package.module". First check the modules dictionary if there's one there, and if not, create a new one and insert it in the modules dictionary. Return a *strong reference* to the module on success. Return "NULL" with an exception set on failure. The module name *name* is decoded from UTF-8. This function does not load or import the module; if the module wasn't already loaded, you will get an empty module object. Use "PyImport_ImportModule()" or one of its variants to import a module. Package structures implied by a dotted name for *name* are not created if not already present. Added in version 3.13. PyObject *PyImport_AddModuleObject(PyObject *name) *Return value: Borrowed reference.** Part of the Stable ABI since version 3.7.* Similar to "PyImport_AddModuleRef()", but return a *borrowed reference* and *name* is a Python "str" object. Added in version 3.3. PyObject *PyImport_AddModule(const char *name) *Return value: Borrowed reference.** Part of the Stable ABI.* Similar to "PyImport_AddModuleRef()", but return a *borrowed reference*. PyObject *PyImport_ExecCodeModule(const char *name, PyObject *co) *Return value: New reference.** Part of the Stable ABI.* Given a module name (possibly of the form "package.module") and a code object read from a Python bytecode file or obtained from the built-in function "compile()", load the module. Return a new reference to the module object, or "NULL" with an exception set if an error occurred. *name* is removed from "sys.modules" in error cases, even if *name* was already in "sys.modules" on entry to "PyImport_ExecCodeModule()". Leaving incompletely initialized modules in "sys.modules" is dangerous, as imports of such modules have no way to know that the module object is an unknown (and probably damaged with respect to the module author's intents) state. The module's "__spec__" and "__loader__" will be set, if not set already, with the appropriate values. The spec's loader will be set to the module's "__loader__" (if set) and to an instance of "SourceFileLoader" otherwise. The module's "__file__" attribute will be set to the code object's "co_filename". If applicable, "__cached__" will also be set. This function will reload the module if it was already imported. See "PyImport_ReloadModule()" for the intended way to reload a module. If *name* points to a dotted name of the form "package.module", any package structures not already created will still not be created. See also "PyImport_ExecCodeModuleEx()" and "PyImport_ExecCodeModuleWithPathnames()". Changed in version 3.12: The setting of "__cached__" and "__loader__" is deprecated. See "ModuleSpec" for alternatives. PyObject *PyImport_ExecCodeModuleEx(const char *name, PyObject *co, const char *pathname) *Return value: New reference.** Part of the Stable ABI.* Like "PyImport_ExecCodeModule()", but the "__file__" attribute of the module object is set to *pathname* if it is non-"NULL". See also "PyImport_ExecCodeModuleWithPathnames()". PyObject *PyImport_ExecCodeModuleObject(PyObject *name, PyObject *co, PyObject *pathname, PyObject *cpathname) *Return value: New reference.** Part of the Stable ABI since version 3.7.* Like "PyImport_ExecCodeModuleEx()", but the "__cached__" attribute of the module object is set to *cpathname* if it is non-"NULL". Of the three functions, this is the preferred one to use. Added in version 3.3. Changed in version 3.12: Setting "__cached__" is deprecated. See "ModuleSpec" for alternatives. PyObject *PyImport_ExecCodeModuleWithPathnames(const char *name, PyObject *co, const char *pathname, const char *cpathname) *Return value: New reference.** Part of the Stable ABI.* Like "PyImport_ExecCodeModuleObject()", but *name*, *pathname* and *cpathname* are UTF-8 encoded strings. Attempts are also made to figure out what the value for *pathname* should be from *cpathname* if the former is set to "NULL". Added in version 3.2. Changed in version 3.3: Uses "imp.source_from_cache()" in calculating the source path if only the bytecode path is provided. Changed in version 3.12: No longer uses the removed "imp" module. long PyImport_GetMagicNumber() * Part of the Stable ABI.* Return the magic number for Python bytecode files (a.k.a. ".pyc" file). The magic number should be present in the first four bytes of the bytecode file, in little-endian byte order. Returns "-1" on error. Changed in version 3.3: Return value of "-1" upon failure. const char *PyImport_GetMagicTag() * Part of the Stable ABI.* Return the magic tag string for **PEP 3147** format Python bytecode file names. Keep in mind that the value at "sys.implementation.cache_tag" is authoritative and should be used instead of this function. Added in version 3.2. PyObject *PyImport_GetModuleDict() *Return value: Borrowed reference.** Part of the Stable ABI.* Return the dictionary used for the module administration (a.k.a. "sys.modules"). Note that this is a per-interpreter variable. PyObject *PyImport_GetModule(PyObject *name) *Return value: New reference.** Part of the Stable ABI since version 3.8.* Return the already imported module with the given name. If the module has not been imported yet then returns "NULL" but does not set an error. Returns "NULL" and sets an error if the lookup failed. Added in version 3.7. PyObject *PyImport_GetImporter(PyObject *path) *Return value: New reference.** Part of the Stable ABI.* Return a finder object for a "sys.path"/"pkg.__path__" item *path*, possibly by fetching it from the "sys.path_importer_cache" dict. If it wasn't yet cached, traverse "sys.path_hooks" until a hook is found that can handle the path item. Return "None" if no hook could; this tells our caller that the *path based finder* could not find a finder for this path item. Cache the result in "sys.path_importer_cache". Return a new reference to the finder object. int PyImport_ImportFrozenModuleObject(PyObject *name) * Part of the Stable ABI since version 3.7.* Load a frozen module named *name*. Return "1" for success, "0" if the module is not found, and "-1" with an exception set if the initialization failed. To access the imported module on a successful load, use "PyImport_ImportModule()". (Note the misnomer --- this function would reload the module if it was already imported.) Added in version 3.3. Changed in version 3.4: The "__file__" attribute is no longer set on the module. int PyImport_ImportFrozenModule(const char *name) * Part of the Stable ABI.* Similar to "PyImport_ImportFrozenModuleObject()", but the name is a UTF-8 encoded string instead of a Unicode object. struct _frozen This is the structure type definition for frozen module descriptors, as generated by the **freeze** utility (see "Tools/freeze/" in the Python source distribution). Its definition, found in "Include/import.h", is: struct _frozen { const char *name; const unsigned char *code; int size; bool is_package; }; Changed in version 3.11: The new "is_package" field indicates whether the module is a package or not. This replaces setting the "size" field to a negative value. const struct _frozen *PyImport_FrozenModules This pointer is initialized to point to an array of "_frozen" records, terminated by one whose members are all "NULL" or zero. When a frozen module is imported, it is searched in this table. Third-party code could play tricks with this to provide a dynamically created collection of frozen modules. int PyImport_AppendInittab(const char *name, PyObject *(*initfunc)(void)) * Part of the Stable ABI.* Add a single module to the existing table of built-in modules. This is a convenience wrapper around "PyImport_ExtendInittab()", returning "-1" if the table could not be extended. The new module can be imported by the name *name*, and uses the function *initfunc* as the initialization function called on the first attempted import. This should be called before "Py_Initialize()". struct _inittab Structure describing a single entry in the list of built-in modules. Programs which embed Python may use an array of these structures in conjunction with "PyImport_ExtendInittab()" to provide additional built-in modules. The structure consists of two members: const char *name The module name, as an ASCII encoded string. PyObject *(*initfunc)(void) Initialization function for a module built into the interpreter. int PyImport_ExtendInittab(struct _inittab *newtab) Add a collection of modules to the table of built-in modules. The *newtab* array must end with a sentinel entry which contains "NULL" for the "name" field; failure to provide the sentinel value can result in a memory fault. Returns "0" on success or "-1" if insufficient memory could be allocated to extend the internal table. In the event of failure, no modules are added to the internal table. This must be called before "Py_Initialize()". If Python is initialized multiple times, "PyImport_AppendInittab()" or "PyImport_ExtendInittab()" must be called before each Python initialization.