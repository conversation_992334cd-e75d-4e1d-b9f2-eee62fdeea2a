Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > list.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > list.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > list.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > list.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > list.txt List Objects ************ type PyListObject This subtype of "PyObject" represents a Python list object. PyTypeObject PyList_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python list type. This is the same object as "list" in the Python layer. int PyList_Check(PyObject *p) Return true if *p* is a list object or an instance of a subtype of the list type. This function always succeeds. int PyList_CheckExact(PyObject *p) Return true if *p* is a list object, but not an instance of a subtype of the list type. This function always succeeds. PyObject *PyList_New(Py_ssize_t len) *Return value: New reference.** Part of the Stable ABI.* Return a new list of length *len* on success, or "NULL" on failure. Note: If *len* is greater than zero, the returned list object's items are set to "NULL". Thus you cannot use abstract API functions such as "PySequence_SetItem()" or expose the object to Python code before setting all items to a real object with "PyList_SetItem()" or "PyList_SET_ITEM()". The following APIs are safe APIs before the list is fully initialized: "PyList_SetItem()" and "PyList_SET_ITEM()". Py_ssize_t PyList_Size(PyObject *list) * Part of the Stable ABI.* Return the length of the list object in *list*; this is equivalent to "len(list)" on a list object. Py_ssize_t PyList_GET_SIZE(PyObject *list) Similar to "PyList_Size()", but without error checking. PyObject *PyList_GetItemRef(PyObject *list, Py_ssize_t index) *Return value: New reference.** Part of the Stable ABI since version 3.13.* Return the object at position *index* in the list pointed to by *list*. The position must be non-negative; indexing from the end of the list is not supported. If *index* is out of bounds ("<0 or >=len(list)"), return "NULL" and set an "IndexError" exception. Added in version 3.13. PyObject *PyList_GetItem(PyObject *list, Py_ssize_t index) *Return value: Borrowed reference.** Part of the Stable ABI.* Like "PyList_GetItemRef()", but returns a *borrowed reference* instead of a *strong reference*. PyObject *PyList_GET_ITEM(PyObject *list, Py_ssize_t i) *Return value: Borrowed reference.* Similar to "PyList_GetItem()", but without error checking. int PyList_SetItem(PyObject *list, Py_ssize_t index, PyObject *item) * Part of the Stable ABI.* Set the item at index *index* in list to *item*. Return "0" on success. If *index* is out of bounds, return "-1" and set an "IndexError" exception. Note: This function "steals" a reference to *item* and discards a reference to an item already in the list at the affected position. void PyList_SET_ITEM(PyObject *list, Py_ssize_t i, PyObject *o) Macro form of "PyList_SetItem()" without error checking. This is normally only used to fill in new lists where there is no previous content. Bounds checking is performed as an assertion if Python is built in debug mode or "with assertions". Note: This macro "steals" a reference to *item*, and, unlike "PyList_SetItem()", does *not* discard a reference to any item that is being replaced; any reference in *list* at position *i* will be leaked. int PyList_Insert(PyObject *list, Py_ssize_t index, PyObject *item) * Part of the Stable ABI.* Insert the item *item* into list *list* in front of index *index*. Return "0" if successful; return "-1" and set an exception if unsuccessful. Analogous to "list.insert(index, item)". int PyList_Append(PyObject *list, PyObject *item) * Part of the Stable ABI.* Append the object *item* at the end of list *list*. Return "0" if successful; return "-1" and set an exception if unsuccessful. Analogous to "list.append(item)". PyObject *PyList_GetSlice(PyObject *list, Py_ssize_t low, Py_ssize_t high) *Return value: New reference.** Part of the Stable ABI.* Return a list of the objects in *list* containing the objects *between* *low* and *high*. Return "NULL" and set an exception if unsuccessful. Analogous to "list[low:high]". Indexing from the end of the list is not supported. int PyList_SetSlice(PyObject *list, Py_ssize_t low, Py_ssize_t high, PyObject *itemlist) * Part of the Stable ABI.* Set the slice of *list* between *low* and *high* to the contents of *itemlist*. Analogous to "list[low:high] = itemlist". The *itemlist* may be "NULL", indicating the assignment of an empty list (slice deletion). Return "0" on success, "-1" on failure. Indexing from the end of the list is not supported. int PyList_Extend(PyObject *list, PyObject *iterable) Extend *list* with the contents of *iterable*. This is the same as "PyList_SetSlice(list, PY_SSIZE_T_MAX, PY_SSIZE_T_MAX, iterable)" and analogous to "list.extend(iterable)" or "list += iterable". Raise an exception and return "-1" if *list* is not a "list" object. Return 0 on success. Added in version 3.13. int PyList_Clear(PyObject *list) Remove all items from *list*. This is the same as "PyList_SetSlice(list, 0, PY_SSIZE_T_MAX, NULL)" and analogous to "list.clear()" or "del list[:]". Raise an exception and return "-1" if *list* is not a "list" object. Return 0 on success. Added in version 3.13. int PyList_Sort(PyObject *list) * Part of the Stable ABI.* Sort the items of *list* in place. Return "0" on success, "-1" on failure. This is equivalent to "list.sort()". int PyList_Reverse(PyObject *list) * Part of the Stable ABI.* Reverse the items of *list* in place. Return "0" on success, "-1" on failure. This is the equivalent of "list.reverse()". PyObject *PyList_AsTuple(PyObject *list) *Return value: New reference.** Part of the Stable ABI.* Return a new tuple object containing the contents of *list*; equivalent to "tuple(list)".