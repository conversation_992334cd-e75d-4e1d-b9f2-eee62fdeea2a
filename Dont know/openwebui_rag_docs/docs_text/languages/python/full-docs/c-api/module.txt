Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > module.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > module.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > module.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > module.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > module.txt Module Objects ************** PyTypeObject PyModule_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python module type. This is exposed to Python programs as "types.ModuleType". int PyModule_Check(PyObject *p) Return true if *p* is a module object, or a subtype of a module object. This function always succeeds. int PyModule_CheckExact(PyObject *p) Return true if *p* is a module object, but not a subtype of "PyModule_Type". This function always succeeds. PyObject *PyModule_NewObject(PyObject *name) *Return value: New reference.** Part of the Stable ABI since version 3.7.* Return a new module object with "module.__name__" set to *name*. The module's "__name__", "__doc__", "__package__" and "__loader__" attributes are filled in (all but "__name__" are set to "None"). The caller is responsible for setting a "__file__" attribute. Return "NULL" with an exception set on error. Added in version 3.3. Changed in version 3.4: "__package__" and "__loader__" are now set to "None". PyObject *PyModule_New(const char *name) *Return value: New reference.** Part of the Stable ABI.* Similar to "PyModule_NewObject()", but the name is a UTF-8 encoded string instead of a Unicode object. PyObject *PyModule_GetDict(PyObject *module) *Return value: Borrowed reference.** Part of the Stable ABI.* Return the dictionary object that implements *module*'s namespace; this object is the same as the "__dict__" attribute of the module object. If *module* is not a module object (or a subtype of a module object), "SystemError" is raised and "NULL" is returned. It is recommended extensions use other "PyModule_*" and "PyObject_*" functions rather than directly manipulate a module's "__dict__". PyObject *PyModule_GetNameObject(PyObject *module) *Return value: New reference.** Part of the Stable ABI since version 3.7.* Return *module*'s "__name__" value. If the module does not provide one, or if it is not a string, "SystemError" is raised and "NULL" is returned. Added in version 3.3. const char *PyModule_GetName(PyObject *module) * Part of the Stable ABI.* Similar to "PyModule_GetNameObject()" but return the name encoded to "'utf-8'". void *PyModule_GetState(PyObject *module) * Part of the Stable ABI.* Return the "state" of the module, that is, a pointer to the block of memory allocated at module creation time, or "NULL". See "PyModuleDef.m_size". PyModuleDef *PyModule_GetDef(PyObject *module) * Part of the Stable ABI.* Return a pointer to the "PyModuleDef" struct from which the module was created, or "NULL" if the module wasn't created from a definition. PyObject *PyModule_GetFilenameObject(PyObject *module) *Return value: New reference.** Part of the Stable ABI.* Return the name of the file from which *module* was loaded using *module*'s "__file__" attribute. If this is not defined, or if it is not a string, raise "SystemError" and return "NULL"; otherwise return a reference to a Unicode object. Added in version 3.2. const char *PyModule_GetFilename(PyObject *module) * Part of the Stable ABI.* Similar to "PyModule_GetFilenameObject()" but return the filename encoded to 'utf-8'. Deprecated since version 3.2: "PyModule_GetFilename()" raises "UnicodeEncodeError" on unencodable filenames, use "PyModule_GetFilenameObject()" instead. Initializing C modules ====================== Modules objects are usually created from extension modules (shared libraries which export an initialization function), or compiled-in modules (where the initialization function is added using "PyImport_AppendInittab()"). See Building C and C++ Extensions or Extending Embedded Python for details. The initialization function can either pass a module definition instance to "PyModule_Create()", and return the resulting module object, or request "multi-phase initialization" by returning the definition struct itself. type PyModuleDef * Part of the Stable ABI (including all members).* The module definition struct, which holds all information needed to create a module object. There is usually only one statically initialized variable of this type for each module. PyModuleDef_Base m_base Always initialize this member to "PyModuleDef_HEAD_INIT". const char *m_name Name for the new module. const char *m_doc Docstring for the module; usually a docstring variable created with "PyDoc_STRVAR" is used. Py_ssize_t m_size Module state may be kept in a per-module memory area that can be retrieved with "PyModule_GetState()", rather than in static globals. This makes modules safe for use in multiple sub- interpreters. This memory area is allocated based on *m_size* on module creation, and freed when the module object is deallocated, after the "m_free" function has been called, if present. Setting "m_size" to "-1" means that the module does not support sub-interpreters, because it has global state. Setting it to a non-negative value means that the module can be re-initialized and specifies the additional amount of memory it requires for its state. Non-negative "m_size" is required for multi-phase initialization. See **PEP 3121** for more details. PyMethodDef *m_methods A pointer to a table of module-level functions, described by "PyMethodDef" values. Can be "NULL" if no functions are present. PyModuleDef_Slot *m_slots An array of slot definitions for multi-phase initialization, terminated by a "{0, NULL}" entry. When using single-phase initialization, *m_slots* must be "NULL". Changed in version 3.5: Prior to version 3.5, this member was always set to "NULL", and was defined as: inquiry m_reload traverseproc m_traverse A traversal function to call during GC traversal of the module object, or "NULL" if not needed. This function is not called if the module state was requested but is not allocated yet. This is the case immediately after the module is created and before the module is executed ("Py_mod_exec" function). More precisely, this function is not called if "m_size" is greater than 0 and the module state (as returned by "PyModule_GetState()") is "NULL". Changed in version 3.9: No longer called before the module state is allocated. inquiry m_clear A clear function to call during GC clearing of the module object, or "NULL" if not needed. This function is not called if the module state was requested but is not allocated yet. This is the case immediately after the module is created and before the module is executed ("Py_mod_exec" function). More precisely, this function is not called if "m_size" is greater than 0 and the module state (as returned by "PyModule_GetState()") is "NULL". Like "PyTypeObject.tp_clear", this function is not *always* called before a module is deallocated. For example, when reference counting is enough to determine that an object is no longer used, the cyclic garbage collector is not involved and "m_free" is called directly. Changed in version 3.9: No longer called before the module state is allocated. freefunc m_free A function to call during deallocation of the module object, or "NULL" if not needed. This function is not called if the module state was requested but is not allocated yet. This is the case immediately after the module is created and before the module is executed ("Py_mod_exec" function). More precisely, this function is not called if "m_size" is greater than 0 and the module state (as returned by "PyModule_GetState()") is "NULL". Changed in version 3.9: No longer called before the module state is allocated. Single-phase initialization --------------------------- The module initialization function may create and return the module object directly. This is referred to as "single-phase initialization", and uses one of the following two module creation functions: PyObject *PyModule_Create(PyModuleDef *def) *Return value: New reference.* Create a new module object, given the definition in *def*. This behaves like "PyModule_Create2()" with *module_api_version* set to "PYTHON_API_VERSION". PyObject *PyModule_Create2(PyModuleDef *def, int module_api_version) *Return value: New reference.** Part of the Stable ABI.* Create a new module object, given the definition in *def*, assuming the API version *module_api_version*. If that version does not match the version of the running interpreter, a "RuntimeWarning" is emitted. Return "NULL" with an exception set on error. Note: Most uses of this function should be using "PyModule_Create()" instead; only use this if you are sure you need it. Before it is returned from in the initialization function, the resulting module object is typically populated using functions like "PyModule_AddObjectRef()". Multi-phase initialization -------------------------- An alternate way to specify extensions is to request "multi-phase initialization". Extension modules created this way behave more like Python modules: the initialization is split between the *creation phase*, when the module object is created, and the *execution phase*, when it is populated. The distinction is similar to the "__new__()" and "__init__()" methods of classes. Unlike modules created using single-phase initialization, these modules are not singletons: if the *sys.modules* entry is removed and the module is re-imported, a new module object is created, and the old module is subject to normal garbage collection -- as with Python modules. By default, multiple modules created from the same definition should be independent: changes to one should not affect the others. This means that all state should be specific to the module object (using e.g. using "PyModule_GetState()"), or its contents (such as the module's "__dict__" or individual classes created with "PyType_FromSpec()"). All modules created using multi-phase initialization are expected to support sub-interpreters. Making sure multiple modules are independent is typically enough to achieve this. To request multi-phase initialization, the initialization function (PyInit_modulename) returns a "PyModuleDef" instance with non-empty "m_slots". Before it is returned, the "PyModuleDef" instance must be initialized with the following function: PyObject *PyModuleDef_Init(PyModuleDef *def) *Return value: Borrowed reference.** Part of the Stable ABI since version 3.5.* Ensures a module definition is a properly initialized Python object that correctly reports its type and reference count. Returns *def* cast to "PyObject*", or "NULL" if an error occurred. Added in version 3.5. The *m_slots* member of the module definition must point to an array of "PyModuleDef_Slot" structures: type PyModuleDef_Slot int slot A slot ID, chosen from the available values explained below. void *value Value of the slot, whose meaning depends on the slot ID. Added in version 3.5. The *m_slots* array must be terminated by a slot with id 0. The available slot types are: Py_mod_create Specifies a function that is called to create the module object itself. The *value* pointer of this slot must point to a function of the signature: PyObject *create_module(PyObject *spec, PyModuleDef *def) The function receives a "ModuleSpec" instance, as defined in **PEP 451**, and the module definition. It should return a new module object, or set an error and return "NULL". This function should be kept minimal. In particular, it should not call arbitrary Python code, as trying to import the same module again may result in an infinite loop. Multiple "Py_mod_create" slots may not be specified in one module definition. If "Py_mod_create" is not specified, the import machinery will create a normal module object using "PyModule_New()". The name is taken from *spec*, not the definition, to allow extension modules to dynamically adjust to their place in the module hierarchy and be imported under different names through symlinks, all while sharing a single module definition. There is no requirement for the returned object to be an instance of "PyModule_Type". Any type can be used, as long as it supports setting and getting import-related attributes. However, only "PyModule_Type" instances may be returned if the "PyModuleDef" has non-"NULL" "m_traverse", "m_clear", "m_free"; non-zero "m_size"; or slots other than "Py_mod_create". Py_mod_exec Specifies a function that is called to *execute* the module. This is equivalent to executing the code of a Python module: typically, this function adds classes and constants to the module. The signature of the function is: int exec_module(PyObject *module) If multiple "Py_mod_exec" slots are specified, they are processed in the order they appear in the *m_slots* array. Py_mod_multiple_interpreters Specifies one of the following values: Py_MOD_MULTIPLE_INTERPRETERS_NOT_SUPPORTED The module does not support being imported in subinterpreters. Py_MOD_MULTIPLE_INTERPRETERS_SUPPORTED The module supports being imported in subinterpreters, but only when they share the main interpreter's GIL. (See Isolating Extension Modules.) Py_MOD_PER_INTERPRETER_GIL_SUPPORTED The module supports being imported in subinterpreters, even when they have their own GIL. (See Isolating Extension Modules.) This slot determines whether or not importing this module in a subinterpreter will fail. Multiple "Py_mod_multiple_interpreters" slots may not be specified in one module definition. If "Py_mod_multiple_interpreters" is not specified, the import machinery defaults to "Py_MOD_MULTIPLE_INTERPRETERS_SUPPORTED". Added in version 3.12. Py_mod_gil Specifies one of the following values: Py_MOD_GIL_USED The module depends on the presence of the global interpreter lock (GIL), and may access global state without synchronization. Py_MOD_GIL_NOT_USED The module is safe to run without an active GIL. This slot is ignored by Python builds not configured with "-- disable-gil". Otherwise, it determines whether or not importing this module will cause the GIL to be automatically enabled. See Free-threaded CPython for more detail. Multiple "Py_mod_gil" slots may not be specified in one module definition. If "Py_mod_gil" is not specified, the import machinery defaults to "Py_MOD_GIL_USED". Added in version 3.13. See **PEP 489** for more details on multi-phase initialization. Low-level module creation functions ----------------------------------- The following functions are called under the hood when using multi- phase initialization. They can be used directly, for example when creating module objects dynamically. Note that both "PyModule_FromDefAndSpec" and "PyModule_ExecDef" must be called to fully initialize a module. PyObject *PyModule_FromDefAndSpec(PyModuleDef *def, PyObject *spec) *Return value: New reference.* Create a new module object, given the definition in *def* and the ModuleSpec *spec*. This behaves like "PyModule_FromDefAndSpec2()" with *module_api_version* set to "PYTHON_API_VERSION". Added in version 3.5. PyObject *PyModule_FromDefAndSpec2(PyModuleDef *def, PyObject *spec, int module_api_version) *Return value: New reference.** Part of the Stable ABI since version 3.7.* Create a new module object, given the definition in *def* and the ModuleSpec *spec*, assuming the API version *module_api_version*. If that version does not match the version of the running interpreter, a "RuntimeWarning" is emitted. Return "NULL" with an exception set on error. Note: Most uses of this function should be using "PyModule_FromDefAndSpec()" instead; only use this if you are sure you need it. Added in version 3.5. int PyModule_ExecDef(PyObject *module, PyModuleDef *def) * Part of the Stable ABI since version 3.7.* Process any execution slots ("Py_mod_exec") given in *def*. Added in version 3.5. int PyModule_SetDocString(PyObject *module, const char *docstring) * Part of the Stable ABI since version 3.7.* Set the docstring for *module* to *docstring*. This function is called automatically when creating a module from "PyModuleDef", using either "PyModule_Create" or "PyModule_FromDefAndSpec". Added in version 3.5. int PyModule_AddFunctions(PyObject *module, PyMethodDef *functions) * Part of the Stable ABI since version 3.7.* Add the functions from the "NULL" terminated *functions* array to *module*. Refer to the "PyMethodDef" documentation for details on individual entries (due to the lack of a shared module namespace, module level "functions" implemented in C typically receive the module as their first parameter, making them similar to instance methods on Python classes). This function is called automatically when creating a module from "PyModuleDef", using either "PyModule_Create" or "PyModule_FromDefAndSpec". Added in version 3.5. Support functions ----------------- The module initialization function (if using single phase initialization) or a function called from a module execution slot (if using multi-phase initialization), can use the following functions to help initialize the module state: int PyModule_AddObjectRef(PyObject *module, const char *name, PyObject *value) * Part of the Stable ABI since version 3.10.* Add an object to *module* as *name*. This is a convenience function which can be used from the module's initialization function. On success, return "0". On error, raise an exception and return "-1". Example usage: static int add_spam(PyObject *module, int value) { PyObject *obj = PyLong_FromLong(value); if (obj == NULL) { return -1; } int res = PyModule_AddObjectRef(module, "spam", obj); Py_DECREF(obj); return res; } To be convenient, the function accepts "NULL" *value* with an exception set. In this case, return "-1" and just leave the raised exception unchanged. The example can also be written without checking explicitly if *obj* is "NULL": static int add_spam(PyObject *module, int value) { PyObject *obj = PyLong_FromLong(value); int res = PyModule_AddObjectRef(module, "spam", obj); Py_XDECREF(obj); return res; } Note that "Py_XDECREF()" should be used instead of "Py_DECREF()" in this case, since *obj* can be "NULL". The number of different *name* strings passed to this function should be kept small, usually by only using statically allocated strings as *name*. For names that aren't known at compile time, prefer calling "PyUnicode_FromString()" and "PyObject_SetAttr()" directly. For more details, see "PyUnicode_InternFromString()", which may be used internally to create a key object. Added in version 3.10. int PyModule_Add(PyObject *module, const char *name, PyObject *value) * Part of the Stable ABI since version 3.13.* Similar to "PyModule_AddObjectRef()", but "steals" a reference to *value*. It can be called with a result of function that returns a new reference without bothering to check its result or even saving it to a variable. Example usage: if (PyModule_Add(module, "spam", PyBytes_FromString(value)) < 0) { goto error; } Added in version 3.13. int PyModule_AddObject(PyObject *module, const char *name, PyObject *value) * Part of the Stable ABI.* Similar to "PyModule_AddObjectRef()", but steals a reference to *value* on success (if it returns "0"). The new "PyModule_Add()" or "PyModule_AddObjectRef()" functions are recommended, since it is easy to introduce reference leaks by misusing the "PyModule_AddObject()" function. Note: Unlike other functions that steal references, "PyModule_AddObject()" only releases the reference to *value* **on success**.This means that its return value must be checked, and calling code must "Py_XDECREF()" *value* manually on error. Example usage: PyObject *obj = PyBytes_FromString(value); if (PyModule_AddObject(module, "spam", obj) < 0) { // If 'obj' is not NULL and PyModule_AddObject() failed, // 'obj' strong reference must be deleted with Py_XDECREF(). // If 'obj' is NULL, Py_XDECREF() does nothing. Py_XDECREF(obj); goto error; } // PyModule_AddObject() stole a reference to obj: // Py_XDECREF(obj) is not needed here. Deprecated since version 3.13: "PyModule_AddObject()" is *soft deprecated*. int PyModule_AddIntConstant(PyObject *module, const char *name, long value) * Part of the Stable ABI.* Add an integer constant to *module* as *name*. This convenience function can be used from the module's initialization function. Return "-1" with an exception set on error, "0" on success. This is a convenience function that calls "PyLong_FromLong()" and "PyModule_AddObjectRef()"; see their documentation for details. int PyModule_AddStringConstant(PyObject *module, const char *name, const char *value) * Part of the Stable ABI.* Add a string constant to *module* as *name*. This convenience function can be used from the module's initialization function. The string *value* must be "NULL"-terminated. Return "-1" with an exception set on error, "0" on success. This is a convenience function that calls "PyUnicode_InternFromString()" and "PyModule_AddObjectRef()"; see their documentation for details. PyModule_AddIntMacro(module, macro) Add an int constant to *module*. The name and the value are taken from *macro*. For example "PyModule_AddIntMacro(module, AF_INET)" adds the int constant *AF_INET* with the value of *AF_INET* to *module*. Return "-1" with an exception set on error, "0" on success. PyModule_AddStringMacro(module, macro) Add a string constant to *module*. int PyModule_AddType(PyObject *module, PyTypeObject *type) * Part of the Stable ABI since version 3.10.* Add a type object to *module*. The type object is finalized by calling internally "PyType_Ready()". The name of the type object is taken from the last component of "tp_name" after dot. Return "-1" with an exception set on error, "0" on success. Added in version 3.9. int PyUnstable_Module_SetGIL(PyObject *module, void *gil) *This is Unstable API. It may change without warning in minor releases.* Indicate that *module* does or does not support running without the global interpreter lock (GIL), using one of the values from "Py_mod_gil". It must be called during *module*'s initialization function. If this function is not called during module initialization, the import machinery assumes the module does not support running without the GIL. This function is only available in Python builds configured with "--disable-gil". Return "-1" with an exception set on error, "0" on success. Added in version 3.13. Module lookup ============= Single-phase initialization creates singleton modules that can be looked up in the context of the current interpreter. This allows the module object to be retrieved later with only a reference to the module definition. These functions will not work on modules created using multi-phase initialization, since multiple such modules can be created from a single definition. PyObject *PyState_FindModule(PyModuleDef *def) *Return value: Borrowed reference.** Part of the Stable ABI.* Returns the module object that was created from *def* for the current interpreter. This method requires that the module object has been attached to the interpreter state with "PyState_AddModule()" beforehand. In case the corresponding module object is not found or has not been attached to the interpreter state yet, it returns "NULL". int PyState_AddModule(PyObject *module, PyModuleDef *def) * Part of the Stable ABI since version 3.3.* Attaches the module object passed to the function to the interpreter state. This allows the module object to be accessible via "PyState_FindModule()". Only effective on modules created using single-phase initialization. Python calls "PyState_AddModule" automatically after importing a module, so it is unnecessary (but harmless) to call it from module initialization code. An explicit call is needed only if the module's own init code subsequently calls "PyState_FindModule". The function is mainly intended for implementing alternative import mechanisms (either by calling it directly, or by referring to its implementation for details of the required state updates). The caller must hold the GIL. Return "-1" with an exception set on error, "0" on success. Added in version 3.3. int PyState_RemoveModule(PyModuleDef *def) * Part of the Stable ABI since version 3.3.* Removes the module object created from *def* from the interpreter state. Return "-1" with an exception set on error, "0" on success. The caller must hold the GIL. Added in version 3.3.