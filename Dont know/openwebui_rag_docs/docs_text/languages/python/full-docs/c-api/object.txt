Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > object.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > object.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > object.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > object.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > object.txt Object Protocol *************** PyObject *Py_GetConstant(unsigned int constant_id) * Part of the Stable ABI since version 3.13.* Get a *strong reference* to a constant. Set an exception and return "NULL" if *constant_id* is invalid. *constant_id* must be one of these constant identifiers: +------------------------------------------+-------+---------------------------+ | Constant Identifier | Value | Returned object | |==========================================|=======|===========================| | Py_CONSTANT_NONE | "0" | "None" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_FALSE | "1" | "False" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_TRUE | "2" | "True" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_ELLIPSIS | "3" | "Ellipsis" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_NOT_IMPLEMENTED | "4" | "NotImplemented" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_ZERO | "5" | "0" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_ONE | "6" | "1" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_EMPTY_STR | "7" | "''" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_EMPTY_BYTES | "8" | "b''" | +------------------------------------------+-------+---------------------------+ | Py_CONSTANT_EMPTY_TUPLE | "9" | "()" | +------------------------------------------+-------+---------------------------+ Numeric values are only given for projects which cannot use the constant identifiers. Added in version 3.13. **CPython implementation detail:** In CPython, all of these constants are *immortal*. PyObject *Py_GetConstantBorrowed(unsigned int constant_id) * Part of the Stable ABI since version 3.13.* Similar to "Py_GetConstant()", but return a *borrowed reference*. This function is primarily intended for backwards compatibility: using "Py_GetConstant()" is recommended for new code. The reference is borrowed from the interpreter, and is valid until the interpreter finalization. Added in version 3.13. PyObject *Py_NotImplemented The "NotImplemented" singleton, used to signal that an operation is not implemented for the given type combination. Py_RETURN_NOTIMPLEMENTED Properly handle returning "Py_NotImplemented" from within a C function (that is, create a new *strong reference* to "NotImplemented" and return it). Py_PRINT_RAW Flag to be used with multiple functions that print the object (like "PyObject_Print()" and "PyFile_WriteObject()"). If passed, these function would use the "str()" of the object instead of the "repr()". int PyObject_Print(PyObject *o, FILE *fp, int flags) Print an object *o*, on file *fp*. Returns "-1" on error. The flags argument is used to enable certain printing options. The only option currently supported is "Py_PRINT_RAW"; if given, the "str()" of the object is written instead of the "repr()". int PyObject_HasAttrWithError(PyObject *o, PyObject *attr_name) * Part of the Stable ABI since version 3.13.* Returns "1" if *o* has the attribute *attr_name*, and "0" otherwise. This is equivalent to the Python expression "hasattr(o, attr_name)". On failure, return "-1". Added in version 3.13. int PyObject_HasAttrStringWithError(PyObject *o, const char *attr_name) * Part of the Stable ABI since version 3.13.* This is the same as "PyObject_HasAttrWithError()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Added in version 3.13. int PyObject_HasAttr(PyObject *o, PyObject *attr_name) * Part of the Stable ABI.* Returns "1" if *o* has the attribute *attr_name*, and "0" otherwise. This function always succeeds. Note: Exceptions that occur when this calls "__getattr__()" and "__getattribute__()" methods aren't propagated, but instead given to "sys.unraisablehook()". For proper error handling, use "PyObject_HasAttrWithError()", "PyObject_GetOptionalAttr()" or "PyObject_GetAttr()" instead. int PyObject_HasAttrString(PyObject *o, const char *attr_name) * Part of the Stable ABI.* This is the same as "PyObject_HasAttr()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Note: Exceptions that occur when this calls "__getattr__()" and "__getattribute__()" methods or while creating the temporary "str" object are silently ignored. For proper error handling, use "PyObject_HasAttrStringWithError()", "PyObject_GetOptionalAttrString()" or "PyObject_GetAttrString()" instead. PyObject *PyObject_GetAttr(PyObject *o, PyObject *attr_name) *Return value: New reference.** Part of the Stable ABI.* Retrieve an attribute named *attr_name* from object *o*. Returns the attribute value on success, or "NULL" on failure. This is the equivalent of the Python expression "o.attr_name". If the missing attribute should not be treated as a failure, you can use "PyObject_GetOptionalAttr()" instead. PyObject *PyObject_GetAttrString(PyObject *o, const char *attr_name) *Return value: New reference.** Part of the Stable ABI.* This is the same as "PyObject_GetAttr()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. If the missing attribute should not be treated as a failure, you can use "PyObject_GetOptionalAttrString()" instead. int PyObject_GetOptionalAttr(PyObject *obj, PyObject *attr_name, PyObject **result); * Part of the Stable ABI since version 3.13.* Variant of "PyObject_GetAttr()" which doesn't raise "AttributeError" if the attribute is not found. If the attribute is found, return "1" and set **result* to a new *strong reference* to the attribute. If the attribute is not found, return "0" and set **result* to "NULL"; the "AttributeError" is silenced. If an error other than "AttributeError" is raised, return "-1" and set **result* to "NULL". Added in version 3.13. int PyObject_GetOptionalAttrString(PyObject *obj, const char *attr_name, PyObject **result); * Part of the Stable ABI since version 3.13.* This is the same as "PyObject_GetOptionalAttr()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. Added in version 3.13. PyObject *PyObject_GenericGetAttr(PyObject *o, PyObject *name) *Return value: New reference.** Part of the Stable ABI.* Generic attribute getter function that is meant to be put into a type object's "tp_getattro" slot. It looks for a descriptor in the dictionary of classes in the object's MRO as well as an attribute in the object's "__dict__" (if present). As outlined in Implementing Descriptors, data descriptors take preference over instance attributes, while non-data descriptors don't. Otherwise, an "AttributeError" is raised. int PyObject_SetAttr(PyObject *o, PyObject *attr_name, PyObject *v) * Part of the Stable ABI.* Set the value of the attribute named *attr_name*, for object *o*, to the value *v*. Raise an exception and return "-1" on failure; return "0" on success. This is the equivalent of the Python statement "o.attr_name = v". If *v* is "NULL", the attribute is deleted. This behaviour is deprecated in favour of using "PyObject_DelAttr()", but there are currently no plans to remove it. int PyObject_SetAttrString(PyObject *o, const char *attr_name, PyObject *v) * Part of the Stable ABI.* This is the same as "PyObject_SetAttr()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. If *v* is "NULL", the attribute is deleted, but this feature is deprecated in favour of using "PyObject_DelAttrString()". The number of different attribute names passed to this function should be kept small, usually by using a statically allocated string as *attr_name*. For attribute names that aren't known at compile time, prefer calling "PyUnicode_FromString()" and "PyObject_SetAttr()" directly. For more details, see "PyUnicode_InternFromString()", which may be used internally to create a key object. int PyObject_GenericSetAttr(PyObject *o, PyObject *name, PyObject *value) * Part of the Stable ABI.* Generic attribute setter and deleter function that is meant to be put into a type object's "tp_setattro" slot. It looks for a data descriptor in the dictionary of classes in the object's MRO, and if found it takes preference over setting or deleting the attribute in the instance dictionary. Otherwise, the attribute is set or deleted in the object's "__dict__" (if present). On success, "0" is returned, otherwise an "AttributeError" is raised and "-1" is returned. int PyObject_DelAttr(PyObject *o, PyObject *attr_name) * Part of the Stable ABI since version 3.13.* Delete attribute named *attr_name*, for object *o*. Returns "-1" on failure. This is the equivalent of the Python statement "del o.attr_name". int PyObject_DelAttrString(PyObject *o, const char *attr_name) * Part of the Stable ABI since version 3.13.* This is the same as "PyObject_DelAttr()", but *attr_name* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. The number of different attribute names passed to this function should be kept small, usually by using a statically allocated string as *attr_name*. For attribute names that aren't known at compile time, prefer calling "PyUnicode_FromString()" and "PyObject_DelAttr()" directly. For more details, see "PyUnicode_InternFromString()", which may be used internally to create a key object for lookup. PyObject *PyObject_GenericGetDict(PyObject *o, void *context) *Return value: New reference.** Part of the Stable ABI since version 3.10.* A generic implementation for the getter of a "__dict__" descriptor. It creates the dictionary if necessary. This function may also be called to get the "__dict__" of the object *o*. Pass "NULL" for *context* when calling it. Since this function may need to allocate memory for the dictionary, it may be more efficient to call "PyObject_GetAttr()" when accessing an attribute on the object. On failure, returns "NULL" with an exception set. Added in version 3.3. int PyObject_GenericSetDict(PyObject *o, PyObject *value, void *context) * Part of the Stable ABI since version 3.7.* A generic implementation for the setter of a "__dict__" descriptor. This implementation does not allow the dictionary to be deleted. Added in version 3.3. PyObject **_PyObject_GetDictPtr(PyObject *obj) Return a pointer to "__dict__" of the object *obj*. If there is no "__dict__", return "NULL" without setting an exception. This function may need to allocate memory for the dictionary, so it may be more efficient to call "PyObject_GetAttr()" when accessing an attribute on the object. PyObject *PyObject_RichCompare(PyObject *o1, PyObject *o2, int opid) *Return value: New reference.** Part of the Stable ABI.* Compare the values of *o1* and *o2* using the operation specified by *opid*, which must be one of "Py_LT", "Py_LE", "Py_EQ", "Py_NE", "Py_GT", or "Py_GE", corresponding to "<", "<=", "==", "!=", ">", or ">=" respectively. This is the equivalent of the Python expression "o1 op o2", where "op" is the operator corresponding to *opid*. Returns the value of the comparison on success, or "NULL" on failure. int PyObject_RichCompareBool(PyObject *o1, PyObject *o2, int opid) * Part of the Stable ABI.* Compare the values of *o1* and *o2* using the operation specified by *opid*, like "PyObject_RichCompare()", but returns "-1" on error, "0" if the result is false, "1" otherwise. Note: If *o1* and *o2* are the same object, "PyObject_RichCompareBool()" will always return "1" for "Py_EQ" and "0" for "Py_NE". PyObject *PyObject_Format(PyObject *obj, PyObject *format_spec) * Part of the Stable ABI.* Format *obj* using *format_spec*. This is equivalent to the Python expression "format(obj, format_spec)". *format_spec* may be "NULL". In this case the call is equivalent to "format(obj)". Returns the formatted string on success, "NULL" on failure. PyObject *PyObject_Repr(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* Compute a string representation of object *o*. Returns the string representation on success, "NULL" on failure. This is the equivalent of the Python expression "repr(o)". Called by the "repr()" built-in function. Changed in version 3.4: This function now includes a debug assertion to help ensure that it does not silently discard an active exception. PyObject *PyObject_ASCII(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* As "PyObject_Repr()", compute a string representation of object *o*, but escape the non-ASCII characters in the string returned by "PyObject_Repr()" with "\x", "\u" or "\U" escapes. This generates a string similar to that returned by "PyObject_Repr()" in Python 2. Called by the "ascii()" built-in function. PyObject *PyObject_Str(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* Compute a string representation of object *o*. Returns the string representation on success, "NULL" on failure. This is the equivalent of the Python expression "str(o)". Called by the "str()" built-in function and, therefore, by the "print()" function. Changed in version 3.4: This function now includes a debug assertion to help ensure that it does not silently discard an active exception. PyObject *PyObject_Bytes(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* Compute a bytes representation of object *o*. "NULL" is returned on failure and a bytes object on success. This is equivalent to the Python expression "bytes(o)", when *o* is not an integer. Unlike "bytes(o)", a TypeError is raised when *o* is an integer instead of a zero-initialized bytes object. int PyObject_IsSubclass(PyObject *derived, PyObject *cls) * Part of the Stable ABI.* Return "1" if the class *derived* is identical to or derived from the class *cls*, otherwise return "0". In case of an error, return "-1". If *cls* is a tuple, the check will be done against every entry in *cls*. The result will be "1" when at least one of the checks returns "1", otherwise it will be "0". If *cls* has a "__subclasscheck__()" method, it will be called to determine the subclass status as described in **PEP 3119**. Otherwise, *derived* is a subclass of *cls* if it is a direct or indirect subclass, i.e. contained in "cls.__mro__". Normally only class objects, i.e. instances of "type" or a derived class, are considered classes. However, objects can override this by having a "__bases__" attribute (which must be a tuple of base classes). int PyObject_IsInstance(PyObject *inst, PyObject *cls) * Part of the Stable ABI.* Return "1" if *inst* is an instance of the class *cls* or a subclass of *cls*, or "0" if not. On error, returns "-1" and sets an exception. If *cls* is a tuple, the check will be done against every entry in *cls*. The result will be "1" when at least one of the checks returns "1", otherwise it will be "0". If *cls* has a "__instancecheck__()" method, it will be called to determine the subclass status as described in **PEP 3119**. Otherwise, *inst* is an instance of *cls* if its class is a subclass of *cls*. An instance *inst* can override what is considered its class by having a "__class__" attribute. An object *cls* can override if it is considered a class, and what its base classes are, by having a "__bases__" attribute (which must be a tuple of base classes). Py_hash_t PyObject_Hash(PyObject *o) * Part of the Stable ABI.* Compute and return the hash value of an object *o*. On failure, return "-1". This is the equivalent of the Python expression "hash(o)". Changed in version 3.2: The return type is now Py_hash_t. This is a signed integer the same size as "Py_ssize_t". Py_hash_t PyObject_HashNotImplemented(PyObject *o) * Part of the Stable ABI.* Set a "TypeError" indicating that "type(o)" is not *hashable* and return "-1". This function receives special treatment when stored in a "tp_hash" slot, allowing a type to explicitly indicate to the interpreter that it is not hashable. int PyObject_IsTrue(PyObject *o) * Part of the Stable ABI.* Returns "1" if the object *o* is considered to be true, and "0" otherwise. This is equivalent to the Python expression "not not o". On failure, return "-1". int PyObject_Not(PyObject *o) * Part of the Stable ABI.* Returns "0" if the object *o* is considered to be true, and "1" otherwise. This is equivalent to the Python expression "not o". On failure, return "-1". PyObject *PyObject_Type(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* When *o* is non-"NULL", returns a type object corresponding to the object type of object *o*. On failure, raises "SystemError" and returns "NULL". This is equivalent to the Python expression "type(o)". This function creates a new *strong reference* to the return value. There's really no reason to use this function instead of the "Py_TYPE()" function, which returns a pointer of type PyTypeObject*, except when a new *strong reference* is needed. int PyObject_TypeCheck(PyObject *o, PyTypeObject *type) Return non-zero if the object *o* is of type *type* or a subtype of *type*, and "0" otherwise. Both parameters must be non-"NULL". Py_ssize_t PyObject_Size(PyObject *o) Py_ssize_t PyObject_Length(PyObject *o) * Part of the Stable ABI.* Return the length of object *o*. If the object *o* provides either the sequence and mapping protocols, the sequence length is returned. On error, "-1" is returned. This is the equivalent to the Python expression "len(o)". Py_ssize_t PyObject_LengthHint(PyObject *o, Py_ssize_t defaultvalue) Return an estimated length for the object *o*. First try to return its actual length, then an estimate using "__length_hint__()", and finally return the default value. On error return "-1". This is the equivalent to the Python expression "operator.length_hint(o, defaultvalue)". Added in version 3.4. PyObject *PyObject_GetItem(PyObject *o, PyObject *key) *Return value: New reference.** Part of the Stable ABI.* Return element of *o* corresponding to the object *key* or "NULL" on failure. This is the equivalent of the Python expression "o[key]". int PyObject_SetItem(PyObject *o, PyObject *key, PyObject *v) * Part of the Stable ABI.* Map the object *key* to the value *v*. Raise an exception and return "-1" on failure; return "0" on success. This is the equivalent of the Python statement "o[key] = v". This function *does not* steal a reference to *v*. int PyObject_DelItem(PyObject *o, PyObject *key) * Part of the Stable ABI.* Remove the mapping for the object *key* from the object *o*. Return "-1" on failure. This is equivalent to the Python statement "del o[key]". int PyObject_DelItemString(PyObject *o, const char *key) * Part of the Stable ABI.* This is the same as "PyObject_DelItem()", but *key* is specified as a const char* UTF-8 encoded bytes string, rather than a PyObject*. PyObject *PyObject_Dir(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* This is equivalent to the Python expression "dir(o)", returning a (possibly empty) list of strings appropriate for the object argument, or "NULL" if there was an error. If the argument is "NULL", this is like the Python "dir()", returning the names of the current locals; in this case, if no execution frame is active then "NULL" is returned but "PyErr_Occurred()" will return false. PyObject *PyObject_GetIter(PyObject *o) *Return value: New reference.** Part of the Stable ABI.* This is equivalent to the Python expression "iter(o)". It returns a new iterator for the object argument, or the object itself if the object is already an iterator. Raises "TypeError" and returns "NULL" if the object cannot be iterated. PyObject *PyObject_SelfIter(PyObject *obj) *Return value: New reference.** Part of the Stable ABI.* This is equivalent to the Python "__iter__(self): return self" method. It is intended for *iterator* types, to be used in the "PyTypeObject.tp_iter" slot. PyObject *PyObject_GetAIter(PyObject *o) *Return value: New reference.** Part of the Stable ABI since version 3.10.* This is the equivalent to the Python expression "aiter(o)". Takes an "AsyncIterable" object and returns an "AsyncIterator" for it. This is typically a new iterator but if the argument is an "AsyncIterator", this returns itself. Raises "TypeError" and returns "NULL" if the object cannot be iterated. Added in version 3.10. void *PyObject_GetTypeData(PyObject *o, PyTypeObject *cls) * Part of the Stable ABI since version 3.12.* Get a pointer to subclass-specific data reserved for *cls*. The object *o* must be an instance of *cls*, and *cls* must have been created using negative "PyType_Spec.basicsize". Python does not check this. On error, set an exception and return "NULL". Added in version 3.12. Py_ssize_t PyType_GetTypeDataSize(PyTypeObject *cls) * Part of the Stable ABI since version 3.12.* Return the size of the instance memory space reserved for *cls*, i.e. the size of the memory "PyObject_GetTypeData()" returns. This may be larger than requested using "-PyType_Spec.basicsize"; it is safe to use this larger size (e.g. with "memset()"). The type *cls* **must** have been created using negative "PyType_Spec.basicsize". Python does not check this. On error, set an exception and return a negative value. Added in version 3.12. void *PyObject_GetItemData(PyObject *o) Get a pointer to per-item data for a class with "Py_TPFLAGS_ITEMS_AT_END". On error, set an exception and return "NULL". "TypeError" is raised if *o* does not have "Py_TPFLAGS_ITEMS_AT_END" set. Added in version 3.12. int PyObject_VisitManagedDict(PyObject *obj, visitproc visit, void *arg) Visit the managed dictionary of *obj*. This function must only be called in a traverse function of the type which has the "Py_TPFLAGS_MANAGED_DICT" flag set. Added in version 3.13. void PyObject_ClearManagedDict(PyObject *obj) Clear the managed dictionary of *obj*. This function must only be called in a traverse function of the type which has the "Py_TPFLAGS_MANAGED_DICT" flag set. Added in version 3.13.