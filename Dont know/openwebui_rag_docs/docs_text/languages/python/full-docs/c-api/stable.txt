Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > stable.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > stable.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > stable.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > stable.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > stable.txt C API Stability *************** Unless documented otherwise, Python's C API is covered by the Backwards Compatibility Policy, **PEP 387**. Most changes to it are source-compatible (typically by only adding new API). Changing existing API or removing API is only done after a deprecation period or to fix serious issues. CPython's Application Binary Interface (ABI) is forward- and backwards-compatible across a minor release (if these are compiled the same way; see Platform Considerations below). So, code compiled for Python 3.10.0 will work on 3.10.8 and vice versa, but will need to be compiled separately for 3.9.x and 3.11.x. There are two tiers of C API with different stability expectations: * Unstable API, may change in minor versions without a deprecation period. It is marked by the "PyUnstable" prefix in names. * Limited API, is compatible across several minor releases. When "Py_LIMITED_API" is defined, only this subset is exposed from "Python.h". These are discussed in more detail below. Names prefixed by an underscore, such as "_Py_InternalState", are private API that can change without notice even in patch releases. If you need to use this API, consider reaching out to CPython developers to discuss adding public API for your use case. Unstable C API ============== Any API named with the "PyUnstable" prefix exposes CPython implementation details, and may change in every minor release (e.g. from 3.9 to 3.10) without any deprecation warnings. However, it will not change in a bugfix release (e.g. from 3.10.0 to 3.10.1). It is generally intended for specialized, low-level tools like debuggers. Projects that use this API are expected to follow CPython development and spend extra effort adjusting to changes. Stable Application Binary Interface =================================== For simplicity, this document talks about *extensions*, but the Limited API and Stable ABI work the same way for all uses of the API for example, embedding Python. Limited C API ------------- Python 3.2 introduced the *Limited API*, a subset of Python's C API. Extensions that only use the Limited API can be compiled once and be loaded on multiple versions of Python. Contents of the Limited API are listed below. Py_LIMITED_API Define this macro before including "Python.h" to opt in to only use the Limited API, and to select the Limited API version. Define "Py_LIMITED_API" to the value of "PY_VERSION_HEX" corresponding to the lowest Python version your extension supports. The extension will be ABI-compatible with all Python 3 releases from the specified one onward, and can use Limited API introduced up to that version. Rather than using the "PY_VERSION_HEX" macro directly, hardcode a minimum minor version (e.g. "0x030A0000" for Python 3.10) for stability when compiling with future Python versions. You can also define "Py_LIMITED_API" to "3". This works the same as "0x03020000" (Python 3.2, the version that introduced Limited API). Stable ABI ---------- To enable this, Python provides a *Stable ABI*: a set of symbols that will remain ABI-compatible across Python 3.x versions. Note: The Stable ABI prevents ABI issues, like linker errors due to missing symbols or data corruption due to changes in structure layouts or function signatures. However, other changes in Python can change the *behavior* of extensions. See Python's Backwards Compatibility Policy (**PEP 387**) for details. The Stable ABI contains symbols exposed in the Limited API, but also other ones for example, functions necessary to support older versions of the Limited API. On Windows, extensions that use the Stable ABI should be linked against "python3.dll" rather than a version-specific library such as "python39.dll". On some platforms, Python will look for and load shared library files named with the "abi3" tag (e.g. "mymodule.abi3.so"). It does not check if such extensions conform to a Stable ABI. The user (or their packaging tools) need to ensure that, for example, extensions built with the 3.10+ Limited API are not installed for lower versions of Python. All functions in the Stable ABI are present as functions in Python's shared library, not solely as macros. This makes them usable from languages that don't use the C preprocessor. Limited API Scope and Performance --------------------------------- The goal for the Limited API is to allow everything that is possible with the full C API, but possibly with a performance penalty. For example, while "PyList_GetItem()" is available, its unsafe macro variant "PyList_GET_ITEM()" is not. The macro can be faster because it can rely on version-specific implementation details of the list object. Without "Py_LIMITED_API" defined, some C API functions are inlined or replaced by macros. Defining "Py_LIMITED_API" disables this inlining, allowing stability as Python's data structures are improved, but possibly reducing performance. By leaving out the "Py_LIMITED_API" definition, it is possible to compile a Limited API extension with a version-specific ABI. This can improve performance for that Python version, but will limit compatibility. Compiling with "Py_LIMITED_API" will then yield an extension that can be distributed where a version-specific one is not available for example, for prereleases of an upcoming Python version. Limited API Caveats ------------------- Note that compiling with "Py_LIMITED_API" is *not* a complete guarantee that code conforms to the Limited API or the Stable ABI. "Py_LIMITED_API" only covers definitions, but an API also includes other issues, such as expected semantics. One issue that "Py_LIMITED_API" does not guard against is calling a function with arguments that are invalid in a lower Python version. For example, consider a function that starts accepting "NULL" for an argument. In Python 3.9, "NULL" now selects a default behavior, but in Python 3.8, the argument will be used directly, causing a "NULL" dereference and crash. A similar argument works for fields of structs. Another issue is that some struct fields are currently not hidden when "Py_LIMITED_API" is defined, even though they're part of the Limited API. For these reasons, we recommend testing an extension with *all* minor Python versions it supports, and preferably to build with the *lowest* such version. We also recommend reviewing documentation of all used API to check if it is explicitly part of the Limited API. Even with "Py_LIMITED_API" defined, a few private declarations are exposed for technical reasons (or even unintentionally, as bugs). Also note that the Limited API is not necessarily stable: compiling with "Py_LIMITED_API" with Python 3.8 means that the extension will run with Python 3.12, but it will not necessarily *compile* with Python 3.12. In particular, parts of the Limited API may be deprecated and removed, provided that the Stable ABI stays stable. Platform Considerations ======================= ABI stability depends not only on Python, but also on the compiler used, lower-level libraries and compiler options. For the purposes of the Stable ABI, these details define a platform . They usually depend on the OS type and processor architecture It is the responsibility of each particular distributor of Python to ensure that all Python versions on a particular platform are built in a way that does not break the Stable ABI. This is the case with Windows and macOS releases from "python.org" and many third-party distributors. Contents of Limited API ======================= Currently, the Limited API includes the following items: * "PY_VECTORCALL_ARGUMENTS_OFFSET" * "PyAIter_Check()" * "PyArg_Parse()" * "PyArg_ParseTuple()" * "PyArg_ParseTupleAndKeywords()" * "PyArg_UnpackTuple()" * "PyArg_VaParse()" * "PyArg_VaParseTupleAndKeywords()" * "PyArg_ValidateKeywordArguments()" * "PyBaseObject_Type" * "PyBool_FromLong()" * "PyBool_Type" * "PyBuffer_FillContiguousStrides()" * "PyBuffer_FillInfo()" * "PyBuffer_FromContiguous()" * "PyBuffer_GetPointer()" * "PyBuffer_IsContiguous()" * "PyBuffer_Release()" * "PyBuffer_SizeFromFormat()" * "PyBuffer_ToContiguous()" * "PyByteArrayIter_Type" * "PyByteArray_AsString()" * "PyByteArray_Concat()" * "PyByteArray_FromObject()" * "PyByteArray_FromStringAndSize()" * "PyByteArray_Resize()" * "PyByteArray_Size()" * "PyByteArray_Type" * "PyBytesIter_Type" * "PyBytes_AsString()" * "PyBytes_AsStringAndSize()" * "PyBytes_Concat()" * "PyBytes_ConcatAndDel()" * "PyBytes_DecodeEscape()" * "PyBytes_FromFormat()" * "PyBytes_FromFormatV()" * "PyBytes_FromObject()" * "PyBytes_FromString()" * "PyBytes_FromStringAndSize()" * "PyBytes_Repr()" * "PyBytes_Size()" * "PyBytes_Type" * "PyCFunction" * "PyCFunctionFast" * "PyCFunctionFastWithKeywords" * "PyCFunctionWithKeywords" * "PyCFunction_GetFlags()" * "PyCFunction_GetFunction()" * "PyCFunction_GetSelf()" * "PyCFunction_New()" * "PyCFunction_NewEx()" * "PyCFunction_Type" * "PyCMethod_New()" * "PyCallIter_New()" * "PyCallIter_Type" * "PyCallable_Check()" * "PyCapsule_Destructor" * "PyCapsule_GetContext()" * "PyCapsule_GetDestructor()" * "PyCapsule_GetName()" * "PyCapsule_GetPointer()" * "PyCapsule_Import()" * "PyCapsule_IsValid()" * "PyCapsule_New()" * "PyCapsule_SetContext()" * "PyCapsule_SetDestructor()" * "PyCapsule_SetName()" * "PyCapsule_SetPointer()" * "PyCapsule_Type" * "PyClassMethodDescr_Type" * "PyCodec_BackslashReplaceErrors()" * "PyCodec_Decode()" * "PyCodec_Decoder()" * "PyCodec_Encode()" * "PyCodec_Encoder()" * "PyCodec_IgnoreErrors()" * "PyCodec_IncrementalDecoder()" * "PyCodec_IncrementalEncoder()" * "PyCodec_KnownEncoding()" * "PyCodec_LookupError()" * "PyCodec_NameReplaceErrors()" * "PyCodec_Register()" * "PyCodec_RegisterError()" * "PyCodec_ReplaceErrors()" * "PyCodec_StreamReader()" * "PyCodec_StreamWriter()" * "PyCodec_StrictErrors()" * "PyCodec_Unregister()" * "PyCodec_XMLCharRefReplaceErrors()" * "PyComplex_FromDoubles()" * "PyComplex_ImagAsDouble()" * "PyComplex_RealAsDouble()" * "PyComplex_Type" * "PyDescr_NewClassMethod()" * "PyDescr_NewGetSet()" * "PyDescr_NewMember()" * "PyDescr_NewMethod()" * "PyDictItems_Type" * "PyDictIterItem_Type" * "PyDictIterKey_Type" * "PyDictIterValue_Type" * "PyDictKeys_Type" * "PyDictProxy_New()" * "PyDictProxy_Type" * "PyDictRevIterItem_Type" * "PyDictRevIterKey_Type" * "PyDictRevIterValue_Type" * "PyDictValues_Type" * "PyDict_Clear()" * "PyDict_Contains()" * "PyDict_Copy()" * "PyDict_DelItem()" * "PyDict_DelItemString()" * "PyDict_GetItem()" * "PyDict_GetItemRef()" * "PyDict_GetItemString()" * "PyDict_GetItemStringRef()" * "PyDict_GetItemWithError()" * "PyDict_Items()" * "PyDict_Keys()" * "PyDict_Merge()" * "PyDict_MergeFromSeq2()" * "PyDict_New()" * "PyDict_Next()" * "PyDict_SetItem()" * "PyDict_SetItemString()" * "PyDict_Size()" * "PyDict_Type" * "PyDict_Update()" * "PyDict_Values()" * "PyEllipsis_Type" * "PyEnum_Type" * "PyErr_BadArgument()" * "PyErr_BadInternalCall()" * "PyErr_CheckSignals()" * "PyErr_Clear()" * "PyErr_Display()" * "PyErr_DisplayException()" * "PyErr_ExceptionMatches()" * "PyErr_Fetch()" * "PyErr_Format()" * "PyErr_FormatV()" * "PyErr_GetExcInfo()" * "PyErr_GetHandledException()" * "PyErr_GetRaisedException()" * "PyErr_GivenExceptionMatches()" * "PyErr_NewException()" * "PyErr_NewExceptionWithDoc()" * "PyErr_NoMemory()" * "PyErr_NormalizeException()" * "PyErr_Occurred()" * "PyErr_Print()" * "PyErr_PrintEx()" * "PyErr_ProgramText()" * "PyErr_ResourceWarning()" * "PyErr_Restore()" * "PyErr_SetExcFromWindowsErr()" * "PyErr_SetExcFromWindowsErrWithFilename()" * "PyErr_SetExcFromWindowsErrWithFilenameObject()" * "PyErr_SetExcFromWindowsErrWithFilenameObjects()" * "PyErr_SetExcInfo()" * "PyErr_SetFromErrno()" * "PyErr_SetFromErrnoWithFilename()" * "PyErr_SetFromErrnoWithFilenameObject()" * "PyErr_SetFromErrnoWithFilenameObjects()" * "PyErr_SetFromWindowsErr()" * "PyErr_SetFromWindowsErrWithFilename()" * "PyErr_SetHandledException()" * "PyErr_SetImportError()" * "PyErr_SetImportErrorSubclass()" * "PyErr_SetInterrupt()" * "PyErr_SetInterruptEx()" * "PyErr_SetNone()" * "PyErr_SetObject()" * "PyErr_SetRaisedException()" * "PyErr_SetString()" * "PyErr_SyntaxLocation()" * "PyErr_SyntaxLocationEx()" * "PyErr_WarnEx()" * "PyErr_WarnExplicit()" * "PyErr_WarnFormat()" * "PyErr_WriteUnraisable()" * "PyEval_AcquireThread()" * "PyEval_EvalCode()" * "PyEval_EvalCodeEx()" * "PyEval_EvalFrame()" * "PyEval_EvalFrameEx()" * "PyEval_GetBuiltins()" * "PyEval_GetFrame()" * "PyEval_GetFrameBuiltins()" * "PyEval_GetFrameGlobals()" * "PyEval_GetFrameLocals()" * "PyEval_GetFuncDesc()" * "PyEval_GetFuncName()" * "PyEval_GetGlobals()" * "PyEval_GetLocals()" * "PyEval_InitThreads()" * "PyEval_ReleaseThread()" * "PyEval_RestoreThread()" * "PyEval_SaveThread()" * "PyExc_ArithmeticError" * "PyExc_AssertionError" * "PyExc_AttributeError" * "PyExc_BaseException" * "PyExc_BaseExceptionGroup" * "PyExc_BlockingIOError" * "PyExc_BrokenPipeError" * "PyExc_BufferError" * "PyExc_BytesWarning" * "PyExc_ChildProcessError" * "PyExc_ConnectionAbortedError" * "PyExc_ConnectionError" * "PyExc_ConnectionRefusedError" * "PyExc_ConnectionResetError" * "PyExc_DeprecationWarning" * "PyExc_EOFError" * "PyExc_EncodingWarning" * "PyExc_EnvironmentError" * "PyExc_Exception" * "PyExc_FileExistsError" * "PyExc_FileNotFoundError" * "PyExc_FloatingPointError" * "PyExc_FutureWarning" * "PyExc_GeneratorExit" * "PyExc_IOError" * "PyExc_ImportError" * "PyExc_ImportWarning" * "PyExc_IndentationError" * "PyExc_IndexError" * "PyExc_InterruptedError" * "PyExc_IsADirectoryError" * "PyExc_KeyError" * "PyExc_KeyboardInterrupt" * "PyExc_LookupError" * "PyExc_MemoryError" * "PyExc_ModuleNotFoundError" * "PyExc_NameError" * "PyExc_NotADirectoryError" * "PyExc_NotImplementedError" * "PyExc_OSError" * "PyExc_OverflowError" * "PyExc_PendingDeprecationWarning" * "PyExc_PermissionError" * "PyExc_ProcessLookupError" * "PyExc_RecursionError" * "PyExc_ReferenceError" * "PyExc_ResourceWarning" * "PyExc_RuntimeError" * "PyExc_RuntimeWarning" * "PyExc_StopAsyncIteration" * "PyExc_StopIteration" * "PyExc_SyntaxError" * "PyExc_SyntaxWarning" * "PyExc_SystemError" * "PyExc_SystemExit" * "PyExc_TabError" * "PyExc_TimeoutError" * "PyExc_TypeError" * "PyExc_UnboundLocalError" * "PyExc_UnicodeDecodeError" * "PyExc_UnicodeEncodeError" * "PyExc_UnicodeError" * "PyExc_UnicodeTranslateError" * "PyExc_UnicodeWarning" * "PyExc_UserWarning" * "PyExc_ValueError" * "PyExc_Warning" * "PyExc_WindowsError" * "PyExc_ZeroDivisionError" * "PyExceptionClass_Name()" * "PyException_GetArgs()" * "PyException_GetCause()" * "PyException_GetContext()" * "PyException_GetTraceback()" * "PyException_SetArgs()" * "PyException_SetCause()" * "PyException_SetContext()" * "PyException_SetTraceback()" * "PyFile_FromFd()" * "PyFile_GetLine()" * "PyFile_WriteObject()" * "PyFile_WriteString()" * "PyFilter_Type" * "PyFloat_AsDouble()" * "PyFloat_FromDouble()" * "PyFloat_FromString()" * "PyFloat_GetInfo()" * "PyFloat_GetMax()" * "PyFloat_GetMin()" * "PyFloat_Type" * "PyFrameObject" * "PyFrame_GetCode()" * "PyFrame_GetLineNumber()" * "PyFrozenSet_New()" * "PyFrozenSet_Type" * "PyGC_Collect()" * "PyGC_Disable()" * "PyGC_Enable()" * "PyGC_IsEnabled()" * "PyGILState_Ensure()" * "PyGILState_GetThisThreadState()" * "PyGILState_Release()" * "PyGILState_STATE" * "PyGetSetDef" * "PyGetSetDescr_Type" * "PyImport_AddModule()" * "PyImport_AddModuleObject()" * "PyImport_AddModuleRef()" * "PyImport_AppendInittab()" * "PyImport_ExecCodeModule()" * "PyImport_ExecCodeModuleEx()" * "PyImport_ExecCodeModuleObject()" * "PyImport_ExecCodeModuleWithPathnames()" * "PyImport_GetImporter()" * "PyImport_GetMagicNumber()" * "PyImport_GetMagicTag()" * "PyImport_GetModule()" * "PyImport_GetModuleDict()" * "PyImport_Import()" * "PyImport_ImportFrozenModule()" * "PyImport_ImportFrozenModuleObject()" * "PyImport_ImportModule()" * "PyImport_ImportModuleLevel()" * "PyImport_ImportModuleLevelObject()" * "PyImport_ImportModuleNoBlock()" * "PyImport_ReloadModule()" * "PyIndex_Check()" * "PyInterpreterState" * "PyInterpreterState_Clear()" * "PyInterpreterState_Delete()" * "PyInterpreterState_Get()" * "PyInterpreterState_GetDict()" * "PyInterpreterState_GetID()" * "PyInterpreterState_New()" * "PyIter_Check()" * "PyIter_Next()" * "PyIter_Send()" * "PyListIter_Type" * "PyListRevIter_Type" * "PyList_Append()" * "PyList_AsTuple()" * "PyList_GetItem()" * "PyList_GetItemRef()" * "PyList_GetSlice()" * "PyList_Insert()" * "PyList_New()" * "PyList_Reverse()" * "PyList_SetItem()" * "PyList_SetSlice()" * "PyList_Size()" * "PyList_Sort()" * "PyList_Type" * "PyLongObject" * "PyLongRangeIter_Type" * "PyLong_AsDouble()" * "PyLong_AsInt()" * "PyLong_AsLong()" * "PyLong_AsLongAndOverflow()" * "PyLong_AsLongLong()" * "PyLong_AsLongLongAndOverflow()" * "PyLong_AsSize_t()" * "PyLong_AsSsize_t()" * "PyLong_AsUnsignedLong()" * "PyLong_AsUnsignedLongLong()" * "PyLong_AsUnsignedLongLongMask()" * "PyLong_AsUnsignedLongMask()" * "PyLong_AsVoidPtr()" * "PyLong_FromDouble()" * "PyLong_FromLong()" * "PyLong_FromLongLong()" * "PyLong_FromSize_t()" * "PyLong_FromSsize_t()" * "PyLong_FromString()" * "PyLong_FromUnsignedLong()" * "PyLong_FromUnsignedLongLong()" * "PyLong_FromVoidPtr()" * "PyLong_GetInfo()" * "PyLong_Type" * "PyMap_Type" * "PyMapping_Check()" * "PyMapping_GetItemString()" * "PyMapping_GetOptionalItem()" * "PyMapping_GetOptionalItemString()" * "PyMapping_HasKey()" * "PyMapping_HasKeyString()" * "PyMapping_HasKeyStringWithError()" * "PyMapping_HasKeyWithError()" * "PyMapping_Items()" * "PyMapping_Keys()" * "PyMapping_Length()" * "PyMapping_SetItemString()" * "PyMapping_Size()" * "PyMapping_Values()" * "PyMem_Calloc()" * "PyMem_Free()" * "PyMem_Malloc()" * "PyMem_RawCalloc()" * "PyMem_RawFree()" * "PyMem_RawMalloc()" * "PyMem_RawRealloc()" * "PyMem_Realloc()" * "PyMemberDef" * "PyMemberDescr_Type" * "PyMember_GetOne()" * "PyMember_SetOne()" * "PyMemoryView_FromBuffer()" * "PyMemoryView_FromMemory()" * "PyMemoryView_FromObject()" * "PyMemoryView_GetContiguous()" * "PyMemoryView_Type" * "PyMethodDef" * "PyMethodDescr_Type" * "PyModuleDef" * "PyModuleDef_Base" * "PyModuleDef_Init()" * "PyModuleDef_Type" * "PyModule_Add()" * "PyModule_AddFunctions()" * "PyModule_AddIntConstant()" * "PyModule_AddObject()" * "PyModule_AddObjectRef()" * "PyModule_AddStringConstant()" * "PyModule_AddType()" * "PyModule_Create2()" * "PyModule_ExecDef()" * "PyModule_FromDefAndSpec2()" * "PyModule_GetDef()" * "PyModule_GetDict()" * "PyModule_GetFilename()" * "PyModule_GetFilenameObject()" * "PyModule_GetName()" * "PyModule_GetNameObject()" * "PyModule_GetState()" * "PyModule_New()" * "PyModule_NewObject()" * "PyModule_SetDocString()" * "PyModule_Type" * "PyNumber_Absolute()" * "PyNumber_Add()" * "PyNumber_And()" * "PyNumber_AsSsize_t()" * "PyNumber_Check()" * "PyNumber_Divmod()" * "PyNumber_Float()" * "PyNumber_FloorDivide()" * "PyNumber_InPlaceAdd()" * "PyNumber_InPlaceAnd()" * "PyNumber_InPlaceFloorDivide()" * "PyNumber_InPlaceLshift()" * "PyNumber_InPlaceMatrixMultiply()" * "PyNumber_InPlaceMultiply()" * "PyNumber_InPlaceOr()" * "PyNumber_InPlacePower()" * "PyNumber_InPlaceRemainder()" * "PyNumber_InPlaceRshift()" * "PyNumber_InPlaceSubtract()" * "PyNumber_InPlaceTrueDivide()" * "PyNumber_InPlaceXor()" * "PyNumber_Index()" * "PyNumber_Invert()" * "PyNumber_Long()" * "PyNumber_Lshift()" * "PyNumber_MatrixMultiply()" * "PyNumber_Multiply()" * "PyNumber_Negative()" * "PyNumber_Or()" * "PyNumber_Positive()" * "PyNumber_Power()" * "PyNumber_Remainder()" * "PyNumber_Rshift()" * "PyNumber_Subtract()" * "PyNumber_ToBase()" * "PyNumber_TrueDivide()" * "PyNumber_Xor()" * "PyOS_AfterFork()" * "PyOS_AfterFork_Child()" * "PyOS_AfterFork_Parent()" * "PyOS_BeforeFork()" * "PyOS_CheckStack()" * "PyOS_FSPath()" * "PyOS_InputHook" * "PyOS_InterruptOccurred()" * "PyOS_double_to_string()" * "PyOS_getsig()" * "PyOS_mystricmp()" * "PyOS_mystrnicmp()" * "PyOS_setsig()" * "PyOS_sighandler_t" * "PyOS_snprintf()" * "PyOS_string_to_double()" * "PyOS_strtol()" * "PyOS_strtoul()" * "PyOS_vsnprintf()" * "PyObject" * "PyObject.ob_refcnt" * "PyObject.ob_type" * "PyObject_ASCII()" * "PyObject_AsFileDescriptor()" * "PyObject_Bytes()" * "PyObject_Call()" * "PyObject_CallFunction()" * "PyObject_CallFunctionObjArgs()" * "PyObject_CallMethod()" * "PyObject_CallMethodObjArgs()" * "PyObject_CallNoArgs()" * "PyObject_CallObject()" * "PyObject_Calloc()" * "PyObject_CheckBuffer()" * "PyObject_ClearWeakRefs()" * "PyObject_CopyData()" * "PyObject_DelAttr()" * "PyObject_DelAttrString()" * "PyObject_DelItem()" * "PyObject_DelItemString()" * "PyObject_Dir()" * "PyObject_Format()" * "PyObject_Free()" * "PyObject_GC_Del()" * "PyObject_GC_IsFinalized()" * "PyObject_GC_IsTracked()" * "PyObject_GC_Track()" * "PyObject_GC_UnTrack()" * "PyObject_GenericGetAttr()" * "PyObject_GenericGetDict()" * "PyObject_GenericSetAttr()" * "PyObject_GenericSetDict()" * "PyObject_GetAIter()" * "PyObject_GetAttr()" * "PyObject_GetAttrString()" * "PyObject_GetBuffer()" * "PyObject_GetItem()" * "PyObject_GetIter()" * "PyObject_GetOptionalAttr()" * "PyObject_GetOptionalAttrString()" * "PyObject_GetTypeData()" * "PyObject_HasAttr()" * "PyObject_HasAttrString()" * "PyObject_HasAttrStringWithError()" * "PyObject_HasAttrWithError()" * "PyObject_Hash()" * "PyObject_HashNotImplemented()" * "PyObject_Init()" * "PyObject_InitVar()" * "PyObject_IsInstance()" * "PyObject_IsSubclass()" * "PyObject_IsTrue()" * "PyObject_Length()" * "PyObject_Malloc()" * "PyObject_Not()" * "PyObject_Realloc()" * "PyObject_Repr()" * "PyObject_RichCompare()" * "PyObject_RichCompareBool()" * "PyObject_SelfIter()" * "PyObject_SetAttr()" * "PyObject_SetAttrString()" * "PyObject_SetItem()" * "PyObject_Size()" * "PyObject_Str()" * "PyObject_Type()" * "PyObject_Vectorcall()" * "PyObject_VectorcallMethod()" * "PyProperty_Type" * "PyRangeIter_Type" * "PyRange_Type" * "PyReversed_Type" * "PySeqIter_New()" * "PySeqIter_Type" * "PySequence_Check()" * "PySequence_Concat()" * "PySequence_Contains()" * "PySequence_Count()" * "PySequence_DelItem()" * "PySequence_DelSlice()" * "PySequence_Fast()" * "PySequence_GetItem()" * "PySequence_GetSlice()" * "PySequence_In()" * "PySequence_InPlaceConcat()" * "PySequence_InPlaceRepeat()" * "PySequence_Index()" * "PySequence_Length()" * "PySequence_List()" * "PySequence_Repeat()" * "PySequence_SetItem()" * "PySequence_SetSlice()" * "PySequence_Size()" * "PySequence_Tuple()" * "PySetIter_Type" * "PySet_Add()" * "PySet_Clear()" * "PySet_Contains()" * "PySet_Discard()" * "PySet_New()" * "PySet_Pop()" * "PySet_Size()" * "PySet_Type" * "PySlice_AdjustIndices()" * "PySlice_GetIndices()" * "PySlice_GetIndicesEx()" * "PySlice_New()" * "PySlice_Type" * "PySlice_Unpack()" * "PyState_AddModule()" * "PyState_FindModule()" * "PyState_RemoveModule()" * "PyStructSequence_Desc" * "PyStructSequence_Field" * "PyStructSequence_GetItem()" * "PyStructSequence_New()" * "PyStructSequence_NewType()" * "PyStructSequence_SetItem()" * "PyStructSequence_UnnamedField" * "PySuper_Type" * "PySys_Audit()" * "PySys_AuditTuple()" * "PySys_FormatStderr()" * "PySys_FormatStdout()" * "PySys_GetObject()" * "PySys_GetXOptions()" * "PySys_ResetWarnOptions()" * "PySys_SetArgv()" * "PySys_SetArgvEx()" * "PySys_SetObject()" * "PySys_WriteStderr()" * "PySys_WriteStdout()" * "PyThreadState" * "PyThreadState_Clear()" * "PyThreadState_Delete()" * "PyThreadState_Get()" * "PyThreadState_GetDict()" * "PyThreadState_GetFrame()" * "PyThreadState_GetID()" * "PyThreadState_GetInterpreter()" * "PyThreadState_New()" * "PyThreadState_SetAsyncExc()" * "PyThreadState_Swap()" * "PyThread_GetInfo()" * "PyThread_ReInitTLS()" * "PyThread_acquire_lock()" * "PyThread_acquire_lock_timed()" * "PyThread_allocate_lock()" * "PyThread_create_key()" * "PyThread_delete_key()" * "PyThread_delete_key_value()" * "PyThread_exit_thread()" * "PyThread_free_lock()" * "PyThread_get_key_value()" * "PyThread_get_stacksize()" * "PyThread_get_thread_ident()" * "PyThread_get_thread_native_id()" * "PyThread_init_thread()" * "PyThread_release_lock()" * "PyThread_set_key_value()" * "PyThread_set_stacksize()" * "PyThread_start_new_thread()" * "PyThread_tss_alloc()" * "PyThread_tss_create()" * "PyThread_tss_delete()" * "PyThread_tss_free()" * "PyThread_tss_get()" * "PyThread_tss_is_created()" * "PyThread_tss_set()" * "PyTraceBack_Here()" * "PyTraceBack_Print()" * "PyTraceBack_Type" * "PyTupleIter_Type" * "PyTuple_GetItem()" * "PyTuple_GetSlice()" * "PyTuple_New()" * "PyTuple_Pack()" * "PyTuple_SetItem()" * "PyTuple_Size()" * "PyTuple_Type" * "PyTypeObject" * "PyType_ClearCache()" * "PyType_FromMetaclass()" * "PyType_FromModuleAndSpec()" * "PyType_FromSpec()" * "PyType_FromSpecWithBases()" * "PyType_GenericAlloc()" * "PyType_GenericNew()" * "PyType_GetFlags()" * "PyType_GetFullyQualifiedName()" * "PyType_GetModule()" * "PyType_GetModuleByDef()" * "PyType_GetModuleName()" * "PyType_GetModuleState()" * "PyType_GetName()" * "PyType_GetQualName()" * "PyType_GetSlot()" * "PyType_GetTypeDataSize()" * "PyType_IsSubtype()" * "PyType_Modified()" * "PyType_Ready()" * "PyType_Slot" * "PyType_Spec" * "PyType_Type" * "PyUnicodeDecodeError_Create()" * "PyUnicodeDecodeError_GetEncoding()" * "PyUnicodeDecodeError_GetEnd()" * "PyUnicodeDecodeError_GetObject()" * "PyUnicodeDecodeError_GetReason()" * "PyUnicodeDecodeError_GetStart()" * "PyUnicodeDecodeError_SetEnd()" * "PyUnicodeDecodeError_SetReason()" * "PyUnicodeDecodeError_SetStart()" * "PyUnicodeEncodeError_GetEncoding()" * "PyUnicodeEncodeError_GetEnd()" * "PyUnicodeEncodeError_GetObject()" * "PyUnicodeEncodeError_GetReason()" * "PyUnicodeEncodeError_GetStart()" * "PyUnicodeEncodeError_SetEnd()" * "PyUnicodeEncodeError_SetReason()" * "PyUnicodeEncodeError_SetStart()" * "PyUnicodeIter_Type" * "PyUnicodeTranslateError_GetEnd()" * "PyUnicodeTranslateError_GetObject()" * "PyUnicodeTranslateError_GetReason()" * "PyUnicodeTranslateError_GetStart()" * "PyUnicodeTranslateError_SetEnd()" * "PyUnicodeTranslateError_SetReason()" * "PyUnicodeTranslateError_SetStart()" * "PyUnicode_Append()" * "PyUnicode_AppendAndDel()" * "PyUnicode_AsASCIIString()" * "PyUnicode_AsCharmapString()" * "PyUnicode_AsDecodedObject()" * "PyUnicode_AsDecodedUnicode()" * "PyUnicode_AsEncodedObject()" * "PyUnicode_AsEncodedString()" * "PyUnicode_AsEncodedUnicode()" * "PyUnicode_AsLatin1String()" * "PyUnicode_AsMBCSString()" * "PyUnicode_AsRawUnicodeEscapeString()" * "PyUnicode_AsUCS4()" * "PyUnicode_AsUCS4Copy()" * "PyUnicode_AsUTF16String()" * "PyUnicode_AsUTF32String()" * "PyUnicode_AsUTF8AndSize()" * "PyUnicode_AsUTF8String()" * "PyUnicode_AsUnicodeEscapeString()" * "PyUnicode_AsWideChar()" * "PyUnicode_AsWideCharString()" * "PyUnicode_BuildEncodingMap()" * "PyUnicode_Compare()" * "PyUnicode_CompareWithASCIIString()" * "PyUnicode_Concat()" * "PyUnicode_Contains()" * "PyUnicode_Count()" * "PyUnicode_Decode()" * "PyUnicode_DecodeASCII()" * "PyUnicode_DecodeCharmap()" * "PyUnicode_DecodeCodePageStateful()" * "PyUnicode_DecodeFSDefault()" * "PyUnicode_DecodeFSDefaultAndSize()" * "PyUnicode_DecodeLatin1()" * "PyUnicode_DecodeLocale()" * "PyUnicode_DecodeLocaleAndSize()" * "PyUnicode_DecodeMBCS()" * "PyUnicode_DecodeMBCSStateful()" * "PyUnicode_DecodeRawUnicodeEscape()" * "PyUnicode_DecodeUTF16()" * "PyUnicode_DecodeUTF16Stateful()" * "PyUnicode_DecodeUTF32()" * "PyUnicode_DecodeUTF32Stateful()" * "PyUnicode_DecodeUTF7()" * "PyUnicode_DecodeUTF7Stateful()" * "PyUnicode_DecodeUTF8()" * "PyUnicode_DecodeUTF8Stateful()" * "PyUnicode_DecodeUnicodeEscape()" * "PyUnicode_EncodeCodePage()" * "PyUnicode_EncodeFSDefault()" * "PyUnicode_EncodeLocale()" * "PyUnicode_EqualToUTF8()" * "PyUnicode_EqualToUTF8AndSize()" * "PyUnicode_FSConverter()" * "PyUnicode_FSDecoder()" * "PyUnicode_Find()" * "PyUnicode_FindChar()" * "PyUnicode_Format()" * "PyUnicode_FromEncodedObject()" * "PyUnicode_FromFormat()" * "PyUnicode_FromFormatV()" * "PyUnicode_FromObject()" * "PyUnicode_FromOrdinal()" * "PyUnicode_FromString()" * "PyUnicode_FromStringAndSize()" * "PyUnicode_FromWideChar()" * "PyUnicode_GetDefaultEncoding()" * "PyUnicode_GetLength()" * "PyUnicode_InternFromString()" * "PyUnicode_InternInPlace()" * "PyUnicode_IsIdentifier()" * "PyUnicode_Join()" * "PyUnicode_Partition()" * "PyUnicode_RPartition()" * "PyUnicode_RSplit()" * "PyUnicode_ReadChar()" * "PyUnicode_Replace()" * "PyUnicode_Resize()" * "PyUnicode_RichCompare()" * "PyUnicode_Split()" * "PyUnicode_Splitlines()" * "PyUnicode_Substring()" * "PyUnicode_Tailmatch()" * "PyUnicode_Translate()" * "PyUnicode_Type" * "PyUnicode_WriteChar()" * "PyVarObject" * "PyVarObject.ob_base" * "PyVarObject.ob_size" * "PyVectorcall_Call()" * "PyVectorcall_NARGS()" * "PyWeakReference" * "PyWeakref_GetObject()" * "PyWeakref_GetRef()" * "PyWeakref_NewProxy()" * "PyWeakref_NewRef()" * "PyWrapperDescr_Type" * "PyWrapper_New()" * "PyZip_Type" * "Py_AddPendingCall()" * "Py_AtExit()" * "Py_BEGIN_ALLOW_THREADS" * "Py_BLOCK_THREADS" * "Py_BuildValue()" * "Py_BytesMain()" * "Py_CompileString()" * "Py_DecRef()" * "Py_DecodeLocale()" * "Py_END_ALLOW_THREADS" * "Py_EncodeLocale()" * "Py_EndInterpreter()" * "Py_EnterRecursiveCall()" * "Py_Exit()" * "Py_FatalError()" * "Py_FileSystemDefaultEncodeErrors" * "Py_FileSystemDefaultEncoding" * "Py_Finalize()" * "Py_FinalizeEx()" * "Py_GenericAlias()" * "Py_GenericAliasType" * "Py_GetBuildInfo()" * "Py_GetCompiler()" * "Py_GetConstant()" * "Py_GetConstantBorrowed()" * "Py_GetCopyright()" * "Py_GetExecPrefix()" * "Py_GetPath()" * "Py_GetPlatform()" * "Py_GetPrefix()" * "Py_GetProgramFullPath()" * "Py_GetProgramName()" * "Py_GetPythonHome()" * "Py_GetRecursionLimit()" * "Py_GetVersion()" * "Py_HasFileSystemDefaultEncoding" * "Py_IncRef()" * "Py_Initialize()" * "Py_InitializeEx()" * "Py_Is()" * "Py_IsFalse()" * "Py_IsFinalizing()" * "Py_IsInitialized()" * "Py_IsNone()" * "Py_IsTrue()" * "Py_LeaveRecursiveCall()" * "Py_Main()" * "Py_MakePendingCalls()" * "Py_NewInterpreter()" * "Py_NewRef()" * "Py_ReprEnter()" * "Py_ReprLeave()" * "Py_SetProgramName()" * "Py_SetPythonHome()" * "Py_SetRecursionLimit()" * "Py_UCS4" * "Py_UNBLOCK_THREADS" * "Py_UTF8Mode" * "Py_VaBuildValue()" * "Py_Version" * "Py_XNewRef()" * "Py_buffer" * "Py_intptr_t" * "Py_ssize_t" * "Py_uintptr_t" * "allocfunc" * "binaryfunc" * "descrgetfunc" * "descrsetfunc" * "destructor" * "getattrfunc" * "getattrofunc" * "getbufferproc" * "getiterfunc" * "getter" * "hashfunc" * "initproc" * "inquiry" * "iternextfunc" * "lenfunc" * "newfunc" * "objobjargproc" * "objobjproc" * "releasebufferproc" * "reprfunc" * "richcmpfunc" * "setattrfunc" * "setattrofunc" * "setter" * "ssizeargfunc" * "ssizeobjargproc" * "ssizessizeargfunc" * "ssizessizeobjargproc" * "symtable" * "ternaryfunc" * "traverseproc" * "unaryfunc" * "vectorcallfunc" * "visitproc"