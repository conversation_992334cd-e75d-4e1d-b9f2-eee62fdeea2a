Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > structures.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > structures.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > structures.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > structures.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > structures.txt Common Object Structures ************************ There are a large number of structures which are used in the definition of object types for Python. This section describes these structures and how they are used. Base object types and macros ============================ All Python objects ultimately share a small number of fields at the beginning of the object's representation in memory. These are represented by the "PyObject" and "PyVarObject" types, which are defined, in turn, by the expansions of some macros also used, whether directly or indirectly, in the definition of all other Python objects. Additional macros can be found under reference counting. type PyObject * Part of the Limited API. (Only some members are part of the stable ABI.)* All object types are extensions of this type. This is a type which contains the information Python needs to treat a pointer to an object as an object. In a normal "release" build, it contains only the object's reference count and a pointer to the corresponding type object. Nothing is actually declared to be a "PyObject", but every pointer to a Python object can be cast to a PyObject*. Access to the members must be done by using the macros "Py_REFCNT" and "Py_TYPE". type PyVarObject * Part of the Limited API. (Only some members are part of the stable ABI.)* This is an extension of "PyObject" that adds the "ob_size" field. This is only used for objects that have some notion of *length*. This type does not often appear in the Python/C API. Access to the members must be done by using the macros "Py_REFCNT", "Py_TYPE", and "Py_SIZE". PyObject_HEAD This is a macro used when declaring new types which represent objects without a varying length. The PyObject_HEAD macro expands to: PyObject ob_base; See documentation of "PyObject" above. PyObject_VAR_HEAD This is a macro used when declaring new types which represent objects with a length that varies from instance to instance. The PyObject_VAR_HEAD macro expands to: PyVarObject ob_base; See documentation of "PyVarObject" above. PyTypeObject PyBaseObject_Type * Part of the Stable ABI.* The base class of all other objects, the same as "object" in Python. int Py_Is(PyObject *x, PyObject *y) * Part of the Stable ABI since version 3.10.* Test if the *x* object is the *y* object, the same as "x is y" in Python. Added in version 3.10. int Py_IsNone(PyObject *x) * Part of the Stable ABI since version 3.10.* Test if an object is the "None" singleton, the same as "x is None" in Python. Added in version 3.10. int Py_IsTrue(PyObject *x) * Part of the Stable ABI since version 3.10.* Test if an object is the "True" singleton, the same as "x is True" in Python. Added in version 3.10. int Py_IsFalse(PyObject *x) * Part of the Stable ABI since version 3.10.* Test if an object is the "False" singleton, the same as "x is False" in Python. Added in version 3.10. PyTypeObject *Py_TYPE(PyObject *o) *Return value: Borrowed reference.* Get the type of the Python object *o*. Return a *borrowed reference*. Use the "Py_SET_TYPE()" function to set an object type. Changed in version 3.11: "Py_TYPE()" is changed to an inline static function. The parameter type is no longer const PyObject*. int Py_IS_TYPE(PyObject *o, PyTypeObject *type) Return non-zero if the object *o* type is *type*. Return zero otherwise. Equivalent to: "Py_TYPE(o) == type". Added in version 3.9. void Py_SET_TYPE(PyObject *o, PyTypeObject *type) Set the object *o* type to *type*. Added in version 3.9. Py_ssize_t Py_SIZE(PyVarObject *o) Get the size of the Python object *o*. Use the "Py_SET_SIZE()" function to set an object size. Changed in version 3.11: "Py_SIZE()" is changed to an inline static function. The parameter type is no longer const PyVarObject*. void Py_SET_SIZE(PyVarObject *o, Py_ssize_t size) Set the object *o* size to *size*. Added in version 3.9. PyObject_HEAD_INIT(type) This is a macro which expands to initialization values for a new "PyObject" type. This macro expands to: _PyObject_EXTRA_INIT 1, type, PyVarObject_HEAD_INIT(type, size) This is a macro which expands to initialization values for a new "PyVarObject" type, including the "ob_size" field. This macro expands to: _PyObject_EXTRA_INIT 1, type, size, Implementing functions and methods ================================== type PyCFunction * Part of the Stable ABI.* Type of the functions used to implement most Python callables in C. Functions of this type take two PyObject* parameters and return one such value. If the return value is "NULL", an exception shall have been set. If not "NULL", the return value is interpreted as the return value of the function as exposed in Python. The function must return a new reference. The function signature is: PyObject *PyCFunction(PyObject *self, PyObject *args); type PyCFunctionWithKeywords * Part of the Stable ABI.* Type of the functions used to implement Python callables in C with signature METH_VARARGS | METH_KEYWORDS. The function signature is: PyObject *PyCFunctionWithKeywords(PyObject *self, PyObject *args, PyObject *kwargs); type PyCFunctionFast * Part of the Stable ABI since version 3.13.* Type of the functions used to implement Python callables in C with signature "METH_FASTCALL". The function signature is: PyObject *PyCFunctionFast(PyObject *self, PyObject *const *args, Py_ssize_t nargs); type PyCFunctionFastWithKeywords * Part of the Stable ABI since version 3.13.* Type of the functions used to implement Python callables in C with signature METH_FASTCALL | METH_KEYWORDS. The function signature is: PyObject *PyCFunctionFastWithKeywords(PyObject *self, PyObject *const *args, Py_ssize_t nargs, PyObject *kwnames); type PyCMethod Type of the functions used to implement Python callables in C with signature METH_METHOD | METH_FASTCALL | METH_KEYWORDS. The function signature is: PyObject *PyCMethod(PyObject *self, PyTypeObject *defining_class, PyObject *const *args, Py_ssize_t nargs, PyObject *kwnames) Added in version 3.9. type PyMethodDef * Part of the Stable ABI (including all members).* Structure used to describe a method of an extension type. This structure has four fields: const char *ml_name Name of the method. PyCFunction ml_meth Pointer to the C implementation. int ml_flags Flags bits indicating how the call should be constructed. const char *ml_doc Points to the contents of the docstring. The "ml_meth" is a C function pointer. The functions may be of different types, but they always return PyObject*. If the function is not of the "PyCFunction", the compiler will require a cast in the method table. Even though "PyCFunction" defines the first parameter as PyObject*, it is common that the method implementation uses the specific C type of the *self* object. The "ml_flags" field is a bitfield which can include the following flags. The individual flags indicate either a calling convention or a binding convention. There are these calling conventions: METH_VARARGS This is the typical calling convention, where the methods have the type "PyCFunction". The function expects two PyObject* values. The first one is the *self* object for methods; for module functions, it is the module object. The second parameter (often called *args*) is a tuple object representing all arguments. This parameter is typically processed using "PyArg_ParseTuple()" or "PyArg_UnpackTuple()". METH_KEYWORDS Can only be used in certain combinations with other flags: METH_VARARGS | METH_KEYWORDS, METH_FASTCALL | METH_KEYWORDS and METH_METHOD | METH_FASTCALL | METH_KEYWORDS. METH_VARARGS | METH_KEYWORDS Methods with these flags must be of type "PyCFunctionWithKeywords". The function expects three parameters: *self*, *args*, *kwargs* where *kwargs* is a dictionary of all the keyword arguments or possibly "NULL" if there are no keyword arguments. The parameters are typically processed using "PyArg_ParseTupleAndKeywords()". METH_FASTCALL Fast calling convention supporting only positional arguments. The methods have the type "PyCFunctionFast". The first parameter is *self*, the second parameter is a C array of PyObject* values indicating the arguments and the third parameter is the number of arguments (the length of the array). Added in version 3.7. Changed in version 3.10: "METH_FASTCALL" is now part of the stable ABI. METH_FASTCALL | METH_KEYWORDS Extension of "METH_FASTCALL" supporting also keyword arguments, with methods of type "PyCFunctionFastWithKeywords". Keyword arguments are passed the same way as in the vectorcall protocol: there is an additional fourth PyObject* parameter which is a tuple representing the names of the keyword arguments (which are guaranteed to be strings) or possibly "NULL" if there are no keywords. The values of the keyword arguments are stored in the *args* array, after the positional arguments. Added in version 3.7. METH_METHOD Can only be used in the combination with other flags: METH_METHOD | METH_FASTCALL | METH_KEYWORDS. METH_METHOD | METH_FASTCALL | METH_KEYWORDS Extension of METH_FASTCALL | METH_KEYWORDS supporting the *defining class*, that is, the class that contains the method in question. The defining class might be a superclass of "Py_TYPE(self)". The method needs to be of type "PyCMethod", the same as for "METH_FASTCALL | METH_KEYWORDS" with "defining_class" argument added after "self". Added in version 3.9. METH_NOARGS Methods without parameters don't need to check whether arguments are given if they are listed with the "METH_NOARGS" flag. They need to be of type "PyCFunction". The first parameter is typically named *self* and will hold a reference to the module or object instance. In all cases the second parameter will be "NULL". The function must have 2 parameters. Since the second parameter is unused, "Py_UNUSED" can be used to prevent a compiler warning. METH_O Methods with a single object argument can be listed with the "METH_O" flag, instead of invoking "PyArg_ParseTuple()" with a ""O"" argument. They have the type "PyCFunction", with the *self* parameter, and a PyObject* parameter representing the single argument. These two constants are not used to indicate the calling convention but the binding when use with methods of classes. These may not be used for functions defined for modules. At most one of these flags may be set for any given method. METH_CLASS The method will be passed the type object as the first parameter rather than an instance of the type. This is used to create *class methods*, similar to what is created when using the "classmethod()" built-in function. METH_STATIC The method will be passed "NULL" as the first parameter rather than an instance of the type. This is used to create *static methods*, similar to what is created when using the "staticmethod()" built-in function. One other constant controls whether a method is loaded in place of another definition with the same method name. METH_COEXIST The method will be loaded in place of existing definitions. Without *METH_COEXIST*, the default is to skip repeated definitions. Since slot wrappers are loaded before the method table, the existence of a *sq_contains* slot, for example, would generate a wrapped method named "__contains__()" and preclude the loading of a corresponding PyCFunction with the same name. With the flag defined, the PyCFunction will be loaded in place of the wrapper object and will co-exist with the slot. This is helpful because calls to PyCFunctions are optimized more than wrapper object calls. PyObject *PyCMethod_New(PyMethodDef *ml, PyObject *self, PyObject *module, PyTypeObject *cls) *Return value: New reference.** Part of the Stable ABI since version 3.9.* Turn *ml* into a Python *callable* object. The caller must ensure that *ml* outlives the *callable*. Typically, *ml* is defined as a static variable. The *self* parameter will be passed as the *self* argument to the C function in "ml->ml_meth" when invoked. *self* can be "NULL". The *callable* object's "__module__" attribute can be set from the given *module* argument. *module* should be a Python string, which will be used as name of the module the function is defined in. If unavailable, it can be set to "None" or "NULL". See also: "function.__module__" The *cls* parameter will be passed as the *defining_class* argument to the C function. Must be set if "METH_METHOD" is set on "ml->ml_flags". Added in version 3.9. PyObject *PyCFunction_NewEx(PyMethodDef *ml, PyObject *self, PyObject *module) *Return value: New reference.** Part of the Stable ABI.* Equivalent to "PyCMethod_New(ml, self, module, NULL)". PyObject *PyCFunction_New(PyMethodDef *ml, PyObject *self) *Return value: New reference.** Part of the Stable ABI since version 3.4.* Equivalent to "PyCMethod_New(ml, self, NULL, NULL)". Accessing attributes of extension types ======================================= type PyMemberDef * Part of the Stable ABI (including all members).* Structure which describes an attribute of a type which corresponds to a C struct member. When defining a class, put a NULL-terminated array of these structures in the "tp_members" slot. Its fields are, in order: const char *name Name of the member. A NULL value marks the end of a "PyMemberDef[]" array. The string should be static, no copy is made of it. int type The type of the member in the C struct. See Member types for the possible values. Py_ssize_t offset The offset in bytes that the member is located on the type s object struct. int flags Zero or more of the Member flags, combined using bitwise OR. const char *doc The docstring, or NULL. The string should be static, no copy is made of it. Typically, it is defined using "PyDoc_STR". By default (when "flags" is "0"), members allow both read and write access. Use the "Py_READONLY" flag for read-only access. Certain types, like "Py_T_STRING", imply "Py_READONLY". Only "Py_T_OBJECT_EX" (and legacy "T_OBJECT") members can be deleted. For heap-allocated types (created using "PyType_FromSpec()" or similar), "PyMemberDef" may contain a definition for the special member ""__vectorcalloffset__"", corresponding to "tp_vectorcall_offset" in type objects. These must be defined with "Py_T_PYSSIZET" and "Py_READONLY", for example: static PyMemberDef spam_type_members[] = { {"__vectorcalloffset__", Py_T_PYSSIZET, offsetof(Spam_object, vectorcall), Py_READONLY}, {NULL} /* Sentinel */ }; (You may need to "#include <stddef.h>" for "offsetof()".) The legacy offsets "tp_dictoffset" and "tp_weaklistoffset" can be defined similarly using ""__dictoffset__"" and ""__weaklistoffset__"" members, but extensions are strongly encouraged to use "Py_TPFLAGS_MANAGED_DICT" and "Py_TPFLAGS_MANAGED_WEAKREF" instead. Changed in version 3.12: "PyMemberDef" is always available. Previously, it required including ""structmember.h"". PyObject *PyMember_GetOne(const char *obj_addr, struct PyMemberDef *m) * Part of the Stable ABI.* Get an attribute belonging to the object at address *obj_addr*. The attribute is described by "PyMemberDef" *m*. Returns "NULL" on error. Changed in version 3.12: "PyMember_GetOne" is always available. Previously, it required including ""structmember.h"". int PyMember_SetOne(char *obj_addr, struct PyMemberDef *m, PyObject *o) * Part of the Stable ABI.* Set an attribute belonging to the object at address *obj_addr* to object *o*. The attribute to set is described by "PyMemberDef" *m*. Returns "0" if successful and a negative value on failure. Changed in version 3.12: "PyMember_SetOne" is always available. Previously, it required including ""structmember.h"". Member flags ------------ The following flags can be used with "PyMemberDef.flags": Py_READONLY Not writable. Py_AUDIT_READ Emit an "object.__getattr__" audit event before reading. Py_RELATIVE_OFFSET Indicates that the "offset" of this "PyMemberDef" entry indicates an offset from the subclass-specific data, rather than from "PyObject". Can only be used as part of "Py_tp_members" "slot" when creating a class using negative "basicsize". It is mandatory in that case. This flag is only used in "PyType_Slot". When setting "tp_members" during class creation, Python clears it and sets "PyMemberDef.offset" to the offset from the "PyObject" struct. Changed in version 3.10: The "RESTRICTED", "READ_RESTRICTED" and "WRITE_RESTRICTED" macros available with "#include "structmember.h"" are deprecated. "READ_RESTRICTED" and "RESTRICTED" are equivalent to "Py_AUDIT_READ"; "WRITE_RESTRICTED" does nothing. Changed in version 3.12: The "READONLY" macro was renamed to "Py_READONLY". The "PY_AUDIT_READ" macro was renamed with the "Py_" prefix. The new names are now always available. Previously, these required "#include "structmember.h"". The header is still available and it provides the old names. Member types ------------ "PyMemberDef.type" can be one of the following macros corresponding to various C types. When the member is accessed in Python, it will be converted to the equivalent Python type. When it is set from Python, it will be converted back to the C type. If that is not possible, an exception such as "TypeError" or "ValueError" is raised. Unless marked (D), attributes defined this way cannot be deleted using e.g. "del" or "delattr()". +----------------------------------+-------------------------------+------------------------+ | Macro name | C type | Python type | |==================================|===============================|========================| | Py_T_BYTE | char | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_SHORT | short | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_INT | int | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_LONG | long | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_LONGLONG | long long | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_UBYTE | unsigned char | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_UINT | unsigned int | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_USHORT | unsigned short | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_ULONG | unsigned long | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_ULONGLONG | unsigned long long | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_PYSSIZET | Py_ssize_t | "int" | +----------------------------------+-------------------------------+------------------------+ | Py_T_FLOAT | float | "float" | +----------------------------------+-------------------------------+------------------------+ | Py_T_DOUBLE | double | "float" | +----------------------------------+-------------------------------+------------------------+ | Py_T_BOOL | char (written as 0 or 1) | "bool" | +----------------------------------+-------------------------------+------------------------+ | Py_T_STRING | const char* (*) | "str" (RO) | +----------------------------------+-------------------------------+------------------------+ | Py_T_STRING_INPLACE | const char[] (*) | "str" (RO) | +----------------------------------+-------------------------------+------------------------+ | Py_T_CHAR | char (0-127) | "str" (**) | +----------------------------------+-------------------------------+------------------------+ | Py_T_OBJECT_EX | PyObject* | "object" (D) | +----------------------------------+-------------------------------+------------------------+ (*): Zero-terminated, UTF8-encoded C string. With "Py_T_STRING" the C representation is a pointer; with "Py_T_STRING_INPLACE" the string is stored directly in the structure. (**): String of length 1. Only ASCII is accepted. (RO): Implies "Py_READONLY". (D): Can be deleted, in which case the pointer is set to "NULL". Reading a "NULL" pointer raises "AttributeError". Added in version 3.12: In previous versions, the macros were only available with "#include "structmember.h"" and were named without the "Py_" prefix (e.g. as "T_INT"). The header is still available and contains the old names, along with the following deprecated types: T_OBJECT Like "Py_T_OBJECT_EX", but "NULL" is converted to "None". This results in surprising behavior in Python: deleting the attribute effectively sets it to "None". T_NONE Always "None". Must be used with "Py_READONLY". Defining Getters and Setters ---------------------------- type PyGetSetDef * Part of the Stable ABI (including all members).* Structure to define property-like access for a type. See also description of the "PyTypeObject.tp_getset" slot. const char *name attribute name getter get C function to get the attribute. setter set Optional C function to set or delete the attribute. If "NULL", the attribute is read-only. const char *doc optional docstring void *closure Optional user data pointer, providing additional data for getter and setter. typedef PyObject *(*getter)(PyObject*, void*) * Part of the Stable ABI.* The "get" function takes one PyObject* parameter (the instance) and a user data pointer (the associated "closure"): It should return a new reference on success or "NULL" with a set exception on failure. typedef int (*setter)(PyObject*, PyObject*, void*) * Part of the Stable ABI.* "set" functions take two PyObject* parameters (the instance and the value to be set) and a user data pointer (the associated "closure"): In case the attribute should be deleted the second parameter is "NULL". Should return "0" on success or "-1" with a set exception on failure.