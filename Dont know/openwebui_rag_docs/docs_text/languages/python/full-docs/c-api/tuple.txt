Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > tuple.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > tuple.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > tuple.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > tuple.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > tuple.txt Tuple Objects ************* type PyTupleObject This subtype of "PyObject" represents a Python tuple object. PyTypeObject PyTuple_Type * Part of the Stable ABI.* This instance of "PyTypeObject" represents the Python tuple type; it is the same object as "tuple" in the Python layer. int PyTuple_Check(PyObject *p) Return true if *p* is a tuple object or an instance of a subtype of the tuple type. This function always succeeds. int PyTuple_CheckExact(PyObject *p) Return true if *p* is a tuple object, but not an instance of a subtype of the tuple type. This function always succeeds. PyObject *PyTuple_New(Py_ssize_t len) *Return value: New reference.** Part of the Stable ABI.* Return a new tuple object of size *len*, or "NULL" with an exception set on failure. PyObject *PyTuple_Pack(Py_ssize_t n, ...) *Return value: New reference.** Part of the Stable ABI.* Return a new tuple object of size *n*, or "NULL" with an exception set on failure. The tuple values are initialized to the subsequent *n* C arguments pointing to Python objects. "PyTuple_Pack(2, a, b)" is equivalent to "Py_BuildValue("(OO)", a, b)". Py_ssize_t PyTuple_Size(PyObject *p) * Part of the Stable ABI.* Take a pointer to a tuple object, and return the size of that tuple. On error, return "-1" and with an exception set. Py_ssize_t PyTuple_GET_SIZE(PyObject *p) Like "PyTuple_Size()", but without error checking. PyObject *PyTuple_GetItem(PyObject *p, Py_ssize_t pos) *Return value: Borrowed reference.** Part of the Stable ABI.* Return the object at position *pos* in the tuple pointed to by *p*. If *pos* is negative or out of bounds, return "NULL" and set an "IndexError" exception. The returned reference is borrowed from the tuple *p* (that is: it is only valid as long as you hold a reference to *p*). To get a *strong reference*, use "Py_NewRef(PyTuple_GetItem(...))" or "PySequence_GetItem()". PyObject *PyTuple_GET_ITEM(PyObject *p, Py_ssize_t pos) *Return value: Borrowed reference.* Like "PyTuple_GetItem()", but does no checking of its arguments. PyObject *PyTuple_GetSlice(PyObject *p, Py_ssize_t low, Py_ssize_t high) *Return value: New reference.** Part of the Stable ABI.* Return the slice of the tuple pointed to by *p* between *low* and *high*, or "NULL" with an exception set on failure. This is the equivalent of the Python expression "p[low:high]". Indexing from the end of the tuple is not supported. int PyTuple_SetItem(PyObject *p, Py_ssize_t pos, PyObject *o) * Part of the Stable ABI.* Insert a reference to object *o* at position *pos* of the tuple pointed to by *p*. Return "0" on success. If *pos* is out of bounds, return "-1" and set an "IndexError" exception. Note: This function "steals" a reference to *o* and discards a reference to an item already in the tuple at the affected position. void PyTuple_SET_ITEM(PyObject *p, Py_ssize_t pos, PyObject *o) Like "PyTuple_SetItem()", but does no error checking, and should *only* be used to fill in brand new tuples. Bounds checking is performed as an assertion if Python is built in debug mode or "with assertions". Note: This function "steals" a reference to *o*, and, unlike "PyTuple_SetItem()", does *not* discard a reference to any item that is being replaced; any reference in the tuple at position *pos* will be leaked. int _PyTuple_Resize(PyObject **p, Py_ssize_t newsize) Can be used to resize a tuple. *newsize* will be the new length of the tuple. Because tuples are *supposed* to be immutable, this should only be used if there is only one reference to the object. Do *not* use this if the tuple may already be known to some other part of the code. The tuple will always grow or shrink at the end. Think of this as destroying the old tuple and creating a new one, only more efficiently. Returns "0" on success. Client code should never assume that the resulting value of "*p" will be the same as before calling this function. If the object referenced by "*p" is replaced, the original "*p" is destroyed. On failure, returns "-1" and sets "*p" to "NULL", and raises "MemoryError" or "SystemError". Struct Sequence Objects *********************** Struct sequence objects are the C equivalent of "namedtuple()" objects, i.e. a sequence whose items can also be accessed through attributes. To create a struct sequence, you first have to create a specific struct sequence type. PyTypeObject *PyStructSequence_NewType(PyStructSequence_Desc *desc) *Return value: New reference.** Part of the Stable ABI.* Create a new struct sequence type from the data in *desc*, described below. Instances of the resulting type can be created with "PyStructSequence_New()". Return "NULL" with an exception set on failure. void PyStructSequence_InitType(PyTypeObject *type, PyStructSequence_Desc *desc) Initializes a struct sequence type *type* from *desc* in place. int PyStructSequence_InitType2(PyTypeObject *type, PyStructSequence_Desc *desc) Like "PyStructSequence_InitType()", but returns "0" on success and "-1" with an exception set on failure. Added in version 3.4. type PyStructSequence_Desc * Part of the Stable ABI (including all members).* Contains the meta information of a struct sequence type to create. const char *name Fully qualified name of the type; null-terminated UTF-8 encoded. The name must contain the module name. const char *doc Pointer to docstring for the type or "NULL" to omit. PyStructSequence_Field *fields Pointer to "NULL"-terminated array with field names of the new type. int n_in_sequence Number of fields visible to the Python side (if used as tuple). type PyStructSequence_Field * Part of the Stable ABI (including all members).* Describes a field of a struct sequence. As a struct sequence is modeled as a tuple, all fields are typed as PyObject*. The index in the "fields" array of the "PyStructSequence_Desc" determines which field of the struct sequence is described. const char *name Name for the field or "NULL" to end the list of named fields, set to "PyStructSequence_UnnamedField" to leave unnamed. const char *doc Field docstring or "NULL" to omit. const char *const PyStructSequence_UnnamedField * Part of the Stable ABI since version 3.11.* Special value for a field name to leave it unnamed. Changed in version 3.9: The type was changed from "char *". PyObject *PyStructSequence_New(PyTypeObject *type) *Return value: New reference.** Part of the Stable ABI.* Creates an instance of *type*, which must have been created with "PyStructSequence_NewType()". Return "NULL" with an exception set on failure. PyObject *PyStructSequence_GetItem(PyObject *p, Py_ssize_t pos) *Return value: Borrowed reference.** Part of the Stable ABI.* Return the object at position *pos* in the struct sequence pointed to by *p*. Bounds checking is performed as an assertion if Python is built in debug mode or "with assertions". PyObject *PyStructSequence_GET_ITEM(PyObject *p, Py_ssize_t pos) *Return value: Borrowed reference.* Alias to "PyStructSequence_GetItem()". Changed in version 3.13: Now implemented as an alias to "PyStructSequence_GetItem()". void PyStructSequence_SetItem(PyObject *p, Py_ssize_t pos, PyObject *o) * Part of the Stable ABI.* Sets the field at index *pos* of the struct sequence *p* to value *o*. Like "PyTuple_SET_ITEM()", this should only be used to fill in brand new instances. Bounds checking is performed as an assertion if Python is built in debug mode or "with assertions". Note: This function "steals" a reference to *o*. void PyStructSequence_SET_ITEM(PyObject *p, Py_ssize_t *pos, PyObject *o) Alias to "PyStructSequence_SetItem()". Changed in version 3.13: Now implemented as an alias to "PyStructSequence_SetItem()".