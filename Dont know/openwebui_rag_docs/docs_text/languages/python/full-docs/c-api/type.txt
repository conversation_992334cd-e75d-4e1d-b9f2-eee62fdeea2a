Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > type.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > type.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > type.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > type.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > type.txt Type Objects ************ type PyTypeObject * Part of the Limited API (as an opaque struct).* The C structure of the objects used to describe built-in types. PyTypeObject PyType_Type * Part of the Stable ABI.* This is the type object for type objects; it is the same object as "type" in the Python layer. int PyType_Check(PyObject *o) Return non-zero if the object *o* is a type object, including instances of types derived from the standard type object. Return 0 in all other cases. This function always succeeds. int PyType_CheckExact(PyObject *o) Return non-zero if the object *o* is a type object, but not a subtype of the standard type object. Return 0 in all other cases. This function always succeeds. unsigned int PyType_ClearCache() * Part of the Stable ABI.* Clear the internal lookup cache. Return the current version tag. unsigned long PyType_GetFlags(PyTypeObject *type) * Part of the Stable ABI.* Return the "tp_flags" member of *type*. This function is primarily meant for use with "Py_LIMITED_API"; the individual flag bits are guaranteed to be stable across Python releases, but access to "tp_flags" itself is not part of the limited API. Added in version 3.2. Changed in version 3.4: The return type is now "unsigned long" rather than "long". PyObject *PyType_GetDict(PyTypeObject *type) Return the type object's internal namespace, which is otherwise only exposed via a read-only proxy ("cls.__dict__"). This is a replacement for accessing "tp_dict" directly. The returned dictionary must be treated as read-only. This function is meant for specific embedding and language-binding cases, where direct access to the dict is necessary and indirect access (e.g. via the proxy or "PyObject_GetAttr()") isn't adequate. Extension modules should continue to use "tp_dict", directly or indirectly, when setting up their own types. Added in version 3.12. void PyType_Modified(PyTypeObject *type) * Part of the Stable ABI.* Invalidate the internal lookup cache for the type and all of its subtypes. This function must be called after any manual modification of the attributes or base classes of the type. int PyType_AddWatcher(PyType_WatchCallback callback) Register *callback* as a type watcher. Return a non-negative integer ID which must be passed to future calls to "PyType_Watch()". In case of error (e.g. no more watcher IDs available), return "-1" and set an exception. In free-threaded builds, "PyType_AddWatcher()" is not thread-safe, so it must be called at start up (before spawning the first thread). Added in version 3.12. int PyType_ClearWatcher(int watcher_id) Clear watcher identified by *watcher_id* (previously returned from "PyType_AddWatcher()"). Return "0" on success, "-1" on error (e.g. if *watcher_id* was never registered.) An extension should never call "PyType_ClearWatcher" with a *watcher_id* that was not returned to it by a previous call to "PyType_AddWatcher()". Added in version 3.12. int PyType_Watch(int watcher_id, PyObject *type) Mark *type* as watched. The callback granted *watcher_id* by "PyType_AddWatcher()" will be called whenever "PyType_Modified()" reports a change to *type*. (The callback may be called only once for a series of consecutive modifications to *type*, if "_PyType_Lookup()" is not called on *type* between the modifications; this is an implementation detail and subject to change.) An extension should never call "PyType_Watch" with a *watcher_id* that was not returned to it by a previous call to "PyType_AddWatcher()". Added in version 3.12. typedef int (*PyType_WatchCallback)(PyObject *type) Type of a type-watcher callback function. The callback must not modify *type* or cause "PyType_Modified()" to be called on *type* or any type in its MRO; violating this rule could cause infinite recursion. Added in version 3.12. int PyType_HasFeature(PyTypeObject *o, int feature) Return non-zero if the type object *o* sets the feature *feature*. Type features are denoted by single bit flags. int PyType_IS_GC(PyTypeObject *o) Return true if the type object includes support for the cycle detector; this tests the type flag "Py_TPFLAGS_HAVE_GC". int PyType_IsSubtype(PyTypeObject *a, PyTypeObject *b) * Part of the Stable ABI.* Return true if *a* is a subtype of *b*. This function only checks for actual subtypes, which means that "__subclasscheck__()" is not called on *b*. Call "PyObject_IsSubclass()" to do the same check that "issubclass()" would do. PyObject *PyType_GenericAlloc(PyTypeObject *type, Py_ssize_t nitems) *Return value: New reference.** Part of the Stable ABI.* Generic handler for the "tp_alloc" slot of a type object. Use Python's default memory allocation mechanism to allocate a new instance and initialize all its contents to "NULL". PyObject *PyType_GenericNew(PyTypeObject *type, PyObject *args, PyObject *kwds) *Return value: New reference.** Part of the Stable ABI.* Generic handler for the "tp_new" slot of a type object. Create a new instance using the type's "tp_alloc" slot. int PyType_Ready(PyTypeObject *type) * Part of the Stable ABI.* Finalize a type object. This should be called on all type objects to finish their initialization. This function is responsible for adding inherited slots from a type's base class. Return "0" on success, or return "-1" and sets an exception on error. Note: If some of the base classes implements the GC protocol and the provided type does not include the "Py_TPFLAGS_HAVE_GC" in its flags, then the GC protocol will be automatically implemented from its parents. On the contrary, if the type being created does include "Py_TPFLAGS_HAVE_GC" in its flags then it **must** implement the GC protocol itself by at least implementing the "tp_traverse" handle. PyObject *PyType_GetName(PyTypeObject *type) *Return value: New reference.** Part of the Stable ABI since version 3.11.* Return the type's name. Equivalent to getting the type's "__name__" attribute. Added in version 3.11. PyObject *PyType_GetQualName(PyTypeObject *type) *Return value: New reference.** Part of the Stable ABI since version 3.11.* Return the type's qualified name. Equivalent to getting the type's "__qualname__" attribute. Added in version 3.11. PyObject *PyType_GetFullyQualifiedName(PyTypeObject *type) * Part of the Stable ABI since version 3.13.* Return the type's fully qualified name. Equivalent to "f"{type.__module__}.{type.__qualname__}"", or "type.__qualname__" if "type.__module__" is not a string or is equal to ""builtins"". Added in version 3.13. PyObject *PyType_GetModuleName(PyTypeObject *type) * Part of the Stable ABI since version 3.13.* Return the type's module name. Equivalent to getting the "type.__module__" attribute. Added in version 3.13. void *PyType_GetSlot(PyTypeObject *type, int slot) * Part of the Stable ABI since version 3.4.* Return the function pointer stored in the given slot. If the result is "NULL", this indicates that either the slot is "NULL", or that the function was called with invalid parameters. Callers will typically cast the result pointer into the appropriate function type. See "PyType_Slot.slot" for possible values of the *slot* argument. Added in version 3.4. Changed in version 3.10: "PyType_GetSlot()" can now accept all types. Previously, it was limited to heap types. PyObject *PyType_GetModule(PyTypeObject *type) * Part of the Stable ABI since version 3.10.* Return the module object associated with the given type when the type was created using "PyType_FromModuleAndSpec()". If no module is associated with the given type, sets "TypeError" and returns "NULL". This function is usually used to get the module in which a method is defined. Note that in such a method, "PyType_GetModule(Py_TYPE(self))" may not return the intended result. "Py_TYPE(self)" may be a *subclass* of the intended class, and subclasses are not necessarily defined in the same module as their superclass. See "PyCMethod" to get the class that defines the method. See "PyType_GetModuleByDef()" for cases when "PyCMethod" cannot be used. Added in version 3.9. void *PyType_GetModuleState(PyTypeObject *type) * Part of the Stable ABI since version 3.10.* Return the state of the module object associated with the given type. This is a shortcut for calling "PyModule_GetState()" on the result of "PyType_GetModule()". If no module is associated with the given type, sets "TypeError" and returns "NULL". If the *type* has an associated module but its state is "NULL", returns "NULL" without setting an exception. Added in version 3.9. PyObject *PyType_GetModuleByDef(PyTypeObject *type, struct PyModuleDef *def) * Part of the Stable ABI since version 3.13.* Find the first superclass whose module was created from the given "PyModuleDef" *def*, and return that module. If no module is found, raises a "TypeError" and returns "NULL". This function is intended to be used together with "PyModule_GetState()" to get module state from slot methods (such as "tp_init" or "nb_add") and other places where a method's defining class cannot be passed using the "PyCMethod" calling convention. Added in version 3.11. int PyUnstable_Type_AssignVersionTag(PyTypeObject *type) *This is Unstable API. It may change without warning in minor releases.* Attempt to assign a version tag to the given type. Returns 1 if the type already had a valid version tag or a new one was assigned, or 0 if a new tag could not be assigned. Added in version 3.12. Creating Heap-Allocated Types ============================= The following functions and structs are used to create heap types. PyObject *PyType_FromMetaclass(PyTypeObject *metaclass, PyObject *module, PyType_Spec *spec, PyObject *bases) * Part of the Stable ABI since version 3.12.* Create and return a heap type from the *spec* (see "Py_TPFLAGS_HEAPTYPE"). The metaclass *metaclass* is used to construct the resulting type object. When *metaclass* is "NULL", the metaclass is derived from *bases* (or *Py_tp_base[s]* slots if *bases* is "NULL", see below). Metaclasses that override "tp_new" are not supported, except if "tp_new" is "NULL". (For backwards compatibility, other "PyType_From*" functions allow such metaclasses. They ignore "tp_new", which may result in incomplete initialization. This is deprecated and in Python 3.14+ such metaclasses will not be supported.) The *bases* argument can be used to specify base classes; it can either be only one class or a tuple of classes. If *bases* is "NULL", the *Py_tp_bases* slot is used instead. If that also is "NULL", the *Py_tp_base* slot is used instead. If that also is "NULL", the new type derives from "object". The *module* argument can be used to record the module in which the new class is defined. It must be a module object or "NULL". If not "NULL", the module is associated with the new type and can later be retrieved with "PyType_GetModule()". The associated module is not inherited by subclasses; it must be specified for each class individually. This function calls "PyType_Ready()" on the new type. Note that this function does *not* fully match the behavior of calling "type()" or using the "class" statement. With user-provided base types or metaclasses, prefer calling "type" (or the metaclass) over "PyType_From*" functions. Specifically: * "__new__()" is not called on the new class (and it must be set to "type.__new__"). * "__init__()" is not called on the new class. * "__init_subclass__()" is not called on any bases. * "__set_name__()" is not called on new descriptors. Added in version 3.12. PyObject *PyType_FromModuleAndSpec(PyObject *module, PyType_Spec *spec, PyObject *bases) *Return value: New reference.** Part of the Stable ABI since version 3.10.* Equivalent to "PyType_FromMetaclass(NULL, module, spec, bases)". Added in version 3.9. Changed in version 3.10: The function now accepts a single class as the *bases* argument and "NULL" as the "tp_doc" slot. Changed in version 3.12: The function now finds and uses a metaclass corresponding to the provided base classes. Previously, only "type" instances were returned.The "tp_new" of the metaclass is *ignored*. which may result in incomplete initialization. Creating classes whose metaclass overrides "tp_new" is deprecated and in Python 3.14+ it will be no longer allowed. PyObject *PyType_FromSpecWithBases(PyType_Spec *spec, PyObject *bases) *Return value: New reference.** Part of the Stable ABI since version 3.3.* Equivalent to "PyType_FromMetaclass(NULL, NULL, spec, bases)". Added in version 3.3. Changed in version 3.12: The function now finds and uses a metaclass corresponding to the provided base classes. Previously, only "type" instances were returned.The "tp_new" of the metaclass is *ignored*. which may result in incomplete initialization. Creating classes whose metaclass overrides "tp_new" is deprecated and in Python 3.14+ it will be no longer allowed. PyObject *PyType_FromSpec(PyType_Spec *spec) *Return value: New reference.** Part of the Stable ABI.* Equivalent to "PyType_FromMetaclass(NULL, NULL, spec, NULL)". Changed in version 3.12: The function now finds and uses a metaclass corresponding to the base classes provided in *Py_tp_base[s]* slots. Previously, only "type" instances were returned.The "tp_new" of the metaclass is *ignored*. which may result in incomplete initialization. Creating classes whose metaclass overrides "tp_new" is deprecated and in Python 3.14+ it will be no longer allowed. type PyType_Spec * Part of the Stable ABI (including all members).* Structure defining a type's behavior. const char *name Name of the type, used to set "PyTypeObject.tp_name". int basicsize If positive, specifies the size of the instance in bytes. It is used to set "PyTypeObject.tp_basicsize". If zero, specifies that "tp_basicsize" should be inherited. If negative, the absolute value specifies how much space instances of the class need *in addition* to the superclass. Use "PyObject_GetTypeData()" to get a pointer to subclass-specific memory reserved this way. For negative "basicsize", Python will insert padding when needed to meet "tp_basicsize"'s alignment requirements. Changed in version 3.12: Previously, this field could not be negative. int itemsize Size of one element of a variable-size type, in bytes. Used to set "PyTypeObject.tp_itemsize". See "tp_itemsize" documentation for caveats. If zero, "tp_itemsize" is inherited. Extending arbitrary variable-sized classes is dangerous, since some types use a fixed offset for variable-sized memory, which can then overlap fixed-sized memory used by a subclass. To help prevent mistakes, inheriting "itemsize" is only possible in the following situations: * The base is not variable-sized (its "tp_itemsize"). * The requested "PyType_Spec.basicsize" is positive, suggesting that the memory layout of the base class is known. * The requested "PyType_Spec.basicsize" is zero, suggesting that the subclass does not access the instance's memory directly. * With the "Py_TPFLAGS_ITEMS_AT_END" flag. unsigned int flags Type flags, used to set "PyTypeObject.tp_flags". If the "Py_TPFLAGS_HEAPTYPE" flag is not set, "PyType_FromSpecWithBases()" sets it automatically. PyType_Slot *slots Array of "PyType_Slot" structures. Terminated by the special slot value "{0, NULL}". Each slot ID should be specified at most once. type PyType_Slot * Part of the Stable ABI (including all members).* Structure defining optional functionality of a type, containing a slot ID and a value pointer. int slot A slot ID. Slot IDs are named like the field names of the structures "PyTypeObject", "PyNumberMethods", "PySequenceMethods", "PyMappingMethods" and "PyAsyncMethods" with an added "Py_" prefix. For example, use: * "Py_tp_dealloc" to set "PyTypeObject.tp_dealloc" * "Py_nb_add" to set "PyNumberMethods.nb_add" * "Py_sq_length" to set "PySequenceMethods.sq_length" The following offset fields cannot be set using "PyType_Slot": * "tp_weaklistoffset" (use "Py_TPFLAGS_MANAGED_WEAKREF" instead if possible) * "tp_dictoffset" (use "Py_TPFLAGS_MANAGED_DICT" instead if possible) * "tp_vectorcall_offset" (use ""__vectorcalloffset__"" in PyMemberDef) If it is not possible to switch to a "MANAGED" flag (for example, for vectorcall or to support Python older than 3.12), specify the offset in "Py_tp_members". See PyMemberDef documentation for details. The following fields cannot be set at all when creating a heap type: * "tp_vectorcall" (use "tp_new" and/or "tp_init") * Internal fields: "tp_dict", "tp_mro", "tp_cache", "tp_subclasses", and "tp_weaklist". Setting "Py_tp_bases" or "Py_tp_base" may be problematic on some platforms. To avoid issues, use the *bases* argument of "PyType_FromSpecWithBases()" instead. Changed in version 3.9: Slots in "PyBufferProcs" may be set in the unlimited API. Changed in version 3.11: "bf_getbuffer" and "bf_releasebuffer" are now available under the limited API. void *pfunc The desired value of the slot. In most cases, this is a pointer to a function. Slots other than "Py_tp_doc" may not be "NULL".