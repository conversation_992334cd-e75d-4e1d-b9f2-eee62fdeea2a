Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > typeobj.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > typeobj.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > typeobj.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > typeobj.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > typeobj.txt Type Object Structures ********************** Perhaps one of the most important structures of the Python object system is the structure that defines a new type: the "PyTypeObject" structure. Type objects can be handled using any of the "PyObject_*" or "PyType_*" functions, but do not offer much that's interesting to most Python applications. These objects are fundamental to how objects behave, so they are very important to the interpreter itself and to any extension module that implements new types. Type objects are fairly large compared to most of the standard types. The reason for the size is that each type object stores a large number of values, mostly C function pointers, each of which implements a small part of the type's functionality. The fields of the type object are examined in detail in this section. The fields will be described in the order in which they occur in the structure. In addition to the following quick reference, the Examples section provides at-a-glance insight into the meaning and use of "PyTypeObject". Quick Reference =============== "tp slots" ---------- +--------------------+--------------------+--------------------+----+----+----+----+ | PyTypeObject Slot | Type | special | Info [2] | | [1] | | methods/attrs | | | | | +----+----+----+----+ | | | | O | T | D | I | | | | | | | | | |====================|====================|====================|====|====|====|====| | <R> "tp_name" | const char * | __name__ | X | X | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_basicsize" | "Py_ssize_t" | | X | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_itemsize" | "Py_ssize_t" | | | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_dealloc" | "destructor" | | X | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_vectorcall_of | "Py_ssize_t" | | | X | | X | | fset" | | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ("tp_getattr") | "getattrfunc" | __getattribute__, | | | | G | | | | __getattr__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ("tp_setattr") | "setattrfunc" | __setattr__, | | | | G | | | | __delattr__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_as_async" | "PyAsyncMethods" * | sub-slots | | | | % | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_repr" | "reprfunc" | __repr__ | X | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_as_number" | "PyNumberMethods" | sub-slots | | | | % | | | * | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_as_sequence" | "PySequenceMethod | sub-slots | | | | % | | | s" * | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_as_mapping" | "PyMappingMethods" | sub-slots | | | | % | | | * | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_hash" | "hashfunc" | __hash__ | X | | | G | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_call" | "ternaryfunc" | __call__ | | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_str" | "reprfunc" | __str__ | X | | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_getattro" | "getattrofunc" | __getattribute__, | X | X | | G | | | | __getattr__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_setattro" | "setattrofunc" | __setattr__, | X | X | | G | | | | __delattr__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_as_buffer" | "PyBufferProcs" * | | | | | % | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_flags" | unsigned long | | X | X | | ? | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_doc" | const char * | __doc__ | X | X | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_traverse" | "traverseproc" | | | X | | G | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_clear" | "inquiry" | | | X | | G | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_richcompare" | "richcmpfunc" | __lt__, __le__, | X | | | G | | | | __eq__, __ne__, | | | | | | | | __gt__, __ge__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ("tp_weaklistoffs | "Py_ssize_t" | | | X | | ? | | et") | | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_iter" | "getiterfunc" | __iter__ | | | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_iternext" | "iternextfunc" | __next__ | | | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_methods" | "PyMethodDef" [] | | X | X | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_members" | "PyMemberDef" [] | | | X | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_getset" | "PyGetSetDef" [] | | X | X | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_base" | "PyTypeObject" * | __base__ | | | X | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_dict" | "PyObject" * | __dict__ | | | ? | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_descr_get" | "descrgetfunc" | __get__ | | | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_descr_set" | "descrsetfunc" | __set__, | | | | X | | | | __delete__ | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ("tp_dictoffset") | "Py_ssize_t" | | | X | | ? | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_init" | "initproc" | __init__ | X | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_alloc" | "allocfunc" | | X | | ? | ? | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_new" | "newfunc" | __new__ | X | X | ? | ? | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_free" | "freefunc" | | X | X | ? | ? | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_is_gc" | "inquiry" | | | X | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | <"tp_bases"> | "PyObject" * | __bases__ | | | ~ | | +--------------------+--------------------+--------------------+----+----+----+----+ | <"tp_mro"> | "PyObject" * | __mro__ | | | ~ | | +--------------------+--------------------+--------------------+----+----+----+----+ | ["tp_cache"] | "PyObject" * | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ["tp_subclasses"] | void * | __subclasses__ | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ["tp_weaklist"] | "PyObject" * | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ("tp_del") | "destructor" | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ["tp_version_tag"] | unsigned int | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_finalize" | "destructor" | __del__ | | | | X | +--------------------+--------------------+--------------------+----+----+----+----+ | "tp_vectorcall" | "vectorcallfunc" | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ | ["tp_watched"] | unsigned char | | | | | | +--------------------+--------------------+--------------------+----+----+----+----+ [1] **()**: A slot name in parentheses indicates it is (effectively) deprecated. **<>**: Names in angle brackets should be initially set to "NULL" and treated as read-only. **[]**: Names in square brackets are for internal use only. **<R>** (as a prefix) means the field is required (must be non-"NULL"). [2] Columns: **"O"**: set on "PyBaseObject_Type" **"T"**: set on "PyType_Type" **"D"**: default (if slot is set to "NULL") X - PyType_Ready sets this value if it is NULL ~ - PyType_Ready always sets this value (it should be NULL) ? - PyType_Ready may set this value depending on other slots Also see the inheritance column ("I"). **"I"**: inheritance X - type slot is inherited via *PyType_Ready* if defined with a *NULL* value % - the slots of the sub-struct are inherited individually G - inherited, but only in combination with other slots; see the slot's description ? - it's complicated; see the slot's description Note that some slots are effectively inherited through the normal attribute lookup chain. sub-slots --------- +----------------------------+-------------------+--------------+ | Slot | Type | special | | | | methods | |============================|===================|==============| | "am_await" | "unaryfunc" | __await__ | +----------------------------+-------------------+--------------+ | "am_aiter" | "unaryfunc" | __aiter__ | +----------------------------+-------------------+--------------+ | "am_anext" | "unaryfunc" | __anext__ | +----------------------------+-------------------+--------------+ | "am_send" | "sendfunc" | | +----------------------------+-------------------+--------------+ | | +----------------------------+-------------------+--------------+ | "nb_add" | "binaryfunc" | __add__ | | | | __radd__ | +----------------------------+-------------------+--------------+ | "nb_inplace_add" | "binaryfunc" | __iadd__ | +----------------------------+-------------------+--------------+ | "nb_subtract" | "binaryfunc" | __sub__ | | | | __rsub__ | +----------------------------+-------------------+--------------+ | "nb_inplace_subtract" | "binaryfunc" | __isub__ | +----------------------------+-------------------+--------------+ | "nb_multiply" | "binaryfunc" | __mul__ | | | | __rmul__ | +----------------------------+-------------------+--------------+ | "nb_inplace_multiply" | "binaryfunc" | __imul__ | +----------------------------+-------------------+--------------+ | "nb_remainder" | "binaryfunc" | __mod__ | | | | __rmod__ | +----------------------------+-------------------+--------------+ | "nb_inplace_remainder" | "binaryfunc" | __imod__ | +----------------------------+-------------------+--------------+ | "nb_divmod" | "binaryfunc" | __divmod__ | | | | __rdivmod__ | +----------------------------+-------------------+--------------+ | "nb_power" | "ternaryfunc" | __pow__ | | | | __rpow__ | +----------------------------+-------------------+--------------+ | "nb_inplace_power" | "ternaryfunc" | __ipow__ | +----------------------------+-------------------+--------------+ | "nb_negative" | "unaryfunc" | __neg__ | +----------------------------+-------------------+--------------+ | "nb_positive" | "unaryfunc" | __pos__ | +----------------------------+-------------------+--------------+ | "nb_absolute" | "unaryfunc" | __abs__ | +----------------------------+-------------------+--------------+ | "nb_bool" | "inquiry" | __bool__ | +----------------------------+-------------------+--------------+ | "nb_invert" | "unaryfunc" | __invert__ | +----------------------------+-------------------+--------------+ | "nb_lshift" | "binaryfunc" | __lshift__ | | | | __rlshift__ | +----------------------------+-------------------+--------------+ | "nb_inplace_lshift" | "binaryfunc" | __ilshift__ | +----------------------------+-------------------+--------------+ | "nb_rshift" | "binaryfunc" | __rshift__ | | | | __rrshift__ | +----------------------------+-------------------+--------------+ | "nb_inplace_rshift" | "binaryfunc" | __irshift__ | +----------------------------+-------------------+--------------+ | "nb_and" | "binaryfunc" | __and__ | | | | __rand__ | +----------------------------+-------------------+--------------+ | "nb_inplace_and" | "binaryfunc" | __iand__ | +----------------------------+-------------------+--------------+ | "nb_xor" | "binaryfunc" | __xor__ | | | | __rxor__ | +----------------------------+-------------------+--------------+ | "nb_inplace_xor" | "binaryfunc" | __ixor__ | +----------------------------+-------------------+--------------+ | "nb_or" | "binaryfunc" | __or__ | | | | __ror__ | +----------------------------+-------------------+--------------+ | "nb_inplace_or" | "binaryfunc" | __ior__ | +----------------------------+-------------------+--------------+ | "nb_int" | "unaryfunc" | __int__ | +----------------------------+-------------------+--------------+ | "nb_reserved" | void * | | +----------------------------+-------------------+--------------+ | "nb_float" | "unaryfunc" | __float__ | +----------------------------+-------------------+--------------+ | "nb_floor_divide" | "binaryfunc" | __floordiv__ | +----------------------------+-------------------+--------------+ | "nb_inplace_floor_divide" | "binaryfunc" | __ifloordiv | | | | __ | +----------------------------+-------------------+--------------+ | "nb_true_divide" | "binaryfunc" | __truediv__ | +----------------------------+-------------------+--------------+ | "nb_inplace_true_divide" | "binaryfunc" | __itruediv__ | +----------------------------+-------------------+--------------+ | "nb_index" | "unaryfunc" | __index__ | +----------------------------+-------------------+--------------+ | "nb_matrix_multiply" | "binaryfunc" | __matmul__ | | | | __rmatmul__ | +----------------------------+-------------------+--------------+ | "nb_inplace_matrix_multip | "binaryfunc" | __imatmul__ | | ly" | | | +----------------------------+-------------------+--------------+ | | +----------------------------+-------------------+--------------+ | "mp_length" | "lenfunc" | __len__ | +----------------------------+-------------------+--------------+ | "mp_subscript" | "binaryfunc" | __getitem__ | +----------------------------+-------------------+--------------+ | "mp_ass_subscript" | "objobjargproc" | __setitem__, | | | | __delitem__ | +----------------------------+-------------------+--------------+ | | +----------------------------+-------------------+--------------+ | "sq_length" | "lenfunc" | __len__ | +----------------------------+-------------------+--------------+ | "sq_concat" | "binaryfunc" | __add__ | +----------------------------+-------------------+--------------+ | "sq_repeat" | "ssizeargfunc" | __mul__ | +----------------------------+-------------------+--------------+ | "sq_item" | "ssizeargfunc" | __getitem__ | +----------------------------+-------------------+--------------+ | "sq_ass_item" | "ssizeobjargproc" | __setitem__ | | | | __delitem__ | +----------------------------+-------------------+--------------+ | "sq_contains" | "objobjproc" | __contains__ | +----------------------------+-------------------+--------------+ | "sq_inplace_concat" | "binaryfunc" | __iadd__ | +----------------------------+-------------------+--------------+ | "sq_inplace_repeat" | "ssizeargfunc" | __imul__ | +----------------------------+-------------------+--------------+ | | +----------------------------+-------------------+--------------+ | "bf_getbuffer" | "getbufferproc()" | | +----------------------------+-------------------+--------------+ | "bf_releasebuffer" | "releasebufferpr | | | | oc()" | | +----------------------------+-------------------+--------------+ slot typedefs ------------- +-------------------------------+-------------------------------+------------------------+ | typedef | Parameter Types | Return Type | |===============================|===============================|========================| | "allocfunc" | "PyTypeObject" * "Py_ssize_t" | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "destructor" | "PyObject" * | void | +-------------------------------+-------------------------------+------------------------+ | "freefunc" | void * | void | +-------------------------------+-------------------------------+------------------------+ | "traverseproc" | "PyObject" * "visitproc" void | int | | | * | | +-------------------------------+-------------------------------+------------------------+ | "newfunc" | "PyTypeObject" * "PyObject" * | "PyObject" * | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "initproc" | "PyObject" * "PyObject" * | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "reprfunc" | "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "getattrfunc" | "PyObject" * const char * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "setattrfunc" | "PyObject" * const char * | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "getattrofunc" | "PyObject" * "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "setattrofunc" | "PyObject" * "PyObject" * | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "descrgetfunc" | "PyObject" * "PyObject" * | "PyObject" * | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "descrsetfunc" | "PyObject" * "PyObject" * | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "hashfunc" | "PyObject" * | Py_hash_t | +-------------------------------+-------------------------------+------------------------+ | "richcmpfunc" | "PyObject" * "PyObject" * int | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "getiterfunc" | "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "iternextfunc" | "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "lenfunc" | "PyObject" * | "Py_ssize_t" | +-------------------------------+-------------------------------+------------------------+ | "getbufferproc" | "PyObject" * "Py_buffer" * | int | | | int | | +-------------------------------+-------------------------------+------------------------+ | "releasebufferproc" | "PyObject" * "Py_buffer" * | void | +-------------------------------+-------------------------------+------------------------+ | "inquiry" | "PyObject" * | int | +-------------------------------+-------------------------------+------------------------+ | "unaryfunc" | "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "binaryfunc" | "PyObject" * "PyObject" * | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "ternaryfunc" | "PyObject" * "PyObject" * | "PyObject" * | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "ssizeargfunc" | "PyObject" * "Py_ssize_t" | "PyObject" * | +-------------------------------+-------------------------------+------------------------+ | "ssizeobjargproc" | "PyObject" * "Py_ssize_t" | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ | "objobjproc" | "PyObject" * "PyObject" * | int | +-------------------------------+-------------------------------+------------------------+ | "objobjargproc" | "PyObject" * "PyObject" * | int | | | "PyObject" * | | +-------------------------------+-------------------------------+------------------------+ See Slot Type typedefs below for more detail. PyTypeObject Definition ======================= The structure definition for "PyTypeObject" can be found in "Include/cpython/object.h". For convenience of reference, this repeats the definition found there: typedef struct _typeobject { PyObject_VAR_HEAD const char *tp_name; /* For printing, in format "<module>.<name>" */ Py_ssize_t tp_basicsize, tp_itemsize; /* For allocation */ /* Methods to implement standard operations */ destructor tp_dealloc; Py_ssize_t tp_vectorcall_offset; getattrfunc tp_getattr; setattrfunc tp_setattr; PyAsyncMethods *tp_as_async; /* formerly known as tp_compare (Python 2) or tp_reserved (Python 3) */ reprfunc tp_repr; /* Method suites for standard classes */ PyNumberMethods *tp_as_number; PySequenceMethods *tp_as_sequence; PyMappingMethods *tp_as_mapping; /* More standard operations (here for binary compatibility) */ hashfunc tp_hash; ternaryfunc tp_call; reprfunc tp_str; getattrofunc tp_getattro; setattrofunc tp_setattro; /* Functions to access object as input/output buffer */ PyBufferProcs *tp_as_buffer; /* Flags to define presence of optional/expanded features */ unsigned long tp_flags; const char *tp_doc; /* Documentation string */ /* Assigned meaning in release 2.0 */ /* call function for all accessible objects */ traverseproc tp_traverse; /* delete references to contained objects */ inquiry tp_clear; /* Assigned meaning in release 2.1 */ /* rich comparisons */ richcmpfunc tp_richcompare; /* weak reference enabler */ Py_ssize_t tp_weaklistoffset; /* Iterators */ getiterfunc tp_iter; iternextfunc tp_iternext; /* Attribute descriptor and subclassing stuff */ struct PyMethodDef *tp_methods; struct PyMemberDef *tp_members; struct PyGetSetDef *tp_getset; // Strong reference on a heap type, borrowed reference on a static type struct _typeobject *tp_base; PyObject *tp_dict; descrgetfunc tp_descr_get; descrsetfunc tp_descr_set; Py_ssize_t tp_dictoffset; initproc tp_init; allocfunc tp_alloc; newfunc tp_new; freefunc tp_free; /* Low-level free-memory routine */ inquiry tp_is_gc; /* For PyObject_IS_GC */ PyObject *tp_bases; PyObject *tp_mro; /* method resolution order */ PyObject *tp_cache; PyObject *tp_subclasses; PyObject *tp_weaklist; destructor tp_del; /* Type attribute cache version tag. Added in version 2.6 */ unsigned int tp_version_tag; destructor tp_finalize; vectorcallfunc tp_vectorcall; /* bitset of which type-watchers care about this type */ unsigned char tp_watched; } PyTypeObject; PyObject Slots ============== The type object structure extends the "PyVarObject" structure. The "ob_size" field is used for dynamic types (created by "type_new()", usually called from a class statement). Note that "PyType_Type" (the metatype) initializes "tp_itemsize", which means that its instances (i.e. type objects) *must* have the "ob_size" field. Py_ssize_t PyObject.ob_refcnt * Part of the Stable ABI.* This is the type object's reference count, initialized to "1" by the "PyObject_HEAD_INIT" macro. Note that for statically allocated type objects, the type's instances (objects whose "ob_type" points back to the type) do *not* count as references. But for dynamically allocated type objects, the instances *do* count as references. **Inheritance:** This field is not inherited by subtypes. PyTypeObject *PyObject.ob_type * Part of the Stable ABI.* This is the type's type, in other words its metatype. It is initialized by the argument to the "PyObject_HEAD_INIT" macro, and its value should normally be "&PyType_Type". However, for dynamically loadable extension modules that must be usable on Windows (at least), the compiler complains that this is not a valid initializer. Therefore, the convention is to pass "NULL" to the "PyObject_HEAD_INIT" macro and to initialize this field explicitly at the start of the module's initialization function, before doing anything else. This is typically done like this: Foo_Type.ob_type = &PyType_Type; This should be done before any instances of the type are created. "PyType_Ready()" checks if "ob_type" is "NULL", and if so, initializes it to the "ob_type" field of the base class. "PyType_Ready()" will not change this field if it is non-zero. **Inheritance:** This field is inherited by subtypes. PyVarObject Slots ================= Py_ssize_t PyVarObject.ob_size * Part of the Stable ABI.* For statically allocated type objects, this should be initialized to zero. For dynamically allocated type objects, this field has a special internal meaning. This field should be accessed using the "Py_SIZE()" and "Py_SET_SIZE()" macros. **Inheritance:** This field is not inherited by subtypes. PyTypeObject Slots ================== Each slot has a section describing inheritance. If "PyType_Ready()" may set a value when the field is set to "NULL" then there will also be a "Default" section. (Note that many fields set on "PyBaseObject_Type" and "PyType_Type" effectively act as defaults.) const char *PyTypeObject.tp_name Pointer to a NUL-terminated string containing the name of the type. For types that are accessible as module globals, the string should be the full module name, followed by a dot, followed by the type name; for built-in types, it should be just the type name. If the module is a submodule of a package, the full package name is part of the full module name. For example, a type named "T" defined in module "M" in subpackage "Q" in package "P" should have the "tp_name" initializer ""P.Q.M.T"". For dynamically allocated type objects, this should just be the type name, and the module name explicitly stored in the type dict as the value for key "'__module__'". For statically allocated type objects, the *tp_name* field should contain a dot. Everything before the last dot is made accessible as the "__module__" attribute, and everything after the last dot is made accessible as the "__name__" attribute. If no dot is present, the entire "tp_name" field is made accessible as the "__name__" attribute, and the "__module__" attribute is undefined (unless explicitly set in the dictionary, as explained above). This means your type will be impossible to pickle. Additionally, it will not be listed in module documentations created with pydoc. This field must not be "NULL". It is the only required field in "PyTypeObject()" (other than potentially "tp_itemsize"). **Inheritance:** This field is not inherited by subtypes. Py_ssize_t PyTypeObject.tp_basicsize Py_ssize_t PyTypeObject.tp_itemsize These fields allow calculating the size in bytes of instances of the type. There are two kinds of types: types with fixed-length instances have a zero "tp_itemsize" field, types with variable-length instances have a non-zero "tp_itemsize" field. For a type with fixed-length instances, all instances have the same size, given in "tp_basicsize". (Exceptions to this rule can be made using "PyUnstable_Object_GC_NewWithExtraData()".) For a type with variable-length instances, the instances must have an "ob_size" field, and the instance size is "tp_basicsize" plus N times "tp_itemsize", where N is the "length" of the object. Functions like "PyObject_NewVar()" will take the value of N as an argument, and store in the instance's "ob_size" field. Note that the "ob_size" field may later be used for other purposes. For example, "int" instances use the bits of "ob_size" in an implementation-defined way; the underlying storage and its size should be accessed using "PyLong_Export()". Note: The "ob_size" field should be accessed using the "Py_SIZE()" and "Py_SET_SIZE()" macros. Also, the presence of an "ob_size" field in the instance layout doesn't mean that the instance structure is variable-length. For example, the "list" type has fixed-length instances, yet those instances have a "ob_size" field. (As with "int", avoid reading lists' "ob_size" directly. Call "PyList_Size()" instead.) The "tp_basicsize" includes size needed for data of the type's "tp_base", plus any extra data needed by each instance. The correct way to set "tp_basicsize" is to use the "sizeof" operator on the struct used to declare the instance layout. This struct must include the struct used to declare the base type. In other words, "tp_basicsize" must be greater than or equal to the base's "tp_basicsize". Since every type is a subtype of "object", this struct must include "PyObject" or "PyVarObject" (depending on whether "ob_size" should be included). These are usually defined by the macro "PyObject_HEAD" or "PyObject_VAR_HEAD", respectively. The basic size does not include the GC header size, as that header is not part of "PyObject_HEAD". For cases where struct used to declare the base type is unknown, see "PyType_Spec.basicsize" and "PyType_FromMetaclass()". Notes about alignment: * "tp_basicsize" must be a multiple of "_Alignof(PyObject)". When using "sizeof" on a "struct" that includes "PyObject_HEAD", as recommended, the compiler ensures this. When not using a C "struct", or when using compiler extensions like "__attribute__((packed))", it is up to you. * If the variable items require a particular alignment, "tp_basicsize" and "tp_itemsize" must each be a multiple of that alignment. For example, if a type's variable part stores a "double", it is your responsibility that both fields are a multiple of "_Alignof(double)". **Inheritance:** These fields are inherited separately by subtypes. (That is, if the field is set to zero, "PyType_Ready()" will copy the value from the base type, indicating that the instances do not need additional storage.) If the base type has a non-zero "tp_itemsize", it is generally not safe to set "tp_itemsize" to a different non-zero value in a subtype (though this depends on the implementation of the base type). destructor PyTypeObject.tp_dealloc A pointer to the instance destructor function. This function must be defined unless the type guarantees that its instances will never be deallocated (as is the case for the singletons "None" and "Ellipsis"). The function signature is: void tp_dealloc(PyObject *self); The destructor function is called by the "Py_DECREF()" and "Py_XDECREF()" macros when the new reference count is zero. At this point, the instance is still in existence, but there are no references to it. The destructor function should free all references which the instance owns, free all memory buffers owned by the instance (using the freeing function corresponding to the allocation function used to allocate the buffer), and call the type's "tp_free" function. If the type is not subtypable (doesn't have the "Py_TPFLAGS_BASETYPE" flag bit set), it is permissible to call the object deallocator directly instead of via "tp_free". The object deallocator should be the one used to allocate the instance; this is normally "PyObject_Del()" if the instance was allocated using "PyObject_New" or "PyObject_NewVar", or "PyObject_GC_Del()" if the instance was allocated using "PyObject_GC_New" or "PyObject_GC_NewVar". If the type supports garbage collection (has the "Py_TPFLAGS_HAVE_GC" flag bit set), the destructor should call "PyObject_GC_UnTrack()" before clearing any member fields. static void foo_dealloc(foo_object *self) { PyObject_GC_UnTrack(self); Py_CLEAR(self->ref); Py_TYPE(self)->tp_free((PyObject *)self); } Finally, if the type is heap allocated ("Py_TPFLAGS_HEAPTYPE"), the deallocator should release the owned reference to its type object (via "Py_DECREF()") after calling the type deallocator. In order to avoid dangling pointers, the recommended way to achieve this is: static void foo_dealloc(foo_object *self) { PyTypeObject *tp = Py_TYPE(self); // free references and buffers here tp->tp_free(self); Py_DECREF(tp); } Warning: In a garbage collected Python, "tp_dealloc" may be called from any Python thread, not just the thread which created the object (if the object becomes part of a refcount cycle, that cycle might be collected by a garbage collection on any thread). This is not a problem for Python API calls, since the thread on which "tp_dealloc" is called will own the Global Interpreter Lock (GIL). However, if the object being destroyed in turn destroys objects from some other C or C++ library, care should be taken to ensure that destroying those objects on the thread which called "tp_dealloc" will not violate any assumptions of the library. **Inheritance:** This field is inherited by subtypes. Py_ssize_t PyTypeObject.tp_vectorcall_offset An optional offset to a per-instance function that implements calling the object using the vectorcall protocol, a more efficient alternative of the simpler "tp_call". This field is only used if the flag "Py_TPFLAGS_HAVE_VECTORCALL" is set. If so, this must be a positive integer containing the offset in the instance of a "vectorcallfunc" pointer. The *vectorcallfunc* pointer may be "NULL", in which case the instance behaves as if "Py_TPFLAGS_HAVE_VECTORCALL" was not set: calling the instance falls back to "tp_call". Any class that sets "Py_TPFLAGS_HAVE_VECTORCALL" must also set "tp_call" and make sure its behaviour is consistent with the *vectorcallfunc* function. This can be done by setting *tp_call* to "PyVectorcall_Call()". Changed in version 3.8: Before version 3.8, this slot was named "tp_print". In Python 2.x, it was used for printing to a file. In Python 3.0 to 3.7, it was unused. Changed in version 3.12: Before version 3.12, it was not recommended for mutable heap types to implement the vectorcall protocol. When a user sets "__call__" in Python code, only *tp_call* is updated, likely making it inconsistent with the vectorcall function. Since 3.12, setting "__call__" will disable vectorcall optimization by clearing the "Py_TPFLAGS_HAVE_VECTORCALL" flag. **Inheritance:** This field is always inherited. However, the "Py_TPFLAGS_HAVE_VECTORCALL" flag is not always inherited. If it's not set, then the subclass won't use vectorcall, except when "PyVectorcall_Call()" is explicitly called. getattrfunc PyTypeObject.tp_getattr An optional pointer to the get-attribute-string function. This field is deprecated. When it is defined, it should point to a function that acts the same as the "tp_getattro" function, but taking a C string instead of a Python string object to give the attribute name. **Inheritance:** Group: "tp_getattr", "tp_getattro" This field is inherited by subtypes together with "tp_getattro": a subtype inherits both "tp_getattr" and "tp_getattro" from its base type when the subtype's "tp_getattr" and "tp_getattro" are both "NULL". setattrfunc PyTypeObject.tp_setattr An optional pointer to the function for setting and deleting attributes. This field is deprecated. When it is defined, it should point to a function that acts the same as the "tp_setattro" function, but taking a C string instead of a Python string object to give the attribute name. **Inheritance:** Group: "tp_setattr", "tp_setattro" This field is inherited by subtypes together with "tp_setattro": a subtype inherits both "tp_setattr" and "tp_setattro" from its base type when the subtype's "tp_setattr" and "tp_setattro" are both "NULL". PyAsyncMethods *PyTypeObject.tp_as_async Pointer to an additional structure that contains fields relevant only to objects which implement *awaitable* and *asynchronous iterator* protocols at the C-level. See Async Object Structures for details. Added in version 3.5: Formerly known as "tp_compare" and "tp_reserved". **Inheritance:** The "tp_as_async" field is not inherited, but the contained fields are inherited individually. reprfunc PyTypeObject.tp_repr An optional pointer to a function that implements the built-in function "repr()". The signature is the same as for "PyObject_Repr()": PyObject *tp_repr(PyObject *self); The function must return a string or a Unicode object. Ideally, this function should return a string that, when passed to "eval()", given a suitable environment, returns an object with the same value. If this is not feasible, it should return a string starting with "'<'" and ending with "'>'" from which both the type and the value of the object can be deduced. **Inheritance:** This field is inherited by subtypes. **Default:** When this field is not set, a string of the form "<%s object at %p>" is returned, where "%s" is replaced by the type name, and "%p" by the object's memory address. PyNumberMethods *PyTypeObject.tp_as_number Pointer to an additional structure that contains fields relevant only to objects which implement the number protocol. These fields are documented in Number Object Structures. **Inheritance:** The "tp_as_number" field is not inherited, but the contained fields are inherited individually. PySequenceMethods *PyTypeObject.tp_as_sequence Pointer to an additional structure that contains fields relevant only to objects which implement the sequence protocol. These fields are documented in Sequence Object Structures. **Inheritance:** The "tp_as_sequence" field is not inherited, but the contained fields are inherited individually. PyMappingMethods *PyTypeObject.tp_as_mapping Pointer to an additional structure that contains fields relevant only to objects which implement the mapping protocol. These fields are documented in Mapping Object Structures. **Inheritance:** The "tp_as_mapping" field is not inherited, but the contained fields are inherited individually. hashfunc PyTypeObject.tp_hash An optional pointer to a function that implements the built-in function "hash()". The signature is the same as for "PyObject_Hash()": Py_hash_t tp_hash(PyObject *); The value "-1" should not be returned as a normal return value; when an error occurs during the computation of the hash value, the function should set an exception and return "-1". When this field is not set (*and* "tp_richcompare" is not set), an attempt to take the hash of the object raises "TypeError". This is the same as setting it to "PyObject_HashNotImplemented()". This field can be set explicitly to "PyObject_HashNotImplemented()" to block inheritance of the hash method from a parent type. This is interpreted as the equivalent of "__hash__ = None" at the Python level, causing "isinstance(o, collections.Hashable)" to correctly return "False". Note that the converse is also true - setting "__hash__ = None" on a class at the Python level will result in the "tp_hash" slot being set to "PyObject_HashNotImplemented()". **Inheritance:** Group: "tp_hash", "tp_richcompare" This field is inherited by subtypes together with "tp_richcompare": a subtype inherits both of "tp_richcompare" and "tp_hash", when the subtype's "tp_richcompare" and "tp_hash" are both "NULL". **Default:** "PyBaseObject_Type" uses "PyObject_GenericHash()". ternaryfunc PyTypeObject.tp_call An optional pointer to a function that implements calling the object. This should be "NULL" if the object is not callable. The signature is the same as for "PyObject_Call()": PyObject *tp_call(PyObject *self, PyObject *args, PyObject *kwargs); **Inheritance:** This field is inherited by subtypes. reprfunc PyTypeObject.tp_str An optional pointer to a function that implements the built-in operation "str()". (Note that "str" is a type now, and "str()" calls the constructor for that type. This constructor calls "PyObject_Str()" to do the actual work, and "PyObject_Str()" will call this handler.) The signature is the same as for "PyObject_Str()": PyObject *tp_str(PyObject *self); The function must return a string or a Unicode object. It should be a "friendly" string representation of the object, as this is the representation that will be used, among other things, by the "print()" function. **Inheritance:** This field is inherited by subtypes. **Default:** When this field is not set, "PyObject_Repr()" is called to return a string representation. getattrofunc PyTypeObject.tp_getattro An optional pointer to the get-attribute function. The signature is the same as for "PyObject_GetAttr()": PyObject *tp_getattro(PyObject *self, PyObject *attr); It is usually convenient to set this field to "PyObject_GenericGetAttr()", which implements the normal way of looking for object attributes. **Inheritance:** Group: "tp_getattr", "tp_getattro" This field is inherited by subtypes together with "tp_getattr": a subtype inherits both "tp_getattr" and "tp_getattro" from its base type when the subtype's "tp_getattr" and "tp_getattro" are both "NULL". **Default:** "PyBaseObject_Type" uses "PyObject_GenericGetAttr()". setattrofunc PyTypeObject.tp_setattro An optional pointer to the function for setting and deleting attributes. The signature is the same as for "PyObject_SetAttr()": int tp_setattro(PyObject *self, PyObject *attr, PyObject *value); In addition, setting *value* to "NULL" to delete an attribute must be supported. It is usually convenient to set this field to "PyObject_GenericSetAttr()", which implements the normal way of setting object attributes. **Inheritance:** Group: "tp_setattr", "tp_setattro" This field is inherited by subtypes together with "tp_setattr": a subtype inherits both "tp_setattr" and "tp_setattro" from its base type when the subtype's "tp_setattr" and "tp_setattro" are both "NULL". **Default:** "PyBaseObject_Type" uses "PyObject_GenericSetAttr()". PyBufferProcs *PyTypeObject.tp_as_buffer Pointer to an additional structure that contains fields relevant only to objects which implement the buffer interface. These fields are documented in Buffer Object Structures. **Inheritance:** The "tp_as_buffer" field is not inherited, but the contained fields are inherited individually. unsigned long PyTypeObject.tp_flags This field is a bit mask of various flags. Some flags indicate variant semantics for certain situations; others are used to indicate that certain fields in the type object (or in the extension structures referenced via "tp_as_number", "tp_as_sequence", "tp_as_mapping", and "tp_as_buffer") that were historically not always present are valid; if such a flag bit is clear, the type fields it guards must not be accessed and must be considered to have a zero or "NULL" value instead. **Inheritance:** Inheritance of this field is complicated. Most flag bits are inherited individually, i.e. if the base type has a flag bit set, the subtype inherits this flag bit. The flag bits that pertain to extension structures are strictly inherited if the extension structure is inherited, i.e. the base type's value of the flag bit is copied into the subtype together with a pointer to the extension structure. The "Py_TPFLAGS_HAVE_GC" flag bit is inherited together with the "tp_traverse" and "tp_clear" fields, i.e. if the "Py_TPFLAGS_HAVE_GC" flag bit is clear in the subtype and the "tp_traverse" and "tp_clear" fields in the subtype exist and have "NULL" values. .. XXX are most flag bits *really* inherited individually? **Default:** "PyBaseObject_Type" uses "Py_TPFLAGS_DEFAULT | Py_TPFLAGS_BASETYPE". **Bit Masks:** The following bit masks are currently defined; these can be ORed together using the "|" operator to form the value of the "tp_flags" field. The macro "PyType_HasFeature()" takes a type and a flags value, *tp* and *f*, and checks whether "tp->tp_flags & f" is non- zero. Py_TPFLAGS_HEAPTYPE This bit is set when the type object itself is allocated on the heap, for example, types created dynamically using "PyType_FromSpec()". In this case, the "ob_type" field of its instances is considered a reference to the type, and the type object is INCREF'ed when a new instance is created, and DECREF'ed when an instance is destroyed (this does not apply to instances of subtypes; only the type referenced by the instance's ob_type gets INCREF'ed or DECREF'ed). Heap types should also support garbage collection as they can form a reference cycle with their own module object. **Inheritance:** ??? Py_TPFLAGS_BASETYPE This bit is set when the type can be used as the base type of another type. If this bit is clear, the type cannot be subtyped (similar to a "final" class in Java). **Inheritance:** ??? Py_TPFLAGS_READY This bit is set when the type object has been fully initialized by "PyType_Ready()". **Inheritance:** ??? Py_TPFLAGS_READYING This bit is set while "PyType_Ready()" is in the process of initializing the type object. **Inheritance:** ??? Py_TPFLAGS_HAVE_GC This bit is set when the object supports garbage collection. If this bit is set, instances must be created using "PyObject_GC_New" and destroyed using "PyObject_GC_Del()". More information in section Supporting Cyclic Garbage Collection. This bit also implies that the GC-related fields "tp_traverse" and "tp_clear" are present in the type object. **Inheritance:** Group: "Py_TPFLAGS_HAVE_GC", "tp_traverse", "tp_clear" The "Py_TPFLAGS_HAVE_GC" flag bit is inherited together with the "tp_traverse" and "tp_clear" fields, i.e. if the "Py_TPFLAGS_HAVE_GC" flag bit is clear in the subtype and the "tp_traverse" and "tp_clear" fields in the subtype exist and have "NULL" values. Py_TPFLAGS_DEFAULT This is a bitmask of all the bits that pertain to the existence of certain fields in the type object and its extension structures. Currently, it includes the following bits: "Py_TPFLAGS_HAVE_STACKLESS_EXTENSION". **Inheritance:** ??? Py_TPFLAGS_METHOD_DESCRIPTOR This bit indicates that objects behave like unbound methods. If this flag is set for "type(meth)", then: * "meth.__get__(obj, cls)(*args, **kwds)" (with "obj" not None) must be equivalent to "meth(obj, *args, **kwds)". * "meth.__get__(None, cls)(*args, **kwds)" must be equivalent to "meth(*args, **kwds)". This flag enables an optimization for typical method calls like "obj.meth()": it avoids creating a temporary "bound method" object for "obj.meth". Added in version 3.8. **Inheritance:** This flag is never inherited by types without the "Py_TPFLAGS_IMMUTABLETYPE" flag set. For extension types, it is inherited whenever "tp_descr_get" is inherited. Py_TPFLAGS_MANAGED_DICT This bit indicates that instances of the class have a *~object.__dict__* attribute, and that the space for the dictionary is managed by the VM. If this flag is set, "Py_TPFLAGS_HAVE_GC" should also be set. The type traverse function must call "PyObject_VisitManagedDict()" and its clear function must call "PyObject_ClearManagedDict()". Added in version 3.12. **Inheritance:** This flag is inherited unless the "tp_dictoffset" field is set in a superclass. Py_TPFLAGS_MANAGED_WEAKREF This bit indicates that instances of the class should be weakly referenceable. Added in version 3.12. **Inheritance:** This flag is inherited unless the "tp_weaklistoffset" field is set in a superclass. Py_TPFLAGS_ITEMS_AT_END Only usable with variable-size types, i.e. ones with non-zero "tp_itemsize". Indicates that the variable-sized portion of an instance of this type is at the end of the instance's memory area, at an offset of "Py_TYPE(obj)->tp_basicsize" (which may be different in each subclass). When setting this flag, be sure that all superclasses either use this memory layout, or are not variable-sized. Python does not check this. Added in version 3.12. **Inheritance:** This flag is inherited. Py_TPFLAGS_LONG_SUBCLASS Py_TPFLAGS_LIST_SUBCLASS Py_TPFLAGS_TUPLE_SUBCLASS Py_TPFLAGS_BYTES_SUBCLASS Py_TPFLAGS_UNICODE_SUBCLASS Py_TPFLAGS_DICT_SUBCLASS Py_TPFLAGS_BASE_EXC_SUBCLASS Py_TPFLAGS_TYPE_SUBCLASS These flags are used by functions such as "PyLong_Check()" to quickly determine if a type is a subclass of a built-in type; such specific checks are faster than a generic check, like "PyObject_IsInstance()". Custom types that inherit from built- ins should have their "tp_flags" set appropriately, or the code that interacts with such types will behave differently depending on what kind of check is used. Py_TPFLAGS_HAVE_FINALIZE This bit is set when the "tp_finalize" slot is present in the type structure. Added in version 3.4. Deprecated since version 3.8: This flag isn't necessary anymore, as the interpreter assumes the "tp_finalize" slot is always present in the type structure. Py_TPFLAGS_HAVE_VECTORCALL This bit is set when the class implements the vectorcall protocol. See "tp_vectorcall_offset" for details. **Inheritance:** This bit is inherited if "tp_call" is also inherited. Added in version 3.9. Changed in version 3.12: This flag is now removed from a class when the class's "__call__()" method is reassigned.This flag can now be inherited by mutable classes. Py_TPFLAGS_IMMUTABLETYPE This bit is set for type objects that are immutable: type attributes cannot be set nor deleted. "PyType_Ready()" automatically applies this flag to static types. **Inheritance:** This flag is not inherited. Added in version 3.10. Py_TPFLAGS_DISALLOW_INSTANTIATION Disallow creating instances of the type: set "tp_new" to NULL and don't create the "__new__" key in the type dictionary. The flag must be set before creating the type, not after. For example, it must be set before "PyType_Ready()" is called on the type. The flag is set automatically on static types if "tp_base" is NULL or "&PyBaseObject_Type" and "tp_new" is NULL. **Inheritance:** This flag is not inherited. However, subclasses will not be instantiable unless they provide a non-NULL "tp_new" (which is only possible via the C API). Note: To disallow instantiating a class directly but allow instantiating its subclasses (e.g. for an *abstract base class*), do not use this flag. Instead, make "tp_new" only succeed for subclasses. Added in version 3.10. Py_TPFLAGS_MAPPING This bit indicates that instances of the class may match mapping patterns when used as the subject of a "match" block. It is automatically set when registering or subclassing "collections.abc.Mapping", and unset when registering "collections.abc.Sequence". Note: "Py_TPFLAGS_MAPPING" and "Py_TPFLAGS_SEQUENCE" are mutually exclusive; it is an error to enable both flags simultaneously. **Inheritance:** This flag is inherited by types that do not already set "Py_TPFLAGS_SEQUENCE". See also: **PEP 634** -- Structural Pattern Matching: Specification Added in version 3.10. Py_TPFLAGS_SEQUENCE This bit indicates that instances of the class may match sequence patterns when used as the subject of a "match" block. It is automatically set when registering or subclassing "collections.abc.Sequence", and unset when registering "collections.abc.Mapping". Note: "Py_TPFLAGS_MAPPING" and "Py_TPFLAGS_SEQUENCE" are mutually exclusive; it is an error to enable both flags simultaneously. **Inheritance:** This flag is inherited by types that do not already set "Py_TPFLAGS_MAPPING". See also: **PEP 634** -- Structural Pattern Matching: Specification Added in version 3.10. Py_TPFLAGS_VALID_VERSION_TAG Internal. Do not set or unset this flag. To indicate that a class has changed call "PyType_Modified()" Warning: This flag is present in header files, but is not be used. It will be removed in a future version of CPython const char *PyTypeObject.tp_doc An optional pointer to a NUL-terminated C string giving the docstring for this type object. This is exposed as the "__doc__" attribute on the type and instances of the type. **Inheritance:** This field is *not* inherited by subtypes. traverseproc PyTypeObject.tp_traverse An optional pointer to a traversal function for the garbage collector. This is only used if the "Py_TPFLAGS_HAVE_GC" flag bit is set. The signature is: int tp_traverse(PyObject *self, visitproc visit, void *arg); More information about Python's garbage collection scheme can be found in section Supporting Cyclic Garbage Collection. The "tp_traverse" pointer is used by the garbage collector to detect reference cycles. A typical implementation of a "tp_traverse" function simply calls "Py_VISIT()" on each of the instance's members that are Python objects that the instance owns. For example, this is function "local_traverse()" from the "_thread" extension module: static int local_traverse(localobject *self, visitproc visit, void *arg) { Py_VISIT(self->args); Py_VISIT(self->kw); Py_VISIT(self->dict); return 0; } Note that "Py_VISIT()" is called only on those members that can participate in reference cycles. Although there is also a "self->key" member, it can only be "NULL" or a Python string and therefore cannot be part of a reference cycle. On the other hand, even if you know a member can never be part of a cycle, as a debugging aid you may want to visit it anyway just so the "gc" module's "get_referents()" function will include it. Heap types ("Py_TPFLAGS_HEAPTYPE") must visit their type with: Py_VISIT(Py_TYPE(self)); It is only needed since Python 3.9. To support Python 3.8 and older, this line must be conditional: #if PY_VERSION_HEX >= 0x03090000 Py_VISIT(Py_TYPE(self)); #endif If the "Py_TPFLAGS_MANAGED_DICT" bit is set in the "tp_flags" field, the traverse function must call "PyObject_VisitManagedDict()" like this: PyObject_VisitManagedDict((PyObject*)self, visit, arg); Warning: When implementing "tp_traverse", only the members that the instance *owns* (by having *strong references* to them) must be visited. For instance, if an object supports weak references via the "tp_weaklist" slot, the pointer supporting the linked list (what *tp_weaklist* points to) must **not** be visited as the instance does not directly own the weak references to itself (the weakreference list is there to support the weak reference machinery, but the instance has no strong reference to the elements inside it, as they are allowed to be removed even if the instance is still alive). Note that "Py_VISIT()" requires the *visit* and *arg* parameters to "local_traverse()" to have these specific names; don't name them just anything. Instances of heap-allocated types hold a reference to their type. Their traversal function must therefore either visit "Py_TYPE(self)", or delegate this responsibility by calling "tp_traverse" of another heap-allocated type (such as a heap- allocated superclass). If they do not, the type object may not be garbage-collected. Changed in version 3.9: Heap-allocated types are expected to visit "Py_TYPE(self)" in "tp_traverse". In earlier versions of Python, due to bug 40217, doing this may lead to crashes in subclasses. **Inheritance:** Group: "Py_TPFLAGS_HAVE_GC", "tp_traverse", "tp_clear" This field is inherited by subtypes together with "tp_clear" and the "Py_TPFLAGS_HAVE_GC" flag bit: the flag bit, "tp_traverse", and "tp_clear" are all inherited from the base type if they are all zero in the subtype. inquiry PyTypeObject.tp_clear An optional pointer to a clear function for the garbage collector. This is only used if the "Py_TPFLAGS_HAVE_GC" flag bit is set. The signature is: int tp_clear(PyObject *); The "tp_clear" member function is used to break reference cycles in cyclic garbage detected by the garbage collector. Taken together, all "tp_clear" functions in the system must combine to break all reference cycles. This is subtle, and if in any doubt supply a "tp_clear" function. For example, the tuple type does not implement a "tp_clear" function, because it's possible to prove that no reference cycle can be composed entirely of tuples. Therefore the "tp_clear" functions of other types must be sufficient to break any cycle containing a tuple. This isn't immediately obvious, and there's rarely a good reason to avoid implementing "tp_clear". Implementations of "tp_clear" should drop the instance's references to those of its members that may be Python objects, and set its pointers to those members to "NULL", as in the following example: static int local_clear(localobject *self) { Py_CLEAR(self->key); Py_CLEAR(self->args); Py_CLEAR(self->kw); Py_CLEAR(self->dict); return 0; } The "Py_CLEAR()" macro should be used, because clearing references is delicate: the reference to the contained object must not be released (via "Py_DECREF()") until after the pointer to the contained object is set to "NULL". This is because releasing the reference may cause the contained object to become trash, triggering a chain of reclamation activity that may include invoking arbitrary Python code (due to finalizers, or weakref callbacks, associated with the contained object). If it's possible for such code to reference *self* again, it's important that the pointer to the contained object be "NULL" at that time, so that *self* knows the contained object can no longer be used. The "Py_CLEAR()" macro performs the operations in a safe order. If the "Py_TPFLAGS_MANAGED_DICT" bit is set in the "tp_flags" field, the traverse function must call "PyObject_ClearManagedDict()" like this: PyObject_ClearManagedDict((PyObject*)self); Note that "tp_clear" is not *always* called before an instance is deallocated. For example, when reference counting is enough to determine that an object is no longer used, the cyclic garbage collector is not involved and "tp_dealloc" is called directly. Because the goal of "tp_clear" functions is to break reference cycles, it's not necessary to clear contained objects like Python strings or Python integers, which can't participate in reference cycles. On the other hand, it may be convenient to clear all contained Python objects, and write the type's "tp_dealloc" function to invoke "tp_clear". More information about Python's garbage collection scheme can be found in section Supporting Cyclic Garbage Collection. **Inheritance:** Group: "Py_TPFLAGS_HAVE_GC", "tp_traverse", "tp_clear" This field is inherited by subtypes together with "tp_traverse" and the "Py_TPFLAGS_HAVE_GC" flag bit: the flag bit, "tp_traverse", and "tp_clear" are all inherited from the base type if they are all zero in the subtype. richcmpfunc PyTypeObject.tp_richcompare An optional pointer to the rich comparison function, whose signature is: PyObject *tp_richcompare(PyObject *self, PyObject *other, int op); The first parameter is guaranteed to be an instance of the type that is defined by "PyTypeObject". The function should return the result of the comparison (usually "Py_True" or "Py_False"). If the comparison is undefined, it must return "Py_NotImplemented", if another error occurred it must return "NULL" and set an exception condition. The following constants are defined to be used as the third argument for "tp_richcompare" and for "PyObject_RichCompare()": +----------------------+--------------+ | Constant | Comparison | |======================|==============| | Py_LT | "<" | +----------------------+--------------+ | Py_LE | "<=" | +----------------------+--------------+ | Py_EQ | "==" | +----------------------+--------------+ | Py_NE | "!=" | +----------------------+--------------+ | Py_GT | ">" | +----------------------+--------------+ | Py_GE | ">=" | +----------------------+--------------+ The following macro is defined to ease writing rich comparison functions: Py_RETURN_RICHCOMPARE(VAL_A, VAL_B, op) Return "Py_True" or "Py_False" from the function, depending on the result of a comparison. VAL_A and VAL_B must be orderable by C comparison operators (for example, they may be C ints or floats). The third argument specifies the requested operation, as for "PyObject_RichCompare()". The returned value is a new *strong reference*. On error, sets an exception and returns "NULL" from the function. Added in version 3.7. **Inheritance:** Group: "tp_hash", "tp_richcompare" This field is inherited by subtypes together with "tp_hash": a subtype inherits "tp_richcompare" and "tp_hash" when the subtype's "tp_richcompare" and "tp_hash" are both "NULL". **Default:** "PyBaseObject_Type" provides a "tp_richcompare" implementation, which may be inherited. However, if only "tp_hash" is defined, not even the inherited function is used and instances of the type will not be able to participate in any comparisons. Py_ssize_t PyTypeObject.tp_weaklistoffset While this field is still supported, "Py_TPFLAGS_MANAGED_WEAKREF" should be used instead, if at all possible. If the instances of this type are weakly referenceable, this field is greater than zero and contains the offset in the instance structure of the weak reference list head (ignoring the GC header, if present); this offset is used by "PyObject_ClearWeakRefs()" and the "PyWeakref_*" functions. The instance structure needs to include a field of type PyObject* which is initialized to "NULL". Do not confuse this field with "tp_weaklist"; that is the list head for weak references to the type object itself. It is an error to set both the "Py_TPFLAGS_MANAGED_WEAKREF" bit and "tp_weaklistoffset". **Inheritance:** This field is inherited by subtypes, but see the rules listed below. A subtype may override this offset; this means that the subtype uses a different weak reference list head than the base type. Since the list head is always found via "tp_weaklistoffset", this should not be a problem. **Default:** If the "Py_TPFLAGS_MANAGED_WEAKREF" bit is set in the "tp_flags" field, then "tp_weaklistoffset" will be set to a negative value, to indicate that it is unsafe to use this field. getiterfunc PyTypeObject.tp_iter An optional pointer to a function that returns an *iterator* for the object. Its presence normally signals that the instances of this type are *iterable* (although sequences may be iterable without this function). This function has the same signature as "PyObject_GetIter()": PyObject *tp_iter(PyObject *self); **Inheritance:** This field is inherited by subtypes. iternextfunc PyTypeObject.tp_iternext An optional pointer to a function that returns the next item in an *iterator*. The signature is: PyObject *tp_iternext(PyObject *self); When the iterator is exhausted, it must return "NULL"; a "StopIteration" exception may or may not be set. When another error occurs, it must return "NULL" too. Its presence signals that the instances of this type are iterators. Iterator types should also define the "tp_iter" function, and that function should return the iterator instance itself (not a new iterator instance). This function has the same signature as "PyIter_Next()". **Inheritance:** This field is inherited by subtypes. struct PyMethodDef *PyTypeObject.tp_methods An optional pointer to a static "NULL"-terminated array of "PyMethodDef" structures, declaring regular methods of this type. For each entry in the array, an entry is added to the type's dictionary (see "tp_dict" below) containing a method descriptor. **Inheritance:** This field is not inherited by subtypes (methods are inherited through a different mechanism). struct PyMemberDef *PyTypeObject.tp_members An optional pointer to a static "NULL"-terminated array of "PyMemberDef" structures, declaring regular data members (fields or slots) of instances of this type. For each entry in the array, an entry is added to the type's dictionary (see "tp_dict" below) containing a member descriptor. **Inheritance:** This field is not inherited by subtypes (members are inherited through a different mechanism). struct PyGetSetDef *PyTypeObject.tp_getset An optional pointer to a static "NULL"-terminated array of "PyGetSetDef" structures, declaring computed attributes of instances of this type. For each entry in the array, an entry is added to the type's dictionary (see "tp_dict" below) containing a getset descriptor. **Inheritance:** This field is not inherited by subtypes (computed attributes are inherited through a different mechanism). PyTypeObject *PyTypeObject.tp_base An optional pointer to a base type from which type properties are inherited. At this level, only single inheritance is supported; multiple inheritance require dynamically creating a type object by calling the metatype. Note: Slot initialization is subject to the rules of initializing globals. C99 requires the initializers to be "address constants". Function designators like "PyType_GenericNew()", with implicit conversion to a pointer, are valid C99 address constants.However, the unary '&' operator applied to a non-static variable like "PyBaseObject_Type" is not required to produce an address constant. Compilers may support this (gcc does), MSVC does not. Both compilers are strictly standard conforming in this particular behavior.Consequently, "tp_base" should be set in the extension module's init function. **Inheritance:** This field is not inherited by subtypes (obviously). **Default:** This field defaults to "&PyBaseObject_Type" (which to Python programmers is known as the type "object"). PyObject *PyTypeObject.tp_dict The type's dictionary is stored here by "PyType_Ready()". This field should normally be initialized to "NULL" before PyType_Ready is called; it may also be initialized to a dictionary containing initial attributes for the type. Once "PyType_Ready()" has initialized the type, extra attributes for the type may be added to this dictionary only if they don't correspond to overloaded operations (like "__add__()"). Once initialization for the type has finished, this field should be treated as read-only. Some types may not store their dictionary in this slot. Use "PyType_GetDict()" to retrieve the dictionary for an arbitrary type. Changed in version 3.12: Internals detail: For static builtin types, this is always "NULL". Instead, the dict for such types is stored on "PyInterpreterState". Use "PyType_GetDict()" to get the dict for an arbitrary type. **Inheritance:** This field is not inherited by subtypes (though the attributes defined in here are inherited through a different mechanism). **Default:** If this field is "NULL", "PyType_Ready()" will assign a new dictionary to it. Warning: It is not safe to use "PyDict_SetItem()" on or otherwise modify "tp_dict" with the dictionary C-API. descrgetfunc PyTypeObject.tp_descr_get An optional pointer to a "descriptor get" function. The function signature is: PyObject * tp_descr_get(PyObject *self, PyObject *obj, PyObject *type); **Inheritance:** This field is inherited by subtypes. descrsetfunc PyTypeObject.tp_descr_set An optional pointer to a function for setting and deleting a descriptor's value. The function signature is: int tp_descr_set(PyObject *self, PyObject *obj, PyObject *value); The *value* argument is set to "NULL" to delete the value. **Inheritance:** This field is inherited by subtypes. Py_ssize_t PyTypeObject.tp_dictoffset While this field is still supported, "Py_TPFLAGS_MANAGED_DICT" should be used instead, if at all possible. If the instances of this type have a dictionary containing instance variables, this field is non-zero and contains the offset in the instances of the type of the instance variable dictionary; this offset is used by "PyObject_GenericGetAttr()". Do not confuse this field with "tp_dict"; that is the dictionary for attributes of the type object itself. The value specifies the offset of the dictionary from the start of the instance structure. The "tp_dictoffset" should be regarded as write-only. To get the pointer to the dictionary call "PyObject_GenericGetDict()". Calling "PyObject_GenericGetDict()" may need to allocate memory for the dictionary, so it is may be more efficient to call "PyObject_GetAttr()" when accessing an attribute on the object. It is an error to set both the "Py_TPFLAGS_MANAGED_DICT" bit and "tp_dictoffset". **Inheritance:** This field is inherited by subtypes. A subtype should not override this offset; doing so could be unsafe, if C code tries to access the dictionary at the previous offset. To properly support inheritance, use "Py_TPFLAGS_MANAGED_DICT". **Default:** This slot has no default. For static types, if the field is "NULL" then no "__dict__" gets created for instances. If the "Py_TPFLAGS_MANAGED_DICT" bit is set in the "tp_flags" field, then "tp_dictoffset" will be set to "-1", to indicate that it is unsafe to use this field. initproc PyTypeObject.tp_init An optional pointer to an instance initialization function. This function corresponds to the "__init__()" method of classes. Like "__init__()", it is possible to create an instance without calling "__init__()", and it is possible to reinitialize an instance by calling its "__init__()" method again. The function signature is: int tp_init(PyObject *self, PyObject *args, PyObject *kwds); The self argument is the instance to be initialized; the *args* and *kwds* arguments represent positional and keyword arguments of the call to "__init__()". The "tp_init" function, if not "NULL", is called when an instance is created normally by calling its type, after the type's "tp_new" function has returned an instance of the type. If the "tp_new" function returns an instance of some other type that is not a subtype of the original type, no "tp_init" function is called; if "tp_new" returns an instance of a subtype of the original type, the subtype's "tp_init" is called. Returns "0" on success, "-1" and sets an exception on error. **Inheritance:** This field is inherited by subtypes. **Default:** For static types this field does not have a default. allocfunc PyTypeObject.tp_alloc An optional pointer to an instance allocation function. The function signature is: PyObject *tp_alloc(PyTypeObject *self, Py_ssize_t nitems); **Inheritance:** This field is inherited by static subtypes, but not by dynamic subtypes (subtypes created by a class statement). **Default:** For dynamic subtypes, this field is always set to "PyType_GenericAlloc()", to force a standard heap allocation strategy. For static subtypes, "PyBaseObject_Type" uses "PyType_GenericAlloc()". That is the recommended value for all statically defined types. newfunc PyTypeObject.tp_new An optional pointer to an instance creation function. The function signature is: PyObject *tp_new(PyTypeObject *subtype, PyObject *args, PyObject *kwds); The *subtype* argument is the type of the object being created; the *args* and *kwds* arguments represent positional and keyword arguments of the call to the type. Note that *subtype* doesn't have to equal the type whose "tp_new" function is called; it may be a subtype of that type (but not an unrelated type). The "tp_new" function should call "subtype->tp_alloc(subtype, nitems)" to allocate space for the object, and then do only as much further initialization as is absolutely necessary. Initialization that can safely be ignored or repeated should be placed in the "tp_init" handler. A good rule of thumb is that for immutable types, all initialization should take place in "tp_new", while for mutable types, most initialization should be deferred to "tp_init". Set the "Py_TPFLAGS_DISALLOW_INSTANTIATION" flag to disallow creating instances of the type in Python. **Inheritance:** This field is inherited by subtypes, except it is not inherited by static types whose "tp_base" is "NULL" or "&PyBaseObject_Type". **Default:** For static types this field has no default. This means if the slot is defined as "NULL", the type cannot be called to create new instances; presumably there is some other way to create instances, like a factory function. freefunc PyTypeObject.tp_free An optional pointer to an instance deallocation function. Its signature is: void tp_free(void *self); An initializer that is compatible with this signature is "PyObject_Free()". **Inheritance:** This field is inherited by static subtypes, but not by dynamic subtypes (subtypes created by a class statement) **Default:** In dynamic subtypes, this field is set to a deallocator suitable to match "PyType_GenericAlloc()" and the value of the "Py_TPFLAGS_HAVE_GC" flag bit. For static subtypes, "PyBaseObject_Type" uses "PyObject_Del()". inquiry PyTypeObject.tp_is_gc An optional pointer to a function called by the garbage collector. The garbage collector needs to know whether a particular object is collectible or not. Normally, it is sufficient to look at the object's type's "tp_flags" field, and check the "Py_TPFLAGS_HAVE_GC" flag bit. But some types have a mixture of statically and dynamically allocated instances, and the statically allocated instances are not collectible. Such types should define this function; it should return "1" for a collectible instance, and "0" for a non-collectible instance. The signature is: int tp_is_gc(PyObject *self); (The only example of this are types themselves. The metatype, "PyType_Type", defines this function to distinguish between statically and dynamically allocated types.) **Inheritance:** This field is inherited by subtypes. **Default:** This slot has no default. If this field is "NULL", "Py_TPFLAGS_HAVE_GC" is used as the functional equivalent. PyObject *PyTypeObject.tp_bases Tuple of base types. This field should be set to "NULL" and treated as read-only. Python will fill it in when the type is "initialized". For dynamically created classes, the "Py_tp_bases" "slot" can be used instead of the *bases* argument of "PyType_FromSpecWithBases()". The argument form is preferred. Warning: Multiple inheritance does not work well for statically defined types. If you set "tp_bases" to a tuple, Python will not raise an error, but some slots will only be inherited from the first base. **Inheritance:** This field is not inherited. PyObject *PyTypeObject.tp_mro Tuple containing the expanded set of base types, starting with the type itself and ending with "object", in Method Resolution Order. This field should be set to "NULL" and treated as read-only. Python will fill it in when the type is "initialized". **Inheritance:** This field is not inherited; it is calculated fresh by "PyType_Ready()". PyObject *PyTypeObject.tp_cache Unused. Internal use only. **Inheritance:** This field is not inherited. void *PyTypeObject.tp_subclasses A collection of subclasses. Internal use only. May be an invalid pointer. To get a list of subclasses, call the Python method "__subclasses__()". Changed in version 3.12: For some types, this field does not hold a valid PyObject*. The type was changed to void* to indicate this. **Inheritance:** This field is not inherited. PyObject *PyTypeObject.tp_weaklist Weak reference list head, for weak references to this type object. Not inherited. Internal use only. Changed in version 3.12: Internals detail: For the static builtin types this is always "NULL", even if weakrefs are added. Instead, the weakrefs for each are stored on "PyInterpreterState". Use the public C-API or the internal "_PyObject_GET_WEAKREFS_LISTPTR()" macro to avoid the distinction. **Inheritance:** This field is not inherited. destructor PyTypeObject.tp_del This field is deprecated. Use "tp_finalize" instead. unsigned int PyTypeObject.tp_version_tag Used to index into the method cache. Internal use only. **Inheritance:** This field is not inherited. destructor PyTypeObject.tp_finalize An optional pointer to an instance finalization function. Its signature is: void tp_finalize(PyObject *self); If "tp_finalize" is set, the interpreter calls it once when finalizing an instance. It is called either from the garbage collector (if the instance is part of an isolated reference cycle) or just before the object is deallocated. Either way, it is guaranteed to be called before attempting to break reference cycles, ensuring that it finds the object in a sane state. "tp_finalize" should not mutate the current exception status; therefore, a recommended way to write a non-trivial finalizer is: static void local_finalize(PyObject *self) { /* Save the current exception, if any. */ PyObject *exc = PyErr_GetRaisedException(); /* ... */ /* Restore the saved exception. */ PyErr_SetRaisedException(exc); } **Inheritance:** This field is inherited by subtypes. Added in version 3.4. Changed in version 3.8: Before version 3.8 it was necessary to set the "Py_TPFLAGS_HAVE_FINALIZE" flags bit in order for this field to be used. This is no longer required. See also: "Safe object finalization" (**PEP 442**) vectorcallfunc PyTypeObject.tp_vectorcall Vectorcall function to use for calls of this type object. In other words, it is used to implement vectorcall for "type.__call__". If "tp_vectorcall" is "NULL", the default call implementation using "__new__()" and "__init__()" is used. **Inheritance:** This field is never inherited. Added in version 3.9: (the field exists since 3.8 but it's only used since 3.9) unsigned char PyTypeObject.tp_watched Internal. Do not use. Added in version 3.12. Static Types ============ Traditionally, types defined in C code are *static*, that is, a static "PyTypeObject" structure is defined directly in code and initialized using "PyType_Ready()". This results in types that are limited relative to types defined in Python: * Static types are limited to one base, i.e. they cannot use multiple inheritance. * Static type objects (but not necessarily their instances) are immutable. It is not possible to add or modify the type object's attributes from Python. * Static type objects are shared across sub-interpreters, so they should not include any subinterpreter-specific state. Also, since "PyTypeObject" is only part of the Limited API as an opaque struct, any extension modules using static types must be compiled for a specific Python minor version. Heap Types ========== An alternative to static types is *heap-allocated types*, or *heap types* for short, which correspond closely to classes created by Python's "class" statement. Heap types have the "Py_TPFLAGS_HEAPTYPE" flag set. This is done by filling a "PyType_Spec" structure and calling "PyType_FromSpec()", "PyType_FromSpecWithBases()", "PyType_FromModuleAndSpec()", or "PyType_FromMetaclass()". Number Object Structures ======================== type PyNumberMethods This structure holds pointers to the functions which an object uses to implement the number protocol. Each function is used by the function of similar name documented in the Number Protocol section. Here is the structure definition: typedef struct { binaryfunc nb_add; binaryfunc nb_subtract; binaryfunc nb_multiply; binaryfunc nb_remainder; binaryfunc nb_divmod; ternaryfunc nb_power; unaryfunc nb_negative; unaryfunc nb_positive; unaryfunc nb_absolute; inquiry nb_bool; unaryfunc nb_invert; binaryfunc nb_lshift; binaryfunc nb_rshift; binaryfunc nb_and; binaryfunc nb_xor; binaryfunc nb_or; unaryfunc nb_int; void *nb_reserved; unaryfunc nb_float; binaryfunc nb_inplace_add; binaryfunc nb_inplace_subtract; binaryfunc nb_inplace_multiply; binaryfunc nb_inplace_remainder; ternaryfunc nb_inplace_power; binaryfunc nb_inplace_lshift; binaryfunc nb_inplace_rshift; binaryfunc nb_inplace_and; binaryfunc nb_inplace_xor; binaryfunc nb_inplace_or; binaryfunc nb_floor_divide; binaryfunc nb_true_divide; binaryfunc nb_inplace_floor_divide; binaryfunc nb_inplace_true_divide; unaryfunc nb_index; binaryfunc nb_matrix_multiply; binaryfunc nb_inplace_matrix_multiply; } PyNumberMethods; Note: Binary and ternary functions must check the type of all their operands, and implement the necessary conversions (at least one of the operands is an instance of the defined type). If the operation is not defined for the given operands, binary and ternary functions must return "Py_NotImplemented", if another error occurred they must return "NULL" and set an exception. Note: The "nb_reserved" field should always be "NULL". It was previously called "nb_long", and was renamed in Python 3.0.1. binaryfunc PyNumberMethods.nb_add binaryfunc PyNumberMethods.nb_subtract binaryfunc PyNumberMethods.nb_multiply binaryfunc PyNumberMethods.nb_remainder binaryfunc PyNumberMethods.nb_divmod ternaryfunc PyNumberMethods.nb_power unaryfunc PyNumberMethods.nb_negative unaryfunc PyNumberMethods.nb_positive unaryfunc PyNumberMethods.nb_absolute inquiry PyNumberMethods.nb_bool unaryfunc PyNumberMethods.nb_invert binaryfunc PyNumberMethods.nb_lshift binaryfunc PyNumberMethods.nb_rshift binaryfunc PyNumberMethods.nb_and binaryfunc PyNumberMethods.nb_xor binaryfunc PyNumberMethods.nb_or unaryfunc PyNumberMethods.nb_int void *PyNumberMethods.nb_reserved unaryfunc PyNumberMethods.nb_float binaryfunc PyNumberMethods.nb_inplace_add binaryfunc PyNumberMethods.nb_inplace_subtract binaryfunc PyNumberMethods.nb_inplace_multiply binaryfunc PyNumberMethods.nb_inplace_remainder ternaryfunc PyNumberMethods.nb_inplace_power binaryfunc PyNumberMethods.nb_inplace_lshift binaryfunc PyNumberMethods.nb_inplace_rshift binaryfunc PyNumberMethods.nb_inplace_and binaryfunc PyNumberMethods.nb_inplace_xor binaryfunc PyNumberMethods.nb_inplace_or binaryfunc PyNumberMethods.nb_floor_divide binaryfunc PyNumberMethods.nb_true_divide binaryfunc PyNumberMethods.nb_inplace_floor_divide binaryfunc PyNumberMethods.nb_inplace_true_divide unaryfunc PyNumberMethods.nb_index binaryfunc PyNumberMethods.nb_matrix_multiply binaryfunc PyNumberMethods.nb_inplace_matrix_multiply Mapping Object Structures ========================= type PyMappingMethods This structure holds pointers to the functions which an object uses to implement the mapping protocol. It has three members: lenfunc PyMappingMethods.mp_length This function is used by "PyMapping_Size()" and "PyObject_Size()", and has the same signature. This slot may be set to "NULL" if the object has no defined length. binaryfunc PyMappingMethods.mp_subscript This function is used by "PyObject_GetItem()" and "PySequence_GetSlice()", and has the same signature as "PyObject_GetItem()". This slot must be filled for the "PyMapping_Check()" function to return "1", it can be "NULL" otherwise. objobjargproc PyMappingMethods.mp_ass_subscript This function is used by "PyObject_SetItem()", "PyObject_DelItem()", "PySequence_SetSlice()" and "PySequence_DelSlice()". It has the same signature as "PyObject_SetItem()", but *v* can also be set to "NULL" to delete an item. If this slot is "NULL", the object does not support item assignment and deletion. Sequence Object Structures ========================== type PySequenceMethods This structure holds pointers to the functions which an object uses to implement the sequence protocol. lenfunc PySequenceMethods.sq_length This function is used by "PySequence_Size()" and "PyObject_Size()", and has the same signature. It is also used for handling negative indices via the "sq_item" and the "sq_ass_item" slots. binaryfunc PySequenceMethods.sq_concat This function is used by "PySequence_Concat()" and has the same signature. It is also used by the "+" operator, after trying the numeric addition via the "nb_add" slot. ssizeargfunc PySequenceMethods.sq_repeat This function is used by "PySequence_Repeat()" and has the same signature. It is also used by the "*" operator, after trying numeric multiplication via the "nb_multiply" slot. ssizeargfunc PySequenceMethods.sq_item This function is used by "PySequence_GetItem()" and has the same signature. It is also used by "PyObject_GetItem()", after trying the subscription via the "mp_subscript" slot. This slot must be filled for the "PySequence_Check()" function to return "1", it can be "NULL" otherwise. Negative indexes are handled as follows: if the "sq_length" slot is filled, it is called and the sequence length is used to compute a positive index which is passed to "sq_item". If "sq_length" is "NULL", the index is passed as is to the function. ssizeobjargproc PySequenceMethods.sq_ass_item This function is used by "PySequence_SetItem()" and has the same signature. It is also used by "PyObject_SetItem()" and "PyObject_DelItem()", after trying the item assignment and deletion via the "mp_ass_subscript" slot. This slot may be left to "NULL" if the object does not support item assignment and deletion. objobjproc PySequenceMethods.sq_contains This function may be used by "PySequence_Contains()" and has the same signature. This slot may be left to "NULL", in this case "PySequence_Contains()" simply traverses the sequence until it finds a match. binaryfunc PySequenceMethods.sq_inplace_concat This function is used by "PySequence_InPlaceConcat()" and has the same signature. It should modify its first operand, and return it. This slot may be left to "NULL", in this case "PySequence_InPlaceConcat()" will fall back to "PySequence_Concat()". It is also used by the augmented assignment "+=", after trying numeric in-place addition via the "nb_inplace_add" slot. ssizeargfunc PySequenceMethods.sq_inplace_repeat This function is used by "PySequence_InPlaceRepeat()" and has the same signature. It should modify its first operand, and return it. This slot may be left to "NULL", in this case "PySequence_InPlaceRepeat()" will fall back to "PySequence_Repeat()". It is also used by the augmented assignment "*=", after trying numeric in-place multiplication via the "nb_inplace_multiply" slot. Buffer Object Structures ======================== type PyBufferProcs This structure holds pointers to the functions required by the Buffer protocol. The protocol defines how an exporter object can expose its internal data to consumer objects. getbufferproc PyBufferProcs.bf_getbuffer The signature of this function is: int (PyObject *exporter, Py_buffer *view, int flags); Handle a request to *exporter* to fill in *view* as specified by *flags*. Except for point (3), an implementation of this function MUST take these steps: 1. Check if the request can be met. If not, raise "BufferError", set view->obj to "NULL" and return "-1". 2. Fill in the requested fields. 3. Increment an internal counter for the number of exports. 4. Set view->obj to *exporter* and increment view->obj. 5. Return "0". If *exporter* is part of a chain or tree of buffer providers, two main schemes can be used: * Re-export: Each member of the tree acts as the exporting object and sets view->obj to a new reference to itself. * Redirect: The buffer request is redirected to the root object of the tree. Here, view->obj will be a new reference to the root object. The individual fields of *view* are described in section Buffer structure, the rules how an exporter must react to specific requests are in section Buffer request types. All memory pointed to in the "Py_buffer" structure belongs to the exporter and must remain valid until there are no consumers left. "format", "shape", "strides", "suboffsets" and "internal" are read- only for the consumer. "PyBuffer_FillInfo()" provides an easy way of exposing a simple bytes buffer while dealing correctly with all request types. "PyObject_GetBuffer()" is the interface for the consumer that wraps this function. releasebufferproc PyBufferProcs.bf_releasebuffer The signature of this function is: void (PyObject *exporter, Py_buffer *view); Handle a request to release the resources of the buffer. If no resources need to be released, "PyBufferProcs.bf_releasebuffer" may be "NULL". Otherwise, a standard implementation of this function will take these optional steps: 1. Decrement an internal counter for the number of exports. 2. If the counter is "0", free all memory associated with *view*. The exporter MUST use the "internal" field to keep track of buffer- specific resources. This field is guaranteed to remain constant, while a consumer MAY pass a copy of the original buffer as the *view* argument. This function MUST NOT decrement view->obj, since that is done automatically in "PyBuffer_Release()" (this scheme is useful for breaking reference cycles). "PyBuffer_Release()" is the interface for the consumer that wraps this function. Async Object Structures ======================= Added in version 3.5. type PyAsyncMethods This structure holds pointers to the functions required to implement *awaitable* and *asynchronous iterator* objects. Here is the structure definition: typedef struct { unaryfunc am_await; unaryfunc am_aiter; unaryfunc am_anext; sendfunc am_send; } PyAsyncMethods; unaryfunc PyAsyncMethods.am_await The signature of this function is: PyObject *am_await(PyObject *self); The returned object must be an *iterator*, i.e. "PyIter_Check()" must return "1" for it. This slot may be set to "NULL" if an object is not an *awaitable*. unaryfunc PyAsyncMethods.am_aiter The signature of this function is: PyObject *am_aiter(PyObject *self); Must return an *asynchronous iterator* object. See "__anext__()" for details. This slot may be set to "NULL" if an object does not implement asynchronous iteration protocol. unaryfunc PyAsyncMethods.am_anext The signature of this function is: PyObject *am_anext(PyObject *self); Must return an *awaitable* object. See "__anext__()" for details. This slot may be set to "NULL". sendfunc PyAsyncMethods.am_send The signature of this function is: PySendResult am_send(PyObject *self, PyObject *arg, PyObject **result); See "PyIter_Send()" for details. This slot may be set to "NULL". Added in version 3.10. Slot Type typedefs ================== typedef PyObject *(*allocfunc)(PyTypeObject *cls, Py_ssize_t nitems) * Part of the Stable ABI.* The purpose of this function is to separate memory allocation from memory initialization. It should return a pointer to a block of memory of adequate length for the instance, suitably aligned, and initialized to zeros, but with "ob_refcnt" set to "1" and "ob_type" set to the type argument. If the type's "tp_itemsize" is non-zero, the object's "ob_size" field should be initialized to *nitems* and the length of the allocated memory block should be "tp_basicsize + nitems*tp_itemsize", rounded up to a multiple of "sizeof(void*)"; otherwise, *nitems* is not used and the length of the block should be "tp_basicsize". This function should not do any other instance initialization, not even to allocate additional memory; that should be done by "tp_new". typedef void (*destructor)(PyObject*) * Part of the Stable ABI.* typedef void (*freefunc)(void*) See "tp_free". typedef PyObject *(*newfunc)(PyTypeObject*, PyObject*, PyObject*) * Part of the Stable ABI.* See "tp_new". typedef int (*initproc)(PyObject*, PyObject*, PyObject*) * Part of the Stable ABI.* See "tp_init". typedef PyObject *(*reprfunc)(PyObject*) * Part of the Stable ABI.* See "tp_repr". typedef PyObject *(*getattrfunc)(PyObject *self, char *attr) * Part of the Stable ABI.* Return the value of the named attribute for the object. typedef int (*setattrfunc)(PyObject *self, char *attr, PyObject *value) * Part of the Stable ABI.* Set the value of the named attribute for the object. The value argument is set to "NULL" to delete the attribute. typedef PyObject *(*getattrofunc)(PyObject *self, PyObject *attr) * Part of the Stable ABI.* Return the value of the named attribute for the object. See "tp_getattro". typedef int (*setattrofunc)(PyObject *self, PyObject *attr, PyObject *value) * Part of the Stable ABI.* Set the value of the named attribute for the object. The value argument is set to "NULL" to delete the attribute. See "tp_setattro". typedef PyObject *(*descrgetfunc)(PyObject*, PyObject*, PyObject*) * Part of the Stable ABI.* See "tp_descr_get". typedef int (*descrsetfunc)(PyObject*, PyObject*, PyObject*) * Part of the Stable ABI.* See "tp_descr_set". typedef Py_hash_t (*hashfunc)(PyObject*) * Part of the Stable ABI.* See "tp_hash". typedef PyObject *(*richcmpfunc)(PyObject*, PyObject*, int) * Part of the Stable ABI.* See "tp_richcompare". typedef PyObject *(*getiterfunc)(PyObject*) * Part of the Stable ABI.* See "tp_iter". typedef PyObject *(*iternextfunc)(PyObject*) * Part of the Stable ABI.* See "tp_iternext". typedef Py_ssize_t (*lenfunc)(PyObject*) * Part of the Stable ABI.* typedef int (*getbufferproc)(PyObject*, Py_buffer*, int) * Part of the Stable ABI since version 3.12.* typedef void (*releasebufferproc)(PyObject*, Py_buffer*) * Part of the Stable ABI since version 3.12.* typedef PyObject *(*unaryfunc)(PyObject*) * Part of the Stable ABI.* typedef PyObject *(*binaryfunc)(PyObject*, PyObject*) * Part of the Stable ABI.* typedef PySendResult (*sendfunc)(PyObject*, PyObject*, PyObject**) See "am_send". typedef PyObject *(*ternaryfunc)(PyObject*, PyObject*, PyObject*) * Part of the Stable ABI.* typedef PyObject *(*ssizeargfunc)(PyObject*, Py_ssize_t) * Part of the Stable ABI.* typedef int (*ssizeobjargproc)(PyObject*, Py_ssize_t, PyObject*) * Part of the Stable ABI.* typedef int (*objobjproc)(PyObject*, PyObject*) * Part of the Stable ABI.* typedef int (*objobjargproc)(PyObject*, PyObject*, PyObject*) * Part of the Stable ABI.* Examples ======== The following are simple examples of Python type definitions. They include common usage you may encounter. Some demonstrate tricky corner cases. For more examples, practical info, and a tutorial, see Defining Extension Types: Tutorial and Defining Extension Types: Assorted Topics. A basic static type: typedef struct { PyObject_HEAD const char *data; } MyObject; static PyTypeObject MyObject_Type = { PyVarObject_HEAD_INIT(NULL, 0) .tp_name = "mymod.MyObject", .tp_basicsize = sizeof(MyObject), .tp_doc = PyDoc_STR("My objects"), .tp_new = myobj_new, .tp_dealloc = (destructor)myobj_dealloc, .tp_repr = (reprfunc)myobj_repr, }; You may also find older code (especially in the CPython code base) with a more verbose initializer: static PyTypeObject MyObject_Type = { PyVarObject_HEAD_INIT(NULL, 0) "mymod.MyObject", /* tp_name */ sizeof(MyObject), /* tp_basicsize */ 0, /* tp_itemsize */ (destructor)myobj_dealloc, /* tp_dealloc */ 0, /* tp_vectorcall_offset */ 0, /* tp_getattr */ 0, /* tp_setattr */ 0, /* tp_as_async */ (reprfunc)myobj_repr, /* tp_repr */ 0, /* tp_as_number */ 0, /* tp_as_sequence */ 0, /* tp_as_mapping */ 0, /* tp_hash */ 0, /* tp_call */ 0, /* tp_str */ 0, /* tp_getattro */ 0, /* tp_setattro */ 0, /* tp_as_buffer */ 0, /* tp_flags */ PyDoc_STR("My objects"), /* tp_doc */ 0, /* tp_traverse */ 0, /* tp_clear */ 0, /* tp_richcompare */ 0, /* tp_weaklistoffset */ 0, /* tp_iter */ 0, /* tp_iternext */ 0, /* tp_methods */ 0, /* tp_members */ 0, /* tp_getset */ 0, /* tp_base */ 0, /* tp_dict */ 0, /* tp_descr_get */ 0, /* tp_descr_set */ 0, /* tp_dictoffset */ 0, /* tp_init */ 0, /* tp_alloc */ myobj_new, /* tp_new */ }; A type that supports weakrefs, instance dicts, and hashing: typedef struct { PyObject_HEAD const char *data; } MyObject; static PyTypeObject MyObject_Type = { PyVarObject_HEAD_INIT(NULL, 0) .tp_name = "mymod.MyObject", .tp_basicsize = sizeof(MyObject), .tp_doc = PyDoc_STR("My objects"), .tp_flags = Py_TPFLAGS_DEFAULT | Py_TPFLAGS_BASETYPE | Py_TPFLAGS_HAVE_GC | Py_TPFLAGS_MANAGED_DICT | Py_TPFLAGS_MANAGED_WEAKREF, .tp_new = myobj_new, .tp_traverse = (traverseproc)myobj_traverse, .tp_clear = (inquiry)myobj_clear, .tp_alloc = PyType_GenericNew, .tp_dealloc = (destructor)myobj_dealloc, .tp_repr = (reprfunc)myobj_repr, .tp_hash = (hashfunc)myobj_hash, .tp_richcompare = PyBaseObject_Type.tp_richcompare, }; A str subclass that cannot be subclassed and cannot be called to create instances (e.g. uses a separate factory func) using "Py_TPFLAGS_DISALLOW_INSTANTIATION" flag: typedef struct { PyUnicodeObject raw; char *extra; } MyStr; static PyTypeObject MyStr_Type = { PyVarObject_HEAD_INIT(NULL, 0) .tp_name = "mymod.MyStr", .tp_basicsize = sizeof(MyStr), .tp_base = NULL, // set to &PyUnicode_Type in module init .tp_doc = PyDoc_STR("my custom str"), .tp_flags = Py_TPFLAGS_DEFAULT | Py_TPFLAGS_DISALLOW_INSTANTIATION, .tp_repr = (reprfunc)myobj_repr, }; The simplest static type with fixed-length instances: typedef struct { PyObject_HEAD } MyObject; static PyTypeObject MyObject_Type = { PyVarObject_HEAD_INIT(NULL, 0) .tp_name = "mymod.MyObject", }; The simplest static type with variable-length instances: typedef struct { PyObject_VAR_HEAD const char *data[1]; } MyObject; static PyTypeObject MyObject_Type = { PyVarObject_HEAD_INIT(NULL, 0) .tp_name = "mymod.MyObject", .tp_basicsize = sizeof(MyObject) - sizeof(char *), .tp_itemsize = sizeof(char *), };