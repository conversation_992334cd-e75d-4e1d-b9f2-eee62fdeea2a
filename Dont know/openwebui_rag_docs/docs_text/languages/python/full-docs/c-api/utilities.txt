Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > utilities.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > utilities.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > utilities.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > utilities.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > c-api > utilities.txt Utilities ********* The functions in this chapter perform various utility tasks, ranging from helping C code be more portable across platforms, using Python modules from C, and parsing function arguments and constructing Python values from C values. * Operating System Utilities * System Functions * Process Control * Importing Modules * Data marshalling support * Parsing arguments and building values * Parsing arguments * Strings and buffers * Numbers * Other objects * API Functions * Building values * String conversion and formatting * PyHash API * Reflection * Codec registry and support functions * Codec lookup API * Registry API for Unicode encoding error handlers * PyTime C API * Types * Clock Functions * Raw Clock Functions * Conversion functions * Support for Perf Maps