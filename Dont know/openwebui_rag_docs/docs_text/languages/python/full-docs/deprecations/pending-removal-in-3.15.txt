Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.15.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.15.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.15.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.15.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.15.txt Pending Removal in Python 3.15 ****************************** * The import system: * Setting "__cached__" on a module while failing to set "__spec__.cached" is deprecated. In Python 3.15, "__cached__" will cease to be set or take into consideration by the import system or standard library. (gh-97879) * Setting "__package__" on a module while failing to set "__spec__.parent" is deprecated. In Python 3.15, "__package__" will cease to be set or take into consideration by the import system or standard library. (gh-97879) * "ctypes": * The undocumented "ctypes.SetPointerType()" function has been deprecated since Python 3.13. * "http.server": * The obsolete and rarely used "CGIHTTPRequestHandler" has been deprecated since Python 3.13. No direct replacement exists. *Anything* is better than CGI to interface a web server with a request handler. * The "--cgi" flag to the **python -m http.server** command-line interface has been deprecated since Python 3.13. * "importlib": * "load_module()" method: use "exec_module()" instead. * "locale": * The "getdefaultlocale()" function has been deprecated since Python 3.11. Its removal was originally planned for Python 3.13 (gh-90817), but has been postponed to Python 3.15. Use "getlocale()", "setlocale()", and "getencoding()" instead. (Contributed by Hugo van Kemenade in gh-111187.) * "pathlib": * "PurePath.is_reserved()" has been deprecated since Python 3.13. Use "os.path.isreserved()" to detect reserved paths on Windows. * "platform": * "java_ver()" has been deprecated since Python 3.13. This function is only useful for Jython support, has a confusing API, and is largely untested. * "sysconfig": * The *check_home* argument of "sysconfig.is_python_build()" has been deprecated since Python 3.12. * "threading": * "RLock()" will take no arguments in Python 3.15. Passing any arguments has been deprecated since Python 3.14, as the Python version does not permit any arguments, but the C version allows any number of positional or keyword arguments, ignoring every argument. * "types": * "types.CodeType": Accessing "co_lnotab" was deprecated in **PEP 626** since 3.10 and was planned to be removed in 3.12, but it only got a proper "DeprecationWarning" in 3.12. May be removed in 3.15. (Contributed by Nikita Sobolev in gh-101866.) * "typing": * The undocumented keyword argument syntax for creating "NamedTuple" classes (e.g. "Point = NamedTuple("Point", x=int, y=int)") has been deprecated since Python 3.13. Use the class-based syntax or the functional syntax instead. * The "typing.no_type_check_decorator()" decorator function has been deprecated since Python 3.13. After eight years in the "typing" module, it has yet to be supported by any major type checker. * "wave": * The "getmark()", "setmark()", and "getmarkers()" methods of the "Wave_read" and "Wave_write" classes have been deprecated since Python 3.13.