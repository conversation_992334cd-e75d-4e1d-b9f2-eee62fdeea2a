Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.16.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.16.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.16.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.16.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-3.16.txt Pending removal in Python 3.16 ****************************** * The import system: * Setting "__loader__" on a module while failing to set "__spec__.loader" is deprecated. In Python 3.16, "__loader__" will cease to be set or taken into consideration by the import system or the standard library. * "array": * The "'u'" format code ("wchar_t") has been deprecated in documentation since Python 3.3 and at runtime since Python 3.13. Use the "'w'" format code ("Py_UCS4") for Unicode characters instead. * "asyncio": * "asyncio.iscoroutinefunction()" is deprecated and will be removed in Python 3.16, use "inspect.iscoroutinefunction()" instead. (Contributed by Jiahao Li and Kumar Aditya in gh-122875.) * "builtins": * Bitwise inversion on boolean types, "~True" or "~False" has been deprecated since Python 3.12, as it produces surprising and unintuitive results ("-2" and "-1"). Use "not x" instead for the logical negation of a Boolean. In the rare case that you need the bitwise inversion of the underlying integer, convert to "int" explicitly ("~int(x)"). * "shutil": * The "ExecError" exception has been deprecated since Python 3.14. It has not been used by any function in "shutil" since Python 3.4, and is now an alias of "RuntimeError". * "symtable": * The "Class.get_methods" method has been deprecated since Python 3.14. * "sys": * The "_enablelegacywindowsfsencoding()" function has been deprecated since Python 3.13. Use the "PYTHONLEGACYWINDOWSFSENCODING" environment variable instead. * "tarfile": * The undocumented and unused "TarFile.tarfile" attribute has been deprecated since Python 3.13.