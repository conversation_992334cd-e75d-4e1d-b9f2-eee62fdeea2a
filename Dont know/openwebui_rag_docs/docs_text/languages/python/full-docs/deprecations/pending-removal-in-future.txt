Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-future.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-future.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-future.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-future.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > deprecations > pending-removal-in-future.txt Pending Removal in Future Versions ********************************** The following APIs will be removed in the future, although there is currently no date scheduled for their removal. * "argparse": Nesting argument groups and nesting mutually exclusive groups are deprecated. * "array"'s "'u'" format code (gh-57281) * "builtins": * "bool(NotImplemented)". * Generators: "throw(type, exc, tb)" and "athrow(type, exc, tb)" signature is deprecated: use "throw(exc)" and "athrow(exc)" instead, the single argument signature. * Currently Python accepts numeric literals immediately followed by keywords, for example "0in x", "1or x", "0if 1else 2". It allows confusing and ambiguous expressions like "[0x1for x in y]" (which can be interpreted as "[0x1 for x in y]" or "[0x1f or x in y]"). A syntax warning is raised if the numeric literal is immediately followed by one of keywords "and", "else", "for", "if", "in", "is" and "or". In a future release it will be changed to a syntax error. (gh-87999) * Support for "__index__()" and "__int__()" method returning non-int type: these methods will be required to return an instance of a strict subclass of "int". * Support for "__float__()" method returning a strict subclass of "float": these methods will be required to return an instance of "float". * Support for "__complex__()" method returning a strict subclass of "complex": these methods will be required to return an instance of "complex". * Delegation of "int()" to "__trunc__()" method. * Passing a complex number as the *real* or *imag* argument in the "complex()" constructor is now deprecated; it should only be passed as a single positional argument. (Contributed by Serhiy Storchaka in gh-109218.) * "calendar": "calendar.January" and "calendar.February" constants are deprecated and replaced by "calendar.JANUARY" and "calendar.FEBRUARY". (Contributed by Prince Roshan in gh-103636.) * "codeobject.co_lnotab": use the "codeobject.co_lines()" method instead. * "datetime": * "utcnow()": use "datetime.datetime.now(tz=datetime.UTC)". * "utcfromtimestamp()": use "datetime.datetime.fromtimestamp(timestamp, tz=datetime.UTC)". * "gettext": Plural value must be an integer. * "importlib": * "cache_from_source()" *debug_override* parameter is deprecated: use the *optimization* parameter instead. * "importlib.metadata": * "EntryPoints" tuple interface. * Implicit "None" on return values. * "logging": the "warn()" method has been deprecated since Python 3.3, use "warning()" instead. * "mailbox": Use of StringIO input and text mode is deprecated, use BytesIO and binary mode instead. * "os": Calling "os.register_at_fork()" in multi-threaded process. * "pydoc.ErrorDuringImport": A tuple value for *exc_info* parameter is deprecated, use an exception instance. * "re": More strict rules are now applied for numerical group references and group names in regular expressions. Only sequence of ASCII digits is now accepted as a numerical reference. The group name in bytes patterns and replacement strings can now only contain ASCII letters and digits and underscore. (Contributed by Serhiy Storchaka in gh-91760.) * "sre_compile", "sre_constants" and "sre_parse" modules. * "shutil": "rmtree()"'s *onerror* parameter is deprecated in Python 3.12; use the *onexc* parameter instead. * "ssl" options and protocols: * "ssl.SSLContext" without protocol argument is deprecated. * "ssl.SSLContext": "set_npn_protocols()" and "selected_npn_protocol()" are deprecated: use ALPN instead. * "ssl.OP_NO_SSL*" options * "ssl.OP_NO_TLS*" options * "ssl.PROTOCOL_SSLv3" * "ssl.PROTOCOL_TLS" * "ssl.PROTOCOL_TLSv1" * "ssl.PROTOCOL_TLSv1_1" * "ssl.PROTOCOL_TLSv1_2" * "ssl.TLSVersion.SSLv3" * "ssl.TLSVersion.TLSv1" * "ssl.TLSVersion.TLSv1_1" * "threading" methods: * "threading.Condition.notifyAll()": use "notify_all()". * "threading.Event.isSet()": use "is_set()". * "threading.Thread.isDaemon()", "threading.Thread.setDaemon()": use "threading.Thread.daemon" attribute. * "threading.Thread.getName()", "threading.Thread.setName()": use "threading.Thread.name" attribute. * "threading.currentThread()": use "threading.current_thread()". * "threading.activeCount()": use "threading.active_count()". * "typing.Text" (gh-92332). * "unittest.IsolatedAsyncioTestCase": it is deprecated to return a value that is not "None" from a test case. * "urllib.parse" deprecated functions: "urlparse()" instead * "splitattr()" * "splithost()" * "splitnport()" * "splitpasswd()" * "splitport()" * "splitquery()" * "splittag()" * "splittype()" * "splituser()" * "splitvalue()" * "to_bytes()" * "urllib.request": "URLopener" and "FancyURLopener" style of invoking requests is deprecated. Use newer "urlopen()" functions and methods. * "wsgiref": "SimpleHandler.stdout.write()" should not do partial writes. * "xml.etree.ElementTree": Testing the truth value of an "Element" is deprecated. In a future release it will always return "True". Prefer explicit "len(elem)" or "elem is not None" tests instead. * "zipimport.zipimporter.load_module()" is deprecated: use "exec_module()" instead.