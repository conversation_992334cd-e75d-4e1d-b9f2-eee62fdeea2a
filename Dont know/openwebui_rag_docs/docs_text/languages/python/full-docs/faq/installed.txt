Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > faq > installed.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > faq > installed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > faq > installed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > faq > installed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > faq > installed.txt "Why is Python Installed on my Computer?" FAQ ********************************************* What is Python? =============== Python is a programming language. It's used for many different applications. It's used in some high schools and colleges as an introductory programming language because Python is easy to learn, but it's also used by professional software developers at places such as Google, NASA, and Lucasfilm Ltd. If you wish to learn more about Python, start with the Beginner's Guide to Python. Why is Python installed on my machine? ====================================== If you find Python installed on your system but don't remember installing it, there are several possible ways it could have gotten there. * Perhaps another user on the computer wanted to learn programming and installed it; you'll have to figure out who's been using the machine and might have installed it. * A third-party application installed on the machine might have been written in Python and included a Python installation. There are many such applications, from GUI programs to network servers and administrative scripts. * Some Windows machines also have Python installed. At this writing we're aware of computers from Hewlett-Packard and Compaq that include Python. Apparently some of HP/Compaq's administrative tools are written in Python. * Many Unix-compatible operating systems, such as macOS and some Linux distributions, have Python installed by default; it's included in the base installation. Can I delete Python? ==================== That depends on where Python came from. If someone installed it deliberately, you can remove it without hurting anything. On Windows, use the Add/Remove Programs icon in the Control Panel. If Python was installed by a third-party application, you can also remove it, but that application will no longer work. You should use that application's uninstaller rather than removing Python directly. If Python came with your operating system, removing it is not recommended. If you remove it, whatever tools were written in Python will no longer run, and some of them might be important to you. Reinstalling the whole system would then be required to fix things again.