Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > howto > pyporting.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > howto > pyporting.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > howto > pyporting.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > howto > pyporting.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > howto > pyporting.txt How to port Python 2 Code to Python 3 ************************************* author: Brett Cannon Python 2 reached its official end-of-life at the start of 2020. This means that no new bug reports, fixes, or changes will be made to Python 2 - it's no longer supported: see **PEP 373** and status of Python versions. If you are looking to port an extension module instead of pure Python code, please see Porting Extension Modules to Python 3. The archived python-porting mailing list may contain some useful guidance. Since Python 3.11 the original porting guide was discontinued. You can find the old guide in the archive. Third-party guides ================== There are also multiple third-party guides that might be useful: * Guide by Fedora * PyCon 2020 tutorial * Guide by DigitalOcean * Guide by ActiveState