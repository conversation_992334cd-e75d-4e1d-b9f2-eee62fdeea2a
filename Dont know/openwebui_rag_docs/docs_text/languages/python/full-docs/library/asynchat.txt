Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asynchat.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asynchat.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asynchat.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asynchat.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asynchat.txt "asynchat" --- Asynchronous socket command/response handler *********************************************************** Deprecated since version 3.6, removed in version 3.12. This module is no longer part of the Python standard library. It was removed in Python 3.12 after being deprecated in Python 3.6. The removal was decided in **PEP 594**. Applications should use the "asyncio" module instead. The last version of Python that provided the "asynchat" module was Python 3.11.