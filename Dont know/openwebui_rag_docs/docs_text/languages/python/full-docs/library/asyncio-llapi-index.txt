Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asyncio-llapi-index.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asyncio-llapi-index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asyncio-llapi-index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asyncio-llapi-index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > asyncio-llapi-index.txt Low-level API Index ******************* This page lists all low-level asyncio APIs. Obtaining the Event Loop ======================== +----------------------------------------------------+----------------------------------------------------+ | "asyncio.get_running_loop()" | The **preferred** function to get the running | | | event loop. | +----------------------------------------------------+----------------------------------------------------+ | "asyncio.get_event_loop()" | Get an event loop instance (running or current via | | | the current policy). | +----------------------------------------------------+----------------------------------------------------+ | "asyncio.set_event_loop()" | Set the event loop as current via the current | | | policy. | +----------------------------------------------------+----------------------------------------------------+ | "asyncio.new_event_loop()" | Create a new event loop. | +----------------------------------------------------+----------------------------------------------------+ -[ Examples ]- * Using asyncio.get_running_loop(). Event Loop Methods ================== See also the main documentation section about the Event Loop Methods. -[ Lifecycle ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.run_until_complete()" | Run a Future/Task/awaitable until complete. | +----------------------------------------------------+----------------------------------------------------+ | "loop.run_forever()" | Run the event loop forever. | +----------------------------------------------------+----------------------------------------------------+ | "loop.stop()" | Stop the event loop. | +----------------------------------------------------+----------------------------------------------------+ | "loop.close()" | Close the event loop. | +----------------------------------------------------+----------------------------------------------------+ | "loop.is_running()" | Return "True" if the event loop is running. | +----------------------------------------------------+----------------------------------------------------+ | "loop.is_closed()" | Return "True" if the event loop is closed. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.shutdown_asyncgens()" | Close asynchronous generators. | +----------------------------------------------------+----------------------------------------------------+ -[ Debugging ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.set_debug()" | Enable or disable the debug mode. | +----------------------------------------------------+----------------------------------------------------+ | "loop.get_debug()" | Get the current debug mode. | +----------------------------------------------------+----------------------------------------------------+ -[ Scheduling Callbacks ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.call_soon()" | Invoke a callback soon. | +----------------------------------------------------+----------------------------------------------------+ | "loop.call_soon_threadsafe()" | A thread-safe variant of "loop.call_soon()". | +----------------------------------------------------+----------------------------------------------------+ | "loop.call_later()" | Invoke a callback *after* the given time. | +----------------------------------------------------+----------------------------------------------------+ | "loop.call_at()" | Invoke a callback *at* the given time. | +----------------------------------------------------+----------------------------------------------------+ -[ Thread/Process Pool ]- +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.run_in_executor()" | Run a CPU-bound or other blocking function in a | | | "concurrent.futures" executor. | +----------------------------------------------------+----------------------------------------------------+ | "loop.set_default_executor()" | Set the default executor for | | | "loop.run_in_executor()". | +----------------------------------------------------+----------------------------------------------------+ -[ Tasks and Futures ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.create_future()" | Create a "Future" object. | +----------------------------------------------------+----------------------------------------------------+ | "loop.create_task()" | Schedule coroutine as a "Task". | +----------------------------------------------------+----------------------------------------------------+ | "loop.set_task_factory()" | Set a factory used by "loop.create_task()" to | | | create "Tasks". | +----------------------------------------------------+----------------------------------------------------+ | "loop.get_task_factory()" | Get the factory "loop.create_task()" uses to | | | create "Tasks". | +----------------------------------------------------+----------------------------------------------------+ -[ DNS ]- +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.getaddrinfo()" | Asynchronous version of "socket.getaddrinfo()". | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.getnameinfo()" | Asynchronous version of "socket.getnameinfo()". | +----------------------------------------------------+----------------------------------------------------+ -[ Networking and IPC ]- +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.create_connection()" | Open a TCP connection. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.create_server()" | Create a TCP server. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.create_unix_connection()" | Open a Unix socket connection. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.create_unix_server()" | Create a Unix socket server. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.connect_accepted_socket()" | Wrap a "socket" into a "(transport, protocol)" | | | pair. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.create_datagram_endpoint()" | Open a datagram (UDP) connection. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sendfile()" | Send a file over a transport. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.start_tls()" | Upgrade an existing connection to TLS. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.connect_read_pipe()" | Wrap a read end of a pipe into a "(transport, | | | protocol)" pair. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.connect_write_pipe()" | Wrap a write end of a pipe into a "(transport, | | | protocol)" pair. | +----------------------------------------------------+----------------------------------------------------+ -[ Sockets ]- +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_recv()" | Receive data from the "socket". | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_recv_into()" | Receive data from the "socket" into a buffer. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_recvfrom()" | Receive a datagram from the "socket". | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_recvfrom_into()" | Receive a datagram from the "socket" into a | | | buffer. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_sendall()" | Send data to the "socket". | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_sendto()" | Send a datagram via the "socket" to the given | | | address. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_connect()" | Connect the "socket". | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_accept()" | Accept a "socket" connection. | +----------------------------------------------------+----------------------------------------------------+ | "await" "loop.sock_sendfile()" | Send a file over the "socket". | +----------------------------------------------------+----------------------------------------------------+ | "loop.add_reader()" | Start watching a file descriptor for read | | | availability. | +----------------------------------------------------+----------------------------------------------------+ | "loop.remove_reader()" | Stop watching a file descriptor for read | | | availability. | +----------------------------------------------------+----------------------------------------------------+ | "loop.add_writer()" | Start watching a file descriptor for write | | | availability. | +----------------------------------------------------+----------------------------------------------------+ | "loop.remove_writer()" | Stop watching a file descriptor for write | | | availability. | +----------------------------------------------------+----------------------------------------------------+ -[ Unix Signals ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.add_signal_handler()" | Add a handler for a "signal". | +----------------------------------------------------+----------------------------------------------------+ | "loop.remove_signal_handler()" | Remove a handler for a "signal". | +----------------------------------------------------+----------------------------------------------------+ -[ Subprocesses ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.subprocess_exec()" | Spawn a subprocess. | +----------------------------------------------------+----------------------------------------------------+ | "loop.subprocess_shell()" | Spawn a subprocess from a shell command. | +----------------------------------------------------+----------------------------------------------------+ -[ Error Handling ]- +----------------------------------------------------+----------------------------------------------------+ | "loop.call_exception_handler()" | Call the exception handler. | +----------------------------------------------------+----------------------------------------------------+ | "loop.set_exception_handler()" | Set a new exception handler. | +----------------------------------------------------+----------------------------------------------------+ | "loop.get_exception_handler()" | Get the current exception handler. | +----------------------------------------------------+----------------------------------------------------+ | "loop.default_exception_handler()" | The default exception handler implementation. | +----------------------------------------------------+----------------------------------------------------+ -[ Examples ]- * Using asyncio.new_event_loop() and loop.run_forever(). * Using loop.call_later(). * Using "loop.create_connection()" to implement an echo-client. * Using "loop.create_connection()" to connect a socket. * Using add_reader() to watch an FD for read events. * Using loop.add_signal_handler(). * Using loop.subprocess_exec(). Transports ========== All transports implement the following methods: +----------------------------------------------------+----------------------------------------------------+ | "transport.close()" | Close the transport. | +----------------------------------------------------+----------------------------------------------------+ | "transport.is_closing()" | Return "True" if the transport is closing or is | | | closed. | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_extra_info()" | Request for information about the transport. | +----------------------------------------------------+----------------------------------------------------+ | "transport.set_protocol()" | Set a new protocol. | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_protocol()" | Return the current protocol. | +----------------------------------------------------+----------------------------------------------------+ Transports that can receive data (TCP and Unix connections, pipes, etc). Returned from methods like "loop.create_connection()", "loop.create_unix_connection()", "loop.connect_read_pipe()", etc: -[ Read Transports ]- +----------------------------------------------------+----------------------------------------------------+ | "transport.is_reading()" | Return "True" if the transport is receiving. | +----------------------------------------------------+----------------------------------------------------+ | "transport.pause_reading()" | Pause receiving. | +----------------------------------------------------+----------------------------------------------------+ | "transport.resume_reading()" | Resume receiving. | +----------------------------------------------------+----------------------------------------------------+ Transports that can Send data (TCP and Unix connections, pipes, etc). Returned from methods like "loop.create_connection()", "loop.create_unix_connection()", "loop.connect_write_pipe()", etc: -[ Write Transports ]- +----------------------------------------------------+----------------------------------------------------+ | "transport.write()" | Write data to the transport. | +----------------------------------------------------+----------------------------------------------------+ | "transport.writelines()" | Write buffers to the transport. | +----------------------------------------------------+----------------------------------------------------+ | "transport.can_write_eof()" | Return "True" if the transport supports sending | | | EOF. | +----------------------------------------------------+----------------------------------------------------+ | "transport.write_eof()" | Close and send EOF after flushing buffered data. | +----------------------------------------------------+----------------------------------------------------+ | "transport.abort()" | Close the transport immediately. | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_write_buffer_size()" | Return the current size of the output buffer. | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_write_buffer_limits()" | Return high and low water marks for write flow | | | control. | +----------------------------------------------------+----------------------------------------------------+ | "transport.set_write_buffer_limits()" | Set new high and low water marks for write flow | | | control. | +----------------------------------------------------+----------------------------------------------------+ Transports returned by "loop.create_datagram_endpoint()": -[ Datagram Transports ]- +----------------------------------------------------+----------------------------------------------------+ | "transport.sendto()" | Send data to the remote peer. | +----------------------------------------------------+----------------------------------------------------+ | "transport.abort()" | Close the transport immediately. | +----------------------------------------------------+----------------------------------------------------+ Low-level transport abstraction over subprocesses. Returned by "loop.subprocess_exec()" and "loop.subprocess_shell()": -[ Subprocess Transports ]- +----------------------------------------------------+----------------------------------------------------+ | "transport.get_pid()" | Return the subprocess process id. | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_pipe_transport()" | Return the transport for the requested | | | communication pipe (*stdin*, *stdout*, or | | | *stderr*). | +----------------------------------------------------+----------------------------------------------------+ | "transport.get_returncode()" | Return the subprocess return code. | +----------------------------------------------------+----------------------------------------------------+ | "transport.kill()" | Kill the subprocess. | +----------------------------------------------------+----------------------------------------------------+ | "transport.send_signal()" | Send a signal to the subprocess. | +----------------------------------------------------+----------------------------------------------------+ | "transport.terminate()" | Stop the subprocess. | +----------------------------------------------------+----------------------------------------------------+ | "transport.close()" | Kill the subprocess and close all pipes. | +----------------------------------------------------+----------------------------------------------------+ Protocols ========= Protocol classes can implement the following **callback methods**: +----------------------------------------------------+----------------------------------------------------+ | "callback" "connection_made()" | Called when a connection is made. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "connection_lost()" | Called when the connection is lost or closed. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "pause_writing()" | Called when the transport's buffer goes over the | | | high water mark. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "resume_writing()" | Called when the transport's buffer drains below | | | the low water mark. | +----------------------------------------------------+----------------------------------------------------+ -[ Streaming Protocols (TCP, Unix Sockets, Pipes) ]- +----------------------------------------------------+----------------------------------------------------+ | "callback" "data_received()" | Called when some data is received. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "eof_received()" | Called when an EOF is received. | +----------------------------------------------------+----------------------------------------------------+ -[ Buffered Streaming Protocols ]- +----------------------------------------------------+----------------------------------------------------+ | "callback" "get_buffer()" | Called to allocate a new receive buffer. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "buffer_updated()" | Called when the buffer was updated with the | | | received data. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "eof_received()" | Called when an EOF is received. | +----------------------------------------------------+----------------------------------------------------+ -[ Datagram Protocols ]- +----------------------------------------------------+----------------------------------------------------+ | "callback" "datagram_received()" | Called when a datagram is received. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "error_received()" | Called when a previous send or receive operation | | | raises an "OSError". | +----------------------------------------------------+----------------------------------------------------+ -[ Subprocess Protocols ]- +----------------------------------------------------+----------------------------------------------------+ | "callback" "pipe_data_received()" | Called when the child process writes data into its | | | *stdout* or *stderr* pipe. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "pipe_connection_lost()" | Called when one of the pipes communicating with | | | the child process is closed. | +----------------------------------------------------+----------------------------------------------------+ | "callback" "process_exited()" | Called when the child process has exited. It can | | | be called before "pipe_data_received()" and | | | "pipe_connection_lost()" methods. | +----------------------------------------------------+----------------------------------------------------+ Event Loop Policies =================== Policies is a low-level mechanism to alter the behavior of functions like "asyncio.get_event_loop()". See also the main policies section for more details. -[ Accessing Policies ]- +----------------------------------------------------+----------------------------------------------------+ | "asyncio.get_event_loop_policy()" | Return the current process-wide policy. | +----------------------------------------------------+----------------------------------------------------+ | "asyncio.set_event_loop_policy()" | Set a new process-wide policy. | +----------------------------------------------------+----------------------------------------------------+ | "AbstractEventLoopPolicy" | Base class for policy objects. | +----------------------------------------------------+----------------------------------------------------+