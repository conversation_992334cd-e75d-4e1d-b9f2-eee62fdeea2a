Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cgi.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cgi.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cgi.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cgi.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cgi.txt "cgi" --- Common Gateway Interface support ****************************************** Deprecated since version 3.11, removed in version 3.13. This module is no longer part of the Python standard library. It was removed in Python 3.13 after being deprecated in Python 3.11. The removal was decided in **PEP 594**. A fork of the module on PyPI can be used instead: legacy-cgi. This is a copy of the cgi module, no longer maintained or supported by the core Python team. The last version of Python that provided the "cgi" module was Python 3.12.