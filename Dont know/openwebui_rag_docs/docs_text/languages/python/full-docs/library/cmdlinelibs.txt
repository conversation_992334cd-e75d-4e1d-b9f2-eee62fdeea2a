Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cmdlinelibs.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cmdlinelibs.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cmdlinelibs.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cmdlinelibs.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > cmdlinelibs.txt Command Line Interface Libraries ******************************** The modules described in this chapter assist with implementing command line and terminal interfaces for applications. Here's an overview: * "argparse" --- Parser for command-line options, arguments and subcommands * "optparse" --- Parser for command line options * "getpass" --- Portable password input * "fileinput" --- Iterate over lines from multiple input streams * "curses" --- Terminal handling for character-cell displays * "curses.textpad" --- Text input widget for curses programs * "curses.ascii" --- Utilities for ASCII characters * "curses.panel" --- A panel stack extension for curses