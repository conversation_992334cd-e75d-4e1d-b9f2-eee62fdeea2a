Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > concurrency.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > concurrency.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > concurrency.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > concurrency.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > concurrency.txt Concurrent Execution ******************** The modules described in this chapter provide support for concurrent execution of code. The appropriate choice of tool will depend on the task to be executed (CPU bound vs IO bound) and preferred style of development (event driven cooperative multitasking vs preemptive multitasking). Here's an overview: * "threading" --- Thread-based parallelism * Thread-Local Data * Thread Objects * Lock Objects * RLock Objects * Condition Objects * Semaphore Objects * "Semaphore" Example * Event Objects * Timer Objects * Barrier Objects * Using locks, conditions, and semaphores in the "with" statement * "multiprocessing" --- Process-based parallelism * Introduction * The "Process" class * Contexts and start methods * Exchanging objects between processes * Synchronization between processes * Sharing state between processes * Using a pool of workers * Reference * "Process" and exceptions * Pipes and Queues * Miscellaneous * Connection Objects * Synchronization primitives * Shared "ctypes" Objects * The "multiprocessing.sharedctypes" module * Managers * Customized managers * Using a remote manager * Proxy Objects * Cleanup * Process Pools * Listeners and Clients * Address Formats * Authentication keys * Logging * The "multiprocessing.dummy" module * Programming guidelines * All start methods * The *spawn* and *forkserver* start methods * Examples * "multiprocessing.shared_memory" --- Shared memory for direct access across processes * The "concurrent" package * "concurrent.futures" --- Launching parallel tasks * Executor Objects * ThreadPoolExecutor * ThreadPoolExecutor Example * ProcessPoolExecutor * ProcessPoolExecutor Example * Future Objects * Module Functions * Exception classes * "subprocess" --- Subprocess management * Using the "subprocess" Module * Frequently Used Arguments * Popen Constructor * Exceptions * Security Considerations * Popen Objects * Windows Popen Helpers * Windows Constants * Older high-level API * Replacing Older Functions with the "subprocess" Module * Replacing **/bin/sh** shell command substitution * Replacing shell pipeline * Replacing "os.system()" * Replacing the "os.spawn" family * Replacing "os.popen()", "os.popen2()", "os.popen3()" * Replacing functions from the "popen2" module * Legacy Shell Invocation Functions * Notes * Converting an argument sequence to a string on Windows * Disabling use of "vfork()" or "posix_spawn()" * "sched" --- Event scheduler * Scheduler Objects * "queue" --- A synchronized queue class * Queue Objects * Terminating queues * SimpleQueue Objects * "contextvars" --- Context Variables * Context Variables * Manual Context Management * asyncio support The following are support modules for some of the above services: * "_thread" --- Low-level threading API