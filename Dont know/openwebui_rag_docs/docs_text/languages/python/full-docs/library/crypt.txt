Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > crypt.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > crypt.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > crypt.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > crypt.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > crypt.txt "crypt" --- Function to check Unix passwords ******************************************** Deprecated since version 3.11, removed in version 3.13. This module is no longer part of the Python standard library. It was removed in Python 3.13 after being deprecated in Python 3.11. The removal was decided in **PEP 594**. Applications can use the "hashlib" module from the standard library. Other possible replacements are third-party libraries from PyPI: legacycrypt, bcrypt, argon2-cffi, or passlib. These are not supported or maintained by the Python core team. The last version of Python that provided the "crypt" module was Python 3.12.