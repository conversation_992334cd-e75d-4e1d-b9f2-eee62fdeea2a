Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > curses.ascii.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > curses.ascii.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > curses.ascii.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > curses.ascii.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > curses.ascii.txt "curses.ascii" --- Utilities for ASCII characters ************************************************* **Source code:** Lib/curses/ascii.py ====================================================================== The "curses.ascii" module supplies name constants for ASCII characters and functions to test membership in various ASCII character classes. The constants supplied are names for control characters as follows: +-----------------+------------------------------------------------+ | Name | Meaning | |=================|================================================| | curses.ascii.N | | | UL | | +-----------------+------------------------------------------------+ | curses.ascii.S | Start of heading, console interrupt | | OH | | +-----------------+------------------------------------------------+ | curses.ascii.S | Start of text | | TX | | +-----------------+------------------------------------------------+ | curses.ascii.E | End of text | | TX | | +-----------------+------------------------------------------------+ | curses.ascii.E | End of transmission | | OT | | +-----------------+------------------------------------------------+ | curses.ascii.E | Enquiry, goes with "ACK" flow control | | NQ | | +-----------------+------------------------------------------------+ | curses.ascii.A | Acknowledgement | | CK | | +-----------------+------------------------------------------------+ | curses.ascii.B | Bell | | EL | | +-----------------+------------------------------------------------+ | curses.ascii.BS | Backspace | +-----------------+------------------------------------------------+ | curses.ascii.T | Tab | | AB | | +-----------------+------------------------------------------------+ | curses.ascii.HT | Alias for "TAB": "Horizontal tab" | +-----------------+------------------------------------------------+ | curses.ascii.LF | Line feed | +-----------------+------------------------------------------------+ | curses.ascii.NL | Alias for "LF": "New line" | +-----------------+------------------------------------------------+ | curses.ascii.VT | Vertical tab | +-----------------+------------------------------------------------+ | curses.ascii.FF | Form feed | +-----------------+------------------------------------------------+ | curses.ascii.CR | Carriage return | +-----------------+------------------------------------------------+ | curses.ascii.SO | Shift-out, begin alternate character set | +-----------------+------------------------------------------------+ | curses.ascii.SI | Shift-in, resume default character set | +-----------------+------------------------------------------------+ | curses.ascii.D | Data-link escape | | LE | | +-----------------+------------------------------------------------+ | curses.ascii.D | XON, for flow control | | C1 | | +-----------------+------------------------------------------------+ | curses.ascii.D | Device control 2, block-mode flow control | | C2 | | +-----------------+------------------------------------------------+ | curses.ascii.D | XOFF, for flow control | | C3 | | +-----------------+------------------------------------------------+ | curses.ascii.D | Device control 4 | | C4 | | +-----------------+------------------------------------------------+ | curses.ascii.N | Negative acknowledgement | | AK | | +-----------------+------------------------------------------------+ | curses.ascii.S | Synchronous idle | | YN | | +-----------------+------------------------------------------------+ | curses.ascii.E | End transmission block | | TB | | +-----------------+------------------------------------------------+ | curses.ascii.C | Cancel | | AN | | +-----------------+------------------------------------------------+ | curses.ascii.EM | End of medium | +-----------------+------------------------------------------------+ | curses.ascii.S | Substitute | | UB | | +-----------------+------------------------------------------------+ | curses.ascii.E | Escape | | SC | | +-----------------+------------------------------------------------+ | curses.ascii.FS | File separator | +-----------------+------------------------------------------------+ | curses.ascii.GS | Group separator | +-----------------+------------------------------------------------+ | curses.ascii.RS | Record separator, block-mode terminator | +-----------------+------------------------------------------------+ | curses.ascii.US | Unit separator | +-----------------+------------------------------------------------+ | curses.ascii.SP | Space | +-----------------+------------------------------------------------+ | curses.ascii.D | Delete | | EL | | +-----------------+------------------------------------------------+ Note that many of these have little practical significance in modern usage. The mnemonics derive from teleprinter conventions that predate digital computers. The module supplies the following functions, patterned on those in the standard C library: curses.ascii.isalnum(c) Checks for an ASCII alphanumeric character; it is equivalent to "isalpha(c) or isdigit(c)". curses.ascii.isalpha(c) Checks for an ASCII alphabetic character; it is equivalent to "isupper(c) or islower(c)". curses.ascii.isascii(c) Checks for a character value that fits in the 7-bit ASCII set. curses.ascii.isblank(c) Checks for an ASCII whitespace character; space or horizontal tab. curses.ascii.iscntrl(c) Checks for an ASCII control character (in the range 0x00 to 0x1f or 0x7f). curses.ascii.isdigit(c) Checks for an ASCII decimal digit, "'0'" through "'9'". This is equivalent to "c in string.digits". curses.ascii.isgraph(c) Checks for ASCII any printable character except space. curses.ascii.islower(c) Checks for an ASCII lower-case character. curses.ascii.isprint(c) Checks for any ASCII printable character including space. curses.ascii.ispunct(c) Checks for any printable ASCII character which is not a space or an alphanumeric character. curses.ascii.isspace(c) Checks for ASCII white-space characters; space, line feed, carriage return, form feed, horizontal tab, vertical tab. curses.ascii.isupper(c) Checks for an ASCII uppercase letter. curses.ascii.isxdigit(c) Checks for an ASCII hexadecimal digit. This is equivalent to "c in string.hexdigits". curses.ascii.isctrl(c) Checks for an ASCII control character (ordinal values 0 to 31). curses.ascii.ismeta(c) Checks for a non-ASCII character (ordinal values 0x80 and above). These functions accept either integers or single-character strings; when the argument is a string, it is first converted using the built- in function "ord()". Note that all these functions check ordinal bit values derived from the character of the string you pass in; they do not actually know anything about the host machine's character encoding. The following two functions take either a single-character string or integer byte value; they return a value of the same type. curses.ascii.ascii(c) Return the ASCII value corresponding to the low 7 bits of *c*. curses.ascii.ctrl(c) Return the control character corresponding to the given character (the character bit value is bitwise-anded with 0x1f). curses.ascii.alt(c) Return the 8-bit character corresponding to the given ASCII character (the character bit value is bitwise-ored with 0x80). The following function takes either a single-character string or integer value; it returns a string. curses.ascii.unctrl(c) Return a string representation of the ASCII character *c*. If *c* is printable, this string is the character itself. If the character is a control character (0x00--0x1f) the string consists of a caret ("'^'") followed by the corresponding uppercase letter. If the character is an ASCII delete (0x7f) the string is "'^?'". If the character has its meta bit (0x80) set, the meta bit is stripped, the preceding rules applied, and "'!'" prepended to the result. curses.ascii.controlnames A 33-element string array that contains the ASCII mnemonics for the thirty-two ASCII control characters from 0 (NUL) to 0x1f (US), in order, plus the mnemonic "SP" for the space character.