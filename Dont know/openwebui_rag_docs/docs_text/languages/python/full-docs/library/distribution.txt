Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distribution.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distribution.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distribution.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distribution.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distribution.txt Software Packaging and Distribution *********************************** These libraries help you with publishing and installing Python software. While these modules are designed to work in conjunction with the Python Package Index, they can also be used with a local index server, or without any index server at all. * "ensurepip" --- Bootstrapping the "pip" installer * Command line interface * Module API * "venv" --- Creation of virtual environments * Creating virtual environments * How venvs work * API * An example of extending "EnvBuilder" * "zipapp" --- Manage executable Python zip archives * Basic Example * Command-Line Interface * Python API * Examples * Specifying the Interpreter * Creating Standalone Applications with zipapp * Caveats * The Python Zip Application Archive Format