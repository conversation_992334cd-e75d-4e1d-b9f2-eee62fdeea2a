Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distutils.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distutils.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distutils.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distutils.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > distutils.txt "distutils" --- Building and installing Python modules ****************************************************** Deprecated since version 3.10, removed in version 3.12. This module is no longer part of the Python standard library. It was removed in Python 3.12 after being deprecated in Python 3.10. The removal was decided in **PEP 632**, which has migration advice. The last version of Python that provided the "distutils" module was Python 3.11.