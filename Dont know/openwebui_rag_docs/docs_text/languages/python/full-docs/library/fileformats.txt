Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > fileformats.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > fileformats.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > fileformats.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > fileformats.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > fileformats.txt File Formats ************ The modules described in this chapter parse various miscellaneous file formats that aren't markup languages and are not related to e-mail. * "csv" --- CSV File Reading and Writing * Module Contents * Dialects and Formatting Parameters * Reader Objects * Writer Objects * Examples * "configparser" --- Configuration file parser * Quick Start * Supported Datatypes * Fallback Values * Supported INI File Structure * Unnamed Sections * Interpolation of values * Mapping Protocol Access * Customizing Parser Behaviour * Legacy API Examples * ConfigParser Objects * RawConfigParser Objects * Exceptions * "tomllib" --- Parse TOML files * Examples * Conversion Table * "netrc" --- netrc file processing * netrc Objects * "plistlib" --- Generate and parse Apple ".plist" files * Examples