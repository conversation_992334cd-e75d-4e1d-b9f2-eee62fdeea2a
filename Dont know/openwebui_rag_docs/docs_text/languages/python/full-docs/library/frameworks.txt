Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > frameworks.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > frameworks.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > frameworks.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > frameworks.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > frameworks.txt Program Frameworks ****************** The modules described in this chapter are frameworks that will largely dictate the structure of your program. Currently the modules described here are all oriented toward writing command-line interfaces. The full list of modules described in this chapter is: * "turtle" --- Turtle graphics * Introduction * Get started * Tutorial * Starting a turtle environment * Basic drawing * Pen control * The turtle's position * Making algorithmic patterns * How to... * Get started as quickly as possible * Use the "turtle" module namespace * Use turtle graphics in a script * Use object-oriented turtle graphics * Turtle graphics reference * Turtle methods * Methods of TurtleScreen/Screen * Methods of RawTurtle/Turtle and corresponding functions * Turtle motion * Tell Turtle's state * Settings for measurement * Pen control * Drawing state * Color control * Filling * More drawing control * Turtle state * Visibility * Appearance * Using events * Special Turtle methods * Compound shapes * Methods of TurtleScreen/Screen and corresponding functions * Window control * Animation control * Using screen events * Input methods * Settings and special methods * Methods specific to Screen, not inherited from TurtleScreen * Public classes * Explanation * Help and configuration * How to use help * Translation of docstrings into different languages * How to configure Screen and Turtles * "turtledemo" --- Demo scripts * Changes since Python 2.6 * Changes since Python 3.0 * "cmd" --- Support for line-oriented command interpreters * Cmd Objects * Cmd Example * "shlex" --- Simple lexical analysis * shlex Objects * Parsing Rules * Improved Compatibility with Shells