Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > functional.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > functional.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > functional.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > functional.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > functional.txt Functional Programming Modules ****************************** The modules described in this chapter provide functions and classes that support a functional programming style, and general operations on callables. The following modules are documented in this chapter: * "itertools" --- Functions creating iterators for efficient looping * Itertool Functions * Itertools Recipes * "functools" --- Higher-order functions and operations on callable objects * "partial" Objects * "operator" --- Standard operators as functions * Mapping Operators to Functions * In-place Operators