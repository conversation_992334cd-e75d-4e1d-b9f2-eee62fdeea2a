Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imghdr.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imghdr.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imghdr.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imghdr.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imghdr.txt "imghdr" --- Determine the type of an image ******************************************* Deprecated since version 3.11, removed in version 3.13. This module is no longer part of the Python standard library. It was removed in Python 3.13 after being deprecated in Python 3.11. The removal was decided in **PEP 594**. Possible replacements are third-party libraries from PyPI: filetype, puremagic, or python-magic. These are not supported or maintained by the Python core team. The last version of Python that provided the "imghdr" module was Python 3.12.