Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imp.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imp.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imp.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imp.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > imp.txt "imp" --- Access the import internals ************************************* Deprecated since version 3.4, removed in version 3.12. This module is no longer part of the Python standard library. It was removed in Python 3.12 after being deprecated in Python 3.4. The removal notice includes guidance for migrating code from "imp" to "importlib". The last version of Python that provided the "imp" module was Python 3.11.