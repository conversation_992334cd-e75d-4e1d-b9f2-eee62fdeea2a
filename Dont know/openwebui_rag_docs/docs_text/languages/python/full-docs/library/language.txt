Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > language.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > language.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > language.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > language.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > language.txt Python Language Services ************************ Python provides a number of modules to assist in working with the Python language. These modules support tokenizing, parsing, syntax analysis, bytecode disassembly, and various other facilities. These modules include: * "ast" --- Abstract Syntax Trees * Abstract Grammar * Node classes * Root nodes * Literals * Variables * Expressions * Subscripting * Comprehensions * Statements * Imports * Control flow * Pattern matching * Type parameters * Function and class definitions * Async and await * "ast" Helpers * Compiler Flags * Command-Line Usage * "symtable" --- Access to the compiler's symbol tables * Generating Symbol Tables * Examining Symbol Tables * Command-Line Usage * "token" --- Constants used with Python parse trees * "keyword" --- Testing for Python keywords * "tokenize" --- Tokenizer for Python source * Tokenizing Input * Command-Line Usage * Examples * "tabnanny" --- Detection of ambiguous indentation * "pyclbr" --- Python module browser support * Function Objects * Class Objects * "py_compile" --- Compile Python source files * Command-Line Interface * "compileall" --- Byte-compile Python libraries * Command-line use * Public functions * "dis" --- Disassembler for Python bytecode * Command-line interface * Bytecode analysis * Analysis functions * Python Bytecode Instructions * Opcode collections * "pickletools" --- Tools for pickle developers * Command line usage * Command line options * Programmatic Interface