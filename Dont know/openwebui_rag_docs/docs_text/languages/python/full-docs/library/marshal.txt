Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > marshal.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > marshal.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > marshal.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > marshal.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > marshal.txt "marshal" --- Internal Python object serialization ************************************************** ====================================================================== This module contains functions that can read and write Python values in a binary format. The format is specific to Python, but independent of machine architecture issues (e.g., you can write a Python value to a file on a PC, transport the file to a Mac, and read it back there). Details of the format are undocumented on purpose; it may change between Python versions (although it rarely does). [1] This is not a general "persistence" module. For general persistence and transfer of Python objects through RPC calls, see the modules "pickle" and "shelve". The "marshal" module exists mainly to support reading and writing the "pseudo-compiled" code for Python modules of ".pyc" files. Therefore, the Python maintainers reserve the right to modify the marshal format in backward incompatible ways should the need arise. The format of code objects is not compatible between Python versions, even if the version of the format is the same. De- serializing a code object in the incorrect Python version has undefined behavior. If you're serializing and de-serializing Python objects, use the "pickle" module instead -- the performance is comparable, version independence is guaranteed, and pickle supports a substantially wider range of objects than marshal. Warning: The "marshal" module is not intended to be secure against erroneous or maliciously constructed data. Never unmarshal data received from an untrusted or unauthenticated source. Not all Python object types are supported; in general, only objects whose value is independent from a particular invocation of Python can be written and read by this module. The following types are supported: booleans, integers, floating-point numbers, complex numbers, strings, bytes, bytearrays, tuples, lists, sets, frozensets, dictionaries, and code objects (if *allow_code* is true), where it should be understood that tuples, lists, sets, frozensets and dictionaries are only supported as long as the values contained therein are themselves supported. The singletons "None", "Ellipsis" and "StopIteration" can also be marshalled and unmarshalled. For format *version* lower than 3, recursive lists, sets and dictionaries cannot be written (see below). There are functions that read/write files as well as functions operating on bytes-like objects. The module defines these functions: marshal.dump(value, file, version=version, /, *, allow_code=True) Write the value on the open file. The value must be a supported type. The file must be a writeable *binary file*. If the value has (or contains an object that has) an unsupported type, a "ValueError" exception is raised --- but garbage data will also be written to the file. The object will not be properly read back by "load()". Code objects are only supported if *allow_code* is true. The *version* argument indicates the data format that "dump" should use (see below). Raises an auditing event "marshal.dumps" with arguments "value", "version". Changed in version 3.13: Added the *allow_code* parameter. marshal.load(file, /, *, allow_code=True) Read one value from the open file and return it. If no valid value is read (e.g. because the data has a different Python version's incompatible marshal format), raise "EOFError", "ValueError" or "TypeError". Code objects are only supported if *allow_code* is true. The file must be a readable *binary file*. Raises an auditing event "marshal.load" with no arguments. Note: If an object containing an unsupported type was marshalled with "dump()", "load()" will substitute "None" for the unmarshallable type. Changed in version 3.10: This call used to raise a "code.__new__" audit event for each code object. Now it raises a single "marshal.load" event for the entire load operation. Changed in version 3.13: Added the *allow_code* parameter. marshal.dumps(value, version=version, /, *, allow_code=True) Return the bytes object that would be written to a file by "dump(value, file)". The value must be a supported type. Raise a "ValueError" exception if value has (or contains an object that has) an unsupported type. Code objects are only supported if *allow_code* is true. The *version* argument indicates the data format that "dumps" should use (see below). Raises an auditing event "marshal.dumps" with arguments "value", "version". Changed in version 3.13: Added the *allow_code* parameter. marshal.loads(bytes, /, *, allow_code=True) Convert the *bytes-like object* to a value. If no valid value is found, raise "EOFError", "ValueError" or "TypeError". Code objects are only supported if *allow_code* is true. Extra bytes in the input are ignored. Raises an auditing event "marshal.loads" with argument "bytes". Changed in version 3.10: This call used to raise a "code.__new__" audit event for each code object. Now it raises a single "marshal.loads" event for the entire load operation. Changed in version 3.13: Added the *allow_code* parameter. In addition, the following constants are defined: marshal.version Indicates the format that the module uses. Version 0 is the historical format, version 1 shares interned strings and version 2 uses a binary format for floating-point numbers. Version 3 adds support for object instancing and recursion. The current version is 4. -[ Footnotes ]- [1] The name of this module stems from a bit of terminology used by the designers of Modula-3 (amongst others), who use the term "marshalling" for shipping of data around in a self-contained form. Strictly speaking, "to marshal" means to convert some data from internal to external form (in an RPC buffer for instance) and "unmarshalling" for the reverse process.