Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > mm.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > mm.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > mm.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > mm.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > mm.txt Multimedia Services ******************* The modules described in this chapter implement various algorithms or interfaces that are mainly useful for multimedia applications. They are available at the discretion of the installation. Here's an overview: * "wave" --- Read and write WAV files * Wave_read Objects * Wave_write Objects * "colorsys" --- Conversions between color systems