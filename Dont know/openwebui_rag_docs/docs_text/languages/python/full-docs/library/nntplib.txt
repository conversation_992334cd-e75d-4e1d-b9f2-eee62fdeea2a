Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > nntplib.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > nntplib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > nntplib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > nntplib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > nntplib.txt "nntplib" --- NNTP protocol client ********************************** Deprecated since version 3.11, removed in version 3.13. This module is no longer part of the Python standard library. It was removed in Python 3.13 after being deprecated in Python 3.11. The removal was decided in **PEP 594**. The last version of Python that provided the "nntplib" module was Python 3.12.