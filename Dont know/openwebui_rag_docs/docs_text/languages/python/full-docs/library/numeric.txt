Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > numeric.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > numeric.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > numeric.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > numeric.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > numeric.txt Numeric and Mathematical Modules ******************************** The modules described in this chapter provide numeric and math-related functions and data types. The "numbers" module defines an abstract hierarchy of numeric types. The "math" and "cmath" modules contain various mathematical functions for floating-point and complex numbers. The "decimal" module supports exact representations of decimal numbers, using arbitrary precision arithmetic. The following modules are documented in this chapter: * "numbers" --- Numeric abstract base classes * The numeric tower * Notes for type implementers * Adding More Numeric ABCs * Implementing the arithmetic operations * "math" --- Mathematical functions * Number-theoretic functions * Floating point arithmetic * Floating point manipulation functions * Power, exponential and logarithmic functions * Summation and product functions * Angular conversion * Trigonometric functions * Hyperbolic functions * Special functions * Constants * "cmath" --- Mathematical functions for complex numbers * Conversions to and from polar coordinates * Power and logarithmic functions * Trigonometric functions * Hyperbolic functions * Classification functions * Constants * "decimal" --- Decimal fixed-point and floating-point arithmetic * Quick-start Tutorial * Decimal objects * Logical operands * Context objects * Constants * Rounding modes * Signals * Floating-Point Notes * Mitigating round-off error with increased precision * Special values * Working with threads * Recipes * Decimal FAQ * "fractions" --- Rational numbers * "random" --- Generate pseudo-random numbers * Bookkeeping functions * Functions for bytes * Functions for integers * Functions for sequences * Discrete distributions * Real-valued distributions * Alternative Generator * Notes on Reproducibility * Examples * Recipes * Command-line usage * Command-line example * "statistics" --- Mathematical statistics functions * Averages and measures of central location * Measures of spread * Statistics for relations between two inputs * Function details * Exceptions * "NormalDist" objects * Examples and Recipes * Classic probability problems * Monte Carlo inputs for simulations * Approximating binomial distributions * Naive bayesian classifier