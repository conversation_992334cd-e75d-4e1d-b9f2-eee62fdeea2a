Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > persistence.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > persistence.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > persistence.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > persistence.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > persistence.txt Data Persistence **************** The modules described in this chapter support storing Python data in a persistent form on disk. The "pickle" and "marshal" modules can turn many Python data types into a stream of bytes and then recreate the objects from the bytes. The various DBM-related modules support a family of hash-based file formats that store a mapping of strings to other strings. The list of modules described in this chapter is: * "pickle" --- Python object serialization * Relationship to other Python modules * Comparison with "marshal" * Comparison with "json" * Data stream format * Module Interface * What can be pickled and unpickled? * Pickling Class Instances * Persistence of External Objects * Dispatch Tables * Handling Stateful Objects * Custom Reduction for Types, Functions, and Other Objects * Out-of-band Buffers * Provider API * Consumer API * Example * Restricting Globals * Performance * Examples * "copyreg" --- Register "pickle" support functions * Example * "shelve" --- Python object persistence * Restrictions * Example * "marshal" --- Internal Python object serialization * "dbm" --- Interfaces to Unix "databases" * "dbm.sqlite3" --- SQLite backend for dbm * "dbm.gnu" --- GNU database manager * "dbm.ndbm" --- New Database Manager * "dbm.dumb" --- Portable DBM implementation * "sqlite3" --- DB-API 2.0 interface for SQLite databases * Tutorial * Reference * Module functions * Module constants * Connection objects * Cursor objects * Row objects * Blob objects * PrepareProtocol objects * Exceptions * SQLite and Python types * Default adapters and converters (deprecated) * Command-line interface * How-to guides * How to use placeholders to bind values in SQL queries * How to adapt custom Python types to SQLite values * How to write adaptable objects * How to register adapter callables * How to convert SQLite values to custom Python types * Adapter and converter recipes * How to use connection shortcut methods * How to use the connection context manager * How to work with SQLite URIs * How to create and use row factories * How to handle non-UTF-8 text encodings * Explanation * Transaction control * Transaction control via the "autocommit" attribute * Transaction control via the "isolation_level" attribute