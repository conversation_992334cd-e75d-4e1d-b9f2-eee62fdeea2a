Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > removed.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > removed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > removed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > removed.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > removed.txt Removed Modules *************** The modules described in this chapter have been removed from the Python standard library. They are documented here to help people find replacements. * "aifc" --- Read and write AIFF and AIFC files * "asynchat" --- Asynchronous socket command/response handler * "asyncore" --- Asynchronous socket handler * "audioop" --- Manipulate raw audio data * "cgi" --- Common Gateway Interface support * "cgitb" --- Traceback manager for CGI scripts * "chunk" --- Read IFF chunked data * "crypt" --- Function to check Unix passwords * "distutils" --- Building and installing Python modules * "imghdr" --- Determine the type of an image * "imp" --- Access the import internals * "mailcap" --- Mailcap file handling * "msilib" --- Read and write Microsoft Installer files * "nis" --- Interface to Sun s NIS (Yellow Pages) * "nntplib" --- NNTP protocol client * "ossaudiodev" --- Access to OSS-compatible audio devices * "pipes" --- Interface to shell pipelines * "smtpd" --- SMTP Server * "sndhdr" --- Determine type of sound file * "spwd" --- The shadow password database * "sunau" --- Read and write Sun AU files * "telnetlib" --- Telnet client * "uu" --- Encode and decode uuencode files * "xdrlib" --- Encode and decode XDR data