Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > unix.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > unix.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > unix.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > unix.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > unix.txt Unix Specific Services ********************** The modules described in this chapter provide interfaces to features that are unique to the Unix operating system, or in some cases to some or many variants of it. Here's an overview: * "posix" --- The most common POSIX system calls * Large File Support * Notable Module Contents * "pwd" --- The password database * "grp" --- The group database * "termios" --- POSIX style tty control * Example * "tty" --- Terminal control functions * "pty" --- Pseudo-terminal utilities * Example * "fcntl" --- The "fcntl" and "ioctl" system calls * "resource" --- Resource usage information * Resource Limits * Resource Usage * "syslog" --- Unix syslog library routines * Examples * Simple example