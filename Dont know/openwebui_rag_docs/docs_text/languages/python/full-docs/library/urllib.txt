Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > urllib.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > urllib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > urllib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > urllib.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > urllib.txt "urllib" --- URL handling modules ********************************* **Source code:** Lib/urllib/ ====================================================================== "urllib" is a package that collects several modules for working with URLs: * "urllib.request" for opening and reading URLs * "urllib.error" containing the exceptions raised by "urllib.request" * "urllib.parse" for parsing URLs * "urllib.robotparser" for parsing "robots.txt" files