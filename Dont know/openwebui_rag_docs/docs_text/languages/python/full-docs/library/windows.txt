Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > windows.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > windows.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > windows.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > windows.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > library > windows.txt MS Windows Specific Services **************************** This chapter describes modules that are only available on MS Windows platforms. * "msvcrt" --- Useful routines from the MS VC++ runtime * File Operations * Console I/O * Other Functions * "winreg" --- Windows registry access * Functions * Constants * HKEY_* Constants * Access Rights * 64-bit Specific * Value Types * Registry Handle Objects * "winsound" --- Sound-playing interface for Windows