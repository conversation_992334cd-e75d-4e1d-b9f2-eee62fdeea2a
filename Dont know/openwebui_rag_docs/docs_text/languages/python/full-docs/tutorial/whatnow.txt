Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > tutorial > whatnow.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > tutorial > whatnow.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > tutorial > whatnow.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > tutorial > whatnow.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > tutorial > whatnow.txt 13. What Now? ************* Reading this tutorial has probably reinforced your interest in using Python --- you should be eager to apply Python to solving your real- world problems. Where should you go to learn more? This tutorial is part of Python's documentation set. Some other documents in the set are: * The Python Standard Library: You should browse through this manual, which gives complete (though terse) reference material about types, functions, and the modules in the standard library. The standard Python distribution includes a *lot* of additional code. There are modules to read Unix mailboxes, retrieve documents via HTTP, generate random numbers, parse command- line options, compress data, and many other tasks. Skimming through the Library Reference will give you an idea of what's available. * Installing Python Modules explains how to install additional modules written by other Python users. * The Python Language Reference: A detailed explanation of Python's syntax and semantics. It's heavy reading, but is useful as a complete guide to the language itself. More Python resources: * https://www.python.org: The major Python web site. It contains code, documentation, and pointers to Python-related pages around the web. * https://docs.python.org: Fast access to Python's documentation. * https://pypi.org: The Python Package Index, previously also nicknamed the Cheese Shop [1], is an index of user-created Python modules that are available for download. Once you begin releasing code, you can register it here so that others can find it. * https://code.activestate.com/recipes/langs/python/: The Python Cookbook is a sizable collection of code examples, larger modules, and useful scripts. Particularly notable contributions are collected in a book also titled Python Cookbook (O'Reilly & Associates, ISBN 0-596-00797-3.) * https://pyvideo.org collects links to Python-related videos from conferences and user-group meetings. * https://scipy.org: The Scientific Python project includes modules for fast array computations and manipulations plus a host of packages for such things as linear algebra, Fourier transforms, non- linear solvers, random number distributions, statistical analysis and the like. For Python-related questions and problem reports, you can post to the newsgroup *comp.lang.python*, or send them to the mailing <NAME_EMAIL>. The newsgroup and mailing list are gatewayed, so messages posted to one will automatically be forwarded to the other. There are hundreds of postings a day, asking (and answering) questions, suggesting new features, and announcing new modules. Mailing list archives are available at https://mail.python.org/pipermail/. Before posting, be sure to check the list of Frequently Asked Questions (also called the FAQ). The FAQ answers many of the questions that come up again and again, and may already contain the solution for your problem. -[ Footnotes ]- [1] "Cheese Shop" is a Monty Python's sketch: a customer enters a cheese shop, but whatever cheese he asks for, the clerk says it's missing.