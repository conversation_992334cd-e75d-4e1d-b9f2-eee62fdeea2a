Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > android.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > android.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > android.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > android.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > android.txt 6. Using Python on Android ************************** Python on Android is unlike Python on desktop platforms. On a desktop platform, Python is generally installed as a system resource that can be used by any user of that computer. Users then interact with Python by running a **python** executable and entering commands at an interactive prompt, or by running a Python script. On Android, there is no concept of installing as a system resource. The only unit of software distribution is an "app". There is also no console where you could run a **python** executable, or interact with a Python REPL. As a result, the only way you can use Python on Android is in embedded mode that is, by writing a native Android application, embedding a Python interpreter using "libpython", and invoking Python code using the Python embedding API. The full Python interpreter, the standard library, and all your Python code is then packaged into your app for its own private use. The Python standard library has some notable omissions and restrictions on Android. See the API availability guide for details. 6.1. Adding Python to an Android app ==================================== Most app developers should use one of the following tools, which will provide a much easier experience: * Briefcase, from the BeeWare project * Buildozer, from the Kivy project * Chaquopy * pyqtdeploy * Termux If you're sure you want to do all of this manually, read on. You can use the testbed app as a guide; each step below contains a link to the relevant file. * Build Python by following the instructions in Android/README.md. This will create the directory "cross-build/HOST/prefix". * Add code to your build.gradle file to copy the following items into your project. All except your own Python code can be copied from "prefix/lib": * In your JNI libraries: * "libpython*.*.so" * "lib*_python.so" (external libraries such as OpenSSL) * In your assets: * "python*.*" (the Python standard library) * "python*.*/site-packages" (your own Python code) * Add code to your app to extract the assets to the filesystem. * Add code to your app to start Python in embedded mode. This will need to be C code called via JNI.