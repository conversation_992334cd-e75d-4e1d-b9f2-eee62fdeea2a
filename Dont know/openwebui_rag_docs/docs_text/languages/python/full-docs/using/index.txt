Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > index.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > using > index.txt Python Setup and Usage ********************** This part of the documentation is devoted to general information on the setup of the Python environment on different platforms, the invocation of the interpreter and things that make working with Python easier. * 1. Command line and environment * 1.1. Command line * 1.2. Environment variables * 2. Using Python on Unix platforms * 2.1. Getting and installing the latest version of Python * 2.2. Building Python * 2.3. Python-related paths and files * 2.4. Miscellaneous * 2.5. Custom OpenSSL * 3. Configure Python * 3.1. Build Requirements * 3.2. Generated files * 3.3. Configure Options * 3.4. Python Build System * 3.5. Compiler and linker flags * 4. Using Python on Windows * 4.1. The full installer * 4.2. The Microsoft Store package * 4.3. The nuget.org packages * 4.4. The embeddable package * 4.5. Alternative bundles * 4.6. Configuring Python * 4.7. UTF-8 mode * 4.8. Python Launcher for Windows * 4.9. Finding modules * 4.10. Additional modules * 4.11. Compiling Python on Windows * 4.12. Other Platforms * 5. Using Python on macOS * 5.1. Using Python for macOS from "python.org" * 5.2. Alternative Distributions * 5.3. Installing Additional Python Packages * 5.4. GUI Programming * 5.5. Advanced Topics * 5.6. Other Resources * 6. Using Python on Android * 6.1. Adding Python to an Android app * 7. Using Python on iOS * 7.1. Python at runtime on iOS * 7.2. Installing Python on iOS * 7.3. App Store Compliance * 8. Editors and IDEs * 8.1. IDLE --- Python editor and shell * 8.2. Other Editors and IDEs