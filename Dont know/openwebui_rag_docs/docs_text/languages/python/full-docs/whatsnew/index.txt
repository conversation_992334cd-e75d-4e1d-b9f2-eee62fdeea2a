Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > whatsnew > index.txt

Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > whatsnew > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > whatsnew > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > whatsnew > index.txt Documentation: Python | Version: 3.13 | Path: languages > python > full-docs > whatsnew > index.txt What's New in Python ******************** The "What's New in Python" series of essays takes tours through the most important changes between major Python versions. They are a "must read" for anyone wishing to stay up-to-date after a new release. * What's New In Python 3.13 * Summary -- Release Highlights * New Features * Other Language Changes * New Modules * Improved Modules * Optimizations * Removed Modules And APIs * New Deprecations * CPython Bytecode Changes * C API Changes * Build Changes * Porting to Python 3.13 * Regression Test Changes * Notable changes in 3.13.1 * What's New In Python 3.12 * Summary -- Release highlights * New Features * New Features Related to Type Hints * Other Language Changes * New Modules * Improved Modules * Optimizations * CPython bytecode changes * Demos and Tools * Deprecated * Removed * Porting to Python 3.12 * Build Changes * C API Changes * What's New In Python 3.11 * Summary -- Release highlights * New Features * New Features Related to Type Hints * Other Language Changes * Other CPython Implementation Changes * New Modules * Improved Modules * Optimizations * Faster CPython * CPython bytecode changes * Deprecated * Pending Removal in Python 3.12 * Removed * Porting to Python 3.11 * Build Changes * C API Changes * Notable changes in 3.11.4 * Notable changes in 3.11.5 * What's New In Python 3.10 * Summary -- Release highlights * New Features * New Features Related to Type Hints * Other Language Changes * New Modules * Improved Modules * Optimizations * Deprecated * Removed * Porting to Python 3.10 * CPython bytecode changes * Build Changes * C API Changes * Notable security feature in 3.10.7 * Notable security feature in 3.10.8 * Notable changes in 3.10.12 * What's New In Python 3.9 * Summary -- Release highlights * You should check for DeprecationWarning in your code * New Features * Other Language Changes * New Modules * Improved Modules * Optimizations * Deprecated * Removed * Porting to Python 3.9 * Build Changes * C API Changes * Notable changes in Python 3.9.1 * Notable changes in Python 3.9.2 * Notable changes in Python 3.9.3 * Notable changes in Python 3.9.5 * Notable security feature in 3.9.14 * Notable changes in 3.9.17 * What's New In Python 3.8 * Summary -- Release highlights * New Features * Other Language Changes * New Modules * Improved Modules * Optimizations * Build and C API Changes * Deprecated * API and Feature Removals * Porting to Python 3.8 * Notable changes in Python 3.8.1 * Notable changes in Python 3.8.2 * Notable changes in Python 3.8.3 * Notable changes in Python 3.8.8 * Notable changes in Python 3.8.9 * Notable changes in Python 3.8.10 * Notable changes in Python 3.8.10 * Notable changes in Python 3.8.12 * Notable security feature in 3.8.14 * Notable changes in 3.8.17 * What's New In Python 3.7 * Summary -- Release Highlights * New Features * Other Language Changes * New Modules * Improved Modules * C API Changes * Build Changes * Optimizations * Other CPython Implementation Changes * Deprecated Python Behavior * Deprecated Python modules, functions and methods * Deprecated functions and types of the C API * Platform Support Removals * API and Feature Removals * Module Removals * Windows-only Changes * Porting to Python 3.7 * Notable changes in Python 3.7.1 * Notable changes in Python 3.7.2 * Notable changes in Python 3.7.6 * Notable changes in Python 3.7.10 * Notable changes in Python 3.7.11 * Notable security feature in 3.7.14 * What's New In Python 3.6 * Summary -- Release highlights * New Features * Other Language Changes * New Modules * Improved Modules * Optimizations * Build and C API Changes * Other Improvements * Deprecated * Removed * Porting to Python 3.6 * Notable changes in Python 3.6.2 * Notable changes in Python 3.6.4 * Notable changes in Python 3.6.5 * Notable changes in Python 3.6.7 * Notable changes in Python 3.6.10 * Notable changes in Python 3.6.13 * Notable changes in Python 3.6.14 * What's New In Python 3.5 * Summary -- Release highlights * New Features * Other Language Changes * New Modules * Improved Modules * Other module-level changes * Optimizations * Build and C API Changes * Deprecated * Removed * Porting to Python 3.5 * Notable changes in Python 3.5.4 * What's New In Python 3.4 * Summary -- Release Highlights * New Features * New Modules * Improved Modules * CPython Implementation Changes * Deprecated * Removed * Porting to Python 3.4 * Changed in 3.4.3 * What's New In Python 3.3 * Summary -- Release highlights * PEP 405: Virtual Environments * PEP 420: Implicit Namespace Packages * PEP 3118: New memoryview implementation and buffer protocol documentation * PEP 393: Flexible String Representation * PEP 397: Python Launcher for Windows * PEP 3151: Reworking the OS and IO exception hierarchy * PEP 380: Syntax for Delegating to a Subgenerator * PEP 409: Suppressing exception context * PEP 414: Explicit Unicode literals * PEP 3155: Qualified name for classes and functions * PEP 412: Key-Sharing Dictionary * PEP 362: Function Signature Object * PEP 421: Adding sys.implementation * Using importlib as the Implementation of Import * Other Language Changes * A Finer-Grained Import Lock * Builtin functions and types * New Modules * Improved Modules * Optimizations * Build and C API Changes * Deprecated * Porting to Python 3.3 * What's New In Python 3.2 * PEP 384: Defining a Stable ABI * PEP 389: Argparse Command Line Parsing Module * PEP 391: Dictionary Based Configuration for Logging * PEP 3148: The "concurrent.futures" module * PEP 3147: PYC Repository Directories * PEP 3149: ABI Version Tagged .so Files * PEP 3333: Python Web Server Gateway Interface v1.0.1 * Other Language Changes * New, Improved, and Deprecated Modules * Multi-threading * Optimizations * Unicode * Codecs * Documentation * IDLE * Code Repository * Build and C API Changes * Porting to Python 3.2 * What's New In Python 3.1 * PEP 372: Ordered Dictionaries * PEP 378: Format Specifier for Thousands Separator * Other Language Changes * New, Improved, and Deprecated Modules * Optimizations * IDLE * Build and C API Changes * Porting to Python 3.1 * What's New In Python 3.0 * Common Stumbling Blocks * Overview Of Syntax Changes * Changes Already Present In Python 2.6 * Library Changes * **PEP 3101**: A New Approach To String Formatting * Changes To Exceptions * Miscellaneous Other Changes * Build and C API Changes * Performance * Porting To Python 3.0 * What's New in Python 2.7 * The Future for Python 2.x * Changes to the Handling of Deprecation Warnings * Python 3.1 Features * PEP 372: Adding an Ordered Dictionary to collections * PEP 378: Format Specifier for Thousands Separator * PEP 389: The argparse Module for Parsing Command Lines * PEP 391: Dictionary-Based Configuration For Logging * PEP 3106: Dictionary Views * PEP 3137: The memoryview Object * Other Language Changes * New and Improved Modules * Build and C API Changes * Other Changes and Fixes * Porting to Python 2.7 * New Features Added to Python 2.7 Maintenance Releases * Acknowledgements * What's New in Python 2.6 * Python 3.0 * Changes to the Development Process * PEP 343: The 'with' statement * PEP 366: Explicit Relative Imports From a Main Module * PEP 370: Per-user "site-packages" Directory * PEP 371: The "multiprocessing" Package * PEP 3101: Advanced String Formatting * PEP 3105: "print" As a Function * PEP 3110: Exception-Handling Changes * PEP 3112: Byte Literals * PEP 3116: New I/O Library * PEP 3118: Revised Buffer Protocol * PEP 3119: Abstract Base Classes * PEP 3127: Integer Literal Support and Syntax * PEP 3129: Class Decorators * PEP 3141: A Type Hierarchy for Numbers * Other Language Changes * New and Improved Modules * Deprecations and Removals * Build and C API Changes * Porting to Python 2.6 * Acknowledgements * What's New in Python 2.5 * PEP 308: Conditional Expressions * PEP 309: Partial Function Application * PEP 314: Metadata for Python Software Packages v1.1 * PEP 328: Absolute and Relative Imports * PEP 338: Executing Modules as Scripts * PEP 341: Unified try/except/finally * PEP 342: New Generator Features * PEP 343: The 'with' statement * PEP 352: Exceptions as New-Style Classes * PEP 353: Using ssize_t as the index type * PEP 357: The '__index__' method * Other Language Changes * New, Improved, and Removed Modules * Build and C API Changes * Porting to Python 2.5 * Acknowledgements * What's New in Python 2.4 * PEP 218: Built-In Set Objects * PEP 237: Unifying Long Integers and Integers * PEP 289: Generator Expressions * PEP 292: Simpler String Substitutions * PEP 318: Decorators for Functions and Methods * PEP 322: Reverse Iteration * PEP 324: New subprocess Module * PEP 327: Decimal Data Type * PEP 328: Multi-line Imports * PEP 331: Locale-Independent Float/String Conversions * Other Language Changes * New, Improved, and Deprecated Modules * Build and C API Changes * Porting to Python 2.4 * Acknowledgements * What's New in Python 2.3 * PEP 218: A Standard Set Datatype * PEP 255: Simple Generators * PEP 263: Source Code Encodings * PEP 273: Importing Modules from ZIP Archives * PEP 277: Unicode file name support for Windows NT * PEP 278: Universal Newline Support * PEP 279: enumerate() * PEP 282: The logging Package * PEP 285: A Boolean Type * PEP 293: Codec Error Handling Callbacks * PEP 301: Package Index and Metadata for Distutils * PEP 302: New Import Hooks * PEP 305: Comma-separated Files * PEP 307: Pickle Enhancements * Extended Slices * Other Language Changes * New, Improved, and Deprecated Modules * Pymalloc: A Specialized Object Allocator * Build and C API Changes * Other Changes and Fixes * Porting to Python 2.3 * Acknowledgements * What's New in Python 2.2 * Introduction * PEPs 252 and 253: Type and Class Changes * PEP 234: Iterators * PEP 255: Simple Generators * PEP 237: Unifying Long Integers and Integers * PEP 238: Changing the Division Operator * Unicode Changes * PEP 227: Nested Scopes * New and Improved Modules * Interpreter Changes and Fixes * Other Changes and Fixes * Acknowledgements * What's New in Python 2.1 * Introduction * PEP 227: Nested Scopes * PEP 236: __future__ Directives * PEP 207: Rich Comparisons * PEP 230: Warning Framework * PEP 229: New Build System * PEP 205: Weak References * PEP 232: Function Attributes * PEP 235: Importing Modules on Case-Insensitive Platforms * PEP 217: Interactive Display Hook * PEP 208: New Coercion Model * PEP 241: Metadata in Python Packages * New and Improved Modules * Other Changes and Fixes * Acknowledgements * What's New in Python 2.0 * Introduction * What About Python 1.6? * New Development Process * Unicode * List Comprehensions * Augmented Assignment * String Methods * Garbage Collection of Cycles * Other Core Changes * Porting to 2.0 * Extending/Embedding Changes * Distutils: Making Modules Easy to Install * XML Modules * Module changes * New modules * IDLE Improvements * Deleted and Deprecated Modules * Acknowledgements The "Changelog" is an HTML version of the file built from the contents of the Misc/NEWS.d directory tree, which contains *all* nontrivial changes to Python for the current version. * Changelog * Python next * Python 3.13.3 final * Python 3.13.2 final * Python 3.13.1 final * Python 3.13.0 final * Python 3.13.0 release candidate 3 * Python 3.13.0 release candidate 2 * Python 3.13.0 release candidate 1 * Python 3.13.0 beta 4 * Python 3.13.0 beta 3 * Python 3.13.0 beta 2 * Python 3.13.0 beta 1 * Python 3.13.0 alpha 6 * Python 3.13.0 alpha 5 * Python 3.13.0 alpha 4 * Python 3.13.0 alpha 3 * Python 3.13.0 alpha 2 * Python 3.13.0 alpha 1 * Python 3.12.0 beta 1 * Python 3.12.0 alpha 7 * Python 3.12.0 alpha 6 * Python 3.12.0 alpha 5 * Python 3.12.0 alpha 4 * Python 3.12.0 alpha 3 * Python 3.12.0 alpha 2 * Python 3.12.0 alpha 1 * Python 3.11.0 beta 1 * Python 3.11.0 alpha 7 * Python 3.11.0 alpha 6 * Python 3.11.0 alpha 5 * Python 3.11.0 alpha 4 * Python 3.11.0 alpha 3 * Python 3.11.0 alpha 2 * Python 3.11.0 alpha 1 * Python 3.10.0 beta 1 * Python 3.10.0 alpha 7 * Python 3.10.0 alpha 6 * Python 3.10.0 alpha 5 * Python 3.10.0 alpha 4 * Python 3.10.0 alpha 3 * Python 3.10.0 alpha 2 * Python 3.10.0 alpha 1 * Python 3.9.0 beta 1 * Python 3.9.0 alpha 6 * Python 3.9.0 alpha 5 * Python 3.9.0 alpha 4 * Python 3.9.0 alpha 3 * Python 3.9.0 alpha 2 * Python 3.9.0 alpha 1 * Python 3.8.0 beta 1 * Python 3.8.0 alpha 4 * Python 3.8.0 alpha 3 * Python 3.8.0 alpha 2 * Python 3.8.0 alpha 1 * Python 3.7.0 final * Python 3.7.0 release candidate 1 * Python 3.7.0 beta 5 * Python 3.7.0 beta 4 * Python 3.7.0 beta 3 * Python 3.7.0 beta 2 * Python 3.7.0 beta 1 * Python 3.7.0 alpha 4 * Python 3.7.0 alpha 3 * Python 3.7.0 alpha 2 * Python 3.7.0 alpha 1 * Python 3.6.6 final * Python 3.6.6 release candidate 1 * Python 3.6.5 final * Python 3.6.5 release candidate 1 * Python 3.6.4 final * Python 3.6.4 release candidate 1 * Python 3.6.3 final * Python 3.6.3 release candidate 1 * Python 3.6.2 final * Python 3.6.2 release candidate 2 * Python 3.6.2 release candidate 1 * Python 3.6.1 final * Python 3.6.1 release candidate 1 * Python 3.6.0 final * Python 3.6.0 release candidate 2 * Python 3.6.0 release candidate 1 * Python 3.6.0 beta 4 * Python 3.6.0 beta 3 * Python 3.6.0 beta 2 * Python 3.6.0 beta 1 * Python 3.6.0 alpha 4 * Python 3.6.0 alpha 3 * Python 3.6.0 alpha 2 * Python 3.6.0 alpha 1 * Python 3.5.5 final * Python 3.5.5 release candidate 1 * Python 3.5.4 final * Python 3.5.4 release candidate 1 * Python 3.5.3 final * Python 3.5.3 release candidate 1 * Python 3.5.2 final * Python 3.5.2 release candidate 1 * Python 3.5.1 final * Python 3.5.1 release candidate 1 * Python 3.5.0 final * Python 3.5.0 release candidate 4 * Python 3.5.0 release candidate 3 * Python 3.5.0 release candidate 2 * Python 3.5.0 release candidate 1 * Python 3.5.0 beta 4 * Python 3.5.0 beta 3 * Python 3.5.0 beta 2 * Python 3.5.0 beta 1 * Python 3.5.0 alpha 4 * Python 3.5.0 alpha 3 * Python 3.5.0 alpha 2 * Python 3.5.0 alpha 1