# Order is important: The last matching pattern takes precedence.
# For more detailed information, see:
# https://docs.github.com/en/free-pro-team@latest/github/creating-cloning-and-archiving-repositories/about-code-owners

# Default owners
* @mdn/core-yari-content

# Default content owners
/files/en-us/ @mdn/yari-content-mdn

# MDN Firefox release notes
/files/en-us/mozilla/ @mdn/core-yari-content

# Mozilla Add-ons
/files/en-us/mozilla/add-ons/ @mdn/yari-content-mozilla-add-ons

# Accessibility
/files/en-us/web/accessibility/ @mdn/yari-content-accessibility

# Web API
/files/en-us/web/api/ @mdn/yari-content-web-api

# CSS
/files/en-us/web/css/ @mdn/yari-content-css

# HTML
/files/en-us/web/html/ @mdn/yari-content-html

# HTTP
/files/en-us/web/http/ @mdn/yari-content-http

# JavaScript
/files/en-us/web/javascript/ @mdn/yari-content-javascript

# MathML
/files/en-us/web/mathml/ @mdn/content-mathml

# Templates and sidebars (rari)
/files/jsondata/L10n-Template.json @mdn/core-dev

# ============================= CONTROL FILES ============================= #
# The CODEOWNERS file must end with these matches: Any pull request changing
# one or more of these files should be escalated to the owners specified here.

# mdn/content GitHub configuration
/.github/ @mdn/core-dev
# Issue templates in .github
/.github/ISSUE_TEMPLATE/ff-project-issue.md @mdn/core-yari-content

# Root directory
/* @mdn/core-dev
# Markdown files in root directory
/*.md @mdn/core-yari-content

# These are @mdn-bot because the auto-merge GHA workflow uses the PAT of this account.
# If another reviewer is specified, update the PAT token or auto-merge will cease to be automatic.
/package.json @mdn-bot
/yarn.lock @mdn-bot
