name: "Content issue"
description: Report a problem with content on MDN.
labels: ["needs triage"]
body:
  - type: markdown
    attributes:
      value: |
        ### Before you start

        **Want to change a page yourself?** This content is open source!
        ↩ Look for the _View this page on GitHub_ link on any MDN page to edit the content on GitHub.com afterwards.

        **Is your issue about a browser compatibility table?**
        ↩ Use the _Report problems with this compatibility data on GitHub_ link next to a compatibility table.

        **Need help with a browser?**
        🙋 To get help with [Firefox](https://support.mozilla.org/en-US/kb/file-bug-report-or-feature-request-mozilla), [Chrome](https://support.google.com/chrome/answer/95315?hl=en-GB&ref_topic=7439544), [Safari](https://www.apple.com/feedback/safari.html), or another browser, check the browser's support site.

        ---
  - type: input
    id: mdn-url
    attributes:
      label: MDN URL
      description: The link to the page this issue is about or, if it's about multiple pages, an example or most important page.
    validations:
      required: true
  # -- BEGIN fields that correspond to `page-report.yml` —-
  # If you edit these fields, edit them in both files!
  - type: input
    id: section
    attributes:
      label: What specific section or headline is this issue about?
  - type: textarea
    id: problem
    attributes:
      label: What information was incorrect, unhelpful, or incomplete?
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What did you expect to see?
    validations:
      required: true
  - type: textarea
    id: references
    attributes:
      label: Do you have any supporting links, references, or citations?
      description: Link to information that helps us confirm your issue.
  - type: textarea
    id: more-info
    attributes:
      label: Do you have anything more you want to share?
      description: For example, steps to reproduce a bug, screenshots, screen recordings, or sample code
  # -- END fields that correspond to `page-report.yml` —-
