Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md

Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md Path: mdn-web-docs > .github > ISSUE_TEMPLATE > ff-project-issue.md --- name: Firefox Release Issue about: "Use this template to create an issue for a Firefox Release Project" labels: "" assignees: title: "" --- ## Acceptance criteria - [ ] The listed features are documented sufficiently on MDN - [ ] BCD is updated - [ ] Interactive example and data repos are updated if appropriate - [ ] The content has been reviewed as needed ### For folks helping with Firefox related documentation - [ ] Set bugs to `dev-doc-complete` - [ ] Add entry to Firefox release notes for enabled/preview features - [ ] Add/remove entry to Firefox experimental features page for preference/released features ## Features to document ## Related Gecko bugs ## Other - [ ] Check content open issues to see if any pertain to the subject matter. If there are any that can be closed because of the work, do so. If there are any that can be fixed relatively quickly because of the knowledge from completing this issue and you have time, feel free to go ahead and fix them.