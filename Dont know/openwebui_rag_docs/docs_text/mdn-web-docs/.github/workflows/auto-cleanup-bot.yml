name: Create content auto-fix PR

on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

# No GITHUB_TOKEN permissions, as we only use it to increase API limit.
# We use AUTOMERGE_TOKEN to create the PR.
permissions: {}

jobs:
  fix:
    if: github.repository == 'mdn/content'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: Install all yarn packages
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          LEFTHOOK: 0

      - name: Lint markdown files
        run: |
          yarn content fix-flaws
          yarn fix:md
          yarn fix:fm
          node scripts/sort_and_unique_file_lines.js .vscode/dictionaries

      - name: Create PR with only fixable issues
        if: success()
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          commit-message: "chore: auto-fix Markdownlint, Prettier, and front-matter issues"
          branch: markdownlint-auto-cleanup
          title: "fix: auto-cleanup by bot"
          author: mdn-bot <<EMAIL>>
          committer: mdn-bot <<EMAIL>>
          body: |
            This PR was generated by [this run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) of the [`auto-cleanup-bot`](https://github.com/${{ github.repository }}/blob/${{ github.ref_name }}/.github/workflows/auto-cleanup-bot.yml) workflow.

            All issues were auto-fixed.
          labels: |
            automated pr
          token: ${{ secrets.AUTOMERGE_TOKEN }}

      - name: Create PR with notice on unfixed issues
        if: failure()
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          commit-message: "chore: auto-fix Markdownlint, Prettier, and front-matter issues"
          branch: markdownlint-auto-cleanup
          title: "fix: auto-cleanup by bot"
          author: mdn-bot <<EMAIL>>
          committer: mdn-bot <<EMAIL>>
          body: |
            This PR was generated by the [`auto-cleanup-bot`](https://github.com/${{ github.repository }}/blob/${{ github.ref_name }}/.github/workflows/auto-cleanup-bot.yml) workflow.

            Auto-fix was run, but additional issues found.
            Please review the [run log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
          labels: |
            automated pr
          token: ${{ secrets.AUTOMERGE_TOKEN }}
