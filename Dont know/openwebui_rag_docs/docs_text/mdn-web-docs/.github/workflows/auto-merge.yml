name: auto-merge

on:
  pull_request_target:

# No GITHUB_TOKEN permissions, as we use AUTOMERGE_TOKEN instead.
permissions: {}

jobs:
  auto-merge:
    runs-on: ubuntu-latest
    if: github.event.pull_request.user.login == 'dependabot[bot]'
    steps:
      - uses: ahmadnassri/action-dependabot-auto-merge@45fc124d949b19b6b8bf6645b6c9d55f4f9ac61a # v2.6.6
        with:
          github-token: ${{ secrets.AUTOMERGE_TOKEN }}
          command: "squash and merge"
          approve: true
          target: minor
