name: Update InterfaceData.json

on:
  schedule:
    # picking Fridays as webref IDL releases tend to be pushed on Thursdays
    - cron: "0 0 * * 6"
  workflow_dispatch:

# No GITHUB_TOKEN permissions, as we use AUTOMERGE_TOKEN instead.
permissions: {}

jobs:
  update:
    if: github.repository == 'mdn/content'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout content
        uses: actions/checkout@v4
        with:
          path: mdn-content
          ref: main

      - name: Setup node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "mdn-content/.nvmrc"

      - name: Checkout webref
        uses: actions/checkout@v4
        with:
          repository: w3c/webref
          path: webref
          ref: "@webref/idl@latest"

      - name: Extract data from webref
        working-directory: mdn-content
        run: node scripts/update-interface-data.js ../webref/

      - name: Create pull request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          path: mdn-content
          token: ${{ secrets.AUTOMERGE_TOKEN }}
          title: Update InterfaceData based on WebRef
          author: mdn-bot <<EMAIL>>
          committer: mdn-bot <<EMAIL>>
          commit-message: Update InterfaceData based on WebRef
          body: Automated changes generated by scripts/update-interface-data via interface-updater github workflow
          labels: |
            automated pr
          delete-branch: true
          branch: interfacedata-update
