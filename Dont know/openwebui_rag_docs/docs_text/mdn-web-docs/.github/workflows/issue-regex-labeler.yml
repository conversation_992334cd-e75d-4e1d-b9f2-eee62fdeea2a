name: "Label issues by MDN URL or other regex match in issue description"
on:
  issues:
    types: [opened]

permissions:
  # Label issues.
  issues: write

jobs:
  issue-labeler:
    runs-on: ubuntu-latest
    steps:
      - uses: github/issue-labeler@v3.4
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          configuration-path: .github/issue-regex-labeler.yml
          enable-versioned-regex: 0
