name: Mark<PERSON><PERSON> (All files)

on:
  pull_request:
    branches:
      - main
    paths:
      - .markdownlint-cli2.jsonc
      - .nvmrc
      - .prettier*
      - package.json
      - yarn.lock
      - .github/workflows/markdown-lint.yml
      - .github/workflows/markdownlint-problem-matcher.json

# No GITHUB_TOKEN permissions, as we only use it to increase API limit.
permissions: {}

jobs:
  docs:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: Install all yarn packages
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Lint markdown files
        run: |
          echo "::add-matcher::.github/workflows/markdownlint-problem-matcher.json"
          yarn lint:md
          yarn lint:fm
