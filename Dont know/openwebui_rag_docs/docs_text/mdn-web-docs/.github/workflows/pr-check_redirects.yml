name: Check Redirects

on:
  pull_request:
    branches:
      - main

# No GITHUB_TOKEN permissions, as we only use it to increase API limit.
permissions: {}

jobs:
  check-redirects:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      # This is a "required" workflow so path filtering can not be used:
      # https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/collaborating-on-repositories-with-code-quality-features/troubleshooting-required-status-checks#handling-skipped-but-required-checks
      # We have to rely on a custom filtering mechanism to run the checks only if required files are modified.
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        name: See if any file needs checking
        id: filter
        with:
          filters: |
            required_files:
              - ".nvmrc"
              - "files/**"
              - ".github/workflows/pr-check_redirects.yml"

      - name: Install all yarn packages
        if: steps.filter.outputs.required_files == 'true'
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check redirects file(s)
        if: steps.filter.outputs.required_files == 'true'
        run: yarn content validate-redirects en-US
