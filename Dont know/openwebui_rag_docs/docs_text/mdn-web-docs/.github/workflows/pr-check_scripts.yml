name: Check scripts

on:
  pull_request:
    branches:
      - main
    paths:
      - .nvmrc
      - package.json
      - yarn.lock
      - .github/workflows/pr-check_scripts.yml

# No GITHUB_TOKEN permissions, as we only use it to increase API limit.
permissions: {}

jobs:
  up-to-date-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: yarn install
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - run: yarn up-to-date-check

  start:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: yarn install
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: yarn start
        run: yarn start > /tmp/stdout.log 2> /tmp/stderr.log &

      - name: Wait for server
        run: |
          # Just a slight delay to wait until the dev server is ready.
          sleep 3

          curl --retry-connrefused --retry 5 http://localhost:5042 > /dev/null

          curl --fail http://localhost:5042/en-US/docs/MDN/Kitchensink > /dev/null
          curl --fail http://localhost:5042/en-US/docs/MDN/Kitchensink/index.json > /dev/null

      - name: Debug server's stdout and stderr if tests failed
        if: failure()
        run: |
          echo "STDOUT..................................................."
          cat /tmp/stdout.log
          echo ""
          echo "STDERR..................................................."
          cat /tmp/stderr.log

  filecheck:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: yarn install
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - run: yarn filecheck --help

  content:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: Install all yarn packages
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - run: yarn content --help

      - run: yarn content fix-redirects en-US

      - run: yarn content validate-redirects en-US

  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: yarn install
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - run: yarn build --help
