name: PR labeler

on:
  - pull_request_target

permissions:
  # Patch issues, see: https://github.com/CodelyTV/pr-size-labeler/pull/89
  issues: write
  # Label pull requests.
  pull-requests: write
  # Fetch files (used by actions/labeler to get config).
  contents: read

jobs:
  # Docs: https://github.com/actions/labeler
  label-by-path:
    # do not run on forks
    if: github.repository == 'mdn/content'
    name: Label by path
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          sync-labels: true

  # Docs: https://github.com/CodelyTV/pr-size-labeler
  label-by-size:
    # do not run on forks
    if: github.repository == 'mdn/content'
    needs: label-by-path
    name: Label by size
    runs-on: ubuntu-latest
    steps:
      - uses: codelytv/pr-size-labeler@1c3422395d899286d5ee2c809fd5aed264d5eb9b # v1.10.2
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          github_api_url: "https://api.github.com"
          xs_label: "size/xs"
          xs_max_size: "5"
          s_label: "size/s"
          s_max_size: "50"
          m_label: "size/m"
          m_max_size: "500"
          l_label: "size/l"
          l_max_size: "1000"
          xl_label: "size/xl"
          fail_if_xl: "false"
          message_if_xl: ""
          files_to_ignore: |
            "files/en-us/_redirects.txt"
            "files/en-us/_wikihistory.json"
