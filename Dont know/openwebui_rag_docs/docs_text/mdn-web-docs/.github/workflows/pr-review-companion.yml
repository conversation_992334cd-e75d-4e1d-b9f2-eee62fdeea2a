# Things to do and run after a "tests" job in "PR Test" workflow has completed successfully.
# Note, as of right now, this workflow does a bunch of things. It might be
# worth considering to break it up so there's a dedicated post-PR
# workflow just to posting PR comments about flaws, for example.

name: PR review companion

on:
  workflow_run:
    workflows: ["PR Test", "PR Test Legacy"]
    types:
      - completed

  workflow_call:
    secrets:
      GCP_PROJECT_NAME:
        required: true
      WIP_PROJECT_ID:
        required: true

permissions:
  # Download artifact.
  actions: read
  # Post comment in pull request.
  pull-requests: write
  # Authenticate with GCP.
  id-token: write

jobs:
  review:
    environment: review
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'
    steps:
      - name: "Download artifact"
        uses: actions/download-artifact@v4
        with:
          pattern: build
          path: build
          merge-multiple: true
          github-token: ${{ secrets.GITHUB_TOKEN }}
          run-id: ${{ github.event.workflow_run.id }}

      - name: Check for artifacts
        id: check
        if: hashFiles('build/') != ''
        run: |
          echo "HAS_ARTIFACT=true" >> "$GITHUB_OUTPUT"
          PR_NUMBER=`cat build/NR | tr -dc '0-9'`
          echo "PR_NUMBER=$PR_NUMBER" >> "$GITHUB_OUTPUT"
          echo "PREFIX=pr$PR_NUMBER" >> "$GITHUB_OUTPUT"

      - name: Authenticate with GCP
        if: steps.check.outputs.HAS_ARTIFACT
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          service_account: deploy-mdn-review-content@${{ secrets.GCP_PROJECT_NAME }}.iam.gserviceaccount.com
          workload_identity_provider: projects/${{ secrets.WIP_PROJECT_ID }}/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Setup gcloud
        if: steps.check.outputs.HAS_ARTIFACT
        uses: google-github-actions/setup-gcloud@v2

      - name: Upload to GCS
        if: steps.check.outputs.HAS_ARTIFACT
        uses: google-github-actions/upload-cloud-storage@v2
        with:
          path: "build"
          destination: "${{ vars.GCP_BUCKET_NAME }}/${{ steps.check.outputs.PREFIX }}"
          resumable: false
          concurrency: 500
          parent: false
          process_gcloudignore: false

      - name: Checkout (mdn/content)
        uses: actions/checkout@v4
        if: steps.check.outputs.HAS_ARTIFACT
        with:
          path: content

      - name: Setup (mdn/content)
        uses: actions/setup-node@v4
        if: steps.check.outputs.HAS_ARTIFACT
        with:
          node-version-file: "content/.nvmrc"

      - name: Install (mdn/content)
        if: steps.check.outputs.HAS_ARTIFACT
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        working-directory: content
        run: yarn --frozen-lockfile

      - name: Analyze PR build
        if: steps.check.outputs.HAS_ARTIFACT
        env:
          BUILD_OUT_ROOT: ${{ github.workspace }}/build
          PREFIX: ${{ steps.check.outputs.PREFIX }}
          PR_NUMBER: ${{ steps.check.outputs.PR_NUMBER }}
        working-directory: content
        run: |
          echo "Pull request:"
          echo "https://github.com/mdn/content/pull/$PR_NUMBER"

          node scripts/analyze-pr-build.js \
            --prefix="$PREFIX" \
            --analyze-flaws \
            --analyze-dangerous-content \
            --github-token="${{ secrets.GITHUB_TOKEN }}" \
            --repo=$GITHUB_REPOSITORY \
            --pr-number="$PR_NUMBER" \
            --diff-file=$BUILD_OUT_ROOT/DIFF \
            $BUILD_OUT_ROOT
