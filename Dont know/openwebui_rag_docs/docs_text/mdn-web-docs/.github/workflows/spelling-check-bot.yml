name: Create an issue to report typos

on:
  schedule:
    - cron: "0 0 * * mon"
  workflow_dispatch:

permissions:
  # Create issue.
  issues: write

jobs:
  sync:
    if: github.repository == 'mdn/content'
    runs-on: ubuntu-latest
    permissions:
      issues: write

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: yarn

      - name: Install all yarn packages
        run: yarn --frozen-lockfile
        env:
          # https://github.com/microsoft/vscode-ripgrep#github-api-limit-note
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Run scripts
        id: check
        run: |
          echo Running spelling check...
          output=$(yarn --silent lint:typos-group-by-file || exit 0)
          if [ -n "${output}" ]; then
            output=$(node scripts/linkify-logs.js "${output}")
            output=$(echo "$output" | sed 's/^/- /')
            echo "$output"
            echo "OUTPUT<<EOF" >> "$GITHUB_OUTPUT"
            echo "$output" >> "$GITHUB_OUTPUT"
            echo "EOF" >> "$GITHUB_OUTPUT"
          else
            echo "No typos found! 🎉"
          fi

      - name: Report spellcheck errors
        if: steps.check.outputs.OUTPUT != ''
        run: |
          issue=$(gh issue create --title "$TITLE" --label "$LABELS" --body "$BODY")
          echo Issue URL ${issue}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TITLE: Weekly spelling check
          LABELS: reported by automation
          BODY: |
            Typos and unknown words:

            ${{ steps.check.outputs.OUTPUT }}

            > [!TIP]
            > If the word is actually valid or it is required to be ignored, consider adding it to one of the dictionaries under [`.vscode/dictionaries`](https://github.com/mdn/content/tree/main/.vscode/dictionaries).
