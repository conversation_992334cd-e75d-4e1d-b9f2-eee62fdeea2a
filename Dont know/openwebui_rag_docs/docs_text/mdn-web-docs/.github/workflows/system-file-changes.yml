name: System file changes

on:
  pull_request_target:
    paths:
      - ".github/workflows/**"
      - ".github/CODEOWNERS"
      - ".github/dependabot.yml"
      - "scripts/**"
      - package.json
      - yarn.lock

# No GITHUB_TOKEN permissions, as we don't use it.
permissions: {}

jobs:
  block:
    # This makes sure it only runs on our origin repo
    # and makes an exception for Dependabot.
    if: github.repository_owner == 'mdn' && github.event.pull_request.user.login != 'dependabot[bot]'
    runs-on: ubuntu-latest
    steps:
      - name: Block if author/actor is not an admin
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          # Check author.
          AUTHOR="${{ github.event.pull_request.user.login }}"
          AUTHOR_PERMISSION=$(gh api https://api.github.com/repos/${{ github.repository }}/collaborators/$AUTHOR/permission --jq .permission)

          if [ "$AUTHOR_PERMISSION" != "admin" ]; then
            echo "PR author ($AUTHOR) is not an admin, please ping someone for a review."
            exit 1
          fi

          # Check actor.
          ACTOR="${{ github.actor }}"
          if [ "$ACTOR" != "$AUTHOR" ]; then
            ACTOR_PERMISSION=$(gh api https://api.github.com/repos/${{ github.repository }}/collaborators/$ACTOR/permission --jq .permission)  

            if [ "$ACTOR_PERMISSION" != "admin" ]; then
              echo "PR actor ($ACTOR) is not an admin, please ping someone for a review."
              exit 1
            fi
          fi
