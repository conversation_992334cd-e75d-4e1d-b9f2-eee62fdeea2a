Path: mdn-web-docs > files > en-us > games > index.md

Path: mdn-web-docs > files > en-us > games > index.md Path: mdn-web-docs > files > en-us > games > index.md Path: mdn-web-docs > files > en-us > games > index.md Path: mdn-web-docs > files > en-us > games > index.md --- title: Game development slug: Games page-type: landing-page --- {{GamesSidebar}} Gaming is one of the most popular computer activities. New technologies are constantly arriving to make it possible to develop better and more powerful games that can be run in any standards-compliant web browser. ## Develop web games Welcome to the MDN game development center! In this area of the site, we provide resources for web developers wanting to develop games. You will find many useful tutorials and technique articles in the main menu on the left, so feel free to explore. We've also included a reference section so you can easily find information about all the most common APIs used in game development. > [!NOTE] > Creating games on the web draws on a number of core web technologies such as HTML, CSS, and JavaScript. The [Learn web development](/en-US/docs/Learn_web_development) section is a good place to go to get started with the basics. ## Port native games to the Web If you are a native developer (for example writing games in C++), and you are interested in how you can port your games over to the Web, you should learn more about our [Emscripten](https://emscripten.org/index.html) tool this is an LLVM to JavaScript compiler, which takes LLVM bytecode (e.g., generated from C/C++ using Clang, or from another language) and compiles that into [asm.js](/en-US/docs/Games/Tools/asm.js), which can be run on the Web. To get started, see: - [About Emscripten](https://emscripten.org/docs/introducing_emscripten/about_emscripten.html) for an introduction including high-level details. - [Download and Install](https://emscripten.org/docs/getting_started/downloads.html) for installing the toolchain. - [Emscripten Tutorial](https://emscripten.org/docs/getting_started/Tutorial.html) for a tutorial to teach you how to get started. ## Examples For a list of web game examples, see our list of [Tutorials](/en-US/docs/Games/Tutorials). ## See also - [Build New Games](http://buildnewgames.com/) - : A collaborative site featuring a large number of open web game development tutorials. Has not been very active recently, but still holds some nice resources. - [Creative JS](http://creativejs.com/) - : A collection of impressive JavaScript techniques and experiments, not specific to games, but helpful nonetheless. Has not been very active recently, but still holds some nice resources. - [Game Programming Patterns](https://gameprogrammingpatterns.com/) - : An online book, written by Bob Nystrom, which discusses programming patterns in the context of game development with the aim of helping game developers produce more effective and efficient code. - [Gamedev.js Weekly](https://gamedevjsweekly.com/) - : Weekly newsletter about HTML game development, sent every Friday. Contains the latest articles, tutorials, tools, and resources. - [HTML5 Game Engine](https://html5gameengine.com/) - : List of the most popular HTML game frameworks along with their rating, features and samples. - [Tuts+ Game Development](https://gamedevelopment.tutsplus.com/) - : Tutorials and articles about game development in general. - [HTML5 Gamedev Starter](https://html5devstarter.enclavegames.com/) - : Starter for the new game developers, a curated list of links to various useful resources around the web. - [js13kGames](https://js13kgames.com/) - : JavaScript coding competition for HTML game developers with the file size limit set to 13 kilobytes. All the submitted games are available in a readable form on GitHub. - [Mozilla Hacks Blog](https://hacks.mozilla.org/category/games/) - : Games category on the Mozilla Hacks blog containing interesting gamedev related articles. - [Games section on wiki.mozilla.org](https://wiki.mozilla.org/Platform/Games) - : A wiki page with information about Mozilla's involvement in platform games.