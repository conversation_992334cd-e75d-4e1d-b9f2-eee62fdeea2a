Path: mdn-web-docs > files > en-us > games > publishing_games > game_monetization > index.md

Path: mdn-web-docs > files > en-us > games > publishing_games > game_monetization > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_monetization > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_monetization > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_monetization > index.md --- title: Game monetization slug: Games/Publishing_games/Game_monetization page-type: guide --- {{GamesSidebar}} When you've spent your time building a game, [distributing](/en-US/docs/Games/Publishing_games/Game_distribution) it and [promoting](/en-US/docs/Games/Publishing_games/Game_promotion) it you should consider earning some money out of it. If your work is a serious endeavor on the path to becoming an independent game developer able to make a living, read on and see what your options are. The technology is mature enough; now it's just about choosing the right approach. ## Paid games The first, most obvious choice that may come to your mind might be selling the games the way it is done for huge AAA titles with a fixed, up front price. Even though the digital market is key and you don't need to print covers and put your game in a physical store in boxes, to earn decent money on selling your games for a fixed price you have to invest your time and money in marketing. Only the best games will break even or earn more than they cost to make, and you still need a lot of luck for that. How much you charge for your game depends on the market, quality of your game and a lot of other small factors. An arcade iOS title can be sold for 0.99 USD, but a longer RPG-style desktop game on Steam can cost 20 USD; both prices are OK. You have to follow the market and do your own research learning from your mistakes quickly is important. ## In-app purchases Instead of having people pay for your game up front, you can offer a free game with in-app purchases (IAPs.) In this case the game can be acquired without spending a dime give the game to the players, but offer in-game currency, bonuses or benefits for real money. Specific examples can include bonus levels, better weapons or spells, or refilling the energy needed to play. Designing a good IAP system is an art of its own. Remember that you need thousands of downloads of your game to make IAPs effective only a small fraction of players will actually pay for IAPs. How small? It varies, but around one person in every thousand is about average. The more people that play your game, the higher the chance someone will pay, so your earnings heavily depend on your [promotion](/en-US/docs/Games/Publishing_games/Game_promotion) activities. ### Freemium Games that feature IAPs are often referred to a **freemium** a freemium game can be acquired and played for free, but you can pay for extra (premium) features, virtual goods or other benefits. The word itself acquired negative connotations after big companies focused on creating games, the main purpose of which was to get as much money from the players as possible instead of delivering a fun experience. The worst cases were when you could use real money to pay for getting advantages over other players, or when they restricted access to the next stages of the game unless the players paid. The term "pay to win" was coined and this approach is disliked by many players and devs. If you want to implement IAPs try to add value to the game with something players will enjoy instead of taking it out and then charging for it. ### Add-ons and DLCs Add-ons and downloadable content are a good way to provide extra value to an already released game, but remember that you'll have to offer decent, entertaining content to attract people to buy it. A totally new set of levels with new characters, weapons and story is a good material for DLC, but to have enough sales the game itself has to be popular, or else there won't be any players interested in spending their hard-earned money on it. ## Advertisements Instead of actively selling the games you can also try to get yourself a passive income showing adverts and relying on previous activities related to [promoting](/en-US/docs/Games/Publishing_games/Game_promotion) your game may benefit, but your game has to be addictive, which isn't as easy as it sounds. You still need to plan it out, and at some point you'll need a bit of luck too. If your game goes viral and people start sharing it, you can get a lot of downloads and money out of adverts. There are many companies offering advert systems you sign up and allow them to show adverts in exchange for a percentage of the earnings. Google AdSense is said to be the most effective one, but it's not designed for games and it's a pretty bad practice to use it for that purpose. Instead of risking of having your account closed and all the money blocked try to use the usual, gamedev targeted portals like [LeadBolt](https://www.leadboltapps.com/). They offer easy to implement systems to show the adverts in your games and split the earnings with you. Video ads are getting more and more popular, especially in the form of a pre-roll they are shown at the beginning of your game while it's still loading. And on the topic of where to put the advertisements in your game it really depends on you. It should be as subtle as possible to not annoy the players too much, but visible enough to make them click it or at least take notice. Adding adverts between game sessions on game over screens is a popular approach. ## Licensing There's an approach that can work as a monetization model on its own, and that's selling licenses for distribution of your game. There are more and more portals interested in showing your games on their websites. They follow various strategies to earn money via your games, but you don't have to worry about all that as selling the license is usually a one-time deal. You get the money and they can get creative with using your game to make money. Finding publishers might be hard at first try to look for them at the [HTML5 Gamedevs forums](https://www.html5gamedevs.com/). If you're well known they may reach out to you. Most of the deals are done through emails when talking to a dedicated person on the publisher side. Some publisher websites have that information easily available, while some others are harder to find. When reaching a publisher try to be nice and straight to the point they are busy people. ### Exclusive licenses The exclusive license is a type of license for one publisher you've built a game and you're selling all the rights to it to a single entity along with the rights to redistribute it [Softgames](https://www.softgames.com/) are an example of such a publisher. You can't sell it again in any form while that publisher has the rights that's why exclusive deals are worth quite a lot of money. How much exactly? It depends on the quality of the game, its genre, its publisher, and many others, but usually it will be something between 2000 and 5000 USD. Once you've sold an exclusive license you can forget about promoting that particular game as you won't earn more, so go into such a deal only if you're sure it's profitable enough. ### Non-exclusive licenses This approach is less strict you can sell a license to multiple publishers. This is the most popular approach as with every new publisher (and they are constantly showing up) you can sell your games on non-exclusive terms. Remember that with this license the publisher can't redistribute it further it's often called a site-locked deal as they buy the right to publish the game on their own given portal. The usual cost of a non-exclusive license is around 500 USD. ### Subscriptions There's also an option to get a passive, monthly revenue via a subscription deal. Instead of getting a one-time payment you can get a small amount of money per game, per month it can be something around 20-50 USD per month, per game. It's normally up to you if you want to get all the money in a lump sum or get it per month. Remember that it can be cancelled, so it's not an indefinitely working solution. ### Ad revenue You can implement advertisements in your game on your own and try to find the traffic to earn a bit, but you can also do a revenue share deal with a publisher. They will take care of driving the traffic and will split the earnings usually in a 70/30 or 50/50 deal, collected per month. Remember that many new, low quality publishers will want to get your game for ad revenue instead of licensing because it will be cheaper for them and you might end up with earnings around 2 USD per game for the whole deal. Be careful when dealing with new publishers sometimes it's better to lower the license cost for a known one rather than risking fraud with an unknown publisher for more money. Publishers taking your games for revenue share, and/or licensing may require implementing their own APIs, which could take extra work, so consider that in your rates too. ### Branding You can sell rights to use your game for branding, or do it yourself. In the first case it's almost like non-exclusive licensing, but the client will usually buy rights for the code and implement their own graphics. In the second case it's like a freelance deal, but you're reusing the code and adding graphics provided by the client, sometimes implementing them as they instruct you. As an example if you've got a game where a player taps items of food, you could change the food to the client's products to give them advertising. Prices in this model vary greatly depending on the brand, client, and amount of work you put in. ## Other non-game focused monetization strategies There are other ways you can earn money when building HTML games, and it doesn't even have to be game-related. ### Selling resources If you're a graphic designer, you can sell the assets from the games you've created, or something brand new exclusively for that purpose at online shops like [Envato Market](https://themeforest.net/). It's not much, but if you're a known designer it can be an extra passive stream of income. ### Writing articles and tutorials It is possible to write articles about your games and even get paid for them. Game [promotion](/en-US/docs/Games/Publishing_games/Game_promotion) and monetization at the same time is a win-win, and if you don't abuse it with too much advertising the readers will enjoy reading them and as well as learning a thing or two. If you focus on sharing the knowledge first and use your games just as the examples it should be OK. Check out [Tuts+ Game Development](https://gamedevelopment.tutsplus.com/) or similar websites for writing opportunities. ### Merchandise You can sell t-shirts, [stickers](https://www.stickermule.com/custom-stickers) or other gadgets some devs make more money from the merchandise than from the games themselves, but it only works on very popular and recognizable games like Angry Birds. Still, it could be another small stream of passive income. The more diversified your earnings are, the better your business stability. ### Donations When all else fails you can try putting a donate button on your game's page and asking for support from the community. Sometimes it works, but only if the player knows you and feels that it will help you in your situation. That's why carefully managing your community is so important. It worked with the [js13kGames](https://js13kgames.com/) competition every participant got a free t-shirt, and some even gave back a few bucks to help keep it going in years to come. ## Summary There are many ways to earn money everything that applies to the "normal" AAA gaming world can be, more or less, applied to casual HTML games. You might however also focus on selling licenses, doing branding, or earning on a revenue share basis from the advertisements. It's totally up to you which path you're going to follow.