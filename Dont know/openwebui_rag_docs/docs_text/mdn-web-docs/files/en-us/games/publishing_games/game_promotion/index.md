Path: mdn-web-docs > files > en-us > games > publishing_games > game_promotion > index.md

Path: mdn-web-docs > files > en-us > games > publishing_games > game_promotion > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_promotion > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_promotion > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > game_promotion > index.md --- title: Game promotion slug: Games/Publishing_games/Game_promotion page-type: guide --- {{GamesSidebar}} Developing and publishing your game is not enough. You have to let the world know that you have something interesting available that people will enjoy playing. There are many ways to promote your game most of them being free, so even if you're struggling to make a living as an indie dev with zero budget you can still do a lot to let people know about your great new game. Promoting the game helps a lot when [monetizing](/en-US/docs/Games/Publishing_games/Game_monetization) it later on too, so it's important to do it correctly. ## Competitions Taking part in competitions will not only level up your gamedev skills and let you meet new devs to befriend and learn from and it will also get you involved in the community. If you make a good game for a competition and win some prizes in the process your game will automatically be promoted by the organizers and other attendees. You'll be rich and famous, or so they say. Many great games get started as a quick, sloppy demo submitted to a competition. If both the idea and the execution are good enough, you will succeed. Plus competitions generally require games to follow a mandatory theme, so you can get creative around a theme if you are stuck for ideas. ## Website and blog You should definitely create your own website containing all the information about your games, so people can see what you've worked on. The more information you can include the better you should include screenshots, description, trailer, press kit, requirements, available platforms, support details and more. You'll get bonus points for allowing your users to directly play your games online at least in demo form. Also, you should do some work on SEO to allow people to find your games more easily. You should also blog about everything related to your gamedev activities. Write about your development process, nasty bugs you encounter, funny stories, lessons learned, and the ups and downs of being a game developer. Continually publishing information about your games will help educate others, increase your reputation in the community, and further improve SEO. A further option is to publish [monthly reports](https://end3r.com/blog?s=monthly+report) that summarize all your progress it helps you see what you've accomplished throughout the month and what's still left to do, and it keeps reminding people that your game is coming out soon building buzz is always good. While you can create your website from scratch, there are also tools that can help make the process easier. [ManaKeep](https://manakeep.com/) is a website builder made for indie game developers and provides a great starting point to create your website. [Presskit()](https://dopresskit.com/) is a press kit builder that helps you create a press page to share with the media. ## Social media Your social media presence is very important. The `#gamedev` hashtag can help find friends and allow you to engage with the community and help other devs in need. Honesty is key and you should be authentic; nobody likes boring press releases or pushy advertisements. When the time comes, your community will help you spread the word about your shiny new game! Keep an eye on gamers who stream on YouTube and Twitch, and be active on forums such as [HTML5GameDevs.com](https://www.html5gamedevs.com/). Share your news and answer questions so that people will value what you're doing and will know that you're trustworthy. Remember to not be too pushy when it comes to telling everyone about your games you're not a walking advertisement. Grow your audience by talking to them, sharing tips, offering discounts, giving away prizes in competitions, or just complaining at the weather or buggy browser you have to deal with. Be generous, be yourself and be there for others, and you'll be treated with respect. ## Game portals Using game portals is mostly concerned with [monetization](/en-US/docs/Games/Publishing_games/Game_monetization), but if you're not planning to [sell licenses](/en-US/docs/Games/Publishing_games/Game_monetization#licensing) to allow people to purchase your game and intend to [implement adverts](/en-US/docs/Games/Publishing_games/Game_monetization#advertisements) or in-app purchases instead, promoting your game across free portals can be effective. There are a number of different game portals to which you can send your games for publication. Some portals have their own APIs that allow you to authorize users, save their progress and process in-app purchases. You can also sell a full version of the game from inside your browser demo version, which will be a great move considering high competition, some developers even manage to make full browser versions. Most portals offer revenue share deals or will buy nonexclusive licenses. Free portals offer traffic, but only the best ones are popular enough to generate revenues from advertisements on in-app purchases. On the other hand, they are a perfect tool to make games visible to a broader audience if you have no budget and limited time. ## Press You can try and reach the [press](https://indiegamesplus.com/) about your game, but bear in mind that they get a tonne of requests like this every single day, so be humble and patient if they don't answer right away, and be polite when talking to them. Be sure to check first if they are dealing with specific genres of games or platforms, so you don't send them something that is not relevant to them in the first place. If you're honest with your approach and your game is good, then you've got more of a chance of success. If you want to learn more about the etiquette of contacting the press you should check out [How To Contact Press](https://app.box.com/s/p0ft5zdolpi0ydkrykab) - a great guide from Pixel Prospector. ## Tutorials It's good to share your knowledge with other devs after all, you probably learned a thing or two from online articles, so you take the time to pay that knowledge forward. Talking or writing about something you achieved or problems you overcame is something people would be interested in. Also, you can use your own game as an example, especially in a tutorial when you're [showing how to do something you've implemented already](/en-US/docs/Games/Techniques/Controls_Gamepad_API). That way everyone benefits people learn new skills, your game gets promoted, and if you're lucky you can even get paid for writing a tutorial if it's good enough. There are portals like [Tuts+ Game Development](https://gamedevelopment.tutsplus.com/) which will be more than happy if you write for them - they pay for the content, but not all topic ideas will be accepted. When writing a tutorial remember to focus on delivering something valuable to the reader. They want to learn something - offer your expertise and use your game as a case study. Focus on one aspect and try to explain it throughout and in detail. Also, remember to follow up on discussions in your comments if people have any questions. If portals you contact are not interested in your content because you don't have any experience yet, try writing tutorials and then publish them on [your own blog](#website_and_blog) first. It's the easiest way to train your writing skills on your own. ## Events If you've gone through all the options listed above you can still find new, creative ways to promote your game events are another good example. Attending events, both local and global, gives you the ability to meet your fans face to face, as well as other members of the development community. Value the fact that they spent their time seeing you. ### Conferences There are many conferences where you can give a talk explaining some technical difficulties you overcame, or how you implemented specific APIs; again use your games as examples for that. It's important to focus on the knowledge part and tone down the marketing devs are sensitive on this matter and you may end up with an angry crowd if you just try to sell them something. ### Fairs The other event-related option is fairs (or expos) at such an event you can get a booth among other devs and promote your game to all the attendees passing by. If you do so, try to be unique and original, so you easily stand out from the crowd. Do it the right way and everybody will be talking about you and your game. Having a booth gives you the possibility to interact with your fans directly besides the promotion part, you can also test new builds of your game on regular people and fix any bugs (or incorporate any feedback) they uncover. You can't imagine what people may come up with when playing your game, and what obvious issues you've missed while spending hours polishing it. ## Promo codes If you're selling the game, then create the ability to distribute promo codes allowing people to access your game for free (or at least a demo or time-limited version), and send them all over the place to press, YouTubers, as competition prizes, etc. If the game reaches certain people you'll get a free advert to thousands of players. It can boost interest in your game more than anything else if you get lucky. ## Fostering the community You can help community grow and promote yourself and your games at the same time. Sending out [weekly newsletters](https://gamedevjsweekly.com/) and organizing [online competitions](https://js13kgames.com/) or [local meetups](https://gamedevjs.com/) will show others that you're passionate about what you do and that they can rely on you. Then when you need any help they will be there for you. ## Summary Any way of promoting your game is good. You have a lot of options to choose from with most of them being free, so it's only about your enthusiasm and available time. Sometimes you have to spend more time promoting a game than actually developing it. Remember that it's no use to have the best game in the world if no one knows it exists. Now, let's get on with that [monetization](/en-US/docs/Games/Publishing_games/Game_monetization) part, and earn something for a living.