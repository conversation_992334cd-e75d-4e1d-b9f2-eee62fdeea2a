Path: mdn-web-docs > files > en-us > games > publishing_games > index.md

Path: mdn-web-docs > files > en-us > games > publishing_games > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > index.md Path: mdn-web-docs > files > en-us > games > publishing_games > index.md --- title: Publishing games slug: Games/Publishing_games page-type: guide --- {{GamesSidebar}} HTML games have a huge advantage over native in terms of publishing and distribution you have the freedom of distribution, promotion and monetization of your game on the Web, rather than each version being locked into a single store controlled by one company. You can benefit from the web being truly multiplatform. This series of articles looks at the options you have when you want to publish and distribute your game, and earn something out of it while you wait for it to become famous. ## Game distribution So you've followed a [tutorial](/en-US/docs/Games/Tutorials/2D_Breakout_game_pure_JavaScript) or [two](/en-US/docs/Games/Tutorials/2D_breakout_game_Phaser) and created an HTML game that's great! [Game distribution](/en-US/docs/Games/Publishing_games/Game_distribution) provides all you need to know about the ways you can distribute your newly created game into the wild including hosting it yourself online, submitting it to open marketplaces, and submitting it to closed ones like Google Play or the iOS App Store. ## Game promotion Developing and finishing the game is not enough. You have to let the world know that you have made something interesting available, which people will enjoy playing. There are many [Game promotion](/en-US/docs/Games/Publishing_games/Game_promotion) techniques many of them free so even if you're struggling to make a living as an indie dev with zero budget you can still do a lot to let people know about your great new game. Promoting the game helps a lot in monetizing it later on too, so it's important to do it effectively. ## Game monetization When you spend your time building, publishing and promoting your game, you will at some point consider earning money out of it. [Game monetization](/en-US/docs/Games/Publishing_games/Game_monetization) is essential to anyone who considers their game development work a serious endeavor on the path to becoming an independent game developer able to make a living, so read on and see what your options are. The technology is mature enough; it's just a matter of choosing the right approach.