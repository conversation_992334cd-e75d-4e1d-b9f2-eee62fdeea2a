Path: mdn-web-docs > files > en-us > games > tutorials > 2d_breakout_game_pure_javascript > mouse_controls > index.md

Path: mdn-web-docs > files > en-us > games > tutorials > 2d_breakout_game_pure_javascript > mouse_controls > index.md Path: mdn-web-docs > files > en-us > games > tutorials > 2d_breakout_game_pure_javascript > mouse_controls > index.md Path: mdn-web-docs > files > en-us > games > tutorials > 2d_breakout_game_pure_javascript > mouse_controls > index.md Path: mdn-web-docs > files > en-us > games > tutorials > 2d_breakout_game_pure_javascript > mouse_controls > index.md --- title: Mouse controls slug: Games/Tutorials/2D_Breakout_game_pure_JavaScript/Mouse_controls page-type: guide --- {{GamesSidebar}} {{PreviousNext("Games/Workflows/2D_Breakout_game_pure_JavaScript/Track_the_score_and_win", "Games/Workflows/2D_Breakout_game_pure_JavaScript/Finishing_up")}} This is the **9th step** out of 10 of the [Gamedev Canvas tutorial](/en-US/docs/Games/Tutorials/2D_Breakout_game_pure_JavaScript). You can find the source code as it should look after completing this lesson at [Gamedev-Canvas-workshop/lesson9.html](https://github.com/end3r/Gamedev-Canvas-workshop/blob/gh-pages/lesson09.html). The game itself is actually finished, so let's work on polishing it up. We have already added keyboard controls, but we could easily add mouse controls too. ## Listening for mouse movement Listening for mouse movement is even easier than listening for key presses: all we need is the listener for the {{domxref("Element/mousemove_event", "mousemove")}} event. Add the following line in the same place as the other event listeners, just below the `keyup event`: ```js document.addEventListener("mousemove", mouseMoveHandler, false); ``` ## Anchoring the paddle movement to the mouse movement We can update the paddle position based on the pointer coordinates the following handler function will do exactly that. Add the following function to your code, below the previous line you added: ```js function mouseMoveHandler(e) { const relativeX = e.clientX - canvas.offsetLeft; if (relativeX > 0 && relativeX < canvas.width) { paddleX = relativeX - paddleWidth / 2; } } ``` In this function we first work out a `relativeX` value, which is equal to the horizontal mouse position in the viewport (`e.clientX`) minus the distance between the left edge of the canvas and left edge of the viewport (`canvas.offsetLeft`) effectively this is equal to the distance between the canvas left edge and the mouse pointer. If the relative X pointer position is greater than zero and lower than the Canvas width, the pointer is within the Canvas boundaries, and the `paddleX` position (anchored on the left edge of the paddle) is set to the `relativeX` value minus half the width of the paddle, so that the movement will actually be relative to the middle of the paddle. The paddle will now follow the position of the mouse cursor, but since we're restricting the movement to the size of the Canvas, it won't disappear completely off either side. ## Compare your code This is the latest state of the code to compare against: ```html hidden <canvas id="myCanvas" width="480" height="320"></canvas> <button id="runButton">Start game</button> ``` ```css hidden canvas { background: #eee; } button { display: block; } ``` ```js hidden const canvas = document.getElementById("myCanvas"); const ctx = canvas.getContext("2d"); const ballRadius = 10; let x = canvas.width / 2; let y = canvas.height - 30; let dx = 2; let dy = -2; const paddleHeight = 10; const paddleWidth = 75; let paddleX = (canvas.width - paddleWidth) / 2; let rightPressed = false; let leftPressed = false; let interval = 0; const brickRowCount = 5; const brickColumnCount = 3; const brickWidth = 75; const brickHeight = 20; const brickPadding = 10; const brickOffsetTop = 30; const brickOffsetLeft = 30; let score = 0; let bricks = []; for (let c = 0; c < brickColumnCount; c++) { bricks[c] = []; for (let r = 0; r < brickRowCount; r++) { bricks[c][r] = { x: 0, y: 0, status: 1 }; } } document.addEventListener("keydown", keyDownHandler, false); document.addEventListener("keyup", keyUpHandler, false); document.addEventListener("mousemove", mouseMoveHandler, false); function keyDownHandler(e) { if (e.key == "Right" || e.key == "ArrowRight") { rightPressed = true; } else if (e.key == "Left" || e.key == "ArrowLeft") { leftPressed = true; } } function keyUpHandler(e) { if (e.key == "Right" || e.key == "ArrowRight") { rightPressed = false; } else if (e.key == "Left" || e.key == "ArrowLeft") { leftPressed = false; } } function mouseMoveHandler(e) { const relativeX = e.clientX - canvas.offsetLeft; if (relativeX > 0 && relativeX < canvas.width) { paddleX = relativeX - paddleWidth / 2; } } function collisionDetection() { for (let c = 0; c < brickColumnCount; c++) { for (let r = 0; r < brickRowCount; r++) { let b = bricks[c][r]; if (b.status == 1) { if ( x > b.x && x < b.x + brickWidth && y > b.y && y < b.y + brickHeight ) { dy = -dy; b.status = 0; score++; if (score == brickRowCount * brickColumnCount) { alert("YOU WIN, CONGRATS!"); document.location.reload(); clearInterval(interval); // Needed for Chrome to end game } } } } } } function drawBall() { ctx.beginPath(); ctx.arc(x, y, ballRadius, 0, Math.PI * 2); ctx.fillStyle = "#0095DD"; ctx.fill(); ctx.closePath(); } function drawPaddle() { ctx.beginPath(); ctx.rect(paddleX, canvas.height - paddleHeight, paddleWidth, paddleHeight); ctx.fillStyle = "#0095DD"; ctx.fill(); ctx.closePath(); } function drawBricks() { for (let c = 0; c < brickColumnCount; c++) { for (let r = 0; r < brickRowCount; r++) { if (bricks[c][r].status == 1) { const brickX = r * (brickWidth + brickPadding) + brickOffsetLeft; const brickY = c * (brickHeight + brickPadding) + brickOffsetTop; bricks[c][r].x = brickX; bricks[c][r].y = brickY; ctx.beginPath(); ctx.rect(brickX, brickY, brickWidth, brickHeight); ctx.fillStyle = "#0095DD"; ctx.fill(); ctx.closePath(); } } } } function drawScore() { ctx.font = "16px Arial"; ctx.fillStyle = "#0095DD"; ctx.fillText("Score: " + score, 8, 20); } function draw() { ctx.clearRect(0, 0, canvas.width, canvas.height); drawBricks(); drawBall(); drawPaddle(); drawScore(); collisionDetection(); if (x + dx > canvas.width - ballRadius || x + dx < ballRadius) { dx = -dx; } if (y + dy < ballRadius) { dy = -dy; } else if (y + dy > canvas.height - ballRadius) { if (x > paddleX && x < paddleX + paddleWidth) { dy = -dy; } else { alert("GAME OVER"); document.location.reload(); clearInterval(interval); // Needed for Chrome to end game } } if (rightPressed && paddleX < canvas.width - paddleWidth) { paddleX += 7; } else if (leftPressed && paddleX > 0) { paddleX -= 7; } x += dx; y += dy; } function startGame() { interval = setInterval(draw, 10); } document.getElementById("runButton").addEventListener("click", function () { startGame(); }); ``` {{embedlivesample("compare_your_code", 600, 360)}} > [!NOTE] > Try adjusting the boundaries of the paddle movement, so the whole paddle will be visible on both edges of the Canvas instead of only half of it. ## Next steps Now we've got a complete game we'll finish our series of lessons with some more small tweaks [Finishing up](/en-US/docs/Games/Tutorials/2D_Breakout_game_pure_JavaScript/Finishing_up). {{PreviousNext("Games/Workflows/2D_Breakout_game_pure_JavaScript/Track_the_score_and_win", "Games/Workflows/2D_Breakout_game_pure_JavaScript/Finishing_up")}}