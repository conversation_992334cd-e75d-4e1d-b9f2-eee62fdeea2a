Path: mdn-web-docs > files > en-us > games > tutorials > index.md

Path: mdn-web-docs > files > en-us > games > tutorials > index.md Path: mdn-web-docs > files > en-us > games > tutorials > index.md Path: mdn-web-docs > files > en-us > games > tutorials > index.md Path: mdn-web-docs > files > en-us > games > tutorials > index.md --- title: Tutorials slug: Games/Tutorials page-type: landing-page --- {{GamesSidebar}} This page contains multiple tutorial series that highlight different workflows for effectively creating different types of web games. - [2D breakout game using pure JavaScript](/en-US/docs/Games/Tutorials/2D_Breakout_game_pure_JavaScript) - : In this step-by-step tutorial you'll implement a simple breakout clone using pure JavaScript. Along the way you will learn the basics of using the {{htmlelement("canvas")}} element to implement fundamental game mechanics like rendering and moving images, collision detection, control mechanisms, and winning and losing states. - [2D breakout game using Phaser](/en-US/docs/Games/Tutorials/2D_breakout_game_Phaser) - : In this step-by-step tutorial you'll implement the same breakout clone as the previous tutorial series, except that this time you'll do it using the [Phaser](https://phaser.io/) HTML game framework. This idea here is to teach some of the fundamentals (and advantages) of working with frameworks, along with fundamental game mechanics. - [2D maze game with device orientation](/en-US/docs/Games/Tutorials/HTML5_Gamedev_Phaser_Device_Orientation) - : This tutorial shows how to create a 2D maze game using HTML, incorporating fundamentals such as collision detection and sprite placement on a {{htmlelement("canvas")}}. This is a mobile game that uses the [Device Orientation](/en-US/docs/Web/API/Device_orientation_events) and [Vibration](/en-US/docs/Web/API/Vibration_API) **APIs** to enhance the gameplay and is built using the [Phaser](https://phaser.io/) framework. - [2D platform game with Phaser](https://mozdevs.github.io/html5-games-workshop/en/guides/platformer/start-here/) - : This tutorial series shows how to create a simple platform game using [Phaser](https://phaser.io/), covering fundamentals such as sprites, collisions, physics, collectables, and more.