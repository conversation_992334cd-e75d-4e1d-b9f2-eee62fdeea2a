Path: mdn-web-docs > files > en-us > glossary > accessibility > index.md

Path: mdn-web-docs > files > en-us > glossary > accessibility > index.md Path: mdn-web-docs > files > en-us > glossary > accessibility > index.md Path: mdn-web-docs > files > en-us > glossary > accessibility > index.md Path: mdn-web-docs > files > en-us > glossary > accessibility > index.md --- title: Accessibility slug: Glossary/Accessibility page-type: glossary-definition --- {{GlossarySidebar}} **Accessibility** (**A11y**) means enabling as many people as possible to use websites, including those with limited abilities. The most common way to achieve this is by ensuring that assistive technologies work properly to access the content. These technologies include {{glossary("screen reader", "screen readers")}}, screen magnifiers, speech recognition tools, and alternative input devices. You can measure the accessibility of your website by using tools like [Lighthouse](https://developer.chrome.com/docs/lighthouse/accessibility/scoring) and the [Firefox Accessibility Inspector](https://firefox-source-docs.mozilla.org/devtools-user/accessibility_inspector/index.html). Web accessibility is formally defined and discussed at the {{Glossary("W3C")}} through the {{Glossary("WAI", "Web Accessibility Initiative (WAI)")}}. ## See also - [Accessibility](/en-US/docs/Web/Accessibility) - [Learn accessibility](/en-US/docs/Learn_web_development/Core/Accessibility) - [ARIA](/en-US/docs/Web/Accessibility/ARIA) - [Web accessibility](https://en.wikipedia.org/wiki/Web_accessibility) on Wikipedia - [Web Accessibility Initiative (WAI)](https://www.w3.org/WAI/) on W3C - [Accessible Rich Internet Applications (WAI-ARIA)](https://w3c.github.io/aria/) specification - [Web Accessibility In Mind](https://webaim.org/) - Related glossary terms: - {{Glossary("ARIA")}} - {{Glossary("Screen reader")}} - {{Glossary("Accessibility tree")}} - {{Glossary("Accessible description")}} - {{Glossary("Accessible name")}}