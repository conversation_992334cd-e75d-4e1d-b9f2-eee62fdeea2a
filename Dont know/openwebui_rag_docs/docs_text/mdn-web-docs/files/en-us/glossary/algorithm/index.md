Path: mdn-web-docs > files > en-us > glossary > algorithm > index.md

Path: mdn-web-docs > files > en-us > glossary > algorithm > index.md Path: mdn-web-docs > files > en-us > glossary > algorithm > index.md Path: mdn-web-docs > files > en-us > glossary > algorithm > index.md Path: mdn-web-docs > files > en-us > glossary > algorithm > index.md --- title: Algorithm slug: Glossary/Algorithm page-type: glossary-definition --- {{GlossarySidebar}} An **algorithm** is a self-contained series of instructions to perform a function. In other words, an algorithm is a means of describing a way to solve a problem so that it can be solved repeatedly, by humans or machines. Computer scientists compare the efficiency of algorithms through the concept of "Algorithmic Complexity" or "Big O" notation. For example: - A cooking recipe is an algorithm for humans. - A sorting algorithm is often used in computer programming to explain to a machine how to sort data. Common algorithms are Pathfinding algorithms such as the optimization [Traveling Salesman Problem](https://optimization.cbe.cornell.edu/index.php?title=Traveling_salesman_problem), [Tree Traversal algorithms](https://brilliant.org/wiki/traversals/), and so on. There are also [Machine Learning algorithms](https://www.coursera.org/articles/machine-learning-algorithms) such as [Linear Regression](https://en.wikipedia.org/wiki/Linear_regression), Logistic Regression, Decision Tree, Random Forest, Support Vector Machine, Recurrent Neural Network (RNN), Long Short Term Memory (LSTM) Neural Network, Convolutional Neural Network (CNN), Deep Convolutional Neural Network, and so on. ## See also - [Algorithm](https://en.wikipedia.org/wiki/Algorithm) on Wikipedia - [Explanations of sorting algorithms](https://www.toptal.com/developers/sorting-algorithms) - [Explanations of algorithmic complexity](https://www.bigocheatsheet.com/)