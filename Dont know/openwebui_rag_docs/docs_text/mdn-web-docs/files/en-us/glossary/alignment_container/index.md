Path: mdn-web-docs > files > en-us > glossary > alignment_container > index.md

Path: mdn-web-docs > files > en-us > glossary > alignment_container > index.md Path: mdn-web-docs > files > en-us > glossary > alignment_container > index.md Path: mdn-web-docs > files > en-us > glossary > alignment_container > index.md Path: mdn-web-docs > files > en-us > glossary > alignment_container > index.md --- title: Alignment container slug: Glossary/Alignment_Container page-type: glossary-definition --- {{GlossarySidebar}} The **alignment container** is the rectangle that the {{glossary("alignment subject")}} is aligned within. This is defined by the layout mode; it is usually the alignment subject's containing block, and assumes the writing mode of the box establishing the containing block. ## See also - [CSS box alignment](/en-US/docs/Web/CSS/CSS_box_alignment) module - Properties reference - CSS {{CSSXref("align-content")}} property - CSS {{CSSXref("align-items")}} property - CSS {{CSSXref("align-self")}} property - CSS {{CSSXref("justify-content")}} property - CSS {{CSSXref("justify-items")}} property - CSS {{CSSXref("justify-self")}} property - CSS {{CSSXref("place-content")}} shorthand property - CSS {{CSSXref("place-self")}} shorthand property - CSS {{CSSXref("scroll-snap-align")}} property - Related glossary terms: - {{Glossary("alignment subject")}}