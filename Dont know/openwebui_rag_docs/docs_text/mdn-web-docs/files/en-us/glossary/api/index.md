Path: mdn-web-docs > files > en-us > glossary > api > index.md

Path: mdn-web-docs > files > en-us > glossary > api > index.md Path: mdn-web-docs > files > en-us > glossary > api > index.md Path: mdn-web-docs > files > en-us > glossary > api > index.md Path: mdn-web-docs > files > en-us > glossary > api > index.md --- title: API slug: Glossary/API page-type: glossary-definition --- {{GlossarySidebar}} An **API** (Application Programming Interface) is a set of features and rules that exist inside a software program (the application) enabling interaction with it through software - as opposed to a human user interface. The API can be seen as a simple contract (the interface) between the application offering it and other items, such as third-party software or hardware. In Web development, an API is generally a set of code features (e.g., {{glossary("method","methods")}}, {{Glossary("property","properties")}}, events, and {{Glossary("URL","URLs")}}) that a developer can use in their apps for interacting with components of a user's web browser, other software/hardware on the user's computer, or third-party websites and services. For example: - The [getUserMedia](/en-US/docs/Web/API/MediaDevices/getUserMedia) API can be used to grab audio and video from a user's webcam, which is then available to the developer, for example, recording video and audio, broadcasting it to another user in a conference call, or capturing image stills from the video. - The [Geolocation API](/en-US/docs/Web/API/Geolocation) can be used to retrieve location information from services the user has available on their device (e.g., GPS), which can then be used in conjunction with other services, such as the [Google Maps APIs](https://developers.google.com/maps/), to plot the user's location on a custom map and show them what tourist attractions are in their area. - The [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) can be used to animate parts of a web page for example, to programmatically move or rotate images. ## See also - [Web API reference](/en-US/docs/Web/API) - [API](https://en.wikipedia.org/wiki/API) on Wikipedia