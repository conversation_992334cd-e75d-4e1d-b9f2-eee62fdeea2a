Path: mdn-web-docs > files > en-us > glossary > apple_safari > index.md

Path: mdn-web-docs > files > en-us > glossary > apple_safari > index.md Path: mdn-web-docs > files > en-us > glossary > apple_safari > index.md Path: mdn-web-docs > files > en-us > glossary > apple_safari > index.md Path: mdn-web-docs > files > en-us > glossary > apple_safari > index.md --- title: Apple Safari slug: Glossary/Apple_Safari page-type: glossary-definition --- {{GlossarySidebar}} [**Safari**](https://www.apple.com/safari/) is a {{Glossary("Browser","web browser")}} developed by Apple and is built into Apple's operating systems, including macOS (for Mac computers), iPadOS (for iPad tablets), iOS (for iPhones), and visionOS (for augmented reality devices like the Apple Vision Pro). Safari uses the open source {{glossary("WebKit")}} rendering engine, which was derived from [KHTML](https://en.wikipedia.org/wiki/KHTML). ## See also - [Safari](<https://en.wikipedia.org/wiki/Safari_(web_browser)>) on Wikipedia - [Safari](https://www.apple.com/safari/) on apple.com - [The WebKit project](https://webkit.org/) - [WebKit Build Archives](https://webkit.org/build-archives/) - [Reporting a bug for Safari](https://bugs.webkit.org/) - Related glossary terms: - {{glossary("Browser")}} - {{glossary("WebKit")}}