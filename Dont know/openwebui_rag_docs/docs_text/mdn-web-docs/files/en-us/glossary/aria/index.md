Path: mdn-web-docs > files > en-us > glossary > aria > index.md

Path: mdn-web-docs > files > en-us > glossary > aria > index.md Path: mdn-web-docs > files > en-us > glossary > aria > index.md Path: mdn-web-docs > files > en-us > glossary > aria > index.md Path: mdn-web-docs > files > en-us > glossary > aria > index.md --- title: ARIA slug: Glossary/ARIA page-type: glossary-definition --- {{GlossarySidebar}} **ARIA** (_{{Glossary("Accessibility", "Accessible")}} Rich {{glossary("Internet")}} Applications_) is a {{Glossary("W3C")}} specification developed by {{Glossary("WAI")}} to make Web content and Web applications more accessible to people with disabilities. It especially helps with dynamic content and advanced user interface controls developed with HTML, JavaScript, and related technologies. The specification, for example, allows you to add the attribute `role="alert"` to a {{HTMLElement("p")}} {{glossary("tag")}} to notify a sight-challenged user that the information is important and time-sensitive (which you might otherwise convey through text color). ## See also - [ARIA](/en-US/docs/Web/Accessibility/ARIA) - [The W3C Web Accessibility Initiative (WAI)](https://www.w3.org/WAI/) - [Accessible Rich Internet Applications (WAI-ARIA)](https://w3c.github.io/aria/) - [ARIA Authoring Practices Guide (APG)](https://www.w3.org/WAI/ARIA/apg/) - [Accessibility](/en-US/docs/Web/Accessibility) - [Learn: Accessibility](/en-US/docs/Learn_web_development/Core/Accessibility) - Related glossary terms - {{Glossary("Accessibility")}} - {{Glossary("WAI")}}