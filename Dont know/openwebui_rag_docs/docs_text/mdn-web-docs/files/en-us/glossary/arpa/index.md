Path: mdn-web-docs > files > en-us > glossary > arpa > index.md

Path: mdn-web-docs > files > en-us > glossary > arpa > index.md Path: mdn-web-docs > files > en-us > glossary > arpa > index.md Path: mdn-web-docs > files > en-us > glossary > arpa > index.md Path: mdn-web-docs > files > en-us > glossary > arpa > index.md --- title: ARPA slug: Glossary/ARPA page-type: glossary-definition --- {{GlossarySidebar}} **.arpa** (address and routing parameter area) is a {{glossary("TLD","top-level domain")}} in the Domain Name System (DNS) used for Internet infrastructure purposes, especially reverse DNS lookup (i.e., find the {{glossary('domain name')}} for a given {{glossary("IP address")}}). The name came from **ARPA**, currently known as [DARPA](https://en.wikipedia.org/wiki/DARPA), the Defense Advanced Research Projects Agency. DARPA is credited with developing precursors to the Internet ({{glossary("ARPANET")}}), GPS, artificial intelligence, and virtual reality. The domain was originally intended only to serve a temporary function for facilitating the systematic naming of the ARPANET computers, but failed to remove it after infrastructural uses had been sanctioned. As a result, the ARPA domain name is a backronym for "Address and Routing Parameter Area". {{glossary("TLD")}} was designed at DARPA during a time when it was called ARPA, hence the `.arpa` domain name. The most prominent `.arpa` domains include `in-addr.arpa` and `ip6.arpa`, which provide namespaces for reverse DNS lookup of IPv4 and IPv6 addresses respectively. ## See also - Related glossary terms: - {{Glossary("ARPANET")}} - [Official website](https://www.iana.org/domains/arpa) - [.arpa](https://en.wikipedia.org/wiki/.arpa) on Wikipedia