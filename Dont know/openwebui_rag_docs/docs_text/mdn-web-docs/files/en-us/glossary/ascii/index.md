Path: mdn-web-docs > files > en-us > glossary > ascii > index.md

Path: mdn-web-docs > files > en-us > glossary > ascii > index.md Path: mdn-web-docs > files > en-us > glossary > ascii > index.md Path: mdn-web-docs > files > en-us > glossary > ascii > index.md Path: mdn-web-docs > files > en-us > glossary > ascii > index.md --- title: ASCII slug: Glossary/ASCII page-type: glossary-definition --- {{GlossarySidebar}} **ASCII** (_American Standard Code for Information Interchange_) is a {{glossary("character encoding")}} standard using 7-bit to represent 128 {{glossary("character", "characters")}} used by computers and other devices for encoding letters, numbers, punctuation, and control codes into digital form. The first 33 ASCII {{glossary("code point", "code points")}} are non-printing control codes including the carriage return, line feed, tab, and several obsolete non-printable codes stemming from its origin of representing telegraph codes. The other 95 are printable characters, including digits (0-9), lowercase (a-z) and uppercase (A-Z) letters, and punctuation symbols. In the modern age, most computer systems use {{glossary("Unicode")}} instead, which is an extension of ASCII, supporting millions of code points. {{Glossary("UTF-8")}} superseded ASCII on the Web in 2007. ## See also - [ASCII](https://en.wikipedia.org/wiki/ASCII) on Wikipedia - {{rfc("20")}} - Related glossary terms: - {{glossary("Unicode")}} - {{glossary("UTF-8")}}