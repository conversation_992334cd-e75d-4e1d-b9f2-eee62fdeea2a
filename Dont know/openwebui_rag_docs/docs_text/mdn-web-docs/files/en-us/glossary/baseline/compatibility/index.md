Path: mdn-web-docs > files > en-us > glossary > baseline > compatibility > index.md

Path: mdn-web-docs > files > en-us > glossary > baseline > compatibility > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > compatibility > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > compatibility > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > compatibility > index.md --- title: Baseline (compatibility) slug: Glossary/Baseline/Compatibility page-type: glossary-definition --- {{GlossarySidebar}} **Baseline** identifies the availability of web platform features across popular browsers, including APIs, CSS properties, and JavaScript syntax. Baseline describes web features as being either widely available or newly available. Features that do not meet the Baseline criteria are said to have limited availability. Baseline considers support in the following browsers: - Apple Safari (iOS) - Apple Safari (macOS) - Google Chrome (Android) - Google Chrome (desktop) - Microsoft Edge (desktop) - Mozilla Firefox (Android) - Mozilla Firefox (desktop) Baseline is a summary of browser support. It is not a substitute for accessibility, usability, performance, security, or other testing. Baseline may not tell you if a feature works with: - Older devices and browser releases - Browsers not covered by the Baseline definition, such as operating system web views - Assistive technology, such as screen readers. ## Baseline badges ![Green widget with the checkmark: Baseline, widely available. Four browsers logos, all with checkmarks.](high.png) Features listed as **widely available** have a consistent history of support in each of the Baseline browsers for at least 2.5 years. ![Blue widget with the checkmark: Baseline 2022, newly available. Four browsers' logos, all with checkmarks.](limited.png) Features listed as **newly available** work in at least the latest stable version of each of the Baseline browsers, but may not work with older browsers and devices. ![Grey widget with the cross: limited availability. Four browsers' logos, two with checkmarks, two with crosses.](low.png) Features listed with **limited availability** are _not_ yet available in all browsers. ## See also - [Testing](/en-US/docs/Learn_web_development/Extensions/Testing) - [web-platform-dx/web-features repository](https://github.com/web-platform-dx/web-features) - [W3C WebDX Community Group](https://www.w3.org/community/webdx/) - [mdn/browser-compat-data repository](https://github.com/mdn/browser-compat-data) - [caniuse.com](https://caniuse.com/) - [a11ysupport.io](https://a11ysupport.io/)