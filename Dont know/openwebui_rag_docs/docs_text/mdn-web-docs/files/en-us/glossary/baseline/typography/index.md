Path: mdn-web-docs > files > en-us > glossary > baseline > typography > index.md

Path: mdn-web-docs > files > en-us > glossary > baseline > typography > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > typography > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > typography > index.md Path: mdn-web-docs > files > en-us > glossary > baseline > typography > index.md --- title: Baseline (typography) slug: Glossary/Baseline/Typography page-type: glossary-definition --- {{GlossarySidebar}} A **baseline** is an imaginary line along the inline axis of a line box along which individual glyphs of text are aligned. Baselines guide the design of glyphs in a font and the alignment of glyphs from different fonts or font sizes when typesetting. The **alphabetic baseline** is the value of the CSS `baseline` keyword. The bottom of most alphabetic glyphs typically align with the alphabetic baseline; most characters of European and West Asian fonts rest _on top_ of the alphabetic baseline. Other writing systems have different baselines. For example, Tibetan and similar unicameral scripts with a strong but not absolute top edge are aligned to the bottom of a "hanging" baseline. East Asian scripts have no baseline; each glyph sits in a square box, with neither ascenders nor descenders. When mixed with scripts with a low baseline, East Asian characters should be set so that the bottom of the character is between the baseline and the descender height. ## See also - [CSS box alignment](/en-US/docs/Web/CSS/CSS_box_alignment/Box_alignment#types_of_alignment) - [CSS inline layout](/en-US/docs/Web/CSS/CSS_inline_layout) module - [Baseline (Typography)](<https://en.wikipedia.org/wiki/Baseline_(typography)>) on Wikipedia