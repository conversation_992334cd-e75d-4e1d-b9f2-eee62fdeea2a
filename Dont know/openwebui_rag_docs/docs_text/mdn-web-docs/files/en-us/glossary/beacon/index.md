Path: mdn-web-docs > files > en-us > glossary > beacon > index.md

Path: mdn-web-docs > files > en-us > glossary > beacon > index.md Path: mdn-web-docs > files > en-us > glossary > beacon > index.md Path: mdn-web-docs > files > en-us > glossary > beacon > index.md Path: mdn-web-docs > files > en-us > glossary > beacon > index.md --- title: Beacon slug: Glossary/Beacon page-type: glossary-definition --- {{GlossarySidebar}} A web **beacon** is a small object, such as a 1 pixel gif, embedded in markup, used to communicate information back to the web server or to 3rd party servers. Beacons are generally included to provide information about the user for statistical purposes. Beacons are often included within third party scripts for collecting user data, performance metrics and error reporting. There is a [W3C Draft Beacon Specification](https://w3c.github.io/beacon/) to standardize the beacon as an interface to asynchronously transfer HTTP data from User Agent to a web server prior to page load without negative performance impact. ## See also - Related glossary terms: - {{glossary("Real User Monitoring")}} (RUM)