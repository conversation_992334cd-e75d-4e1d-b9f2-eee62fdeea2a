Path: mdn-web-docs > files > en-us > glossary > bigint > index.md

Path: mdn-web-docs > files > en-us > glossary > bigint > index.md Path: mdn-web-docs > files > en-us > glossary > bigint > index.md Path: mdn-web-docs > files > en-us > glossary > bigint > index.md Path: mdn-web-docs > files > en-us > glossary > bigint > index.md --- title: BigInt slug: Glossary/BigInt page-type: glossary-definition --- {{GlossarySidebar}} In {{Glossary("JavaScript")}}, **BigInt** is a numeric data type that can represent integers in the [arbitrary precision format](https://en.wikipedia.org/wiki/Arbitrary-precision_arithmetic). In other programming languages different numeric types can exist, for examples: Integers, Floats, Doubles, or Bignums. ## See also - [Numeric types](https://en.wikipedia.org/wiki/Data_type#Numeric_types) on Wikipedia - The JavaScript type: [`BigInt`](/en-US/docs/Web/JavaScript/Guide/Data_structures#bigint_type) - The JavaScript global object {{jsxref("BigInt")}}