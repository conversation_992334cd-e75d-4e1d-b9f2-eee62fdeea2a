Path: mdn-web-docs > files > en-us > glossary > block > css > index.md

Path: mdn-web-docs > files > en-us > glossary > block > css > index.md Path: mdn-web-docs > files > en-us > glossary > block > css > index.md Path: mdn-web-docs > files > en-us > glossary > block > css > index.md Path: mdn-web-docs > files > en-us > glossary > block > css > index.md --- title: Block (CSS) slug: Glossary/Block/CSS page-type: glossary-definition --- {{GlossarySidebar}} A **block** on a webpage is an {{glossary("HTML")}} {{glossary("element")}} that appears on a new line, i.e., underneath the preceding element in a horizontal writing mode, and above the following element (commonly known as a _block-level element_). For example, {{htmlelement("p")}} is by default a block-level element, whereas {{htmlelement("a")}} is an _inline element_ you can put several links next to one another in your HTML source and they will sit on the same line as one another in the rendered output. Using the {{cssxref("display")}} property you can change whether an element displays inline or as a block (among many other options); **blocks** are also subject to the effects of positioning schemes and use of the {{cssxref("position")}} property. ## See also - [Visual formatting model](/en-US/docs/Web/CSS/CSS_display/Visual_formatting_model)