Path: mdn-web-docs > files > en-us > glossary > block > scripting > index.md

Path: mdn-web-docs > files > en-us > glossary > block > scripting > index.md Path: mdn-web-docs > files > en-us > glossary > block > scripting > index.md Path: mdn-web-docs > files > en-us > glossary > block > scripting > index.md Path: mdn-web-docs > files > en-us > glossary > block > scripting > index.md --- title: Block (scripting) slug: Glossary/Block/Scripting page-type: glossary-definition --- {{GlossarySidebar}} In {{glossary("JavaScript")}}, a **block** is a collection of related {{glossary("statement","statements")}} enclosed in braces ("{}"). For example, you can put a block of statements after an {{jsxref("Statements/if...else","if (condition)")}} block, indicating that the interpreter should run the code inside the block if the condition is true, or skip the whole block if the condition is false. ## See also - [JavaScript block statement](/en-US/docs/Web/JavaScript/Reference/Statements/block)