Path: mdn-web-docs > files > en-us > glossary > boolean > aria > index.md

Path: mdn-web-docs > files > en-us > glossary > boolean > aria > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > aria > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > aria > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > aria > index.md --- title: Boolean attribute (ARIA) slug: Glossary/Boolean/ARIA page-type: glossary-definition --- {{GlossarySidebar}} A **boolean attribute** in ARIA is an {{Glossary("Enumerated", "enumerated attribute")}} that includes `true` or `false` in the enumerated list. In ARIA, booleans only come in the form of strings `"true"` and `"false"`. ## See also - Related glossary terms: - {{Glossary("Enumerated")}} that contains more behavioral details and examples of ARIA boolean attributes - {{Glossary("Boolean/HTML", "Boolean attribute")}} in HTML