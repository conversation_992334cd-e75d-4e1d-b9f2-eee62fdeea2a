Path: mdn-web-docs > files > en-us > glossary > boolean > index.md

Path: mdn-web-docs > files > en-us > glossary > boolean > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > index.md --- title: Boolean slug: Glossary/Boolean page-type: glossary-disambiguation --- {{GlossarySidebar}} In computer science, a **Boolean** is a logical data type that can have only the values `true` or `false`. For example, in JavaScript, Boolean conditionals are often used to decide which sections of code to execute (such as in [if statements](/en-US/docs/Web/JavaScript/Reference/Statements/if...else)) or repeat (such as in [for loops](/en-US/docs/Web/JavaScript/Reference/Statements/for)). Below is some JavaScript pseudocode (it's not truly executable code) demonstrating this concept. ```js-nolint /* JavaScript if statement */ if (boolean conditional) { // code to execute if the conditional is true } if (boolean conditional) { console.log("boolean conditional resolved to true"); } else { console.log("boolean conditional resolved to false"); } /* JavaScript for loop */ for (control variable; boolean conditional; counter) { // code to execute repeatedly if the conditional is true } ``` The Boolean value is named after English mathematician [George Boole](https://en.wikipedia.org/wiki/George_Boole), who pioneered the field of mathematical logic. Above is a general introduction. The term **Boolean** can have more specific meanings depending on the context. It may refer to: {{GlossaryDisambiguation}} ## See also - [Boolean](https://en.wikipedia.org/wiki/Boolean_data_type) on Wikipedia