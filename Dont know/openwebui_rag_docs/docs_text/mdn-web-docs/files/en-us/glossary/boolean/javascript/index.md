Path: mdn-web-docs > files > en-us > glossary > boolean > javascript > index.md

Path: mdn-web-docs > files > en-us > glossary > boolean > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > boolean > javascript > index.md --- title: Boolean (JavaScript) slug: Glossary/Boolean/JavaScript page-type: glossary-definition --- {{GlossarySidebar}} A **Boolean** in JavaScript is a {{Glossary("Primitive", "primitive value")}} that can be either `true` or `false`. A {{jsxref("Boolean")}} object is a {{Glossary("wrapper")}} around a Boolean primitive. ## See also - [The JavaScript global object](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean) - [JavaScript data types and data structures](/en-US/docs/Web/JavaScript/Guide/Data_structures)