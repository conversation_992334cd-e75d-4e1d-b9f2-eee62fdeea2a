Path: mdn-web-docs > files > en-us > glossary > bounding_box > index.md

Path: mdn-web-docs > files > en-us > glossary > bounding_box > index.md Path: mdn-web-docs > files > en-us > glossary > bounding_box > index.md Path: mdn-web-docs > files > en-us > glossary > bounding_box > index.md Path: mdn-web-docs > files > en-us > glossary > bounding_box > index.md --- title: Bounding Box slug: Glossary/Bounding_box page-type: glossary-definition --- {{GlossarySidebar}} The **bounding box** of an element is the smallest possible rectangle (aligned with the axes of that element's user coordinate system) that entirely encloses it and its descendants. The bounding box is the invisible rectangular box the browser places the object in when it draws the object on a page.