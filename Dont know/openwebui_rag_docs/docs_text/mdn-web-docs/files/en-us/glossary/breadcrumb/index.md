Path: mdn-web-docs > files > en-us > glossary > breadcrumb > index.md

Path: mdn-web-docs > files > en-us > glossary > breadcrumb > index.md Path: mdn-web-docs > files > en-us > glossary > breadcrumb > index.md Path: mdn-web-docs > files > en-us > glossary > breadcrumb > index.md Path: mdn-web-docs > files > en-us > glossary > breadcrumb > index.md --- title: Breadcrumb slug: Glossary/Breadcrumb page-type: glossary-definition --- {{GlossarySidebar}} A **breadcrumb**, or breadcrumb trail, is a navigational aid that is typically placed between a site's header and the main content, displaying either a hierarchy of the current page in relation to the site's structure, from top level to current page, or a list of the links the user followed to get to the current page, in the order visited. A location breadcrumb for this document might look something like this: [MDN](/) > [Glossary](/en-US/docs/Glossary) > Breadcrumb Breadcrumb trails enable users to be aware of their location within a website. This type of navigation, if done correctly, helps users know where they are in a site and how they got there. They can also help a user get back to where they were before and can reduce the number of clicks needed to get to a higher-level page. > [!NOTE] > While there's no dedicated [semantic HTML element](https://html.spec.whatwg.org/multipage/semantics-other.html#rel-up) for breadcrumb navigation menus, the {{htmlelement("ol")}} element is commonly used to represent their hierarchical structure. ## See also - [Breadcrumb Navigation](/en-US/docs/Web/CSS/Layout_cookbook/Breadcrumb_Navigation) - [Google Search Central: Breadcrumb Structured Data](https://developers.google.com/search/docs/appearance/structured-data/breadcrumb) - [APG Guide: Breadcrumb Example](https://www.w3.org/WAI/ARIA/apg/patterns/breadcrumb/examples/breadcrumb/) - [Understanding Success Criterion 2.4.8 | W3C Understanding WCAG 2.2](https://www.w3.org/WAI/WCAG22/Understanding/location) - [Understanding Technique 65 | W3C Understanding WCAG 2.2](https://www.w3.org/WAI/WCAG22/Techniques/general/G65)