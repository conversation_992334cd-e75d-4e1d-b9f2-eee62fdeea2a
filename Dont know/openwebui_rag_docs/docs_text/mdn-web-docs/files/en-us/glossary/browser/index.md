Path: mdn-web-docs > files > en-us > glossary > browser > index.md

Path: mdn-web-docs > files > en-us > glossary > browser > index.md Path: mdn-web-docs > files > en-us > glossary > browser > index.md Path: mdn-web-docs > files > en-us > glossary > browser > index.md Path: mdn-web-docs > files > en-us > glossary > browser > index.md --- title: Browser slug: Glossary/Browser page-type: glossary-definition --- {{GlossarySidebar}} A **Web browser** or **browser** is a program that retrieves and displays pages from the {{Glossary("World Wide Web", "Web")}}, and lets users access further pages through {{Glossary("hyperlink", "hyperlinks")}}. A browser is the most familiar type of {{Glossary("user agent")}}. It uses a {{Glossary("Engine/Rendering", "rendering engine")}} to display web pages. Common browsers include: - {{Glossary("Google Chrome")}} - {{Glossary("Mozilla Firefox")}} - {{Glossary("Apple Safari")}} - {{Glossary("Microsoft Edge")}} - {{Glossary("Opera Browser")}} ## See also - [Web browser](https://en.wikipedia.org/wiki/Web_browser) on Wikipedia - {{HTTPHeader("User-agent")}} (HTTP Header) - Download a browser - [Mozilla Firefox](https://www.mozilla.org/en-US/firefox/new/) - [Google Chrome](https://www.google.com/chrome/) - [Microsoft Edge](https://www.microsoft.com/en-us/edge) - [Opera Browser](https://www.opera.com/) - Related glossary terms: - {{Glossary("Engine/Rendering", "Rendering engine")}} - {{Glossary("User agent")}}