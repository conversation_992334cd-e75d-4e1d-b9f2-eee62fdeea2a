Path: mdn-web-docs > files > en-us > glossary > bun > index.md

Path: mdn-web-docs > files > en-us > glossary > bun > index.md Path: mdn-web-docs > files > en-us > glossary > bun > index.md Path: mdn-web-docs > files > en-us > glossary > bun > index.md Path: mdn-web-docs > files > en-us > glossary > bun > index.md --- title: Bun slug: Glossary/Bun page-type: glossary-definition --- {{GlossarySidebar}} **Bun** is a fast and modern {{Glossary("JavaScript")}} runtime environment designed to prioritize performance and developer tooling. Unlike [Node.js](https://nodejs.org/) and [Deno](https://deno.com/), Bun is built on Apple's [JavaScriptCore](https://trac.webkit.org/wiki/JavaScriptCore). Notable features of Bun include: - Drop-in Node.js replacement: almost all Node.js programs can run using Bun-equivalent commands. - State-of-the-art JavaScript execution speed in most real-world scenarios. - Native support for JavaScript syntax extensions like JSX and TypeScript. - Global cache for managing package installations to minimize disk usage. - Built-in bundler, transpiler, [Jest](https://jestjs.io/) compatible test-runner, package manager, and more built-in utilities that web developers may find useful. ## See also - [Bun website](https://bun.sh/)