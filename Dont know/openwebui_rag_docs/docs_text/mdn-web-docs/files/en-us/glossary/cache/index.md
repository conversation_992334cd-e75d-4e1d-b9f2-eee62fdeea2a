Path: mdn-web-docs > files > en-us > glossary > cache > index.md

Path: mdn-web-docs > files > en-us > glossary > cache > index.md Path: mdn-web-docs > files > en-us > glossary > cache > index.md Path: mdn-web-docs > files > en-us > glossary > cache > index.md Path: mdn-web-docs > files > en-us > glossary > cache > index.md --- title: Cache slug: Glossary/Cache page-type: glossary-definition --- {{GlossarySidebar}} A **cache** (web cache or HTTP cache) is a component that stores HTTP responses temporarily so that it can be used for subsequent HTTP requests as long as it meets certain conditions. ## See also - [Web cache](https://en.wikipedia.org/wiki/Web_cache) on Wikipedia