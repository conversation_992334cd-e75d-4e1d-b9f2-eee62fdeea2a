Path: mdn-web-docs > files > en-us > glossary > call_stack > index.md

Path: mdn-web-docs > files > en-us > glossary > call_stack > index.md Path: mdn-web-docs > files > en-us > glossary > call_stack > index.md Path: mdn-web-docs > files > en-us > glossary > call_stack > index.md Path: mdn-web-docs > files > en-us > glossary > call_stack > index.md --- title: Call stack slug: Glossary/Call_stack page-type: glossary-definition --- {{GlossarySidebar}} A **call stack** is a mechanism for an interpreter (like the JavaScript interpreter in a web browser) to keep track of its place in a script that calls multiple {{glossary("function","functions")}} what function is currently being run and what functions are called from within that function, etc. - When a script calls a function, the interpreter adds it to the call stack and then starts carrying out the function. - Any functions that are called by that function are added to the call stack further up, and run where their calls are reached. - When the current function is finished, the interpreter takes it off the stack and resumes execution where it left off in the last code listing. - If the stack takes up more space than it was assigned, a "stack overflow" error is thrown. ## Example ```js function greeting() { // [1] Some code here sayHi(); // [2] Some code here } function sayHi() { return "Hi!"; } // Invoke the `greeting` function greeting(); // [3] Some code here ``` The call stack will be empty at the very beginning, and the code above would be executed like this: 1. Ignore all functions, until it reaches the `greeting()` function invocation. 2. Add the `greeting()` function to the call stack list, and we have: ```plain - greeting ``` 3. Execute all lines of code inside the `greeting()` function. 4. Get to the `sayHi()` function invocation. 5. Add the `sayHi()` function to the call stack list, like: ```plain - sayHi - greeting ``` 6. Execute all lines of code inside the `sayHi()` function, until reaches its end. 7. Return execution to the line that invoked `sayHi()` and continue executing the rest of the `greeting()` function. 8. Delete the `sayHi()` function from our call stack list. Now the call stack looks like: ```plain - greeting ``` 9. When everything inside the `greeting()` function has been executed, return to its invoking line to continue executing the rest of the JS code. 10. Delete the `greeting()` function from the call stack list. Once again, the call stack become empty. In summary, then, we start with an empty Call Stack. Whenever we invoke a function, it is automatically added to the Call Stack. Once the function has executed all of its code, it is automatically removed from the Call Stack. Ultimately, the Stack is empty again. ## See also - [Call stack](https://en.wikipedia.org/wiki/Call_stack) on Wikipedia - Related glossary terms: - {{Glossary("Function")}}