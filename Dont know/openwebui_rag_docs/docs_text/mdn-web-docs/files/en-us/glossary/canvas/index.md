Path: mdn-web-docs > files > en-us > glossary > canvas > index.md

Path: mdn-web-docs > files > en-us > glossary > canvas > index.md Path: mdn-web-docs > files > en-us > glossary > canvas > index.md Path: mdn-web-docs > files > en-us > glossary > canvas > index.md Path: mdn-web-docs > files > en-us > glossary > canvas > index.md --- title: Canvas slug: Glossary/Canvas page-type: glossary-definition --- {{GlossarySidebar}} The **canvas element** is part of [HTML5](https://en.wikipedia.org/wiki/HTML5) and allows for dynamic, [scriptable](https://en.wikipedia.org/wiki/Scripting_language) [rendering](<https://en.wikipedia.org/wiki/Rendering_(computer_graphics)>) of 2D and 3D shapes and [bitmap](https://en.wikipedia.org/wiki/Bitmap) images. It is a low level, procedural model that updates a [bitmap](https://en.wikipedia.org/wiki/Bitmap) and does not have a built-in [scene graph](https://en.wikipedia.org/wiki/Scene_graph). It provides an empty graphic zone on which specific {{Glossary("JavaScript")}} {{Glossary("API","APIs")}} can draw (such as Canvas 2D or {{Glossary("WebGL")}}). ## See also - [Canvas tutorial](/en-US/docs/Web/API/Canvas_API/Tutorial) - {{HTMLElement("canvas")}} element - {{domxref("CanvasRenderingContext2D")}}: The canvas 2D drawing API - [Canvas API](/en-US/docs/Web/API/Canvas_API) - [Canvas](https://en.wikipedia.org/wiki/Canvas_element) on Wikipedia - [The Canvas 2D API specification](https://html.spec.whatwg.org/multipage/)