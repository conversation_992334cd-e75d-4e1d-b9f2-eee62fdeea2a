Path: mdn-web-docs > files > en-us > glossary > card_sorting > index.md

Path: mdn-web-docs > files > en-us > glossary > card_sorting > index.md Path: mdn-web-docs > files > en-us > glossary > card_sorting > index.md Path: mdn-web-docs > files > en-us > glossary > card_sorting > index.md Path: mdn-web-docs > files > en-us > glossary > card_sorting > index.md --- title: Card sorting slug: Glossary/Card_sorting page-type: glossary-definition --- {{GlossarySidebar}} **Card sorting** is a simple technique used in {{glossary("Information architecture")}} whereby people involved in the design of a website (or other type of product) are invited to write down the content / services / features they feel the product should contain, and then organize those features into categories or groupings. This can be used for example to work out what should go on each page of a website. The name comes from the fact that often card sorting is carried out by literally writing the items to sort onto cards, and then arranging the cards into piles. ## See also - [Card sorting](https://en.wikipedia.org/wiki/Card_sorting) on Wikipedia