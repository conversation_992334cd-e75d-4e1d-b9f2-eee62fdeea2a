Path: mdn-web-docs > files > en-us > glossary > character > index.md

Path: mdn-web-docs > files > en-us > glossary > character > index.md Path: mdn-web-docs > files > en-us > glossary > character > index.md Path: mdn-web-docs > files > en-us > glossary > character > index.md Path: mdn-web-docs > files > en-us > glossary > character > index.md --- title: Character slug: Glossary/Character page-type: glossary-definition --- {{GlossarySidebar}} A **character** is either a symbol (letter, number, punctuation) or a non-printing "control" (e.g., carriage return or soft hyphen). {{glossary("UTF-8")}} is the most common character set and includes the graphemes of the most popular human languages. ## See also - [Character (computing)](<https://en.wikipedia.org/wiki/Character_(computing)>) on Wikipedia - [Character encoding](https://en.wikipedia.org/wiki/Character_encoding) on Wikipedia - Related glossary terms: - {{glossary("ASCII")}} - [UTF-8](https://en.wikipedia.org/wiki/UTF-8) on Wikipedia - [Unicode](https://en.wikipedia.org/wiki/Unicode) on Wikipedia