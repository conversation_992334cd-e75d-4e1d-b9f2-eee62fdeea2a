Path: mdn-web-docs > files > en-us > glossary > closure > index.md

Path: mdn-web-docs > files > en-us > glossary > closure > index.md Path: mdn-web-docs > files > en-us > glossary > closure > index.md Path: mdn-web-docs > files > en-us > glossary > closure > index.md Path: mdn-web-docs > files > en-us > glossary > closure > index.md --- title: Closure slug: Glossary/Closure page-type: glossary-definition --- {{GlossarySidebar}} In computer programming, a **closure** is a technique for implementing lexically {{glossary("scope", "scoped")}} name binding in a language with {{glossary("first-class function", "first-class functions")}}. In {{glossary("JavaScript")}}, a {{glossary("function")}} creates a closure context. ## See also - [Closures in JavaScript](/en-US/docs/Web/JavaScript/Guide/Closures) - [Closure](https://en.wikipedia.org/wiki/Closure_%28computer_programming%29) on Wikipedia