Path: mdn-web-docs > files > en-us > glossary > cloud_computing > index.md

Path: mdn-web-docs > files > en-us > glossary > cloud_computing > index.md Path: mdn-web-docs > files > en-us > glossary > cloud_computing > index.md Path: mdn-web-docs > files > en-us > glossary > cloud_computing > index.md Path: mdn-web-docs > files > en-us > glossary > cloud_computing > index.md --- title: Cloud computing slug: Glossary/Cloud_computing page-type: glossary-definition --- {{GlossarySidebar}} **Cloud computing** refers to the on-demand delivery of a wide range of computing services such as storage, databases, networking, analytics, and intelligence via the Internet also known as **cloud services**. Cloud services are infrastructure, platforms, or software made available to users via the internet, hosted by third-party providers such as Google (Google Cloud Platform), Amazon AWS (Amazon Web Services), and Microsoft (Azure). Users can access cloud services through a _pay-as-you-go_ pricing model, ensuring they only pay for what they use, and without requiring any complex software set up on their own computers. This model enables faster innovation, flexible scalability, and significant cost savings. The three main types of cloud computing are Infrastructure as a Service (IaaS), Platform as a Service (PaaS), and Software as a Service (SaaS). Each type of cloud computing provides different levels of control, flexibility, and management. - Infrastructure as a Service - : IaaS provides virtualized computing resources over the internet, including servers, storage, and networking, allowing users to manage their infrastructure. This gives users a high level of flexibility and control over the resources. Companies using IaaS can scale their server infrastructure rapidly without owning physical servers. Examples include Amazon Web Services (AWS EC2), Microsoft Azure Virtual Machines, and Google Compute Engine (GCE). - Platform as a Service - : PaaS provides a platform allowing customers to develop, run, and manage applications without worrying about the underlying infrastructure such as servers, storage, and networking. PaaS allows developers to focus on writing code and application logic, without managing servers or operating systems. Examples include Google App Engine, Microsoft Azure App Service, Heroku, and AWS Elastic Beanstalk. - Software as a Service - : SaaS delivers software applications over the internet, which users can access via a web browser. The provider manages all the underlying infrastructure, platform, and data. Businesses using SaaS don't need to install or manage software locally. Instead, they can access software such as email, CRM, and collaboration tools directly from a web browser. Examples include Google Workspace, Microsoft 365, Slack, GitHub, and ChatGPT. ## See also - [Cloud Computing](https://en.wikipedia.org/wiki/Cloud_computing) on Wikipedia - [What is Cloud Computing?](https://cloud.google.com/learn/what-is-cloud-computing?hl=en) on Google Cloud - [Cloud Service Provider](https://cloud.google.com/learn/what-is-a-cloud-service-provider?hl=en) on Google Cloud - [NIST Definition of Cloud Computing](https://nvlpubs.nist.gov/nistpubs/legacy/sp/nistspecialpublication800-145.pdf)