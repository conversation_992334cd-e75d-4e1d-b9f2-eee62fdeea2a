Path: mdn-web-docs > files > en-us > glossary > computer_programming > index.md

Path: mdn-web-docs > files > en-us > glossary > computer_programming > index.md Path: mdn-web-docs > files > en-us > glossary > computer_programming > index.md Path: mdn-web-docs > files > en-us > glossary > computer_programming > index.md Path: mdn-web-docs > files > en-us > glossary > computer_programming > index.md --- title: Computer Programming slug: Glossary/Computer_Programming page-type: glossary-definition --- {{GlossarySidebar}} **Computer programming** is the process of composing and organizing a collection of instructions. These tell a computer/software program what to do in a language which the computer understands. These instructions come in the form of many different languages such as C++, Java, JavaScript, HTML, Python, Ruby, and Rust. Using an appropriate language, you can program/create all sorts of software. For example, a program that helps scientists with complex calculations, a database that stores huge amounts of data, a website that allows people to download music, or animation software that allows people to create animated movies. ## See also - [Computer programming](https://en.wikipedia.org/wiki/Computer_programming) on Wikipedia - [List of Programming Languages](https://en.wikipedia.org/wiki/List_of_programming_languages) on Wikipedia