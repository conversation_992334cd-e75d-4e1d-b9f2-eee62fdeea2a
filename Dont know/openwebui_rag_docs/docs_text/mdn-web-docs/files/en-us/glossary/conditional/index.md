Path: mdn-web-docs > files > en-us > glossary > conditional > index.md

Path: mdn-web-docs > files > en-us > glossary > conditional > index.md Path: mdn-web-docs > files > en-us > glossary > conditional > index.md Path: mdn-web-docs > files > en-us > glossary > conditional > index.md Path: mdn-web-docs > files > en-us > glossary > conditional > index.md --- title: Conditional slug: Glossary/Conditional page-type: glossary-definition --- {{GlossarySidebar}} A **condition** is a set of rules that can interrupt normal code execution or change it, depending on whether the condition is completed or not. An instruction or a set of instructions is executed if a specific condition is fulfilled. Otherwise, another instruction is executed. It is also possible to repeat the execution of an instruction, or set of instructions, while a condition is not yet fulfilled. ## See also - Related glossary terms: - {{glossary("Control flow")}} - [Learn: Making decisions in your code conditionals](/en-US/docs/Learn_web_development/Core/Scripting/Conditionals) - [Control flow and error handling in JavaScript](/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling) - [Conditional](<https://en.wikipedia.org/wiki/Conditional_(computer_programming)>) on Wikipedia