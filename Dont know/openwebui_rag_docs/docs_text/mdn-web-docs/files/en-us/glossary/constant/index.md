Path: mdn-web-docs > files > en-us > glossary > constant > index.md

Path: mdn-web-docs > files > en-us > glossary > constant > index.md Path: mdn-web-docs > files > en-us > glossary > constant > index.md Path: mdn-web-docs > files > en-us > glossary > constant > index.md Path: mdn-web-docs > files > en-us > glossary > constant > index.md --- title: Constant slug: Glossary/Constant page-type: glossary-definition --- {{GlossarySidebar}} A **constant** is a value that the programmer cannot change, for example numbers (1, 2, 42). With {{glossary("variable","variables")}}, on the other hand, the programmer can assign a new {{glossary("value")}} to a variable name already in use. Like variables, some constants are bound to identifiers. For example, the identifier `pi` could be bound to the value 3.14 . ## See also - [Constant](<https://en.wikipedia.org/wiki/Constant_(computer_programming)>) on Wikipedia