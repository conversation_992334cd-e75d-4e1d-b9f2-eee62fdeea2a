Path: mdn-web-docs > files > en-us > glossary > constructor > index.md

Path: mdn-web-docs > files > en-us > glossary > constructor > index.md Path: mdn-web-docs > files > en-us > glossary > constructor > index.md Path: mdn-web-docs > files > en-us > glossary > constructor > index.md Path: mdn-web-docs > files > en-us > glossary > constructor > index.md --- title: Constructor slug: Glossary/Constructor page-type: glossary-definition --- {{GlossarySidebar}} A **constructor** is a specialized {{glossary("function")}} that generates {{glossary("object", "objects")}} with the same shape and behavior. The constructor initializes this object with some data specific to the object. The concept of a constructor can be applied to most {{glossary("OOP","object-oriented programming")}} languages. In {{glossary("JavaScript")}}, a constructor is usually declared within a [class](/en-US/docs/Web/JavaScript/Reference/Classes), but it can also be declared as a [function](/en-US/docs/Web/JavaScript/Reference/Functions). In fact, any function that can be called with the [`new`](/en-US/docs/Web/JavaScript/Reference/Operators/new) operator is a constructor. ## See also - [Classes and constructors in JavaScript](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Classes_in_JavaScript#classes_and_constructors) - [`new` operator in JavaScript](/en-US/docs/Web/JavaScript/Reference/Operators/new) - [Constructor](https://en.wikipedia.org/wiki/Constructor_%28object-oriented_programming%29) on Wikipedia