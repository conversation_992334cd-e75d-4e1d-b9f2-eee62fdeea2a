Path: mdn-web-docs > files > en-us > glossary > continuous_media > index.md

Path: mdn-web-docs > files > en-us > glossary > continuous_media > index.md Path: mdn-web-docs > files > en-us > glossary > continuous_media > index.md Path: mdn-web-docs > files > en-us > glossary > continuous_media > index.md Path: mdn-web-docs > files > en-us > glossary > continuous_media > index.md --- title: Continuous Media slug: Glossary/Continuous_Media page-type: glossary-definition --- {{GlossarySidebar}} **Continuous media** is data where there is a timing relationship between source and destination. The most common examples of continuous media are audio and motion video. Continuous media can be real-time (interactive), where there is a "tight" timing relationship between source and sink, or streaming (playback), where the relationship is less strict. CSS can be used in a variety of contexts, including print media. And some CSS, particularly those that are used for layout, behave differently depending on the context they are in. Continuous Media, therefore, identifies a context where the content is not broken up. It flows continuously. Web content displayed on a screen is continuous media, as is spoken content.