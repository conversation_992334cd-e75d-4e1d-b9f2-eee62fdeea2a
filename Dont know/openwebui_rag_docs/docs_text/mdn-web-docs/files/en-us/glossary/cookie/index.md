Path: mdn-web-docs > files > en-us > glossary > cookie > index.md

Path: mdn-web-docs > files > en-us > glossary > cookie > index.md Path: mdn-web-docs > files > en-us > glossary > cookie > index.md Path: mdn-web-docs > files > en-us > glossary > cookie > index.md Path: mdn-web-docs > files > en-us > glossary > cookie > index.md --- title: Cookie slug: Glossary/Cookie page-type: glossary-definition --- {{GlossarySidebar}} A **cookie** is a small piece of information left on a visitor's computer by a website, via a web browser. Cookies are used to personalize a user's web experience with a website. It may contain the user's preferences or inputs when accessing that website. A user can customize their web browser to accept, reject, or delete cookies. Cookies can be set and modified at the server level using the `Set-Cookie` [HTTP header](/en-US/docs/Web/HTTP/Guides/Cookies), or with JavaScript using [`document.cookie`](/en-US/docs/Web/API/Document/cookie). ## See also - [HTTP cookie](https://en.wikipedia.org/wiki/HTTP_cookie) on Wikipedia