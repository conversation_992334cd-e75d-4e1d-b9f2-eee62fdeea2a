Path: mdn-web-docs > files > en-us > glossary > credential > index.md

Path: mdn-web-docs > files > en-us > glossary > credential > index.md Path: mdn-web-docs > files > en-us > glossary > credential > index.md Path: mdn-web-docs > files > en-us > glossary > credential > index.md Path: mdn-web-docs > files > en-us > glossary > credential > index.md --- title: Credential slug: Glossary/Credential page-type: glossary-definition --- {{GlossarySidebar}} A **credential** is an object which enables a system to make an {{glossary("authentication")}} decision: for example, to decide whether to sign a user into an account. In web security, types of credential include: - a password - biometric data - a token entered from a one-time SMS code - the key used to make authentication assertions in a public-key system such as [Web Authentication](/en-US/docs/Web/API/Web_Authentication_API) The [Credential Management API](/en-US/docs/Web/API/Credential_Management_API) enables developers to create, store, and retrieve various types of credential. ## See also - {{rfc("4949", "Internet Security Glossary")}}