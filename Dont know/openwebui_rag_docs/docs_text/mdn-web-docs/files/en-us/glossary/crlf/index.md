Path: mdn-web-docs > files > en-us > glossary > crlf > index.md

Path: mdn-web-docs > files > en-us > glossary > crlf > index.md Path: mdn-web-docs > files > en-us > glossary > crlf > index.md Path: mdn-web-docs > files > en-us > glossary > crlf > index.md Path: mdn-web-docs > files > en-us > glossary > crlf > index.md --- title: CRLF slug: Glossary/CRLF page-type: glossary-definition --- {{GlossarySidebar}} **CR and LF** are [control characters](https://en.wikipedia.org/wiki/Control_character) or [bytecode](https://en.wikipedia.org/wiki/Bytecode) that can be used to mark a line break in a text file. - CR = **Carriage Return** (`\r`, `0x0D` in hexadecimal, 13 in decimal) moves the cursor to the beginning of the line without advancing to the next line. - LF = **Line Feed** (`\n`, `0x0A` in hexadecimal, 10 in decimal) moves the cursor down to the next line without returning to the beginning of the line. A CR immediately followed by a LF (CRLF, `\r\n`, or `0x0D0A`) moves the cursor to the beginning of the line and then down to the next line. ## See also - [Newline](https://en.wikipedia.org/wiki/Newline#In_programming_languages) on Wikipedia - [Carriage return](https://en.wikipedia.org/wiki/Carriage_return#Computers) on Wikipedia