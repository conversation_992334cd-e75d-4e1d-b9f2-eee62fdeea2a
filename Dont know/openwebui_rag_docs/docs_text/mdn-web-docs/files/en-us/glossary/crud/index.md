Path: mdn-web-docs > files > en-us > glossary > crud > index.md

Path: mdn-web-docs > files > en-us > glossary > crud > index.md Path: mdn-web-docs > files > en-us > glossary > crud > index.md Path: mdn-web-docs > files > en-us > glossary > crud > index.md Path: mdn-web-docs > files > en-us > glossary > crud > index.md --- title: CRUD slug: Glossary/CRUD page-type: glossary-definition --- {{GlossarySidebar}} **CRUD** (Create, Read, Update, Delete) is an acronym for ways one can operate on stored data. It is a mnemonic for the four basic functions of persistent storage. CRUD typically refers to operations performed in a database or datastore, but it can also apply to higher level functions of an application such as soft deletes where data is not actually deleted but marked as deleted via a status. ## See also - [CRUD](https://en.wikipedia.org/wiki/CRUD) on Wikipedia