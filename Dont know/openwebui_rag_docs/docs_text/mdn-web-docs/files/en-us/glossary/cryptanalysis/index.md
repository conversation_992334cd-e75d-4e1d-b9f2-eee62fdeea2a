Path: mdn-web-docs > files > en-us > glossary > cryptanalysis > index.md

Path: mdn-web-docs > files > en-us > glossary > cryptanalysis > index.md Path: mdn-web-docs > files > en-us > glossary > cryptanalysis > index.md Path: mdn-web-docs > files > en-us > glossary > cryptanalysis > index.md Path: mdn-web-docs > files > en-us > glossary > cryptanalysis > index.md --- title: Cryptanalysis slug: Glossary/Cryptanalysis page-type: glossary-definition --- {{GlossarySidebar}} **Cryptanalysis** is the branch of {{glossary("cryptography")}} that studies how to break codes and cryptosystems. Cryptanalysis creates techniques to break {{glossary("cipher","ciphers")}}, in particular by methods more efficient than a [brute-force search](https://en.wikipedia.org/wiki/Brute-force_search). In addition to traditional methods like [frequency analysis](https://en.wikipedia.org/wiki/Frequency_analysis) and [index of coincidence](https://en.wikipedia.org/wiki/Index_of_coincidence), cryptanalysis includes more recent methods, like [linear cryptanalysis](https://en.wikipedia.org/wiki/Linear_cryptanalysis) or [differential cryptanalysis](https://en.wikipedia.org/wiki/Differential_cryptanalysis), that can break more advanced ciphers. ## See also - [Cryptanalysis](https://en.wikipedia.org/wiki/Cryptanalysis) on Wikipedia