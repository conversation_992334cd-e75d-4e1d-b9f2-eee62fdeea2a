Path: mdn-web-docs > files > en-us > glossary > cryptography > index.md

Path: mdn-web-docs > files > en-us > glossary > cryptography > index.md Path: mdn-web-docs > files > en-us > glossary > cryptography > index.md Path: mdn-web-docs > files > en-us > glossary > cryptography > index.md Path: mdn-web-docs > files > en-us > glossary > cryptography > index.md --- title: Cryptography slug: Glossary/Cryptography page-type: glossary-definition --- {{GlossarySidebar}} **Cryptography**, or cryptology, is the science that studies how to encode and transmit messages securely. Cryptography designs and studies algorithms used to encode and decode messages in an insecure environment, and their applications. More than just **data confidentiality**, cryptography also tackles **identification**, **authentication**, **non-repudiation**, and **data integrity**. Therefore it also studies usage of cryptographic methods in context, **cryptosystems**. ## See also - [Cryptography](https://en.wikipedia.org/wiki/Cryptography) on Wikipedia - Related glossary terms: - {{Glossary("Block cipher mode of operation")}} - {{Glossary("Cipher")}} - {{Glossary("Ciphertext")}} - {{Glossary("Cipher suite")}} - {{Glossary("Cryptanalysis")}} - {{Glossary("Decryption")}} - {{Glossary("Encryption")}} - {{Glossary("Key")}} - {{Glossary("Plaintext")}} - {{Glossary("Public-key cryptography")}} - {{Glossary("Symmetric-key cryptography")}}