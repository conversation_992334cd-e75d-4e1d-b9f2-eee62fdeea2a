Path: mdn-web-docs > files > en-us > glossary > csp > index.md

Path: mdn-web-docs > files > en-us > glossary > csp > index.md Path: mdn-web-docs > files > en-us > glossary > csp > index.md Path: mdn-web-docs > files > en-us > glossary > csp > index.md Path: mdn-web-docs > files > en-us > glossary > csp > index.md --- title: CSP slug: Glossary/CSP page-type: glossary-definition --- {{GlossarySidebar}} A **CSP** ([Content Security Policy](/en-US/docs/Web/HTTP/Guides/CSP)) is used to detect and mitigate certain types of website related attacks like {{Glossary("Cross-site_scripting")}}, [clickjacking](/en-US/docs/Web/Security/Attacks/Clickjacking) and data injections. The implementation is based on an {{Glossary("HTTP")}} header called {{HTTPHeader("Content-Security-Policy")}}. ## See also - [Content Security Policy documentation](/en-US/docs/Web/HTTP/Guides/CSP) - [Content Security Policy on Wikipedia](https://en.wikipedia.org/wiki/Content_Security_Policy)