Path: mdn-web-docs > files > en-us > glossary > csr > index.md

Path: mdn-web-docs > files > en-us > glossary > csr > index.md Path: mdn-web-docs > files > en-us > glossary > csr > index.md Path: mdn-web-docs > files > en-us > glossary > csr > index.md Path: mdn-web-docs > files > en-us > glossary > csr > index.md --- title: Client-side rendering (CSR) slug: Glossary/CSR page-type: glossary-definition --- {{GlossarySidebar}} **Client-side rendering** (CSR) refers to the practice of generating HTML content using JavaScript in the browser. CSR is opposed to {{glossary("SSR", "server-side rendering")}}, where the server generates the HTML content. Both techniques are not mutually exclusive and can be used together in the same application. A pure CSR app may return the following HTML content: ```html <!doctype html> <html> <head> <title>My App</title> <script src="bundle.js"></script> </head> <body> <div id="root"></div> <noscript> <p>This app requires JavaScript to run.</p> </noscript> </body> </html> ``` Then, the actual page content is generated by JavaScript in `bundle.js`, using [DOM manipulation](/en-US/docs/Web/API/Document_Object_Model). Benefits of CSR include: - Interactivity: any page update, including route transitions, do not require a full page reload. This makes the app feel faster and more responsive. - Performance: the server only needs to send the initial HTML content and JavaScript assets. Subsequent page updates can be fetched from an API, which can be faster than fetching a full HTML page, and causes less load on the server. Both SSR and CSR have their performance tradeoffs, and a mix of SSR and CSR can be used to combine the benefits of both techniques. For example, the server can generate a page skeleton with empty placeholders, and the client can fetch additional data and update the page as needed. Note that {{glossary("SPA", "single-page applications")}} do not require the site to be CSR. Modern frameworks, such as [React](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/React_getting_started), [Vue](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/Vue_getting_started), and [Svelte](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/Svelte_getting_started), can be used to build SPAs with SSR capabilities. ## See also - [Introduction to client-side frameworks > server-side rendering](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/Introduction#server-side_rendering) - [Client-side rendering](https://en.wikipedia.org/wiki/Client-side_rendering) on Wikipedia - {{glossary("SSR", "Server-side rendering")}} - {{glossary("SSG", "Static site generator")}} - {{glossary("SPA", "Single-page application")}}