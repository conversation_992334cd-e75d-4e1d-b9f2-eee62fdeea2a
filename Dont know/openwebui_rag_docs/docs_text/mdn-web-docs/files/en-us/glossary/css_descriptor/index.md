Path: mdn-web-docs > files > en-us > glossary > css_descriptor > index.md

Path: mdn-web-docs > files > en-us > glossary > css_descriptor > index.md Path: mdn-web-docs > files > en-us > glossary > css_descriptor > index.md Path: mdn-web-docs > files > en-us > glossary > css_descriptor > index.md Path: mdn-web-docs > files > en-us > glossary > css_descriptor > index.md --- title: Descriptor (CSS) slug: Glossary/CSS_Descriptor page-type: glossary-definition --- {{GlossarySidebar}} A **CSS descriptor** defines the characteristics of an [at-rule](/en-US/docs/Web/CSS/CSS_syntax/At-rule). At-rules may have one or multiple descriptors. Each descriptor has: - A name - A value, which holds the component values - An "!important" flag, which in its default state is unset