Path: mdn-web-docs > files > en-us > glossary > css_pixel > index.md

Path: mdn-web-docs > files > en-us > glossary > css_pixel > index.md Path: mdn-web-docs > files > en-us > glossary > css_pixel > index.md Path: mdn-web-docs > files > en-us > glossary > css_pixel > index.md Path: mdn-web-docs > files > en-us > glossary > css_pixel > index.md --- title: CSS pixel slug: Glossary/CSS_pixel page-type: glossary-definition --- {{GlossarySidebar}} The term **CSS pixel** is synonymous with the CSS unit of absolute length _px_ which is [normatively defined](/en-US/docs/Web/CSS/CSS_Values_and_Units/Numeric_data_types#absolute_length_units) as being exactly 1/96th of 1 CSS inch (_in_). ## See also - {{glossary("Device pixel")}} - [CSS length explained](https://hacks.mozilla.org/2013/09/css-length-explained/) via MDN Hacks Blog (2013)