Path: mdn-web-docs > files > en-us > glossary > css_preprocessor > index.md

Path: mdn-web-docs > files > en-us > glossary > css_preprocessor > index.md Path: mdn-web-docs > files > en-us > glossary > css_preprocessor > index.md Path: mdn-web-docs > files > en-us > glossary > css_preprocessor > index.md Path: mdn-web-docs > files > en-us > glossary > css_preprocessor > index.md --- title: CSS preprocessor slug: Glossary/CSS_preprocessor page-type: glossary-definition --- {{GlossarySidebar}} A **CSS preprocessor** is a program that lets you generate {{Glossary("CSS")}} from the preprocessor's own unique {{Glossary("syntax")}}. There are many CSS preprocessors to choose from, however most CSS preprocessors will add some features that don't exist in pure CSS, such as mixin, nesting selector, inheritance selector, and so on. These features make the CSS structure more readable and easier to maintain. To use a CSS preprocessor, you must install a CSS compiler on your web {{Glossary("server")}}; Or use the CSS preprocessor to compile on the development environment, and then upload compiled CSS file to the web server. ## See also - A few of most popular CSS preprocessors: - [Sass](https://sass-lang.com/) - [LESS](https://lesscss.org/) - [Stylus](https://stylus-lang.com/) - Related glossary terms: - {{Glossary("CSS")}}