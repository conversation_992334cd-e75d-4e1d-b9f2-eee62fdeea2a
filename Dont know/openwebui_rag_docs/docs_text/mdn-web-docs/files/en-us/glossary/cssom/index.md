Path: mdn-web-docs > files > en-us > glossary > cssom > index.md

Path: mdn-web-docs > files > en-us > glossary > cssom > index.md Path: mdn-web-docs > files > en-us > glossary > cssom > index.md Path: mdn-web-docs > files > en-us > glossary > cssom > index.md Path: mdn-web-docs > files > en-us > glossary > cssom > index.md --- title: CSS Object Model (CSSOM) slug: Glossary/CSSOM page-type: glossary-definition --- {{GlossarySidebar}} The [**CSS Object Model (CSSOM)**](/en-US/docs/Web/API/CSS_Object_Model) is a set of APIs for reading and modifying a document's style-related (CSS) information. In other words, similar to the way in which the [DOM](/en-US/docs/Web/API/Document_Object_Model) enables a document's structure and content to be read and modified from JavaScript, the CSSOM allows the document's styling to be read and modified from JavaScript. ## See also - [Populating the page: how browsers work](/en-US/docs/Web/Performance/Guides/How_browsers_work)