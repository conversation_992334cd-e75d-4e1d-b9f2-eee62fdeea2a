Path: mdn-web-docs > files > en-us > glossary > database > index.md

Path: mdn-web-docs > files > en-us > glossary > database > index.md Path: mdn-web-docs > files > en-us > glossary > database > index.md Path: mdn-web-docs > files > en-us > glossary > database > index.md Path: mdn-web-docs > files > en-us > glossary > database > index.md --- title: Database slug: Glossary/Database page-type: glossary-definition --- {{GlossarySidebar}} A **database** is a storing system that collects organized data, to make some works easier like searching, structure, and extend. In web development, most databases use the relational database management system (RDBMS) to organize data and programming in {{glossary("SQL")}}. Some databases, however, don't follow the former mechanism to organized data, which called NoSQL. Some famous server-side RDBMS are _MySQL_(or _MariaDB_ which is a fork of it), _SQL Server_, and _Oracle Database_. On the other hand, some famous NoSQL examples are _MongoDB_, _Cassandra_, and _Redis_. Browsers also have their own database system called {{glossary("IndexedDB")}}. ## See also - [Database](https://en.wikipedia.org/wiki/Database) on Wikipedia - Related glossary terms: - {{Glossary("IndexedDB")}} - {{Glossary("SQL")}}