Path: mdn-web-docs > files > en-us > glossary > decryption > index.md

Path: mdn-web-docs > files > en-us > glossary > decryption > index.md Path: mdn-web-docs > files > en-us > glossary > decryption > index.md Path: mdn-web-docs > files > en-us > glossary > decryption > index.md Path: mdn-web-docs > files > en-us > glossary > decryption > index.md --- title: Decryption slug: Glossary/Decryption page-type: glossary-definition --- {{GlossarySidebar}} In {{glossary("cryptography")}}, **decryption** is the conversion of {{glossary("ciphertext")}} into {{glossary("plaintext")}}. Decryption is an operation which transforms a ciphertext message into plaintext using a cryptographic algorithm called a {{glossary("cipher")}}. Like encryption, decryption in modern ciphers is performed by using a specific algorithm and a secret, called the {{glossary("key")}}. ![The decryption primitive.](decryption.png) Decryption is the reverse process of {{glossary("encryption")}} and if the key stays secret, is mathematically hard to perform. How hard it is depends on how secure the cryptographic algorithm is, and that in itself is subject to change as the study of {{glossary("cryptanalysis")}} advances. ## See also - Related glossary terms: - {{glossary("Encryption")}} - {{glossary("Cipher")}} - {{glossary("Cryptography")}}