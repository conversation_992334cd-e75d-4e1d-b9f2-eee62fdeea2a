Path: mdn-web-docs > files > en-us > glossary > denial_of_service > index.md

Path: mdn-web-docs > files > en-us > glossary > denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > denial_of_service > index.md --- title: Denial of Service (DoS) slug: Glossary/Denial_of_Service page-type: glossary-definition --- {{GlossarySidebar}} **Denial of Service** (DoS) is a category of network attack that consumes available {{Glossary("server")}} resources, typically by flooding the server with requests. The server is then sluggish or unavailable for legitimate users. Computers have limited resources, for example computation power or memory. When these are exhausted, the program can freeze or crash, making it unavailable. A DoS attack consists of various techniques to exhaust these resources and make a server or a network unavailable to legitimate users, or at least make the server perform sluggishly. There are also {{Glossary("Distributed Denial of Service", "Distributed Denial of Service (DDoS)")}} attacks in which a multitude of servers are used to exhaust the computing capacity of an attacked computer. ### Types of DoS attack DoS attacks are more of a category than a particular kind of attack. Here is a non-exhaustive list of DoS attack types: - bandwidth attack - service request flood - SYN flooding attack - ICMP flood attack - peer-to-peer attack - permanent DoS attack - application level flood attack ## See also - [Denial-of-service attack](https://en.wikipedia.org/wiki/Denial-of-service_attack) on Wikipedia - [Denial of Service](https://owasp.org/www-community/attacks/Denial_of_Service) on OWASP