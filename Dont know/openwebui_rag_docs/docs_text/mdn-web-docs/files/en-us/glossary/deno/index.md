Path: mdn-web-docs > files > en-us > glossary > deno > index.md

Path: mdn-web-docs > files > en-us > glossary > deno > index.md Path: mdn-web-docs > files > en-us > glossary > deno > index.md Path: mdn-web-docs > files > en-us > glossary > deno > index.md Path: mdn-web-docs > files > en-us > glossary > deno > index.md --- title: Deno slug: Glossary/Deno page-type: glossary-definition --- {{GlossarySidebar}} **Deno** is a runtime environment for {{Glossary("JavaScript")}}, {{Glossary("TypeScript")}}, and {{glossary("WebAssembly")}}. Like {{glossary("Node.js")}}, Deno is based on the V8 JavaScript engine and enables developers to run JavaScript outside browsers. However, there are some notable differences between Node and Deno. In particular, Deno: - Has built-in support for TypeScript. - Restricts file system and network access by default. - Includes a large standard library, including implementations of many Web APIs. - Does not depend on a centralized registry, instead allowing modules to be loaded from URLs. ## See also - [Deno website](https://deno.com/)