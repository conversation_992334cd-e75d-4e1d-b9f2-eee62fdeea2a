Path: mdn-web-docs > files > en-us > glossary > deserialization > index.md

Path: mdn-web-docs > files > en-us > glossary > deserialization > index.md Path: mdn-web-docs > files > en-us > glossary > deserialization > index.md Path: mdn-web-docs > files > en-us > glossary > deserialization > index.md Path: mdn-web-docs > files > en-us > glossary > deserialization > index.md --- title: Deserialization slug: Glossary/Deserialization page-type: glossary-definition --- {{GlossarySidebar}} **Deserialization** is the process whereby a lower-level format (e.g., that has been transferred over a network, or stored in a data store) is translated into a readable object or other data structure. In {{Glossary("JavaScript")}}, for example, you can deserialize a {{Glossary("JSON")}} {{Glossary("string")}} to an object by calling the {{Glossary("function")}} {{jsxref("JSON.parse()")}}. ## See also - [Serialization](https://en.wikipedia.org/wiki/Serialization) on Wikipedia