Path: mdn-web-docs > files > en-us > glossary > developer_tools > index.md

Path: mdn-web-docs > files > en-us > glossary > developer_tools > index.md Path: mdn-web-docs > files > en-us > glossary > developer_tools > index.md Path: mdn-web-docs > files > en-us > glossary > developer_tools > index.md Path: mdn-web-docs > files > en-us > glossary > developer_tools > index.md --- title: Developer Tools slug: Glossary/Developer_Tools page-type: glossary-definition --- {{GlossarySidebar}} **Developer tools** (or "development tools" or short "DevTools") are programs that allow a developer to create, test and debug software. Current browsers provide integrated developer tools, which allow to inspect a website. They let users inspect and debug the page's {{Glossary("HTML")}}, {{Glossary("CSS")}}, and {{Glossary("JavaScript")}}, allow to inspect the network traffic it causes, make it possible to measure its performance, and much more. ## See also - [Web development tools](https://en.wikipedia.org/wiki/Web_development_tools) on Wikipedia - [Firefox Developer Tools](https://firefox-source-docs.mozilla.org/devtools-user/index.html) on MDN - [Firebug](https://getfirebug.com/) (former developer tool for Firefox) - [Chrome DevTools](https://developer.chrome.com/docs/devtools/) on chrome.com - [Safari Developer Tools](https://support.apple.com/en-gb/guide/safari-developer/dev073038698/mac) on apple.com - [Edge Dev Tools](https://learn.microsoft.com/en-us/archive/microsoft-edge/legacy/developer/) on microsoft.com