Path: mdn-web-docs > files > en-us > glossary > distributed_denial_of_service > index.md

Path: mdn-web-docs > files > en-us > glossary > distributed_denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > distributed_denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > distributed_denial_of_service > index.md Path: mdn-web-docs > files > en-us > glossary > distributed_denial_of_service > index.md --- title: Distributed Denial of Service (DDoS) slug: Glossary/Distributed_Denial_of_Service page-type: glossary-definition --- {{GlossarySidebar}} **Distributed Denial-of-Service** (DDoS) is a type of {{Glossary("Denial of Service", "DoS")}} attack in which many compromised systems are made to attack a single target, in order to swamp server resources and block legitimate users. Normally many persons, using many bots, attack high-profile Web {{Glossary("server", "servers")}} like banks or credit-card payment gateways. DDoS concerns computer networks and CPU resource management. In a typical DDoS attack, the assailant begins by exploiting a vulnerability in one computer system and making it the DDoS master. The attack master, also known as the botmaster, identifies and infects other vulnerable systems with malware. Eventually, the assailant instructs the controlled machines to launch an attack against a specified target. There are two types of DDoS attacks: a network-centric attack (which overloads a service by using up bandwidth) and an application-layer attack (which overloads a service or database with application calls). The overflow of data to the target causes saturation in the target machine so that it cannot respond or responds very slowly to legitimate traffic (hence the name "denial of service"). The infected computers' owners normally don't know that their computers have been compromised, and they also suffer loss of service. A computer under an intruder's control is called a zombie or bot. A network of co-infected computers is known as a botnet or a zombie army. Both Kaspersky Labs and Symantec have identified botnets not spam, viruses, or worms as the biggest threat to Internet security. The United States Computer Emergency Readiness Team (US-CERT) defines symptoms of denial-of-service attacks to include: - Unusually slow network performance (opening files or accessing websites) - Unavailability of a particular website - Inability to access any website - Dramatic increase in the number of spam emails received (this type of DoS attack is considered an email bomb) - Disconnection of a wireless or wired internet connection - Longterm denial of access to the Web or any internet services ## See also - [Distributed DoS attack](https://en.wikipedia.org/wiki/Denial-of-service_attack#Distributed_DoS) on Wikipedia