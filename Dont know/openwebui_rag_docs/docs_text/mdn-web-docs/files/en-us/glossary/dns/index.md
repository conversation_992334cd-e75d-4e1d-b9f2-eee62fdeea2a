Path: mdn-web-docs > files > en-us > glossary > dns > index.md

Path: mdn-web-docs > files > en-us > glossary > dns > index.md Path: mdn-web-docs > files > en-us > glossary > dns > index.md Path: mdn-web-docs > files > en-us > glossary > dns > index.md Path: mdn-web-docs > files > en-us > glossary > dns > index.md --- title: DNS slug: Glossary/DNS page-type: glossary-definition --- {{GlossarySidebar}} **DNS** (Domain Name System) is a hierarchical and decentralized naming system for Internet connected resources. DNS maintains a list of {{Glossary("domain name","domain names")}} along with the resources, such as IP addresses, that are associated with them. The most prominent function of DNS is the translation of human-friendly domain names (such as mozilla.org) to a numeric {{Glossary("IP address")}} (such as ***********); this process of mapping a domain name to the appropriate IP address is known as a **DNS lookup**. By contrast, a **reverse DNS lookup** (rDNS) is used to determine the domain name associated with an IP address. ## See also - [Understanding domain names](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_domain_name) - [Domain Name System](https://en.wikipedia.org/wiki/Domain_Name_System) on Wikipedia