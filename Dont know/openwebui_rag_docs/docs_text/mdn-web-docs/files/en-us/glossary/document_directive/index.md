Path: mdn-web-docs > files > en-us > glossary > document_directive > index.md

Path: mdn-web-docs > files > en-us > glossary > document_directive > index.md Path: mdn-web-docs > files > en-us > glossary > document_directive > index.md Path: mdn-web-docs > files > en-us > glossary > document_directive > index.md Path: mdn-web-docs > files > en-us > glossary > document_directive > index.md --- title: Document directive slug: Glossary/Document_directive page-type: glossary-definition --- {{GlossarySidebar}} In {{Glossary("CSP")}}, **document directives** are used in a {{HTTPHeader("Content-Security-Policy")}} header and govern the properties of a document or [worker](/en-US/docs/Web/API/Web_Workers_API) environment to which a policy applies. Document directives don't fall back to the {{CSP("default-src")}} directive. See [Document directives](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy#document_directives) for a complete list. ## See also - Related glossary terms: - {{Glossary("CSP")}} - {{Glossary("Reporting directive")}} - {{Glossary("Fetch directive")}} - {{Glossary("Navigation directive")}} - Reference - <https://www.w3.org/TR/CSP/#directives-document> - {{HTTPHeader("Content-Security-Policy/upgrade-insecure-requests", "upgrade-insecure-requests")}} - {{HTTPHeader("Content-Security-Policy/block-all-mixed-content", "block-all-mixed-content")}} - {{HTTPHeader("Content-Security-Policy")}}