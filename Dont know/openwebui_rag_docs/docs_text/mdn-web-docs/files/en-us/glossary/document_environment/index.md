Path: mdn-web-docs > files > en-us > glossary > document_environment > index.md

Path: mdn-web-docs > files > en-us > glossary > document_environment > index.md Path: mdn-web-docs > files > en-us > glossary > document_environment > index.md Path: mdn-web-docs > files > en-us > glossary > document_environment > index.md Path: mdn-web-docs > files > en-us > glossary > document_environment > index.md --- title: Document environment slug: Glossary/Document_environment page-type: glossary-definition --- {{GlossarySidebar}} When the JavaScript global environment is a window or an iframe, it is called a **document environment**. A global environment is an environment that doesn't have an outer environment. ## See also - [document environment](https://html.spec.whatwg.org/multipage/webappapis.html#document-environment) in the HTML specification