Path: mdn-web-docs > files > en-us > glossary > dom > index.md

Path: mdn-web-docs > files > en-us > glossary > dom > index.md Path: mdn-web-docs > files > en-us > glossary > dom > index.md Path: mdn-web-docs > files > en-us > glossary > dom > index.md Path: mdn-web-docs > files > en-us > glossary > dom > index.md --- title: DOM (Document Object Model) slug: Glossary/DOM page-type: glossary-definition --- {{GlossarySidebar}} The **DOM** (Document Object Model) is an {{glossary("API")}} that represents and interacts with any {{glossary("HTML")}} or {{glossary("XML")}}-based markup language document. The DOM is a document model loaded in the {{glossary("browser")}} and representing the document as a {{Glossary("Node/DOM","node")}} tree, or **DOM tree**, where each node represents part of the document (e.g., an {{Glossary("element")}}, text string, or comment). The DOM is one of the most-used {{Glossary("API")}}s on the {{glossary("World Wide Web","Web")}} because it allows code running in a browser to access and interact with every node in the document. Nodes can be created, moved, and changed. Event listeners can be added to nodes and triggered on the occurrence of a given event. ## See also - [The DOM documentation on MDN](/en-US/docs/Web/API/Document_Object_Model) - [The DOM Standard](https://dom.spec.whatwg.org/) - [Document Object Model](https://en.wikipedia.org/wiki/Document_Object_Model) on Wikipedia