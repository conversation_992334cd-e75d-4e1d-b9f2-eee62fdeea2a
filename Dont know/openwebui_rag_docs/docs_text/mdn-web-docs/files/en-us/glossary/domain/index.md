Path: mdn-web-docs > files > en-us > glossary > domain > index.md

Path: mdn-web-docs > files > en-us > glossary > domain > index.md Path: mdn-web-docs > files > en-us > glossary > domain > index.md Path: mdn-web-docs > files > en-us > glossary > domain > index.md Path: mdn-web-docs > files > en-us > glossary > domain > index.md --- title: Domain slug: Glossary/Domain page-type: glossary-definition --- {{GlossarySidebar}} A **domain** is an authority within the internet that controls its own resources. Its "domain name" is a way to address this authority as part of the hierarchy in a {{Glossary("URL")}} - usually the most memorable part of it, for instance a brand name. A fully qualified domain name (FQDN) contains all necessary parts to look up this authority by name unambiguously using the {{Glossary("DNS")}} system of the internet. For example, in "developer.mozilla.org": 1. "org" is called a [top-level domain](https://en.wikipedia.org/wiki/Top-level_domain). They are registered as an internet standard by the [IANA](https://en.wikipedia.org/wiki/Internet_Assigned_Numbers_Authority). Here, "org" means "organization" which is defined in a top-level _domain registry_. 2. "mozilla" is the domain. If you like to own a domain you have to register it with one of the many [registrars](https://en.wikipedia.org/wiki/Domain_name_registrar) who are allowed to do so with a top-level domain registry. 3. "developer" is a "sub-domain", something you as the owner of a domain may define yourself. Many owners choose to have a subdomain "www" to point to their {{Glossary("World_Wide_Web")}} resource, but that's not required (and has even fallen somewhat out of favor). ## See also - [Domain Name](https://en.wikipedia.org/wiki/Domain_name) on Wikipedia