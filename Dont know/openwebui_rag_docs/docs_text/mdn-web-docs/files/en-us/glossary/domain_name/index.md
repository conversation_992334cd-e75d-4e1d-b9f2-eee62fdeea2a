Path: mdn-web-docs > files > en-us > glossary > domain_name > index.md

Path: mdn-web-docs > files > en-us > glossary > domain_name > index.md Path: mdn-web-docs > files > en-us > glossary > domain_name > index.md Path: mdn-web-docs > files > en-us > glossary > domain_name > index.md Path: mdn-web-docs > files > en-us > glossary > domain_name > index.md --- title: Domain name slug: Glossary/Domain_name page-type: glossary-definition --- {{GlossarySidebar}} A **domain name** is a website's address on the {{Glossary("Internet")}}. Domain names are used in {{Glossary("URL","URLs")}} to identify which server a specific webpage belongs to. The domain name consists of a hierarchical sequence of names (labels) separated by periods (dots) and ending with an {{glossary("TLD","extension")}}. ## See also - [Domain name](https://en.wikipedia.org/wiki/Domain_name) on Wikipedia - [Understanding domain names](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_domain_name)