Path: mdn-web-docs > files > en-us > glossary > dsl > domain_specific_language > index.md

Path: mdn-web-docs > files > en-us > glossary > dsl > domain_specific_language > index.md Path: mdn-web-docs > files > en-us > glossary > dsl > domain_specific_language > index.md Path: mdn-web-docs > files > en-us > glossary > dsl > domain_specific_language > index.md Path: mdn-web-docs > files > en-us > glossary > dsl > domain_specific_language > index.md --- title: DSL (Domain-Specific Language) slug: Glossary/DSL/Domain_specific_language page-type: glossary-definition --- {{GlossarySidebar}} A **Domain-Specific Language (DSL)** is a type of computer language of limited scope, designed to address a particular problem within an application domain. Contrast DSLs with _general-purpose languages (GPLs)_, which are designed to address various problems across domains. ## See also - [Domain-specific language](https://en.wikipedia.org/wiki/Domain-specific_language) on Wikipedia - [DSL Guide](https://martinfowler.com/dsl.html) on martinfowler.com