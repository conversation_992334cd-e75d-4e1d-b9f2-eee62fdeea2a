Path: mdn-web-docs > files > en-us > glossary > dtmf > index.md

Path: mdn-web-docs > files > en-us > glossary > dtmf > index.md Path: mdn-web-docs > files > en-us > glossary > dtmf > index.md Path: mdn-web-docs > files > en-us > glossary > dtmf > index.md Path: mdn-web-docs > files > en-us > glossary > dtmf > index.md --- title: DTMF (Dual-Tone Multi-Frequency signaling) slug: Glossary/DTMF page-type: glossary-definition --- {{GlossarySidebar}} **Dual-Tone Multi-Frequency** (**DTMF**) signaling is a system by which audible tones are used to represent buttons being pressed on a keypad. Frequently referred to in the United States as "touch tone" (after the Touch-Tone trademark used when the transition from pulse dialing to DTMF began), DTMF makes it possible to signal the digits 0-9 as well as the letters "A" through "D" and the symbols "#" and "\*". Few telephone keypads include the letters, which are typically used for control signaling by the telephone network. Computers may make use of DTMF when dialing a modem, or when sending commands to a menu system for teleconferencing or other purposes. ## See also - [Dual-tone multi-frequency signaling](https://en.wikipedia.org/wiki/Dual-tone_multi-frequency_signaling) on Wikipedia - [Pulse dialing](https://en.wikipedia.org/wiki/Pulse_dialing) on Wikipedia