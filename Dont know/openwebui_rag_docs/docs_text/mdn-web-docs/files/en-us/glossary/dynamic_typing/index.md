Path: mdn-web-docs > files > en-us > glossary > dynamic_typing > index.md

Path: mdn-web-docs > files > en-us > glossary > dynamic_typing > index.md Path: mdn-web-docs > files > en-us > glossary > dynamic_typing > index.md Path: mdn-web-docs > files > en-us > glossary > dynamic_typing > index.md Path: mdn-web-docs > files > en-us > glossary > dynamic_typing > index.md --- title: Dynamic typing slug: Glossary/Dynamic_typing page-type: glossary-definition --- {{GlossarySidebar}} **Dynamically-typed languages** are those (like {{glossary("JavaScript")}}) where the interpreter assigns {{glossary("variable","variables")}} a {{glossary("type")}} at runtime based on the variable's {{glossary("value")}} at the time. ## See also - [JavaScript data types and data structures](/en-US/docs/Web/JavaScript/Guide/Data_structures) - [Type system](https://en.wikipedia.org/wiki/Type_system#DYNAMIC) on Wikipedia