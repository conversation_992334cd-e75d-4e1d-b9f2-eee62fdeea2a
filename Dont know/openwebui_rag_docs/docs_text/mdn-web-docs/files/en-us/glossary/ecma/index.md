Path: mdn-web-docs > files > en-us > glossary > ecma > index.md

Path: mdn-web-docs > files > en-us > glossary > ecma > index.md Path: mdn-web-docs > files > en-us > glossary > ecma > index.md Path: mdn-web-docs > files > en-us > glossary > ecma > index.md Path: mdn-web-docs > files > en-us > glossary > ecma > index.md --- title: ECMA slug: Glossary/ECMA page-type: glossary-definition --- {{GlossarySidebar}} **Ecma International** (formally _European Computer Manufacturers Association_) is a non-profit organization that develops standards in computer hardware, communications, and programming languages. On the web it is famous for being the organization which maintain [the ECMA-262 specification](https://ecma-international.org/publications-and-standards/standards/ecma-262/) (aka. {{Glossary("ECMAScript")}}) which is the core specification for the {{Glossary("JavaScript")}} language. ## See also - [Ecma International](https://en.wikipedia.org/wiki/Ecma_International) on Wikipedia - [The Ecma International website](https://ecma-international.org/)