Path: mdn-web-docs > files > en-us > glossary > element > index.md

Path: mdn-web-docs > files > en-us > glossary > element > index.md Path: mdn-web-docs > files > en-us > glossary > element > index.md Path: mdn-web-docs > files > en-us > glossary > element > index.md Path: mdn-web-docs > files > en-us > glossary > element > index.md --- title: Element slug: Glossary/Element page-type: glossary-definition --- {{GlossarySidebar}} An **element** is a part of a webpage. In {{glossary("XML")}} and {{glossary("HTML")}}, an element may contain a data item or a chunk of text or an image, or perhaps nothing. A typical element includes an opening tag with some {{glossary("attribute", "attributes")}}, enclosed text content, and a closing tag. ![Example: in <p class="nice">Hello world!</p>, '<p class="nice">' is an opening tag, 'class="nice"' is an attribute and its value, 'Hello world!' is enclosed text content, and '</p>' is a closing tag.](anatomy-of-an-html-element.png) Elements and {{glossary("tag", "tags")}} are _not_ the same things. Tags begin or end an element in source code, whereas elements are part of the {{Glossary("DOM")}}, the document model for displaying the page in the {{glossary("browser")}}. ## See also - [Basic HTML Syntax](/en-US/docs/Learn_web_development/Core/Structuring_content/Basic_HTML_syntax) - [Defining custom elements](/en-US/docs/Web/API/Web_components/Using_custom_elements) - The {{domxref("Element")}} interface, representing an element in the DOM.