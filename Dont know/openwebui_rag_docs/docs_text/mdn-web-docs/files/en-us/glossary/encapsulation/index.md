Path: mdn-web-docs > files > en-us > glossary > encapsulation > index.md

Path: mdn-web-docs > files > en-us > glossary > encapsulation > index.md Path: mdn-web-docs > files > en-us > glossary > encapsulation > index.md Path: mdn-web-docs > files > en-us > glossary > encapsulation > index.md Path: mdn-web-docs > files > en-us > glossary > encapsulation > index.md --- title: Encapsulation slug: Glossary/Encapsulation page-type: glossary-definition --- {{GlossarySidebar}} Encapsulation is the packing of data and {{glossary("function","functions")}} into one component (for example, a {{glossary("class")}}) and then controlling access to that component to make a "blackbox" out of the {{glossary("object")}}. Because of this, a user of that class only needs to know its interface (that is, the data and functions exposed outside the class), not the hidden implementation. ## See also - [Encapsulation](<https://en.wikipedia.org/wiki/Encapsulation_(object-oriented_programming)>) on Wikipedia