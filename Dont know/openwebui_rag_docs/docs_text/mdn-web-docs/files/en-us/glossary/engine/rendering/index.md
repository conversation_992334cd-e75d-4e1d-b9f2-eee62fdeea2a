Path: mdn-web-docs > files > en-us > glossary > engine > rendering > index.md

Path: mdn-web-docs > files > en-us > glossary > engine > rendering > index.md Path: mdn-web-docs > files > en-us > glossary > engine > rendering > index.md Path: mdn-web-docs > files > en-us > glossary > engine > rendering > index.md Path: mdn-web-docs > files > en-us > glossary > engine > rendering > index.md --- title: Rendering engine slug: Glossary/Engine/Rendering page-type: glossary-definition --- {{GlossarySidebar}} **Rendering engines** (also known as layout engines or browser engines) are part of a {{glossary("Browser", "web browser")}} that transforms {{glossary("HTML")}}, {{glossary("CSS")}}, and other resources of a web page into a visual representation on a screen. Common rendering engines include: - {{glossary("Blink")}} - {{glossary("Gecko")}} - {{glossary("WebKit")}} - {{glossary("Trident")}} ## See also - [Browser engine](https://en.wikipedia.org/wiki/Browser_engine) on Wikipedia - Related glossary terms: - {{Glossary("Engine")}} - {{Glossary("Browser")}}