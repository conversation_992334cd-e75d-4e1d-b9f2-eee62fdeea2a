Path: mdn-web-docs > files > en-us > glossary > entity > index.md

Path: mdn-web-docs > files > en-us > glossary > entity > index.md Path: mdn-web-docs > files > en-us > glossary > entity > index.md Path: mdn-web-docs > files > en-us > glossary > entity > index.md Path: mdn-web-docs > files > en-us > glossary > entity > index.md --- title: Entity slug: Glossary/Entity page-type: glossary-definition --- {{GlossarySidebar}} Entity is a term from the Standard Generalized Markup Language (SGML), which refers to a reference to information that can be defined once and used throughout a document. The term "HTML Entity" is used as a synonym for a {{glossary("character reference")}} a pattern of characters that can represent another character in the HTML. For example, `&lt;` is used to represent the less-than symbol (`<`) in HTML content.