Path: mdn-web-docs > files > en-us > glossary > event > index.md

Path: mdn-web-docs > files > en-us > glossary > event > index.md Path: mdn-web-docs > files > en-us > glossary > event > index.md Path: mdn-web-docs > files > en-us > glossary > event > index.md Path: mdn-web-docs > files > en-us > glossary > event > index.md --- title: Event slug: Glossary/Event page-type: glossary-definition --- {{GlossarySidebar}} Events are assets generated by {{Glossary("DOM")}} elements, which can be manipulated by a JavaScript code. ## See also - [Event documentation on MDN](/en-US/docs/Web/API/Event) - [Official website](https://www.w3.org/TR/DOM-Level-2-Events/events.html) - [DOM Events](https://en.wikipedia.org/wiki/DOM_Events) on Wikipedia