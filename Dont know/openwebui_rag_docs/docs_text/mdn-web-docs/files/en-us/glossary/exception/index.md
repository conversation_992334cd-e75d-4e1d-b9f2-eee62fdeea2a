Path: mdn-web-docs > files > en-us > glossary > exception > index.md

Path: mdn-web-docs > files > en-us > glossary > exception > index.md Path: mdn-web-docs > files > en-us > glossary > exception > index.md Path: mdn-web-docs > files > en-us > glossary > exception > index.md Path: mdn-web-docs > files > en-us > glossary > exception > index.md --- title: Exception slug: Glossary/Exception page-type: glossary-definition --- {{GlossarySidebar}} An **exception** is a condition that interrupts normal code execution. In JavaScript {{glossary("syntax error", "syntax errors")}} are a very common source of exceptions. ## See also - [Exception handling](https://en.wikipedia.org/wiki/Exception_handling) on Wikipedia