Path: mdn-web-docs > files > en-us > glossary > firewall > index.md

Path: mdn-web-docs > files > en-us > glossary > firewall > index.md Path: mdn-web-docs > files > en-us > glossary > firewall > index.md Path: mdn-web-docs > files > en-us > glossary > firewall > index.md Path: mdn-web-docs > files > en-us > glossary > firewall > index.md --- title: Firewall slug: Glossary/Firewall page-type: glossary-definition --- {{GlossarySidebar}} A **firewall** is a system that filters network traffic. It can either let it pass or block it, according to some specified rules. For example, it can block incoming connections aimed at a certain port or outgoing connections to a certain IP address. Firewalls can be as simple as a single piece of software, or more complex, like a dedicated machine whose only function is to act as a firewall. ## See also - [Firewall (computing)](<https://en.wikipedia.org/wiki/Firewall_(computing)>) on Wikipedia