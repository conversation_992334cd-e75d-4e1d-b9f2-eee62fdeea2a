Path: mdn-web-docs > files > en-us > glossary > first_cpu_idle > index.md

Path: mdn-web-docs > files > en-us > glossary > first_cpu_idle > index.md Path: mdn-web-docs > files > en-us > glossary > first_cpu_idle > index.md Path: mdn-web-docs > files > en-us > glossary > first_cpu_idle > index.md Path: mdn-web-docs > files > en-us > glossary > first_cpu_idle > index.md --- title: First CPU idle slug: Glossary/First_CPU_idle page-type: glossary-definition --- {{GlossarySidebar}} **First CPU Idle** measures when a page is minimally interactive, or when the window is quiet enough to handle user input. It is a non-standard Google web performance metric. Generally, it occurs when most, but not necessarily all visible UI elements are interactive, and the user interface responds, on average, to most user input within 50ms. ## See also - Metrics which replaced First CPU Idle: - {{Glossary("Time to interactive")}} - [Total blocking time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)