Path: mdn-web-docs > files > en-us > glossary > flex > index.md

Path: mdn-web-docs > files > en-us > glossary > flex > index.md Path: mdn-web-docs > files > en-us > glossary > flex > index.md Path: mdn-web-docs > files > en-us > glossary > flex > index.md Path: mdn-web-docs > files > en-us > glossary > flex > index.md --- title: Flex slug: Glossary/Flex page-type: glossary-definition --- {{GlossarySidebar}} `flex` is a value of the CSS {{cssxref("display")}} property. Along with `inline-flex`, it causes the element it applies to become a {{glossary("flex container")}}, and the element's children to each become a {{glossary("flex item")}}. The items then participate in flex layout, and all of the properties defined in the [CSS flexible box layout module](/en-US/docs/Web/CSS/CSS_flexible_box_layout) may be applied. There is also a {{cssxref("flex")}} property, which is a shorthand for the flexbox properties {{cssxref("flex-grow")}}, {{cssxref("flex-shrink")}} and {{cssxref("flex-basis")}}. This property is only applicable to flex containers. In addition [`<flex>`](/en-US/docs/Web/CSS/flex_value) can refer to a [flexible length](/en-US/docs/Web/CSS/flex_value) in CSS Grid Layout. ### Related CSS properties - {{cssxref("align-content")}} - {{cssxref("align-items")}} - {{cssxref("align-self")}} - {{cssxref("flex")}} - {{cssxref("flex-basis")}} - {{cssxref("flex-direction")}} - {{cssxref("flex-flow")}} - {{cssxref("flex-grow")}} - {{cssxref("flex-shrink")}} - {{cssxref("flex-wrap")}} - {{cssxref("gap")}} - {{cssxref("justify-content")}} - {{cssxref("order")}} - {{cssxref("place-items")}} - {{cssxref("place-self")}} ## See also - [Basic concepts of flexbox](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Basic_concepts_of_flexbox) - [Relationship of flexbox to other layout methods](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Relationship_of_flexbox_to_other_layout_methods) - [Aligning items in a flex container](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Aligning_items_in_a_flex_container) - [Ordering flex items](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Ordering_flex_items) - [Controlling ratios of flex items along the main axis](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Controlling_ratios_of_flex_items_along_the_main_axis) - [Mastering wrapping of flex items](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Mastering_wrapping_of_flex_items) - [Typical use cases of flexbox](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Typical_use_cases_of_flexbox)