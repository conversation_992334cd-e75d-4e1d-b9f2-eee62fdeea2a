Path: mdn-web-docs > files > en-us > glossary > flex_item > index.md

Path: mdn-web-docs > files > en-us > glossary > flex_item > index.md Path: mdn-web-docs > files > en-us > glossary > flex_item > index.md Path: mdn-web-docs > files > en-us > glossary > flex_item > index.md Path: mdn-web-docs > files > en-us > glossary > flex_item > index.md --- title: Flex Item slug: Glossary/Flex_Item page-type: glossary-definition --- {{GlossarySidebar}} The direct children of a {{glossary("Flex Container")}} (elements with `display: flex` or `display: inline-flex` set on them) become **flex items**. Continuous runs of text inside flex containers will also become flex items. ## See also ### Property reference - {{cssxref("align-self")}} - {{cssxref("flex-basis")}} - {{cssxref("flex-grow")}} - {{cssxref("flex-shrink")}} - {{cssxref("order")}} ### Further reading - [Basic concepts of flexbox](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Basic_concepts_of_flexbox) - [Ordering flex items](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Ordering_flex_items) - [Controlling ratios of flex items along the main axis](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Controlling_ratios_of_flex_items_along_the_main_axis)