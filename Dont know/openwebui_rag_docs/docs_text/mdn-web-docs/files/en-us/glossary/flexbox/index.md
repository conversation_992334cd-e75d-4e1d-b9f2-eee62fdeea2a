Path: mdn-web-docs > files > en-us > glossary > flexbox > index.md

Path: mdn-web-docs > files > en-us > glossary > flexbox > index.md Path: mdn-web-docs > files > en-us > glossary > flexbox > index.md Path: mdn-web-docs > files > en-us > glossary > flexbox > index.md Path: mdn-web-docs > files > en-us > glossary > flexbox > index.md --- title: Flexbox slug: Glossary/Flexbox page-type: glossary-definition --- {{GlossarySidebar}} Flexbox is the commonly-used name for the [CSS flexible box layout module](/en-US/docs/Web/CSS/CSS_flexible_box_layout), a layout model for displaying items in a single dimension as a row or as a column. In the specification, flexbox is described as a layout model for user interface design. The key feature of flexbox is that items in a flex layout can grow and shrink. Space can be assigned to the items themselves, or distributed between or around the items. Flexbox also enables alignment of items on the main or cross axis, thus providing a high level of control over the size and alignment of a group of items. ## See also ### Property reference - {{cssxref("align-content")}} - {{cssxref("align-items")}} - {{cssxref("align-self")}} - {{cssxref("flex")}} - {{cssxref("flex-basis")}} - {{cssxref("flex-direction")}} - {{cssxref("flex-flow")}} - {{cssxref("flex-grow")}} - {{cssxref("flex-shrink")}} - {{cssxref("flex-wrap")}} - {{cssxref("justify-content")}} - {{cssxref("order")}} ### Further reading - CSS flexbox guides: - [Basic concepts of flexbox](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Basic_concepts_of_flexbox) - [Relationship of flexbox to other layout methods](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Relationship_of_flexbox_to_other_layout_methods) - [Aligning items in a flex container](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Aligning_items_in_a_flex_container) - [Ordering flex items](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Ordering_flex_items) - [Controlling ratios of flex items along the main axis](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Controlling_ratios_of_flex_items_along_the_main_axis) - [Mastering wrapping of flex items](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Mastering_wrapping_of_flex_items) - [Typical use cases of flexbox](/en-US/docs/Web/CSS/CSS_flexible_box_layout/Typical_use_cases_of_flexbox) - _[CSS Flexible Box Layout Module Level 1 Specification](https://www.w3.org/TR/css-flexbox-1/)_