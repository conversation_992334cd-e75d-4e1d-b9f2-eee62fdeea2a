Path: mdn-web-docs > files > en-us > glossary > forbidden_response_header_name > index.md

Path: mdn-web-docs > files > en-us > glossary > forbidden_response_header_name > index.md Path: mdn-web-docs > files > en-us > glossary > forbidden_response_header_name > index.md Path: mdn-web-docs > files > en-us > glossary > forbidden_response_header_name > index.md Path: mdn-web-docs > files > en-us > glossary > forbidden_response_header_name > index.md --- title: Forbidden response header name slug: Glossary/Forbidden_response_header_name page-type: glossary-definition --- {{GlossarySidebar}} A _forbidden response header name_ is an [HTTP header](/en-US/docs/Web/HTTP/Reference/Headers) name (`Set-Cookie`) that cannot be modified programmatically. ## See also - [Fetch specification: forbidden response-header name](https://fetch.spec.whatwg.org/#forbidden-response-header-name) - Related glossary terms: - {{Glossary("Forbidden request header")}}