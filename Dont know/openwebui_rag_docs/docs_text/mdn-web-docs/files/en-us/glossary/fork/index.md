Path: mdn-web-docs > files > en-us > glossary > fork > index.md

Path: mdn-web-docs > files > en-us > glossary > fork > index.md Path: mdn-web-docs > files > en-us > glossary > fork > index.md Path: mdn-web-docs > files > en-us > glossary > fork > index.md Path: mdn-web-docs > files > en-us > glossary > fork > index.md --- title: Fork slug: Glossary/Fork page-type: glossary-definition --- {{GlossarySidebar}} A fork is a copy of an existing software project at some point to add someone's own modifications to the project. Basically, if the license of the original software allows, you can copy the code to develop your own version of it, with your own additions, which will be a "fork". Forks are often seen in free and open source software development. This is now a more popular term thanks to contribution model using Git (and/or the GitHub platform). ## See also - [Fork](<https://en.wikipedia.org/wiki/Fork_(software_development)>) on Wikipedia - [How to fork a GitHub repo](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/fork-a-repo) (fork as in a Git context) - Various "well-known" forks - [Linux distributions](https://upload.wikimedia.org/wikipedia/commons/1/1b/Linux_Distribution_Timeline.svg) - [Node.js and io.js (which have been merged together back)](https://nodejs.org/en/blog/announcements/foundation-v4-announce/) - [LibreOffice, a fork of OpenOffice](https://www.libreoffice.org/about-us/who-are-we/)