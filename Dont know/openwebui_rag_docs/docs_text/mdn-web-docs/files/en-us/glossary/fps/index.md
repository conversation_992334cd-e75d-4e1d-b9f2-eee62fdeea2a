Path: mdn-web-docs > files > en-us > glossary > fps > index.md

Path: mdn-web-docs > files > en-us > glossary > fps > index.md Path: mdn-web-docs > files > en-us > glossary > fps > index.md Path: mdn-web-docs > files > en-us > glossary > fps > index.md Path: mdn-web-docs > files > en-us > glossary > fps > index.md --- title: Frame rate (FPS) slug: Glossary/FPS page-type: glossary-definition --- {{GlossarySidebar}} A **frame rate** is the speed at which the browser is able to recalculate, layout and paint content to the display. The **frames per second**, or **fps**, is how many frames can be repainted in one second. The goal frame rate for in website computer graphics is 60fps. Movies generally have a frame rate of 24 fps. They are able to have fewer frames per second because the illusion of life is created with motion blurs. When moving on a computer screen there are no motion blurs (unless you are animating an image [sprite](/en-US/docs/Web/CSS/CSS_images/Implementing_image_sprites_in_CSS) with motion blurs). ## See also - [Frame rate](https://en.wikipedia.org/wiki/Frame_rate) (Wikipedia)