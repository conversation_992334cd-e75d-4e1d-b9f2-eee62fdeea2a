Path: mdn-web-docs > files > en-us > glossary > fragmentainer > index.md

Path: mdn-web-docs > files > en-us > glossary > fragmentainer > index.md Path: mdn-web-docs > files > en-us > glossary > fragmentainer > index.md Path: mdn-web-docs > files > en-us > glossary > fragmentainer > index.md Path: mdn-web-docs > files > en-us > glossary > fragmentainer > index.md --- title: Fragmentainer slug: Glossary/Fragmentainer page-type: glossary-definition --- {{GlossarySidebar}} A fragmentainer is defined in the [CSS Fragmentation Specification](https://www.w3.org/TR/css-break-3/) as follows: > A box such as a page box, column box, or region that contains a portion (or all) of a fragmented flow. Fragmentainers can be pre-defined, or generated as needed. When breakable content would overflow a fragmentainer in the block dimension, it breaks into the next container in its fragmentation context instead. Fragmented contexts are found in CSS Paged Media, where the fragmentainer would be the box which defines a page. In Multiple-column Layout the fragmentainer is the column box created when columns are defined on a multicol container. In CSS Regions each Region is a fragmentainer.