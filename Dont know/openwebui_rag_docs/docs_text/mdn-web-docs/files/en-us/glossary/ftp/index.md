Path: mdn-web-docs > files > en-us > glossary > ftp > index.md

Path: mdn-web-docs > files > en-us > glossary > ftp > index.md Path: mdn-web-docs > files > en-us > glossary > ftp > index.md Path: mdn-web-docs > files > en-us > glossary > ftp > index.md Path: mdn-web-docs > files > en-us > glossary > ftp > index.md --- title: FTP slug: Glossary/FTP page-type: glossary-definition --- {{GlossarySidebar}} **FTP** (File Transfer Protocol) is an insecure {{glossary("protocol")}} for transferring files from one {{glossary("host")}} to another over the Internet. For many years it was the defacto standard way of transferring files, but as it is inherently insecure, it is no longer supported by many hosting accounts. Instead you should use SFTP (a secure, encrypted version of FTP) or another secure method for transferring files like Rsync over SSH. ## See also - [Be<PERSON><PERSON>'s guide to uploading files via FTP](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Upload_files_to_a_web_server) - [FTP](https://en.wikipedia.org/wiki/File_Transfer_Protocol) on Wikipedia