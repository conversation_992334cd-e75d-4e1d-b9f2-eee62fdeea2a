Path: mdn-web-docs > files > en-us > glossary > fuzzing > index.md

Path: mdn-web-docs > files > en-us > glossary > fuzzing > index.md Path: mdn-web-docs > files > en-us > glossary > fuzzing > index.md Path: mdn-web-docs > files > en-us > glossary > fuzzing > index.md Path: mdn-web-docs > files > en-us > glossary > fuzzing > index.md --- title: Fuzz testing slug: Glossary/Fuzzing page-type: glossary-definition --- {{GlossarySidebar}} **Fuzzing** is a technique for testing software using automated tools to provide invalid or unexpected input to a program or function in a program, then checking the results to see if the program crashes or otherwise acts inappropriately. This is an important way to ensure that software is stable, reliable, and secure. - [Wikipedia: Fuzz testing](https://en.wikipedia.org/wiki/Fuzz_testing)