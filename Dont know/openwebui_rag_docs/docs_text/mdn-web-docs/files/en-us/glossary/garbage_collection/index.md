Path: mdn-web-docs > files > en-us > glossary > garbage_collection > index.md

Path: mdn-web-docs > files > en-us > glossary > garbage_collection > index.md Path: mdn-web-docs > files > en-us > glossary > garbage_collection > index.md Path: mdn-web-docs > files > en-us > glossary > garbage_collection > index.md Path: mdn-web-docs > files > en-us > glossary > garbage_collection > index.md --- title: Garbage collection slug: Glossary/Garbage_collection page-type: glossary-definition --- {{GlossarySidebar}} **[Garbage collection](/en-US/docs/Web/JavaScript/Guide/Memory_management#garbage_collection)** is a term used in {{Glossary("computer programming")}} to describe the process of finding and deleting {{Glossary("object", "objects")}} which are no longer being {{Glossary("object reference", "referenced")}} by other objects. In other words, garbage collection is the process of removing any objects which are not being used by any other objects. Often abbreviated "GC," garbage collection is a fundamental component of the [memory management](/en-US/docs/Web/JavaScript/Guide/Memory_management) system used by {{Glossary("JavaScript")}}. ## See also - [Memory management](https://en.wikipedia.org/wiki/Memory_management) on Wikipedia - [Garbage collection (computer science)](<https://en.wikipedia.org/wiki/Garbage_collection_(computer_science)>) on Wikipedia - [Garbage collection](/en-US/docs/Web/JavaScript/Guide/Memory_management#garbage_collection) in the MDN JavaScript guide. - [Memory management in JavaScript](/en-US/docs/Web/JavaScript/Guide/Memory_management)