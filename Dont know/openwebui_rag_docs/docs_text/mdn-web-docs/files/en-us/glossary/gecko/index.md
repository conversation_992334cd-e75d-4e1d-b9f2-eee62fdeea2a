Path: mdn-web-docs > files > en-us > glossary > gecko > index.md

Path: mdn-web-docs > files > en-us > glossary > gecko > index.md Path: mdn-web-docs > files > en-us > glossary > gecko > index.md Path: mdn-web-docs > files > en-us > glossary > gecko > index.md Path: mdn-web-docs > files > en-us > glossary > gecko > index.md --- title: Gecko slug: Glossary/Gecko page-type: glossary-definition --- {{GlossarySidebar}} **Gecko** is the layout engine developed by the Mozilla Project and used in many apps/devices, including {{glossary("Mozilla Firefox","Firefox")}} and {{glossary("Firefox OS")}}. Web {{glossary("browser","browsers")}} need software called a layout engine to interpret {{glossary("HTML")}}, {{glossary("CSS")}}, {{glossary("JavaScript")}}, and embedded content (like images) and draw everything to your screen. Besides this, Gecko makes sure associated {{glossary("API","APIs")}} work well on every operating system Gecko supports, and that appropriate APIs are exposed only to relevant support targets. This means that Gecko includes, among other things, a networking stack, graphics stack, layout engine, a JavaScript virtual machine, and porting layers. Since all Firefox OS apps are Web apps, Firefox OS uses Gecko as its app runtime as well. ## See also - [Gecko](<https://en.wikipedia.org/wiki/Gecko_(software)>) on Wikipedia