Path: mdn-web-docs > files > en-us > glossary > general_header > index.md

Path: mdn-web-docs > files > en-us > glossary > general_header > index.md Path: mdn-web-docs > files > en-us > glossary > general_header > index.md Path: mdn-web-docs > files > en-us > glossary > general_header > index.md Path: mdn-web-docs > files > en-us > glossary > general_header > index.md --- title: General header slug: Glossary/General_header page-type: glossary-definition --- {{GlossarySidebar}} **General header** is an outdated term used to refer to an {{glossary('HTTP_header', 'HTTP header')}} that can be used in both request and response messages, but which doesn't apply to the content itself (a header that applied to the content was called an {{glossary("entity header")}}). Depending on the context they are used in, general headers might either be {{glossary("Response header", "response")}} or {{glossary("request header", "request headers")}} (e.g., {{HTT<PERSON>header("Cache-Control")}}). > [!NOTE] > Current versions of the HTTP/1.1 specification do not specifically categorize headers as "general headers". These are now simply referred to as {{glossary("Response header", "response")}} or {{glossary("request header", "request headers")}} depending on context.