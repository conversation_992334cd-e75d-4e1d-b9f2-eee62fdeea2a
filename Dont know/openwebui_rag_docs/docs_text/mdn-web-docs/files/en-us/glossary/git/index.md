Path: mdn-web-docs > files > en-us > glossary > git > index.md

Path: mdn-web-docs > files > en-us > glossary > git > index.md Path: mdn-web-docs > files > en-us > glossary > git > index.md Path: mdn-web-docs > files > en-us > glossary > git > index.md Path: mdn-web-docs > files > en-us > glossary > git > index.md --- title: Git slug: Glossary/Git page-type: glossary-definition --- {{GlossarySidebar}} **Git** is a free, open-source, distributed Source Code Management ({{Glossary("SCM")}}) system. It facilitates handling code bases with distributed development teams. What sets it apart from previous SCM systems is the ability to do common operations (branching, committing, etc.) on your local development machine, without having to change the master repository or even having write access to it. ## See also - [Official website with documentation](https://git-scm.com/) - [GitHub](https://github.com/), a Git-based graphical project host