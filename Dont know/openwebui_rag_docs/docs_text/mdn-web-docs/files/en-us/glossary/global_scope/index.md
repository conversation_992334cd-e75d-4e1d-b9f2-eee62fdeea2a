Path: mdn-web-docs > files > en-us > glossary > global_scope > index.md

Path: mdn-web-docs > files > en-us > glossary > global_scope > index.md Path: mdn-web-docs > files > en-us > glossary > global_scope > index.md Path: mdn-web-docs > files > en-us > glossary > global_scope > index.md Path: mdn-web-docs > files > en-us > glossary > global_scope > index.md --- title: Global scope slug: Glossary/Global_scope page-type: glossary-definition --- {{GlossarySidebar}} In a programming environment, the _global scope_ is the {{glossary("scope")}} that contains, and is visible in, all other scopes. In client-side JavaScript, the global scope is generally the web page inside which all the code is being executed. ## See also - [Introduction to variable scope in JavaScript](/en-US/docs/Web/JavaScript/Guide/Grammar_and_types#variable_scope) - [Scope](<https://en.wikipedia.org/wiki/Scope_(computer_science)>) on Wikipedia