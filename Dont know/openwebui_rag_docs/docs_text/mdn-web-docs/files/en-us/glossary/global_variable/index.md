Path: mdn-web-docs > files > en-us > glossary > global_variable > index.md

Path: mdn-web-docs > files > en-us > glossary > global_variable > index.md Path: mdn-web-docs > files > en-us > glossary > global_variable > index.md Path: mdn-web-docs > files > en-us > glossary > global_variable > index.md Path: mdn-web-docs > files > en-us > glossary > global_variable > index.md --- title: Global variable slug: Glossary/Global_variable page-type: glossary-definition --- {{GlossarySidebar}} A global variable is a {{glossary("variable")}} that is declared in the {{glossary("global scope")}} in other words, a variable that is visible from all other scopes. In JavaScript it is a {{glossary("property")}} of the {{glossary("global object")}}. ## See also - [Global variable](https://en.wikipedia.org/wiki/Global_variable) on Wikipedia