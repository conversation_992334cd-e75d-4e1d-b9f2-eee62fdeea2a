Path: mdn-web-docs > files > en-us > glossary > grid_column > index.md

Path: mdn-web-docs > files > en-us > glossary > grid_column > index.md Path: mdn-web-docs > files > en-us > glossary > grid_column > index.md Path: mdn-web-docs > files > en-us > glossary > grid_column > index.md Path: mdn-web-docs > files > en-us > glossary > grid_column > index.md --- title: Grid Column slug: Glossary/Grid_Column page-type: glossary-definition --- {{GlossarySidebar}} A **grid column** is a vertical track in a [CSS grid layout](/en-US/docs/Web/CSS/CSS_grid_layout), and is the space between two vertical grid lines. It is defined by the {{cssxref("grid-template-columns")}} property or in the shorthand {{cssxref("grid")}} or {{cssxref("grid-template")}} properties. In addition, columns may be created in the _implicit grid_ when items are placed outside of columns created in the _explicit grid_. These columns will be auto-sized by default, or can have a size specified with the {{cssxref("grid-auto-columns")}} property. When working with alignment in [CSS grid layout](/en-US/docs/Web/CSS/CSS_grid_layout), the axis down which columns run is known as the _block, or column, axis_. ## See also ### Property reference - {{cssxref("grid-template-columns")}} - {{cssxref("grid-auto-columns")}} - {{cssxref("grid")}} - {{cssxref("grid-template")}} ### Further reading - [Basic concepts of grid layout](/en-US/docs/Web/CSS/CSS_grid_layout/Basic_concepts_of_grid_layout)