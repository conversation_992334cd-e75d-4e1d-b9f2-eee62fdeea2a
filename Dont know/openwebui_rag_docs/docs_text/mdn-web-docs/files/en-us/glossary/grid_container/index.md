Path: mdn-web-docs > files > en-us > glossary > grid_container > index.md

Path: mdn-web-docs > files > en-us > glossary > grid_container > index.md Path: mdn-web-docs > files > en-us > glossary > grid_container > index.md Path: mdn-web-docs > files > en-us > glossary > grid_container > index.md Path: mdn-web-docs > files > en-us > glossary > grid_container > index.md --- title: Grid container slug: Glossary/Grid_Container page-type: glossary-definition --- {{GlossarySidebar}} Using the value `grid` or `inline-grid` on an element turns it into a **grid container** using [CSS grid layout](/en-US/docs/Web/CSS/CSS_grid_layout), and any direct children of this element become grid items. When an element becomes a grid container it establishes a **grid formatting context**. The direct children can now lay themselves out on any explicit grid defined using {{cssxref("grid-template-columns")}} and {{cssxref("grid-template-rows")}}, or on the _implicit grid_ created when an item is placed outside of the _explicit grid_. ## See also ### Property reference - {{cssxref("grid-template-columns")}} - {{cssxref("grid-template-rows")}} - {{cssxref("grid-auto-columns")}} - {{cssxref("grid-auto-rows")}} - {{cssxref("grid")}} - {{cssxref("grid-template")}} ### Further reading - [Basic concepts of grid layout](/en-US/docs/Web/CSS/CSS_grid_layout/Basic_concepts_of_grid_layout)