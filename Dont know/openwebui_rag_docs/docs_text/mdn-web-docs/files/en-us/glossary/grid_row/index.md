Path: mdn-web-docs > files > en-us > glossary > grid_row > index.md

Path: mdn-web-docs > files > en-us > glossary > grid_row > index.md Path: mdn-web-docs > files > en-us > glossary > grid_row > index.md Path: mdn-web-docs > files > en-us > glossary > grid_row > index.md Path: mdn-web-docs > files > en-us > glossary > grid_row > index.md --- title: Grid Row slug: Glossary/Grid_Row page-type: glossary-definition --- {{GlossarySidebar}} A **grid row** is a horizontal track in a [CSS grid layout](/en-US/docs/Web/CSS/CSS_grid_layout), that is the space between two horizontal grid lines. It is defined by the {{cssxref("grid-template-rows")}} property or in the shorthand {{cssxref("grid")}} or {{cssxref("grid-template")}} properties. In addition, rows may be created in the _implicit grid_ when items are placed outside of rows created in the _explicit grid_. These rows will be auto sized by default, or can have a size specified with the {{cssxref("grid-auto-rows")}} property. When working with alignment in [CSS grid layout](/en-US/docs/Web/CSS/CSS_grid_layout), the axis along which rows run is known as the _inline, or row, axis_. ## See also ### Property reference - {{cssxref("grid-template-rows")}} - {{cssxref("grid-auto-rows")}} - {{cssxref("grid")}} - {{cssxref("grid-template")}} ### Further reading - [Basic concepts of grid layout](/en-US/docs/Web/CSS/CSS_grid_layout/Basic_concepts_of_grid_layout)