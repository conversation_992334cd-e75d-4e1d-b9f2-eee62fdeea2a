Path: mdn-web-docs > files > en-us > glossary > gzip_compression > index.md

Path: mdn-web-docs > files > en-us > glossary > gzip_compression > index.md Path: mdn-web-docs > files > en-us > glossary > gzip_compression > index.md Path: mdn-web-docs > files > en-us > glossary > gzip_compression > index.md Path: mdn-web-docs > files > en-us > glossary > gzip_compression > index.md --- title: gzip compression slug: Glossary/gzip_compression page-type: glossary-definition --- {{GlossarySidebar}} **gzip** is a file format used in file compression and decompression. It is based on [the Deflate algorithm](https://www.zlib.net/feldspar.html) which allows files to be made smaller, allowing for faster network transfers. gzip is commonly supported by web servers and modern browsers, meaning that servers can automatically compress files with gzip before sending them, and browsers can uncompress files upon receiving them. ## See also - Related glossary terms: - {{glossary("Lossless compression")}} - {{glossary("Lossy compression")}} - {{Glossary("Brotli compression")}} - {{Glossary("Zstandard compression")}} - [The gzip home page](https://www.gzip.org/) - [gzip on Wikipedia](https://en.wikipedia.org/wiki/Gzip)