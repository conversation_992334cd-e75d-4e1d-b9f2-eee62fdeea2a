Path: mdn-web-docs > files > en-us > glossary > head > index.md

Path: mdn-web-docs > files > en-us > glossary > head > index.md Path: mdn-web-docs > files > en-us > glossary > head > index.md Path: mdn-web-docs > files > en-us > glossary > head > index.md Path: mdn-web-docs > files > en-us > glossary > head > index.md --- title: Head slug: Glossary/Head page-type: glossary-definition --- {{GlossarySidebar}} The **Head** is the part of an {{glossary("HTML")}} document that contains {{glossary("metadata")}} about that document, such as author, description, and links to {{glossary("CSS")}} or {{glossary("JavaScript")}} files that should be applied to the HTML. ## See also - {{htmlelement("head")}} element reference on MDN - [What's in the head? Web page metadata](/en-US/docs/Learn_web_development/Core/Structuring_content/Webpage_metadata) on the MDN Learning Area