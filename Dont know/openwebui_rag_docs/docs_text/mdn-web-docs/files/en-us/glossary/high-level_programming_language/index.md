Path: mdn-web-docs > files > en-us > glossary > high-level_programming_language > index.md

Path: mdn-web-docs > files > en-us > glossary > high-level_programming_language > index.md Path: mdn-web-docs > files > en-us > glossary > high-level_programming_language > index.md Path: mdn-web-docs > files > en-us > glossary > high-level_programming_language > index.md Path: mdn-web-docs > files > en-us > glossary > high-level_programming_language > index.md --- title: High-level programming language slug: Glossary/High-level_programming_language page-type: glossary-definition --- {{GlossarySidebar}} A high-level programming language has a **significant abstraction** from the details of computer operation. It is designed to be easily understood by humans and for this reason they must be translated by another software. Unlike low-level programming languages, it may use natural language elements, or may automate (or even entirely hide) significant areas of computing systems, making the process of developing simpler and more understandable relative to a lower-level language. The amount of abstraction provided defines how "high-level" a programming language is. The idea of a language automatically translatable into machine code, but nearer to human logic, was introduced in computer science in the 1950s, especially thanks to the work of <PERSON>*<PERSON> (IBM), to whom it owes the first high-level language to have been widely circulated: Fortran. For this innovation <PERSON>us received the Turing prize.