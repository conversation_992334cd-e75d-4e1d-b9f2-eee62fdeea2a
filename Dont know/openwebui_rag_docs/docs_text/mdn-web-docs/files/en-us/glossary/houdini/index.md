Path: mdn-web-docs > files > en-us > glossary > houdini > index.md

Path: mdn-web-docs > files > en-us > glossary > houdini > index.md Path: mdn-web-docs > files > en-us > glossary > houdini > index.md Path: mdn-web-docs > files > en-us > glossary > houdini > index.md Path: mdn-web-docs > files > en-us > glossary > houdini > index.md --- title: <PERSON><PERSON><PERSON> slug: Glossary/Houdini page-type: glossary-definition --- {{GlossarySidebar}} Ho<PERSON><PERSON> is a set of low level APIs that give developers the power to extend CSS, providing the ability to hook into the styling and layout process of a browser's rendering engine. <PERSON><PERSON><PERSON> gives developers access to the [CSS Object Model](/en-US/docs/Web/API/CSS_Object_Model) ({{Glossary("CSSOM")}}), enabling developers to write code the browser can parse as CSS. The benefit of <PERSON><PERSON><PERSON> is that developers can create CSS features without waiting for web standards specifications to define them and without waiting for every browser to fully implement the features. While many of the features <PERSON><PERSON><PERSON> enables can be created with JavaScript, interacting directly with the CSSOM before JavaScript is enabled provides for faster parse times. Browsers create the CSSOM including layout, paint, and composite processes before applying any style updates found in scripts: layout, paint, and composite processes are repeated for updated JavaScript styles to be implemented. Houdini code doesn't wait for that first rendering cycle to be complete. Rather, it is included in that first cycle, creating renderable, understandable styles. ## See also - [Houdini APIs](/en-US/docs/Web/API/Houdini_APIs) - [CSSOM](/en-US/docs/Web/API/CSS_Object_Model) - [CSS Paint API](/en-US/docs/Web/API/CSS_Painting_API) - [CSS Typed OM](/en-US/docs/Web/API/CSS_Typed_OM_API)