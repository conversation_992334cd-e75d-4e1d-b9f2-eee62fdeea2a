Path: mdn-web-docs > files > en-us > glossary > hpkp > index.md

Path: mdn-web-docs > files > en-us > glossary > hpkp > index.md Path: mdn-web-docs > files > en-us > glossary > hpkp > index.md Path: mdn-web-docs > files > en-us > glossary > hpkp > index.md Path: mdn-web-docs > files > en-us > glossary > hpkp > index.md --- title: HPKP slug: Glossary/HPKP page-type: glossary-definition --- {{GlossarySidebar}} **HTTP Public Key Pinning** (**HPKP**) is an obsolete security feature that tells a web client to associate a specific cryptographic public key with a certain web server to decrease the risk of {{Glossary("MITM")}} attacks with forged certificates. ## See also - [RFC 7469](https://datatracker.ietf.org/doc/html/rfc7469) - Wikipedia: [HTTP Public Key Pinning](https://en.wikipedia.org/wiki/HTTP_Public_Key_Pinning)