Path: mdn-web-docs > files > en-us > glossary > html5 > index.md

Path: mdn-web-docs > files > en-us > glossary > html5 > index.md Path: mdn-web-docs > files > en-us > glossary > html5 > index.md Path: mdn-web-docs > files > en-us > glossary > html5 > index.md Path: mdn-web-docs > files > en-us > glossary > html5 > index.md --- title: HTML5 slug: Glossary/HTML5 page-type: glossary-definition --- {{GlossarySidebar}} The term HTML5 is essentially a buzzword that refers to a set of modern web technologies. This includes the {{Glossary("HTML")}} Living Standard, along with {{glossary("JavaScript")}} {{glossary("API","APIs")}} to enhance storage, multimedia, and hardware access. You may sometimes hear about "new HTML5 elements", or find HTML5 described as a new version of HTML. HTML5 was the successor to previous HTML versions and introduced new elements and capabilities to the language on top of the previous version, HTML 4.01, as well as improving or removing some existing functionality. However, as a Living Standard HTML now has no version. The up-to-date specification can be found at [html.spec.whatwg.org/](https://html.spec.whatwg.org/). Any modern site should use the [HTML doctype](/en-US/docs/MDN/Writing_guidelines/Code_style_guide/HTML#doctype) this will ensure that you are using the latest version of HTML. > [!NOTE] > Until 2019, the {{glossary("W3C")}} published a competing HTML5 standard with version numbers. Since [28 May 2019](https://www.w3.org/blog/news/archives/7753), the WHATWG Living Standard was announced by the W3C as the sole version of HTML. ## See also - [our HTML documentation](/en-US/docs/Web/HTML) - [HTML beginner's learning guides](/en-US/docs/Learn_web_development/Core/Structuring_content) - [Web APIs](/en-US/docs/Web/API)