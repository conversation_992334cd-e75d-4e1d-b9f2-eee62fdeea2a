Path: mdn-web-docs > files > en-us > glossary > html_color_codes > index.md

Path: mdn-web-docs > files > en-us > glossary > html_color_codes > index.md Path: mdn-web-docs > files > en-us > glossary > html_color_codes > index.md Path: mdn-web-docs > files > en-us > glossary > html_color_codes > index.md Path: mdn-web-docs > files > en-us > glossary > html_color_codes > index.md --- title: HTML color codes slug: Glossary/html_color_codes page-type: glossary-definition --- {{GlossarySidebar}} **HTML color codes** is a _defacto_ general term used to describe the earliest-available methods for specifying colors on web pages. This includes HTML color names such as `black`, `purple`, and `aqua`, and hexadecimal notations such as `#000000`, `#800080`, and `#00ffff`. These were originally defined in HTML specifications see for example the [HTML 3.2 color definitions](https://www.w3.org/TR/2018/SPSD-html32-20180315/#colors) of the original 16 HTML colors. It is no longer accurate to refer to colors on the web as "HTML color codes" or "HTML color names". Colors are now specified in the [CSS color module](/en-US/docs/Web/CSS/CSS_colors), and generally known as CSS colors or web colors. ## See also ### General knowledge [Web colors](https://en.wikipedia.org/wiki/Web_colors) on Wikipedia ### Technical reference To look up web colors on MDN, see our CSS {{cssxref("&lt;color&gt;")}} values reference documentation, or more specifically: - Color names: {{cssxref("&lt;named-color&gt;")}}. - Hexadecimal notations: {{cssxref("&lt;hex-color&gt;")}}. - Color functions: - [sRGB](/en-US/docs/Glossary/Color_space#rgb_color_spaces) color space: {{CSSXref("color_value/hsl", "hsl()")}}, {{CSSXref("color_value/hwb", "hwb()")}}, and {{CSSXref("color_value/rgb", "rgb()")}}. - [CIELAB](/en-US/docs/Glossary/Color_space#cielab_color_spaces) color space: {{CSSXref("color_value/lab", "lab()")}} and {{CSSXref("color_value/lch", "lch()")}}, {{CSSXref("color_value/oklab", "oklab()")}}, and {{CSSXref("color_value/oklch", "oklch()")}}. - Other color spaces: {{CSSXref("color_value/color", "color()")}}.