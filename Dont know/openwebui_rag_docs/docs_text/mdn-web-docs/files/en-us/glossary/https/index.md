Path: mdn-web-docs > files > en-us > glossary > https > index.md

Path: mdn-web-docs > files > en-us > glossary > https > index.md Path: mdn-web-docs > files > en-us > glossary > https > index.md Path: mdn-web-docs > files > en-us > glossary > https > index.md Path: mdn-web-docs > files > en-us > glossary > https > index.md --- title: HTTPS slug: Glossary/HTTPS page-type: glossary-definition --- {{GlossarySidebar}} **HTTPS** (**_HyperText Transfer Protocol Secure_**) is an encrypted version of the {{Glossary("HTTP")}} protocol. It uses {{Glossary("TLS")}} to encrypt all communication between a client and a server. This secure connection allows clients to safely exchange sensitive data with a server, such as when performing banking activities or online shopping. ## See also - [HTTPS](https://en.wikipedia.org/wiki/HTTPS) on Wikipedia - [Moving to HTTPS community guide](https://movingtohttps.com/) - [Secure Contexts](/en-US/docs/Web/Security/Secure_Contexts) - Related glossary terms: - {{glossary("HTTP")}} - {{glossary("TLS")}} - {{glossary("SSL")}}