Path: mdn-web-docs > files > en-us > glossary > ide > index.md

Path: mdn-web-docs > files > en-us > glossary > ide > index.md Path: mdn-web-docs > files > en-us > glossary > ide > index.md Path: mdn-web-docs > files > en-us > glossary > ide > index.md Path: mdn-web-docs > files > en-us > glossary > ide > index.md --- title: IDE slug: Glossary/IDE page-type: glossary-definition --- {{GlossarySidebar}} An Integrated Development Environment (**IDE**) or Interactive Development environment is a software application that provides comprehensive facilities to computer programmers for software development. An IDE normally consists of a source code editor, build automation tools and a debugger. ## See also - [IDE](https://en.wikipedia.org/wiki/Integrated_development_environment) on Wikipedia