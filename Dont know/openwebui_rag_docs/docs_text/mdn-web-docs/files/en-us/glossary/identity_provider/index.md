Path: mdn-web-docs > files > en-us > glossary > identity_provider > index.md

Path: mdn-web-docs > files > en-us > glossary > identity_provider > index.md Path: mdn-web-docs > files > en-us > glossary > identity_provider > index.md Path: mdn-web-docs > files > en-us > glossary > identity_provider > index.md Path: mdn-web-docs > files > en-us > glossary > identity_provider > index.md --- title: Identity provider (IdP) slug: Glossary/Identity_provider page-type: glossary-definition --- {{GlossarySidebar}} An **identity provider** (IdP) is an entity in a {{glossary("federated identity")}} system that manages a user's {{glossary("credential", "credentials")}} and can {{glossary("authentication", "authenticate")}} users. In federated identity systems, {{glossary("relying party", "relying parties")}}, that need to control access to a resource (for example, a website deciding whether to sign a user in) outsource the act of authenticating users to a third party, which they trust to make authentication decisions. These third parties are called identity providers. Examples of identity providers on the web include Google, Microsoft, and Facebook. This enables websites to allow users to sign in using the user's Google, Microsoft, or Facebook account. ## See also - Related glossary terms: - {{glossary("Federated identity")}} - {{glossary("Relying party")}} - [Federated Credential Management (FedCM) API](/en-US/docs/Web/API/FedCM_API)