Path: mdn-web-docs > files > en-us > glossary > indexeddb > index.md

Path: mdn-web-docs > files > en-us > glossary > indexeddb > index.md Path: mdn-web-docs > files > en-us > glossary > indexeddb > index.md Path: mdn-web-docs > files > en-us > glossary > indexeddb > index.md Path: mdn-web-docs > files > en-us > glossary > indexeddb > index.md --- title: IndexedDB slug: Glossary/IndexedDB page-type: glossary-definition --- {{GlossarySidebar}} IndexedDB is a Web {{glossary("API")}} for storing large data structures within browsers and indexing them for high-performance searching. Like an {{glossary("SQL")}}-based [RDBMS](https://en.wikipedia.org/wiki/Relational_database_management_system), IndexedDB is a transactional database system. However, it uses {{glossary("JavaScript")}} objects rather than fixed columns tables to store data. ## See also - The {{domxref('IndexedDB_API','IndexedDB API','',1)}} on MDN - [The W3C specification for IndexedDB](https://w3c.github.io/IndexedDB/)