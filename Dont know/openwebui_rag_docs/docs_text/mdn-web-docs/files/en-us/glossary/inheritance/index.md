Path: mdn-web-docs > files > en-us > glossary > inheritance > index.md

Path: mdn-web-docs > files > en-us > glossary > inheritance > index.md Path: mdn-web-docs > files > en-us > glossary > inheritance > index.md Path: mdn-web-docs > files > en-us > glossary > inheritance > index.md Path: mdn-web-docs > files > en-us > glossary > inheritance > index.md --- title: Inheritance slug: Glossary/Inheritance page-type: glossary-definition --- {{GlossarySidebar}} Inheritance is a major feature of {{glossary("OOP","object-oriented programming")}}. Data abstraction can be carried up several levels, that is, {{glossary("class","classes")}} can have superclasses and subclasses. As an app developer, you can choose which of the superclass's {{glossary("attribute","attributes")}} and {{glossary("method","methods")}} to keep and add your own, making class definition very flexible. Some languages let a class inherit from more than one parent (multiple inheritance). ## See also - [Inheritance and the prototype chain](/en-US/docs/Web/JavaScript/Guide/Inheritance_and_the_prototype_chain)