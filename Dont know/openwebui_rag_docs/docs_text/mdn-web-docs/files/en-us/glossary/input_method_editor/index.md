Path: mdn-web-docs > files > en-us > glossary > input_method_editor > index.md

Path: mdn-web-docs > files > en-us > glossary > input_method_editor > index.md Path: mdn-web-docs > files > en-us > glossary > input_method_editor > index.md Path: mdn-web-docs > files > en-us > glossary > input_method_editor > index.md Path: mdn-web-docs > files > en-us > glossary > input_method_editor > index.md --- title: Input method editor slug: Glossary/Input_method_editor page-type: glossary-definition --- {{GlossarySidebar}} An Input Method Editor (IME) is a program that provides a specialized user interface for text input. Input method editors are used in many situations: - To enter Chinese, Japanese, or Korean text using a Latin keyboard. - To enter Latin text using a numeric keypad. - To enter text on a touch screen using handwriting recognition. ## See also - [Input method](https://en.wikipedia.org/wiki/Input_method) - Related glossary terms: - {{Glossary("Internationalization", "Internationalization (I18N)")}}