Path: mdn-web-docs > files > en-us > glossary > internationalization > index.md

Path: mdn-web-docs > files > en-us > glossary > internationalization > index.md Path: mdn-web-docs > files > en-us > glossary > internationalization > index.md Path: mdn-web-docs > files > en-us > glossary > internationalization > index.md Path: mdn-web-docs > files > en-us > glossary > internationalization > index.md --- title: Internationalization (i18n) slug: Glossary/Internationalization page-type: glossary-definition --- {{GlossarySidebar}} **Internationalization**, often shortened to "i18n", is the practice of designing a system in such a way that it can easily be adapted for different target audiences, that may vary in region, language, or culture. The complementary process of adapting a system for a specific target audience is called {{glossary("Localization")}}. Among other things, internationalization covers adapting to differences in: - writing systems - units of measure (currency, C/ F, km/miles, etc.) - time and date formats - keyboard layouts The work of the [Unicode Consortium](https://home.unicode.org/) is a fundamental part of internationalization. Unicode supports not only variations among the world's writing systems but also cultural variations such as currencies and time/date formats. ## See also - Related glossary terms: - {{glossary("Localization")}} - [W3C Internationalization Activity](https://www.w3.org/International/) - [Unicode Consortium](https://home.unicode.org/) - [JavaScript Internationalization API](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)