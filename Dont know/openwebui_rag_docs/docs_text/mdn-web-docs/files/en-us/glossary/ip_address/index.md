Path: mdn-web-docs > files > en-us > glossary > ip_address > index.md

Path: mdn-web-docs > files > en-us > glossary > ip_address > index.md Path: mdn-web-docs > files > en-us > glossary > ip_address > index.md Path: mdn-web-docs > files > en-us > glossary > ip_address > index.md Path: mdn-web-docs > files > en-us > glossary > ip_address > index.md --- title: IP Address slug: Glossary/IP_Address page-type: glossary-definition --- {{GlossarySidebar}} An **IP address** is a number used to address each device on an IP network uniquely. _IP_ stands for _Internet Protocol_ which is the [protocol layer](https://docs.oracle.com/cd/E19683-01/806-4075/ipov-7/index.html) with which the address is associated. "IP address" typically still refers to 32-bit IPv4 addresses until IPv6 is deployed more broadly. ## See also - [IP address](https://en.wikipedia.org/wiki/IP_address) on Wikipedia