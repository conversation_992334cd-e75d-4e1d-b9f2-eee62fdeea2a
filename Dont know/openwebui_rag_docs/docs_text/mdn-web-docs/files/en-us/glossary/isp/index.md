Path: mdn-web-docs > files > en-us > glossary > isp > index.md

Path: mdn-web-docs > files > en-us > glossary > isp > index.md Path: mdn-web-docs > files > en-us > glossary > isp > index.md Path: mdn-web-docs > files > en-us > glossary > isp > index.md Path: mdn-web-docs > files > en-us > glossary > isp > index.md --- title: ISP slug: Glossary/ISP page-type: glossary-definition --- {{GlossarySidebar}} An ISP (Internet Service Provider) sells Internet access, and sometimes email, web hosting, and voice over IP, either by a dial-up connection over a phone line (formerly more common), or through a broadband connection such as a cable modem or DSL service. ## See also - [How the Internet works](/en-US/docs/Learn_web_development/Howto/Web_mechanics/How_does_the_Internet_work) (explanation for beginners) - [Internet service provider](https://en.wikipedia.org/wiki/Internet_service_provider) on Wikipedia