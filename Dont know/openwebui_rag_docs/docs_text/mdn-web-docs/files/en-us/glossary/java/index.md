Path: mdn-web-docs > files > en-us > glossary > java > index.md

Path: mdn-web-docs > files > en-us > glossary > java > index.md Path: mdn-web-docs > files > en-us > glossary > java > index.md Path: mdn-web-docs > files > en-us > glossary > java > index.md Path: mdn-web-docs > files > en-us > glossary > java > index.md --- title: Java slug: Glossary/Java page-type: glossary-definition --- {{GlossarySidebar}} Java is a {{glossary("Compile", "compiled")}}, {{glossary("OOP", "object-oriented")}}, highly portable {{Glossary("computer programming", "programming")}} language. Java is statically typed and features a similar syntax to C. It comes with a large library of readily usable functions, the Java Software Development Kit (SDK). Programs are {{glossary("Compile", "compiled")}} only once ahead of time into a proprietary byte code and package format that runs inside the Java Virtual Machine (JVM). The JVM is available across many platforms, which allows Java programs to run almost everywhere without the need to be compiled or packaged again. This makes it a preferred language in many large enterprises with heterogeneous landscapes, but may be perceived "heavy". ## See also - [Java](<https://en.wikipedia.org/wiki/Java_(programming_language)>) on Wikipedia