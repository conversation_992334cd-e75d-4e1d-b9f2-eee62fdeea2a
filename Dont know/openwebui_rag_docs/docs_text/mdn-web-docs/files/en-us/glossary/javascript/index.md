Path: mdn-web-docs > files > en-us > glossary > javascript > index.md

Path: mdn-web-docs > files > en-us > glossary > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > javascript > index.md Path: mdn-web-docs > files > en-us > glossary > javascript > index.md --- title: JavaScript slug: Glossary/JavaScript page-type: glossary-definition --- {{GlossarySidebar}} JavaScript (or "JS") is a programming language used most often for dynamic client-side scripts on webpages, but it is also often used on the {{Glossary("Server","server")}}-side, using a runtime such as {{Glossary("Node.js")}}, {{Glossary("Deno")}}, and [Bun](https://bun.sh/). JavaScript **should not** be confused with the [Java programming language](<https://en.wikipedia.org/wiki/Java_(programming_language)>). Although _"Java"_ and _"JavaScript"_ are trademarks (or registered trademarks) of Oracle in the U.S. and other countries, the two programming languages are significantly different in their syntax, semantics, and use cases. JavaScript is primarily used in the browser, enabling developers to manipulate webpage content through the {{Glossary("DOM")}}, retrieve content from servers using the {{domxref("Window/fetch", "fetch()")}} API, store complex data using {{Glossary("IndexedDB")}}, draw graphics with {{Glossary("canvas")}}, interact with the device running the browser through various {{Glossary("API","APIs")}}, and more. JavaScript is one of the world's most commonly-used languages, owing to the recent growth and performance improvement of {{Glossary("API","APIs")}} available in browsers. ## Origins and History Conceived as a server-side language by Brendan Eich (then employed by the Netscape Corporation), JavaScript soon came to Netscape Navigator 2.0 in September 1995. JavaScript enjoyed immediate success and {{glossary("Microsoft Internet Explorer", "Internet Explorer 3.0")}} introduced JavaScript support under the name JScript in August 1996. In November 1996, Netscape began working with Ecma International to make JavaScript an industry standard. Since then, the standardized JavaScript is called ECMAScript and specified under ECMA-262. The standard is constantly updated and implemented (a _living standard_). Recently, JavaScript's popularity has expanded even further through the successful [Node.js](https://nodejs.org/en) platform the most popular cross-platform JavaScript runtime environment outside the browser. Node.js - built using [Chrome's V8 JavaScript Engine](<https://en.wikipedia.org/wiki/V8_(JavaScript_engine)>) - allows developers to use JavaScript as a scripting language to automate things on a computer and build fully functional {{Glossary("HTTP")}} and {{Glossary("WebSockets")}} servers. ## See also - [JavaScript](https://en.wikipedia.org/wiki/JavaScript) on Wikipedia - The [JavaScript Guide](/en-US/docs/Web/JavaScript/Guide) on MDN - [The "javascripting" workshop on NodeSchool](https://nodeschool.io/#workshoppers) - [The JavaScript course on codecademy.com](https://www.codecademy.com/catalog/language/javascript) - [The latest ECMAScript standard](https://ecma-international.org/publications-and-standards/standards/ecma-262/) - The [JavaScript reference](/en-US/docs/Web/JavaScript/Reference) on MDN - [The _Eloquent JavaScript_ book](https://eloquentjavascript.net/)