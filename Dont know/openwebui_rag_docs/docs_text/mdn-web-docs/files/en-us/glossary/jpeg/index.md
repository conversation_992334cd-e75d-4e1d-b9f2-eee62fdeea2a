Path: mdn-web-docs > files > en-us > glossary > jpeg > index.md

Path: mdn-web-docs > files > en-us > glossary > jpeg > index.md Path: mdn-web-docs > files > en-us > glossary > jpeg > index.md Path: mdn-web-docs > files > en-us > glossary > jpeg > index.md Path: mdn-web-docs > files > en-us > glossary > jpeg > index.md --- title: JPEG slug: Glossary/JPEG page-type: glossary-definition --- {{GlossarySidebar}} **JPEG** (Joint Photographic Experts Group) is a commonly used method of lossy compression for digital images. JPEG compression is composed of three compression techniques applied in successive layers, including chrominance subsampling, discrete cosine transformation and quantization, and run-length Delta & Huffman encoding. Chroma subsampling involves implementing less resolution for chroma information than for luma information, taking advantage of the human visual system's lower acuity for color differences than for luminance. A discrete cosine transform expresses a finite sequence of data points in terms of a sum of cosine functions oscillating at different frequencies. ## See also - [JPEG](https://en.wikipedia.org/wiki/JPEG) on Wikipedia