Path: mdn-web-docs > files > en-us > glossary > json > index.md

Path: mdn-web-docs > files > en-us > glossary > json > index.md Path: mdn-web-docs > files > en-us > glossary > json > index.md Path: mdn-web-docs > files > en-us > glossary > json > index.md Path: mdn-web-docs > files > en-us > glossary > json > index.md --- title: JSON slug: Glossary/JSON page-type: glossary-definition --- {{GlossarySidebar}} _JavaScript Object Notation_ (**JSON**) is a data-interchange format. Although not a strict subset, JSON closely resembles a subset of {{Glossary("JavaScript")}} syntax. Though many programming languages support JSON, it is especially useful for JavaScript-based apps, including websites and browser extensions. JSON can represent numbers, booleans, strings, `null`, arrays (ordered sequences of values), and objects (string-value mappings) made up of these values (or of other arrays and objects). JSON does not natively represent more complex data types like functions, regular expressions, dates, and so on. (Date objects by default serialize to a string containing the date in ISO format, so the information isn't completely lost.) If you need JSON to represent additional data types, transform values as they are serialized or before they are deserialized. ## See also - [JSON](/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON) - [JSON](https://en.wikipedia.org/wiki/JSON) on Wikipedia