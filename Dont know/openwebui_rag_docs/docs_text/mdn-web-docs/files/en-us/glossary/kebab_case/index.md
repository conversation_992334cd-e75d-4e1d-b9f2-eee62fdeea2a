Path: mdn-web-docs > files > en-us > glossary > kebab_case > index.md

Path: mdn-web-docs > files > en-us > glossary > kebab_case > index.md Path: mdn-web-docs > files > en-us > glossary > kebab_case > index.md Path: mdn-web-docs > files > en-us > glossary > kebab_case > index.md Path: mdn-web-docs > files > en-us > glossary > kebab_case > index.md --- title: Kebab case slug: Glossary/Kebab_case page-type: glossary-definition --- {{GlossarySidebar}} **Kebab case** is a way of writing phrases without spaces, where spaces are replaced with hyphens `-`, and the words are typically all lower case. The name comes from the similarity of the words to meat on a kebab skewer. It's often stylized as "kebab-case" to remind the reader of its appearance. Kebab casing is often used as a variable naming convention. However, in many languages, hyphens represent subtraction, so kebab casing is not an option. CSS properties such as {{cssxref("background-color")}} and {{cssxref("font-family")}} are in kebab case, and so are HTML attributes such as [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) and [`data-*`](/en-US/docs/Web/HTML/Reference/Global_attributes/data-*). Kebab-cased words are often simply referred to as _hyphenated_. ## See also - Related glossary terms: - {{Glossary("Camel case")}} - {{Glossary("Snake case")}} - [typescript-eslint rule: `naming-convention`](https://typescript-eslint.io/rules/naming-convention/)