Path: mdn-web-docs > files > en-us > glossary > key > index.md

Path: mdn-web-docs > files > en-us > glossary > key > index.md Path: mdn-web-docs > files > en-us > glossary > key > index.md Path: mdn-web-docs > files > en-us > glossary > key > index.md Path: mdn-web-docs > files > en-us > glossary > key > index.md --- title: Key slug: Glossary/Key page-type: glossary-definition --- {{GlossarySidebar}} A key is a piece of information used by a {{Glossary("cipher")}} for {{Glossary("encryption")}} and/or {{Glossary("decryption")}}. Encrypted messages should remain secure even if everything about the cryptosystem, except for the key, is public knowledge. In {{Glossary("symmetric-key cryptography")}}, the same key is used for both encryption and decryption. This key is referred to as the _shared key_, or the _secret key_. In {{Glossary("public-key cryptography")}}, there exists a pair of related keys known as the _public key_ and _private key_. The public key is freely available, whereas the private key is kept secret. The public key is able to encrypt messages that only the corresponding private key is able to decrypt, and vice versa. ## See also - [Kerckhoffs's principle](https://en.wikipedia.org/wiki/Kerckhoffs%27s_principle) on Wikipedia - Related glossary terms: - {{Glossary("Block cipher mode of operation")}} - {{Glossary("Cipher")}} - {{Glossary("Ciphertext")}} - {{Glossary("Cipher suite")}} - {{Glossary("Cryptanalysis")}} - {{Glossary("Cryptography")}} - {{Glossary("Decryption")}} - {{Glossary("Encryption")}} - {{Glossary("Plaintext")}} - {{Glossary("Public-key cryptography")}} - {{Glossary("Symmetric-key cryptography")}}