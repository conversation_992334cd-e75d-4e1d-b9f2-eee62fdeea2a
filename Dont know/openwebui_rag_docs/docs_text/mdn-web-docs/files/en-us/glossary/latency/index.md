Path: mdn-web-docs > files > en-us > glossary > latency > index.md

Path: mdn-web-docs > files > en-us > glossary > latency > index.md Path: mdn-web-docs > files > en-us > glossary > latency > index.md Path: mdn-web-docs > files > en-us > glossary > latency > index.md Path: mdn-web-docs > files > en-us > glossary > latency > index.md --- title: Latency slug: Glossary/Latency page-type: glossary-definition --- {{GlossarySidebar}} **Latency** is the network time it takes for a requested resource to reach its destination. Low latency is good, meaning there is little or no delay. High latency is bad, meaning it takes a long time for the requested resource to reach its destination. Latency can be a factor in any kind of data flow, but is most commonly discussed in terms of network latency (the time it takes for a packet of data to travel from source to destination) and media codec latency (the time it takes for the source data to be encoded or decoded and reach the consumer of the resulting data). ## See also - [Understanding Latency](/en-US/docs/Web/Performance/Guides/Understanding_latency)