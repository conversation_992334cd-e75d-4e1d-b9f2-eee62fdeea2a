Path: mdn-web-docs > files > en-us > glossary > lazy_load > index.md

Path: mdn-web-docs > files > en-us > glossary > lazy_load > index.md Path: mdn-web-docs > files > en-us > glossary > lazy_load > index.md Path: mdn-web-docs > files > en-us > glossary > lazy_load > index.md Path: mdn-web-docs > files > en-us > glossary > lazy_load > index.md --- title: Lazy load slug: Glossary/Lazy_load page-type: glossary-definition --- {{GlossarySidebar}} **Lazy loading** is a strategy that delays the loading of some assets (e.g., images) until they are needed by the user based on the user's activity and navigation pattern; typically, these assets are only loaded when they are scrolled into view. If correctly implemented, this delay in asset loading is seamless to the user experience and might help improve initial load performance, including {{Glossary("time to interactive")}}, as fewer assets are required for the page to start working. ## See also - [Lazy loading](/en-US/docs/Web/Performance/Guides/Lazy_loading)