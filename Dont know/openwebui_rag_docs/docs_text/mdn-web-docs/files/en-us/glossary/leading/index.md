Path: mdn-web-docs > files > en-us > glossary > leading > index.md

Path: mdn-web-docs > files > en-us > glossary > leading > index.md Path: mdn-web-docs > files > en-us > glossary > leading > index.md Path: mdn-web-docs > files > en-us > glossary > leading > index.md Path: mdn-web-docs > files > en-us > glossary > leading > index.md --- title: Leading slug: Glossary/Leading page-type: glossary-definition --- {{GlossarySidebar}} In typography, **leading** is an amount of space included above and below text to provide spacing between lines. Historically, in physical typesetting, pieces of actual [lead](https://en.wikipedia.org/wiki/Lead) were used to implement this spacing, which is where the name comes from. In CSS, typographic leading is the difference between the content height and the line-height, generally set by the {{cssxref("line-height")}} property. Leading set via `line-height` provides spacing between lines, which can be negative. The space is distributed equally above and below the text, which is referred to as **half-leading**. The area of a font above the cap baseline is referred to as the _over edge_. The area below the {{glossary("/Baseline/Typography", "alphabetic baseline")}} is referred to as the _under edge_. Likewise, the half-leading above and below a line is referred to as the _over leading_ and _under leading_, respectively. The half-leading can be trimmed from the block-start edge and block-end edge of a text element's block container using the {{cssxref("text-box")}} properties. ## See also - {{cssxref("line-height")}} - {{cssxref("text-box")}} - [CSS inline layout](/en-US/docs/Web/CSS/CSS_inline_layout) module - [Leading](https://en.wikipedia.org/wiki/Leading) on Wikipedia - [The Thing With Lead ing in CSS](https://matthiasott.com/notes/the-thing-with-leading-in-css) on matthiasott.com (2022)