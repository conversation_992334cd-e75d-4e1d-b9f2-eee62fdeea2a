Path: mdn-web-docs > files > en-us > glossary > lgpl > index.md

Path: mdn-web-docs > files > en-us > glossary > lgpl > index.md Path: mdn-web-docs > files > en-us > glossary > lgpl > index.md Path: mdn-web-docs > files > en-us > glossary > lgpl > index.md Path: mdn-web-docs > files > en-us > glossary > lgpl > index.md --- title: LGPL slug: Glossary/LGPL page-type: glossary-definition --- {{GlossarySidebar}} LGPL (GNU Lesser General Public License) is a free software license published by the Free Software Foundation. The LGPL provides a more permissive alternative for the strictly {{Glossary("copyleft")}} {{Glossary("GPL")}}. While any derivative work using a GPL-licensed program must be released under the same terms (free to use, share, study, and modify), the LGPL only requires the LGPL-licensed component of the derivative program to continue using the LGPL, not the whole program. LGPL is usually used to license shared components such as libraries (`.dll`, `.so`, `.jar`, etc.). ## See also - [GNU LGPL](https://en.wikipedia.org/wiki/GNU_Lesser_General_Public_License) on Wikipedia - [LGPL License](https://www.gnu.org/licenses/lgpl-3.0.html) text on gnu.org