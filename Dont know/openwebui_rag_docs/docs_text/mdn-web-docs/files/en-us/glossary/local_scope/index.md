Path: mdn-web-docs > files > en-us > glossary > local_scope > index.md

Path: mdn-web-docs > files > en-us > glossary > local_scope > index.md Path: mdn-web-docs > files > en-us > glossary > local_scope > index.md Path: mdn-web-docs > files > en-us > glossary > local_scope > index.md Path: mdn-web-docs > files > en-us > glossary > local_scope > index.md --- title: Local scope slug: Glossary/Local_scope page-type: glossary-definition --- {{GlossarySidebar}} Local scope is a characteristic of {{glossary("variable","variables")}} that makes them local (i.e., the variable name is only bound to its {{glossary("value")}} within a scope which is not the {{glossary("global scope")}}). ## See also - [Scope](<https://en.wikipedia.org/wiki/Scope_(computer_science)>) on Wikipedia