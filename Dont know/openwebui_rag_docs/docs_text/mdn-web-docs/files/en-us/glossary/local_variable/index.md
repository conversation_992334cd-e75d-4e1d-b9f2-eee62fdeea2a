Path: mdn-web-docs > files > en-us > glossary > local_variable > index.md

Path: mdn-web-docs > files > en-us > glossary > local_variable > index.md Path: mdn-web-docs > files > en-us > glossary > local_variable > index.md Path: mdn-web-docs > files > en-us > glossary > local_variable > index.md Path: mdn-web-docs > files > en-us > glossary > local_variable > index.md --- title: Local variable slug: Glossary/Local_variable page-type: glossary-definition --- {{GlossarySidebar}} A {{glossary("variable")}} whose name is bound to its {{glossary("value")}} only within a {{Glossary("local scope")}}. ## Example ```js let global = 5; // A global variable function fun() { let local = 10; // A local variable } ``` ## See also - [Local variable](https://en.wikipedia.org/wiki/Local_variable) on Wikipedia