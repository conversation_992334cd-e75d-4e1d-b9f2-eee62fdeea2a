Path: mdn-web-docs > files > en-us > glossary > localization > index.md

Path: mdn-web-docs > files > en-us > glossary > localization > index.md Path: mdn-web-docs > files > en-us > glossary > localization > index.md Path: mdn-web-docs > files > en-us > glossary > localization > index.md Path: mdn-web-docs > files > en-us > glossary > localization > index.md --- title: Localization slug: Glossary/Localization page-type: glossary-definition --- {{GlossarySidebar}} **Localization** (l10n) is the process of adapting a software user interface to a specific culture. The following are common factors to consider: - language - unit of measure (e.g., kilometers in Europe, miles in U.S.) - text direction (e.g., European languages are left-to-right, Arabic right-to-left) - capitalization in Latin script (e.g., English uses capitals for weekdays, Spanish uses lowercase) - adaptation of idioms (e.g., "raining cats and dogs" makes no sense when translated literally) - use of register (e.g., in Japanese respectful speech differs exceptionally from casual speech) - number format (e.g., 10 000,00 in Germany vs. 10,000.00 in the U.S.) - date format - currency - cultural references - paper size - color psychology - compliance with local laws - local holidays - personal names The complementary practice, of designing a system so it is easy to localize, is called {{glossary("Internationalization")}}. ## See also - [Localization](https://en.wikipedia.org/wiki/Language_localisation) on Wikipedia - Related glossary terms: - {{glossary("Internationalization")}}