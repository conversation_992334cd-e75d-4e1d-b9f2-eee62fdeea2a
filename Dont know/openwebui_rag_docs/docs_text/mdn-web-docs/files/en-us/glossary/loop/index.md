Path: mdn-web-docs > files > en-us > glossary > loop > index.md

Path: mdn-web-docs > files > en-us > glossary > loop > index.md Path: mdn-web-docs > files > en-us > glossary > loop > index.md Path: mdn-web-docs > files > en-us > glossary > loop > index.md Path: mdn-web-docs > files > en-us > glossary > loop > index.md --- title: Loop slug: Glossary/Loop page-type: glossary-definition --- {{GlossarySidebar}} A **loop** is a sequence of instructions that is repeated until a certain condition is met. An example would be the process of getting an item of data and changing it, and then making sure some {{Glossary("conditional", "condition")}} is checked such as if a counter has reached a prescribed number. Loops are one way to execute a statement for a variable number of times. The same effect can be achieved with {{glossary("Recursion", "recursion")}}, especially in languages where all data is {{glossary("Immutable", "immutable")}}, making it impossible to update a counter variable. ## See also - [Control flow](https://en.wikipedia.org/wiki/Control_flow#Loops) on Wikipedia - [Loops and iteration guide](/en-US/docs/Web/JavaScript/Guide/Loops_and_iteration)