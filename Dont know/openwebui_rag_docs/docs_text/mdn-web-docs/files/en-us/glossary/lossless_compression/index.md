Path: mdn-web-docs > files > en-us > glossary > lossless_compression > index.md

Path: mdn-web-docs > files > en-us > glossary > lossless_compression > index.md Path: mdn-web-docs > files > en-us > glossary > lossless_compression > index.md Path: mdn-web-docs > files > en-us > glossary > lossless_compression > index.md Path: mdn-web-docs > files > en-us > glossary > lossless_compression > index.md --- title: Lossless compression slug: Glossary/Lossless_compression page-type: glossary-definition --- {{GlossarySidebar}} **Lossless compression** is a class of data compression algorithms that allows the original data to be perfectly reconstructed from the compressed data. Lossless compression methods are reversible. Examples of lossless compression include {{glossary("GZip_compression", "gzip")}}, {{glossary("Brotli_compression", "brotli")}}, {{glossary("Zstandard compression", "Zstandard")}}, {{glossary("WebP")}}, and {{glossary("PNG")}}. {{glossary("Lossy compression")}}, on the other hand, uses inexact approximations by discarding some data from the original file, making it an irreversible compression method. Compression methods such as {{glossary("WebP")}} are capable of [both lossy and lossless compression](https://developers.google.com/speed/webp/docs/compression) depending on the compression level or the options you want to use during encoding. ## See also - Related glossary terms: - {{glossary("Lossy compression")}}