Path: mdn-web-docs > files > en-us > glossary > ltr > index.md

Path: mdn-web-docs > files > en-us > glossary > ltr > index.md Path: mdn-web-docs > files > en-us > glossary > ltr > index.md Path: mdn-web-docs > files > en-us > glossary > ltr > index.md Path: mdn-web-docs > files > en-us > glossary > ltr > index.md --- title: LTR (Left To Right) slug: Glossary/LTR page-type: glossary-definition --- {{GlossarySidebar}} **LTR** (**Left To Right**) is a {{Glossary("locale")}} property indicating that text is written from left to right. For example, the `en-US` locale (for US English) specifies left-to-right. Most Western languages, as well as many others around the world, are written LTR. The opposite of LTR, {{Glossary("RTL")}} (Right To Left) is used in other common languages, including Arabic (`ar`) and Hebrew (`he`). ## See also - Related glossary terms: - {{Glossary("Locale")}} - {{Glossary("Localization")}} - {{Glossary("RTL")}} - {{Glossary("BiDi")}} - [HTML global attributes](/en-US/docs/Web/HTML/Reference/Global_attributes) - [dir](/en-US/docs/Web/HTML/Reference/Global_attributes/dir) - [lang](/en-US/docs/Web/HTML/Reference/Global_attributes/lang) - [CSS](/en-US/docs/Web/CSS) - {{cssxref(":dir")}} - {{cssxref("direction")}} - {{cssxref("unicode-bidi")}} - {{cssxref("writing-mode")}}