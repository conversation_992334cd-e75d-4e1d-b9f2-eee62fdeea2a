Path: mdn-web-docs > files > en-us > glossary > markup > index.md

Path: mdn-web-docs > files > en-us > glossary > markup > index.md Path: mdn-web-docs > files > en-us > glossary > markup > index.md Path: mdn-web-docs > files > en-us > glossary > markup > index.md Path: mdn-web-docs > files > en-us > glossary > markup > index.md --- title: Markup slug: Glossary/Markup page-type: glossary-definition --- {{GlossarySidebar}} A markup language is one that is designed for defining and presenting text. {{glossary("HTML")}} (HyperText Markup Language), is an example of a markup language. Within a text file such as an HTML file, elements are _marked up_ using {{glossary("Tag","tags")}} which explain the purpose of that part of the content. ## Types of markup language - **Presentational Markup:** - : Used by traditional word processing system with WYSIWYG (what you see it is what you get); this is hidden from human authors, users and editors. - **Procedural Markup:** - : Combined with text to provide instructions on text processing to programs. This text is visibly manipulated by the author. - **Descriptive Markup:** - : Labels sections of documents as to how the program should handle them. For example, the HTML {{HTMLElement("td")}} defines a cell in a HTML table. ## See also - Related glossary terms: - {{Glossary("HTML")}} - {{Glossary("XHTML")}} - {{Glossary("XML")}} - {{Glossary("SVG")}}