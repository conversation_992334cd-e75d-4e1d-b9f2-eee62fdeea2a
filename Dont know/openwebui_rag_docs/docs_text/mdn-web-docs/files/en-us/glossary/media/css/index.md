Path: mdn-web-docs > files > en-us > glossary > media > css > index.md

Path: mdn-web-docs > files > en-us > glossary > media > css > index.md Path: mdn-web-docs > files > en-us > glossary > media > css > index.md Path: mdn-web-docs > files > en-us > glossary > media > css > index.md Path: mdn-web-docs > files > en-us > glossary > media > css > index.md --- title: Media (CSS) slug: Glossary/Media/CSS page-type: glossary-definition --- {{GlossarySidebar}} In the context of {{Glossary("CSS")}} (Cascading Style Sheets), the term **_media_** refers to the destination to which the document is to be drawn by the {{Glossary("Engine/Rendering", "rendering engine")}}. Typically, this is a screen but it may also be a printer, Braille display, or another type of device. CSS offers several features that allow you to tweak your document's styles or even offer different styles according to the media **type** (such as screen or print, to name two) or media **capabilities** (such as width, resolution, or other values) of the viewer's device. ## See also - [Using media queries](/en-US/docs/Web/CSS/CSS_media_queries/Using_media_queries) - [Media queries](/en-US/docs/Web/CSS/CSS_media_queries) - {{cssxref("@media")}} [at-rule](/en-US/docs/Web/CSS/CSS_syntax/At-rule): Conditionally apply part of a CSS stylesheet, based on the result of a media query. - {{domxref("Window.matchMedia()")}}: Test the viewing device against a media query