Path: mdn-web-docs > files > en-us > glossary > microsoft_edge > index.md

Path: mdn-web-docs > files > en-us > glossary > microsoft_edge > index.md Path: mdn-web-docs > files > en-us > glossary > microsoft_edge > index.md Path: mdn-web-docs > files > en-us > glossary > microsoft_edge > index.md Path: mdn-web-docs > files > en-us > glossary > microsoft_edge > index.md --- title: Microsoft Edge slug: Glossary/Microsoft_Edge page-type: glossary-definition --- {{GlossarySidebar}} **Microsoft Edge** is a proprietary cross-platform {{glossary("World Wide Web", "Web")}} {{Glossary("browser")}} developed by Microsoft since 2014. Initially known as Spartan, Edge replaced the longstanding browser {{glossary("Microsoft Internet Explorer", "Internet Explorer")}}. Edge is included with Windows 10 and Windows 11, and is also available for macOS, iOS/iPadOS, Android and Linux. Edge used EdgeHTML as its {{Glossary("Engine/Rendering", "rendering engine")}} until 2019, when it was replaced by {{glossary("Blink")}}, the rendering engine used by {{Glossary("Google Chrome")}}. On iOS/iPadOS, Edge instead uses {{glossary("WebKit")}} as its rendering engine. Edge supports 'IE mode' for backwards compatibility that uses the {{glossary("Trident")}} engine to render pages requiring legacy Internet Explorer features. ## See also - [Official website](https://www.microsoft.com/en-us/edge) - [Microsoft Edge](https://en.wikipedia.org/wiki/Microsoft_Edge) on Wikipedia - Related glossary terms: - Related glossary terms: - {{Glossary("Browser")}} - {{Glossary("Engine/Rendering", "Rendering engine")}} - {{Glossary("Microsoft Internet Explorer", "Internet Explorer")}} - {{Glossary("Blink")}} - {{Glossary("Trident")}} - {{Glossary("WebKit")}}