Path: mdn-web-docs > files > en-us > glossary > middleware > index.md

Path: mdn-web-docs > files > en-us > glossary > middleware > index.md Path: mdn-web-docs > files > en-us > glossary > middleware > index.md Path: mdn-web-docs > files > en-us > glossary > middleware > index.md Path: mdn-web-docs > files > en-us > glossary > middleware > index.md --- title: Middleware slug: Glossary/Middleware page-type: glossary-definition --- {{GlossarySidebar}} Middleware is a (loosely defined) term for any software or service that enables the parts of a system to communicate and manage data. It is the software that handles communication between components and input/output, so developers can focus on the specific purpose of their application. In server-side web application frameworks, the term is often more specifically used to refer to pre-built software components that can be added to the framework's request/response processing pipeline, to handle tasks such as database access. ## See also - [Middleware\_(distributed_applications)](<https://en.wikipedia.org/wiki/Middleware_(distributed_applications)>) on Wikipedia - [Middleware](https://en.wikipedia.org/wiki/Middleware) on Wikipedia