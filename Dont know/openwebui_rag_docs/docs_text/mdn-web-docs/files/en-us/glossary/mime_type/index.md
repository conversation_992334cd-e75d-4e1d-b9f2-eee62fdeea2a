Path: mdn-web-docs > files > en-us > glossary > mime_type > index.md

Path: mdn-web-docs > files > en-us > glossary > mime_type > index.md Path: mdn-web-docs > files > en-us > glossary > mime_type > index.md Path: mdn-web-docs > files > en-us > glossary > mime_type > index.md Path: mdn-web-docs > files > en-us > glossary > mime_type > index.md --- title: MIME type slug: Glossary/MIME_type page-type: glossary-definition --- {{GlossarySidebar}} A **MIME type** (now properly called "media type", but also sometimes "content type") is a string sent along with a file indicating the type of the file (describing the content format, for example, a sound file might be labeled `audio/ogg`, or an image file `image/png`). It serves the same purpose as filename extensions traditionally do on Windows. The name originates from the {{glossary("mime","MIME")}} standard originally used in email. ## See also - [Internet media type](https://en.wikipedia.org/wiki/Internet_media_type) on Wikipedia - [List of MIME types](https://www.iana.org/assignments/media-types/media-types.xhtml) - [Properly Configuring Server MIME Types](/en-US/docs/Learn_web_development/Extensions/Server-side/Configuring_server_MIME_types) - Details information about the usage of [MIME Types](/en-US/docs/Web/HTTP/Guides/MIME_types) in a Web context. - [Incomplete list of MIME types](/en-US/docs/Web/HTTP/Guides/MIME_types/Common_types) - [MediaRecorder.mimeType](/en-US/docs/Web/API/MediaRecorder/mimeType)