Path: mdn-web-docs > files > en-us > glossary > minification > index.md

Path: mdn-web-docs > files > en-us > glossary > minification > index.md Path: mdn-web-docs > files > en-us > glossary > minification > index.md Path: mdn-web-docs > files > en-us > glossary > minification > index.md Path: mdn-web-docs > files > en-us > glossary > minification > index.md --- title: Minification slug: Glossary/Minification page-type: glossary-definition --- {{GlossarySidebar}} **Minification** is the process of removing unnecessary or redundant data without affecting how a resource is processed by the browser. Minification can include the removal of code comments, white space, and unused code, as well as the shortening of variable and function names. Minification is used to improve web performance by reducing file size. It is generally an automated step that occurs at build time. As minification makes code less legible to humans, developer tools have 'prettification' features that can add white space back into the code to make it a bit more legible.