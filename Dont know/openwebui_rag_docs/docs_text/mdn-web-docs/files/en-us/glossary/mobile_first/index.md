Path: mdn-web-docs > files > en-us > glossary > mobile_first > index.md

Path: mdn-web-docs > files > en-us > glossary > mobile_first > index.md Path: mdn-web-docs > files > en-us > glossary > mobile_first > index.md Path: mdn-web-docs > files > en-us > glossary > mobile_first > index.md Path: mdn-web-docs > files > en-us > glossary > mobile_first > index.md --- title: Mobile First slug: Glossary/Mobile_First page-type: glossary-definition --- {{GlossarySidebar}} **Mobile first**, a form of {{Glossary("progressive enhancement")}}, is a web-development and web-design approach that focuses on prioritizing design and development for mobile screen sizes over design and development for desktop screen sizes. The rationale behind the mobile-first approach is to provide users with good user experiences at all screen sizes by starting with creating a user experience that works well on small screens, and then building on top of that to further enrich the user experience as the screen size increases. The mobile-first approach contrasts with the older approach of designing for desktop screen sizes first, and then only later adding some support for small screen sizes.