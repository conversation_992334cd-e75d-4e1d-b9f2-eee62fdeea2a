Path: mdn-web-docs > files > en-us > glossary > modularity > index.md

Path: mdn-web-docs > files > en-us > glossary > modularity > index.md Path: mdn-web-docs > files > en-us > glossary > modularity > index.md Path: mdn-web-docs > files > en-us > glossary > modularity > index.md Path: mdn-web-docs > files > en-us > glossary > modularity > index.md --- title: Modularity slug: Glossary/Modularity page-type: glossary-definition --- {{GlossarySidebar}} The term Modularity refers to the degree to which a system's components may be separated and recombined, it is also division of a software package into logical units. The advantage of a modular system is that one can reason the parts independently ## See also - [Modularity](https://en.wikipedia.org/wiki/Modularity) on Wikipedia