Path: mdn-web-docs > files > en-us > glossary > namespace > index.md

Path: mdn-web-docs > files > en-us > glossary > namespace > index.md Path: mdn-web-docs > files > en-us > glossary > namespace > index.md Path: mdn-web-docs > files > en-us > glossary > namespace > index.md Path: mdn-web-docs > files > en-us > glossary > namespace > index.md --- title: Namespace slug: Glossary/Namespace page-type: glossary-definition --- {{GlossarySidebar}} Namespace is a context for identifiers, a logical grouping of names used in a program. Within the same context and same scope, an identifier must uniquely identify an entity. In an operating system, a directory is a namespace. Each file or subdirectory within a directory has a unique name; the same name can be used multiple times across subdirectories. In HTML, CSS, and XML-based languages, a namespace is the explicitly declared or implied dialect to which an element (or attribute) belongs. ## See also - [Namespaces crash course](/en-US/docs/Web/SVG/Guides/Namespaces_crash_course) - [CSS namespaces](/en-US/docs/Web/CSS/CSS_namespaces) module - CSS {{CSSXref("@namespace")}} - [`Document.createElementNS()`](/en-US/docs/Web/API/Document/createElementNS) method - [Namespace](https://en.wikipedia.org/wiki/Namespace) on Wikipedia