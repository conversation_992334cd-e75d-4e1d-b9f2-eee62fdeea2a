Path: mdn-web-docs > files > en-us > glossary > native > index.md

Path: mdn-web-docs > files > en-us > glossary > native > index.md Path: mdn-web-docs > files > en-us > glossary > native > index.md Path: mdn-web-docs > files > en-us > glossary > native > index.md Path: mdn-web-docs > files > en-us > glossary > native > index.md --- title: Native slug: Glossary/Native page-type: glossary-definition --- {{GlossarySidebar}} A _native_ application has been compiled to run on the hardware/software environment that comprises the targeted architecture. An example of a native Android app would be a mobile application written in Java using the Android toolchain. On the other hand, a Web App that runs inside a browser is not native it is run in the web browser, which sits on top of the native environment, not the native environment itself. ## See also - [Native (computing)](<https://en.wikipedia.org/wiki/Native_(computing)>) on Wikipedia