Path: mdn-web-docs > files > en-us > glossary > navigation_directive > index.md

Path: mdn-web-docs > files > en-us > glossary > navigation_directive > index.md Path: mdn-web-docs > files > en-us > glossary > navigation_directive > index.md Path: mdn-web-docs > files > en-us > glossary > navigation_directive > index.md Path: mdn-web-docs > files > en-us > glossary > navigation_directive > index.md --- title: Navigation directive slug: Glossary/Navigation_directive page-type: glossary-definition --- {{GlossarySidebar}} **{{Glossary("CSP")}} navigation directives** are used in a {{HTTPHeader("Content-Security-Policy")}} header and govern to which location a user can navigate to or submit a form to, for example. Navigation directives don't fall back to the {{CSP("default-src")}} directive. See [Navigation directives](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy#navigation_directives) for a complete list. ## See also - <https://www.w3.org/TR/CSP/#directives-navigation> - Other kinds of directives: - {{Glossary("Fetch directive")}} - {{Glossary("Document directive")}} - {{Glossary("Reporting directive")}} - [`block-all-mixed-content`](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy/block-all-mixed-content) - [`upgrade-insecure-requests`](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy/upgrade-insecure-requests) - [`trusted-types`](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy/trusted-types) - {{HTTPHeader("Content-Security-Policy")}}