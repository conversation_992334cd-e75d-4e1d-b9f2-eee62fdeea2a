Path: mdn-web-docs > files > en-us > glossary > node.js > index.md

Path: mdn-web-docs > files > en-us > glossary > node.js > index.md Path: mdn-web-docs > files > en-us > glossary > node.js > index.md Path: mdn-web-docs > files > en-us > glossary > node.js > index.md Path: mdn-web-docs > files > en-us > glossary > node.js > index.md --- title: Node.js slug: Glossary/Node.js page-type: glossary-definition --- {{GlossarySidebar}} Node.js is a cross-platform {{Glossary("JavaScript")}} runtime environment that allows developers to build server-side and network applications with JavaScript. ## Node Package Manager (npm) [npm](https://www.npmjs.com/) is a package manager that is downloaded and bundled alongside Node.js. Its command-line (CLI) client `npm` can be used to download, configure and create packages for use in Node.js projects. Downloaded packages can be imported by [ES imports](/en-US/docs/Web/JavaScript/Reference/Statements/import) and [CommonJS `require()`](https://en.wikipedia.org/wiki/CommonJS) without including the dependency directory `node_modules/` they are downloaded to, as Node.js [resolves](https://nodejs.org/api/modules.html#loading-from-node_modules-folders) packages without a relative or absolute path specified in their import. Packages hosted on npm are downloaded from the registry at [https://registry.npmjs.org/](https://registry.npmjs.org/), but the CLI can be configured to use any compatible instance. ## See also - [Node.js](https://en.wikipedia.org/wiki/Node.js) on Wikipedia - [Node.js website](https://nodejs.org/) - [API reference documentation](https://nodejs.org/api/) - [Guides](https://nodejs.org/en/learn/getting-started/introduction-to-nodejs) - [npm Documentation](https://docs.npmjs.com/)