Path: mdn-web-docs > files > en-us > glossary > node > dom > index.md

Path: mdn-web-docs > files > en-us > glossary > node > dom > index.md Path: mdn-web-docs > files > en-us > glossary > node > dom > index.md Path: mdn-web-docs > files > en-us > glossary > node > dom > index.md Path: mdn-web-docs > files > en-us > glossary > node > dom > index.md --- title: Node (DOM) slug: Glossary/Node/DOM page-type: glossary-definition --- {{GlossarySidebar}} In the context of the {{Glossary("DOM")}}, a **node** is a single point in the node tree. Various things that are nodes are the document itself, elements, text, and comments. ## See also - The [node tree](https://dom.spec.whatwg.org/#concept-node) WHATWG spec - [Node](/en-US/docs/Web/API/Node) objects