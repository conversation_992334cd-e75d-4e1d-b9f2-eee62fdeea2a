Path: mdn-web-docs > files > en-us > glossary > non-normative > index.md

Path: mdn-web-docs > files > en-us > glossary > non-normative > index.md Path: mdn-web-docs > files > en-us > glossary > non-normative > index.md Path: mdn-web-docs > files > en-us > glossary > non-normative > index.md Path: mdn-web-docs > files > en-us > glossary > non-normative > index.md --- title: Non-normative slug: Glossary/Non-normative page-type: glossary-definition --- {{GlossarySidebar}} Software {{Glossary("specification", "specifications")}} often contain information marked as **non-normative** or _informative_, which is provided to help readers understand the specification better, or to show an example or best practice. Non-normative parts of the specification are provided as guidelines, and are not considered part of the formal specification. Sections that contain the official part of the specification are often marked as {{Glossary("normative")}}. ## See also - Description of [normative and informative content](https://wiki.whatwg.org/wiki/Specs/howto#Content) in WHATWG wiki