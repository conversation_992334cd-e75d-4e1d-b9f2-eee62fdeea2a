Path: mdn-web-docs > files > en-us > glossary > normative > index.md

Path: mdn-web-docs > files > en-us > glossary > normative > index.md Path: mdn-web-docs > files > en-us > glossary > normative > index.md Path: mdn-web-docs > files > en-us > glossary > normative > index.md Path: mdn-web-docs > files > en-us > glossary > normative > index.md --- title: Normative slug: Glossary/Normative page-type: glossary-definition --- {{GlossarySidebar}} Normative is a word commonly used in software {{Glossary("specification", "specifications")}} to denote sections that are standardized and must be followed as a rule. Specifications might also contain sections that are marked as _{{Glossary("non-normative")}}_ or _informative_, which means those are provided there for the purpose of helping the reader understand the specifications better or to showcase an example or best practice, which need not be followed as a rule. ## See also - Description of [normative and informative content](https://wiki.whatwg.org/wiki/Specs/howto#Content) in WHATWG wiki