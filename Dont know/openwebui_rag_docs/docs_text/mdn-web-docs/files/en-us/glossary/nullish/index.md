Path: mdn-web-docs > files > en-us > glossary > nullish > index.md

Path: mdn-web-docs > files > en-us > glossary > nullish > index.md Path: mdn-web-docs > files > en-us > glossary > nullish > index.md Path: mdn-web-docs > files > en-us > glossary > nullish > index.md Path: mdn-web-docs > files > en-us > glossary > nullish > index.md --- title: Nullish value slug: Glossary/Nullish page-type: glossary-definition --- {{GlossarySidebar}} In {{Glossary("JavaScript")}}, a nullish value is the value which is either [`null`](/en-US/docs/Web/JavaScript/Reference/Operators/null) or {{JSxRef("undefined")}}. Nullish values are always {{Glossary("falsy")}}.