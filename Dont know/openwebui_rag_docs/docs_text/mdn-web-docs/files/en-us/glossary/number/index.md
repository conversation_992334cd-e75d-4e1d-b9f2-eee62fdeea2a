Path: mdn-web-docs > files > en-us > glossary > number > index.md

Path: mdn-web-docs > files > en-us > glossary > number > index.md Path: mdn-web-docs > files > en-us > glossary > number > index.md Path: mdn-web-docs > files > en-us > glossary > number > index.md Path: mdn-web-docs > files > en-us > glossary > number > index.md --- title: Number slug: Glossary/Number page-type: glossary-definition --- {{GlossarySidebar}} In {{Glossary("JavaScript")}}, **Number** is a numeric data type in the [double-precision 64-bit floating point format (IEEE 754)](https://en.wikipedia.org/wiki/Double_precision_floating-point_format). In other programming languages different numeric types exist; for example, Integers, Floats, Doubles, or Bignums. ## See also - [Numeric types](https://en.wikipedia.org/wiki/Data_type#Numeric_types) on Wikipedia - The JavaScript type: [`Number`](/en-US/docs/Web/JavaScript/Guide/Data_structures#number_type) - The JavaScript global object {{jsxref("Number")}} - Related glossary terms: - {{Glossary("JavaScript")}} - {{Glossary("Primitive")}}