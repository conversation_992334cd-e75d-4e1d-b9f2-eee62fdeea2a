Path: mdn-web-docs > files > en-us > glossary > object > index.md

Path: mdn-web-docs > files > en-us > glossary > object > index.md Path: mdn-web-docs > files > en-us > glossary > object > index.md Path: mdn-web-docs > files > en-us > glossary > object > index.md Path: mdn-web-docs > files > en-us > glossary > object > index.md --- title: Object slug: Glossary/Object page-type: glossary-definition --- {{GlossarySidebar}} In JavaScript, objects can be seen as a collection of properties. With the [object literal syntax](/en-US/docs/Web/JavaScript/Guide/Grammar_and_types#object_literals), a limited set of properties are initialized; then properties can be added and removed. Property values can be values of any type, including other objects, which enables building complex data structures. Properties are identified using _key_ values. A _key_ value is either a {{Glossary("String", "String value")}} or a [Symbol value](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol). There are two types of object properties: The [_data_ property](/en-US/docs/Web/JavaScript/Guide/Data_structures#data_property) and the [_accessor_ property](/en-US/docs/Web/JavaScript/Guide/Data_structures#accessor_property). > [!NOTE] > It's important to recognize it's accessor _property_ not accessor _method_. We can give a JavaScript object class-_like_ accessors by using a function as a value but that doesn't make the object a class. ## See also - [Detailed explanation of JavaScript objects](/en-US/docs/Web/JavaScript/Guide/Data_structures#objects) in the [JavaScript data types and data structures](/en-US/docs/Web/JavaScript/Guide/Data_structures) article - {{jsxref("Object")}} in the [JavaScript reference](/en-US/docs/Web/JavaScript/Reference)