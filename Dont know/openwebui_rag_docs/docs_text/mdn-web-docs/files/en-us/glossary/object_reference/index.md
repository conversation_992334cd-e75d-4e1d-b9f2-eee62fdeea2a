Path: mdn-web-docs > files > en-us > glossary > object_reference > index.md

Path: mdn-web-docs > files > en-us > glossary > object_reference > index.md Path: mdn-web-docs > files > en-us > glossary > object_reference > index.md Path: mdn-web-docs > files > en-us > glossary > object_reference > index.md Path: mdn-web-docs > files > en-us > glossary > object_reference > index.md --- title: Object reference slug: Glossary/Object_reference page-type: glossary-definition --- {{GlossarySidebar}} An **object reference** is a link to an _{{glossary("object")}}_. Object references can be used exactly like the linked objects. The concept of object references becomes clear when assigning the same object to more than one _{{Glossary("property/javascript", "property")}}_. Rather than holding a copy of the object, each assigned property holds object references that link to the same object so that when the object changes, all properties referring to the object reflect the change. ## See also - [Reference (computer science)](<https://en.wikipedia.org/wiki/Reference_(computer_science)>) on Wikipedia