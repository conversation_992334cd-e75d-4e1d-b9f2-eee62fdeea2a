Path: mdn-web-docs > files > en-us > glossary > oop > index.md

Path: mdn-web-docs > files > en-us > glossary > oop > index.md Path: mdn-web-docs > files > en-us > glossary > oop > index.md Path: mdn-web-docs > files > en-us > glossary > oop > index.md Path: mdn-web-docs > files > en-us > glossary > oop > index.md --- title: OOP slug: Glossary/OOP page-type: glossary-definition --- {{GlossarySidebar}} **OOP** (Object-Oriented Programming) is an approach in programming in which data is encapsulated within **{{glossary("object","objects")}}** and the object itself is operated on, rather than its component parts. {{glossary("JavaScript")}} is heavily object-oriented. It follows a [**prototype**-based model](/en-US/docs/Web/JavaScript/Guide/Inheritance_and_the_prototype_chain), but it also offers a [class syntax](/en-US/docs/Web/JavaScript/Guide/Using_classes) to enable typical OOP paradigms. ## See also - [Object-oriented programming](https://en.wikipedia.org/wiki/Object-oriented_programming) on Wikipedia - [Introduction to object-oriented JavaScript](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects) - [Inheritance and the prototype chain](/en-US/docs/Web/JavaScript/Guide/Inheritance_and_the_prototype_chain)