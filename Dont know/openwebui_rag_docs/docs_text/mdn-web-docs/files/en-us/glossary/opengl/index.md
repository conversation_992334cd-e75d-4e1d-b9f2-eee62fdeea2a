Path: mdn-web-docs > files > en-us > glossary > opengl > index.md

Path: mdn-web-docs > files > en-us > glossary > opengl > index.md Path: mdn-web-docs > files > en-us > glossary > opengl > index.md Path: mdn-web-docs > files > en-us > glossary > opengl > index.md Path: mdn-web-docs > files > en-us > glossary > opengl > index.md --- title: OpenGL slug: Glossary/OpenGL page-type: glossary-definition --- {{GlossarySidebar}} **OpenGL** (**Open Graphics Library**) is a cross-language, multi-platform application programming interface (API) for rendering 2D and 3D vector graphics. The API is typically used to interact with a graphics processing unit (GPU), to achieve hardware-accelerated rendering. ## See also - [OpenGL](https://en.wikipedia.org/wiki/OpenGL) on Wikipedia - [OpenGL](https://www.opengl.org/)