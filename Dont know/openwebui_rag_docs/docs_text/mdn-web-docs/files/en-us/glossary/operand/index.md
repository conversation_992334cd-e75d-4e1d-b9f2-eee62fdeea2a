Path: mdn-web-docs > files > en-us > glossary > operand > index.md

Path: mdn-web-docs > files > en-us > glossary > operand > index.md Path: mdn-web-docs > files > en-us > glossary > operand > index.md Path: mdn-web-docs > files > en-us > glossary > operand > index.md Path: mdn-web-docs > files > en-us > glossary > operand > index.md --- title: Operand slug: Glossary/Operand page-type: glossary-definition --- {{GlossarySidebar}} An **operand** is the part of an instruction representing the data manipulated by the {{glossary("operator")}}. For example, when you add two numbers, the numbers are the operand and "+" is the operator. ## See also - [Operand](https://en.wikipedia.org/wiki/Operand) on Wikipedia