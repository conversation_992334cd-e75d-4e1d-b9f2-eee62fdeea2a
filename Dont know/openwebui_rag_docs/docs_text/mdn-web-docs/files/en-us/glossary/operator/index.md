Path: mdn-web-docs > files > en-us > glossary > operator > index.md

Path: mdn-web-docs > files > en-us > glossary > operator > index.md Path: mdn-web-docs > files > en-us > glossary > operator > index.md Path: mdn-web-docs > files > en-us > glossary > operator > index.md Path: mdn-web-docs > files > en-us > glossary > operator > index.md --- title: Operator slug: Glossary/Operator page-type: glossary-definition --- {{GlossarySidebar}} Reserved **syntax** consisting of punctuation or alphanumeric characters that carries out built-in functionality. For example, in JavaScript the addition operator ("+") adds numbers together and concatenates strings, whereas the "not" operator ("!") negates an expression for example making a `true` statement return `false`. ## See also - [Operator (computer programming)](<https://en.wikipedia.org/wiki/Operator_(computer_programming)>) on Wikipedia - [JavaScript operators](/en-US/docs/Web/JavaScript/Reference/Operators)