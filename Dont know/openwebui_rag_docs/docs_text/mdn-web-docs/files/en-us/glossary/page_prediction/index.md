Path: mdn-web-docs > files > en-us > glossary > page_prediction > index.md

Path: mdn-web-docs > files > en-us > glossary > page_prediction > index.md Path: mdn-web-docs > files > en-us > glossary > page_prediction > index.md Path: mdn-web-docs > files > en-us > glossary > page_prediction > index.md Path: mdn-web-docs > files > en-us > glossary > page_prediction > index.md --- title: Page prediction slug: Glossary/Page_prediction page-type: glossary-definition --- {{GlossarySidebar}} **Page Prediction** is a browser feature or script which, when enabled, tells the browser to download resources the user is likely to visit before the user requests the content. Page prediction improves performance by enabling almost instant loading of predicted content. However, page prediction may also download content a user does not seek. Some web applications include a prediction feature completing search text and address bar URLs based on browsing history and related searches. For example, as the user types in the address bar, the browser might send the current text in the address bar to the search engine before the user submits the request. Although browser page prediction and prediction services enable faster page loads, they consume additional bandwidth. Also, pre-loaded websites and embedded content can set and read their cookies as if they were visited even if they weren't. ## See also - Related glossary terms: - {{Glossary("prerender")}} - {{Glossary("prefetch")}}