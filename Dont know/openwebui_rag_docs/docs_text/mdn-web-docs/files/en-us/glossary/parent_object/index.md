Path: mdn-web-docs > files > en-us > glossary > parent_object > index.md

Path: mdn-web-docs > files > en-us > glossary > parent_object > index.md Path: mdn-web-docs > files > en-us > glossary > parent_object > index.md Path: mdn-web-docs > files > en-us > glossary > parent_object > index.md Path: mdn-web-docs > files > en-us > glossary > parent_object > index.md --- title: Parent object slug: Glossary/Parent_object page-type: glossary-definition --- {{GlossarySidebar}} The {{glossary("object")}} to which a given {{glossary("property")}} or {{glossary("method")}} belongs. ## See also - [Discussion of Inheritance and prototypes in JavaScript](/en-US/docs/Web/JavaScript/Guide/Inheritance_and_the_prototype_chain)