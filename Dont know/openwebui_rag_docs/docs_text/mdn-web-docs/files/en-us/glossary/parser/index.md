Path: mdn-web-docs > files > en-us > glossary > parser > index.md

Path: mdn-web-docs > files > en-us > glossary > parser > index.md Path: mdn-web-docs > files > en-us > glossary > parser > index.md Path: mdn-web-docs > files > en-us > glossary > parser > index.md Path: mdn-web-docs > files > en-us > glossary > parser > index.md --- title: Parser slug: Glossary/Parser page-type: glossary-definition --- {{GlossarySidebar}} A **parser** is the module of a compiler or interpreter that {{glossary("parse","parses")}} a source code file. More generally, it's a piece of software that parses text and transforms its content to another representation. ## See also - [Parser](https://en.wikipedia.org/wiki/Parsing#Parser) on Wikipedia