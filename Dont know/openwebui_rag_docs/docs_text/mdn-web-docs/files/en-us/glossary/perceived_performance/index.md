Path: mdn-web-docs > files > en-us > glossary > perceived_performance > index.md

Path: mdn-web-docs > files > en-us > glossary > perceived_performance > index.md Path: mdn-web-docs > files > en-us > glossary > perceived_performance > index.md Path: mdn-web-docs > files > en-us > glossary > perceived_performance > index.md Path: mdn-web-docs > files > en-us > glossary > perceived_performance > index.md --- title: Perceived performance slug: Glossary/Perceived_performance page-type: glossary-definition --- {{GlossarySidebar}} **Perceived performance** is a measure of how fast, responsive, and reliable a website _feels_ to its users. The perception of how well a site is performing can have more impact on the user experience than the actual load and response times. ## See also - [Perceived performance](/en-US/docs/Learn_web_development/Extensions/Performance/Perceived_performance)