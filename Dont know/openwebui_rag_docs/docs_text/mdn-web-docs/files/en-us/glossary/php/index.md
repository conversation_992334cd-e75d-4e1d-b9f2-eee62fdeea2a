Path: mdn-web-docs > files > en-us > glossary > php > index.md

Path: mdn-web-docs > files > en-us > glossary > php > index.md Path: mdn-web-docs > files > en-us > glossary > php > index.md Path: mdn-web-docs > files > en-us > glossary > php > index.md Path: mdn-web-docs > files > en-us > glossary > php > index.md --- title: PHP slug: Glossary/PHP page-type: glossary-definition --- {{GlossarySidebar}} PHP (a recursive initialism for PHP: Hypertext Preprocessor) is an open-source server-side scripting language that can be embedded into HTML to build web applications and dynamic websites. ## Examples ### Basic syntax ```php // start of PHP code <?php // PHP code goes here ?> // end of PHP code ``` ### Printing data on screen ```php <?php echo "Hello World!"; ?> ``` ### PHP variables ```php <?php // variables $name='<PERSON><PERSON>'; $surname='<PERSON>'; $country='Brasil'; $email='<EMAIL>'; // printing the variables echo $name; echo $surname; echo $country; echo $email; ?> ``` ## See also - [Official website](https://www.php.net/) - [PHP](https://en.wikipedia.org/wiki/PHP) on Wikipedia - [PHP programming](https://en.wikibooks.org/wiki/PHP_Programming) on Wikibooks - Related glossary terms: - {{Glossary("Java")}} - {{Glossary("JavaScript")}} - {{Glossary("Python")}} - {{Glossary("Ruby")}}