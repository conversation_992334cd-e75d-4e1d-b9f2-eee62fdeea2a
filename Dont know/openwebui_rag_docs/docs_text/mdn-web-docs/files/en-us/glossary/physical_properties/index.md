Path: mdn-web-docs > files > en-us > glossary > physical_properties > index.md

Path: mdn-web-docs > files > en-us > glossary > physical_properties > index.md Path: mdn-web-docs > files > en-us > glossary > physical_properties > index.md Path: mdn-web-docs > files > en-us > glossary > physical_properties > index.md Path: mdn-web-docs > files > en-us > glossary > physical_properties > index.md --- title: Physical properties slug: Glossary/Physical_properties page-type: glossary-definition --- {{GlossarySidebar}} CSS **physical properties** define positions based on physical directions or element sides. For example: - The {{Glossary("inset properties")}} {{cssxref("top")}}, {{cssxref("right")}}, {{cssxref("bottom")}}, and {{cssxref("left")}} refer to the physical dimensions of the viewport. - Features such as {{cssxref("margin-top")}}, {{cssxref("border-right")}}, {{cssxref("padding-bottom")}}, and {{cssxref("border-bottom-left-radius")}} reference specific sides of an element, and style features by that physical direction. This is opposed to {{glossary("logical properties")}}, which are relative to the content flow and use directional keywords relative to the block and inline axes. ## See also - [CSS positioned layout](/en-US/docs/Web/CSS/CSS_positioned_layout) module - [CSS box model](/en-US/docs/Web/CSS/CSS_box_model) module - [CSS box sizing](/en-US/docs/Web/CSS/CSS_box_sizing) module