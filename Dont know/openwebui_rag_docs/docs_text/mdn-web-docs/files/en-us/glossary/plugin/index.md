Path: mdn-web-docs > files > en-us > glossary > plugin > index.md

Path: mdn-web-docs > files > en-us > glossary > plugin > index.md Path: mdn-web-docs > files > en-us > glossary > plugin > index.md Path: mdn-web-docs > files > en-us > glossary > plugin > index.md Path: mdn-web-docs > files > en-us > glossary > plugin > index.md --- title: Plugin slug: Glossary/Plugin page-type: glossary-definition --- {{GlossarySidebar}} A browser plugin is a software component that users can install to handle content that the browser can't support natively. Browser plugins are usually written using the [NPAPI](https://en.wikipedia.org/wiki/NPAPI) (Netscape Plugin Application Programming Interface) architecture. The most well-known and widely used plugin was the now outdated Adobe Flash player, which enabled browsers to run {{Glossary("Adobe Flash")}} content. As browsers have become more powerful, plugins have become less useful. Plugins also have a history of causing security and performance problems for web users. Between 2016 and 2021 browser vendors worked on a deprecation roadmap for plugins and in particular for Adobe Flash, and today plugins are no longer supported by any major browsers. Plugins should not be confused with browser extensions, which unlike plugins are distributed as source code rather than binaries, and which are still supported by browsers, notably using the {{Glossary("WebExtensions")}} system. ## See also - [Adobe Flash end-of-life announcement](https://blog.adobe.com/en/publish/2017/07/25/adobe-flash-update#gs.g8mmgf)