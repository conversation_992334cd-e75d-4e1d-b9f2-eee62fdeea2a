Path: mdn-web-docs > files > en-us > glossary > polymorphism > index.md

Path: mdn-web-docs > files > en-us > glossary > polymorphism > index.md Path: mdn-web-docs > files > en-us > glossary > polymorphism > index.md Path: mdn-web-docs > files > en-us > glossary > polymorphism > index.md Path: mdn-web-docs > files > en-us > glossary > polymorphism > index.md --- title: Polymorphism slug: Glossary/Polymorphism page-type: glossary-definition --- {{GlossarySidebar}} Polymorphism is the presentation of one interface for multiple data types. For example, integers, floats, and doubles are implicitly polymorphic: regardless of their different types, they can all be added, subtracted, multiplied, and so on. In the case of {{glossary("OOP")}}, by making the {{glossary("class")}} responsible for its code as well as its own data, polymorphism can be achieved in that each class has its own {{glossary("function")}} that (once called) behaves properly for any {{glossary("object")}}. ## See also - [Polymorphism](https://en.wikipedia.org/wiki/Polymorphism_%28computer_science%29) on Wikipedia