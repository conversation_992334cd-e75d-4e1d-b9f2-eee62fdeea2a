Path: mdn-web-docs > files > en-us > glossary > pop > index.md

Path: mdn-web-docs > files > en-us > glossary > pop > index.md Path: mdn-web-docs > files > en-us > glossary > pop > index.md Path: mdn-web-docs > files > en-us > glossary > pop > index.md Path: mdn-web-docs > files > en-us > glossary > pop > index.md --- title: POP3 slug: Glossary/POP page-type: glossary-definition --- {{GlossarySidebar}} **POP3** (Post Office Protocol) is a very common {{glossary("protocol")}} for getting emails from a mail server over a {{glossary("TCP")}} connection. POP3 does not support folders, unlike the more recent {{Glossary("IMAP")}}, which is harder to implement because of its more complex structure. Clients usually retrieve all messages and then delete them from the server, but POP3 does allow retaining a copy on the server. Nearly all email servers and clients currently support POP3. ## See also - [POP](https://en.wikipedia.org/wiki/Post_Office_Protocol) on Wikipedia - [RFC 1734](https://datatracker.ietf.org/doc/html/rfc1734) (Specification of POP3 authentication mechanism) - [RFC 1939](https://datatracker.ietf.org/doc/html/rfc1939) (Specification of POP3) - [RFC 2449](https://datatracker.ietf.org/doc/html/rfc2449) (Specification of POP3 extension mechanism) - Related glossary terms: - {{Glossary("IMAP")}}