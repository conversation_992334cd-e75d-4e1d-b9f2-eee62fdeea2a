Path: mdn-web-docs > files > en-us > glossary > port > index.md

Path: mdn-web-docs > files > en-us > glossary > port > index.md Path: mdn-web-docs > files > en-us > glossary > port > index.md Path: mdn-web-docs > files > en-us > glossary > port > index.md Path: mdn-web-docs > files > en-us > glossary > port > index.md --- title: Port slug: Glossary/Port page-type: glossary-definition --- {{GlossarySidebar}} For a computer connected to a network with an {{Glossary("IP address")}}, a **port** is a communication endpoint. Ports are designated by numbers, and below 1024 each port is associated by default with a specific {{Glossary("protocol")}}. For example, the default port for the {{Glossary("HTTP")}} protocol is 80 and the default port for the HTTPS protocol is 443, so a {{Glossary("HTTP")}} server waits for requests on those ports. Each Internet protocol is associated with a default port: {{Glossary("SMTP")}} (25), {{Glossary("POP")}} (110), {{Glossary("IMAP")}} (143), {{Glossary("IRC")}} (194), and so on. ## See also - [Port](<https://en.wikipedia.org/wiki/Port_(computer_networking)>) on Wikipedia