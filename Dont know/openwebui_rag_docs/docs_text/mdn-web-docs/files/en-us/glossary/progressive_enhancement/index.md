Path: mdn-web-docs > files > en-us > glossary > progressive_enhancement > index.md

Path: mdn-web-docs > files > en-us > glossary > progressive_enhancement > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_enhancement > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_enhancement > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_enhancement > index.md --- title: Progressive Enhancement slug: Glossary/Progressive_Enhancement page-type: glossary-definition --- {{GlossarySidebar}} **Progressive enhancement** is a design philosophy that provides a baseline of essential content and functionality to as many users as possible, while delivering the best possible experience only to users of the most modern browsers that can run all the required code. The word _progressive_ in _progressive enhancement_ means creating a design that achieves a simpler-but-still-usable experience for users of older browsers and devices with limited capabilities, while at the same time being a design that **progresses the user experience up** to a more-compelling, fully-featured experience for users of newer browsers and devices with richer capabilities. [Feature detection](/en-US/docs/Learn_web_development/Extensions/Testing/Feature_detection) is generally used to determine whether browsers can handle more modern functionality, while {{Glossary("polyfill", "polyfills")}} are often used to add missing features with JavaScript. Special notice should be taken of accessibility. Acceptable alternatives should be provided where possible. Progressive enhancement is a useful technique that allows web developers to focus on developing the best possible websites while making those websites work on multiple unknown user agents. {{Glossary("Graceful degradation")}} is related but is not the same thing and is often seen as going in the opposite direction to progressive enhancement. In reality both approaches are valid and can often complement one another. ## See also - [Progressive enhancement](https://en.wikipedia.org/wiki/Progressive_enhancement) at Wikipedia - [What is Progressive Enhancement, and why it matters](https://www.freecodecamp.org/news/what-is-progressive-enhancement-and-why-it-matters-e80c7aaf834a/) at freeCodeCamp (2018) - [Progressive Enhancement](https://www.quirksmode.org/blog/archives/2021/02/progressive_enh_1.html) at QuirksMode (2021) - [The Power of Progressive Enhancement](https://archive.hankchizljaw.com/wrote/the-power-of-progressive-enhancement/) at Piccalilli (2018)