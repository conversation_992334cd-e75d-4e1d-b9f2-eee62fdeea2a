Path: mdn-web-docs > files > en-us > glossary > progressive_web_apps > index.md

Path: mdn-web-docs > files > en-us > glossary > progressive_web_apps > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_web_apps > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_web_apps > index.md Path: mdn-web-docs > files > en-us > glossary > progressive_web_apps > index.md --- title: Progressive web apps slug: Glossary/Progressive_web_apps page-type: glossary-definition --- {{GlossarySidebar}} Progressive web applications (PWA) are applications that are built using web platform technologies, but that provide a user experience like that of a platform-specific app. These kinds of apps enjoy all the best parts of the Web such as discoverability via search engines, being linkable via {{Glossary("URL")}}s, and working across multiple form factors, but are progressively enhanced with modern APIs (such as [Service Workers](/en-US/docs/Web/API/Service_Worker_API) and [Push](/en-US/docs/Web/API/Push_API)). These features include being installable, working offline, and being easy to sync with and re-engage the user from the server. ## See also - [Progressive web apps](/en-US/docs/Web/Progressive_web_apps) on MDN - [Progressive web apps](https://web.dev/explore/progressive-web-apps) on web.dev