Path: mdn-web-docs > files > en-us > glossary > property > css > index.md

Path: mdn-web-docs > files > en-us > glossary > property > css > index.md Path: mdn-web-docs > files > en-us > glossary > property > css > index.md Path: mdn-web-docs > files > en-us > glossary > property > css > index.md Path: mdn-web-docs > files > en-us > glossary > property > css > index.md --- title: Property (CSS) slug: Glossary/Property/CSS page-type: glossary-definition --- {{GlossarySidebar}} A **CSS property** is a characteristic (like color) whose associated value defines one aspect of how the browser should display the element. Here's an example of a CSS rule: ```css /* "div" is a selector indicating that all the div elements */ /* in the document will be styled by that rule */ div { /* The property "color" with the value "black" indicates */ /* that the text will have the color black */ color: black; /* The property "background-color" with the value "white" indicates */ /* that the background color of the elements will be white */ background-color: white; } ``` ## See also - [Learn CSS](/en-US/docs/Learn_web_development/Core/Styling_basics) - [The CSS reference on MDN](/en-US/docs/Web/CSS/Reference) - [The CSS Working Group current work](https://www.w3.org/Style/CSS/current-work)