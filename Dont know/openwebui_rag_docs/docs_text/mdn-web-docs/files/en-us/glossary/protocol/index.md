Path: mdn-web-docs > files > en-us > glossary > protocol > index.md

Path: mdn-web-docs > files > en-us > glossary > protocol > index.md Path: mdn-web-docs > files > en-us > glossary > protocol > index.md Path: mdn-web-docs > files > en-us > glossary > protocol > index.md Path: mdn-web-docs > files > en-us > glossary > protocol > index.md --- title: Protocol slug: Glossary/Protocol page-type: glossary-definition --- {{GlossarySidebar}} A **protocol** is a system of rules that define how data is exchanged within or between computers. Communications between devices require that the devices agree on the format of the data that is being exchanged. The set of rules that defines a format is called a protocol. ## See also - [Communications protocol](https://en.wikipedia.org/wiki/Communications_protocol) on Wikipedia - [RFC Official Internet Protocol Standards](https://www.rfc-editor.org/standards) - [HTTP overview](/en-US/docs/Web/HTTP/Guides/Overview) - Related glossary terms: - {{glossary("TCP")}} - {{glossary("Packet")}}