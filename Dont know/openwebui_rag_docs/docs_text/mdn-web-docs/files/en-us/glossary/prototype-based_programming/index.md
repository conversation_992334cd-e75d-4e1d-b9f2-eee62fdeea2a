Path: mdn-web-docs > files > en-us > glossary > prototype-based_programming > index.md

Path: mdn-web-docs > files > en-us > glossary > prototype-based_programming > index.md Path: mdn-web-docs > files > en-us > glossary > prototype-based_programming > index.md Path: mdn-web-docs > files > en-us > glossary > prototype-based_programming > index.md Path: mdn-web-docs > files > en-us > glossary > prototype-based_programming > index.md --- title: Prototype-based programming slug: Glossary/Prototype-based_programming page-type: glossary-definition --- {{GlossarySidebar}} **Prototype-based programming** is a style of {{Glossary("OOP", "object-oriented programming")}} in which {{Glossary('Class', 'classes')}} are not explicitly defined, but rather derived by adding properties and methods to an instance of another class or, less frequently, adding them to an empty object. In simple words: this type of style allows the creation of an {{Glossary('Object', 'object')}} without first defining its {{Glossary('Class', 'class')}}. ## See also - [Prototype-based programming](https://en.wikipedia.org/wiki/Prototype-based_programming) on Wikipedia