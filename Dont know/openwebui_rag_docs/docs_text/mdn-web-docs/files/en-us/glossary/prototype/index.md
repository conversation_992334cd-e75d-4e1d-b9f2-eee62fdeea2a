Path: mdn-web-docs > files > en-us > glossary > prototype > index.md

Path: mdn-web-docs > files > en-us > glossary > prototype > index.md Path: mdn-web-docs > files > en-us > glossary > prototype > index.md Path: mdn-web-docs > files > en-us > glossary > prototype > index.md Path: mdn-web-docs > files > en-us > glossary > prototype > index.md --- title: Prototype slug: Glossary/Prototype page-type: glossary-definition --- {{GlossarySidebar}} A prototype is a model that displays the appearance and behavior of an application or product early in the development lifecycle. See [Inheritance and the prototype chain](/en-US/docs/Web/JavaScript/Guide/Inheritance_and_the_prototype_chain) ## See also - [Software Prototyping](https://en.wikipedia.org/wiki/Software_Prototyping) on Wikipedia