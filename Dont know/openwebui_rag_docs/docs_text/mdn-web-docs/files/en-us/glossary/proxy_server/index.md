Path: mdn-web-docs > files > en-us > glossary > proxy_server > index.md

Path: mdn-web-docs > files > en-us > glossary > proxy_server > index.md Path: mdn-web-docs > files > en-us > glossary > proxy_server > index.md Path: mdn-web-docs > files > en-us > glossary > proxy_server > index.md Path: mdn-web-docs > files > en-us > glossary > proxy_server > index.md --- title: Proxy server slug: Glossary/Proxy_server page-type: glossary-definition --- {{GlossarySidebar}} A **proxy server** is an intermediate program or computer used when navigating through different networks of the Internet. They facilitate access to content on the World Wide Web. A proxy intercepts requests and serves back responses; it may forward the requests, or not (for example in the case of a cache), and it may modify it (for example changing its headers, at the boundary between two networks). A proxy can be on the user's local computer, or anywhere between the user's computer and a destination server on the Internet. In general there are two main types of proxy servers: - A **forward proxy** that handles requests from and to anywhere on the Internet. - A **reverse proxy** taking requests from the Internet and forwarding them to servers in an internal network. ## See also - [Proxy servers and tunneling](/en-US/docs/Web/HTTP/Guides/Proxy_servers_and_tunneling) - [Proxy server](https://en.wikipedia.org/wiki/Proxy_server) on Wikipedia