Path: mdn-web-docs > files > en-us > glossary > pseudo-class > index.md

Path: mdn-web-docs > files > en-us > glossary > pseudo-class > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-class > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-class > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-class > index.md --- title: Pseudo-class slug: Glossary/Pseudo-class page-type: glossary-definition --- {{GlossarySidebar}} In CSS, a **pseudo-class** selector targets elements depending on their state rather than on information from the document tree. For example, the selector {{cssxref(":visited", "a:visited")}} applies styles only to links that the user has already followed. ## See also - [Pseudo-class documentation](/en-US/docs/Web/CSS/Pseudo-classes)