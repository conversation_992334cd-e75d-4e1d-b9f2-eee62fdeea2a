Path: mdn-web-docs > files > en-us > glossary > pseudo-element > index.md

Path: mdn-web-docs > files > en-us > glossary > pseudo-element > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-element > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-element > index.md Path: mdn-web-docs > files > en-us > glossary > pseudo-element > index.md --- title: Pseudo-element slug: Glossary/Pseudo-element page-type: glossary-definition --- {{GlossarySidebar}} In CSS, a **pseudo-element** selector applies styles to parts of your document content in scenarios where there isn't a specific HTML element to select. For example, rather than putting the first letter of each paragraph in its own element, you can style them all with {{Cssxref("::first-letter", "p::first-letter")}}. ## See also - [Pseudo-elements](/en-US/docs/Web/CSS/Pseudo-elements)