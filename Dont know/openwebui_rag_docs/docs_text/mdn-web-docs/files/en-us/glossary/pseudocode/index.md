Path: mdn-web-docs > files > en-us > glossary > pseudocode > index.md

Path: mdn-web-docs > files > en-us > glossary > pseudocode > index.md Path: mdn-web-docs > files > en-us > glossary > pseudocode > index.md Path: mdn-web-docs > files > en-us > glossary > pseudocode > index.md Path: mdn-web-docs > files > en-us > glossary > pseudocode > index.md --- title: Pseudocode slug: Glossary/Pseudocode page-type: glossary-definition --- {{GlossarySidebar}} Pseudocode refers to code-like syntax that is generally used to indicate to humans how some code syntax works, or illustrate the design of an item of code architecture. It **won't** work if you try to run it as code. ## See also - [Pseudocode](https://en.wikipedia.org/wiki/Pseudocode) on Wikipedia.