Path: mdn-web-docs > files > en-us > glossary > python > index.md

Path: mdn-web-docs > files > en-us > glossary > python > index.md Path: mdn-web-docs > files > en-us > glossary > python > index.md Path: mdn-web-docs > files > en-us > glossary > python > index.md Path: mdn-web-docs > files > en-us > glossary > python > index.md --- title: Python slug: Glossary/Python page-type: glossary-definition --- {{GlossarySidebar}} **Python** is a high level general-purpose programming language. It uses a multi-paradigm approach, meaning it supports procedural, object-oriented, and some functional programming constructs. It was created by <PERSON> as a successor to another language (called ABC) between 1985 and 1990, and is currently used on a large array of domains like web development, desktop applications, data science, DevOps, and automation/productivity. Python is developed under an OSI-approved open source license, making it freely usable and distributable, even for commercial use. Python's license is administered by the [Python Software Foundation](https://www.python.org/psf-landing/). ## See also - [Python](<https://en.wikipedia.org/wiki/Python_(programming_language)>) on Wikipedia - [Official Python docs tutorials](https://docs.python.org/3/tutorial/index.html) - [Tutorials Point Python tutorial](https://www.tutorialspoint.com/python/index.htm) - [AlphaCodingSkills Python Tutorial](https://www.alphacodingskills.com/python/python-tutorial.php) - [Django Web Framework (Python)](/en-US/docs/Learn_web_development/Extensions/Server-side/Django) on MDN - Related glossary terms: - {{Glossary("Java")}} - {{Glossary("JavaScript")}} - {{Glossary("PHP")}} - {{Glossary("Ruby")}}