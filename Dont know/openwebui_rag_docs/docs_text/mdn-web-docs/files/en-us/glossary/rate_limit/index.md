Path: mdn-web-docs > files > en-us > glossary > rate_limit > index.md

Path: mdn-web-docs > files > en-us > glossary > rate_limit > index.md Path: mdn-web-docs > files > en-us > glossary > rate_limit > index.md Path: mdn-web-docs > files > en-us > glossary > rate_limit > index.md Path: mdn-web-docs > files > en-us > glossary > rate_limit > index.md --- title: Rate limit slug: Glossary/Rate_limit page-type: glossary-definition --- {{GlossarySidebar}} In computing, especially in networking, **rate limiting** means controlling how many operations can be performed in a given amount of time, usually to avoid overloading the system and causing performance degradation. For example, a server might limit the number of requests it will accept from a single client in a given time period, which not only optimizes the server's overall performance but also mitigates attacks like {{Glossary("Denial of Service", "DoS attack")}}. Rate limiting is typically synonymous with {{glossary("throttle", "throttling")}}, although {{glossary("debounce", "debouncing")}} is another viable strategy which provides better semantics and user experience in certain cases. ## See also - Glossary terms: - {{Glossary("Debounce")}} - {{Glossary("Throttle")}} - {{HTTPStatus("429", "429 Too Many Requests")}} - [What is rate limiting? | Rate limiting and bots](https://www.cloudflare.com/en-gb/learning/bots/what-is-rate-limiting/) on cloudflare.com