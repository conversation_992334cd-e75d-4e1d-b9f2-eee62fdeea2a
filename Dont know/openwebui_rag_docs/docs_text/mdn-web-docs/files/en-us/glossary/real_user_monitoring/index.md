Path: mdn-web-docs > files > en-us > glossary > real_user_monitoring > index.md

Path: mdn-web-docs > files > en-us > glossary > real_user_monitoring > index.md Path: mdn-web-docs > files > en-us > glossary > real_user_monitoring > index.md Path: mdn-web-docs > files > en-us > glossary > real_user_monitoring > index.md Path: mdn-web-docs > files > en-us > glossary > real_user_monitoring > index.md --- title: Real User Monitoring (RUM) slug: Glossary/Real_User_Monitoring page-type: glossary-definition --- {{GlossarySidebar}} **Real User Monitoring** or RUM measures the performance of a page from real users' machines. Generally, a third party script injects a script on each page to measure and report page load data for every request made. This technique monitors an application's actual user interactions. In RUM, the third party script collects performance metrics from the real users' browsers. RUM helps identify how an application is being used, including the geographic distribution of users and the impact of that distribution on the end user experience. ## See also - [Real User Monitoring (RUM) versus Synthetic Monitoring](/en-US/docs/Web/Performance/Guides/Rum-vs-Synthetic) - Related glossary terms: - {{Glossary("Synthetic monitoring")}} - {{Glossary("Beacon")}}