Path: mdn-web-docs > files > en-us > glossary > reflow > index.md

Path: mdn-web-docs > files > en-us > glossary > reflow > index.md Path: mdn-web-docs > files > en-us > glossary > reflow > index.md Path: mdn-web-docs > files > en-us > glossary > reflow > index.md Path: mdn-web-docs > files > en-us > glossary > reflow > index.md --- title: Reflow slug: Glossary/Reflow page-type: glossary-definition --- {{GlossarySidebar}} **Reflow** happens when a {{glossary("browser")}} recalculates the position and geometry of certain parts of a webpage, such as after an update on an interactive site. This tends to be followed by repainting, which is when the browser redraws the webpage to show the resulting visual updates. ## See also - Related glossary terms: - {{Glossary("Repaint")}} - [Minimizing browser reflow](https://developers.google.com/speed/docs/insights/browser-reflow) on developers.google.com - [Understanding Reflow and Repaint in the browser](https://dev.to/gopal1996/understanding-reflow-and-repaint-in-the-browser-1jbg) on dev.to (2020)