Path: mdn-web-docs > files > en-us > glossary > regular_expression > index.md

Path: mdn-web-docs > files > en-us > glossary > regular_expression > index.md Path: mdn-web-docs > files > en-us > glossary > regular_expression > index.md Path: mdn-web-docs > files > en-us > glossary > regular_expression > index.md Path: mdn-web-docs > files > en-us > glossary > regular_expression > index.md --- title: Regular expression slug: Glossary/Regular_expression page-type: glossary-definition --- {{GlossarySidebar}} **Regular expressions** (or _regex_) are rules that govern which sequences of characters come up in a search. Regular expressions are implemented in various languages, but the best-known implementation is the Perl Implementation, which has given rise to its own ecosystem of implementations called PCRE (_Perl Compatible Regular Expression_). On the Web, {{glossary("JavaScript")}} provides another regex implementation through the {{jsxref("RegExp")}} object. ## See also - [Regular expressions](https://en.wikipedia.org/wiki/Regular_expressions) on Wikipedia - [Interactive tutorial](https://regexone.com/) - [Visualized Regular Expression](https://regexper.com/) - [Writing regular expressions in JavaScript](/en-US/docs/Web/JavaScript/Guide/Regular_expressions)