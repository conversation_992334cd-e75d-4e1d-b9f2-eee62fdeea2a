Path: mdn-web-docs > files > en-us > glossary > relying_party > index.md

Path: mdn-web-docs > files > en-us > glossary > relying_party > index.md Path: mdn-web-docs > files > en-us > glossary > relying_party > index.md Path: mdn-web-docs > files > en-us > glossary > relying_party > index.md Path: mdn-web-docs > files > en-us > glossary > relying_party > index.md --- title: Relying party slug: Glossary/Relying_party page-type: glossary-definition --- {{GlossarySidebar}} A **relying party** is an entity that needs to control access to a resource and, to do so, needs to {{glossary("authentication", "authenticate")}} other entities that are trying to access that resource. On the web, a relying party is usually a website that allows users to sign in and needs to authenticate users (for example by checking a password) before deciding whether to grant them access. The website _relies on_ the validity of the credentials the browser presents when it grants access to its resources. ## See also - Related glossary terms: - {{glossary("Federated identity")}} - {{glossary("Identity provider")}}