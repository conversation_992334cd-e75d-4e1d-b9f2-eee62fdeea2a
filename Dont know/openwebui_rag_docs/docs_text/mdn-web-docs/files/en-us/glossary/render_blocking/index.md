Path: mdn-web-docs > files > en-us > glossary > render_blocking > index.md

Path: mdn-web-docs > files > en-us > glossary > render_blocking > index.md Path: mdn-web-docs > files > en-us > glossary > render_blocking > index.md Path: mdn-web-docs > files > en-us > glossary > render_blocking > index.md Path: mdn-web-docs > files > en-us > glossary > render_blocking > index.md --- title: Render-blocking slug: Glossary/Render_blocking page-type: glossary-definition --- {{GlossarySidebar}} **Render-blocking** refers to any part of the process of loading a website that blocks the rendering of the user interface. Render-blocking is bad for web performance because it increases the length of time until users can interact with the site for example, viewing content or interacting with controls. The most common causes of render-blocking are initially-loaded CSS or JavaScript files. ## See also - [CSS performance optimization](/en-US/docs/Learn_web_development/Extensions/Performance/CSS) - [JavaScript performance optimization](/en-US/docs/Learn_web_development/Extensions/Performance/JavaScript)