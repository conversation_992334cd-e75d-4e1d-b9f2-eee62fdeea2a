Path: mdn-web-docs > files > en-us > glossary > repaint > index.md

Path: mdn-web-docs > files > en-us > glossary > repaint > index.md Path: mdn-web-docs > files > en-us > glossary > repaint > index.md Path: mdn-web-docs > files > en-us > glossary > repaint > index.md Path: mdn-web-docs > files > en-us > glossary > repaint > index.md --- title: Repaint slug: Glossary/Repaint page-type: glossary-definition --- {{GlossarySidebar}} **Repaint** happens when a {{glossary("browser")}} redraws a web page to show the visual updates resulting from a UI change, such as after an update on an interactive site. This usually follows reflowing, which is when the browser recalculates the position and geometry of certain parts of a web page. ## See also - Related glossary terms: - {{Glossary("Reflow")}} - [Understanding Reflow and Repaint in the browser](https://dev.to/gopal1996/understanding-reflow-and-repaint-in-the-browser-1jbg) on dev.to (2020)