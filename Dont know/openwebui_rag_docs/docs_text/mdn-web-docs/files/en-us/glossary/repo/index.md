Path: mdn-web-docs > files > en-us > glossary > repo > index.md

Path: mdn-web-docs > files > en-us > glossary > repo > index.md Path: mdn-web-docs > files > en-us > glossary > repo > index.md Path: mdn-web-docs > files > en-us > glossary > repo > index.md Path: mdn-web-docs > files > en-us > glossary > repo > index.md --- title: Repo slug: Glossary/Repo page-type: glossary-definition --- {{GlossarySidebar}} In a revision control system like {{Glossary("Git")}} or {{Glossary("SVN")}}, a repo (i.e., "repository") is a place that hosts an application's code source, together with various metadata. ## See also - [Repository](https://en.wikipedia.org/wiki/Repository_%28revision_control%29) on Wikipedia