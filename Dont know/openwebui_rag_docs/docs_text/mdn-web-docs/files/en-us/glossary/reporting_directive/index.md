Path: mdn-web-docs > files > en-us > glossary > reporting_directive > index.md

Path: mdn-web-docs > files > en-us > glossary > reporting_directive > index.md Path: mdn-web-docs > files > en-us > glossary > reporting_directive > index.md Path: mdn-web-docs > files > en-us > glossary > reporting_directive > index.md Path: mdn-web-docs > files > en-us > glossary > reporting_directive > index.md --- title: Reporting directive slug: Glossary/Reporting_directive page-type: glossary-definition --- {{GlossarySidebar}} **{{Glossary("CSP")}} reporting directives** are used in a {{HTTPHeader("Content-Security-Policy")}} header and control the reporting process of CSP violations. See [Reporting directives](/en-US/docs/Web/HTTP/Reference/Headers/Content-Security-Policy#reporting_directives) for a complete list. ## See also - Related glossary terms: - {{Glossary("CSP")}} - {{Glossary("Fetch directive")}} - {{Glossary("Document directive")}} - {{Glossary("Navigation directive")}} - Reference - <https://www.w3.org/TR/CSP/#directives-reporting> - {{HTTPHeader("Content-Security-Policy/upgrade-insecure-requests", "upgrade-insecure-requests")}} - {{HTTPHeader("Content-Security-Policy/block-all-mixed-content", "block-all-mixed-content")}} - {{HTTPHeader("Content-Security-Policy")}}