Path: mdn-web-docs > files > en-us > glossary > resource_timing > index.md

Path: mdn-web-docs > files > en-us > glossary > resource_timing > index.md Path: mdn-web-docs > files > en-us > glossary > resource_timing > index.md Path: mdn-web-docs > files > en-us > glossary > resource_timing > index.md Path: mdn-web-docs > files > en-us > glossary > resource_timing > index.md --- title: Resource Timing slug: Glossary/Resource_Timing page-type: glossary-definition --- {{GlossarySidebar}} Diagnosing performance issues requires performance data at the granularity of the resource. The [Resource Timing API](/en-US/docs/Web/API/Performance_API/Resource_timing) is a JavaScript API that is able to capture timing information for each individual resource that is fetched when a page is loaded. ## See also - [Using the resource timing API](/en-US/docs/Web/API/Performance_API/Resource_timing) - [Server Timing](https://www.w3.org/TR/server-timing/)