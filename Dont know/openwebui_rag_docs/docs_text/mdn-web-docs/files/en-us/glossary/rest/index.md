Path: mdn-web-docs > files > en-us > glossary > rest > index.md

Path: mdn-web-docs > files > en-us > glossary > rest > index.md Path: mdn-web-docs > files > en-us > glossary > rest > index.md Path: mdn-web-docs > files > en-us > glossary > rest > index.md Path: mdn-web-docs > files > en-us > glossary > rest > index.md --- title: REST slug: Glossary/REST page-type: glossary-definition --- {{GlossarySidebar}} **REST** (Representational State Transfer) refers to a group of software architecture design constraints that bring about efficient, reliable and scalable distributed systems. The basic idea of REST is that a resource, e.g., a document, is transferred via well-recognized, language-agnostic, and reliably standardized client/server interactions. Services are deemed RESTful when they adhere to these constraints. HTTP APIs in general are sometimes colloquially referred to as RESTful APIs, RESTful services, or REST services, although they don't necessarily adhere to all REST constraints. Beginners can assume a REST API means an HTTP service that can be called using standard web libraries and tools. ## See also - [restapitutorial.com](https://www.restapitutorial.com/) - [restcookbook.com](https://restcookbook.com/) - [REST](https://en.wikipedia.org/wiki/Representational_state_transfer) on Wikipedia - [REST Architecture](https://www.service-architecture.com/articles/web-services/representational-state-transfer-rest.html)