Path: mdn-web-docs > files > en-us > glossary > router > index.md

Path: mdn-web-docs > files > en-us > glossary > router > index.md Path: mdn-web-docs > files > en-us > glossary > router > index.md Path: mdn-web-docs > files > en-us > glossary > router > index.md Path: mdn-web-docs > files > en-us > glossary > router > index.md --- title: Router slug: Glossary/Router page-type: glossary-definition --- {{GlossarySidebar}} There are three definitions for **routers** on the web: 1. For the network layer, the router is a networking device that decides where to direct {{Glossary('Packet', 'data packets')}}. 2. For a {{Glossary('SPA', 'Single-page application')}} in the application layer, a router is a library that decides what web page is presented by a given {{Glossary('URL')}}. This middleware module is used for all URL functions, as these are given a path to a file that is rendered to open the next page. 3. In the implementation of an {{Glossary('API')}} in a service layer, a router is a software component that parses a request and directs or routes the request to various handlers within a program. The router code usually accepts a response from the handler and facilitates its return to the requester. ## See also For network layer context: - [Router (computing)](<https://en.wikipedia.org/wiki/Router_(computing)>) on Wikipedia For SPA in application layer context, most of the popular SPA frameworks have their routing libraries: - [Angular router](https://angular.dev/guide/routing/common-router-tasks) - [React router](https://reactrouter.com/) - [Vue router](https://router.vuejs.org/)