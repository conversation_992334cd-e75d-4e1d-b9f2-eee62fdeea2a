Path: mdn-web-docs > files > en-us > glossary > rtl > index.md

Path: mdn-web-docs > files > en-us > glossary > rtl > index.md Path: mdn-web-docs > files > en-us > glossary > rtl > index.md Path: mdn-web-docs > files > en-us > glossary > rtl > index.md Path: mdn-web-docs > files > en-us > glossary > rtl > index.md --- title: RTL (Right to Left) slug: Glossary/RTL page-type: glossary-definition --- {{GlossarySidebar}} **RTL** (**Right To Left**) is a {{Glossary("locale")}} property indicating that text is written from right to left. For example, the `he` locale (for Hebrew) specifies right-to-left. Arabic (`ar`) is another common language written RTL. The opposite of RTL, LTR (Left To Right) is used in other languages, including English (`en`, `en-US`, `en-GB`, etc.), Spanish (`es`), and French (`fr`). ## See also - Related glossary terms: - {{Glossary("Locale")}} - {{Glossary("Localization")}} - {{Glossary("LTR")}} (Left to Right) - {{Glossary("BiDi")}}