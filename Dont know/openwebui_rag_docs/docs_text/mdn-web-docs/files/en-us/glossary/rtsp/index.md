Path: mdn-web-docs > files > en-us > glossary > rtsp > index.md

Path: mdn-web-docs > files > en-us > glossary > rtsp > index.md Path: mdn-web-docs > files > en-us > glossary > rtsp > index.md Path: mdn-web-docs > files > en-us > glossary > rtsp > index.md Path: mdn-web-docs > files > en-us > glossary > rtsp > index.md --- title: "RTSP: Real-time streaming protocol" slug: Glossary/RTSP page-type: glossary-definition --- {{GlossarySidebar}} Real-time streaming protocol (RTSP) is a network protocol that controls how the streaming of a media should occur between a {{glossary("server")}} and a client. Basically, RTSP is the protocol that describes what happens when you click "Pause"/"Play" when streaming a video. If your computer were a remote control and the streaming server a television, RTSP would describe how the instruction of the remote control affects the TV. ## See also - [RTSP](https://en.wikipedia.org/wiki/Real_Time_Streaming_Protocol) on Wikipedia - [RFC 7826](https://datatracker.ietf.org/doc/html/rfc7826) (one of the documents that specifies precisely how the protocol works)