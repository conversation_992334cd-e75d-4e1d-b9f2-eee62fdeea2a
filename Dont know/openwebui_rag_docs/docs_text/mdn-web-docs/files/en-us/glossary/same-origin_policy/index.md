Path: mdn-web-docs > files > en-us > glossary > same-origin_policy > index.md

Path: mdn-web-docs > files > en-us > glossary > same-origin_policy > index.md Path: mdn-web-docs > files > en-us > glossary > same-origin_policy > index.md Path: mdn-web-docs > files > en-us > glossary > same-origin_policy > index.md Path: mdn-web-docs > files > en-us > glossary > same-origin_policy > index.md --- title: Same-origin policy slug: Glossary/Same-origin_policy page-type: glossary-definition --- {{GlossarySidebar}} The **[same-origin policy](/en-US/docs/Web/Security/Same-origin_policy)** is a critical security mechanism that restricts how a document or script loaded from one {{Glossary("origin")}} can interact with a resource from another origin. It helps isolate potentially malicious documents, reducing possible attack vectors. ## See also - [Same-origin policy](/en-US/docs/Web/Security/Same-origin_policy) - Related glossary terms: - {{Glossary("CORS")}} - {{Glossary("origin")}}