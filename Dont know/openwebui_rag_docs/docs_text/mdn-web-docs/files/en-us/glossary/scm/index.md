Path: mdn-web-docs > files > en-us > glossary > scm > index.md

Path: mdn-web-docs > files > en-us > glossary > scm > index.md Path: mdn-web-docs > files > en-us > glossary > scm > index.md Path: mdn-web-docs > files > en-us > glossary > scm > index.md Path: mdn-web-docs > files > en-us > glossary > scm > index.md --- title: SCM slug: Glossary/SCM page-type: glossary-definition --- {{GlossarySidebar}} SCM (Source Control Management) is a system for managing source code. Usually it refers to the use of software to handle versioning of source files. A programmer can modify source code files without being afraid of editing out useful stuff, because a SCM keeps track of how the source code has changed and who made the changes. Some SCM systems include CVS, SVN, GIT. ## See also - [Revision control](https://en.wikipedia.org/wiki/Revision_control) on Wikipedia