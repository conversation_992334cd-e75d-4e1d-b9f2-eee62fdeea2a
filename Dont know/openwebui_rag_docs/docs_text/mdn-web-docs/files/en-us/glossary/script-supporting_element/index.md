Path: mdn-web-docs > files > en-us > glossary > script-supporting_element > index.md

Path: mdn-web-docs > files > en-us > glossary > script-supporting_element > index.md Path: mdn-web-docs > files > en-us > glossary > script-supporting_element > index.md Path: mdn-web-docs > files > en-us > glossary > script-supporting_element > index.md Path: mdn-web-docs > files > en-us > glossary > script-supporting_element > index.md --- title: Script-supporting element slug: Glossary/Script-supporting_element page-type: glossary-definition --- {{GlossarySidebar}} In an {{Glossary("HTML")}} document, **script-supporting elements** are those elements that don't directly contribute to the appearance or layout of the page; instead, they're either scripts or contain information that's only used by scripts. These elements may be important, but do not affect the displayed page unless the page's scripts explicitly cause them to do so. There are only two script-supporting elements: {{HTMLElement("script")}} and {{HTMLElement("template")}}. ## See also [Script-supporting elements](/en-US/docs/Web/HTML/Guides/Content_categories#script-supporting_elements)