Path: mdn-web-docs > files > en-us > glossary > sctp > index.md

Path: mdn-web-docs > files > en-us > glossary > sctp > index.md Path: mdn-web-docs > files > en-us > glossary > sctp > index.md Path: mdn-web-docs > files > en-us > glossary > sctp > index.md Path: mdn-web-docs > files > en-us > glossary > sctp > index.md --- title: SCTP slug: Glossary/SCTP page-type: glossary-definition --- {{GlossarySidebar}} **SCTP** (Stream Control Transmission {{glossary("Protocol")}}) is an {{Glossary("IETF")}} standard for a transport protocol which enables the reliable, in-order transmission of messages while offering congestion control, multi-homing, and other features to improve reliability and stability of the connection. It's used for sending traditional telephone calls over the Internet, but is also used for {{Glossary("WebRTC")}} data. ## See also - {{RFC(4960, "Stream Control Transmission Protocol")}} - [Stream Control Transmission Protocol](https://en.wikipedia.org/wiki/Stream_Control_Transmission_Protocol) on Wikipedia