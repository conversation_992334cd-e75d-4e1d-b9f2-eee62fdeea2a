Path: mdn-web-docs > files > en-us > glossary > sdk > index.md

Path: mdn-web-docs > files > en-us > glossary > sdk > index.md Path: mdn-web-docs > files > en-us > glossary > sdk > index.md Path: mdn-web-docs > files > en-us > glossary > sdk > index.md Path: mdn-web-docs > files > en-us > glossary > sdk > index.md --- title: SDK (Software Development Kit) slug: Glossary/SDK page-type: glossary-definition --- {{GlossarySidebar}} An **SDK** (**Software Development Kit**) is an integrated collection of tools that a developer can use to create software for a specific framework, operating system, or other platform. An SDK can include: - An editor - A compiler - A debugger - An emulator or simulator, if the target platform is different from the platform used to create the program. - Tools to help test and package the program for distribution. SDKs are usually provided by the owner of a software platform, to support developers targeting the platform. For example, Google provides an [Android SDK](https://developer.android.com/studio) for developers writing Android apps. In many respects, the {{Glossary("developer tools")}} built into modern web browsers provide a similar function for web developers.