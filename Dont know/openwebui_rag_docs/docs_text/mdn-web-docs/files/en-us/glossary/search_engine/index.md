Path: mdn-web-docs > files > en-us > glossary > search_engine > index.md

Path: mdn-web-docs > files > en-us > glossary > search_engine > index.md Path: mdn-web-docs > files > en-us > glossary > search_engine > index.md Path: mdn-web-docs > files > en-us > glossary > search_engine > index.md Path: mdn-web-docs > files > en-us > glossary > search_engine > index.md --- title: Search engine slug: Glossary/Search_engine page-type: glossary-definition --- {{GlossarySidebar}} A search engine is a software system that collects information from the {{Glossary("World Wide Web")}} and presents it to users who are looking for specific information. A search engine conducts the following processes: - **Web crawling:** Searching websites by navigating {{Glossary("Hyperlink", "Hyperlinks")}} on web pages, both within a site, and from one site to another. A website owner can exclude areas of the site from being accessed by a search engine's _web crawler_ (or _spider_), by defining "robot exclusion" information in a file named `robots.txt`. - **Indexing:** Associating keywords and other information with specific web pages that have been crawled. This enables users to find relevant pages as quickly as possible. - **Searching:** Looking for relevant web pages based on queries consisting of key words and other commands to the search engine. The search engine finds the URLs of pages that match the query, and ranks them based on their relevance. It then presents results to the user in order of the ranking. The most popular search engine is Google. Other top search engines include Yahoo!, Bing, Baidu, and AOL. ## See also - [Web search engine](https://en.wikipedia.org/wiki/Web_search_engine) on Wikipedia - [Search engine](https://www.webopedia.com/definitions/search-engine/) on Webopedia - [How Internet search engines work](https://computer.howstuffworks.com/internet/basics/search-engine.htm) on How Stuff Works