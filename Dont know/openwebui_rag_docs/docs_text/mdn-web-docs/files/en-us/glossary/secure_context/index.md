Path: mdn-web-docs > files > en-us > glossary > secure_context > index.md

Path: mdn-web-docs > files > en-us > glossary > secure_context > index.md Path: mdn-web-docs > files > en-us > glossary > secure_context > index.md Path: mdn-web-docs > files > en-us > glossary > secure_context > index.md Path: mdn-web-docs > files > en-us > glossary > secure_context > index.md --- title: Secure Context slug: Glossary/Secure_Context page-type: glossary-definition --- {{GlossarySidebar}} A **secure context** is a `Window` or `Worker` in which certain minimum standards of authentication and confidentiality are met. Many Web APIs and features are only accessible in secure contexts, reducing the opportunity for misuse by malicious code. For more information see: [Web > Security > Secure Contexts](/en-US/docs/Web/Security/Secure_Contexts).