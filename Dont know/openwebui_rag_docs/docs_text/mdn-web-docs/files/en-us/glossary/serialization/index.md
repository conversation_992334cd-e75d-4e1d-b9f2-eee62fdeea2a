Path: mdn-web-docs > files > en-us > glossary > serialization > index.md

Path: mdn-web-docs > files > en-us > glossary > serialization > index.md Path: mdn-web-docs > files > en-us > glossary > serialization > index.md Path: mdn-web-docs > files > en-us > glossary > serialization > index.md Path: mdn-web-docs > files > en-us > glossary > serialization > index.md --- title: Serialization slug: Glossary/Serialization page-type: glossary-definition --- {{GlossarySidebar}} The process whereby an object or data structure is translated into a format suitable for transfer over a network, or storage (e.g., in an array buffer or file format). In {{Glossary("JavaScript")}}, for example, you can serialize an object to a {{Glossary("JSON")}} {{Glossary("string")}} by calling the {{Glossary("function")}} {{jsxref("JSON.stringify()")}}. {{Glossary("CSS")}} values are serialized by calling the function {{domxref("CSSStyleDeclaration.getPropertyValue()")}}. ## See also - [Serialization](https://en.wikipedia.org/wiki/Serialization) on Wikipedia