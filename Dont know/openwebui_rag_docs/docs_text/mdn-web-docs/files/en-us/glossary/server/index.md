Path: mdn-web-docs > files > en-us > glossary > server > index.md

Path: mdn-web-docs > files > en-us > glossary > server > index.md Path: mdn-web-docs > files > en-us > glossary > server > index.md Path: mdn-web-docs > files > en-us > glossary > server > index.md Path: mdn-web-docs > files > en-us > glossary > server > index.md --- title: Server slug: Glossary/Server page-type: glossary-definition --- {{GlossarySidebar}} A server is a software or hardware offering a service to a user, usually referred to as client. A hardware server is a shared computer on a network, usually powerful and housed in a data center. A software server (often running on a hardware server) is a program that provides services to client programs or a {{glossary("UI","user interface")}} to human clients. Services are provided generally over local area networks or wide area networks such as the internet. A client program and server program traditionally connect by passing messages encoded using a {{glossary("protocol")}} over an {{glossary("API")}}. For example: - An Internet-connected Web server is sending a {{glossary("HTML")}} file to your browser software so that you can read this page - Local area network server for file, name, mail, print, and fax - Minicomputers, mainframes, and super computers at data centers ## See also - [Introduction to servers](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_web_server) - [Server (computing)](<https://en.wikipedia.org/wiki/Server_(computing)>) on Wikipedia