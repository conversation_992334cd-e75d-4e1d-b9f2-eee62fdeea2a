Path: mdn-web-docs > files > en-us > glossary > server_timing > index.md

Path: mdn-web-docs > files > en-us > glossary > server_timing > index.md Path: mdn-web-docs > files > en-us > glossary > server_timing > index.md Path: mdn-web-docs > files > en-us > glossary > server_timing > index.md Path: mdn-web-docs > files > en-us > glossary > server_timing > index.md --- title: Server Timing slug: Glossary/Server_Timing page-type: glossary-definition --- {{GlossarySidebar}} The [Server Timing specification](https://www.w3.org/TR/server-timing/) enables the server to communicate performance metrics from the request-response cycle to the user agent, and utilizes a JavaScript interface to allow applications to collect, process, and act on these metrics to optimize application delivery. ## See also - <https://www.w3.org/TR/server-timing/> - [Resource Timing](https://www.w3.org/TR/resource-timing/)