Path: mdn-web-docs > files > en-us > glossary > sgml > index.md

Path: mdn-web-docs > files > en-us > glossary > sgml > index.md Path: mdn-web-docs > files > en-us > glossary > sgml > index.md Path: mdn-web-docs > files > en-us > glossary > sgml > index.md Path: mdn-web-docs > files > en-us > glossary > sgml > index.md --- title: SGML slug: Glossary/SGML page-type: glossary-definition --- {{GlossarySidebar}} The _Standard Generalized Markup Language_ (**SGML**) is an {{Glossary("ISO")}} specification for defining declarative markup languages. On the web, {{Glossary("HTML")}} 4, {{Glossary("XHTML")}}, and {{Glossary("XML")}} are popular SGML-based languages. It is worth noting that since its fifth edition, HTML is no longer SGML-based and has its own parsing rules. ## See also - [SGML](https://en.wikipedia.org/wiki/SGML) on Wikipedia - [Introduction to SGML](https://www.tei-c.org/Vault/GL/P3/SG.htm)