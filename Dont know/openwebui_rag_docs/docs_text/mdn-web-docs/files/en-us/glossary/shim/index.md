Path: mdn-web-docs > files > en-us > glossary > shim > index.md

Path: mdn-web-docs > files > en-us > glossary > shim > index.md Path: mdn-web-docs > files > en-us > glossary > shim > index.md Path: mdn-web-docs > files > en-us > glossary > shim > index.md Path: mdn-web-docs > files > en-us > glossary > shim > index.md --- title: Shim slug: Glossary/Shim page-type: glossary-definition --- {{GlossarySidebar}} A **shim** is a piece of code used to correct the behavior of code that already exists, usually by adding new API that works around the problem. This differs from a {{Glossary("polyfill")}}, which implements a new API that is not supported by the stock browser as shipped. ## See also - [Shim](<https://en.wikipedia.org/wiki/Shim_(computing)>) on Wikipedia