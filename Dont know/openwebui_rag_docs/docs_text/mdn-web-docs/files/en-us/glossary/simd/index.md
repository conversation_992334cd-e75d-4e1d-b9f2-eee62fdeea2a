Path: mdn-web-docs > files > en-us > glossary > simd > index.md

Path: mdn-web-docs > files > en-us > glossary > simd > index.md Path: mdn-web-docs > files > en-us > glossary > simd > index.md Path: mdn-web-docs > files > en-us > glossary > simd > index.md Path: mdn-web-docs > files > en-us > glossary > simd > index.md --- title: SIMD slug: Glossary/SIMD page-type: glossary-definition --- {{GlossarySidebar}} SIMD (pronounced "sim-dee") is short for **Single Instruction/Multiple Data** which is one [classification of computer architectures](https://en.wikipedia.org/wiki/Flynn%27s_taxonomy). SIMD allows one same operation to be performed on multiple data points resulting in data level parallelism and thus performance gains for example, for 3D graphics and video processing, physics simulations or cryptography, and other domains. See also {{Glossary("SISD")}} for a sequential architecture with no parallelism in either the instructions or the data sets. ## See also - [SIMD](https://en.wikipedia.org/wiki/SIMD) on Wikipedia - Related glossary terms: - {{Glossary("SISD")}}