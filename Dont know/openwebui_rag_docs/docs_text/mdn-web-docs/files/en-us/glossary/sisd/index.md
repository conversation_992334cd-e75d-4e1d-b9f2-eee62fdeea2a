Path: mdn-web-docs > files > en-us > glossary > sisd > index.md

Path: mdn-web-docs > files > en-us > glossary > sisd > index.md Path: mdn-web-docs > files > en-us > glossary > sisd > index.md Path: mdn-web-docs > files > en-us > glossary > sisd > index.md Path: mdn-web-docs > files > en-us > glossary > sisd > index.md --- title: SISD slug: Glossary/SISD page-type: glossary-definition --- {{GlossarySidebar}} SISD is short for **Single Instruction/Single Data** which is one [classification of computer architectures](https://en.wikipedia.org/wiki/Flynn%27s_taxonomy). In SISD architecture, a single processor executes a single instruction and operates on a single data point in memory. See also {{Glossary("SIMD")}} for a parallel architecture that allows one same operation to be performed on multiple data points. ## See also - [SISD](https://en.wikipedia.org/wiki/Single_instruction,_single_data) on Wikipedia