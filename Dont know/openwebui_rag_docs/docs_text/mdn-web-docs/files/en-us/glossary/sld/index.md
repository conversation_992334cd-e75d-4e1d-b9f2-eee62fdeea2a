Path: mdn-web-docs > files > en-us > glossary > sld > index.md

Path: mdn-web-docs > files > en-us > glossary > sld > index.md Path: mdn-web-docs > files > en-us > glossary > sld > index.md Path: mdn-web-docs > files > en-us > glossary > sld > index.md Path: mdn-web-docs > files > en-us > glossary > sld > index.md --- title: SLD slug: Glossary/SLD page-type: glossary-definition --- {{GlossarySidebar}} The **Second Level Domain (SLD)** is the part of the domain name that is located right before a _{{Glossary("TLD", "Top Level Domain (TLD)")}}_. For example, in `mozilla.org` the SLD is `mozilla` and the TLD is `org`. A domain name is not limited to a TLD and SLD. Additional subdomains can be created to provide additional information about various server functions or to delimit areas under the same domain. For example, `www` is a commonly used subdomain to indicate that the domain points to a web server. As another example, in `developer.mozilla.org`, the `developer` subdomain is used to specify that the subdomain contains the developer section of the Mozilla website. ## See also - [SLD](https://en.wikipedia.org/wiki/Second-level_domain) (Wikipedia) - Related glossary terms: - {{Glossary("DNS")}} - {{Glossary("Domain")}} - {{Glossary("Domain name")}} - {{Glossary("TLD")}}