Path: mdn-web-docs > files > en-us > glossary > sloppy_mode > index.md

Path: mdn-web-docs > files > en-us > glossary > sloppy_mode > index.md Path: mdn-web-docs > files > en-us > glossary > sloppy_mode > index.md Path: mdn-web-docs > files > en-us > glossary > sloppy_mode > index.md Path: mdn-web-docs > files > en-us > glossary > sloppy_mode > index.md --- title: Sloppy mode slug: Glossary/Sloppy_mode page-type: glossary-definition --- {{GlossarySidebar}} {{Glossary("ECMAScript")}} 5 and later let scripts opt in to a new [strict mode](/en-US/docs/Web/JavaScript/Reference/Strict_mode), which alters the semantics of JavaScript in several ways to improve its resiliency and which make it easier to understand what's going on when there are problems. The normal, non-strict mode of JavaScript is sometimes referred to as **sloppy mode**. This isn't an official designation, but you are likely to come across it if you spend time doing serious JavaScript code. ## See also - "[Strict Mode](https://exploringjs.com/es5/ch07.html#_strict_mode)" in chapter 7 ("JavaScript Syntax") in the book Speaking _JavaScript_.