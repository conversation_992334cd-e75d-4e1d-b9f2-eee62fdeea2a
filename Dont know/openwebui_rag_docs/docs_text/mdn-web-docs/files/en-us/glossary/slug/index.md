Path: mdn-web-docs > files > en-us > glossary > slug > index.md

Path: mdn-web-docs > files > en-us > glossary > slug > index.md Path: mdn-web-docs > files > en-us > glossary > slug > index.md Path: mdn-web-docs > files > en-us > glossary > slug > index.md Path: mdn-web-docs > files > en-us > glossary > slug > index.md --- title: Slug slug: Glossary/Slug page-type: glossary-definition --- {{GlossarySidebar}} A Slug is the unique identifying part of a web address, typically at the end of the URL. In the context of MDN, it is the portion of the URL following "_\<locale>/docs/_". It may also just be the final component when a new document is created under a parent document; for example, this page's slug is `Glossary/Slug`. ## See also - [Choosing titles and slugs](/en-US/docs/MDN/Writing_guidelines/Writing_style_guide#slugs)