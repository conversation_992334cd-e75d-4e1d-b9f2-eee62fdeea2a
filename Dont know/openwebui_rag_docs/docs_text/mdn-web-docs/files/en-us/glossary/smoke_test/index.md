Path: mdn-web-docs > files > en-us > glossary > smoke_test > index.md

Path: mdn-web-docs > files > en-us > glossary > smoke_test > index.md Path: mdn-web-docs > files > en-us > glossary > smoke_test > index.md Path: mdn-web-docs > files > en-us > glossary > smoke_test > index.md Path: mdn-web-docs > files > en-us > glossary > smoke_test > index.md --- title: Smoke Test slug: Glossary/Smoke_Test page-type: glossary-definition --- {{GlossarySidebar}} A smoke test consists of functional or unit tests of critical software functionality. Smoke testing comes before further, in-depth testing. Smoke testing answers questions like - "Does the program start up correctly?" - "Do the main control buttons function?" - "Can you save a simple blank new test user account?" If this basic functionality fails, there is no point investing time in more detailed QA work at this stage. ## See also - [Smoke testing (software)](<https://en.wikipedia.org/wiki/Smoke_testing_(software)>) on Wikipedia