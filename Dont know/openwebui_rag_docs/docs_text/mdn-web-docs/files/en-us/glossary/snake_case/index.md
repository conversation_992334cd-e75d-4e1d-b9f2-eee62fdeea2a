Path: mdn-web-docs > files > en-us > glossary > snake_case > index.md

Path: mdn-web-docs > files > en-us > glossary > snake_case > index.md Path: mdn-web-docs > files > en-us > glossary > snake_case > index.md Path: mdn-web-docs > files > en-us > glossary > snake_case > index.md Path: mdn-web-docs > files > en-us > glossary > snake_case > index.md --- title: Snake case slug: Glossary/Snake_case page-type: glossary-definition --- {{GlossarySidebar}} **Snake case** is a way of writing phrases without spaces, where spaces are replaced with underscores `_`, and the words are typically all lower case. It's often stylized as "snake_case" to remind the reader of its appearance. Snake casing is often used as a variable naming convention. The following names are in snake case: `left_shift`, `bitwise_invert`, `matrix_transpose`. Note that snake case never contains upper case letters. Sometimes, constants are written in all-uppercase, such as JavaScript's {{jsxref("Number.MAX_SAFE_INTEGER")}}. This is not typically considered as snake case. Instead, it is sometimes called _screaming snake case_. Snake case is the most popular convention in Python, Rust, and various other languages. ## See also - Related glossary terms: - {{Glossary("Camel case")}} - {{Glossary("Kebab case")}} - [typescript-eslint rule: `naming-convention`](https://typescript-eslint.io/rules/naming-convention/)