Path: mdn-web-docs > files > en-us > glossary > snap_positions > index.md

Path: mdn-web-docs > files > en-us > glossary > snap_positions > index.md Path: mdn-web-docs > files > en-us > glossary > snap_positions > index.md Path: mdn-web-docs > files > en-us > glossary > snap_positions > index.md Path: mdn-web-docs > files > en-us > glossary > snap_positions > index.md --- title: Snap positions slug: Glossary/Snap_positions page-type: glossary-definition --- {{GlossarySidebar}} Snap positions are points where the {{Glossary("scroll container", "scrollport")}} stops moving after the scrolling operation completes. Setting up snap positions allows to create a scrolling experience of paging through content instead of needing to drag content into view. Snap positions are set up on a {{Glossary("scroll container")}}. See the [CSS Scroll Snap](/en-US/docs/Web/CSS/CSS_scroll_snap) properties. ## See also - {{glossary("Scroll snap")}}