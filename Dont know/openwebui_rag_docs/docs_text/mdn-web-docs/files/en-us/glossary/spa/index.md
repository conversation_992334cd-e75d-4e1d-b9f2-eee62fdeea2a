Path: mdn-web-docs > files > en-us > glossary > spa > index.md

Path: mdn-web-docs > files > en-us > glossary > spa > index.md Path: mdn-web-docs > files > en-us > glossary > spa > index.md Path: mdn-web-docs > files > en-us > glossary > spa > index.md Path: mdn-web-docs > files > en-us > glossary > spa > index.md --- title: SPA (Single-page application) slug: Glossary/SPA page-type: glossary-definition --- {{GlossarySidebar}} An SPA (Single-page application) is a web app implementation that loads only a single web document, and then updates the body content of that single document via JavaScript APIs such as [Fetch](/en-US/docs/Web/API/Fetch_API) when different content is to be shown. This therefore allows users to use websites without loading whole new pages from the server, which can result in performance gains and a more dynamic experience, with some tradeoff disadvantages such as SEO, more effort required to maintain state, implement navigation, and do meaningful performance monitoring. ## See also - [Single-page application](https://en.wikipedia.org/wiki/Single-page_application) (Wikipedia) - [Understanding client-side JavaScript frameworks](/en-US/docs/Learn_web_development/Core/Frameworks_libraries) - Related glossary terms: - {{Glossary("API")}} - {{Glossary("AJAX")}} - {{Glossary("JavaScript")}} - Popular SPA frameworks: - [React](https://react.dev/) - [Angular](https://angular.dev/) - [Vue.JS](https://vuejs.org/)