Path: mdn-web-docs > files > en-us > glossary > specification > index.md

Path: mdn-web-docs > files > en-us > glossary > specification > index.md Path: mdn-web-docs > files > en-us > glossary > specification > index.md Path: mdn-web-docs > files > en-us > glossary > specification > index.md Path: mdn-web-docs > files > en-us > glossary > specification > index.md --- title: Specification slug: Glossary/Specification page-type: glossary-definition --- {{GlossarySidebar}} A **specification** is a document that lays out in detail what functionality or attributes a product must include before delivery. In the context of describing the Web, the term "specification" (often shortened to "spec") generally means a document describing a language, technology, or {{Glossary("API")}} which makes up the complete set of open Web technologies. ## See also - [Specification](https://en.wikipedia.org/wiki/Specification) on Wikipedia