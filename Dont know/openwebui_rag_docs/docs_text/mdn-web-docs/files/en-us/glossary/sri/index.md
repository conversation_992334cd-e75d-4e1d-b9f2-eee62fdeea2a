Path: mdn-web-docs > files > en-us > glossary > sri > index.md

Path: mdn-web-docs > files > en-us > glossary > sri > index.md Path: mdn-web-docs > files > en-us > glossary > sri > index.md Path: mdn-web-docs > files > en-us > glossary > sri > index.md Path: mdn-web-docs > files > en-us > glossary > sri > index.md --- title: SRI slug: Glossary/SRI page-type: glossary-definition --- {{GlossarySidebar}} **Subresource Integrity** (SRI) is a security feature that enables browsers to verify that files they fetch (for example, from a {{Glossary("CDN")}}) are delivered without unexpected manipulation. It works by allowing you to provide a cryptographic hash that a fetched file must match. ## See also - [Subresource Integrity](/en-US/docs/Web/Security/Subresource_Integrity)