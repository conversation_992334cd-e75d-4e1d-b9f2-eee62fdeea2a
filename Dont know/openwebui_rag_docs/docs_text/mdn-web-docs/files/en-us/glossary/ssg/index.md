Path: mdn-web-docs > files > en-us > glossary > ssg > index.md

Path: mdn-web-docs > files > en-us > glossary > ssg > index.md Path: mdn-web-docs > files > en-us > glossary > ssg > index.md Path: mdn-web-docs > files > en-us > glossary > ssg > index.md Path: mdn-web-docs > files > en-us > glossary > ssg > index.md --- title: Static site generator (SSG) slug: Glossary/SSG page-type: glossary-definition --- {{GlossarySidebar}} A **static site generator** (SSG) is a software used to generate _static_ websites. A static website is comprised of {{glossary("HTML")}}, {{glossary("CSS")}}, and {{glossary("JavaScript")}} files. Most importantly static sites do not have [server-side logic](/en-US/docs/Learn_web_development/Extensions/Server-side), so for any given URL, all users will receive the same content. Authors write content in any form accepted by the generator, such as <PERSON><PERSON>, reStructuredText, HTML, (and sometimes even [React](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/React_getting_started), and so on), and the generator compiles them into a set of optimized static files that can be rendered by the browser. Static sites are commonly used for blogs, documentation, and other content-driven websites, which don't have data that needs to be fetched or generated server-side. They are fast, secure, and easy to deploy, because they can be served from a {{glossary("CDN")}}. ## See also - [Introduction to client-side frameworks > static site generators](/en-US/docs/Learn_web_development/Core/Frameworks_libraries/Introduction#static_site_generators) - [Static site generator](https://en.wikipedia.org/wiki/Static_site_generator) on Wikipedia - [Jamstack site generators](https://jamstack.org/generators/) - [WordPress](https://wordpress.com/) - [Docusaurus](https://docusaurus.io/) - [Jekyll](https://jekyllrb.com/) - [Astro](https://astro.build/) - [VitePress](https://vitepress.dev/) - [Eleventy](https://www.11ty.dev/)