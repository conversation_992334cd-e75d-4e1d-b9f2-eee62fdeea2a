Path: mdn-web-docs > files > en-us > glossary > ssl > index.md

Path: mdn-web-docs > files > en-us > glossary > ssl > index.md Path: mdn-web-docs > files > en-us > glossary > ssl > index.md Path: mdn-web-docs > files > en-us > glossary > ssl > index.md Path: mdn-web-docs > files > en-us > glossary > ssl > index.md --- title: Secure Sockets Layer (SSL) slug: Glossary/SSL page-type: glossary-definition --- {{GlossarySidebar}} Secure Sockets Layer, or SSL, was the old standard security technology for creating an encrypted network link between a server and client, ensuring all data passed is private and secure. The current version of SSL is version 3.0, released by Netscape in 1996, and has been superseded by the {{Glossary("TLS", "Transport Layer Security (TLS)")}} protocol. ## See also - [Transport Layer Security](https://en.wikipedia.org/wiki/Transport_Layer_Security) (Wikipedia) - [Transport Layer Security (TLS) protocol](/en-US/docs/Web/Security/Transport_Layer_Security) - Related glossary terms: - {{Glossary("HTTPS")}} - {{Glossary("TLS")}}