Path: mdn-web-docs > files > en-us > glossary > stacking_context > index.md

Path: mdn-web-docs > files > en-us > glossary > stacking_context > index.md Path: mdn-web-docs > files > en-us > glossary > stacking_context > index.md Path: mdn-web-docs > files > en-us > glossary > stacking_context > index.md Path: mdn-web-docs > files > en-us > glossary > stacking_context > index.md --- title: Stacking context slug: Glossary/Stacking_context page-type: glossary-definition --- {{GlossarySidebar}} **Stacking context** refers to how elements on a webpage appear to sit on top of other elements, just as you can arrange index cards on your desk to lie side-by-side or overlap each other. ## See also - [Explanation and example of the stacking context](/en-US/docs/Web/CSS/CSS_positioned_layout/Stacking_context)