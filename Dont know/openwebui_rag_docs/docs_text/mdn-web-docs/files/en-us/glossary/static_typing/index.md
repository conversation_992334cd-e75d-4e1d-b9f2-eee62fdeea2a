Path: mdn-web-docs > files > en-us > glossary > static_typing > index.md

Path: mdn-web-docs > files > en-us > glossary > static_typing > index.md Path: mdn-web-docs > files > en-us > glossary > static_typing > index.md Path: mdn-web-docs > files > en-us > glossary > static_typing > index.md Path: mdn-web-docs > files > en-us > glossary > static_typing > index.md --- title: Static typing slug: Glossary/Static_typing page-type: glossary-definition --- {{GlossarySidebar}} A **statically-typed** language is a language (such as Java, C, or C++) where variable types are known at compile time. In most of these languages, types must be expressly indicated by the programmer; in other cases (such as OCaml), type inference allows the programmer to not indicate their variable types. ## See also - [Type system](https://en.wikipedia.org/wiki/Type_system) on Wikipedia