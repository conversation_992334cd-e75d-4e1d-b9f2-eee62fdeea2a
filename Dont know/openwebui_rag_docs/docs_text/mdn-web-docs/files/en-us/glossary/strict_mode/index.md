Path: mdn-web-docs > files > en-us > glossary > strict_mode > index.md

Path: mdn-web-docs > files > en-us > glossary > strict_mode > index.md Path: mdn-web-docs > files > en-us > glossary > strict_mode > index.md Path: mdn-web-docs > files > en-us > glossary > strict_mode > index.md Path: mdn-web-docs > files > en-us > glossary > strict_mode > index.md --- title: Strict mode slug: Glossary/Strict_mode page-type: glossary-definition --- {{GlossarySidebar}} JavaScript's **strict mode** is a way to _opt in_ to a restricted variant of JavaScript, thereby implicitly opting-out of "{{Glossary("Sloppy_mode", "sloppy mode")}}". Strict mode isn't just a subset: it _intentionally_ has different semantics from normal code. Strict mode for an entire script is invoked by including the statement `"use strict";` before any other statements. ## See also - [Strict mode](/en-US/docs/Web/JavaScript/Reference/Strict_mode) - Related glossary terms: - {{Glossary("Sloppy mode")}}