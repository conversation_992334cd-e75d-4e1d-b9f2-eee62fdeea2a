Path: mdn-web-docs > files > en-us > glossary > string > index.md

Path: mdn-web-docs > files > en-us > glossary > string > index.md Path: mdn-web-docs > files > en-us > glossary > string > index.md Path: mdn-web-docs > files > en-us > glossary > string > index.md Path: mdn-web-docs > files > en-us > glossary > string > index.md --- title: String slug: Glossary/String page-type: glossary-definition --- {{GlossarySidebar}} In any computer programming language, a string is a sequence of {{Glossary("character","characters")}} used to represent text. In {{Glossary("JavaScript")}}, a String is one of the {{Glossary("Primitive", "primitive values")}} and the {{jsxref("String")}} object is a {{Glossary("wrapper")}} around a String primitive. ## See also - [String (computer science)](<https://en.wikipedia.org/wiki/String_(computer_science)>) on Wikipedia - [JavaScript data types and data structures](/en-US/docs/Web/JavaScript/Guide/Data_structures#string_type)