Path: mdn-web-docs > files > en-us > glossary > stun > index.md

Path: mdn-web-docs > files > en-us > glossary > stun > index.md Path: mdn-web-docs > files > en-us > glossary > stun > index.md Path: mdn-web-docs > files > en-us > glossary > stun > index.md Path: mdn-web-docs > files > en-us > glossary > stun > index.md --- title: STUN slug: Glossary/STUN page-type: glossary-definition --- {{GlossarySidebar}} **STUN** (Session Traversal Utilities for NAT) is an auxiliary protocol for transmitting data around a {{glossary("NAT")}} (Network Address Translator). STUN returns the {{glossary("IP address")}}, {{glossary("port")}}, and connectivity status of a networked computer behind a NAT. ## See also - [STUN](https://en.wikipedia.org/wiki/STUN) on Wikipedia - [WebRTC protocols](/en-US/docs/Web/API/WebRTC_API/Protocols) ### Technical reference - [Specification](https://datatracker.ietf.org/doc/html/rfc5389)