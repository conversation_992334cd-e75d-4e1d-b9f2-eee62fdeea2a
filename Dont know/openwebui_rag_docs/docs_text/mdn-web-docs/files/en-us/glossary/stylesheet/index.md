Path: mdn-web-docs > files > en-us > glossary > stylesheet > index.md

Path: mdn-web-docs > files > en-us > glossary > stylesheet > index.md Path: mdn-web-docs > files > en-us > glossary > stylesheet > index.md Path: mdn-web-docs > files > en-us > glossary > stylesheet > index.md Path: mdn-web-docs > files > en-us > glossary > stylesheet > index.md --- title: Stylesheet slug: Glossary/Stylesheet page-type: glossary-definition --- {{GlossarySidebar}} A **stylesheet** is a set of CSS rules used to control the layout and design of a webpage or document. _Internal_ stylesheets are placed inside a {{htmlelement("style")}} element inside the {{htmlelement("head")}} of a web document, and _external_ stylesheets are placed inside a separate `.css` file, which is applied to a document by referencing the file inside a {{htmlelement("link")}} element in the document's head. External stylesheets are generally preferred because they allow you to control the styling of multiple pages from a single place, rather than having to repeat the CSS across each page. ## See also - [CSS Styling basics](/en-US/docs/Learn_web_development/Core/Styling_basics) - Stylesheets on [Wikipedia](<https://en.wikipedia.org/wiki/Style_sheet_(web_development)>)