Path: mdn-web-docs > files > en-us > glossary > svg > index.md

Path: mdn-web-docs > files > en-us > glossary > svg > index.md Path: mdn-web-docs > files > en-us > glossary > svg > index.md Path: mdn-web-docs > files > en-us > glossary > svg > index.md Path: mdn-web-docs > files > en-us > glossary > svg > index.md --- title: SVG slug: Glossary/SVG page-type: glossary-definition --- {{GlossarySidebar}} _Scalable Vector Graphics_ (**SVG**) is a 2D vector image format based on an {{Glossary("XML")}} syntax. The {{Glossary("W3C")}} began work on SVG in the late 1990s, but SVG only became popular when {{Glossary("Microsoft Internet Explorer", "Internet Explorer")}} 9 came out with SVG support. All major {{Glossary("browser","browsers")}} now support SVG. Based on an {{Glossary("XML")}} syntax, SVG can be styled with {{Glossary("CSS")}} and made interactive using {{Glossary("JavaScript")}}. HTML allows direct embedding of SVG {{Glossary("Tag","tags")}} in an {{Glossary("HTML")}} document. As a [vector image format](https://en.wikipedia.org/wiki/Vector_graphics), SVG graphics can scale infinitely, making them invaluable in {{Glossary("responsive web design", "responsive design")}}, since you can create interface elements and graphics that scale to any screen size. SVG also provides a useful set of tools, such as clipping, masking, filters, and animations. ## See also - [SVG](https://en.wikipedia.org/wiki/SVG) on Wikipedia - [W3.org's SVG Primer](https://www.w3.org/Graphics/SVG/IG/resources/svgprimer.html) - [SVG documentation on MDN](/en-US/docs/Web/SVG) - [Latest SVG specification](https://www.w3.org/TR/SVG/)