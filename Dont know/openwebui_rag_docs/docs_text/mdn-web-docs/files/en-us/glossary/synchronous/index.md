Path: mdn-web-docs > files > en-us > glossary > synchronous > index.md

Path: mdn-web-docs > files > en-us > glossary > synchronous > index.md Path: mdn-web-docs > files > en-us > glossary > synchronous > index.md Path: mdn-web-docs > files > en-us > glossary > synchronous > index.md Path: mdn-web-docs > files > en-us > glossary > synchronous > index.md --- title: Synchronous slug: Glossary/Synchronous page-type: glossary-definition --- {{GlossarySidebar}} **Synchronous** refers to real-time communication where each party receives (and if necessary, processes and replies to) messages instantly (or as near to instantly as possible). A human example is the telephone during a telephone call you tend to respond to another person immediately. Many programming commands are also synchronous for example when you type in a calculation, the environment will return the result to you instantly, unless you program it not to. ## See also - Related glossary terms: - {{glossary("Asynchronous")}} - [Asynchronous JavaScript](/en-US/docs/Learn_web_development/Extensions/Async_JS)