Path: mdn-web-docs > files > en-us > glossary > syntax > index.md

Path: mdn-web-docs > files > en-us > glossary > syntax > index.md Path: mdn-web-docs > files > en-us > glossary > syntax > index.md Path: mdn-web-docs > files > en-us > glossary > syntax > index.md Path: mdn-web-docs > files > en-us > glossary > syntax > index.md --- title: Syntax slug: Glossary/Syntax page-type: glossary-definition --- {{GlossarySidebar}} Syntax specifies the required combination and sequence of {{Glossary("character","characters")}} making up correctly structured code. Syntax generally includes grammar and the rules that apply to writing it, such as indentation requirements in Python. Syntax varies from language to language (e.g., syntax is different in {{Glossary("HTML")}} and {{Glossary("JavaScript")}}). Although languages can share few similarities in terms of their syntaxes for example "operand operator operand" rule in JavaScript and Python. This does not mean the two languages share similarities with syntax. Syntax applies both to programming languages (commands to the computer) and markup languages (document structure information) alike. Syntax only governs ordering and structure; the instructions must also be _meaningful_, which is the province of {{Glossary("semantics")}}. Code must have correct syntax in order to {{Glossary("compile")}} correctly, otherwise a {{Glossary("syntax error")}} occurs. Even small errors, like a missing parenthesis, can stop source code from compiling successfully. Frameworks are said to have a "clean" syntax if they produce simple, readable, concise results. If a codebase uses "a lot of syntax", it requires more characters to achieve the same functionality. ## See also - [Syntax (programming languages)](<https://en.wikipedia.org/wiki/Syntax_(programming_languages)>) on Wikipedia