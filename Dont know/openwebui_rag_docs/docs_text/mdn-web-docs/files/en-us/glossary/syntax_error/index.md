Path: mdn-web-docs > files > en-us > glossary > syntax_error > index.md

Path: mdn-web-docs > files > en-us > glossary > syntax_error > index.md Path: mdn-web-docs > files > en-us > glossary > syntax_error > index.md Path: mdn-web-docs > files > en-us > glossary > syntax_error > index.md Path: mdn-web-docs > files > en-us > glossary > syntax_error > index.md --- title: Syntax error slug: Glossary/Syntax_error page-type: glossary-definition --- {{GlossarySidebar}} An {{Glossary("exception")}} caused by the incorrect use of a pre-defined {{Glossary("syntax")}}. Syntax errors are detected while compiling or parsing source code. For example, if you leave off a closing brace (`}`) when defining a {{Glossary("JavaScript")}} function, you trigger a syntax error. Browser development tools display {{Glossary("JavaScript")}} and {{Glossary("CSS")}} syntax errors in the console. ## See also - [Syntax error](https://en.wikipedia.org/wiki/Syntax_error) on Wikipedia - {{jsxref("SyntaxError")}} JavaScript object