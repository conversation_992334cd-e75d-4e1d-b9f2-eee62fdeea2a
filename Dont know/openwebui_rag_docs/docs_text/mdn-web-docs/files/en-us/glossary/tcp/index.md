Path: mdn-web-docs > files > en-us > glossary > tcp > index.md

Path: mdn-web-docs > files > en-us > glossary > tcp > index.md Path: mdn-web-docs > files > en-us > glossary > tcp > index.md Path: mdn-web-docs > files > en-us > glossary > tcp > index.md Path: mdn-web-docs > files > en-us > glossary > tcp > index.md --- title: TCP slug: Glossary/TCP page-type: glossary-definition --- {{GlossarySidebar}} **TCP (Transmission Control Protocol)** is an important network {{Glossary("protocol")}} that lets two hosts connect and exchange data streams. TCP guarantees the delivery of data and packets in the same order as they were sent. <PERSON><PERSON> and <PERSON>, who were DARPA scientists at the time, designed TCP in the 1970s. TCP's role is to ensure the packets are reliably delivered, and error-free. TCP implements [congestion control](https://en.wikipedia.org/wiki/TCP_congestion_control), which means the initial requests start small, increasing in size to the levels of bandwidth the computers, servers, and network can support. ## See also - [Transmission Control Protocol](https://en.wikipedia.org/wiki/Transmission_Control_Protocol) (Wikipedia) - [HTTP Overview](/en-US/docs/Web/HTTP/Guides/Overview) - [How browsers work](/en-US/docs/Web/Performance/Guides/How_browsers_work) - Related glossary terms: - {{Glossary("IPv4")}} - {{Glossary("IPv6")}} - {{Glossary("Packet")}}