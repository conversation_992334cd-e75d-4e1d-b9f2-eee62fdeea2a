Path: mdn-web-docs > files > en-us > glossary > three_js > index.md

Path: mdn-web-docs > files > en-us > glossary > three_js > index.md Path: mdn-web-docs > files > en-us > glossary > three_js > index.md Path: mdn-web-docs > files > en-us > glossary > three_js > index.md Path: mdn-web-docs > files > en-us > glossary > three_js > index.md --- title: Three js slug: Glossary/Three_js page-type: glossary-definition --- {{GlossarySidebar}} three.js is a {{Glossary("JavaScript")}}-based {{Glossary("WebGL")}} engine that can run GPU-powered games and other graphics-powered apps straight from the {{Glossary("browser")}}. The three.js library provides many features and {{Glossary("API","APIs")}} for drawing 3D scenes in your browser. ## See also - [Three.js](https://en.wikipedia.org/wiki/Three.js) on Wikipedia - [three.js official website](https://threejs.org/)