Path: mdn-web-docs > files > en-us > glossary > tld > index.md

Path: mdn-web-docs > files > en-us > glossary > tld > index.md Path: mdn-web-docs > files > en-us > glossary > tld > index.md Path: mdn-web-docs > files > en-us > glossary > tld > index.md Path: mdn-web-docs > files > en-us > glossary > tld > index.md --- title: TLD slug: Glossary/TLD page-type: glossary-definition --- {{GlossarySidebar}} A TLD (_top-level domain_) is the most generic {{Glossary("domain")}} in the Internet's hierarchical {{Glossary("DNS")}} (domain name system). A TLD is the final component of a {{Glossary("domain name")}}, for example, "org" in `developer.mozilla.org`. {{Glossary("ICANN")}} (Internet Corporation for Assigned Names and Numbers) designates organizations to manage each TLD. Depending on how strict an administrating organization might be, TLD often serves as a clue to the purpose, ownership, or nationality of a website. Consider an example Internet address: `https://developer.mozilla.org` Here org is the TLD; mozilla.org is the second-level domain name; and developer is a subdomain name. All together, these constitute a fully-qualified domain name; the addition of https\:// makes this a complete URL. {{Glossary("IANA")}} today distinguishes the following groups of top-level domains: - country-code top-level domains (ccTLD) - : Two-character domains established for countries or territories. Example: _.us_ for United States. - internationalized country code top-level domains (IDN ccTLD) - : ccTLDs in non-Latin character sets (e.g., Arabic or Chinese). - generic top-level domains (gTLD) - : Top-level domains with three or more characters. - unsponsored top-level domains - : Domains that operate directly under policies established by ICANN processes for the global Internet community, for example "com" and "edu". - sponsored top-level domains (sTLD) - : These domains are proposed and sponsored by private organizations that decide whether an applicant is eligible to use the TLD, based on community theme concepts. - infrastructure top-level domain - : This group consists of one domain, the {{Glossary("ARPA", "Address and Routing Parameter Area")}} (ARPA). ## See also - [TLD](https://en.wikipedia.org/wiki/TLD) on Wikipedia - [List of top-level domains](https://www.iana.org/domains/root/db)