Path: mdn-web-docs > files > en-us > glossary > trident > index.md

Path: mdn-web-docs > files > en-us > glossary > trident > index.md Path: mdn-web-docs > files > en-us > glossary > trident > index.md Path: mdn-web-docs > files > en-us > glossary > trident > index.md Path: mdn-web-docs > files > en-us > glossary > trident > index.md --- title: Trident slug: Glossary/Trident page-type: glossary-definition --- {{GlossarySidebar}} **Trident** (or _MSHTML_) was the proprietary {{Glossary("Engine/Rendering", "rendering engine")}} that powered {{Glossary("Microsoft Internet Explorer", "Internet Explorer")}}. A {{Glossary("fork")}} called _EdgeHTML_ replaced Trident in Internet Explorer's successor, {{Glossary("Microsoft Edge", "Edge")}} (later referred to as "Microsoft Edge Legacy"). Trident is used in Microsoft Edge when 'IE mode' is on, a feature that allows Edge to render web pages using Trident for compatibility with legacy websites. Trident will only receive security updates until 2029 to support usage in IE mode. ## See also - [Trident](<https://en.wikipedia.org/wiki/Trident_(software)>) on Wikipedia - Related glossary terms: - {{Glossary("Microsoft Internet Explorer")}} - {{Glossary("Microsoft Edge")}} - {{Glossary("Engine/Rendering", "Rendering engine")}}