Path: mdn-web-docs > files > en-us > glossary > turn > index.md

Path: mdn-web-docs > files > en-us > glossary > turn > index.md Path: mdn-web-docs > files > en-us > glossary > turn > index.md Path: mdn-web-docs > files > en-us > glossary > turn > index.md Path: mdn-web-docs > files > en-us > glossary > turn > index.md --- title: TURN slug: Glossary/TURN page-type: glossary-definition --- {{GlossarySidebar}} **TURN** (Traversal Using Relays around NAT) is a {{Glossary('protocol')}} enabling a computer to receive and send data from behind a {{glossary("NAT", "Network Address Translator")}} (NAT) or firewall. TURN is used by {{Glossary("WebRTC")}} to allow any two devices on the Internet to enter a peer-to-peer connection. ## See also - [TURN](https://en.wikipedia.org/wiki/TURN) on Wikipedia - [WebRTC protocols](/en-US/docs/Web/API/WebRTC_API/Protocols) - [Specification](https://datatracker.ietf.org/doc/html/rfc5766) - [Specification update for IPv6](https://datatracker.ietf.org/doc/html/rfc6156.txt)