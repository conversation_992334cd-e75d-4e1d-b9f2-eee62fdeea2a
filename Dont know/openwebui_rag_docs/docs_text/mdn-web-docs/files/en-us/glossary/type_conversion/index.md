Path: mdn-web-docs > files > en-us > glossary > type_conversion > index.md

Path: mdn-web-docs > files > en-us > glossary > type_conversion > index.md Path: mdn-web-docs > files > en-us > glossary > type_conversion > index.md Path: mdn-web-docs > files > en-us > glossary > type_conversion > index.md Path: mdn-web-docs > files > en-us > glossary > type_conversion > index.md --- title: Type conversion slug: Glossary/Type_Conversion page-type: glossary-definition --- {{GlossarySidebar}} Type conversion (or typecasting) means transfer of data from one data type to another. _Implicit conversion_ happens when the compiler (for compiled languages) or runtime (for script languages like {{glossary("JavaScript")}}) automatically converts data types. The source code can also _explicitly_ require a conversion to take place. For example, given the expression `"foo" + 1`, the {{glossary("Number")}} `1` is implicitly converted into a {{glossary("String")}} and the expression returns `"foo1"`. Given the instruction `Number("0x11")`, the string `"0x11"` is explicitly converted to the number `17`. ## See also - [Type conversion](https://en.wikipedia.org/wiki/Type_conversion) (Wikipedia) - Related glossary terms: - {{Glossary("Type")}} - {{Glossary("Type coercion")}}