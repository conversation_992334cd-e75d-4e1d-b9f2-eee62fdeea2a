Path: mdn-web-docs > files > en-us > glossary > uaag > index.md

Path: mdn-web-docs > files > en-us > glossary > uaag > index.md Path: mdn-web-docs > files > en-us > glossary > uaag > index.md Path: mdn-web-docs > files > en-us > glossary > uaag > index.md Path: mdn-web-docs > files > en-us > glossary > uaag > index.md --- title: UAAG slug: Glossary/UAAG page-type: glossary-definition --- {{GlossarySidebar}} _User Agent {{glossary("Accessibility")}} Guidelines_ (**UAAG**) is a recommendation published by the {{Glossary("WAI","Web Accessibility Initiative")}} group at the {{Glossary("W3C")}}, explaining how to make user agents accessible to people with disabilities. User agents include browsers, browser extensions, media players, readers and other applications that render web content. Some accessibility needs are better met in the browser than in the web content, such as text customization, preferences, and user interface accessibility. ## See also - [UAAG as part of WAI](<https://en.wikipedia.org/wiki/Web_Accessibility_Initiative#User_Agent_Accessibility_Guidelines_(UAAG)>) on Wikipedia - [User Agent Accessibility Guidelines (UAAG) 2.0: Recommendation](https://www.w3.org/TR/UAAG20/) - Glossary - {{Glossary("Accessibility")}} - {{Glossary("WAI")}} - {{Glossary("WCAG")}}