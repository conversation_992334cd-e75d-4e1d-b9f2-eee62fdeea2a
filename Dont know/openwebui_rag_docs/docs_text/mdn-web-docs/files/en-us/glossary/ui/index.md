Path: mdn-web-docs > files > en-us > glossary > ui > index.md

Path: mdn-web-docs > files > en-us > glossary > ui > index.md Path: mdn-web-docs > files > en-us > glossary > ui > index.md Path: mdn-web-docs > files > en-us > glossary > ui > index.md Path: mdn-web-docs > files > en-us > glossary > ui > index.md --- title: UI slug: Glossary/UI page-type: glossary-definition --- {{GlossarySidebar}} **User Interface** (UI) is anything that facilitates the interaction between a user and a machine. In the world of computers, it can be anything from a keyboard, a joystick, a screen or a program. In case of computer software, it can be a command-line prompt, a webpage, a user input form, or the front-end of any application. ## See also - [User interface](https://en.wikipedia.org/wiki/User_interface) on Wikipedia - [Front end development](https://en.wikipedia.org/wiki/Front_end_development) on Wikipedia