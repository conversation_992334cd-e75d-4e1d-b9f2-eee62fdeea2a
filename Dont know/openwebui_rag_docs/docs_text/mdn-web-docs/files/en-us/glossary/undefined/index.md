Path: mdn-web-docs > files > en-us > glossary > undefined > index.md

Path: mdn-web-docs > files > en-us > glossary > undefined > index.md Path: mdn-web-docs > files > en-us > glossary > undefined > index.md Path: mdn-web-docs > files > en-us > glossary > undefined > index.md Path: mdn-web-docs > files > en-us > glossary > undefined > index.md --- title: Undefined slug: Glossary/Undefined page-type: glossary-definition --- {{GlossarySidebar}} **`undefined`** is a {{Glossary("primitive")}} value automatically assigned to {{glossary("variable", "variables")}} that have just been declared, or to formal {{Glossary("Argument","arguments")}} for which there are no actual arguments. ## Example ```js let x; // create a variable but assign it no value console.log(`x's value is ${x}`); // logs "x's value is undefined" ``` ## See also - [Undefined value](https://en.wikipedia.org/wiki/Undefined_value) on Wikipedia - [JavaScript data types and data structures](/en-US/docs/Web/JavaScript/Guide/Data_structures)