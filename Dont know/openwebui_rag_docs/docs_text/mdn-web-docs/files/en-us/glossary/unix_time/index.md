Path: mdn-web-docs > files > en-us > glossary > unix_time > index.md

Path: mdn-web-docs > files > en-us > glossary > unix_time > index.md Path: mdn-web-docs > files > en-us > glossary > unix_time > index.md Path: mdn-web-docs > files > en-us > glossary > unix_time > index.md Path: mdn-web-docs > files > en-us > glossary > unix_time > index.md --- title: Unix time slug: Glossary/Unix_time page-type: glossary-definition --- {{GlossarySidebar}} Unix time is a method to represent a timestamp, and is usually defined as the number of seconds since the beginning of the Unix epoch, which is January 1st, 1970, at midnight (UTC). Leap seconds are ignored. On the web platform, Unix time is used for timestamps, and is given as the number of milliseconds since the beginning of the Unix epoch. ## See also - [Unix time](https://en.wikipedia.org/wiki/Unix_time) on Wikipedia - [Leap second](https://en.wikipedia.org/wiki/Leap_second) on Wikipedia