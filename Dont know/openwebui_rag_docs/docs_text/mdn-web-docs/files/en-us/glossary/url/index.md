Path: mdn-web-docs > files > en-us > glossary > url > index.md

Path: mdn-web-docs > files > en-us > glossary > url > index.md Path: mdn-web-docs > files > en-us > glossary > url > index.md Path: mdn-web-docs > files > en-us > glossary > url > index.md Path: mdn-web-docs > files > en-us > glossary > url > index.md --- title: URL slug: Glossary/URL page-type: glossary-definition --- {{GlossarySidebar}} **Uniform Resource Locator** (**URL**) is a text string that specifies where a resource (such as a web page, image, or video) can be found on the Internet. In the context of {{Glossary("HTTP")}}, URLs are called "Web address" or "link". Your {{glossary("browser")}} displays URLs in its address bar, for example: `https://developer.mozilla.org`. Some browsers display only the part of a URL after the "//", that is, the {{Glossary("Domain name")}}. URLs can also be used for file transfer ({{Glossary("FTP")}}), emails ({{Glossary("SMTP")}}), and other applications. ## See also - [Understanding URLs and their structure](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_URL) - The syntax of URLs is defined in the [URL Living Standard](https://url.spec.whatwg.org/) - [URL](https://en.wikipedia.org/wiki/URL) on Wikipedia