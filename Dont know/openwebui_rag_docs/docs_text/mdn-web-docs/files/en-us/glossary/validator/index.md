Path: mdn-web-docs > files > en-us > glossary > validator > index.md

Path: mdn-web-docs > files > en-us > glossary > validator > index.md Path: mdn-web-docs > files > en-us > glossary > validator > index.md Path: mdn-web-docs > files > en-us > glossary > validator > index.md Path: mdn-web-docs > files > en-us > glossary > validator > index.md --- title: Validator slug: Glossary/Validator page-type: glossary-definition --- {{GlossarySidebar}} A validator is a program that checks for syntax errors in code. Validators can be created for any format or language, but in our context we speak of tools that check {{Glossary("HTML")}}, {{Glossary("CSS")}}, and {{Glossary("XML")}}. ## See also - [Validator](https://en.wikipedia.org/wiki/Validator) on Wikipedia - [Short list of validators](https://firefox-source-docs.mozilla.org/devtools-user/validators/index.html)