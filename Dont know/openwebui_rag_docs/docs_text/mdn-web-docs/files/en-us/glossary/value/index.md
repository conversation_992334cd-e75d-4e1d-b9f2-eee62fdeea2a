Path: mdn-web-docs > files > en-us > glossary > value > index.md

Path: mdn-web-docs > files > en-us > glossary > value > index.md Path: mdn-web-docs > files > en-us > glossary > value > index.md Path: mdn-web-docs > files > en-us > glossary > value > index.md Path: mdn-web-docs > files > en-us > glossary > value > index.md --- title: Value slug: Glossary/Value page-type: glossary-definition --- {{GlossarySidebar}} In the context of data or an object **{{Glossary("Wrapper", "wrapper")}}** around that data, the value is the **{{Glossary("Primitive","primitive value")}}** that the object wrapper contains. In the context of a **{{Glossary("Variable","variable")}}** or **{{Glossary("Property","property")}}**, the value can be either a primitive or an **{{Glossary("Object reference","object reference")}}**. In the context of CSS property values, there are specified, computed, and actual values. The final value for every CSS property applied to every element and pseudo-element is the result of a four-step calculation: the value is determined through specification (the "[specified value](/en-US/docs/Web/CSS/CSS_cascade/Value_processing#specified_value)", then resolved into a value that is used for inheritance (the "[computed value](/en-US/docs/Web/CSS/CSS_cascade/Value_processing#computed_value)"), then converted into an absolute value if necessary (the "[used value](/en-US/docs/Web/CSS/CSS_cascade/Value_processing#used_value)"), and finally transformed according to the limitations of the local environment (the "[actual value](/en-US/docs/Web/CSS/CSS_cascade/Value_processing#actual_value)"). For CSS data types, see [CSS values and units](/en-US/docs/Web/CSS/CSS_Values_and_Units). ## See also - [Primitive wrapper class](https://en.wikipedia.org/wiki/Primitive_wrapper_class) on Wikipedia