Path: mdn-web-docs > files > en-us > glossary > variable > index.md

Path: mdn-web-docs > files > en-us > glossary > variable > index.md Path: mdn-web-docs > files > en-us > glossary > variable > index.md Path: mdn-web-docs > files > en-us > glossary > variable > index.md Path: mdn-web-docs > files > en-us > glossary > variable > index.md --- title: Variable slug: Glossary/Variable page-type: glossary-definition --- {{GlossarySidebar}} A variable is a named reference to a {{Glossary("Value", "value")}}. That way an unpredictable value can be accessed through a predetermined name. ## See also - [Variable (computer science)](<https://en.wikipedia.org/wiki/Variable_(computer_science)>) on Wikipedia - [Declaring variables in JavaScript](/en-US/docs/Web/JavaScript/Guide/Grammar_and_types#declarations) - [`var` statement in JavaScript](/en-US/docs/Web/JavaScript/Reference/Statements/var)