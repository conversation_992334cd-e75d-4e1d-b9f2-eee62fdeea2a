Path: mdn-web-docs > files > en-us > glossary > visual_viewport > index.md

Path: mdn-web-docs > files > en-us > glossary > visual_viewport > index.md Path: mdn-web-docs > files > en-us > glossary > visual_viewport > index.md Path: mdn-web-docs > files > en-us > glossary > visual_viewport > index.md Path: mdn-web-docs > files > en-us > glossary > visual_viewport > index.md --- title: Visual Viewport slug: Glossary/Visual_Viewport page-type: glossary-definition --- {{GlossarySidebar}} The portion of the {{Glossary("viewport")}} that is currently visible is called the visual viewport. This can be smaller than the {{Glossary("layout viewport")}}, for example when the user has pinch-zoomed. The visual viewport is the visual portion of a screen excluding on-screen keyboards, areas outside of a pinch-zoom area, and any other on-screen artifact that doesn't scale with the dimensions of a page. ## See also - {{Glossary("Viewport")}} - {{Glossary("Layout viewport")}} - [Viewport concepts](/en-US/docs/Web/CSS/CSSOM_view/Viewport_concepts) - [CSSOM view](/en-US/docs/Web/CSS/CSSOM_view) module - [Visual Viewport API](/en-US/docs/Web/API/Visual_Viewport_API) - [Viewport](https://en.wikipedia.org/wiki/Viewport) on Wikipedia - [A tale of two viewports](https://www.quirksmode.org/mobile/viewports.html) (Quirksmode)