Path: mdn-web-docs > files > en-us > glossary > wai > index.md

Path: mdn-web-docs > files > en-us > glossary > wai > index.md Path: mdn-web-docs > files > en-us > glossary > wai > index.md Path: mdn-web-docs > files > en-us > glossary > wai > index.md Path: mdn-web-docs > files > en-us > glossary > wai > index.md --- title: WAI slug: Glossary/WAI page-type: glossary-definition --- {{GlossarySidebar}} The **Web Accessibility Initiative (WAI)** is an effort by the {{glossary("W3C")}} to develop standards and guidelines to help everyone building the web ensure and improve {{glossary("accessibility")}} for people with various challenges. WAI develops guidelines and other technical reports through the same process as other parts of the W3C. WAI develops accessibility guidelines for web content ({{glossary("WCAG")}}), authoring tools ({{glossary("ATAG")}}), and user agents ({{glossary("UAAG")}}). WAI also develops the Accessible Rich Internet Applications ({{Glossary("ARIA")}}) suite of technologies to make Web content and Web applications more accessible to people with disabilities. It especially helps with dynamic content and advanced user interface controls developed with HTML, JavaScript, and related technologies. ## See also - [WAI official website](https://www.w3.org/WAI/) - [Web Accessibility Initiative](https://en.wikipedia.org/wiki/Web_Accessibility_Initiative) on Wikipedia - Glossary - {{Glossary("W3C")}} - {{Glossary("Accessibility")}} - {{Glossary("WCAG")}} - {{Glossary("ATAG")}} - {{Glossary("UAAG")}} - {{Glossary("ARIA")}}