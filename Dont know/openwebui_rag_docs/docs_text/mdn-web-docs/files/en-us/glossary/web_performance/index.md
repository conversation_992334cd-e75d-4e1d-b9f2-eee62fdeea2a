Path: mdn-web-docs > files > en-us > glossary > web_performance > index.md

Path: mdn-web-docs > files > en-us > glossary > web_performance > index.md Path: mdn-web-docs > files > en-us > glossary > web_performance > index.md Path: mdn-web-docs > files > en-us > glossary > web_performance > index.md Path: mdn-web-docs > files > en-us > glossary > web_performance > index.md --- title: Web performance slug: Glossary/Web_performance page-type: glossary-definition --- {{GlossarySidebar}} **Web performance** is the objective time from when a request for content is made until the requested content is displayed in the user's browser, objective render times, and the subjective user experience of load time and runtime. Objectively, it is measurable time, in milliseconds, it takes for the web page or web application to be downloaded, painted in the user's web browser, and become responsive and interactive. It is the frames per second and times the main thread is not available for user interactions. Subjectively, it is the user's perception of whether the time it takes between the time the user requests the content and the time until the user feels the content requested is available and usable _feels_ slow or fast. ## See also - [Web performance](/en-US/docs/Web/Performance) guides - Related glossary terms: - {{Glossary("Perceived performance")}}