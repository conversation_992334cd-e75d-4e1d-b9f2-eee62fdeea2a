Path: mdn-web-docs > files > en-us > glossary > web_server > index.md

Path: mdn-web-docs > files > en-us > glossary > web_server > index.md Path: mdn-web-docs > files > en-us > glossary > web_server > index.md Path: mdn-web-docs > files > en-us > glossary > web_server > index.md Path: mdn-web-docs > files > en-us > glossary > web_server > index.md --- title: Web server slug: Glossary/Web_server page-type: glossary-definition --- {{GlossarySidebar}} A web server is a piece of software that often runs on a hardware server offering service to a user, usually referred to as the client. A server, on the other hand, is a piece of hardware that lives in a room full of computers, commonly known as a data center. ## See also - [Introduction to servers](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_web_server) - [Server (computing)](<https://en.wikipedia.org/wiki/Server_(computing)>) on Wikipedia