Path: mdn-web-docs > files > en-us > glossary > web_standards > index.md

Path: mdn-web-docs > files > en-us > glossary > web_standards > index.md Path: mdn-web-docs > files > en-us > glossary > web_standards > index.md Path: mdn-web-docs > files > en-us > glossary > web_standards > index.md Path: mdn-web-docs > files > en-us > glossary > web_standards > index.md --- title: Web standards slug: Glossary/Web_standards page-type: glossary-definition --- {{GlossarySidebar}} Web standards are rules established by international standards bodies that define how the {{Glossary("World Wide Web", "Web")}} works (and which sometimes control the {{Glossary("Internet")}} as well). Several standards bodies are responsible for defining different aspects of the Web, and all the standards must coordinate to keep the Web maximally usable and accessible. Web standards also must evolve to improve the current status and adapt to new circumstances. This non-exhaustive list gives you an idea of which standards websites and network systems must conform to: - **IETF** (Internet Engineering Task Force): Internet standards (STD), which among other things govern set-up and use of {{Glossary("URI", "URIs")}}, {{Glossary("HTTP")}}, and {{Glossary("MIME")}} - **{{Glossary("W3C")}}**: specifications for markup language (e.g., {{Glossary("HTML")}}), style definitions (i.e., {{Glossary("CSS")}}), {{Glossary("DOM")}}, {{Glossary("Accessibility", "accessibility")}} - **IANA** (Internet Assigned Numbers Authority): name and number registries - **Ecma Intl.:** scripting standards, most prominently for {{Glossary("JavaScript")}} - **{{Glossary("ISO")}}** (International Organization for Standardization): standards governing a diverse array of aspects, including character encodings, website management, and user-interface design ## Opposing standards Occasionally, the members of a standards body may disagree with how a feature is developing, and some may **oppose** it. This means that technology implementers (for example web browser vendors) who disagree with the feature in its current form are unlikely to ever implement it. Vendors that _do agree_ with the feature usually still implement it so that it is available to experiment with and provide feedback on. This enables interested parties to build use cases and demonstrate the issues with the feature, hopefully working toward reaching a consensus on a future version. Standards body members often publish a record of their position on different standards, for reference. For example, see [Mozilla Specification Positions](https://mozilla.github.io/standards-positions/) and [WebKit Standards Positions](https://webkit.org/standards-positions/). ## See also - [Web standards](https://en.wikipedia.org/wiki/Web_standards) on Wikipedia