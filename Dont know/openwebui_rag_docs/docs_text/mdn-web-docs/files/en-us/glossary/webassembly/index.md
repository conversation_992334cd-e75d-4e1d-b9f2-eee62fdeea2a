Path: mdn-web-docs > files > en-us > glossary > webassembly > index.md

Path: mdn-web-docs > files > en-us > glossary > webassembly > index.md Path: mdn-web-docs > files > en-us > glossary > webassembly > index.md Path: mdn-web-docs > files > en-us > glossary > webassembly > index.md Path: mdn-web-docs > files > en-us > glossary > webassembly > index.md --- title: WebAssembly slug: Glossary/WebAssembly page-type: glossary-definition --- {{GlossarySidebar}} **WebAssembly** (abbr. _Wasm_) is an open {{Glossary("binary")}} programming format that can be run in modern web {{Glossary("Browser", "browsers")}} in order to gain performance and/or provide new features for web pages. ## See also - [WebAssembly](https://en.wikipedia.org/wiki/WebAssembly) on Wikipedia - [Official website](https://webassembly.org/) - [WebAssembly](/en-US/docs/WebAssembly) on MDN