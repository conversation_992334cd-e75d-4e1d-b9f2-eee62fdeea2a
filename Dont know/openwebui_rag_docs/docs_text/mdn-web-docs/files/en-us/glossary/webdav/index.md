Path: mdn-web-docs > files > en-us > glossary > webdav > index.md

Path: mdn-web-docs > files > en-us > glossary > webdav > index.md Path: mdn-web-docs > files > en-us > glossary > webdav > index.md Path: mdn-web-docs > files > en-us > glossary > webdav > index.md Path: mdn-web-docs > files > en-us > glossary > webdav > index.md --- title: WebDAV slug: Glossary/WebDAV page-type: glossary-definition --- {{GlossarySidebar}} **WebDAV** (_Web Distributed Authoring and Versioning_) is an {{Glossary("HTTP")}} Extension that lets web developers update their content remotely from a client. WebDAV is rarely used alone, but two extensions are very common: {{Glossary("CalDAV")}} (remote-access calendar) and {{Glossary("CardDAV")}} (remote-access address book). WebDAV allows clients to - add, delete, and retrieve webpage metadata (e.g., author or creation date) - link pages of any media type to related pages - create sets of documents and retrieve hierarchical list - copy and move webpages - lock a document from being edited by more than one person at a time ## See also - [WebDAV](https://en.wikipedia.org/wiki/WebDAV) on Wikipedia - Specifications: - {{rfc(2518)}} - {{rfc(3253)}} - {{rfc(3744)}} - {{rfc(4918)}} (obsoletes RFC 2518)