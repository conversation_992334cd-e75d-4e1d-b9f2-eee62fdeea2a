Path: mdn-web-docs > files > en-us > glossary > webgl > index.md

Path: mdn-web-docs > files > en-us > glossary > webgl > index.md Path: mdn-web-docs > files > en-us > glossary > webgl > index.md Path: mdn-web-docs > files > en-us > glossary > webgl > index.md Path: mdn-web-docs > files > en-us > glossary > webgl > index.md --- title: WebGL slug: Glossary/WebGL page-type: glossary-definition --- {{GlossarySidebar}} **WebGL** (_Web Graphics Library_) is a {{Glossary("JavaScript")}} {{Glossary("API")}} that draws interactive 2D and 3D graphics. The [Khronos Group](https://www.khronos.org/) maintains WebGL, which is based on {{Glossary("OpenGL")}} ES 2.0. You can invoke WebGL within the {{Glossary("HTML")}} {{HTMLElement("canvas")}} element, which provides a rendering surface. All major {{Glossary("Browser","browsers")}} now support WebGL, but its availability depends also on external factors (e.g., GPU support). ## See also - [WebGL](https://en.wikipedia.org/wiki/WebGL) on Wikipedia - [Check for WebGL support](https://get.webgl.org/) - [WebGL on MDN](/en-US/docs/Web/API/WebGL_API) - [Support table for WebGL](https://caniuse.com/#feat=webgl)