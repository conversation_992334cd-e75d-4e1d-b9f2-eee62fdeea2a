Path: mdn-web-docs > files > en-us > glossary > webidl > index.md

Path: mdn-web-docs > files > en-us > glossary > webidl > index.md Path: mdn-web-docs > files > en-us > glossary > webidl > index.md Path: mdn-web-docs > files > en-us > glossary > webidl > index.md Path: mdn-web-docs > files > en-us > glossary > webidl > index.md --- title: WebIDL slug: Glossary/WebIDL page-type: glossary-definition --- {{GlossarySidebar}} **WebIDL** is the interface description language used to describe the {{Glossary("type", "data types")}}, {{Glossary("interface", "interfaces")}}, {{Glossary("method", "methods")}}, {{Glossary("property", "properties")}}, and other components which make up a Web application programming interface ({{Glossary("API")}}). It uses a somewhat stylized syntax which is independent of any specific programming language, so that the underlying code which is used to build each API can be written in whatever language is most appropriate, while still being possible to map the API's components to JavaScript-compatible constructs. WebIDL is used in nearly every API {{Glossary("specification")}} for the Web, and due to its standard format and syntax, the programmers who create Web browsers can more easily ensure that their browsers are compatible with one another, regardless of how they choose to write the code to implement the API. ## See also - [Specification](https://webidl.spec.whatwg.org/) - [Information contained in a WebIDL file](/en-US/docs/MDN/Writing_guidelines/Howto/Write_an_api_reference/Information_contained_in_a_WebIDL_file) - [Gecko WebIDL bindings](https://firefox-source-docs.mozilla.org/dom/webIdlBindings/index.html) - [WebIDL](https://en.wikipedia.org/wiki/WebIDL)