Path: mdn-web-docs > files > en-us > glossary > webkit > index.md

Path: mdn-web-docs > files > en-us > glossary > webkit > index.md Path: mdn-web-docs > files > en-us > glossary > webkit > index.md Path: mdn-web-docs > files > en-us > glossary > webkit > index.md Path: mdn-web-docs > files > en-us > glossary > webkit > index.md --- title: WebKit slug: Glossary/WebKit page-type: glossary-definition --- {{GlossarySidebar}} _WebKit_ is a framework that displays properly-formatted webpages based on their markup. {{Glossary("Apple Safari")}} depends on WebKit, and so do many mobile browsers (since WebKit is highly portable and customizable). WebKit began life as a fork of KDE's KHTML and KJS libraries, but many individuals and companies have since contributed (including KDE, Apple, Google, and Nokia). WebKit is an Apple trademark, and the framework is distributed under a BSD-form license. However, two important components fall under the {{Glossary("LGPL")}}: the **WebCore** rendering library and the **JavaScriptCore** engine. ## See also - [WebKit](https://en.wikipedia.org/wiki/WebKit) on Wikipedia - [WebKit CSS extensions](/en-US/docs/Web/CSS/WebKit_Extensions)