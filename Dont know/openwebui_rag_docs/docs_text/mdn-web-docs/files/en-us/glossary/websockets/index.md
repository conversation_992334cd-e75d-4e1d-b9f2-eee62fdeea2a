Path: mdn-web-docs > files > en-us > glossary > websockets > index.md

Path: mdn-web-docs > files > en-us > glossary > websockets > index.md Path: mdn-web-docs > files > en-us > glossary > websockets > index.md Path: mdn-web-docs > files > en-us > glossary > websockets > index.md Path: mdn-web-docs > files > en-us > glossary > websockets > index.md --- title: WebSockets slug: Glossary/WebSockets page-type: glossary-definition --- {{GlossarySidebar}} _WebSocket_ is a {{Glossary("protocol")}} that allows for a persistent {{Glossary("TCP")}} connection between {{Glossary("Server", "server")}} and client so they can exchange data at any time. Any client or server application can use WebSocket, but principally web {{Glossary("Browser", "browsers")}} and web servers. Through WebSocket, servers can pass data to a client without prior client request, allowing for dynamic content updates. ## See also - [WebSocket](https://en.wikipedia.org/wiki/WebSocket) on Wikipedia - [WebSocket reference on MDN](/en-US/docs/Web/API/WebSocket) - [Writing WebSocket client applications](/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_client_applications) - [Writing WebSocket servers](/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers)