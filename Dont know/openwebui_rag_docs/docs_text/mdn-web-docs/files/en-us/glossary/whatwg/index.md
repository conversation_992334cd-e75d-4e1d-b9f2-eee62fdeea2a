Path: mdn-web-docs > files > en-us > glossary > whatwg > index.md

Path: mdn-web-docs > files > en-us > glossary > whatwg > index.md Path: mdn-web-docs > files > en-us > glossary > whatwg > index.md Path: mdn-web-docs > files > en-us > glossary > whatwg > index.md Path: mdn-web-docs > files > en-us > glossary > whatwg > index.md --- title: WHATWG slug: Glossary/WHATWG page-type: glossary-definition --- {{GlossarySidebar}} The WHATWG (_Web Hypertext Application Technology Working Group_) is a community that [maintains and develops web standards](https://spec.whatwg.org/), including {{Glossary("DOM")}}, Fetch, and {{Glossary("HTML")}}. Employees of Apple, Mozilla, and Opera established WHATWG in 2004. ## See also - [WHATWG website](https://whatwg.org/) - [WHATWG](https://en.wikipedia.org/wiki/WHATWG) on Wikipedia