Path: mdn-web-docs > files > en-us > glossary > whitespace > index.md

Path: mdn-web-docs > files > en-us > glossary > whitespace > index.md Path: mdn-web-docs > files > en-us > glossary > whitespace > index.md Path: mdn-web-docs > files > en-us > glossary > whitespace > index.md Path: mdn-web-docs > files > en-us > glossary > whitespace > index.md --- title: Whitespace slug: Glossary/Whitespace page-type: glossary-definition --- {{GlossarySidebar}} **Whitespace** refers to {{Glossary("Character", "characters")}} which are used to provide horizontal or vertical space between other characters. Whitespace is often used to separate tokens in {{Glossary("HTML")}}, {{Glossary("CSS")}}, {{Glossary("JavaScript")}}, and other computer languages. Whitespace characters and their usage vary among languages. ## In HTML The [Infra Living Standard](https://infra.spec.whatwg.org/#ascii-whitespace) defines five characters as "{{Glossary("ASCII")}} whitespace": U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, and U+0020 SPACE. ## In JavaScript The [ECMAScript Language Specification](https://tc39.es/ecma262/multipage/ecmascript-language-lexical-grammar.html#sec-white-space) defines several Unicode code points as "white space": U+0009 CHARACTER TABULATION \<TAB>, U+000B LINE TABULATION \<VT>, U+000C FORM FEED \<FF>, U+0020 SPACE \<SP>, U+00A0 NO-BREAK SPACE \<NBSP>, U+FEFF ZERO WIDTH NO-BREAK SPACE \<ZWNBSP>, and any other Unicode "Space_Separator" code points \<USP>. ## See also - [Whitespace character](https://en.wikipedia.org/wiki/Whitespace_character) (Wikipedia) - [How whitespace is handled by HTML, CSS, and in the DOM](/en-US/docs/Web/API/Document_Object_Model/Whitespace) - {{cssxref("white-space")}} - Specifications - [ASCII whitespace spec](https://infra.spec.whatwg.org/#ascii-whitespace) - [ECMAScript Language Specification](https://tc39.es/ecma262/multipage/ecmascript-language-lexical-grammar.html#sec-white-space) - Related glossary terms: - {{Glossary("Character")}}