Path: mdn-web-docs > files > en-us > glossary > wrapper > index.md

Path: mdn-web-docs > files > en-us > glossary > wrapper > index.md Path: mdn-web-docs > files > en-us > glossary > wrapper > index.md Path: mdn-web-docs > files > en-us > glossary > wrapper > index.md Path: mdn-web-docs > files > en-us > glossary > wrapper > index.md --- title: Wrapper slug: Glossary/Wrapper page-type: glossary-definition --- {{GlossarySidebar}} In programming languages such as JavaScript, a wrapper is a function that is intended to call one or more other functions, sometimes purely for convenience, and sometimes adapting them to do a slightly different task in the process. For example, SDK Libraries for AWS are examples of wrappers. ## See also - [Wrapper function](https://en.wikipedia.org/wiki/Wrapper_function) (Wikipedia) - Related glossary terms: - {{Glossary("API")}} - {{Glossary("Class")}} - {{Glossary("Function")}}