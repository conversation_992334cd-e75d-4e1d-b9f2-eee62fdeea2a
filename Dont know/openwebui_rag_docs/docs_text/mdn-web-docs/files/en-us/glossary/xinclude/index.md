Path: mdn-web-docs > files > en-us > glossary > xinclude > index.md

Path: mdn-web-docs > files > en-us > glossary > xinclude > index.md Path: mdn-web-docs > files > en-us > glossary > xinclude > index.md Path: mdn-web-docs > files > en-us > glossary > xinclude > index.md Path: mdn-web-docs > files > en-us > glossary > xinclude > index.md --- title: XInclude slug: Glossary/XInclude page-type: glossary-definition --- {{GlossarySidebar}} XInclude is a W3C Recommendation defining inclusion tags that enable documents to include other documents or parts of other documents. Content can be included from other XML files or from text files. The XInclude mechanism is not supported natively by any major browsers. ## See also - [XInclude standard](https://www.w3.org/TR/xinclude-11/) - [`XPath`](/en-US/docs/Web/XML/XPath)