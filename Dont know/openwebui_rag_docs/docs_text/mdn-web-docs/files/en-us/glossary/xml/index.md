Path: mdn-web-docs > files > en-us > glossary > xml > index.md

Path: mdn-web-docs > files > en-us > glossary > xml > index.md Path: mdn-web-docs > files > en-us > glossary > xml > index.md Path: mdn-web-docs > files > en-us > glossary > xml > index.md Path: mdn-web-docs > files > en-us > glossary > xml > index.md --- title: XML slug: Glossary/XML page-type: glossary-definition --- {{GlossarySidebar}} eXtensible Markup Language (XML) is a generic markup language specified by the W3C. The information technology (IT) industry uses many languages based on XML as data-description languages. XML tags resemble HTML tags, but XML is much more flexible because it lets users define their own tags. In this way XML acts like a meta-language that is, it can be used to define other languages, such as {{Glossary("RSS")}}. Moreover, HTML is a presentation language, whereas XML is a data-description language. This means that XML has far broader applications than just the Web. For example, Web services can use XML to exchange requests and responses. ## See also - [XML introduction](/en-US/docs/Web/XML/Guides/XML_introduction)