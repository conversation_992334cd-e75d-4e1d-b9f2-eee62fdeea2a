Path: mdn-web-docs > files > en-us > glossary > xmlhttprequest > index.md

Path: mdn-web-docs > files > en-us > glossary > xmlhttprequest > index.md Path: mdn-web-docs > files > en-us > glossary > xmlhttprequest > index.md Path: mdn-web-docs > files > en-us > glossary > xmlhttprequest > index.md Path: mdn-web-docs > files > en-us > glossary > xmlhttprequest > index.md --- title: XMLHttpRequest (XHR) slug: Glossary/XMLHttpRequest page-type: glossary-definition --- {{GlossarySidebar}} {{domxref("XMLHttpRequest")}} (XHR) is a {{Glossary("JavaScript")}} {{Glossary("API")}} to create {{Glossary("HTTP")}} requests. Its methods provide the ability to send network requests between the {{Glossary("browser")}} and a {{Glossary("server")}}. The [Fetch API](/en-US/docs/Web/API/Fetch_API) is the modern replacement for XMLHttpRequest. ## See also - The [XMLHttpRequest API](/en-US/docs/Web/API/XMLHttpRequest_API) documentation.