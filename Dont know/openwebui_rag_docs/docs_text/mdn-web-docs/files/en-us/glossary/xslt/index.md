Path: mdn-web-docs > files > en-us > glossary > xslt > index.md

Path: mdn-web-docs > files > en-us > glossary > xslt > index.md Path: mdn-web-docs > files > en-us > glossary > xslt > index.md Path: mdn-web-docs > files > en-us > glossary > xslt > index.md Path: mdn-web-docs > files > en-us > glossary > xslt > index.md --- title: XSLT slug: Glossary/XSLT page-type: glossary-definition --- {{GlossarySidebar}} _eXtensible Stylesheet Language Transformations_ (**XSLT**) is a declarative language used to convert {{Glossary("XML")}} documents into other XML documents, {{Glossary("HTML")}}, {{Glossary("PDF")}}, plain text, and so on. XSLT has its own processor that accepts XML input, or any format convertible to an XQuery and XPath Data Model. The XSLT processor produces a new document based on the XML document and an XSLT stylesheet, making no changes to the original files in the process. ## See also - [XSLT](https://en.wikipedia.org/wiki/XSLT) on Wikipedia - [XSLT documentation on MDN](/en-US/docs/Web/XML/XSLT)