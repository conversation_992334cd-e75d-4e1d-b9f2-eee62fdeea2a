Path: mdn-web-docs > files > en-us > learn_web_development > about > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > about > index.md Path: mdn-web-docs > files > en-us > learn_web_development > about > index.md Path: mdn-web-docs > files > en-us > learn_web_development > about > index.md Path: mdn-web-docs > files > en-us > learn_web_development > about > index.md --- title: About Learn web development slug: Learn_web_development/About page-type: landing-page sidebar: learnsidebar --- MDN Learn web development aims to teach the fundamental skills and knowledge that a front-end web developer should have for employability and longevity in today's web industry. It embodies the values we think the web should have accessibility, sustainability, usability, performance, and community. We would love educators, developers, and students to use this resource and champion these values in their work, in their teachings, and in the products they build. This content has been created by the MDN community with review and feedback from experts within Mozilla and throughout the wider web community. Thank you for your valuable input; you know who you are! ## Background and motivation We originally [launched the MDN Learn Web Development section in 2016](https://hacks.mozilla.org/2016/06/learning-to-code-for-the-web-the-mdn-learning-area-welcomes-you/) with the aim of making MDN more accessible to non-experts and helping to take beginning web developers from "beginner to comfortable". The content was pretty successful but, moving forward a few years we noted that the structure was sub-par. Beginners really want a robust pathway they can follow to get the knowledge they need, rather than being expected to figure out what to learn and when. In addition, Mozilla talks to industry professionals every day, and we regularly get feedback on the knowledge gaps in new hires. Hiring managers often observe: - Too much of a focus on using frameworks to build web apps quickly, coupled with a lack of understanding of the underlying technologies behind these frameworks. This leads to a lack of problem-solving skills and less long-term employability as tools change. - A lack of core best practices such as semantics, accessibility, and responsive design. This results in a lack of user focus, leading to usability limitations. - Gaps in the knowledge of how browsers fundamentally work, how they surface information, and the interactivity you get for free. This causes solutions to be overcomplicated and often inaccessible. - Limited problem-solving, teamwork, research, and other vital soft skills. As a result, we created a curriculum to help guide people towards learning a better skill set, making them more employable, and enabling them to build a better, more accessible, more responsible web of tomorrow. We want them to have the best possible chance of success. We [launched the MDN Curriculum in early 2024](/en-US/blog/mdn-curriculum-launch/). However, we quickly received feedback that users found it confusing having two learning resources on MDN, with the curriculum/learning pathway in one place and the learning content in another place. as a result, we [merged the Curriculum into the learning area in December 2024](/en-US/docs/Learn_web_development/Changelog#december_2024). ## Target audience ### Students This curriculum is useful for several groups of students: - Students who want to get a job in the industry, which may involve gaining a related qualification or certification. The curriculum will act as a guide for what they should study. - Existing web developers who want to "level up" their skills, making sure their skill set is current and identifying gaps in their knowledge that they should learn more about. - Non-front-end web developers who have existing development experience in other areas (for example back-end web developers or platform-specific developers), who wish to get into front-end web development and want a guide to the topics they should learn. ### Educators Educators can use this content as a guide when creating programs, units, and assessment specifications for a web-related university degree, college course, coding school course, or similar. Conforming to the learning outcomes in our articles will help ensure that courses teach current techniques and best practices, and avoid bad practices and out-of-date information. To find out more, consult our [Resources for Educators](/en-US/docs/Learn_web_development/Educators) page. > [!NOTE] > The complete MDN Learn Web Development Curriculum is available as a convenient PDF to share with your students and colleagues. [Download the Curriculum](https://github.com/mdn/curriculum/releases/latest/download/MDN-Curriculum.pdf). ## Scope The term _front-end developer_ can be ambiguous; it can mean different things to different people, and folks working on the front end can be expected to do a wide variety of different tasks. ### What's covered This set of articles does not attempt to teach every topic that a web developer could conceivably be expected to know in-depth. The curriculum covers the following: - Core technical skills such as semantic HTML, CSS, and JavaScript fundamentals. - Best practices such as accessibility, responsive design, and UI design theory. - Key tools such as frameworks and version control. - Soft skills for promoting the mindset and attitude required to secure a job. - Environment knowledge like computer and file systems, browsing the web, command line basics, and code editors. - Several "extensions" that we feel constitute useful additional skills to learn as developers start to expand their knowledge and develop specialisms. This includes: - CSS transforms and animation - Common categories of Web API (for example, media, graphics, and client-side storage) - Server-side web development fundamentals - Performance - Security and privacy - Testing ### Level of detail The topics presented are covered in differing levels of detail. - Some are covered in depth, for example, HTML and CSS fundamentals. These are important to have a clear understanding of before a student goes too far on their learning journey. - Some are covered more superficially, for example, version control or testing. It is important to understand what these topics are and get started with some basics, but these kinds of skills can be expanded upon as you continue through your career. ### What is not covered There are also several areas that we explicitly don't cover in this curriculum, namely: - Exhaustive coverage of back-end languages/platforms. We do provide a brief introduction in [Node.js (Express)](/en-US/docs/Learn_web_development/Extensions/Server-side/Express_Nodejs) and [Python (Django)](/en-US/docs/Learn_web_development/Extensions/Server-side/Django) as it is useful to every web developer to understand how HTTP and server-side technologies work. However, we don't provide exhaustive coverage across multiple platforms; that would be out of scope for MDN. - In-depth coverage of traditional relational databases (for example, [MySQL](https://dev.mysql.com/doc/) or [Postgres](https://www.postgresql.org/)) and other server-side datastores (for example, cloud databases such as [MongoDB](https://www.mongodb.com/) or [Google Cloud Datastore](https://cloud.google.com/products/datastore)). We provide a brief introduction to such technologies in our [Server-side website programming](/en-US/docs/Learn_web_development/Extensions/Server-side) modules. - Deep-dive DevOps topics such as cloud platforms for provisioning and automation (for example, [Amazon AWS](https://aws.amazon.com/), [Google Cloud Platform](https://console.cloud.google.com/), and [Microsoft Azure](https://azure.microsoft.com/)) and containerization tools (for example, [Kubernetes](https://kubernetes.io/) and [Docker](https://www.docker.com/)). We lightly touch upon some tools that are considered to be in the DevOps space like GitHub and automated testing tools but these have distinct crossover into the front-end developer space. - Graphic design beyond the basic knowledge outlined in [Design for developers](/en-US/docs/Learn_web_development/Core/Design_for_developers). - Skills related to roles such as product and program management (for example, organization, research, and planning). ## Attribution This resource is free for anyone to use. If you find it useful, we request that you consider doing the following: - Link to it. For example, an educator could include the following in their public program information: ```html <p> This course is based on <a href="https://developer.mozilla.org/en-US/curriculum/" >MDN Learn Web Development</a >. </p> ``` - Tell others about it! We would love as many students and educators as possible to start using this material and converging around it as a standard for web developer baseline knowledge. > [!NOTE] > Educators should use this material as a guide, but its use does not imply endorsement by Mozilla. ## Update process The web development industry is changing constantly and rapidly. To keep our recommendations current, we will review our material regularly, update our [changelog](/en-US/docs/Learn_web_development/Changelog), and make an announcement every year, contacting the creators of known conforming courses to let them know the course has changed and encourage them to review/update their courses as appropriate. We intend to do this in Q2 each year, to give educators time over Q2/Q3 to implement changes before the start of the following academic year. ## Frequently asked questions ### Scrimba partnership questions #### How does MDN know Scrimba's courses are high quality and follow best practices? Scrimba already had a great reputation before we started talking to them about a partnership. However, we didn't just take the community's word for it. We did an extensive review of Scrimba's [Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j:details?via=mdn) (FDCP) and provided them with feedback on possible improvements, focusing on increasing coverage of best practices and conformance to our [Core modules](/en-US/docs/Learn_web_development/Core). Scrimba implemented all of our feedback, and the FDCP is even better than it was before. Now that it conforms to our Curriculum Core, we are confident it aligns with MDN standards. #### Is MDN sharing user data with Scrimba? We prioritize user privacy and transparency. The only information MDN shares with Scrimba is user navigation to Scrimba from MDN, and this happens through their own actions by following a link that is marked as external. In cases where we embed Scrimba content on MDN, Scrimba won't see user data until a user chooses to interact with Scrimba's content. #### Scrimba's content isn't free. Doesn't this conflict with MDN's philosophy of providing free content? A lot of Scrimba's content requires a paid subscription, but they also offer several complete courses that are free to access after you register. It is also worth pointing out that Scrimba's courses are not necessary to make use of MDN Learn Web Development they are an enhancement for those who wish to pay for a structured course that covers our curriculum core. You can still learn all our learning outcomes for free by working through our articles. #### Is a certification awarded on completing Scrimba's Frontend Developer Career Path? Yes, once you complete all the topics in the Frontend Developer Career Path, you can access a certificate of completion to share with potential employers or include in your portfolio. See [Where can I find my completion certificate?](https://forum.scrimba.com/t/where-can-i-find-my-completion-certificate/43?via=mdn) for more information.