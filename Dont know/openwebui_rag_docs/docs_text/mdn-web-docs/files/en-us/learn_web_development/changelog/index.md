Path: mdn-web-docs > files > en-us > learn_web_development > changelog > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > changelog > index.md Path: mdn-web-docs > files > en-us > learn_web_development > changelog > index.md Path: mdn-web-docs > files > en-us > learn_web_development > changelog > index.md Path: mdn-web-docs > files > en-us > learn_web_development > changelog > index.md --- title: Changelog slug: Learn_web_development/Changelog page-type: landing-page sidebar: learnsidebar --- ## December 2024 A major update to the Learn web development section was started in November 2024 and finally published in December 2024. To summarize, the MDN Curriculum has been merged into Learn web development. Specifically: - The content is the same, but it has been rearranged to follow the same learning pathway structure as the Curriculum. In a few cases, content was deemed not suitable for a beginner audience and has been migrated to other parts of MDN. - Specific learning outcomes have been added to the top of most of the articles, to match the learning outcomes detailed in the Curriculum. - Other features from the Curriculum have been migrated across to the Learn Web Development section, such as the [About](/en-US/docs/Learn_web_development/About) page and [Resources for educators](/en-US/docs/Learn_web_development/Educators). The Curriculum is being kept for the moment, but can be considered deprecated and will be removed when it is felt the time is right. Moving forward, we will continue to update the content and design to make Learn web development even more useful to learners and educators.