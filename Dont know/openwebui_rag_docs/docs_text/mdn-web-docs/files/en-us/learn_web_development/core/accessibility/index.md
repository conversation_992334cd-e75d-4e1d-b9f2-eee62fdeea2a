Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > index.md --- title: Accessibility on the web short-title: Accessibility slug: Learn_web_development/Core/Accessibility page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Core/Accessibility/What_is_accessibility", "Learn_web_development/Core")}} Access to web content such as public services, education, e-commerce sites, and entertainment is a human right. No one should be excluded based on disability, race, geography, or other human characteristics. This module discusses the best practices and techniques you should learn to make your websites as accessible as possible. ## Prerequisites You should be familiar with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content), [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics), and [JavaScript](/en-US/docs/Learn_web_development/Core/Scripting) before starting this module. > [!NOTE] > If you are working on a computer/tablet/other devices where you don't have the ability to create your own files, you can try out most of the code examples in an online coding program such as [JS Bin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials and challenges - [What is accessibility?](/en-US/docs/Learn_web_development/Core/Accessibility/What_is_accessibility) - : This article starts off the module with a good look at what accessibility is this includes what groups of people we need to consider and why, what tools different people use to interact with the web, and how we can make accessibility part of our web development workflow. - [Accessibility tooling and assistive technology](/en-US/docs/Learn_web_development/Core/Accessibility/Tooling) - : Next we turn our attention to accessibility tooling, providing information on the kinds of tools you can use to help solve accessibility issues, and the assistive technologies used by people with disabilities as they browse the web. You'll be using these tools throughout subsequent articles. - [HTML: A good basis for accessibility](/en-US/docs/Learn_web_development/Core/Accessibility/HTML) - : A great deal of web content can be made accessible just by making sure the correct HTML elements are always used for the correct purpose. This article looks in detail at how HTML can be used to ensure maximum accessibility. - [CSS and JavaScript accessibility best practices](/en-US/docs/Learn_web_development/Core/Accessibility/CSS_and_JavaScript) - : CSS and JavaScript, when used properly, also have the potential to allow for accessible web experiences, but if misused they can significantly harm accessibility. This article outlines some CSS and JavaScript best practices that should be considered to ensure that even complex content is as accessible as possible. - [WAI-ARIA basics](/en-US/docs/Learn_web_development/Core/Accessibility/WAI-ARIA_basics) - : Following on from the previous article, sometimes making complex UI controls that involve unsemantic HTML and dynamic JavaScript-updated content can be difficult. WAI-ARIA is a technology that can help with such problems by adding in further semantics that browsers and assistive technologies can recognize and use to let users know what is going on. Here we'll show how to use it at a basic level to improve accessibility. - [Accessible multimedia](/en-US/docs/Learn_web_development/Core/Accessibility/Multimedia) - : Another category of content that can create accessibility problems is multimedia video, audio, and image content need to be given proper textual alternatives, so they can be understood by assistive technologies and their users. This article shows how. - [Mobile accessibility](/en-US/docs/Learn_web_development/Core/Accessibility/Mobile) - : With web access on mobile devices being so popular, and popular platforms such as iOS and Android having fully-fledged accessibility tools, it is important to consider the accessibility of your web content on these platforms. This article looks at mobile-specific accessibility considerations. - [Accessibility troubleshooting](/en-US/docs/Learn_web_development/Core/Accessibility/Accessibility_troubleshooting) <sup>Challenge</sup> - : In this challenge, we present to you a simple site with several accessibility issues that you need to diagnose and fix. ## See also - [Start Building Accessible Web Applications Today](https://egghead.io/courses/start-building-accessible-web-applications-today) - : An excellent series of video tutorials by Marcy Sutton. - [Deque University resources](https://dequeuniversity.com/resources/) - : Includes code examples, screen reader references, and other useful resources. - [WebAIM resources](https://webaim.org/resources/) - : Includes guides, checklists, tools, and more. - [Web Accessibility Evaluation Tools List](https://www.w3.org/WAI/ER/tools/) - : Includes a list of web accessibility evaluation tools. - [Learn Accessible Web Design](https://scrimba.com/learn-accessible-web-design-c031?via=mdn) <sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup> - : [Scrimba's](https://scrimba.com/?via=mdn) _Learn Accessible Web Design_ course teaches you how to write accessible HTML by solving interactive coding challenges and fixing a real-world website. {{NextMenu("Learn_web_development/Core/Accessibility/What_is_accessibility", "Learn_web_development/Core")}}