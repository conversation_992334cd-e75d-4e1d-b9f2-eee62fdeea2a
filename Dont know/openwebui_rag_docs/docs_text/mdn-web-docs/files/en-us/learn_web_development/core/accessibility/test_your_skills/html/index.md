Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > test_your_skills > html > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > test_your_skills > html > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > test_your_skills > html > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > test_your_skills > html > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > test_your_skills > html > index.md --- title: "Test your skills: HTML accessibility" short-title: HTML slug: Learn_web_development/Core/Accessibility/Test_your_skills/HTML page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [HTML: A good basis for accessibility](/en-US/docs/Learn_web_development/Core/Accessibility/HTML) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON><PERSON><PERSON>](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## HTML accessibility 1 In this task we will test your understanding of text semantics, and why they are good for accessibility. The given text is an information panel with action buttons, but the HTML is really bad. We want you to update it use appropriate semantic HTML. You don't need to worry too much about recreating the _exact_ same look and text sizing, as long as the semantics are good. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/accessibility/tasks/html-css/html/html-a11y1.html", '100%', 1100)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/accessibility/tasks/html-css/html/html-a11y1-download.html) to work in your own editor or in an online editor. ## HTML accessibility 2 In the second task, you have a form containing three input fields. You need to: - Semantically associate the input with their labels. - Assume that these inputs will be part of a larger form, and wrap them in an element that associates them all together as a single related group. - Give the group a description/title that summarizes all of the information as personal data. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/accessibility/tasks/html-css/html/html-a11y2.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/accessibility/tasks/html-css/html/html-a11y2-download.html) to work in your own editor or in an online editor. ## HTML accessibility 3 In this task you are required to turn all the information links in the paragraph into good, accessible links. - The first two links just go to regular web pages. - The third link goes to a PDF, and it's large 8MB. - The fourth link goes to a Word document, so the user will need some kind of application installed that can handle that. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/accessibility/tasks/html-css/html/html-a11y3.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/accessibility/tasks/html-css/html/html-a11y3-download.html) to work in your own editor or in an online editor. ## HTML accessibility 4 In our final HTML accessibility task, you are given an image gallery, which has some accessibility problems. Can you fix them? - The header image has an accessibility issue, and so do the gallery images. - You could take the header image further and implement it using CSS for arguably better accessibility. Why is this better, and what would a solution look like? Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/accessibility/tasks/html-css/html/html-a11y4.html", '100%', 1100)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/accessibility/tasks/html-css/html/html-a11y4-download.html) to work in your own editor or in an online editor.