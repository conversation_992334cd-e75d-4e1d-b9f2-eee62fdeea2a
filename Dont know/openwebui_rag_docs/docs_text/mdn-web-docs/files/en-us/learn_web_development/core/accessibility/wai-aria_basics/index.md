Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > wai-aria_basics > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > wai-aria_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > wai-aria_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > wai-aria_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > accessibility > wai-aria_basics > index.md --- title: WAI-ARIA basics short-title: WAI-ARIA slug: Learn_web_development/Core/Accessibility/WAI-ARIA_basics page-type: learn-module-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Core/Accessibility/CSS_and_JavaScript","Learn_web_development/Core/Accessibility/Multimedia", "Learn_web_development/Core/Accessibility")}} Following on from the previous article, sometimes making complex UI controls that involve unsemantic HTML and dynamic JavaScript-updated content can be difficult. WAI-ARIA is a technology that can help with such problems by adding in further semantics that browsers and assistive technologies can recognize and use to let users know what is going on. Here we'll show how to use it at a basic level to improve accessibility. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td>Familiarity with <a href="/en-US/docs/Learn_web_development/Core/Structuring_content">HTML</a>, <a href="/en-US/docs/Learn_web_development/Core/Styling_basics">CSS</a>, and accessibility best practices as taught in previous lessons in the module.</a>.</td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>The purpose of WAI-ARIA to provide semantics to otherwise non-semantic HTML, so that AT users can make sense of the interfaces being presented to them.</li> <li>The basic syntax roles, properties, and states.</li> <li>Landmarks and signposting.</li> <li>Enhancing keyboard accessibility.</li> <li>Announcing dynamic content updates with live regions.</li> </ul> </td> </tr> </tbody> </table> ## What is WAI-ARIA? Let's start by looking at what WAI-ARIA is, and what it can do for us. ### A whole new set of problems As web apps started to get more complex and dynamic, a new set of accessibility features and problems started to appear. For example, HTML introduced a number of semantic elements to define common page features ({{htmlelement("nav")}}, {{htmlelement("footer")}}, etc.). Before these were available, developers would use {{htmlelement("div")}}s with IDs or classes, e.g., `<div class="nav">`, but these were problematic, as there was no easy way to easily find a specific page feature such as the main navigation programmatically. The initial solution was to add one or more hidden links at the top of the page to link to the navigation (or whatever else), for example: ```html <a href="#hidden" class="hidden">Skip to navigation</a> ``` But this is still not very precise, and can only be used when the screen reader is reading from the top of the page. As another example, apps started to feature complex controls like date pickers for choosing dates, sliders for choosing values, etc. HTML provides special input types to render such controls: ```html <input type="date" /> <input type="range" /> ``` These were originally not well-supported and it was, and still is to a lesser extent, difficult to style them, leading designers and developers to opt for custom solutions. Instead of using these native features, some developers rely on JavaScript libraries that generate such controls as a series of nested {{htmlelement("div")}}s which are then styled using CSS and controlled using JavaScript. The problem here is that visually they work, but screen readers can't make any sense of what they are at all, and their users just get told that they can see a jumble of elements with no semantics to describe what they mean. ### Enter WAI-ARIA [WAI-ARIA](https://www.w3.org/TR/wai-aria/) (Web Accessibility Initiative - Accessible Rich Internet Applications) is a specification written by the W3C, defining a set of additional HTML attributes that can be applied to elements to provide additional semantics and improve accessibility wherever it is lacking. There are three main features defined in the spec: - [Roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles) - : These define what an element is or does. Many of these are so-called landmark roles, which largely duplicate the semantic value of structural elements, such as `role="navigation"` ({{htmlelement("nav")}}), `role="banner"` (document {{htmlelement("header")}}), `role="complementary"` ({{htmlelement("aside")}}) or , `role="search"` ({{htmlelement("search")}}). Some other roles describe different page structures that do not have elements with that match those roles, such as `role="tablist"`, and `role="tabpanel"`, which are commonly found in UIs. - Properties - : These define properties of elements, which can be used to give them extra meaning or semantics. As an example, `aria-required="true"` specifies that a form input needs to be filled in order to be valid, whereas `aria-labelledby="label"` allows you to put an ID on an element, then reference it as being the label for anything else on the page, including multiple elements, which is not possible using `<label for="input">`. As an example, you could use `aria-labelledby` to specify that a key description contained in a {{htmlelement("div")}} is the label for multiple table cells, or you could use it as an alternative to image alt text specify existing information on the page as an image's alt text, rather than having to repeat it inside the `alt` attribute. You can see an example of this at [Text alternatives](/en-US/docs/Learn_web_development/Core/Accessibility/HTML#text_alternatives). - States - : Special properties that define the current conditions of elements, such as `aria-disabled="true"`, which specifies to a screen reader that a form input is currently disabled. States differ from properties in that properties don't change throughout the lifecycle of an app, whereas states can change, generally programmatically via JavaScript. An important point about WAI-ARIA attributes is that they don't affect anything about the web page, except for the information exposed by the browser's accessibility APIs (where screen readers get their information from). WAI-ARIA doesn't affect webpage structure, the DOM, etc., although the attributes can be useful for selecting elements by CSS. > [!NOTE] > You can find a useful list of all the ARIA roles and their uses, with links to further information, in the WAI-ARIA spec see [Definition of Roles](https://www.w3.org/TR/wai-aria-1.1/#role_definitions) on this site see [ARIA roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles). > > The spec also contains a list of all the properties and states, with links to further information see [Definitions of States and Properties (all `aria-*` attributes)](https://www.w3.org/TR/wai-aria-1.1/#state_prop_def). ### Where is WAI-ARIA supported? This is not an easy question to answer. It is difficult to find a conclusive resource that states what features of WAI-ARIA are supported, and where, because: 1. There are a lot of features in the WAI-ARIA spec. 2. There are many combinations of operating systems, browsers, and screen readers to consider. This last point is key To use a screen reader in the first place, your operating system needs to run browsers that have the necessary accessibility APIs in place to expose the information screen readers need to do their job. Most popular OSes have one or two browsers in place that screen readers can work with. The Paciello Group has a fairly up-to-date post that provides data for this see [Rough Guide: browsers, operating systems and screen reader support updated](https://www.tpgi.com/rough-guide-browsers-operating-systems-and-screen-reader-support-updated/). Next, you need to worry about whether the browsers in question support ARIA features and expose them via their APIs, but also whether screen readers recognize that information and present it to their users in a useful way. 1. Browser support is almost universal. 2. Screen reader support for ARIA features isn't quite at this level, but the most popular screen readers are getting there. You can get an idea of support levels by looking at Powermapper's [WAI-ARIA Screen reader compatibility](https://www.powermapper.com/tests/screen-readers/aria/) article. In this article, we won't attempt to cover every WAI-ARIA feature, and its exact support details. Instead, we will cover the most critical WAI-ARIA features for you to know about; if we don't mention any support details, you can assume that the feature is well-supported. We will clearly mention any exceptions to this. > [!NOTE] > Some JavaScript libraries support WAI-ARIA, meaning that when they generate UI features like complex form controls, they add ARIA attributes to improve the accessibility of those features. If you are looking for a 3rd party JavaScript solution for rapid UI development, you should definitely consider the accessibility of its UI widgets as an important factor when making your choice. Good examples are jQuery UI (see [About jQuery UI: Deep accessibility support](https://jqueryui.com/about/#deep-accessibility-support)), [ExtJS](https://www.sencha.com/products/extjs/), and [Dojo/Dijit](https://dojotoolkit.org/reference-guide/1.10/dijit/a11y/statement.html). ### When should you use WAI-ARIA? We talked about some of the problems that prompted WAI-ARIA to be created earlier on, but essentially, there are four main areas that WAI-ARIA is useful in: - Signposts/Landmarks - : ARIA's [`role`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles) attribute values can act as landmarks that either replicate the semantics of HTML elements (e.g., {{htmlelement("nav")}}), or go beyond HTML semantics to provide signposts to different functional areas, for example, `search`, `tablist`, `tab`, `listbox`, etc. - Dynamic content updates - : Screen readers tend to have difficulty with reporting constantly changing content; with ARIA we can use `aria-live` to inform screen reader users when an area of content is updated dynamically: for example, by JavaScript in the page [fetching new content from the server and updating the DOM](/en-US/docs/Learn_web_development/Core/Scripting/Network_requests). - Enhancing keyboard accessibility - : There are built-in HTML elements that have native keyboard accessibility; when other elements are used along with JavaScript to simulate similar interactions, keyboard accessibility and screen reader reporting suffers as a result. Where this is unavoidable, WAI-ARIA provides a means to allow other elements to receive focus (using `tabindex`). - Accessibility of non-semantic controls - : When a series of nested `<div>`s along with CSS/JavaScript is used to create a complex UI-feature, or a native control is greatly enhanced/changed via JavaScript, accessibility can suffer screen reader users will find it difficult to work out what the feature does if there are no semantics or other clues. In these situations, ARIA can help to provide what's missing with a combination of roles like `button`, `listbox`, or `tablist`, and properties like `aria-required` or `aria-posinset` to provide further clues as to functionality. #### You should only use WAI-ARIA when you need to! Using the correct HTML elements implicitly gives you the roles that are needed and you should _always_ use [native HTML features](/en-US/docs/Learn_web_development/Core/Accessibility/HTML) to provide the semantics required by screen readers to tell their users what is going on. Sometimes this isn't possible, either because you have limited control over the code, or because you are creating something complex that doesn't have an easy HTML element to implement it. In such cases, WAI-ARIA can be a valuable accessibility enhancing tool. But again, only use it when necessary! > [!NOTE] > Also, try to make sure you test your site with a variety of _real_ users non-disabled people, people using screen readers, people using keyboard navigation, etc. They will have better insights than you about how well it works. ## Practical WAI-ARIA implementations In the next section, we'll look at the four areas in more detail, along with practical examples. Before you continue, you should get a screen reader testing setup put in place, so you can test some of the examples as you go through. See our section on [testing screen readers](/en-US/docs/Learn_web_development/Core/Accessibility/Tooling#screen_readers) for more information. ### Signposts/Landmarks WAI-ARIA adds the [`role` attribute](https://www.w3.org/TR/wai-aria-1.1/#role_definitions) to browsers, which allows you to add extra semantic value to elements on your site wherever they are needed. The first major area in which this is useful is providing information for screen readers so that their users can find common page elements. This example has the following structure: ```html live-sample___aria-website-no-roles <header> <h1>Header</h1> <!-- Even is it's not mandatory, it's common practice to put the main navigation menu within the main header --> <nav> <ul> <li><a href="#">Home</a></li> <li><a href="#">Team</a></li> <li><a href="#">Projects</a></li> <li><a href="#">Contact</a></li> </ul> <!-- A Search form is another common non-linear way to navigate through a website. --> <form> <input type="search" name="q" placeholder="Search query" /> <input type="submit" value="Go!" /> </form> </nav> </header> <!-- Here is our page's main content --> <main> <!-- It contains an article --> <article> <h2>Article heading</h2> <p> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Donec a diam lectus. Set sit amet ipsum mauris. Maecenas congue ligula as quam viverra nec consectetur ant hendrerit. Donec et mollis dolor. Praesent et diam eget libero egestas mattis sit amet vitae augue. Nam tincidunt congue enim, ut porta lorem lacinia consectetur. </p> <h3>subsection</h3> <p> Donec ut librero sed accu vehicula ultricies a non tortor. Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aenean ut gravida lorem. Ut turpis felis, pulvinar a semper sed, adipiscing id dolor. </p> </article> <!-- the aside content can also be nested within the main content --> <aside> <h2>Related</h2> <ul> <li><a href="#">Oh I do like to be beside the seaside</a></li> <li><a href="#">Oh I do like to be beside the sea</a></li> <li><a href="#">Although in the North of England</a></li> <li><a href="#">It never stops raining</a></li> <li><a href="#">Oh well...</a></li> </ul> </aside> </main> <!-- And here is our main footer that is used across all the pages of our website --> <footer> <p> Copyright 2050 by nobody. All rights reversed.</p> </footer> ``` ```css hidden live-sample___aria-website-no-roles /* || General setup */ html, body { margin: 0; padding: 0; } html { font-size: 10px; background-color: #a9a9a9; } body { width: max(70vw, 90%); margin: 0 auto; padding: 0 10px; display: flex; flex-direction: column; } /* || typography */ h1, h2, h3 { font-family: "Sonsie One", cursive; color: #2a2a2a; } p, input, li { font-family: "Open Sans Condensed", sans-serif; color: #2a2a2a; } h1 { font-size: 4rem; text-align: center; color: white; text-shadow: 2px 2px 10px black; } h2 { font-size: 3rem; text-align: center; } h3 { font-size: 2.2rem; } p, li { font-size: 1.6rem; line-height: 1.5; } /* || header layout */ header { margin-bottom: 10px; } nav, article, aside, footer { background-color: white; padding: 1%; } nav { background-color: ff80ff; display: flex; gap: 2vw; @media (width <= 650px) { flex-direction: column; } } nav ul { padding: 0; list-style-type: none; flex: 2; display: flex; gap: 2vw; } nav li { display: inline; text-align: center; } nav a { display: inline-block; font-size: 2rem; text-transform: uppercase; text-decoration: none; color: black; } nav form { flex: 1; display: flex; align-items: center; height: 100%; } input { font-size: 1.6rem; height: 32px; } input[type="search"] { flex: 3; } input[type="submit"] { flex: 1; margin-left: 1rem; background: #333; border: 0; color: white; } /* || main layout */ main { display: flex; gap: 2vw; @media (width <= 650px) { flex-direction: column; } } article { flex: 4; } aside { flex: 1; background-color: #ff80ff; } aside li { padding-bottom: 10px; } footer { margin-top: 10px; } ``` {{EmbedLiveSample("aria-website-no-roles", "100", "850")}} If you try testing the example with a screen reader in a modern browser, you'll already get some useful information. For example, VoiceOver gives you the following: - On the `<header>` element "banner, 2 items" (it contains a heading and the `<nav>`). - On the `<nav>` element "navigation 2 items" (it contains a list and a form). - On the `<main>` element "main 2 items" (it contains an article and an aside). - On the `<aside>` element "complementary 2 items" (it contains a heading and a list). - On the search form input "Search query, insertion at beginning of text". - On the `<footer>` element "footer 1 item". If you go to VoiceOver's landmarks menu (accessed using VoiceOver key + U and then using the cursor keys to cycle through the menu choices), you'll see that most of the elements are nicely listed so they can be accessed quickly. ![Mac's VoiceOver menu for quick accessibility. Landmarks header and landmarks list including banner, navigation, main, and complementary.](landmarks-list.png) However, we could do better here. The search form is a really important landmark that people will want to find, but it is not listed in the landmarks menu or treated like a notable landmark beyond the actual input being called out as a search input (`<input type="search">`). We could improve it by the use of the ARIA `role="search"`, but using the {{htmlelement("search")}} element implicitly gives that role to the form. ```html live-sample___aria-website-roles <header> <h1>Header</h1> <!-- Even is it's not mandatory, it's common practice to put the main navigation menu within the main header --> <nav> <ul> <li><a href="#">Home</a></li> <li><a href="#">Our team</a></li> <li><a href="#">Projects</a></li> <li><a href="#">Contact</a></li> </ul> <!-- A Search form is another common non-linear way to navigate through a website. --> <search> <form> <input type="search" name="q" placeholder="Search query" aria-label="Search through site content" /> <input type="submit" value="Go!" /> </form> </search> </nav> </header> <!-- Here is our page's main content --> <main> <!-- It contains an article --> <article> <h2>Article heading</h2> <p> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Donec a diam lectus. Set sit amet ipsum mauris. Maecenas congue ligula as quam viverra nec consectetur ant hendrerit. Donec et mollis dolor. Praesent et diam eget libero egestas mattis sit amet vitae augue. Nam tincidunt congue enim, ut porta lorem lacinia consectetur. </p> <h3>subsection</h3> <p> Donec ut librero sed accu vehicula ultricies a non tortor. Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aenean ut gravida lorem. Ut turpis felis, pulvinar a semper sed, adipiscing id dolor. </p> <p> Pelientesque auctor nisi id magna consequat sagittis. Curabitur dapibus, enim sit amet elit pharetra tincidunt feugiat nist imperdiet. Ut convallis libero in urna ultrices accumsan. Donec sed odio eros. </p> </article> <!-- the aside content can also be nested within the main content --> <aside> <h2>Related</h2> <ul> <li><a href="#">Oh I do like to be beside the seaside</a></li> <li><a href="#">Oh I do like to be beside the sea</a></li> <li><a href="#">Although in the North of England</a></li> <li><a href="#">It never stops raining</a></li> <li><a href="#">Oh well...</a></li> </ul> </aside> </main> <!-- And here is our main footer that is used across all the pages of our website --> <footer> <p> Copyright 2050 by nobody. All rights reversed.</p> </footer> ``` ```css hidden live-sample___aria-website-roles /* || General setup */ html, body { margin: 0; padding: 0; } html { font-size: 10px; background-color: #a9a9a9; } body { width: max(70vw, 90%); margin: 0 auto; padding: 0 10px; display: flex; flex-direction: column; } /* || typography */ h1, h2, h3 { font-family: "Sonsie One", cursive; color: #2a2a2a; } p, input, li { font-family: "Open Sans Condensed", sans-serif; color: #2a2a2a; } h1 { font-size: 4rem; text-align: center; color: white; text-shadow: 2px 2px 10px black; } h2 { font-size: 3rem; text-align: center; } h3 { font-size: 2.2rem; } p, li { font-size: 1.6rem; line-height: 1.5; } /* || header layout */ header { margin-bottom: 10px; } nav, article, aside, footer { background-color: white; padding: 1%; } nav { background-color: ff80ff; display: flex; gap: 2vw; @media (width <= 650px) { flex-direction: column; } } nav ul { padding: 0; list-style-type: none; flex: 2; display: flex; gap: 2vw; } nav li { display: inline; text-align: center; } nav a { display: inline-block; font-size: 2rem; text-transform: uppercase; text-decoration: none; color: black; } nav form { flex: 1; display: flex; align-items: center; height: 100%; } input { font-size: 1.6rem; height: 32px; } input[type="search"] { flex: 3; } input[type="submit"] { flex: 1; margin-left: 1rem; background: #333; border: 0; color: white; } /* || main layout */ main { display: flex; gap: 2vw; @media (width <= 650px) { flex-direction: column; } } article { flex: 4; } aside { flex: 1; background-color: #ff80ff; } aside li { padding-bottom: 10px; } footer { margin-top: 10px; } ``` {{EmbedLiveSample("aria-website-roles", "100", "850")}} Most importantly, we have used semantic HTML that gives meaning and roles to the structure of the page without adding unnecessary [`role`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles) attributes to our HTML structure, which has a structure like this: ```html <header> <h1> </h1> <nav> <ul> </ul> <search> <form> <!-- search form --> </form> </search> </nav> </header> <main> <article> </article> <aside> </aside> </main> <footer> </footer> ``` We've also given you a bonus feature in this example the {{htmlelement("input")}} element has been given the attribute [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label), which gives it a descriptive label to be read out by a screen reader, even though we haven't included a {{htmlelement("label")}} element. In cases like these, this is very useful a search form like this one is a very common, easily recognized feature, and adding a visual label would spoil the page design. ```html <input type="search" name="q" placeholder="Search query" aria-label="Search through site content" /> ``` Now if we use VoiceOver to look at this example, we get some improvements: - The search form is called out as a separate item, both when browsing through the page, and in the Landmarks menu. - The label text contained in the `aria-label` attribute is read out when the form input is highlighted. If you need to support older browsers such as IE8; it is worth including ARIA roles for that purpose. And if for some reason your site is built using just `<div>`s, you should definitely include the ARIA roles to provide these much needed semantics! You'll see a lot more about these semantics and the power of ARIA properties/attributes below, especially in the [Accessibility of non-semantic controls](#accessibility_of_non-semantic_controls) section. For now though, let's look at how ARIA can help with dynamic content updates. ### Dynamic content updates Content loaded into the DOM can be easily accessed using a screen reader, from textual content to alternative text attached to images. Traditional static websites with largely text content are therefore easy to make accessible for people with visual impairments. The problem is that modern web apps are often not just static text they often update parts of the page by fetching new content from the server (in this example we are using a static array of quotes) and updating the DOM. These are sometimes referred to as **live regions**. ```html live-sample___aria-no-live <section> <h1>Random quote</h1> <blockquote> <p></p> </blockquote> </section> ``` ```css live-sample___aria-no-live html { font-family: sans-serif; } h1 { letter-spacing: 2px; } p { line-height: 1.6; } section { padding: 10px; width: calc(100% - 20px); background: #666; text-shadow: 1px 1px 1px black; color: white; min-height: 160px; } ``` ```js live-sample___aria-no-live let quotes = [ { quote: "Every child is an artist. The problem is how to remain an artist once he grows up.", author: "Pablo Picasso", }, { quote: "You can never cross the ocean until you have the courage to lose sight of the shore.", author: "Christopher Columbus", }, { quote: "I love deadlines. I love the whooshing noise they make as they go by.", author: "Douglas Adams", }, ]; ``` ```js live-sample___aria-no-live const quotePara = document.querySelector("section p"); window.setInterval(showQuote, 10000); function showQuote() { let random = Math.floor(Math.random() * quotes.length); quotePara.textContent = `${quotes[random].quote} -- ${quotes[random].author}`; } ``` {{EmbedLiveSample("aria-no-live", "100", "180")}} This works OK, but it is not good for accessibility the content update is not detected by screen readers, so their users would not know what is going on. This is a fairly trivial example, but just imagine if you were creating a complex UI with lots of constantly updating content, like a chat room, or a strategy game UI, or a live updating shopping cart display it would be impossible to use the app in any effective way without some kind of way of alerting the user to the updates. WAI-ARIA, fortunately, provides a useful mechanism to provide these alerts the [`aria-live`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-live) property. Applying this to an element causes screen readers to read out the content that is updated. How urgently the content is read out depends on the attribute value: - `off` - : The default. Updates should not be announced. - `polite` - : Updates should be announced only if the user is idle. - `assertive` - : Updates should be announced to the user as soon as possible. Here we update the `<section>` opening tag as follows: ```html <section aria-live="assertive"> </section> ``` This will cause a screen reader to read out the content as it is updated. There is an additional consideration here only the bit of text that updates is read out. It might be nice if we always read out the heading too, so the user can remember what is being read out. To do this, we can add the [`aria-atomic`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-atomic) property to the section. Update your `<section>` opening tag again, like so: ```html <section aria-live="assertive" aria-atomic="true"> </section> ``` The `aria-atomic="true"` attribute tells screen readers to read out the entire element contents as one atomic unit, not just the bits that were updated. ```html live-sample___aria-live <section aria-live="assertive" aria-atomic="true"> <h1>Random quote</h1> <blockquote> <p></p> </blockquote> </section> ``` ```css live-sample___aria-live html { font-family: sans-serif; } h1 { letter-spacing: 2px; } p { line-height: 1.6; } section { padding: 10px; width: calc(100% - 20px); background: #666; text-shadow: 1px 1px 1px black; color: white; min-height: 160px; } ``` ```js live-sample___aria-live let quotes = [ { quote: "Every child is an artist. The problem is how to remain an artist once he grows up.", author: "Pablo Picasso", }, { quote: "You can never cross the ocean until you have the courage to lose sight of the shore.", author: "Christopher Columbus", }, { quote: "I love deadlines. I love the whooshing noise they make as they go by.", author: "Douglas Adams", }, ]; ``` ```js live-sample___aria-live const quotePara = document.querySelector("section p"); window.setInterval(showQuote, 10000); function showQuote() { let random = Math.floor(Math.random() * quotes.length); quotePara.textContent = `${quotes[random].quote} -- ${quotes[random].author}`; } ``` {{EmbedLiveSample("aria-live", "100", "180")}} > [!NOTE] > The [`aria-relevant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-relevant) property is also quite useful for controlling what gets read out when a live region is updated. You can for example only get content additions or removals read out. ### Enhancing keyboard accessibility As discussed in a few other places in the module, one of the key strengths of HTML with respect to accessibility is the built-in keyboard accessibility of features such as buttons, form controls, and links. Generally, you can use the tab key to move between controls, the Enter/Return key to select or activate controls, and occasionally other controls as needed (for example the up and down cursor to move between options in a `<select>` box). However, sometimes you will end up having to write code that either uses non-semantic elements as buttons (or other types of control), or uses focusable controls for not quite the right purpose. You might be trying to fix some bad code you've inherited, or you might be building some kind of complex widget that requires it. In terms of making non-focusable code focusable, WAI-ARIA extends the `tabindex` attribute with some new values: - `tabindex="0"` as indicated above, this value allows elements that are not normally tabbable to become tabbable. This is the most useful value of `tabindex`. - `tabindex="-1"` this allows not normally tabbable elements to receive focus programmatically, e.g., via JavaScript, or as the target of links. We discussed this in more detail and showed a typical implementation back in our HTML accessibility article see [Building keyboard accessibility back in](/en-US/docs/Learn_web_development/Core/Accessibility/HTML#building_keyboard_accessibility_back_in). ### Accessibility of non-semantic controls This follows on from the previous section when a series of nested `<div>`s along with CSS/JavaScript is used to create a complex UI-feature, or a native control is greatly enhanced/changed via JavaScript, not only can keyboard accessibility suffer, but screen reader users will find it difficult to work out what the feature does if there are no semantics or other clues. In such situations, ARIA can help to provide those missing semantics. #### Form validation and error alerts First of all, let's revisit the form example we first looked at in our CSS and JavaScript accessibility article (read [Keeping it unobtrusive](/en-US/docs/Learn_web_development/Core/Accessibility/CSS_and_JavaScript#keeping_it_unobtrusive) for a full recap). At the end of this section, we showed that we have included some ARIA attributes on the error message box that displays any validation errors when you try to submit the form: ```html <div class="errors" role="alert" aria-relevant="all"> <ul></ul> </div> ``` - [`role="alert"`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/alert_role) automatically turns the element it is applied to into a live region, so changes to it are read out; it also semantically identifies it as an alert message (important time/context-sensitive information), and represents a better, more accessible way of delivering an alert to a user (modal dialogs like [`alert()`](/en-US/docs/Web/API/Window/alert) calls have a number of accessibility problems; see [Popup Windows](https://webaim.org/techniques/javascript/other#popups) by WebAIM). - An [`aria-relevant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-relevant) value of `all` instructs the screen reader to read out the contents of the error list when any changes are made to it i.e., when errors are added or removed. This is useful because the user will want to know what errors are left, not just what has been added or removed from the list. We could go further with our ARIA usage, and provide some more validation help. How about indicating whether fields are required in the first place, and what range the age should be? 1. At this point, take a copy of our [`form-validation.html`](https://github.com/mdn/learning-area/blob/main/accessibility/css/form-validation.html) and [`validation.js`](https://github.com/mdn/learning-area/blob/main/accessibility/css/validation.js) files, and save them in a local directory. 2. Open them both in a text editor and have a look at how the code works. 3. First of all, add a paragraph just above the opening `<form>` tag, like the one below, and mark both the form `<label>`s with an asterisk. This is normally how we mark required fields for sighted users. ```html <p>Fields marked with an asterisk (*) are required.</p> ``` 4. This makes visual sense, but it isn't as easy to understand for screen reader users. Fortunately, WAI-ARIA provides the [`aria-required`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-required) attribute to give screen readers hints that they should tell users that form inputs need to be filled in. Update the `<input>` elements like so: ```html <input type="text" name="name" id="name" aria-required="true" /> <input type="number" name="age" id="age" aria-required="true" /> ``` 5. If you save the example now and test it with a screen reader, you should hear something like "Enter your name star, required, edit text". 6. It might also be useful if we give screen reader users and sighted users an idea of what the age value should be. This is often presented as a tooltip or placeholder inside the form field. WAI-ARIA does include [`aria-valuemin`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-valuemin) and [`aria-valuemax`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-valuemax) properties to specify min and max values, and screen readers support the native `min` and `max` attributes. Another well-supported feature is the HTML `placeholder` attribute, which can contain a message that is shown in the input when no value is entered and is read out by a few screen readers. Update your number input like this: ```html <label for="age">Your age:</label> <input type="number" name="age" id="age" placeholder="Enter 1 to 150" required aria-required="true" /> ``` Always include a {{HTMLelement('label')}} for every input. While some screen readers announce the placeholder text, most do not. Acceptable substitutions for providing form controls with an accessible name include [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) and [`aria-labelledby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby). But the `<label>` element with a `for` attribute is the preferred method as it provides usability for all users, including mouse users. > [!NOTE] > You can see the finished example live at [`form-validation-updated.html`](https://mdn.github.io/learning-area/accessibility/aria/form-validation-updated.html). WAI-ARIA also enables some advanced form labelling techniques, beyond the classic {{htmlelement("label")}} element. We already talked about using the [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) property to provide a label where we don't want the label to be visible to sighted users (see the [Signposts/Landmarks](#signpostslandmarks) section, above). Some other labeling techniques use other properties such as [`aria-labelledby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby) if you want to designate a non-`<label>` element as a label or label multiple form inputs with the same label, and [`aria-describedby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-describedby), if you want to associate other information with a form input and have it read out as well. See [WebAIM's Advanced Form Labeling article](https://webaim.org/techniques/forms/advanced) for more details. There are many other useful properties and states too, for indicating the status of form elements. For example, `aria-disabled="true"` can be used to indicate that a form field is disabled. Many browsers will skip past disabled form fields which leads to them not being read out by screen readers. In some cases, a disabled element will be perceived, so it is a good idea to include this attribute to let the screen reader know that a disabled form control is in fact disabled. If the disabled state of an input is likely to change, then it is also a good idea to indicate when it happens, and what the result is. For example, in our [`form-validation-checkbox-disabled.html`](https://mdn.github.io/learning-area/accessibility/aria/form-validation-checkbox-disabled.html) demo, there is a checkbox that when checked, enables another form input to allow further information to be entered. We've set up a hidden live region: ```html <p class="hidden-alert" aria-live="assertive"></p> ``` which is hidden from view using absolute positioning. When this is checked/unchecked, we update the text inside the hidden live region to tell screen reader users what the result of checking this checkbox is, as well as updating the `aria-disabled` state, and some visual indicators too: ```js function toggleMusician(bool) { const instrument = formItems[formItems.length - 1]; if (bool) { instrument.input.disabled = false; instrument.label.style.color = "#000"; instrument.input.setAttribute("aria-disabled", "false"); hiddenAlert.textContent = "Instruments played field now enabled; use it to tell us what you play."; } else { instrument.input.disabled = true; instrument.label.style.color = "#999"; instrument.input.setAttribute("aria-disabled", "true"); instrument.input.removeAttribute("aria-label"); hiddenAlert.textContent = "Instruments played field now disabled."; } } ``` #### Describing non-semantic buttons as buttons A few times in this course already, we've mentioned the native accessibility of (and the accessibility issues behind using other elements to fake) buttons, links, or form elements (see [Use semantic UI controls where possible](/en-US/docs/Learn_web_development/Core/Accessibility/HTML#use_semantic_ui_controls_where_possible) in the HTML accessibility article, and [Enhancing keyboard accessibility](#enhancing_keyboard_accessibility), above). Basically, you can add keyboard accessibility back in without too much trouble in many cases, using `tabindex` and a bit of JavaScript. But what about screen readers? They still won't see the elements as buttons. If we test our [`fake-div-buttons.html`](https://mdn.github.io/learning-area/tools-testing/cross-browser-testing/accessibility/fake-div-buttons.html) example in a screen reader, our fake buttons will be reported using phrases like "Click me!, group", which is obviously confusing. We can fix this using a WAI-ARIA role. Make a local copy of [`fake-div-buttons.html`](https://github.com/mdn/learning-area/blob/main/tools-testing/cross-browser-testing/accessibility/fake-div-buttons.html), and add [`role="button"`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/button_role) to each button `<div>`, for example: ```html <div data-message="This is from the first button" tabindex="0" role="button"> Click me! </div> ``` Now when you try this using a screen reader, you'll have buttons be reported using phrases like "Click me!, button". While this is much better, you still have to add in all the native button features users expect, like handling <kbd>enter</kbd> and click events, as explained in the [`button` role documentation](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/button_role). > [!NOTE] > Don't forget however that using the correct semantic element where possible is always better. If you want to create a button, and can use a {{htmlelement("button")}} element, you should use a {{htmlelement("button")}} element! #### Guiding users through complex widgets There are a whole host of other [roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles) that can identify non-semantic element structures as common UI features that go beyond what's available in standard HTML, for example [`combobox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/combobox_role), [`slider`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/slider_role), [`tabpanel`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tabpanel_role), [`tree`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tree_role). You can see several useful examples in the [Deque university code library](https://dequeuniversity.com/library/) to give you an idea of how such controls can be made accessible. Let's go through an example of our own. We'll return to our simple absolutely-positioned tabbed interface (see [Hiding things](/en-US/docs/Learn_web_development/Core/Accessibility/CSS_and_JavaScript#hiding_things) in our CSS and JavaScript accessibility article), which you can find at [Tabbed info box example](/en-US/docs/Learn_web_development/Core/CSS_layout/Practical_positioning_examples#a_tabbed_info-box). ```html live-sample___aria-tabbed-info-box <section class="info-box"> <div role="tablist" class="manual"> <button id="tab-1" type="button" role="tab" aria-selected="true" aria-controls="tabpanel-1"> <span>Tab 1</span> </button> <button id="tab-2" type="button" role="tab" aria-selected="false" aria-controls="tabpanel-2" tabindex="-1"> <span>Tab 2</span> </button> <button id="tab-3" type="button" role="tab" aria-selected="false" aria-controls="tabpanel-3" tabindex="-1"> <span>Tab 3</span> </button> </div> <div class="panels"> <article id="tabpanel-1" role="tabpanel" aria-labelledby="tab-1"> <h2>The first tab</h2> <p>This is the content for tab one and is just a paragraph.</p> </article> <article id="tabpanel-2" role="tabpanel" aria-labelledby="tab-2" class="is-hidden"> <h2>The second tab</h2> <p>This is the content for tab two and is just a paragraph.</p> </article> <article id="tabpanel-3" role="tabpanel" aria-labelledby="tab-3" class="is-hidden"> <h2>The third tab</h2> <p>This is the content for tab three and is a paragraph and a list.</p> <ul> <li>Cat</li> <li>Dog</li> <li>Horse</li> </ul> </article> </div> </section> ``` ```css live-sample___aria-tabbed-info-box /* General setup */ html { font-family: sans-serif; } * { box-sizing: border-box; } body { margin: 0; } /* info-box setup */ .info-box { width: 452px; height: 250px; margin: 1.25rem auto 0; } /* styling info-box tabs */ .info-box [role="tablist"] { min-width: 100%; display: flex; } .info-box [role="tab"] { border: none; background: white; padding: 0 1rem 0 1rem; line-height: 3rem; color: #b60000; font-weight: bold; outline: none; } .info-box [role="tab"]:focus span, .info-box [role="tab"]:hover span { outline: 1px solid blue; outline-offset: 6px; border-radius: 4px; } .info-box [role="tab"][aria-selected="true"] { background-color: #b60000; color: white; } /* styling info-box panels */ .info-box .panels { height: 200px; clear: both; position: relative; } .info-box [role="tabpanel"] { color: white; position: absolute; padding: 0.8rem 1.2rem; height: 200px; width: 100%; top: 0; background-color: #b60000; left: 0; } .info-box [role="tabpanel"].is-hidden { display: none; } ``` ```js live-sample___aria-tabbed-info-box class TabsManual { constructor(groupNode) { this.tablistNode = groupNode; this.tabs = []; this.firstTab = null; this.lastTab = null; this.tabs = Array.from(this.tablistNode.querySelectorAll("[role=tab]")); this.tabpanels = []; for (let i = 0; i < this.tabs.length; i += 1) { const tab = this.tabs[i]; const tabpanel = document.getElementById( tab.getAttribute("aria-controls"), ); tab.tabIndex = -1; tab.setAttribute("aria-selected", "false"); this.tabpanels.push(tabpanel); tab.addEventListener("keydown", this.onKeydown.bind(this)); tab.addEventListener("click", this.onClick.bind(this)); if (!this.firstTab) { this.firstTab = tab; } this.lastTab = tab; } this.setSelectedTab(this.firstTab); } setSelectedTab(currentTab) { for (let i = 0; i < this.tabs.length; i += 1) { const tab = this.tabs[i]; if (currentTab === tab) { tab.setAttribute("aria-selected", "true"); tab.removeAttribute("tabindex"); this.tabpanels[i].classList.remove("is-hidden"); } else { tab.setAttribute("aria-selected", "false"); tab.tabIndex = -1; this.tabpanels[i].classList.add("is-hidden"); } } } moveFocusToTab(currentTab) { currentTab.focus(); } moveFocusToPreviousTab(currentTab) { let index; if (currentTab === this.firstTab) { this.moveFocusToTab(this.lastTab); } else { index = this.tabs.indexOf(currentTab); this.moveFocusToTab(this.tabs[index - 1]); } } moveFocusToNextTab(currentTab) { let index; if (currentTab === this.lastTab) { this.moveFocusToTab(this.firstTab); } else { index = this.tabs.indexOf(currentTab); this.moveFocusToTab(this.tabs[index + 1]); } } /* EVENT HANDLERS */ onKeydown(event) { const tgt = event.currentTarget; let flag = false; switch (event.key) { case "ArrowLeft": this.moveFocusToPreviousTab(tgt); flag = true; break; case "ArrowRight": this.moveFocusToNextTab(tgt); flag = true; break; case "Home": this.moveFocusToTab(this.firstTab); flag = true; break; case "End": this.moveFocusToTab(this.lastTab); flag = true; break; default: break; } if (flag) { event.stopPropagation(); event.preventDefault(); } } // Since this example uses buttons for the tabs, the click onr also is activated // with the space and enter keys onClick(event) { this.setSelectedTab(event.currentTarget); } } // Initialize tablist window.addEventListener("load", function () { const tablists = document.querySelectorAll("[role=tablist].manual"); for (let i = 0; i < tablists.length; i++) { new TabsManual(tablists[i]); } }); ``` {{EmbedLiveSample("aria-tabbed-info-box", "100", "270")}} In this example we have used a combination of semantic elements, aria roles and aria attributes. The first of these is we have used a {{htmlelement("button")}} element as a _tab_, this means that the tab can be selected via a mouse click or via the keyboard using space or enter. ARIA features used include: - New roles [`tablist`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tablist_role), [`tab`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tab_role), [`tabpanel`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tabpanel_role) - : These identify the important areas of the tabbed interface the container for the tabs, the tabs themselves, and the corresponding tabpanels. - [`aria-selected`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-selected) - : Defines which tab is currently selected. As different tabs are selected by the user, the value of this attribute on the different tabs is updated via JavaScript. - `tabindex="-1"` - : `tabindex="-1"` takes the element out of the tab order. As we are using JavaScript to allow the user to control the tabs via keyboard or mouse we do not want the user to be able to use the tab key to navigate to the buttons. - [`aria-labelledby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby) - : This attribute identifies an element (by its `id`) that labels the element, in this example the `<article>` is labelled by the corresponding tab or `<button>`. - [`aria-controls`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-controls) - : This attribute identifies an element (by its `id`) that is controlled the element, in this example the `<article>` is controlled by the corresponding tab or `<button>`. We could have used `aria-hidden` to hide the content of the tabpanels from assistive technologies but if that content contained focusable content, such as links, the user would still be able to tab to that content even when aria-hidden=true is set for the non-active panels. In this example we have applied `class="is-hidden"` to tabpanels that correspond to the tabs with `aria-selected="false"` and using CSS to `display: none;` which prevents the hidden content from being tabbed to. In our tests, this new structure did serve to improve things overall. The `<button>`s are now recognized as tabs (e.g., "tab" is spoken by the screen reader), the selected tab is indicated by "selected" being read out with the tab name and any content that is not shown can not be tabbed to. The user can also navigate the tabs with keyboard or mouse. ## Test your skills! You've reached the end of this article, but can you remember the most important information? You can find some further tests to verify that you've retained this information before you move on see [Test your skills: WAI-ARIA](/en-US/docs/Learn_web_development/Core/Accessibility/Test_your_skills/WAI-ARIA). ## Summary This article has by no means covered all that's available in WAI-ARIA, but it should have given you enough information to understand how to use it, and know some of the most common patterns you will encounter that require it. ## See also - [Aria states and properties](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes): All `aria-*` attributes - [WAI-ARIA roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles): Categories of ARIA roles and the roles covered on MDN - [ARIA in HTML](https://www.w3.org/TR/html-aria/) on W3C: A specification that defines, for each HTML feature, the accessibility (ARIA) semantics implicitly applied on it by the browser and the WAI-ARIA features you may set on it if extra semantics are required - [Deque university code library](https://dequeuniversity.com/library/): A library of really useful and practical examples showing complex UI controls made accessible using WAI-ARIA features - [WAI-ARIA authoring practices](https://www.w3.org/WAI/ARIA/apg/) on W3C: A very detailed design pattern from the W3C, explaining how to implement different types of complex UI control whilst making them accessible using WAI-ARIA features {{PreviousMenuNext("Learn_web_development/Core/Accessibility/CSS_and_JavaScript","Learn_web_development/Core/Accessibility/Multimedia", "Learn_web_development/Core/Accessibility")}}