Path: mdn-web-docs > files > en-us > learn_web_development > core > css_layout > test_your_skills > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > css_layout > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > css_layout > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > css_layout > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > css_layout > test_your_skills > index.md --- title: "Test your skills: CSS layout" short-title: Test your skills slug: Learn_web_development/Core/CSS_layout/Test_your_skills page-type: learn-module-assessment sidebar: learnsidebar --- This page lists CSS layout tests you can try so you can verify if you've understood the content in this module. ## Test your CSS layout skills by topic {{SubpagesWithSummaries}} ## See also - [CSS layout](/en-US/docs/Learn_web_development/Core/CSS_layout)