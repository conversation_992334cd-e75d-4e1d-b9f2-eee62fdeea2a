Path: mdn-web-docs > files > en-us > learn_web_development > core > design_for_developers > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > design_for_developers > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > design_for_developers > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > design_for_developers > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > design_for_developers > index.md --- title: Design for developers slug: Learn_web_development/Core/Design_for_developers page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Core/Version_control", "Learn_web_development/Core")}} > [!NOTE] > As you'll see below, this module is currently only a curriculum outline/syllabus. We are intending to convert this into a full course in the future, as time allows. The idea of this module is to (re-)introduce developers to design thinking. They may not want to work as designers, but having some basic user experience and design theory is good for everyone involved in building websites, no matter what their role. At the very least, even the most technical, "non-designer" developer should understand design briefs, why things are designed as they are, and be able to get into the mindset of the user. And it'll help them make their portfolios look better. In addition, front-end developers are often tasked with doing various bits of design work on projects. Clients and employers often assume that they can do it because they are involved with the visual elements of the website. Historically, "web developer" used to be more of a hybrid designer/developer role than it is today. ## Prerequisites Before starting this module, you should be familiar with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). > [!NOTE] > If you are working on a computer/tablet/other device where you don't have the ability to create your own files, you could try out (most of) the code examples in an online coding program such as [JSBin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Lessons ## Basic design theory Learning outcomes: - UI design fundamentals: - Contrast. - Typography. - Visual Hierarchy. - Scale. - Alignment. - Use of whitespace. - Color theory. - Use of images. Resources: - [Fundamental text and font styling](/en-US/docs/Learn_web_development/Core/Text_styling/Fundamentals) ## User-centered design Learning outcomes: - Understand that everything we do is for the user. - Intro to user research/testing, and user requirements. - Design for accessibility consider the target audience and what additional needs they may have. Design for those from the very start. - Understand what design patterns are, and the common patterns used on the web, for example: - Dark mode. - Breadcrumbs. - Cards. - Deferred/Lazy registration. - Infinite scroll. - Modal dialogs. - Progressive disclosure. - Progress indication on forms/registration/setup. - Shopping cart. Resources: - [Accessibility overview](/en-US/docs/Learn_web_development/Core/Accessibility) - [Inclusive design principles](https://inclusivedesignprinciples.info/) ## Design briefs Learning outcomes: - Speaking design language, to communicate with designers. - Interpreting design brief requirements to produce an implementation. - Typical tools designers use to get their message across to developers (e.g., Figma). ## See also - [Learn UI Design Fundamentals](https://scrimba.com/intro-to-ui-design-fundamentals-c0q?via=mdn), Scrimba <sup>Course Partner</sup> - [The Shape of Design](https://shapeofdesignbook.com/chapters/00-introduction/), Frank Chimero - [Designing for the Web](https://designingfortheweb.co.uk/), Mark Boulton - [Design for web](https://designforweb.org/), Prisca Schmarsow + other contributors - [Practical Typography](https://practicaltypography.com/), Matthew Butterick - [Web Style Guide](https://webstyleguide.com/), Patrick J. Lynch and Sarah Horton - [Visual design rules you can safely follow every time](https://anthonyhobday.com/sideprojects/saferules/), Anthony Hobday - [16 little UI design rules that make a big impact](https://www.adhamdannaway.com/blog/ui-design/ui-design-tips), Adham Dannaway {{NextMenu("Learn_web_development/Core/Version_control", "Learn_web_development/Core")}}