Path: mdn-web-docs > files > en-us > learn_web_development > core > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > index.md --- title: Core learning modules slug: Learn_web_development/Core page-type: landing-page sidebar: learnsidebar --- Our **Core modules** cover topics that we feel every web developer should have a good grounding in. This includes all the information they need to design and build a basic, accessible web app that follows modern best practices, and manage and deploy their code using appropriate tools. ## Prerequisites While there is no prerequisite knowledge for starting this set of modules, we would recommend that you familiarize yourself with your computer, make sure you've got a basic set of required applications installed (such as web browsers and a code editor), and do some background reading on the web and web technologies, if you have not already done so. All these topics are covered in our [Getting started modules](/en-US/docs/Learn_web_development/Getting_started). In particular, if you've never done any coding before, we'd recommend the [Your first website](/en-US/docs/Learn_web_development/Getting_started/Your_first_website) module, which introduces you web technologies very gently and takes you through the process of building a simple website. ## Modules - [Structuring content with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) - : To build a high-quality, usable, accessible website, you need to understand how to define your content structure using semantic HTML. This module covers the basics of the HTML language, before looking at key areas such as document structure, links, lists, images, forms, and more. - [CSS styling basics](/en-US/docs/Learn_web_development/Core/Styling_basics) - : CSS (Cascading Style Sheets) is used to style and layout web pages for example, to alter the font, color, size, and spacing of your content, split it into multiple columns, or add animations and other decorative features. This module provides all the CSS fundamentals you'll need for now, including syntax, features, and techniques. - [CSS text styling](/en-US/docs/Learn_web_development/Core/Text_styling) - : With the basics of the CSS language covered, the next CSS topic for you to concentrate on is styling text one of the most common things you'll do with CSS. Here we look at fundamentals including setting font, boldness, italics, line and letter spacing, drop shadows, and other text features. We round off the module by looking at applying custom fonts to your page, and styling lists and links. - [CSS layout](/en-US/docs/Learn_web_development/Core/CSS_layout) - : In previous modules we looked at how to style and manipulate the boxes that your content sits inside. Now it's time to look at how to correctly lay out your boxes in relation to one another, and the browser viewport. This module looks at floats, positioning, other modern layout tools, and building responsive designs that will adapt to different devices, screen sizes, and resolutions. - [Dynamic scripting with JavaScript](/en-US/docs/Learn_web_development/Core/Scripting) - : JavaScript is a huge topic, with so many different features, styles, and techniques to learn, and so many APIs and tools built on top of it. This module focuses mostly on the essentials of the core language, plus some key surrounding topics learning these topics will give you a solid basis to work from. - [JavaScript frameworks and libraries](/en-US/docs/Learn_web_development/Core/Frameworks_libraries) - : JavaScript frameworks are an essential part of modern front-end web development, providing developers with tried and tested tools for building scalable, interactive web applications. Many modern companies use frameworks as a standard part of their tooling, so many front-end development jobs now require framework experience. In this set of articles, we aim to give you a comfortable starting point to help you begin learning frameworks. - [Accessibility](/en-US/docs/Learn_web_development/Core/Accessibility) - : Access to web content such as public services, education, e-commerce sites, and entertainment is a human right. No one should be excluded based on disability, race, geography, or other human characteristics. This module discusses the best practices and techniques you should learn to make your websites as accessible as possible. - [Design for developers](/en-US/docs/Learn_web_development/Core/Design_for_developers) - : The idea of this module is to (re-)introduce developers to design thinking. They may not want to work as designers, but having some basic user experience and design theory is good for everyone involved in building websites, no matter what their role. At the very least, even the most technical, "non-designer" developer should understand design briefs, why things are designed as they are, and be able to get into the mindset of the user. And it'll help them make their portfolios look better. - [Version control](/en-US/docs/Learn_web_development/Core/Version_control) - : Version control tools are an essential part of modern workflows, for backing up and collaborating on codebases. This module takes you through the essentials of version control using Git and GitHub. ## See also - [The Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j?via=mdn) <sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup> - : [Scrimba's](https://scrimba.com/?via=mdn) _Frontend Developer Career Path_ teaches all you need to know to be a competent front-end web developer, with fun interactive lessons and challenges, knowledgeable teachers, and a supportive community. Go from zero to landing your first front-end job! Many of the course components are available as standalone free versions.