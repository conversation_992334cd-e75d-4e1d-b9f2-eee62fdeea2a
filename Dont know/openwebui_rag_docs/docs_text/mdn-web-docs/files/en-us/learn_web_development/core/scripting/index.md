Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > index.md --- title: Dynamic scripting with JavaScript short-title: JavaScript slug: Learn_web_development/Core/Scripting page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Core/Scripting/What_is_JavaScript", "Learn_web_development/Core")}} JavaScript is a huge topic, with so many different features, styles, and techniques to learn, and so many APIs and tools built on top of it. This module focuses mostly on the essentials of the core language, plus some key surrounding topics learning these topics will give you a solid basis to work from. ## Prerequisites Before starting this module, you don't need any previous JavaScript knowledge, but you should have worked through the previous modules in the course. You should at least know [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and the [basic fundamentals of CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). > [!NOTE] > If you are working on a computer/tablet/other device where you don't have the ability to create your own files, you could try out (most of) the code examples in an online coding program such as [JSBin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials and challenges - [What is JavaScript?](/en-US/docs/Learn_web_development/Core/Scripting/What_is_JavaScript) - : Welcome to the MDN beginner's JavaScript course! In this first article we will look at JavaScript from a high level, answering questions such as "what is it?", and "what is it doing?", and making sure you are comfortable with JavaScript's purpose. - [A first splash into JavaScript](/en-US/docs/Learn_web_development/Core/Scripting/A_first_splash) - : Now you've learned something about the theory of JavaScript, and what you can do with it, we are going to give you a crash course on the basic features of JavaScript via a completely practical tutorial. Here you'll build up a simple "Guess the number" game, step by step. - [What went wrong? Troubleshooting JavaScript](/en-US/docs/Learn_web_development/Core/Scripting/What_went_wrong) - : When you built up the "Guess the number" game in the previous article, you may have found that it didn't work. Never fear this article aims to save you from tearing your hair out over such problems by providing you with some simple tips on how to find and fix errors in JavaScript programs. - [Storing the information you need Variables](/en-US/docs/Learn_web_development/Core/Scripting/Variables) - : After reading the last couple of articles you should now know what JavaScript is, what it can do for you, how you use it alongside other web technologies, and what its main features look like from a high level. In this article, we will get down to the real basics, looking at how to work with the most basic building blocks of JavaScript Variables. - [Basic math in JavaScript numbers and operators](/en-US/docs/Learn_web_development/Core/Scripting/Math) - : At this point in the course, we discuss maths in JavaScript how we can combine operators and other features to successfully manipulate numbers to do our bidding. - [Handling text strings in JavaScript](/en-US/docs/Learn_web_development/Core/Scripting/Strings) - : Next, we'll turn our attention to strings this is what pieces of text are called in programming. In this article, we'll look at all the common things that you really ought to know about strings when learning JavaScript, such as creating strings, escaping quotes in strings, and joining them together. - [Useful string methods](/en-US/docs/Learn_web_development/Core/Scripting/Useful_string_methods) - : Now we've looked at the very basics of strings, let's move up a gear and start thinking about what useful operations we can do on strings with built-in methods, such as finding the length of a text string, joining and splitting strings, substituting one character in a string for another, and more. - [Arrays](/en-US/docs/Learn_web_development/Core/Scripting/Arrays) - : In this lesson we'll look at arrays a neat way of storing a list of data items under a single variable name. Here we look at why this is useful, then explore how to create an array, retrieve, add, and remove items stored in an array, and more besides. - [Challenge: Silly story generator](/en-US/docs/Learn_web_development/Core/Scripting/Silly_story_generator) <sup>Challenge</sup> - : In this challenge, you'll be tasked with taking some of the knowledge you've picked up in this module's articles and applying it to creating a fun app that generates random silly stories. Have fun! - [Making decisions in your code conditionals](/en-US/docs/Learn_web_development/Core/Scripting/Conditionals) - : In any programming language, the code needs to make decisions and carry out actions accordingly depending on different inputs. For example, in a game, if the player's number of lives is 0, then it's game over. In a weather app, if it is being looked at in the morning, show a sunrise graphic; show stars and a moon if it is nighttime. In this article, we'll explore how so-called conditional statements work in JavaScript. - [Looping code](/en-US/docs/Learn_web_development/Core/Scripting/Loops) - : Programming languages are very useful for rapidly completing repetitive tasks, from multiple basic calculations to just about any other situation where you've got a lot of similar items of work to complete. Here we'll look at the loop structures available in JavaScript that handle such needs. - [Functions reusable blocks of code](/en-US/docs/Learn_web_development/Core/Scripting/Functions) - : Another essential concept in coding is **functions**, which allow you to store a piece of code that does a single task inside a defined block, and then call that code whenever you need it using a single short command rather than having to type out the same code multiple times. In this article we'll explore fundamental concepts behind functions such as basic syntax, how to invoke and define them, scope, and parameters. - [Build your own function](/en-US/docs/Learn_web_development/Core/Scripting/Build_your_own_function) - : With most of the essential theory dealt with in the previous article, this article provides practical experience. Here you will get some practice building your own, custom function. Along the way, we'll also explain some useful details of dealing with functions. - [Function return values](/en-US/docs/Learn_web_development/Core/Scripting/Return_values) - : There's one last essential concept about functions for us to discuss return values. Some functions don't return a significant value, but others do. It's important to understand what their values are, how to use them in your code, and how to make functions return useful values. We'll cover all of these below. - [Introduction to events](/en-US/docs/Learn_web_development/Core/Scripting/Events) - : In this article, we discuss some important concepts surrounding events, and look at the fundamentals of how they work in browsers. - [Event bubbling](/en-US/docs/Learn_web_development/Core/Scripting/Event_bubbling) - : This article introduces the concepts of event bubbling, event capture, and event delegation, which are all about what happens when you add a listener to an element that contains another element, and an event then happens to the contained element. - [Challenge: Image gallery](/en-US/docs/Learn_web_development/Core/Scripting/Image_gallery) <sup>Challenge</sup> - : Now that we've looked at the fundamental building blocks of JavaScript, we'll test your knowledge of loops, functions, conditionals and events by getting you to build a fairly common item you'll see on a lot of websites a JavaScript-powered image gallery. - [Object basics](/en-US/docs/Learn_web_development/Core/Scripting/Object_basics) - : In this article, we'll look at fundamental JavaScript object syntax, and revisit some JavaScript features that we've already seen earlier in the course, reiterating the fact that many of the features you've already dealt with are objects. - [DOM scripting introduction](/en-US/docs/Learn_web_development/Core/Scripting/DOM_scripting) - : When writing web pages and apps, one of the most common things you'll want to do is change the document structure in some way. This is usually done by manipulating the Document Object Model (DOM) via a set of built-in browser APIs for controlling HTML and styling information. In this article we'll introduce you to **DOM scripting**. - [Making network requests with JavaScript](/en-US/docs/Learn_web_development/Core/Scripting/Network_requests) - : Another very common task in modern websites and applications is making network requests to retrieve individual data items from the server to update sections of a webpage without having to load an entire new page. This seemingly small detail has had a huge impact on the performance and behavior of sites, so in this article, we'll explain the concept and look at technologies that make it possible. - [Working with JSON](/en-US/docs/Learn_web_development/Core/Scripting/JSON) - : JavaScript Object Notation (JSON) is a standard text-based format for representing structured data based on JavaScript object syntax. It is commonly used for transmitting data in web applications (e.g., sending some data from the server to the client, so it can be displayed on a web page, or vice versa). You'll come across it quite often, so in this article, we give you all you need to work with JSON using JavaScript, including parsing JSON so you can access data within it, and creating JSON. - [JavaScript debugging and error handling](/en-US/docs/Learn_web_development/Core/Scripting/Debugging_JavaScript) - : In this lesson, we will return to the subject of debugging JavaScript (which we first looked at in [What went wrong?](/en-US/docs/Learn_web_development/Core/Scripting/What_went_wrong)). Here we will delve deeper into techniques for tracking down errors, but also look at how to code defensively and handle errors in your code, avoiding problems in the first place. - [Test your skills: JavaScript](/en-US/docs/Learn_web_development/Core/Scripting/Test_your_skills) - : This page lists JavaScript tests you can try so you can verify if you've understood the content in this module. ## See also - [Scrimba: Learn JavaScript](https://scrimba.com/learn-javascript-c0v?via=mdn) <sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup> - : [Scrimba's](https://scrimba.com/?via=mdn) _Learn JavaScript_ course teaches you JavaScript through solving 140+ interactive coding challenges, building projects including a game, a browser extension, and even a mobile app. Scrimba features fun interactive lessons taught by knowledgeable teachers. - [Learn JavaScript](https://learnjavascript.online/) - : An excellent resource for aspiring web developers Learn JavaScript in an interactive environment, with short lessons and interactive tests, guided by automated assessment. The first 40 lessons are free, and the complete course is available for a small one-time payment. {{NextMenu("Learn_web_development/Core/Scripting/What_is_JavaScript", "Learn_web_development/Core")}}