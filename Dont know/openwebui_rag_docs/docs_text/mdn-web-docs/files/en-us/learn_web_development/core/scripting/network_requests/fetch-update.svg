<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="652" height="301" viewBox="-0.5 -0.5 652 301"><path fill="none" pointer-events="all" d="M78 0h98v98H78z"/><path d="M162.2 81.77v-6.61h-16.23v-3.79h27.55c.99 0 1.79-.73 1.79-1.63V1.64c0-.91-.8-1.64-1.79-1.64H80.14c-.99 0-1.79.73-1.79 1.64v68.09c0 .91.8 1.63 1.79 1.63h27.88v3.79H91.8v6.61L78 90.75V98h98v-7.25l-13.8-8.98zM84.9 64.93V5.92c0-.78.69-1.41 1.53-1.41h80.43c.85 0 1.54.63 1.54 1.41v59.01c0 .78-.7 1.41-1.54 1.41H86.44c-.85 0-1.54-.64-1.54-1.41zm-2.08 25.5 12.44-8.03h63.48l12.42 8.03H82.82z" pointer-events="all"/><path fill="#FFF" stroke="#000" stroke-width="5" pointer-events="all" d="M529 9h120v60H529z"/><path d="M541 18v10m20-10v10m-10-10v10" fill="none" stroke="#000" stroke-width="5" stroke-miterlimit="10" pointer-events="stroke"/><path d="M128 123h451.76" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m585.76 123-8 4 2-4-2-4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g font-family="Helvetica" text-anchor="middle" font-size="16"><path fill="#FFF" d="M237 121h256v20H237z"/><text x="364" y="126.5"> Request page (HTML, JS, CSS, ...) </text></g><path d="M128 200h451.76" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m585.76 200-8 4 2-4-2-4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g font-family="Helvetica" text-anchor="middle" font-size="16"><path fill="#FFF" d="M312 198h106v20H312z"/><text x="364" y="203.5"> Request data  </text></g><path d="M126.5 298V98M588 290V69" fill="none" stroke="#000" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="9 9" pointer-events="stroke"/><path d="M588 150H136.24" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 150 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M588 230H136.24" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 230 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M8 180h101.76" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m115.76 180-8 4 2-4-2-4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g font-family="Helvetica" text-anchor="middle" font-size="16"><path fill="#FFF" d="M28 171h59v20H28z"/><text x="56" y="186"> search  </text></g><path d="M128 250q60 0 60 20t-51.76 20" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 290 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path fill="none" pointer-events="all" d="M194 254h94v30h-94z"/><switch transform="translate(-.5 -.5)"><foreignObject style="overflow:visible;text-align:left" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display:flex;align-items:unsafe flex-start;justify-content:unsafe flex-start;width:92px;height:1px;padding-top:261px;margin-left:196px"><div style="box-sizing:border-box;font-size:0;text-align:left" data-drawio-colors="color: rgb(0 0 0);"><div style="display:inline-block;font-size:16px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">update page</div></div></div></foreignObject><text x="196" y="277" font-family="Helvetica" font-size="16">update page</text></switch><path fill="none" pointer-events="all" d="M78 0h98v98H78z"/><path d="M162.2 81.77v-6.61h-16.23v-3.79h27.55c.99 0 1.79-.73 1.79-1.63V1.64c0-.91-.8-1.64-1.79-1.64H80.14c-.99 0-1.79.73-1.79 1.64v68.09c0 .91.8 1.63 1.79 1.63h27.88v3.79H91.8v6.61L78 90.75V98h98v-7.25l-13.8-8.98zM84.9 64.93V5.92c0-.78.69-1.41 1.53-1.41h80.43c.85 0 1.54.63 1.54 1.41v59.01c0 .78-.7 1.41-1.54 1.41H86.44c-.85 0-1.54-.64-1.54-1.41zm-2.08 25.5 12.44-8.03h63.48l12.42 8.03H82.82z" pointer-events="all"/><path fill="#FFF" stroke="#000" stroke-width="5" pointer-events="all" d="M529 9h120v60H529z"/><path d="M541 18v10m20-10v10m-10-10v10" fill="none" stroke="#000" stroke-width="5" stroke-miterlimit="10" pointer-events="stroke"/><path d="M126.5 298V98M588 290V69" fill="none" stroke="#000" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="9 9" pointer-events="stroke"/><path d="M588 150H136.24" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 150 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M588 230H136.24" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 230 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M8 180h101.76" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m115.76 180-8 4 2-4-2-4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g font-family="Helvetica" text-anchor="middle" font-size="16"><path fill="#FFF" d="M28 171h59v20H28z"/><text x="56" y="186"> search  </text></g><path d="M128 250q60 0 60 20t-51.76 20" fill="none" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="m130.24 290 8-4-2 4 2 4z" stroke="#000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path fill="none" pointer-events="all" d="M194 254h94v30h-94z"/><switch transform="translate(-.5 -.5)"><foreignObject style="overflow:visible;text-align:left" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display:flex;align-items:unsafe flex-start;justify-content:unsafe flex-start;width:92px;height:1px;padding-top:261px;margin-left:196px"><div style="box-sizing:border-box;font-size:0;text-align:left" data-drawio-colors="color: rgb(0 0 0);"><div style="display:inline-block;font-size:16px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">update page</div></div></div></foreignObject><text x="196" y="277" font-family="Helvetica" font-size="16">update page</text></switch><switch><a transform="translate(0 -5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>
