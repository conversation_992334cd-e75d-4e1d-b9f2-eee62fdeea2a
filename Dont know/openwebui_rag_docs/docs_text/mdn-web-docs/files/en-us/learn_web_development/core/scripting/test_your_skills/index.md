Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > index.md --- title: "Test your skills: JavaScript" short-title: Test your skills slug: Learn_web_development/Core/Scripting/Test_your_skills page-type: learn-module-assessment sidebar: learnsidebar --- This page lists JavaScript tests you can try so you can verify if you've understood the content in this module. ## Test your JavaScript skills by topic {{SubpagesWithSummaries}} ## See also - [Dynamic scripting with JavaScript](/en-US/docs/Learn_web_development/Core/Scripting)