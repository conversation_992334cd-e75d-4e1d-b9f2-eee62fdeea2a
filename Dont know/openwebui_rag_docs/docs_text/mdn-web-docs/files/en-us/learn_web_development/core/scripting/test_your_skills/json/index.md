Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > json > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > json > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > json > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > json > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > json > index.md --- title: "Test your skills: JSON" short-title: JSON slug: Learn_web_development/Core/Scripting/Test_your_skills/JSON page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [Working with JSON](/en-US/docs/Learn_web_development/Core/Scripting/JSON) article. > [!NOTE] > You can try solutions by downloading the code and putting it in an online editor such as [<PERSON><PERSON><PERSON>](https://codepen.io/), [J<PERSON>iddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > If there is an error, it will be logged in the results panel on the page or into the browser's JavaScript console to help you. > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## JSON 1 The one and only task in this article concerns accessing JSON data and using it in your page. JSON data about some mother cats and their kittens is available in [sample.json](https://github.com/mdn/learning-area/blob/main/javascript/oojs/tasks/json/sample.json). The JSON is loaded into the page as a text string and made available in the `catString` parameter of the `displayCatInfo()` function. Your task is to fill in the missing parts of the `displayCatInfo()` function to store: - The names of the three mother cats, separated by commas, in the `motherInfo` variable. - The total number of kittens, and how many are male and female, in the `kittenInfo` variable. The values of these variables are then printed to the screen inside paragraphs. Some hints/questions: - The JSON data is provided as text inside the `displayCatInfo()` function. You'll need to parse it into JSON before you can get any data out of it. - You'll probably want to use an outer loop to loop through the cats and add their names to the `motherInfo` variable string, and an inner loop to loop through all the kittens, add up the total of all/male/female kittens, and add those details to the `kittenInfo` variable string. - The last mother cat name should have an "and" before it, and a full stop after it. How do you make sure this can work, no matter how many cats are in the JSON? - Why are the `para1.textContent = motherInfo;` and `para2.textContent = kittenInfo;` lines inside the `displayCatInfo()` function, and not at the end of the script? This has something to do with async code. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/javascript/oojs/tasks/json/json1.html", '100%', 400)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/javascript/oojs/tasks/json/json1-download.html) to work in your own editor or in an online editor.