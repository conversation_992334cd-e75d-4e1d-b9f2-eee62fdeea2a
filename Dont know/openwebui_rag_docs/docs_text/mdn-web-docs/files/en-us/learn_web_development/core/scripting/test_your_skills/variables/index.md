Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > variables > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > variables > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > variables > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > variables > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > scripting > test_your_skills > variables > index.md --- title: "Test your skills: Variables" short-title: Variables slug: Learn_web_development/Core/Scripting/Test_your_skills/Variables page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [Storing the information you need Variables](/en-US/docs/Learn_web_development/Core/Scripting/Variables) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON><PERSON><PERSON>](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > If there is an error in your code, it will be logged into the results panel on this page or in the JavaScript console. > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Variables 1 In this task we want you to: - Declare a variable called `myName`. - Initialize `myName` with a suitable value, on a separate line (you can use your actual name, or something else). - Declare a variable called `myAge` and initialize it with a value, on the same line. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/javascript/introduction-to-js-1/tasks/variables/variables1.html", '100%', 400)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/javascript/introduction-to-js-1/tasks/variables/variables1-download.html) to work in your own editor or in an online editor. ## Variables 2 In this task you need to add a new line to correct the value stored in the existing `myName` variable to your own name. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/javascript/introduction-to-js-1/tasks/variables/variables2.html", '100%', 400)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/javascript/introduction-to-js-1/tasks/variables/variables2-download.html) to work in your own editor or in an online editor. ## Variables 3 The final task for now in this case you are provided with some existing code, which has two errors present in it. The results panel should be outputting the name `Chris`, and a statement about how old Chris will be in 20 years' time. How can you fix the problem and correct the output? Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/javascript/introduction-to-js-1/tasks/variables/variables3.html", '100%', 400)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/javascript/introduction-to-js-1/tasks/variables/variables3-download.html) to work in your own editor or in an online editor.