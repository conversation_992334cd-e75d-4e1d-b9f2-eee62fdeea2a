Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > marking_up_a_letter > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > marking_up_a_letter > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > marking_up_a_letter > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > marking_up_a_letter > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > marking_up_a_letter > index.md --- title: "Challenge: Marking up a letter" short-title: "Challenge: Letter markup" slug: Learn_web_development/Core/Structuring_content/Marking_up_a_letter page-type: learn-module-assessment sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Core/Structuring_content/Creating_links", "Learn_web_development/Core/Structuring_content/Structuring_a_page_of_content", "Learn_web_development/Core/Structuring_content")}} We all learn to write a letter sooner or later; it is also a useful example to test our text formatting skills. In this challenge, you'll have a letter to mark up as a test for your HTML text formatting skills, as well as hyperlinks and proper use of the HTML `<head>` element. ## Starting point To begin, get the [raw text you need to mark up](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/marking-up-a-letter-start/letter-text.txt), and the [CSS to style the HTML](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/marking-up-a-letter-start/css.txt). Create a new `.html` file using your text editor or use an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > [!NOTE] > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Project brief For this project, your task is to mark up a letter that needs to be hosted on a university intranet. The letter is a response from a research fellow to a prospective PhD student concerning their application to the university. ### Block/structural semantics - Use appropriate document structure including doctype, and {{htmlelement("html")}}, {{htmlelement("head")}} and {{htmlelement("body")}} elements. - In general, the letter should be marked up as an organization of headings and paragraphs, with the following exception. There is one top level heading (the "Re:" line) and three second level headings. - Use an appropriate list type to mark up the semester start dates, study subjects, and exotic dances. - Put the two addresses inside {{htmlelement("address")}} elements. Each line of the address should sit on a new line, but not be in a new paragraph. ### Inline semantics - The names of the sender and receiver (and _Tel_ and _Email_) should be marked up with strong importance. - The four dates in the document should have appropriate elements containing machine-readable dates. - The first address and first date in the letter should have a class attribute value of _sender-column_. The CSS you'll add later will cause these to be right aligned, as it should be in the case in a classic letter layout. - Mark up the following five acronyms/abbreviations in the main text of the letter "PhD," "HTML," "CSS," "BC," and "Esq." to provide expansions of each one. - The six sub/superscripts should be marked up appropriately in the chemical formulae, and the numbers 103 and 104 (they should be 10 to the power of 3 and 4, respectively). - Try to mark up at least two appropriate words in the text with strong importance/emphasis. - There are two places where the letter should have a hyperlink. Add appropriate links with titles. For the location that the links point to, you may use `http://example.com` as the URL. - Mark up the university motto quote and citation with appropriate elements. ### The head of the document - The character set of the document should be set as utf-8 using the appropriate meta tag. - The author of the letter should be specified in an appropriate meta tag. - The provided CSS should be included inside an appropriate tag. ## Hints and tips - Use the [W3C HTML validator](https://validator.w3.org/) to validate your HTML. Award yourself bonus points if it validates. - You don't need to know any CSS to do this assignment. You just need to put the provided CSS inside an HTML element. ## Example The following screenshot shows an example of what the letter might look like after being marked up. ![Example](letter-update.png) {{PreviousMenuNext("Learn_web_development/Core/Structuring_content/Creating_links", "Learn_web_development/Core/Structuring_content/Structuring_a_page_of_content", "Learn_web_development/Core/Structuring_content")}}