Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > advanced_html_text > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > advanced_html_text > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > advanced_html_text > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > advanced_html_text > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > advanced_html_text > index.md --- title: "Test your skills: Advanced HTML text" short-title: Advanced HTML text slug: Learn_web_development/Core/Structuring_content/Test_your_skills/Advanced_HTML_text page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you understand how to use [lesser-known HTML elements to mark up advanced semantic features](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features). > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Task 1 In this task, we want you to add some semantics to the provided HTML as follows: - Turn the second paragraph into a block-level quote, and semantically indicate that the quote is taken from [Accessibility](/en-US/docs/Learn_web_development/Core/Accessibility). - Semantically mark up "HTML" and "CSS" as acronyms, providing expansions as tooltips. - Use subscript and superscript to provide correct semantics for the chemical formulae and dates, and make them display correctly. - Semantically associate machine-readable dates with the dates in the text. The finished example should look like this: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/advanced-text/advanced-text2-finished.html", '100%', 300)}} Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/advanced-text/advanced-text2.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/tasks/advanced-text/advanced-text2-download.html) to work in your own editor or in an online editor.