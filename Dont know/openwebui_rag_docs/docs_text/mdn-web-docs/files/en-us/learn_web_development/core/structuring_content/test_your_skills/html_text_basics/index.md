Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > html_text_basics > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > html_text_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > html_text_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > html_text_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > html_text_basics > index.md --- title: "Test your skills: HTML text basics" short-title: HTML text basics slug: Learn_web_development/Core/Structuring_content/Test_your_skills/HTML_text_basics page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you understand how to mark up text in HTML to give it structure and meaning. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Task 1 In this task, we want you to mark up the provided HTML using semantic heading and paragraph elements. The finished example should look like this: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text1-finished.html", '100%', 300)}} Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/tasks/basic-text/basic-text1-download.html) to work in your own editor or in an online editor. ## Task 2 In this task, we want you to turn the first un-marked up list into an unordered list, and the second one into an ordered list. The finished example should look like this: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text2-finished.html", '100%', 400)}} Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text2.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/tasks/basic-text/basic-text2-download.html) to work in your own editor or in an online editor. ## Task 3 In this task, we want you to turn the provided animals and their definitions into a description list. The finished example should look like this: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/advanced-text/advanced-text1-finished.html", '100%', 250)}} Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/advanced-text/advanced-text1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/tasks/advanced-text/advanced-text1-download.html) to work in your own editor or in an online editor. ## Task 4 In this task, you are provided with a paragraph, and your aim is to use some inline elements to mark up a couple of appropriate words with strong importance, and a couple with emphasis. The finished example should look like this: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text3-finished.html", '100%', 120)}} Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/introduction-to-html/tasks/basic-text/basic-text3.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/introduction-to-html/tasks/basic-text/basic-text3-download.html) to work in your own editor or in an online editor.