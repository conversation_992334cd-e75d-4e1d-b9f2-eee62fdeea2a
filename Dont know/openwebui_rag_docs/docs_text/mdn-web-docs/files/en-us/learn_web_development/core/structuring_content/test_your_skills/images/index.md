Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > images > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > images > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > images > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > images > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > structuring_content > test_your_skills > images > index.md --- title: "Test your skills: HTML images" short-title: Images slug: Learn_web_development/Core/Structuring_content/Test_your_skills/Images page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you understand [images and how to embed them in HTML](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_images). > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Task 1 In this task, we want you to embed an image of some Blueberries into the page. You need to: - Add the path to the image to an appropriate attribute to embed it on the page. The image is called `blueberries.jpg`, and it is in a folder inside the current folder called `images`. - Add some alternative text to an appropriate attribute to describe the image, for people that cannot see it. - Give the `<img>` element an appropriate `width` and `height` so that it displays at the correct {{glossary("aspect ratio")}}, and enough space is left on the page to display it. The image's {{glossary("intrinsic size")}} is 615 x 419 pixels. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/multimedia-and-embedding/tasks/images/images1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/multimedia-and-embedding/tasks/images/images1-download.html) to work in your own editor or in an online editor. ## Task 2 In this task, you already have a full-featured image, but we'd like you to add a tooltip that appears when the image is moused over. You should put some appropriate information into the tooltip. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/multimedia-and-embedding/tasks/images/images2.html", '100%', 1000)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/multimedia-and-embedding/tasks/images/images2-download.html) to work in your own editor or in an online editor. ## Task 3 In this task, you are provided with both a full-featured image and some caption text. What you need to do here is add elements that will associate the image with the caption. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/multimedia-and-embedding/tasks/images/images3.html", '100%', 1000)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/multimedia-and-embedding/tasks/images/images3-download.html) to work in your own editor or in an online editor.