Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > overflow > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > overflow > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > overflow > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > overflow > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > overflow > index.md --- title: Overflowing content short-title: Overflow slug: Learn_web_development/Core/Styling_basics/Overflow page-type: learn-module-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Backgrounds_and_borders", "Learn_web_development/Core/Styling_basics/Images_media_forms", "Learn_web_development/Core/Styling_basics")}} Overflow is what happens when there is too much content to fit inside an element box. In this lesson, you will learn how to manage overflow using CSS. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> HTML basics (study <a href="/en-US/docs/Learn_web_development/Core/Structuring_content/Basic_HTML_syntax" >Basic HTML syntax</a >), CSS <a href="/en-US/docs/Learn_web_development/Core/Styling_basics/Values_and_units">Values and units</a> and <a href="/en-US/docs/Learn_web_development/Core/Styling_basics/Sizing">Sizing</a>. </td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>Understand what overflow is.</li> <li>Control overflow with the <code>overflow</code> property. </li> </ul> </td> </tr> </tbody> </table> ## What is overflow? Everything in CSS is a box. You can constrain the size of these boxes by assigning values such as {{cssxref("width")}} and {{cssxref("height")}}. **Overflow happens when there is too much content to fit in a box.** CSS provides various tools to manage overflow. As you go further with CSS layout and writing CSS, you will encounter more overflow situations. ## CSS tries to avoid "data loss" Let's consider two examples that demonstrate the default behavior of CSS when there is overflow. The first example is a box that has been restricted by setting a `height`. Then we add content that exceeds the allocated space. The content overflows the box and falls into the paragraph below. ```html live-sample___block-overflow <div class="box"> This box has a height and a width. This means that if there is too much content to be displayed within the assigned height, there will be an overflow situation. If overflow is set to hidden then any overflow will not be visible. </div> <p>This content is outside of the box.</p> ``` ```css live-sample___block-overflow .box { border: 1px solid #333333; width: 250px; height: 100px; } ``` {{EmbedLiveSample("block-overflow", "", "200px")}} The second example is a word in a box. The box has been made too small for the word and so it breaks out of the box. ```html live-sample___inline-overflow <div class="word">Overflow</div> ``` ```css live-sample___inline-overflow .word { border: 1px solid #333333; width: 100px; font-size: 250%; } ``` {{EmbedLiveSample("inline-overflow")}} You might wonder why CSS works in such a messy way, displaying content outside of its intended container. Why not hide overflowing content? Why not scale the size of the container to fit all the content? Wherever possible, CSS does not hide content. This would cause data loss. The problem with data loss is that you might not notice. Website visitors may not notice. If the submit button on a form disappears and no one can complete the form, this could be a big problem! Instead, CSS overflows in visible ways. You are more likely to see there is a problem. At worst, a site visitor will let you know that content is overlapping. If you restrict a box with a `width` or a `height`, CSS trusts you to know what you are doing. CSS assumes that you are managing the potential for overflow. In general, restricting the block dimension is problematic when the box contains text. There may be more text than you expected when designing the site, or the text may be larger (for example, if the user has increased their font size). ## The overflow property The {{cssxref("overflow")}} property helps you manage an element's content overflow. Using this property, you can convey to a browser how it should handle overflow content. The default value of the [`<overflow>`](/en-US/docs/Web/CSS/overflow_value) value type is `visible`. With this default setting, one can see content when it overflows. ### Hiding overflowing content To hide content when it overflows, you can set `overflow: hidden`. This does exactly what it says: it hides overflow. Beware that this can make some content invisible. You should only do this if hiding content won't cause problems. ```html live-sample___hidden <div class="box"> This box has a height and a width. This means that if there is too much content to be displayed within the assigned height, there will be an overflow situation. If overflow is set to hidden then any overflow will not be visible. </div> <p>This content is outside of the box.</p> ``` ```css live-sample___hidden .box { border: 1px solid #333333; width: 250px; height: 100px; overflow: hidden; } ``` {{EmbedLiveSample("hidden", "", "200px")}} ### Scrolling overflowing content Instead, perhaps you would like to add scrollbars when content overflows? Using `overflow: scroll`, browsers with visible scrollbars will always display them even if there is not enough content to overflow. This offers the advantage of keeping the layout consistent, instead of scrollbars appearing or disappearing, depending upon the amount of content in the container. Remove some content from the box below. Notice how the scrollbars remain, even if there is no need for scrolling: > [!NOTE] > Scrollbar visibility depends on the operating system. > You may have to change your browser settings to always show scroll bars in order for the scroll bars to always show in the following examples. ```html live-sample___scroll <div class="box"> This box has a height and a width. This means that if there is too much content to be displayed within the assigned height, there will be an overflow situation. If overflow is set to hidden then any overflow will not be visible. </div> <p>This content is outside of the box.</p> ``` ```css live-sample___scroll .box { border: 1px solid #333333; width: 250px; height: 100px; overflow: scroll; } ``` {{EmbedLiveSample("scroll", "", "200px")}} In the example above, we only need to scroll on the `y` axis, however we get scrollbars in both axes. To just scroll on the `y` axis, you could use the {{cssxref("overflow-y")}} property, setting `overflow-y: scroll`. ```html live-sample___scroll-y <div class="box"> This box has a height and a width. This means that if there is too much content to be displayed within the assigned height, there will be an overflow situation. If overflow is set to hidden then any overflow will not be visible. </div> <p>This content is outside of the box.</p> ``` ```css live-sample___scroll-y .box { border: 1px solid #333333; width: 250px; height: 100px; overflow-y: scroll; } ``` {{EmbedLiveSample("scroll-y", "", "200px")}} You can also enable scrolling along the x-axis by using {{cssxref("overflow-x")}}, although this is not a recommended way to accommodate long words! If you have a long word in a small box, consider using the {{cssxref("word-break")}} or {{cssxref("overflow-wrap")}} property. In addition, some of the methods discussed in [Sizing items in CSS](/en-US/docs/Learn_web_development/Core/Styling_basics/Sizing) may help you create boxes that scale better with varying amounts of content. ```html live-sample___scroll-x <div class="word">Overflow</div> ``` ```css live-sample___scroll-x .word { border: 5px solid #333333; width: 100px; font-size: 250%; overflow-x: scroll; } ``` {{EmbedLiveSample("scroll-x")}} As with `scroll`, you get a scrollbar in the scrolling dimension whether or not there is enough content to cause a scrollbar. > [!NOTE] > You can specify x- and y-axis scrolling using the `overflow` property, passing two values. If two keywords are specified, the first applies to `overflow-x` and the second applies to `overflow-y`. Otherwise, both `overflow-x` and `overflow-y` are set to the same value. For example, `overflow: scroll hidden` would set `overflow-x` to `scroll` and `overflow-y` to `hidden`. ### Only displaying scrollbars when needed If you only want scrollbars to appear when there is more content than can fit in the box, use `overflow: auto`. This allows the browser to determine if it should display scrollbars. In the example below, remove content until it fits into the box. You should see the scrollbars disappear: ```html live-sample___auto <div class="box"> This box has a height and a width. This means that if there is too much content to be displayed within the assigned height, there will be an overflow situation. If overflow is set to hidden then any overflow will not be visible. </div> <p>This content is outside of the box.</p> ``` ```css live-sample___auto .box { border: 1px solid #333333; width: 250px; height: 100px; overflow: auto; } ``` {{EmbedLiveSample("auto", "", "200px")}} ## Unwanted overflow in web design Modern layout methods (which you'll meet later in the [CSS layout](/en-US/docs/Learn_web_development/Core/CSS_layout) module) manage overflow. They largely work without assumptions or dependencies for how much content there will be on a web page. This was not always the norm. In the past, some sites were built with fixed-height containers to align box bottoms. These boxes may otherwise have had no relationship to each other. This was fragile. If you encounter a box where content is overlaying other content on the page in legacy applications, you will now recognize that this happens with overflow. Ideally, you will refactor the layout to not rely on fixed-height containers. When developing a site, always keep overflow in mind. Test designs with large and small amounts of content. Increase and decrease font sizes by at least two increments. Ensure your CSS is robust. Changing overflow values to hide content or to add scrollbars is reserved for a few select use cases (for example, where you intend to have a scrolling box). ## Test your skills! You've reached the end of this article, but can you remember the most important information? You can find some further tests to verify that you've retained this information before you move on see [Test your skills: Overflow](/en-US/docs/Learn_web_development/Core/Styling_basics/Test_your_skills/Overflow). ## Summary This lesson introduced the concept of overflow. You should understand that default CSS avoids making overflowing content invisible. You have discovered that you can manage potential overflow, and also, that you should test work to make sure it does not accidentally cause problematic overflow. In the next article, we'll take a look at how to handle styling of special page features such as images and form elements. {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Backgrounds_and_borders", "Learn_web_development/Core/Styling_basics/Images_media_forms", "Learn_web_development/Core/Styling_basics")}}