Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > pseudo_classes_and_elements > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > pseudo_classes_and_elements > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > pseudo_classes_and_elements > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > pseudo_classes_and_elements > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > pseudo_classes_and_elements > index.md --- title: Pseudo-classes and pseudo-elements short-title: Pseudo-classes and elements slug: Learn_web_development/Core/Styling_basics/Pseudo_classes_and_elements page-type: learn-module-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Attribute_selectors", "Learn_web_development/Core/Styling_basics/Combinators", "Learn_web_development/Core/Styling_basics")}} The next set of selectors we will look at are referred to as **pseudo-classes** and **pseudo-elements**. There are a large number of these, and they often serve quite specific purposes. Once you know how to use them, you can look through the different types to see if there is something which works for the task you are trying to achieve. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> HTML basics (study <a href="/en-US/docs/Learn_web_development/Core/Structuring_content/Basic_HTML_syntax" >Basic HTML syntax</a >), <a href="/en-US/docs/Learn_web_development/Core/Styling_basics/Basic_selectors">Basic CSS selectors</a>. </td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>Pseudo-classes and pseudo-elements.</li> <li>The difference between the two.</li> <li>Combining pseudo-classes and pseudo-elements.</li> <li>Generated content.</li> </ul> </td> </tr> </tbody> </table> ## What is a pseudo-class? A pseudo-class is a selector that selects elements that are in a specific state, e.g., they are the first element of their type, or they are being hovered over by the mouse pointer. They tend to act as if you had applied a class to some part of your document, often helping you cut down on excess classes in your markup, and giving you more flexible, maintainable code. Pseudo-classes are keywords that start with a colon. For example, `:hover` is a pseudo-class. ### Basic pseudo-class example Let's look at a basic example. If we wanted to make the first paragraph in an article larger and bold, we could add a class to that paragraph and then add CSS to that class, as shown in the first example below: ```html live-sample___first-child <article> <p class="first"> Veggies es bonus vobis, proinde vos postulo essum magis kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <p> Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> </article> ``` ```css live-sample___first-child .first { font-size: 120%; font-weight: bold; } ``` {{EmbedLiveSample("first-child")}} However, this could be annoying to maintain what if a new paragraph got added to the top of the document? We'd need to move the class over to the new paragraph. Instead of adding the class, we could use the {{cssxref(":first-child")}} pseudo-class selector this will _always_ target the first child element in the article, and we will no longer need to edit the HTML (this may not always be possible anyway, maybe due to it being generated by a CMS). ```html live-sample___first-child2 <article> <p> Veggies es bonus vobis, proinde vos postulo essum magis kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <p> Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> </article> ``` ```css live-sample___first-child2 article p:first-child { font-size: 120%; font-weight: bold; } ``` {{EmbedLiveSample("first-child2")}} All pseudo-classes behave in this same kind of way. They target some bit of your document that is in a certain state, behaving as if you had added a class into your HTML. Take a look at some other examples on MDN: - [`:last-child`](/en-US/docs/Web/CSS/:last-child) - [`:only-child`](/en-US/docs/Web/CSS/:only-child) - [`:invalid`](/en-US/docs/Web/CSS/:invalid) > [!NOTE] > It is valid to write pseudo-classes and elements without any element selector preceding them. In the example above, you could write `:first-child` and the rule would apply to _any_ element that is the first child of an `<article>` element, not just a paragraph first child `:first-child` is equivalent to `*:first-child`. However, usually you want more control than that, so you need to be more specific. ### User-action pseudo classes Some pseudo-classes only apply when the user interacts with the document in some way. These **user-action** pseudo-classes, sometimes referred to as **dynamic pseudo-classes**, act as if a class had been added to the element when the user interacts with it. Examples include: - [`:hover`](/en-US/docs/Web/CSS/:hover) mentioned above; this only applies if the user moves their pointer over an element, typically a link. - [`:focus`](/en-US/docs/Web/CSS/:focus) only applies if the user focuses the element by clicking or using keyboard controls. ```html live-sample___hover <p><a href="">Hover over me</a></p> ``` ```css live-sample___hover a:link, a:visited { color: rebeccapurple; font-weight: bold; } a:hover { color: hotpink; } ``` {{EmbedLiveSample("hover")}} ## What is a pseudo-element? Pseudo-elements behave in a similar way. However, they act as if you had added a whole new HTML element into the markup, rather than applying a class to existing elements. Pseudo-elements start with a double colon `::`. `::before` is an example of a pseudo-element. > [!NOTE] > Some early pseudo-elements used the single colon syntax, so you may sometimes see this in code or examples. Modern browsers support the early pseudo-elements with single- or double-colon syntax for backwards compatibility. For example, if you wanted to select the first line of a paragraph you could wrap it in a `<span>` element and use an element selector; however, that would fail if the number of words you had wrapped were longer or shorter than the parent element's width. As we tend not to know how many words will fit on a line as that will change if the screen width or font-size changes it is impossible to robustly do this by adding HTML. The `::first-line` pseudo-element selector will do this for you reliably if the number of words increases or decreases it will still only select the first line. ```html live-sample___first-line <article> <p> Veggies es bonus vobis, proinde vos postulo essum magis kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <p> Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> </article> ``` ```css live-sample___first-line article p::first-line { font-size: 120%; font-weight: bold; } ``` {{EmbedLiveSample("first-line")}} It acts as if a `<span>` was magically wrapped around that first formatted line, and updated each time the line length changed. You can see that this selects the first line of both paragraphs. ## Combining pseudo-classes and pseudo-elements If you wanted to make the first line of the first paragraph bold you could chain the `:first-child` and `::first-line` selectors together. Try editing the previous live example so it uses the following CSS. We are saying that we want to select the first line, of the first `<p>` element, which is inside an `<article>` element. ```css article p:first-child::first-line { font-size: 120%; font-weight: bold; } ``` ## Generating content with ::before and ::after There are a couple of special pseudo-elements, which are used along with the [`content`](/en-US/docs/Web/CSS/content) property to insert content into your document using CSS. You could use these to insert a string of text, such as in the live example below. Try changing the text value of the {{cssxref("content")}} property and see it change in the output. You could also change the `::before` pseudo-element to `::after` and see the text inserted at the end of the element instead of the beginning. ```html live-sample___before <p class="box">Content in the box in my HTML page.</p> ``` ```css live-sample___before .box::before { content: "This should show before the other content. "; } ``` {{EmbedLiveSample("before")}} Inserting strings of text from CSS isn't really something we do very often on the web however, as that text is inaccessible to some screen readers and might be hard for someone to find and edit in the future. A more valid use of these pseudo-elements is to insert an icon, for example the little arrow added in the example below, which is a visual indicator that we wouldn't want read out by a screen reader: ```html live-sample___after-icon <p class="box">Content in the box in my HTML page.</p> ``` ```css live-sample___after-icon .box::after { content: " "; } ``` {{EmbedLiveSample("after-icon")}} These pseudo-elements are also frequently used to insert an empty string, which can then be styled just like any element on the page. In this next example, we have added an empty string using the `::before` pseudo-element. We have set this to `display: block` in order that we can style it with a width and height. We then use CSS to style it just like any element. You can play around with the CSS and change how it looks and behaves. ```html live-sample___before-styled <p class="box">Content in the box in my HTML page.</p> ``` ```css live-sample___before-styled .box::before { content: ""; display: block; width: 100px; height: 100px; background-color: rebeccapurple; border: 1px solid black; } ``` {{EmbedLiveSample("before-styled", "", "160")}} The use of the `::before` and `::after` pseudo-elements along with the `content` property is referred to as "Generated Content" in CSS, and you will often see this technique being used for various tasks. A great example is the site [CSS Arrow Please](https://cssarrowplease.com/), which helps you to generate an arrow with CSS. Look at the CSS as you create your arrow and you will see the {{cssxref("::before")}} and {{cssxref("::after")}} pseudo-elements in use. Whenever you see these selectors, look at the {{cssxref("content")}} property to see what is being added to the HTML element. ## Summary In this article we've introduced CSS pseudo-classes and pseudo-elements, which are special types of selectors. Pseudo-classes enable you to target an element when it's in a particular state, as if you had added a class for that state to the DOM. Pseudo-elements act as if you had added a whole new element to the DOM, and enable you to style that. The `::before` and `::after` pseudo-elements enable you to insert content into the document using CSS. In the next article, we'll learn about combinators. ## See also - [Pseudo-classes reference](/en-US/docs/Web/CSS/Pseudo-classes) - [Pseudo-elements reference](/en-US/docs/Web/CSS/Pseudo-elements) {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Attribute_selectors", "Learn_web_development/Core/Styling_basics/Combinators", "Learn_web_development/Core/Styling_basics")}}