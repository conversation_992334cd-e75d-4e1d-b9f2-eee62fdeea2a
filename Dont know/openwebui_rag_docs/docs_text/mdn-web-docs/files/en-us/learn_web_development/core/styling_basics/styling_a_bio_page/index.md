Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > styling_a_bio_page > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > styling_a_bio_page > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > styling_a_bio_page > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > styling_a_bio_page > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > styling_a_bio_page > index.md --- title: "Challenge: Styling a biography page" short-title: "Challenge: Biography page" slug: Learn_web_development/Core/Styling_basics/Styling_a_bio_page page-type: learn-module-assessment sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Getting_started", "Learn_web_development/Core/Styling_basics/Basic_selectors", "Learn_web_development/Core/Styling_basics")}} In this challenge you will style a simple bio page, testing you on some of the skills you learned in the last couple of lessons including writing selectors and text styling. > [!NOTE] > You can click "Play" in the live samples below to open the code in the MDN Playground, or you can copy and paste the code into your own IDE or an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Project brief The following live sample shows a biography, which has been styled using CSS. The CSS properties that are used are as follows each one links to its property page on MDN, which will give you more examples of its use. - {{cssxref("font-family")}} - {{cssxref("color")}} - {{cssxref("border-bottom")}} - {{cssxref("font-weight")}} - {{cssxref("font-size")}} - {{cssxref("font-style")}} - {{cssxref("text-decoration")}} In the example, there is some CSS already in place which selects parts of the document using element selectors, classes, and pseudo-classes. Make the following changes to this CSS: 1. Make the level one heading pink, using the CSS color keyword `hotpink`. 2. Give the heading a 10px dotted {{cssxref("border-bottom")}} which uses the CSS color keyword `purple`. 3. Make the level 2 heading italic. 4. Give the `ul` used for the contact details a {{cssxref("background-color")}} of `#eeeeee`, and a 5px solid purple {{cssxref("border")}}. Use some {{cssxref("padding")}} to push the content away from the border. 5. Make the links `green` on hover. ## Hints and tips - Use the [W3C CSS Validator](https://jigsaw.w3.org/css-validator/) to catch unintended mistakes in your CSS mistakes you might have otherwise missed so that you can fix them. - Afterwards try looking up some properties not mentioned on this page in the [MDN CSS reference](/en-US/docs/Web/CSS/Reference) and get adventurous! - Remember that there is no wrong answer here at this stage in your learning you can afford to have a bit of fun. ## Example You should end up with something like this image. ![Screenshot of how the example should look after completing the assessment.](learn-css-basics-assessment.png) Here are HTML and CSS code blocks and the result of combining them: ```html live-sample___biog <h1>Jane Doe</h1> <div class="job-title">Web Developer</div> <p> Far far away, behind the word mountains, far from the countries Vokalia and Consonantia, there live the blind texts. Separated they live in Bookmarksgrove right at the coast of the Semantics, a large language ocean. </p> <p> A small river named Duden flows by their place and supplies it with the necessary regelialia. It is a paradisematic country, in which roasted parts of sentences fly into your mouth. </p> <h2>Contact information</h2> <ul> <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li> <li>Web: <a href="http://example.com">http://example.com</a></li> <li>Tel: 123 45678</li> </ul> ``` ```css live-sample___biog body { font-family: Arial, Helvetica, sans-serif; } h1 { color: #375e97; font-size: 2em; font-family: Georgia, "Times New Roman", Times, serif; border-bottom: 1px solid #375e97; } h2 { font-size: 1.5em; } .job-title { color: #999999; font-weight: bold; } a:link, a:visited { color: #fb6542; } a:hover { text-decoration: none; } ``` {{EmbedLiveSample("biog", "", "400px")}} {{PreviousMenuNext("Learn_web_development/Core/Styling_basics/Getting_started", "Learn_web_development/Core/Styling_basics/Basic_selectors", "Learn_web_development/Core/Styling_basics")}}