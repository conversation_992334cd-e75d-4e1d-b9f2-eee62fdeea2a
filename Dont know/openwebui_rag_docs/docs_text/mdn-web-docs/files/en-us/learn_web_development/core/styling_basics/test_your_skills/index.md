Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > index.md --- title: "Test your skills: CSS styling basics" short-title: Test your skills slug: Learn_web_development/Core/Styling_basics/Test_your_skills page-type: learn-module-assessment sidebar: learnsidebar --- This page lists CSS styling basics tests you can try so you can verify if you've understood the content in this module. ## Test your CSS styling basics skills by topic {{SubpagesWithSummaries}} ## See also - [CSS styling basics](/en-US/docs/Learn_web_development/Core/Styling_basics)