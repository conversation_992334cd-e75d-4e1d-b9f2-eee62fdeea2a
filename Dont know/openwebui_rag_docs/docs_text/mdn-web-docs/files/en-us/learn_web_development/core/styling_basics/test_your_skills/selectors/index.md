Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > selectors > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > selectors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > selectors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > selectors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > styling_basics > test_your_skills > selectors > index.md --- title: "Test your skills: Selectors" short-title: Selectors slug: Learn_web_development/Core/Styling_basics/Test_your_skills/Selectors page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you understand [CSS selectors](/en-US/docs/Learn_web_development/Core/Styling_basics/Basic_selectors). > [!NOTE] > Click **"Play"** in the code blocks below to edit the examples in the MDN Playground. > You can also copy the code (click the clipboard icon) and paste it into an online editor such as [CodePen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Task 1 In this task, use CSS to do the following things, without changing the HTML: - Make `<h1>` headings blue. - Give `<h2>` headings a blue background and white text. - Cause text wrapped in a `<span>` to have a font-size of 200%. Your final result should look like the image below: ![Text with the CSS applied for the solution to task 1.](selectors1.jpg) Try to update the code below to recreate the finished example: ```html live-sample___type <div class="container"> <h1>This is a heading</h1> <p> Veggies es <span>bonus vobis</span>, proinde vos postulo essum magis kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <h2>A level 2 heading</h2> <p> Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> </div> ``` ```css live-sample___type body { font: 1.2em / 1.5 sans-serif; } /* Add styles here */ ``` {{EmbedLiveSample("type", "", "260px")}} <details> <summary>Click here to show the solution</summary> You need to target the `h1`, `h2` and `span` selectors to change their color or size. ```css h1 { color: blue; } h2 { background-color: blue; color: white; } span { font-size: 200%; } ``` </details> ## Task 2 In this task, we want you to make the following changes to the look of the content in this example, without changing the HTML: - Give the element with an id of `special` a yellow background. - Give the element with a class of `alert` a 2px grey border. - If the element with a class of `alert` also has a class of `stop`, make the background red. - If the element with a class of `alert` also has a class of `go`, make the background green. Your final result should look like the image below: ![Text with the CSS applied for the solution to task 2.](selectors2.jpg) Try to update the code below to recreate the finished example: ```html live-sample___class-id <div class="container"> <h1>This is a heading</h1> <p> Veggies es <span class="alert">bonus vobis</span>, proinde vos postulo <span class="alert stop">essum magis</span> kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <h2 id="special">A level 2 heading</h2> <p>Gumbo beet greens corn soko endive gumbo gourd.</p> <h2>Another level 2 heading</h2> <p> <span class="alert go">Parsley shallot</span> courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> </div> ``` ```css live-sample___class-id body { font: 1.2em / 1.5 sans-serif; } /* Add styles here */ ``` {{EmbedLiveSample("class-id", "", "320px")}} <details> <summary>Click here to show the solution</summary> This tests that you understand the difference between class and id selectors and also how to target multiple classes on an item. ```css #special { background-color: yellow; } .alert { border: 2px solid grey; } .alert.stop { background-color: red; } .alert.go { background-color: green; } ``` </details> ## Task 3 In this task, we want you to make the following changes without changing the HTML: - Style links, making the link-state orange, visited links green, and remove the underline on hover. - Make the first element inside the container `font-size: 150%` and the first line of that element red. - Stripe every other row in the table by selecting these rows and giving them a background color of `#333` and foreground white. Your final result should look like the image below: ![Text with the CSS applied for the solution to task 3.](selectors3.jpg) Try to update the code below to recreate the finished example: ```html live-sample___pseudo <div class="container"> <p> Veggies es <a href="http://example.com">bonus vobis</a>, proinde vos postulo essum magis kohlrabi welsh onion daikon amaranth tatsoi tomatillo melon azuki bean garlic. </p> <p> Gumbo beet greens corn soko endive gumbo gourd. Parsley shallot courgette tatsoi pea sprouts fava bean collard greens dandelion okra wakame tomato. Dandelion cucumber earthnut pea peanut soko zucchini. </p> <table> <tbody> <tr> <th>Fruits</th> <th>Vegetables</th> </tr> <tr> <td>Apple</td> <td>Potato</td> </tr> <tr> <td>Orange</td> <td>Carrot</td> </tr> <tr> <td>Tomato</td> <td>Parsnip</td> </tr> <tr> <td>Kiwi</td> <td>Onion</td> </tr> <tr> <td>Banana</td> <td>Beet</td> </tr> </tbody> </table> </div> ``` ```css hidden live-sample___pseudo body { font: 1.2em / 1.5 sans-serif; } * { box-sizing: border-box; } table { border-collapse: collapse; width: 300px; } td, th { padding: 0.2em; text-align: left; } ``` ```css live-sample___pseudo /* Add styles here */ ``` {{EmbedLiveSample("pseudo", "", "320px")}} <details> <summary>Click here to show the solution</summary> Apply a pseudo-class (`:first-child`) and pseudo-element (`::first-line`) to the content. Style the `:link`, `:visited`, and `:hover` states of the `a` element, and create striped table rows using the `:nth-child` pseudo-class. ```css .container p:first-child { font-size: 150%; } .container p:first-child::first-line { color: red; } a:link { color: orange; } a:visited { color: green; } a:hover { text-decoration: none; } tr:nth-child(even) { background-color: #333; color: #fff; } ``` </details> ## Task 4 In this task, we want you to do the following: - Make any paragraph that directly follows an `<h2>` element red. - Remove the bullets and add a 1px grey bottom border only to list items that are a direct child of the ul with a class of `list`. Your final result should look like the image below: ![Text with the CSS applied for the solution to task 4.](selectors4.jpg) Try to update the code below to recreate the finished example: ```html live-sample___combinators <div class="container"> <h2>This is a heading</h2> <p>This paragraph comes after the heading.</p> <p>This is the second paragraph.</p> <h2>Another heading</h2> <p>This paragraph comes after the heading.</p> <ul class="list"> <li>One</li> <li> Two <ul> <li>2.1</li> <li>2.2</li> </ul> </li> <li>Three</li> </ul> </div> ``` ```css live-sample___combinators body { font: 1.2em / 1.5 sans-serif; } /* Add styles here */ ``` {{EmbedLiveSample("combinators", "", "350px")}} <details> <summary>Click here to show the solution</summary> This task checks that you understand how to use different combinators. Here's an appropriate solution: ```css h2 + p { color: red; } .list > li { list-style: none; border-bottom: 1px solid #ccc; } ``` </details> ## Task 5 In this task, add CSS using attribute selectors to do the following: - Target the `<a>` element with a `title` attribute and make the border pink (`border-color: pink`). - Target the `<a>` element with an `href` attribute that contains the word `contact` somewhere in its value and make the border orange (`border-color: orange`). - Target the `<a>` element with an `href` value starting with `https` and give it a green border (`border-color: green`). Your final result should look like the image below: ![Four links with different color borders.](selectors-attribute.png) Try to update the code below to recreate the finished example: ```html live-sample___attribute-links <ul> <li><a href="https://example.com">Link 1</a></li> <li><a href="http://example.com" title="Visit example.com">Link 2</a></li> <li><a href="/contact">Link 3</a></li> <li><a href="../contact/index.html">Link 4</a></li> </ul> ``` ```css hidden live-sample___attribute-links body { font: 1.2em / 1.5 sans-serif; } ul { list-style: none; margin: 0; padding: 0; } li { margin: 0 0 0.5em 0; } a { display: block; padding: 0.5em; } ``` ```css live-sample___attribute-links a { border: 5px solid grey; } /* Add styles here */ ``` {{EmbedLiveSample("attribute-links", "", "300px")}} <details> <summary>Click here to show the solution</summary> - To select elements with a title attribute we can add title inside the square brackets (`a[title]`), which will select the second link, which is the only one with a title attribute. - Target the `<a>` element with an `href` attribute which contains the word "contact" anywhere in its value and make the border orange (`border-color: orange`). There are two things we want to match here, the href value `/contact` and also `../contact`. So we need to match the string "contact" anywhere in the value using `*=`. This will select the third and fourth links. - Target the `<a>` element with an href value starting with `https` and give it a green border (`border-color: green`). Look for an `href` value which starts with "https", so use `^=` to only select the first link. ```css a[title] { border-color: pink; } a[href*="contact"] { border-color: orange; } a[href^="https"] { border-color: green; } ``` </details> ## See also - [CSS styling basics](/en-US/docs/Learn_web_development/Core/Styling_basics)