Path: mdn-web-docs > files > en-us > learn_web_development > core > text_styling > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > core > text_styling > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > text_styling > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > text_styling > index.md Path: mdn-web-docs > files > en-us > learn_web_development > core > text_styling > index.md --- title: CSS text styling slug: Learn_web_development/Core/Text_styling page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Core/Text_styling/Fundamentals", "Learn_web_development/Core")}} With the basics of the CSS language covered, the next CSS topic for you to concentrate on is styling text one of the most common things you'll do with CSS. Here we look at fundamentals including setting font, boldness, italics, line and letter spacing, drop shadows, and other text features. We round off the module by looking at applying custom fonts to your page, and styling lists and links. ## Prerequisites Before starting this module, you should be familiar with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and the [basic fundamentals of CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). > [!NOTE] > If you are working on a computer/tablet/other device where you don't have the ability to create your own files, you could try out (most of) the code examples in an online coding program such as [JSBin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials and challenges - [Fundamental text and font styling](/en-US/docs/Learn_web_development/Core/Text_styling/Fundamentals) - : In this article we go through all the basics of text/font styling in detail, including setting font weight, family and style, font shorthand, text alignment and other effects, and line and letter spacing. - [Styling lists](/en-US/docs/Learn_web_development/Core/Text_styling/Styling_lists) - : Lists behave like any other text for the most part, but there are some CSS properties specific to lists that you need to know about, and some best practices to consider. This article explains all. - [Styling links](/en-US/docs/Learn_web_development/Core/Text_styling/Styling_links) - : When styling links, it is important to understand why default link styles are important, how to use pseudo-classes to style link states effectively, and how to style links for use in common varied interface features such as navigation menus and tabs. We'll look at all these topics in this article. - [Web fonts](/en-US/docs/Learn_web_development/Core/Text_styling/Web_fonts) - : Here we will explore web fonts in detail these allow you to download custom fonts along with your web page, to allow for more varied, custom text styling. - [Challenge: Typesetting a community school homepage](/en-US/docs/Learn_web_development/Core/Text_styling/Typesetting_a_homepage) <sup>Challenge</sup> - : In this assessment we'll test your understanding of styling text by getting you to style the text for a community school's homepage. {{NextMenu("Learn_web_development/Core/Text_styling/Fundamentals", "Learn_web_development/Core")}}