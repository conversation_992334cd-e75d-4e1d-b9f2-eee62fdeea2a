Path: mdn-web-docs > files > en-us > learn_web_development > educators > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > educators > index.md Path: mdn-web-docs > files > en-us > learn_web_development > educators > index.md Path: mdn-web-docs > files > en-us > learn_web_development > educators > index.md Path: mdn-web-docs > files > en-us > learn_web_development > educators > index.md --- title: Resources for educators slug: Learn_web_development/Educators page-type: landing-page sidebar: learnsidebar --- Educators can use MDN's "Learn web development" section as a guide when creating programs, units, and assessment specifications for a web-related university degree, college course, coding school course, or similar. Each article includes a "Learning outcomes" section at the top detailing the topics taught in that article. The MDN team is committed to reviewing the material contained within "Learn web development" annually to ensure it remains current with front-end development best practices. By basing their courses on it, educators can ensure their teaching is up-to-date. This page provides guidance and resources to make it easy to adapt MDN's learning content for your teaching. ## Choosing an appropriate scope Our [Core modules](/en-US/docs/Learn_web_development/Core) include fundamental web standards concepts, HTML, CSS, JavaScript, accessibility, design theory, and version control. We believe the contained topics are the minimum that every new web developer should know before starting in the industry. However, you may have additional topic requirements for your course, in which case you could: - Consider including some of the learning objectives from our [Getting started modules](/en-US/docs/Learn_web_development/Getting_started), such as soft skills like teamwork, research, and job interview techniques, and gaining familiarity with a typical web development toolset. - Extend the technology coverage in the Core modules with some [Extensions](/en-US/docs/Learn_web_development/Extensions). We will aim to publish more extensions as time goes on. You could consider writing your own extensions and contributing them to the curriculum so others can benefit. Contact us if you have an extension to submit! ## Using an effective learning pathway When teaching students, it is vital to provide them with a clear progression through the topics they are required to learn, with clear learning objectives and regular assessments to ensure they are on the right path. Our [Core modules](/en-US/docs/Learn_web_development/Core) provide a pathway to follow through the essentials of front-end web development and include "Test your skills" sections and challenge articles. You could take your students through the provided pathway as-is, or you may wish to customize it for your needs by: - Adding in your own skill tests/assessments at appropriate places. We recommend assessing students using a combination of exam-based theory testing, and practical real-world projects to get students to put their knowledge into action. - Grouping the learning outcomes differently. For example, you may wish to start with the basics of HTML document structure, then follow up quickly with some CSS fundamentals such as box model and text styling. In the next step, you might introduce more complex HTML features such as images and form fields, ramp up the CSS with some flexbox and media queries, and introduce some rudimentary DOM scripting. - Providing students with a repository containing their project work that can be published as a portfolio to demonstrate proof of their skills to prospective employers. You could also consider using an advanced turnkey solution such as an interactive video course. We would recommend [The Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j?via=mdn) from our learning partner, [Scrimba](https://scrimba.com/?via=mdn), which teaches all of the learning outcomes in the MDN Curriculum Core and more. > [!NOTE] > Scrimba offers multiple license deals see [Scrimba for Teams](https://scrimba.com/teams?via=mdn-edu). The Frontend Developer Career Path features fun interactive lessons and challenges, knowledgeable teachers, and a supportive community; it aims to give students all they need to land their first junior developer job. Many of the course components are available as standalone free versions. ## Resources [Complete MDN Curriculum download](https://github.com/mdn/curriculum/releases/latest/download/MDN-Curriculum.pdf) (PDF)