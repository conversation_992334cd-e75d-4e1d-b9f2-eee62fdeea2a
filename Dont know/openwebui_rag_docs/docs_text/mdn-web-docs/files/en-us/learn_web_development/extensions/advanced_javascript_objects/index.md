Path: mdn-web-docs > files > en-us > learn_web_development > extensions > advanced_javascript_objects > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > advanced_javascript_objects > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > advanced_javascript_objects > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > advanced_javascript_objects > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > advanced_javascript_objects > index.md --- title: Advanced JavaScript objects slug: Learn_web_development/Extensions/Advanced_JavaScript_objects page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Extensions/Advanced_JavaScript_objects/Object_prototypes", "Learn_web_development/Extensions")}} In JavaScript, most things are objects, from core JavaScript features like arrays to the browser {{Glossary("API", "APIs")}} built on top of JavaScript. You can also create your own objects to encapsulate related functions and variables into efficient packages and act as handy data containers. Having a deeper knowledge of how JavaScript objects work is useful as you build confidence with web development, start to build more complex apps, and create your own libraries. This module will help you by providing JavaScript object theory and object-oriented programming practice. ## Prerequisites Before starting this module, You should really have learnt the fundamentals of [JavaScript](/en-US/docs/Learn_web_development/Core/Scripting), especially [JavaScript object basics](/en-US/docs/Learn_web_development/Core/Scripting/Object_basics). It would also be helpful to have some familiarity with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). > [!NOTE] > If you are working on a computer/tablet/other devices where you are not able to create your own files, you could try out (most of) the code examples in an online coding program such as [JSBin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials and challenges - [Object prototypes](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Object_prototypes) - : Prototypes are the mechanism by which JavaScript objects inherit features from one another, and they work differently from inheritance mechanisms in classical object-oriented programming languages. In this article, we explore how prototype chains work. - [Object-oriented programming](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Object-oriented_programming) - : In this article, we'll describe some of the basic principles of "classical" object-oriented programming, and look at the ways it is different from the prototype model in JavaScript. - [Classes in JavaScript](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Classes_in_JavaScript) - : JavaScript provides some features for people wanting to implement "classical" object-oriented programs, and in this article, we'll describe these features. - [Object building practice](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Object_building_practice) - : In previous articles we looked at all the essential JavaScript object theory and syntax details, giving you a solid base to start from. In this article we dive into a practical exercise, giving you some more practice in building custom JavaScript objects, which produce something fun and colorful some colored bouncing balls. - [Adding features to our bouncing balls demo](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects/Adding_bouncing_balls_features) <sup>Challenge</sup> - : In this challenge, you are expected to use the bouncing balls demo from the previous article as a starting point, and add some new and interesting features to it. ## See also - [Learn JavaScript](https://learnjavascript.online/) - : An excellent resource for aspiring web developers Learn JavaScript in an interactive environment, with short lessons and interactive tests, guided by automated assessment. The first 40 lessons are free, and the complete course is available for a small one-time payment.