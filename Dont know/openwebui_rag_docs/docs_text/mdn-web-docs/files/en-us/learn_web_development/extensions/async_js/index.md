Path: mdn-web-docs > files > en-us > learn_web_development > extensions > async_js > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > async_js > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > async_js > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > async_js > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > async_js > index.md --- title: Asynchronous JavaScript slug: Learn_web_development/Extensions/Async_JS page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Extensions/Async_JS/Introducing", "Learn_web_development/Extensions")}} In this module, we take a look at {{Glossary("asynchronous")}} {{Glossary("JavaScript")}}, why it is important, and how it can be used to effectively handle potential blocking operations, such as fetching resources from a server. ## Prerequisites Asynchronous JavaScript is a fairly advanced topic, and you are advised to work through [Dynamic scripting with JavaScript](/en-US/docs/Learn_web_development/Core/Scripting) modules before attempting this. > [!NOTE] > If you are working on a computer/tablet/other device where you don't have the ability to create your own files, you can try out (most of) the code examples in an online coding program such as [JS Bin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials and challenges - [Introducing asynchronous JavaScript](/en-US/docs/Learn_web_development/Extensions/Async_JS/Introducing) - : In this article, we'll learn about **synchronous** and **asynchronous** programming, why we often need to use asynchronous techniques, and the problems related to the way asynchronous functions have historically been implemented in JavaScript. - [How to use promises](/en-US/docs/Learn_web_development/Extensions/Async_JS/Promises) - : Here we'll introduce promises and show how to use promise-based APIs. We'll also introduce the `async` and `await` keywords. - [Implementing a promise-based API](/en-US/docs/Learn_web_development/Extensions/Async_JS/Implementing_a_promise-based_API) - : This article will outline how to implement your own promise-based API. - [Introducing workers](/en-US/docs/Learn_web_development/Extensions/Async_JS/Introducing_workers) - : Workers enable you to run certain tasks in a separate thread to keep your main code responsive. In this article, we'll rewrite a long-running synchronous function to use a worker. - [Sequencing animations](/en-US/docs/Learn_web_development/Extensions/Async_JS/Sequencing_animations) <sup>Challenge</sup> - : This challenge asks you to use promises to play a set of animations in a particular sequence. ## See also - [Asynchronous Programming](https://eloquentjavascript.net/11_async.html) from the fantastic [Eloquent JavaScript](https://eloquentjavascript.net/) online book by Marijn Haverbeke. {{NextMenu("Learn_web_development/Extensions/Async_JS/Introducing", "Learn_web_development/Extensions")}}