Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_apis > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_apis > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_apis > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_apis > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_apis > index.md --- title: Client-side web APIs slug: Learn_web_development/Extensions/Client-side_APIs page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Extensions/Client-side_APIs/Introduction", "Learn_web_development/Extensions")}} **Application Programming Interfaces** (**APIs**) are programming features for manipulating different aspects of the browser and operating system the site is running on, or manipulating data from other websites or services. This module covers common aspects of some of the most common classes of Web APIs that we haven't previously covered in any kind of detail, providing a useful grounding for those who want to go deeper into browser API usage. ## Prerequisites Before starting this module, You should really have learnt the fundamentals of [JavaScript](/en-US/docs/Learn_web_development/Core/Scripting), especially [JavaScript object basics](/en-US/docs/Learn_web_development/Core/Scripting/Object_basics) and core API coverage such as [DOM scripting](/en-US/docs/Learn_web_development/Core/Scripting/DOM_scripting) and [Network requests](/en-US/docs/Learn_web_development/Core/Scripting/Network_requests). It would also be helpful to have some familiarity with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). > [!NOTE] > If you are working on a device where you don't have the ability to create your own files, you could try out (most of) the code examples in an online coding program such as [JSBin](https://jsbin.com/) or [Glitch](https://glitch.com/). ## Tutorials - [Introduction to web APIs](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs/Introduction) - : First up, we'll start by looking at APIs from a high level what are they, how do they work, how do you use them in your code, and how are they structured? We'll also take a look at what the different main classes of APIs are, and what kind of uses they have. - [Video and audio APIs](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs/Video_and_audio_APIs) - : HTML comes with elements for embedding rich media in documents {{htmlelement("video")}} and {{htmlelement("audio")}} which in turn come with their own APIs for controlling playback, seeking, etc. This article shows you how to do common tasks such as creating custom playback controls. - [Drawing graphics](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs/Drawing_graphics) - : The browser contains some very powerful graphics programming tools, from the Scalable Vector Graphics ([SVG](/en-US/docs/Web/SVG)) language, to APIs for drawing on HTML {{htmlelement("canvas")}} elements, (see [The Canvas API](/en-US/docs/Web/API/Canvas_API) and [WebGL](/en-US/docs/Web/API/WebGL_API)). This article provides an introduction to the Canvas API, and further resources to allow you to learn more. - [Client-side storage](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs/Client-side_storage) - : Modern web browsers feature a number of different technologies that allow you to store data related to websites and retrieve it when necessary allowing you to persist data long term, save sites offline, and more. This article explains the very basics of how these work. - [Third party APIs](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs/Third_party_APIs) - : The APIs we've covered so far are built into the browser, but not all APIs are. Many large websites and services such as Google Maps, Facebook, PayPal, etc. provide APIs allowing developers to make use of their data or services (e.g., displaying custom Google Maps on your site, or using Facebook login to log in your users). This article looks at the difference between browser APIs and 3rd party APIs and shows some typical uses of the latter. {{NextMenu("Learn_web_development/Extensions/Client-side_APIs/Introduction", "Learn_web_development/Extensions")}}