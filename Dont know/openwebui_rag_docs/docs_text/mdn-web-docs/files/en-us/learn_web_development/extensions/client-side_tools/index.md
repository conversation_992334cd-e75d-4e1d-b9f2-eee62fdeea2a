Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_tools > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_tools > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_tools > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_tools > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > client-side_tools > index.md --- title: Understanding client-side web development tools slug: Learn_web_development/Extensions/Client-side_tools page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Extensions/Client-side_tools/Overview", "Learn_web_development/Extensions")}} Client-side tooling can be intimidating, but this series of articles aims to illustrate the purpose of some of the most common client-side tool types, explain the tools you can chain together, how to install them using package managers, and control them using the command line. We finish up by providing a complete toolchain example showing you how to get productive. ## Prerequisites Before starting this module, You should have learnt the fundamentals of [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content), [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics), and [JavaScript](/en-US/docs/Learn_web_development/Core/Scripting). You should also be comfortable with using [the terminal/command line](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Command_line). ## Tutorials - [Client-side tooling overview](/en-US/docs/Learn_web_development/Extensions/Client-side_tools/Overview) - : In this article we provide an overview of modern web tooling, what kinds of tools are available and where you'll meet them in the lifecycle of web app development, and how to find help with individual tools. - [Package management basics](/en-US/docs/Learn_web_development/Extensions/Client-side_tools/Package_management) - : In this article we'll look at package managers in some detail to understand how we can use them in our own projects to install project tool dependencies, keep them up-to-date, and more. - [Introducing a complete toolchain](/en-US/docs/Learn_web_development/Extensions/Client-side_tools/Introducing_complete_toolchain) - : In the final couple of articles in the series we will solidify your tooling knowledge by walking you through the process of building up a sample case study toolchain. We'll go all the way from setting up a sensible development environment and putting transformation tools in place to actually deploying your app. In this article we'll introduce the case study, set up our development environment, and set up our code transformation tools. - [Deploying our app](/en-US/docs/Learn_web_development/Extensions/Client-side_tools/Deployment) - : In the final article in our series, we take the example toolchain we built up in the previous article and add to it so that we can deploy our sample app. We push the code to GitHub and deploy it to GitHub pages, and even show you how to add a simple test into the process. {{NextMenu("Learn_web_development/Extensions/Client-side_tools/Overview", "Learn_web_development/Extensions")}}