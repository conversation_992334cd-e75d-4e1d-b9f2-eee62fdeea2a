Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_1 > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_1 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_1 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_1 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_1 > index.md --- title: Example 1 slug: Learn_web_development/Extensions/Forms/How_to_build_custom_form_controls/Example_1 page-type: learn-module-chapter sidebar: learnsidebar --- This is the first example of code that explains [how to build a custom form widget](/en-US/docs/Learn_web_development/Extensions/Forms/How_to_build_custom_form_controls). ## Basic state ### HTML ```html <div class="select"> <span class="value">Cherry</span> <ul class="optList hidden"> <li class="option">Cherry</li> <li class="option">Lemon</li> <li class="option">Banana</li> <li class="option">Strawberry</li> <li class="option">Apple</li> </ul> </div> ``` ### CSS ```css /* --------------- */ /* Required Styles */ /* --------------- */ .select { position: relative; display: inline-block; } .select.active, .select:focus { box-shadow: 0 0 3px 1px #227755; outline-color: transparent; } .select .optList { position: absolute; top: 100%; left: 0; } .select .optList.hidden { max-height: 0; visibility: hidden; } /* ------------ */ /* Fancy Styles */ /* ------------ */ .select { font-size: 0.625em; /* 10px */ font-family: Verdana, Arial, sans-serif; box-sizing: border-box; padding: 0.1em 2.5em 0.2em 0.5em; /* 1px 25px 2px 5px */ width: 10em; /* 100px */ border: 0.2em solid #000; /* 2px */ border-radius: 0.4em; /* 4px */ box-shadow: 0 0.1em 0.2em rgb(0 0 0 / 45%); /* 0 1px 2px */ background: #f0f0f0; background: linear-gradient(0deg, #e3e3e3, #fcfcfc 50%, #f0f0f0); } .select .value { display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: top; } .select::after { content: " "; position: absolute; z-index: 1; height: 100%; width: 2em; /* 20px */ top: 0; right: 0; padding-top: 0.1em; box-sizing: border-box; text-align: center; border-left: 0.2em solid #000; border-radius: 0 0.1em 0.1em 0; background-color: #000; color: #fff; } .select .optList { z-index: 2; list-style: none; margin: 0; padding: 0; background: #f0f0f0; border: 0.2em solid #000; border-top-width: 0.1em; border-radius: 0 0 0.4em 0.4em; box-shadow: 0 0.2em 0.4em rgb(0 0 0 / 40%); box-sizing: border-box; min-width: 100%; max-height: 10em; /* 100px */ overflow-y: auto; overflow-x: hidden; } .select .option { padding: 0.2em 0.3em; } .select .highlight { background: #000; color: #ffffff; } ``` ### Result for basic state {{ EmbedLiveSample('Basic_state', 120, 130) }} ## Active state ### HTML ```html <div class="select active"> <span class="value">Cherry</span> <ul class="optList hidden"> <li class="option">Cherry</li> <li class="option">Lemon</li> <li class="option">Banana</li> <li class="option">Strawberry</li> <li class="option">Apple</li> </ul> </div> ``` ### CSS ```css /* --------------- */ /* Required Styles */ /* --------------- */ .select { position: relative; display: inline-block; } .select.active, .select:focus { box-shadow: 0 0 3px 1px #227755; outline-color: transparent; } .select .optList { position: absolute; top: 100%; left: 0; } .select .optList.hidden { max-height: 0; visibility: hidden; } /* ------------ */ /* Fancy Styles */ /* ------------ */ .select { font-size: 0.625em; /* 10px */ font-family: Verdana, Arial, sans-serif; box-sizing: border-box; padding: 0.1em 2.5em 0.2em 0.5em; /* 1px 25px 2px 5px */ width: 10em; /* 100px */ border: 0.2em solid #000; /* 2px */ border-radius: 0.4em; /* 4px */ box-shadow: 0 0.1em 0.2em rgb(0 0 0 / 45%); /* 0 1px 2px */ background: #f0f0f0; background: linear-gradient(0deg, #e3e3e3, #fcfcfc 50%, #f0f0f0); } .select .value { display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: top; } .select::after { content: " "; position: absolute; z-index: 1; height: 100%; width: 2em; /* 20px */ top: 0; right: 0; padding-top: 0.1em; box-sizing: border-box; text-align: center; border-left: 0.2em solid #000; border-radius: 0 0.1em 0.1em 0; background-color: #000; color: #fff; } .select .optList { z-index: 2; list-style: none; margin: 0; padding: 0; background: #f0f0f0; border: 0.2em solid #000; border-top-width: 0.1em; border-radius: 0 0 0.4em 0.4em; box-shadow: 0 0.2em 0.4em rgb(0 0 0 / 40%); box-sizing: border-box; min-width: 100%; max-height: 10em; /* 100px */ overflow-y: auto; overflow-x: hidden; } .select .option { padding: 0.2em 0.3em; } .select .highlight { background: #000; color: #ffffff; } ``` ### Result for active state {{ EmbedLiveSample('Active_state', 120, 130) }} ## Open state ### HTML ```html <div class="select active"> <span class="value">Cherry</span> <ul class="optList"> <li class="option highlight">Cherry</li> <li class="option">Lemon</li> <li class="option">Banana</li> <li class="option">Strawberry</li> <li class="option">Apple</li> </ul> </div> ``` ### CSS ```css /* --------------- */ /* Required Styles */ /* --------------- */ .select { position: relative; display: inline-block; } .select.active, .select:focus { box-shadow: 0 0 3px 1px #227755; outline-color: transparent; } .select .optList { position: absolute; top: 100%; left: 0; } .select .optList.hidden { max-height: 0; visibility: hidden; } /* ------------ */ /* Fancy Styles */ /* ------------ */ .select { font-size: 0.625em; /* 10px */ font-family: Verdana, Arial, sans-serif; box-sizing: border-box; padding: 0.1em 2.5em 0.2em 0.5em; /* 1px 25px 2px 5px */ width: 10em; /* 100px */ border: 0.2em solid #000; /* 2px */ border-radius: 0.4em; /* 4px */ box-shadow: 0 0.1em 0.2em rgb(0 0 0 / 45%); /* 0 1px 2px */ background: #f0f0f0; background: linear-gradient(0deg, #e3e3e3, #fcfcfc 50%, #f0f0f0); } .select .value { display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: top; } .select::after { content: " "; position: absolute; z-index: 1; height: 100%; width: 2em; /* 20px */ top: 0; right: 0; padding-top: 0.1em; box-sizing: border-box; text-align: center; border-left: 0.2em solid #000; border-radius: 0 0.1em 0.1em 0; background-color: #000; color: #fff; } .select .optList { z-index: 2; list-style: none; margin: 0; padding: 0; background: #f0f0f0; border: 0.2em solid #000; border-top-width: 0.1em; border-radius: 0 0 0.4em 0.4em; box-shadow: 0 0.2em 0.4em rgb(0 0 0 / 40%); box-sizing: border-box; min-width: 100%; max-height: 10em; /* 100px */ overflow-y: auto; overflow-x: hidden; } .select .option { padding: 0.2em 0.3em; } .select .highlight { background: #000; color: #fff; } ``` ### Result for open state {{ EmbedLiveSample('Open_state', 120, 130) }}