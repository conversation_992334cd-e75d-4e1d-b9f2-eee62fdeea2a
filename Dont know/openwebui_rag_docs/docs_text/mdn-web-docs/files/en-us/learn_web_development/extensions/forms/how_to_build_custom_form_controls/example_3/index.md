Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_3 > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_3 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_3 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_3 > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > how_to_build_custom_form_controls > example_3 > index.md --- title: Example 3 slug: Learn_web_development/Extensions/Forms/How_to_build_custom_form_controls/Example_3 page-type: learn-module-chapter sidebar: learnsidebar --- This is the third example that explain [how to build custom form widgets](/en-US/docs/Learn_web_development/Extensions/Forms/How_to_build_custom_form_controls). ## Change states ### HTML ```html <form class="no-widget"> <select name="myFruit" tabindex="-1"> <option>Cherry</option> <option>Lemon</option> <option>Banana</option> <option>Strawberry</option> <option>Apple</option> </select> <div class="select" tabindex="0"> <span class="value">Cherry</span> <ul class="optList hidden"> <li class="option">Cherry</li> <li class="option">Lemon</li> <li class="option">Banana</li> <li class="option">Strawberry</li> <li class="option">Apple</li> </ul> </div> </form> ``` ### CSS ```css .widget select, .no-widget .select { position: absolute; left: -5000em; height: 0; overflow: hidden; } /* --------------- */ /* Required Styles */ /* --------------- */ .select { position: relative; display: inline-block; } .select.active, .select:focus { box-shadow: 0 0 3px 1px #227755; outline-color: transparent; } .select .optList { position: absolute; top: 100%; left: 0; } .select .optList.hidden { max-height: 0; visibility: hidden; } /* ------------ */ /* Fancy Styles */ /* ------------ */ .select { font-size: 0.625em; /* 10px */ font-family: Verdana, Arial, sans-serif; box-sizing: border-box; padding: 0.1em 2.5em 0.2em 0.5em; /* 1px 25px 2px 5px */ width: 10em; /* 100px */ border: 0.2em solid #000; /* 2px */ border-radius: 0.4em; /* 4px */ box-shadow: 0 0.1em 0.2em rgb(0 0 0 / 45%); /* 0 1px 2px */ background: #f0f0f0; background: linear-gradient(0deg, #e3e3e3, #fcfcfc 50%, #f0f0f0); } .select .value { display: inline-block; width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: top; } .select::after { content: " "; position: absolute; z-index: 1; height: 100%; width: 2em; /* 20px */ top: 0; right: 0; padding-top: 0.1em; box-sizing: border-box; text-align: center; border-left: 0.2em solid #000; border-radius: 0 0.1em 0.1em 0; background-color: #000; color: #fff; } .select .optList { z-index: 2; list-style: none; margin: 0; padding: 0; background: #f0f0f0; border: 0.2em solid #000; border-top-width: 0.1em; border-radius: 0 0 0.4em 0.4em; box-shadow: 0 0.2em 0.4em rgb(0 0 0 / 40%); box-sizing: border-box; min-width: 100%; max-height: 10em; /* 100px */ overflow-y: auto; overflow-x: hidden; } .select .option { padding: 0.2em 0.3em; } .select .highlight { background: #000; color: #ffffff; } ``` ### JavaScript ```js // -------------------- // // Function definitions // // -------------------- // function deactivateSelect(select) { if (!select.classList.contains("active")) return; const optList = select.querySelector(".optList"); optList.classList.add("hidden"); select.classList.remove("active"); } function activeSelect(select, selectList) { if (select.classList.contains("active")) return; selectList.forEach(deactivateSelect); select.classList.add("active"); } function toggleOptList(select, show) { const optList = select.querySelector(".optList"); optList.classList.toggle("hidden"); } function highlightOption(select, option) { const optionList = select.querySelectorAll(".option"); optionList.forEach((other) => { other.classList.remove("highlight"); }); option.classList.add("highlight"); } // ------------- // // Event binding // // ------------- // window.addEventListener("load", () => { const form = document.querySelector("form"); form.classList.remove("no-widget"); form.classList.add("widget"); }); window.addEventListener("load", () => { const selectList = document.querySelectorAll(".select"); selectList.forEach((select) => { const optionList = select.querySelectorAll(".option"); optionList.forEach((option) => { option.addEventListener("mouseover", () => { highlightOption(select, option); }); }); select.addEventListener("click", (event) => { toggleOptList(select); }); select.addEventListener("focus", (event) => { activeSelect(select, selectList); }); select.addEventListener("blur", (event) => { deactivateSelect(select); }); select.addEventListener("keyup", (event) => { if (event.key === "Escape") { deactivateSelect(select); } }); }); }); ``` ### Result {{ EmbedLiveSample('Change_states') }}