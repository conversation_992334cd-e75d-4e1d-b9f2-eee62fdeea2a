Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > basic_controls > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > basic_controls > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > basic_controls > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > basic_controls > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > basic_controls > index.md --- title: "Test your skills: Basic controls" short-title: Basic controls slug: Learn_web_development/Extensions/Forms/Test_your_skills/Basic_controls page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [Basic native form controls](/en-US/docs/Learn_web_development/Extensions/Forms/Basic_native_form_controls) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON>P<PERSON>](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Basic controls 1 This task starts you off nice and gently by asking you to create two `<input>` elements, for a user's ID and password, along with a submit button. 1. Create appropriate inputs for user ID and password. 2. You should also associate them with their text labels semantically. 3. Create a submit button inside the remaining list item, with button text of "Log in". Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/forms/tasks/basic-controls/basic-controls1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/basic-controls/basic-controls1-download.html) to work in your own editor or in an online editor. ## Basic controls 2 The next task requires you to create working sets of checkboxes and radio buttons, from the provided text labels. 1. Turn the first `<fieldset>`'s contents into a set of radio buttons you should only be able to select one pony character at once. 2. Make it so that the first radio button is selected upon page load. 3. Turn the second `<fieldset>`'s content into a set of checkboxes. 4. Add a couple more hotdog choices of your own. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/forms/tasks/basic-controls/basic-controls2.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/basic-controls/basic-controls2-download.html) to work in your own editor or in an online editor. ## Basic controls 3 The final task in this set requires you to create a file picker. 1. Create a basic file picker. 2. Allow the user to pick multiple files at once. 3. Allow the file picker to accept JPG and PNG images only. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/forms/tasks/basic-controls/basic-controls3.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/basic-controls/basic-controls3-download.html) to work in your own editor or in an online editor.