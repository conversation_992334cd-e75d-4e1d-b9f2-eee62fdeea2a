Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > form_structure > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > form_structure > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > form_structure > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > form_structure > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > form_structure > index.md --- title: "Test your skills: Form structure" short-title: Form structure slug: Learn_web_development/Extensions/Forms/Test_your_skills/Form_structure page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [How to structure a web form](/en-US/docs/Learn_web_development/Extensions/Forms/How_to_structure_a_web_form) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON>Pen](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Form structure 1 In this task we want you to structure the provided form features: 1. Separate out the first two and second two form fields into two distinct containers, each with a descriptive legend (use "Personal details" for the first two, and "Comment information" for the second two). 2. Mark up each text label with an appropriate element so that it is semantically associated with its respective form field. 3. Add a suitable set of structural elements around the label/field pairs to separate them out. {{EmbedGHLiveSample("learning-area/html/forms/tasks/form-structure/form-structure1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/form-structure/form-structure1-download.html) to work in your own editor or in an online editor.