Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > input_types > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > input_types > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > input_types > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > input_types > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > input_types > index.md --- title: "Test your skills: Input types" short-title: Input types slug: Learn_web_development/Extensions/Forms/Test_your_skills/Input_types page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [The HTML5 input types](/en-US/docs/Learn_web_development/Extensions/Forms/HTML5_input_types) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON><PERSON><PERSON>](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## HTML controls 1 First, let's explore some input types. Create appropriate inputs for a user to update their details for: 1. Email 2. Website 3. Phone number 4. Favorite color Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/forms/tasks/html5-controls/html5-controls1.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/html5-controls/html5-controls1-download.html) to work in your own editor or in an online editor. ## HTML controls 2 Next, we want you to implement a slider control to allow the user to choose a maximum number of people to invite to their party. 1. Implement a basic slider control to go along with the provided label. 2. Give it a minimum value of 1, maximum value of 30, initial value of 10 and element `id` of `max-invite`. 3. Create a corresponding output element to put the current value of the slider into. Give it a class of `invite-output`, and semantically associate it with the input. If you do this correctly, the JavaScript included on the page will automatically update the output value when the slider is moved. Try updating the live code below to recreate the finished example: {{EmbedGHLiveSample("learning-area/html/forms/tasks/html5-controls/html5-controls2.html", '100%', 700)}} > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/html5-controls/html5-controls2-download.html) to work in your own editor or in an online editor.