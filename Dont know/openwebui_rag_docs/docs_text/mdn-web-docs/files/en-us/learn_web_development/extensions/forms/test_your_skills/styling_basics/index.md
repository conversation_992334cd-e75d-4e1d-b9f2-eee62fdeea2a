Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > styling_basics > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > styling_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > styling_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > styling_basics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > forms > test_your_skills > styling_basics > index.md --- title: "Test your skills: Styling basics" short-title: Basic styling slug: Learn_web_development/Extensions/Forms/Test_your_skills/Styling_basics page-type: learn-module-assessment sidebar: learnsidebar --- The aim of this skill test is to assess whether you've understood our [Styling web forms](/en-US/docs/Learn_web_development/Extensions/Forms/Styling_web_forms) article. > [!NOTE] > You can try solutions in the interactive editors on this page or in an online editor such as [<PERSON><PERSON><PERSON>](https://codepen.io/), [JSFiddle](https://jsfiddle.net/), or [Glitch](https://glitch.com/). > > If you get stuck, you can reach out to us in one of our [communication channels](/en-US/docs/MDN/Community/Communication_channels). ## Styling basics 1 Our basic form styling assessment is fairly free-form, and you have a lot of flexibility over what you end up doing here. But your CSS should aim to fulfill the following requirements: 1. Add some kind of lightweight "reset" to make fonts, padding, margin, and sizing more consistent to begin with. 2. On top of that, add in some nice, consistent styling for the inputs and button. 3. Use some kind of layout technique to make the inputs and labels line up neatly. > [!CALLOUT] > > [Download the starting point for this task](https://github.com/mdn/learning-area/blob/main/html/forms/tasks/styling-basics/styling-basics1-download.html) to work in your own editor or in an online editor.