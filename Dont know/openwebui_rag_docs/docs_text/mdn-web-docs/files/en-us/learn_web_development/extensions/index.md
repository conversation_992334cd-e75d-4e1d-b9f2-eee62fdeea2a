Path: mdn-web-docs > files > en-us > learn_web_development > extensions > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > index.md --- title: Extension modules slug: Learn_web_development/Extensions page-type: landing-page sidebar: learnsidebar --- Our **Extension modules** constitute useful additional skills to learn as web developers start to expand their knowledge and develop specialisms. ## Prerequisites While there is no specific prerequisite knowledge for starting any of these modules, it is advisable that you work through our [Core modules](/en-US/docs/Learn_web_development/Core) first, if you are a newcomer to web technologies. In particular, if you've never done any coding before, we'd recommend the [Your first website](/en-US/docs/Learn_web_development/Getting_started/Your_first_website) module, which introduces you web technologies very gently and takes you through the process of building a simple website. ## Modules - [Advanced JavaScript objects](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects) - : Having a deeper knowledge of how JavaScript objects work is useful as you build confidence with web development, start to build more complex apps, and create your own libraries. This module will help you by providing JavaScript object theory and object-oriented programming practice. - [Client-side web APIs](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs) - : **Application Programming Interfaces** (**APIs**) are programming features for manipulating different aspects of the browser and operating system the site is running on, or manipulating data from other websites or services. This module covers common aspects of some of the most common classes of Web APIs that we haven't previously covered in any kind of detail, providing a useful grounding for those who want to go deeper into browser API usage. - [Asynchronous JavaScript](/en-US/docs/Learn_web_development/Extensions/Async_JS) - : In this module, we take a look at {{Glossary("asynchronous")}} {{Glossary("JavaScript")}}, why it is important, and how it can be used to effectively handle potential blocking operations, such as fetching resources from a server. - [Web forms](/en-US/docs/Learn_web_development/Extensions/Forms) - : This module provides a series of articles that will help you master the essentials of web forms. Web forms are a very powerful tool for interacting with users most commonly they are used for collecting data from users, or allowing them to control a user interface. However, for historical and technical reasons, it's not always obvious how to use them to their full potential. In the articles listed below, we'll cover all the essential aspects of Web forms including marking up their HTML structure, styling form controls, validating form data, and submitting data to the server. - [Understanding client-side web development tools](/en-US/docs/Learn_web_development/Extensions/Client-side_tools) - : Client-side tooling can be intimidating, but this series of articles aims to illustrate the purpose of some of the most common client-side tool types, explain the tools you can chain together, how to install them using package managers, and control them using the command line. We finish up by providing a complete toolchain example showing you how to get productive. - [Server-side website programming](/en-US/docs/Learn_web_development/Extensions/Server-side) - : This series of modules shows how to create server-side websites; websites that deliver customized information in response to HTTP requests. The modules provide a general introduction to server-side programming, along with specific beginner-level guides on how to use the Django (Python) and Express (Node.js/JavaScript) web frameworks to create basic applications. - [Web performance](/en-US/docs/Learn_web_development/Extensions/Performance) - : Building websites requires HTML, CSS, and JavaScript. To build websites and applications people want to use, which attract and retain users, you need to create a good user experience. Part of good user experience is ensuring the content is quick to load and responsive to user interaction. This is known as **web performance**, and in this module you'll focus on the fundamentals of how to create performant websites. - [Testing](/en-US/docs/Learn_web_development/Extensions/Testing) - : Any codebase past a certain level of complexity needs to have a system of tests associated with it, to make sure that as new code is added, the codebase continues to function correctly and performantly, and continues to meet the users' needs. This module lists the fundamentals that you should start with. - [Transform and animate CSS](/en-US/docs/Learn_web_development/Extensions/Transform_animate) - : Animations are a vital part of a good user experience. Subtle usage can make page designs more interesting and appealing, and also enhance usability and perceived performance. - [Security and privacy](/en-US/docs/Learn_web_development/Extensions/Security_privacy) - : It is vital to have an understanding of how you can and should protect your data and your user's data from would-be attackers who may try to steal it. This module covers both hardening websites to make it more difficult to steal data, and collecting user data in a respectful way that avoids tracking them or sharing it with unsuitable third parties.