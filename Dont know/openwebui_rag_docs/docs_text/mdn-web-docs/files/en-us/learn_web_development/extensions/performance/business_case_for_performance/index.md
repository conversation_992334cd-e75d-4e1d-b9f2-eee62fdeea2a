Path: mdn-web-docs > files > en-us > learn_web_development > extensions > performance > business_case_for_performance > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > performance > business_case_for_performance > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > performance > business_case_for_performance > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > performance > business_case_for_performance > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > performance > business_case_for_performance > index.md --- title: The business case for web performance short-title: Performance business case slug: Learn_web_development/Extensions/Performance/business_case_for_performance page-type: learn-module-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Extensions/Performance/CSS", "Learn_web_development/Extensions/Performance/Web_Performance_Basics", "Learn_web_development/Extensions/Performance")}} We've discussed the importance of web performance. You've learned what you need to do to optimize for web performance. But how do you convince your clients and/or management to prioritize and invest in performance? In this section, we discuss creating a clear business case to convince decision-makers to make the investment. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> Basic knowledge of <a href="/en-US/docs/Learn_web_development/Getting_started/Your_first_website" >client-side web technologies</a >, and a basic understanding of <a href="/en-US/docs/Web/Performance">web performance optimization</a>. </td> </tr> <tr> <th scope="row">Objective:</th> <td> To gain confidence in working with clients and management to get them to make web performance a priority. </td> </tr> </tbody> </table> ## Making performance a business priority We've discussed how prioritizing performance can improve user experience and therefore revenue. We know that not prioritizing web performance can result in a loss of business revenue. This article discusses how certain business metrics directly relate to a user's web performance experience and how to apply service design to boost the user's experiences of web performance. It highlights the importance of understanding how cumulative experiences impact conversion and retention rates. ### Performance budgets Setting a web performance budget can help you make sure the team stays on track in keeping the site and help prevent regressions. A performance budget is a set of limits that are set to specify limits, such as the maximum number of HTTP requests allowed, the maximum total size of all the assets combined, the minimum allowable FPS on a specific device, etc., that must be maintained. The budget can be applied to a single file, a file type, all files loaded on a page, a specific metric, or a threshold over a period of time. The budget reflects reachable goals; whether they are time, quantity, or rule-based. Defining and promoting a budget helps performance proponents advocate for good user experience against competing interests, such as marketing, sales, or even other developers that may want to add videos, 3rd party scripts, or even frameworks. [Performance budgets](/en-US/docs/Web/Performance/Guides/Performance_budgets) help developer teams protect optimal performance for users while enabling the business to tap into new markets and deliver custom experiences. ### Key Performance Indicators Setting key performance indicators (KPI) as objectives can highlight performance objectives that are also business objectives. KPIs can be both a set of important business metrics in measuring the impact of user experience and performance on the business's top line, and a way of demonstrating the benefits of prioritizing performance. Here are some KPIs to consider: - **Conversion Rate** - : The percent of your traffic that takes an intended action, such as completing a purchase or signing up for a newsletter. When a business site is slow, it can prevent users from completing their intended task. This can lead to low conversion rates. - **Time on Site** - : The average time that your users in aggregate spend on your site. When a site performs slowly, users are more likely to abandon the site prematurely which can lead to low time on site metrics. - **Net Promotion Score** - : The net promoter score (NPS) is a metric for assessing customer-loyalty for a company's brand, product, or service. Poor user performance experiences can equate to poor brand reputation. Setting conversion rate, time on site, and/or net promotion scores as KPIs give financial and other business goal value to the web performance efforts, and get help boost buy-in, with metrics to prove the efforts worth. {{PreviousMenu("Learn_web_development/Extensions/Performance/CSS", "Learn_web_development/Extensions/Performance")}}