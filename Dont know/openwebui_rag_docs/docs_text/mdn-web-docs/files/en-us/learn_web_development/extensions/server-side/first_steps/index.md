Path: mdn-web-docs > files > en-us > learn_web_development > extensions > server-side > first_steps > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > server-side > first_steps > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > server-side > first_steps > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > server-side > first_steps > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > server-side > first_steps > index.md --- title: Server-side website programming first steps short-title: First steps slug: Learn_web_development/Extensions/Server-side/First_steps page-type: learn-module sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Extensions/Server-side/First_steps/Introduction", "Learn_web_development/Extensions/Server-side")}} In this module, we answer a few fundamental questions about server-side programming such as "What is it?", "How does it differ from client-side programming?", and "Why is it so useful?". We also provide an overview of some of the most popular server-side web frameworks, along with guidance on how to select the most suitable framework for creating your first project. Finally, we provide a high-level introductory article about web server security. ## Prerequisites Before starting this module, you don't need to have any knowledge of server-side website programming or any other type of programming. However, you should understand something about the workings of websites and web servers. For that purpose, this is our recommended reading: - [What is a web server?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_web_server) - [What software do I need to build a website?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/What_software_do_I_need) - [How do you upload files to a web server?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Upload_files_to_a_web_server) With the basic understanding that you gain from this preparation, you'll be ready to work your way through the modules in this section. ## Tutorials - [Introduction to the server-side](/en-US/docs/Learn_web_development/Extensions/Server-side/First_steps/Introduction) - : Welcome to the MDN beginner's server-side programming course! The first article examines server-side programming from a high level, answering questions such as "What is it?", "How does it differ from client-side programming?", and "Why it is so useful?". After reading this, you will understand the additional capabilities available to websites through server-side coding. - [Client-Server overview](/en-US/docs/Learn_web_development/Extensions/Server-side/First_steps/Client-Server_overview) - : Now that you know the purpose and potential benefits of server-side programming, we're going to examine what happens when a server receives a "dynamic request" from a browser. As most websites' server-side code handles requests and responses in a similar way, this will help you understand what you need to do when writing your own code. - [Server-side web frameworks](/en-US/docs/Learn_web_development/Extensions/Server-side/First_steps/Web_frameworks) - : The previous article explained what a server-side web application needs to do to respond to web browser requests. This article explains how web frameworks can simplify these tasks, and helps you choose the right framework for your first server-side web application. - [Website security](/en-US/docs/Learn_web_development/Extensions/Server-side/First_steps/Website_security) - : Website security requires vigilance in all aspects of building and operating a site. This introductory article helps you understand the first important steps you can take to protect your web application against the most common threats. > [!NOTE] > This topic deals with server-side frameworks, and how to use them to create websites. If you are looking for information on client-side JavaScript frameworks, see [Understanding client-side JavaScript frameworks](/en-US/docs/Learn_web_development/Core/Frameworks_libraries). ## Assessments This "first steps" module doesn't have any assessment because we haven't yet shown you any code. At this point, you should have a general understanding of the functionality you can deliver with server-side programming, and you have made a decision about what server-side web framework you will use to create your first server-side application. {{NextMenu("Learn_web_development/Extensions/Server-side/First_steps/Introduction", "Learn_web_development/Extensions/Server-side")}}