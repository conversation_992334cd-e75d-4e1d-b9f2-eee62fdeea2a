Path: mdn-web-docs > files > en-us > learn_web_development > extensions > transform_animate > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > extensions > transform_animate > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > transform_animate > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > transform_animate > index.md Path: mdn-web-docs > files > en-us > learn_web_development > extensions > transform_animate > index.md --- title: Transform and animate CSS slug: Learn_web_development/Extensions/Transform_animate page-type: learn-module sidebar: learnsidebar --- > [!NOTE] > As you'll see below, this module is currently only a curriculum outline/syllabus, with some links provided to starter guides. We are intending to convert this into a full course in the future, as time allows. Animations are a vital part of a good user experience. Subtle usage can make page designs more interesting and appealing, and also enhance usability and perceived performance. ## Prerequisites Before starting this module, you should be familiar with [HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) and [CSS](/en-US/docs/Learn_web_development/Core/Styling_basics). ## Learning outcomes - Understand why CSS transforms and animation are needed. - A caveat overuse can negatively affect usability and accessibility. - Common transforms scaling, rotation, and translation. - 3D transforms, and how 3D positioning/perspective is handled on the web. - Transitions. - Animations. ## Guides - [Using CSS transforms](/en-US/docs/Web/CSS/CSS_transforms/Using_CSS_transforms) - : By modifying the coordinate space, CSS transforms change the shape and position of the affected content without disrupting the normal document flow. This guide provides an introduction to using transforms. - [Using CSS transitions](/en-US/docs/Web/CSS/CSS_transitions/Using_CSS_transitions) - : CSS transitions provide a way to control animation speed when changing CSS properties. Instead of having property changes take effect immediately, you can cause the changes in a property to take place over a period of time. For example, if you change the color of an element from white to black, usually the change is instantaneous. With CSS transitions enabled, changes occur at time intervals that follow an acceleration curve, all of which can be customized. - [Using CSS animations](/en-US/docs/Web/CSS/CSS_animations/Using_CSS_animations) - : CSS animations make it possible to animate transitions from one CSS style configuration to another. Animations consist of two components: a style describing the CSS animation and a set of keyframes that indicate the start and end states of the animation's style, as well as possible intermediate waypoints.