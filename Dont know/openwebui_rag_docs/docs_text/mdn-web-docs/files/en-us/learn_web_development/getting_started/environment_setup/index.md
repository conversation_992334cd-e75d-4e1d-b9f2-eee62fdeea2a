Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > environment_setup > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > environment_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > environment_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > environment_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > environment_setup > index.md --- title: Environment Setup slug: Learn_web_development/Getting_started/Environment_setup page-type: landing-page sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Getting_started/Environment_setup/Installing_software", "Learn_web_development/Getting_started")}} In the _Environment setup_ module, we show you what tools you need to do simple web development and how to install them properly and help you understand important aspects of your environment such as file systems and the command line. ## Prerequisites This module assumes no prior technical knowledge, beyond basic computer usage. You should be able to: - Sign into your computer and connect it to the internet. - Use basic system controls such as keyboard and mouse (or other pointing devices). - Install applications. If you need to refresh yourself on such basics, we'd recommend the following resources depending on what operating system you are using: - [Windows help and learning](https://support.microsoft.com/en-us/windows), Microsoft (2024) - [macOS User Guide](https://support.apple.com/guide/mac-help/welcome/mac), Apple (2024) - [Official Ubuntu documentation](https://help.ubuntu.com/), ubuntu.com (2024) ## Tutorials - [Installing software](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Installing_software) - : In this article, we show you what tools you need to do simple web development and how to install them properly. We'll set you up with the bare minimum of tools for now, including a text editor and some modern web browsers. - [Browsing the web](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Browsing_the_web) - : This article goes a little deeper into using browsers, looking at how a web browser works, the difference between some of the common items you'll interact with, and how to search for information. - [Code editors](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Code_editors) - : In this article we look at code editors in more detail, giving you an idea of what they can do for you. - [Dealing with files](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Dealing_with_files) - : This article discusses some issues you should be aware of with file systems so you can set up a sensible file structure for your website. - [Command line crash course](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Command_line) - : This article provides an introduction to the terminal, the essential commands you'll need to enter into it, how to chain commands together, and how to add your own command line interface (CLI) tools.