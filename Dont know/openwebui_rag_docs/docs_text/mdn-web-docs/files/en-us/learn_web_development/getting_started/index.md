Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > index.md --- title: Getting started modules slug: Learn_web_development/Getting_started page-type: landing-page sidebar: learnsidebar --- Welcome to **Getting started**! If you are a complete beginner (i.e., you've not installed a code editor or written any code yet), then this is the place to be. The Getting started modules take you through installing the software you need, familiarity with your development environment, taking your first stab at building a simple website, and understanding some of the essential concepts surrounding web development. ## Prerequisites There is no prerequisite knowledge for starting this course. Also note that we don't consider learning these topics as required before moving on to the [Core modules](/en-US/docs/Learn_web_development/Core), but we believe students will have an easier time if they spend some time on these topics first. ## Modules - [Environment setup](/en-US/docs/Learn_web_development/Getting_started/Environment_setup) - : In the _Environment setup_ module, we show you what tools you need to do simple web development and how to install them properly and help you understand important aspects of your environment such as file systems and the command line. - [Your first website](/en-US/docs/Learn_web_development/Getting_started/Your_first_website) - : This module introduces you to the practicalities of web development. You'll gather the assets and write the code to construct and publish a simple webpage. - [Web standards](/en-US/docs/Learn_web_development/Getting_started/Web_standards) - : This module covers the fundamentals of how the web works at a high level including the model used for communication, the core technologies involved, how those technologies are created, and how a web browser renders and displays websites to a user. - [Soft skills](/en-US/docs/Learn_web_development/Getting_started/Soft_skills) - : This module provides recommendations of soft skills you can aim to get better at while learning web development, and which constitute good traits to have when entering the industry. They will help immensely in developing the right attitudes for learning, researching, and collaborating, and increase the chances of getting hired. ## See also - [The Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j?via=mdn) <sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup> - : [Scrimba's](https://scrimba.com?via=mdn) _Frontend Developer Career Path_ teaches all you need to know to be a competent front-end web developer, with fun interactive lessons and challenges, knowledgeable teachers, and a supportive community. Go from zero to landing your first front-end job! Many of the course components are available as standalone free versions.