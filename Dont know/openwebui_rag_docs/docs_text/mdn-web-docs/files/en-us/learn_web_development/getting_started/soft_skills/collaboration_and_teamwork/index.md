Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > collaboration_and_teamwork > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > collaboration_and_teamwork > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > collaboration_and_teamwork > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > collaboration_and_teamwork > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > collaboration_and_teamwork > index.md --- title: Collaboration and teamwork slug: Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork page-type: tutorial-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Getting_started/Soft_skills/Research_and_learning", "Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes", "Learn_web_development/Getting_started/Soft_skills")}} As a professional in the web industry, you are going to have to work with other people on projects during multiple phases brainstorming ideas, writing proposals, implementing code, and more. This article looks at how to work together with others. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> N/A </td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>Practice collaboration with teammates, including sharing useful information.</li> <li>Learn to ask useful questions and help one another.</li> <li>Publish your work</li> <li>Widen the net to meetup groups or open source projects to find people to help and collaborate with.</li> <li>Learn to not fear failure, and instead embrace it as an effective learning tool.</li> </ul> </td> </tr> </tbody> </table> ## Working with others As a web developer, you'll have to spend a lot of time working with your peers, whether you are employed or studying. Your colleagues or fellow students could be a hugely valuable aid to your learning, as we first discussed back in [Research and learning](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Research_and_learning#build_a_network_of_contacts), and you should at least make sure that you get on with them to begin with. It is important to treat people with the same level of empathy and respect as you'd wish for. Different people have different requirements emotionally, socially, etc., but at a base level you should be polite, respectful, and cooperative in all engagements. ## Join a community network The web industry has a strong sense of community, meaning that you will often interact with people outside your immediate work or study group to discuss topics of interest or solve problems. The web industry has its own online spaces, such as forums (for example, Discord servers), Slack channels, etc. It also has many in-person events, from high-profile conferences to more low-key local meetups. You should dive into some of those groups, say hello, get to know people, share resources, ask questions, and offer to help. Getting to know people in the community has a host of advantages you will: - Meet useful contacts who may end up being friends or future colleagues or employers. - Learn new techniques and technologies and get help with your tech problems. - Get practice with explaining how code works and answering other people's questions. - Learn about resources, meetups, and industry news. - Encounter adverts for jobs. - Find information about the companies other community members work for, which can also be beneficial for scoping out the job market and figuring out where you'd like to work. Here are some initial ideas for how to get involved in some community networks: - Do some research in your local area, see what local in-person meetup groups there are, and attend some of them. You can look events up using sites like [Meetup.com](https://www.meetup.com/) or [Eventbrite](https://www.eventbrite.co.uk/). - Have a look at resources such as [confs.tech](https://confs.tech/) to find tech conferences of interest. If they are out of your price range, try contacting the organizers to see if you could work the conference as a volunteer you'll still get to attend it and meet lots of people. - [Stack Overflow](https://stackoverflow.com/) is a great place to ask questions and answer other people's questions. - The [freeCodeCamp forums](https://forum.freecodecamp.org/) is another great place to ask and answer questions. - Check out the [Frontend Developers](https://discord.me/frontenddevelopers) Discord server. - Also hang out on [MDN's Discord](/discord). - Our learning partner, Scrimba, provides a [strong community and collaboration experience](https://scrimba.com/the-frontend-developer-career-path-c0j/~0xyi?via=mdn) via their Discord server, intending to help their students gain experience in working with others. - Find web developers on the social networks, see what they are saying, and join in conversations. If you are not sure how to participate, you could start off by reading existing threads and observing how experienced folks engage in discussions. > [!NOTE] > Most communities publish a document called the **Code of Conduct** (or **participation guidelines**) that defines their expectations of behavior for people who participate in their project. These guidelines usually include a definition of what is considered unacceptable behavior, the consequences of unacceptable behavior, and how to report unacceptable behavior. For example, MDN is governed by the wider [Mozilla Community Participation Guidelines](https://www.mozilla.org/en-US/about/governance/policies/participation/). ## Help others, ask questions If a colleague or network contact is struggling, offer to help them. Web developers who provide help are much more likely to build strong relationships and get help in return. - If you've already got some experience in the area they are struggling in, share knowledge or code examples with them. The act of explaining something will improve your understanding as well, helping you to learn too. - If you have an idea of where they might find some useful answers, help them do some research. - Even if you are not sure what the answer is, offer to take a break with them and listen while they explain the problem. You might have some useful insights, and the act of explaining the problem might help them to figure out the solution (see [Taking breaks, and rubber ducking](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Research_and_learning#taking_breaks_and_rubber_ducking)). Always err on the side of helping immediate peers, rather than waiting for a teacher or senior dev to go around and help everyone. Less able peers will get help more quickly, and more able peers will become mentors and experience the satisfaction that it brings. There is an art to asking and answering questions. It is essential to practice asking the right questions to find out what you need to know, and explaining what you are doing when someone else asks you. If you are not great at doing this, ask your peers if you can set up sessions with them, where they ask questions and you explain how your code works, and vice versa. Peer reviews are also a good place to get practice on this ([see below](#teamwork_and_peer_reviews)). ## Publish your work As a general rule, it is very worthwhile to publish your work, including source code, lessons learned, what was challenging, etc. Your peers will find this very useful, and so will the wider community. When you find a useful answer, write about it. Put your code on a social coding site like [GitHub](https://github.com/) or [GitLab](https://about.gitlab.com/), and write up your thoughts on a blog. Share your publications via social media. Not only will the process of writing clarify the concepts to you, but you'll also get validation and/or feedback from the community. It could help you to start making a name for yourself in the industry. Even if you think your achievements are trivial, you probably know more than you think you do, and there will always be someone at an earlier stage in their journey towards mastering code that will find your work useful. ## Participate in open source Another way to make friends and influence people is to find open source projects and offer updates to fix issues in other people's work. Provided you are polite and respectful, and explain your changes and the reasoning behind them, the project owners will be very grateful for your contributions. Your updates will also give you valuable experience and practice. For example, [MDN's source code](https://github.com/mdn/content) is available on GitHub, and we have many [issues](https://github.com/mdn/content/issues) that you could work on. There are plenty of [Community resources](/en-US/docs/MDN/Community) on MDN that describe the project and what contributing to it looks like. If you are not sure what open source is and why or how to start contributing, both of the following are really useful: - [How to Contribute to Open Source Projects A Beginner's Guide](https://www.freecodecamp.org/news/how-to-contribute-to-open-source-projects-beginners-guide/) - [Open source etiquette](/en-US/docs/MDN/Community/Open_source_etiquette) ## Teamwork and peer reviews You will often be required to work on a team with other developers, with different team members performing different functions in the team. We'll look at the different roles you'll encounter and common work strategies in [Workflows and processes](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes), but for now, think about what working in a team means: - Jointly owning the project you are working on both the successes and failures. - Working together to plan not only what you are going to do, but how you will work together to achieve it. What process will you use? How will you measure success? How much time have you got; what goals need to be achieved, and when? - Communicating regularly and openly to make sure problems are caught early, and everyone knows what is going on. - Showing empathy for other team members at all times, but especially when things don't go to plan and adjustments are needed. - Helping each other to succeed. Even if you are not doing a real work project you might be doing a college mock project, or working on your own hobby project you should practice running it like a real software project, with a timeline, plan, and responsibilities. Learn about typical [software development lifecycles](https://www.geeksforgeeks.org/software-development-life-cycle-sdlc/). Pick up some basic [project planning](https://en.wikipedia.org/wiki/Project_planning) skills and tools to be able to estimate and plan your work. It is also a good idea to start doing peer reviews colleagues will review each other's work and provide feedback on how it could be improved. Areas looked at might include general code quality and adherence to code style guides, performance, sticking to best practices, usability, and quality of documentation. You can benefit greatly, whether you are the reviewer, or the person being reviewed: - If you are reviewing someone else's work, aim to provide a balance between encouragement and praise for good work, and critical feedback. Practice how to deliver that feedback constructively and respectfully. Even if they seem somewhat trivial, always come up with one or two questions to ask when discussing or reviewing peer work. - If you work is being reviewed, try not to take criticism personally, and focus on the positives and what you can learn. Assume the best of intentions from your reviewer they are just trying to help. ## Learn to embrace failure A very common issue that causes students and new developers to shy away from experimentation and taking risks (for example when starting new projects or exploring new ideas) is fear of failure, and how it will be judged by peers/team mates. You should spend some time learning about the value that can be gleaned from making mistakes. Failure is really only a small step on the road to success. All of the most successful developers have failed many times, and it is not the actual failure that matters, but how you handle that failure, and the lessons you learn from it. Here are some tips: - Define a safe space/peer group where people are free to ask questions remember that there are no "stupid questions", only people who fail to question. Foster a culture where peers support each other and failure is not judged harshly. - Look to your network and try to find online or in-person groups who can either give you help and advice or are facing the same issues you are and can provide moral support or experiment together. - (For educators) Set up the marking schemes for your assessments so that you can still get a reasonable number of marks even if you didn't get the correct result provided the process is well documented. Award extra marks for innovation. - Run show 'n' tell or one-on-one sessions part-way through a project with peers and mentors to get feedback and insights into where you are going wrong and get advice on how to get back on the right path. - Run retrospective meetings to analyze projects, look at what didn't go so well, and talk about how to improve things next time. {{PreviousMenuNext("Learn_web_development/Getting_started/Soft_skills/Research_and_learning", "Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes", "Learn_web_development/Getting_started/Soft_skills")}}