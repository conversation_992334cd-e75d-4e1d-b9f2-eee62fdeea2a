Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > finding_a_job > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > finding_a_job > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > finding_a_job > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > finding_a_job > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > finding_a_job > index.md --- title: Finding a job slug: Learn_web_development/Getting_started/Soft_skills/Finding_a_job page-type: tutorial-chapter sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes", "Learn_web_development/Core", "Learn_web_development/Getting_started/Soft_skills")}} Technical job interviews can be very demanding, and some have quite specific requirements. Finding the right jobs to apply for in the first place can be challenging! This article provides some tips and resources to help with the process from finding potential jobs and applying, to succeeding in interviews. The process outlined below might not work for everyone exactly as-is you might find that a slightly different approach or order works for you but most will find it useful. > [!NOTE] > You might not even want to think about what job you want right now; you may just want to concentrate on learning some code. That's fine; you can always return to this article later on, when the time is right. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> N/A </td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>Deciding what kind of job you want.</li> <li>Searching for jobs</li> <li>Developing qualities for that employers look for.</li> <li>Selling yourself to would-be employers.</li> <li>Applying for jobs.</li> <li>Preparing yourself for job interviews.</li> </ul> </td> </tr> </tbody> </table> ## Decide what kind of job you want It almost sounds too obvious to write down, but you should think carefully about the kind of job you want before you go out into the job market and start searching. If you don't, you'll probably end up using a scattergun approach that isn't very effective going for a wide variety of jobs and not making yourself look like the ideal candidate for _any_ of them. If you are reading this site, you are probably interested in some kind of web development job. That's fine, but think about it more specifically, and write some ideas down about what you want in the following categories: - Exact type of role: Do you want to be more of a: - Web designer: Use HTML, CSS, and graphics packages to design user interfaces. - JS developer: Implement functionality using JavaScript and frameworks. - Developer relations or sales engineer: Talk to customers, get them excited about technology, help them solve problems and develop solutions. - Technical writer: Write documentation to explain how technology works and how to use it. - More of an all-round developer. - Something else? - Kind of employer: Do you want to work for: - Yourself, as a contractor, on lots of different projects. - A design/development agency, again working on lots of different projects. - A company or corporation, as an in-house developer. - A not-for-profit, or charity. - Sector: Do you want to work in: - Technology. - Health. - Finance. - Charity. - Music industry. - etc. - Work location: Do you want to work - From home full time. - In an office. - In some kind of hybrid setup. - In the same location all the time, or move around between different areas or countries? If you are not sure of the answers to these questions, that's OK. Ask your peers, friends, teachers, or mentor what they are interested in, to get some inspiration, and look around the industry to see what's available. Realistically, many of you will probably start by seeing an interesting job ad or company, then working out how it fits in to the above categories and how to "sell yourself" to that company. ## Sell yourself To get a job, you have to learn how to **sell yourself**, which means making yourself look like a great candidate for the jobs you want. This involves a few different steps, which are described in the sections below. ### Develop qualities for that employers look for As a general point, you should be aware of the attributes that hiring managers look for in a candidate and prepare accordingly. Obviously, you'll need technical skills and other abilities that help you to directly do the task at hand. However, companies also look for "soft skills" that indirectly help you do the job, and also make their employees lives easier and happier while they are at work. We already touched on some of these in our [Collaboration and teamwork](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork) article. To summarize, hiring managers will look for: - Someone they can get along with. - A positive attitude, someone who is respectful, empathic, and constructive. - An open-minded individual who works well in a diverse team with diverse viewpoints. - Someone that is graceful when a decision does not go their way, and able to align for the greater good. - A good communicator and relationship builder. - A tenacious, focused individual with good problem solving skills. Think about whether these statements describe you, or not. This could be a useful way to identify some of your weaknesses, meaning that you can take steps to improve them. For example, if you are not good at communication or problem solving, look up some guides or classes to help you improve those skills. Even something informal such as talking to your friends about your work and describing your projects will help you become better at communicating. ### Create a CV/résumé You should create a CV/résumé, which communicates your worth to an employer. There are lots of guides on how to write one on the web, but in brief, they should generally contain the following: - Your contact details: Include your name, postal address, phone number, email, and any maybe one more contact method that is important to you and suitable for professional communication. A [LinkedIn profile](#create_a_linkedin_profile) is recommended. - A summary/personal statement, which describes you in a single sentence. This can be really hard to write it needs to sound confident but not arrogant, positive but realistic. A good strategy here is to keep it really short and focus on some facts, what you believe in, and what kind of level you are at. For example, a good personal statement for a junior web developer might read some something like this: "Bob Smith is a web developer with 2 years' experience and a great passion for semantics, accessibility, and inclusive design". - A list of work experiences, each including: - A date range specifying when you worked there for example 06/2023 10/2024, or 06/2023 present if you still work there. - Company and job title. - A short paragraph or bulleted list describing what you did in that job/internship/etc. Don't describe _everything_, just what you think are useful experiences for selling yourself. - An education summary, which lists the different qualifications you have, the school you did them at, and the grade you achieved. Include degrees and other higher education, certificates and achievements that are directly relevant to your field (for example, a web development certification or summer school), and perhaps also regular school qualifications that employers often look for (such as English, other languages, and Math). - A section containing supporting information, such as: - Links to your [portfolio](#create_a_portfolio). - Descriptions of any other relevant experience that doesn't fit into the above sections. - Personal interests most employers like their employees to be well-rounded and have a life outside work rather than just being corporate robots whether that's playing music, watching movies, or volunteering at an animal sanctuary. It also helps to create talking points during job interviews. Here are some general tips for CV/résumé writing: - Make it short and concise: Ideally you want it to fit on 2 sides of A4/letter paper. Hiring managers won't bother to read a long essay describing your skill set. - Use professional language: Don't use conversational language or slang terms you need to sound like you are serious about your application. - Check your writing carefully for spelling and grammatical mistakes: Run it through a service such as [Grammarly](https://www.grammarly.com/) to make sure you don't miss any mistakes and get hints for further improvements. Hiring managers will often discard a CV/résumé that contains a typo it shows a lack of professionalism and attention to detail. - Don't necessarily include all of your work experience on your CV/résumé: If you have a lot of experience, just include the most important or significant recent entries. You want to keep it short, but you also don't want to make it look like there are any suspicious gaps in your work history, which hiring managers might question you about. Put your full experience listing on your LinkedIn profile and only include a summary on your CV/résumé. - Include soft skills: As mentioned in the [Develop qualities for that employers look for](#develop_qualities_for_that_employers_look_for) section, soft skills are important, and they are worth including on your CV/résumé. You should think about which ones are relevant employers won't be interested in pages of description of how nice you are, but they will be interested to know that you are a good team player, calm under pressure, and have problem solving skills. - References on request: Employers always require references so they can get a second opinion of you and your skills from someone else. This should ideally be someone you worked with and have a good relationship with, like a previous manager or teacher. If you are not sure who your references are right now, just write "References available on request." at the bottom of your CV/résumé. - Keep your CV/résumé up-to-date: Check it every year to see if anything new needs to be added, or if you want to remove anything. > [!NOTE] > You should think of your CV/résumé as a general document, and then customize it for individual jobs you go for, in terms of the most suitable work experience and skills for each job. ### Get some experience When you start searching for jobs, you will find yourself in a vicious circle _you need experience to get a job, but you also need to get a job to get experience._ Don't despair there are other ways to get experience. Here are some ideas: - Contact local business, charities, churches, schools, etc., to see if they want a website, or help updating their existing website. If you are just starting out, you could offer to do it for free, just to get the experience. - Following on from the previous item, move on to doing some small bits of freelance work, once you feel that you are experienced enough to start charging for your work. You could ask around at local businesses, or find work through dedicated services such as [UpWork](https://www.upwork.com/). - Build your own hobby projects, for example, your own website to showcase your work, some fun demos and games, or a web app related to a company that you want to work at. - You could even identify an existing issue on a company's website, fix it, and send them a link to a deployed version of the fix. - Hang around on forums and help others with their coding problems. (See [Help others, ask questions](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork#help_others_ask_questions).) - [Contribute to open source projects](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork#participate_in_open_source). ### Create a portfolio Once you've got some relevant experience worth talking about, the next step is to put together a portfolio that you can include on your CV/résumé, and show to employers. The whole idea here is _don't tell me what you can do show me what you can do_. This should include: - A nicely-presented landing page to summarize everything. - A list of websites, apps, and other relevant products that you've worked on. For each one, include a title, thumbnail image, and a description of the project and what you did on it. If you want to say quite a lot about the projects, create a separate page about each one! Include a list of the technologies used to build the project and a link to the source code if you are able to (not all source is open). - Links to the source code and live demos of your own personal projects and experiments. Each project should include a description of what it does and the technologies used to build it. - Links to blog posts, writing samples, conference talks, and any other assets you think might help show off your skills. - A list of contributions you've made to open source projects, stating what the contribution was, how it improved the project, and what technologies were involved. You should include links to the actual changes you made (source code, published documentation, etc.), and a link to the changelog (for example, the GitHub pull request) that proves you did the work. Keep your portfolio as highlights of your best work. You want it to be easy to access and look through. Include a few significant items in each section, rather than hundreds of items, otherwise the hiring manager will get bored and move on. A nice clean design will help with the experience, but a complex design might reduce legibility. In terms of how to actually publish your portfolio: - You should [publish your work](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork#publish_your_work) in publicly-viewable places, such as a GitHub organization for source code, and a blogging platform such as medium (or your own website) for blog posts. - For source code that is demos or snippets rather than full websites, publish live versions of the code, so that people can easily check out the live result. See [Publishing your website](/en-US/docs/Learn_web_development/Getting_started/Your_first_website/Publishing_your_website) for tips on how to do this. - In terms of the actual portfolio landing page, you could create a GitHub repo and provide your list of links in a `README.md` file, but this doesn't look particularly great. Much better would be to create a dedicated website to contain your portfolio, with a dedicated [domain name](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_domain_name). If you are not great at design work, ask a colleague or friend for some help, perhaps in exchange for doing some coding work for them. ### Create a LinkedIn profile [LinkedIn](https://www.linkedin.com/) is one of the most popular sites in the world for people to find jobs, and for employers to find employees. It is a great place to host an online account of your work experience and skills, which is similar to your CV/résumé, but also different. LinkedIn has many useful advantages that allow your profile to work well alongside your CV/résumé. You can: - List your full set of experience and skills on there without making your profile look too long. - Get people to post recommendations. - Link to the profiles of all your past employers, educational institutions, etc. - Write posts on it and use it as a blog. - Actively search for and apply for jobs on it, and get it to send you new job updates based on your skills and interests. - Connect with contacts to build a network that you can share skills and leads with, which will ultimately help you find jobs. - Take training courses. For these reasons, you should create a profile. Make it look good and sound professional. [Create a good LinkedIn profile](https://www.linkedin.com/help/linkedin/answer/a554351/how-do-i-create-a-good-linkedin-profile-?lang=en) provides some useful tips. ## Applying for a job Let's now look at the process of applying for a job. ### Job searches There are different ways to approach job searches, and a combination often works best: - Sign up to sites where jobs are advertised, such as LinkedIn (there will also be specific sites for your locale) and choose to receive regular digests of the jobs that are available. - Go to your [community networks](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork#join_a_community_network), look for job ads, and ask contacts if they know of any jobs you might be interested in. - Research the companies you'd like to work for, go to their job pages, and see what's available. - Attend networking events and job fairs to meet potential employers. The ["Decide what kind of job you want" criteria](#decide_what_kind_of_job_you_want) that you created earlier should be useful for narrowing down your lists of what companies you want to work for and what kind of jobs you want to apply for. ### Before applying Before you start applying for a job, there are some useful steps you can take to prepare: - Check the job description to make sure it is suitable for you to apply for. For example, you don't want to waste your time applying for a job that specifies working from an office in California full-time if you live in the UK and don't want to relocate. And you won't have the experience to do a senior web developer job if you have only been learning JavaScript for 3 months. > [!NOTE] > If you are not sure if you should apply for a job, or want to know if that company has any more suitable jobs available, find the recruiter's contact details and ask them. They may be a useful contact in the future, if not now. - Try to find some contacts that already work at the company, for example in your community network, or on LinkedIn, and start a conversation with them. Ask them what it is like working for the company, if there is anything specific you need to do the job, and if they have any other jobs coming up in the near future that might be more suitable. Avoid contacting too many people that work at the same company, but instead choose one or two people you think are the closest to the role you're applying for. - Do some other research on the company read their websites/blogs, find out about their history and what their goals are. Spend some time using their products and becoming familiar with how they work for example install their software and use it to build a demo app, or write a website that uses their APIs to do something. > [!NOTE] > For bonus points, try to identify a problem they are currently trying to solve or a bug in their product, and suggest a fix for it along with a demo or code snippets. - Customize your CV/résumé to suit this particular job. Look at the job description and make sure to amplify your most relevant skills and work experience, while minimizing or even removing items that don't seem relevant. Also make sure the demos/examples most relevant to this job are highlighted on your portfolio in some way, for example put at the top of the list. ### Applying for the job When you apply for the job, make sure you follow the process exactly as they want you to. Each job advert will have instructions on how to apply. If the job ad was more of a conversation with a network contact, ask them exactly how to apply. Above all: - Make sure your application is professional and well-written, and doesn't include any typos. If it is poorly-written, you are unlikely to succeed. Check it over using a tool like Grammarly. - Don't be late applying make sure you submit your application by the deadline date! Not all job applications require the same things; you'll probably have to: - Fill out some details on an online form. Make sure you type out your answers locally (for example, in VSCode) or in an online document (for example, a Google doc) before entering them into the form fields, in case something (for example, a browser crash) causes you to lose your work. This can save hours of frustration, and text is also easier to review and get feedback on if you put it somewhere else first. - Upload your CV/résumé. - Write and upload a covering letter. There are many guides to writing effective covering letters elsewhere online, but in general, such a letter should provide a description of why you: - Want to work for the company. - Would be a good culture fit. - Are perfectly suited to doing the job. - Provide some examples of your previous work in a separate section. ### Constant process A key point is that your job search should be a constant process where you do a little bit each day, and constantly keep working towards your goal. Don't just fire off a whole load of applications and then sit around waiting for a response. You should follow up with any promising leads you meet, for example on community networks or LinkedIn. Also, keep an inventory of the people you meet and the companies you apply to, along with contact details and descriptions of each. Periodically follow up with companies you'd like to work for to see what jobs they have available. If you get rejected for one job, they might have something else more suitable for you, either now or in the future. If you haven't had a response from a company after applying for one of their jobs, wait a little while and then followup with their recruiter to see if they have any feedback for you, or any other jobs coming up. Even if a company you want to work for doesn't have any suitable full-time jobs advertised at the moment, contact them any way and see if they are looking for someone with your skill set. It might lead to some useful experience, such as a freelance contract. Keep looking for more jobs via your chosen job sites or community networks, and chat to people about what is available. New opportunities will come up all the time. If you are struggling to find anything permanent that you really want, there might be some intermediate steps to take: - Sometimes a good strategy is to get a job adjacent to the one you want. You might have to get a lower-ranking job to begin with, to get the required experience for the job you really want, either in terms of the specific technology stack used, or the particular industry you want to work in. - You might have to get some freelance/contract work to keep paying the rent while you continue your job search. This will also give you useful experience that will help you get the job you are after. ## Preparing yourself for job interviews If the recruiter and hiring manager like your application, they will invite you to some interviews and other sessions. This section provides some advice on preparing for those. There are some general things you should do to prepare for _any_ interview: - Make sure you have a tidy, quiet place for conducting the interviews in. You want to make sure that you are not distracted by noise and commotion during the interview, and you want to make sure that your surroundings look tidy during the interview. If you are having a video call with the interviewer(s) and your space is a complete mess, it won't give them a good impression. - Think about what you are going to wear when you do your interviews. Most developer jobs don't require you to wear a suit (check the job ad to see if it has anything about a dress code), but you should choose something fairly neutral and smart-_ish_. Don't wear a t-shirt with an offensive slogan on it, and don't turn up in your pyjamas. - Do some [preparation for coding interviews](#coding_interviews). - Build a collection of anecdotes to use for [experience-based interview questions](#experience-based_questions). - Think about what your strengths and weaknesses are, and be realistic with your answers. You will seem more plausible if you don't present yourself as perfect in every way, and interviewers will commonly ask you about your biggest strength and weakness. - Do some research into what the typical salary ranges and other benefits (such as health insurance or bonuses) are for the kind of job you are going for (level, geographical region, etc.) This will be useful when [deciding whether to accept a job offer](#accepting_the_offer). There is also some preparation to do specific to each company you apply to: - As we mentioned earlier, you should do some research into the company's history, goals, and products. - Read about the company from a high level, and familiarize yourself with their recent news. One good reason to do this is to see if they have done anything that would raise red flags and make you reconsider working there. - Find out about the company's market position and what _their_ strengths and weaknesses are. - Research who their main competitors are. - Become familiar with their products and how they work for example, install their software and use it to build a demo app, or write a website that uses their APIs to do something. It will impress your interviewers if you can demonstrate some good insights around their situation and how you think it could be improved. Interviewers will often ask some specific questions to test your knowledge of the company and industry it exists in. - Related to the above point, think of some questions you would ask the interviewers about the company and their roles. Interviewers always ask if you have any questions for them at the end of the interview, and it reflects well on you and makes you sound thoughtful and inquisitive if you come out with some questions more meaningful than "what did you have for breakfast today?" - If you have contacts at the company (from your community networks, etc.), reach out to them and ask about the job. What do they think about it, is there anything specific you should focus on, say, or not say? It might also help, during the interview, to mention that you know "Bob from the e-commerce team" (or whatever). ### Initial screening Once the company have looked at your application and decided you sound like a "potential", they will probably start off by inviting you to an initial phone interview, just with the recruiter or hiring manager. This is usually a short interview to assess "culture fit" (are you going to get on with well with them?), make sure they don't pick up any immediate red flags that suggest you won't be a good employee, and tell you about what to expect from the rest of the interview process. At this stage, you should be friendly and talkative, but don't talk too much, act naturally, and see where the interviewer steers the conversation. Make sure to let them know what days and times will be suitable for you to attend further interviews. Try to be as flexible as you can, and be prepared to move your schedule around to accommodate them. Don't be complacent however you don't know for definite what questions they will throw at you at this early stage, and you should treat it just as seriously as all the other stages. ### The main interview Usually, the bulk of the interview process is one or more main interviews. These can vary greatly between different companies in function and frequency. - You might have one main interview, but you are more likely to have multiple interviews that test you on different aspects of the job (for example, the actual coding work, teamwork, working processes, documentation/communication, conflict resolution). - They may be with a single interviewer, or multiple interviewers asking you questions about different aspects (aka a panel interview). - Some interviews may be high level or more exploratory (for example, "describe the process you'd use to deliver x", or "what do you think the main problems are with this solution?") and some may be lower level and more intensive (for example "write a JavaScript program that solves this problem"). #### Experience-based questions Interviews will inevitably feature experience-based questions. These ask you to provide anecdotes demonstrating that you have experience relevant to the job, and will be along these lines: - "Give me an example of a situation in which you had to deliver some negative feedback to a colleague about their code. How did you handle it?" - "Tell me about a time when you discovered partway through a project that your chosen approach wasn't going to work. How did you address that, and what steps did you take?" - "Fixing minor bugs can often be hard to fit into a team's work process often, the pile gets neglected and builds up. How have you dealt with this on past teams?" You should build up a set of anecdotes that you can use when answering such questions. This will get easier over time as you get more experience. #### Coding interviews Some interviews will be coding interviews, designed to test your ability at solving coding problems. These might be something like: - "Write a JavaScript function that takes inputs x and y, and returns z." - "A colleague has written the following React component, but it has some bugs. Can you identify what's wrong with it, and rewrite it to fix the problems?" - "Look at the following code, and describe to me what it is doing." - "Write an algorithm that generates the first n entries in the fibonacci sequence." Often employers are looking at how you approach the problem rather than the end result how you commented your code, how you dealt with error handling, and what you might have done if you'd had more time. You should research common coding interview question types, practice solving them, and practice explaining what they are doing. You'll find many resources online for practicing coding interview questions. #### Homework questions Some companies will set homework for an interview candidate to do, as an alternative or additional way of testing your coding ability. This tends to be in the form of a mini-project of some kind, which will ask you to code some functionality relevant to the job you are applying for. For example, you might be asked to create a standalone demo that uses the company's JavaScript library to pull data from their APIs and populate a page of information. While this is a reasonable way to test candidates if done right, many people dislike the practice of setting homework questions: - Sometimes they can feel a little too much like "real work", almost like the employer is asking you to do work for free. - They can be poorly scoped, or expect an unreasonable time commitment. A specific scope that time-boxes the project to four hours and will handle follow-up questions in an separate interview is reasonable, but an expectation to spend an entire weekend on a project is not, especially for people who have families and busy lives. - Their effectiveness is often disputed, as they are hard to evaluate conclusively. If you are asked to do a homework challenge, make sure the scope and timing of the work are clearly defined, to mitigate the risk of going in the wrong direction and wasting your time. ### Accepting the offer If you make it through the interview process and get offered the job, then well done! It is tempting to immediately accept the offer, but there are a few things to bear in mind before you do: - Make sure the salary you are being offered is reasonable compared to the typical market rate for similar jobs. If it is significantly less, you might want to negotiate. - Check that leaving your current job (or other activity) is compatible with starting the new job. For example, check how much notice your current employer wants you to work, and make sure this works for the new job. - You should also let the employer know about preexisting commitments you have outside work, such as a booked vacation. Respectable employers will work around such commitments, and agree a start date that suits you. - If you need to move house/relocate to take the new job, make sure that you've got everything in place for the move to work out. Starting a new job is stressful enough without the added stress of house moving difficulties. ## Try, try, and try again If you have applied for lots of jobs and not gotten anything yet, try not to become disheartened. Keep trying, keep passionate about the web, and keep talking to new people and learning new things. If you got to the interview stage but got rejected, ask the recruiter for some feedback on why you weren't chosen, and use it to improve future applications. Be patient. Even the best candidates will get rejections from multiple job applications before they land the job they want. You will get there. ## See also - [Getting hired](https://scrimba.com/the-frontend-developer-career-path-c0j/~0156?via=mdn), Scrimba <sup>Course Partner</sup> - [Technical Interviewing 101: Ultimate Guide to Acing Your Tech Interview](https://learntocodewith.me/posts/technical-interview/), learntocodewith.me (2022) - [30 Technical Interview Questions and Tips for Answering](https://www.coursera.org/articles/technical-interview-questions), Coursera (2023) - [How to land your first developer job](/en-US/blog/how-to-land-your-first-developer-job), MDN Blog, 2024 {{PreviousMenuNext("Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes", "Learn_web_development/Core", "Learn_web_development/Getting_started/Soft_skills")}}