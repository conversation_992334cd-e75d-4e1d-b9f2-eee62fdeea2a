Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > index.md --- title: Soft skills slug: Learn_web_development/Getting_started/Soft_skills page-type: landing-page sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Getting_started/Web_standards/How_browsers_load_websites", "Learn_web_development/Getting_started/Soft_skills/Research_and_learning", "Learn_web_development/Getting_started")}} This module provides recommendations of soft skills you can aim to get better at while learning web development, and which constitute good traits to have when entering the industry. They will help immensely in developing the right attitudes for learning, researching, and collaborating, and increase the chances of getting hired. ## Prerequisites This module is mainly theoretical, and assumes no specific practical knowledge of web technologies. However, we would still recommend that you work through the preceding modules in order before tackling this one. ## Tutorials - [Research and learning](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Research_and_learning) - : There is a lot to know as a web developer, and you are constantly learning. But how do you find out what you need to know most effectively? This article provides useful tips on research and learning. - [Collaboration and teamwork](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork) - : As a professional in the web industry, you are going to have to work with other people on projects during multiple phases brainstorming ideas, writing proposals, implementing code, and more. This article looks at how to work together with others. - [Workflows and processes](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Workflows_and_processes) - : Beginners often focus on specific individual aspects of technology, but miss out on the bigger picture of an overall project. This article provides some of that high-level context. - [Finding a job](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Finding_a_job) - : Technical job interviews can be very demanding, and some have quite specific requirements. Finding the right jobs to apply for in the first place can be challenging! This article provides some tips and resources to help with the process from finding potential jobs and applying, to succeeding in interviews. {{PreviousMenuNext("Learn_web_development/Getting_started/Web_standards/How_browsers_load_websites", "Learn_web_development/Getting_started/Soft_skills/Research_and_learning", "Learn_web_development/Getting_started")}}