Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > research_and_learning > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > research_and_learning > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > research_and_learning > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > research_and_learning > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > soft_skills > research_and_learning > index.md --- title: Research and learning slug: Learn_web_development/Getting_started/Soft_skills/Research_and_learning page-type: tutorial-chapter sidebar: learnsidebar --- {{NextMenu("Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork", "Learn_web_development/Getting_started/Soft_skills")}} There is a lot to know as a web developer, and you are constantly learning. But how do you find out what you need to know most effectively? This article provides useful tips on research and learning. <table> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> N/A </td> </tr> <tr> <th scope="row">Learning outcomes:</th> <td> <ul> <li>Effective research methodologies.</li> <li>Making a learning plan.</li> <li>Staying motivated.</li> <li>Rubber ducking</li> <li>Effective problem solving.</li> <li>Getting into the mindset of constant learning.</li> </ul> </td> </tr> </tbody> </table> ## Effective research As a web developer, you will spend a lot of time searching for solutions to problems encountered in your work you aren't expected to remember everything! Professional web developers use tools like MDN Web Docs to look up things they have forgotten all the time. As you'll discover, learning web development is more about problem-solving and learning patterns than it is about learning lots of syntaxes. To facilitate this and become more self-sufficient, you should learn effective strategies for finding answers as discussed in the sections below and when to use which strategy. For example, look up information online first, and only ask your mentor, classmates, or senior dev for help when you are truly stuck. ### Choose high-quality resources You should make sure that the resources you choose for research are high quality and reputable. To find out which resources are reputable, ask your teacher or more experienced colleagues what they'd recommend, and do some web searches to find out what other folks around the web recommend. MDN of course comes recommended; other good examples include: - [Stack Overflow](https://stackoverflow.com/) - [Scrimba](https://scrimba.com/?via=mdn) <sup>Course partner</sup> - [YouTube](https://www.youtube.com/) - [freeCodeCamp](https://www.freecodecamp.org/) - [Codecademy](https://www.codecademy.com/) - [Dev.to](https://dev.to/) ### Learn what resources to use and when Documentation comes in different types, and it is useful to know when each one is useful. The terminology will vary between sites, and there is often some degree of overlap, but in general the common types are: - **References**: Similar to how human languages have dictionaries that list the meaning of every word, programming languages have references that try to exhaustively cover the purpose and syntax of every possible feature that developers will use, often along with usage examples. References focus on _what_ a feature does rather than _how_ to use it. - **Tutorials**: Allow developers to learn _how_ to use new technologies. Tutorials are often step-by-step and aimed at beginners. - **Guides**/**How-tos**: Also associated with learning, but tend to be less beginner-focused, and are often aimed at solving specific problems. - **Explainers**/**Conceptual**: Tend to focus on explaining the high-level concepts and use cases behind a specific technology, rather than getting down to the low-level code details. - **Q&A**/**forums**: Sites where people ask questions and others provide answers. You should consult multiple different sites, see which documentation styles are prevalent on each one, and think about which ones are useful in different situations. For example: - MDN is great for looking up reference documentation about different HTML, CSS, and JavaScript features and also contains useful guides and tutorials for you to learn new techniques from. - Stack Overflow is a Q&A-style site, where people help each other find solutions to specific problems. - Scrimba and Codecademy provide step-by-step interactive tutorials. YouTube is also a great place to find video tutorials. Above all, work out which learning approaches work best for you. Different people learn in different ways. Realistically, you'll probably end up using multiple resources for research and learning. ### Search effectively If you can't find a solution to your problem on your favorite sites, you'll need to widen your search to the rest of the web. We covered general tips for effective web searches earlier in the course (see [Searching for information](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Browsing_the_web#searching_for_information)); this section provides a few specific hints and tips related to looking up good-quality information. - When you have a problem with using a specific tool or product, consult the official documentation first. It is surprising how many people do not think to do this; official docs are often geared towards beginners, so people with experience may not think of them as being suitable. If the product you are using is the web platform, the _unofficial_ official documentation is MDN, so you are in the right place! - Check the dates on the resources you find. Are they recently maintained, or not updated for some time and, therefore, possibly out-of-date? Reputable sites tend to include publication and "last-updated" dates on them, precisely so you can easily check this. Ideally, an article from a reputable site covering a modern technology or technique shouldn't be left unmaintained for longer than a few years (say 2 3). It is perfectly possible that older documentation covering a mature technology (for example, fundamental HTML or CSS features) can still be accurate, but you should keep this in mind. - As a follow-on from the previous point, even newly-published resources can contain out-of-date information. For example, does an article on CSS page layout talk about modern approaches like grid and flexbox, or does it still present obsolete techniques like using floats to create multi-column layouts? - Don't just rely on one single source of truth. Cross-reference different sources, especially if you are not sure whether a source is trustworthy or not. - You can also try using AI to help with coding issues (for example [ChatGPT](https://openai.com/index/chatgpt) or [GitHub Copilot](https://github.com/features/copilot/copilot-business)). You should use AI tools with caution (especially as a beginner), and familiarize yourself with their strengths and weaknesses: - On the plus side, they can speed up research/searches enormously, and help with structuring code and copy. - On the other hand, AI tools have no reasoning skills and frequently provide answers that are misleading or just plain wrong. You shouldn't just assume that AI answers are correct test them, and verify them with other sources. ### Aside on copying and pasting code Whether generated by an AI or not, copying, pasting, and using code you didn't write and don't understand has always been cautioned against, regardless of your skill level. It makes bugs harder to fix and there may be security, performance, or accessibility problems with the code, often resulting from the context or manner in which you are using it. As a beginner, just copying and pasting a solution means that you miss out on the learning opportunity of doing something yourself, which is key to memorizing skills or concepts. You can also run into licensing issues, if the code is copyrighted and you don't have permission to use it. The advice here is that if you are going to use a solution from somewhere else, at least try to understand how it works first before copying and pasting it. Ideally, learn from the solution and then write your own version. ### Build a network of contacts We've talked enough about websites, so now we'll talk about people. And websites. Building a network of folks to collaborate with is an essential part of being a web developer. We'll also talk about this in [Collaboration and teamwork](/en-US/docs/Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork#join_a_community_network); here we'll concentrate specifically on using your network to help with research. An effective research network consists of several groups of people with different levels of knowledge and proximity to you: - Peers: It is useful to share information with and ask questions of your immediate peers this could be your class mates or your team mates as they might have useful insights to share or want to work on the problem together. They might also have worked on the same problem previously and be able to provide an answer. - Teachers or senior colleagues: If your peers can't help, the next step is normally to ask a senior colleague, manager, or teacher. They will have more experience than your peers, but bear in mind that they will have less free time available. You will get better results and more appreciation if you demonstrate that you've already tried to solve the problem yourself first, and are being respectful of their time. To do so: - Keep a record of what you've already tried to do. What already failed? - If the research subject is a specific code example or technique that you can't get to work, create an online test case to show to them using a site like [Scrimba](https://scrimba.com/?via=mdn) <sup>Course partner</sup> or [Glitch](https://glitch.com/). - Think carefully about what questions to ask them what specifically is the problem? If you have multiple problems to ask them about, put them in order of priority to make sure you cover the most important items. - Set a goal for the session, for example, "try to find a solution to the highest priority problem". Note also that solving the biggest problem may also lead to a fix for other problems. - Ask them how they would prefer to communicate with you about the problem, to make the interaction as comfortable for them as possible. - If arranging a meeting, don't book more of their time than you need. If you are not sure how long you will need, timebox the session to say 30 minutes. You can always ask them for more help in the future. - Wider community network: This could consist of a dedicated online community such as forums or chat groups (for example the [The MDN Web Docs discord](/discord) or the [freeCodeCamp forums](https://forum.freecodecamp.org/)), or a physical meetup such as a conference or skill-share event. - Global network: Sometimes you might be out of options, in which case you could try asking your question on a general global community like a social media network. Sometimes helpful answers can come from unexpected places. > [!NOTE] > There is definitely a balance to knowing the right time to ask for help. Web developers shouldn't constantly pester their peers/colleagues, but equally, they shouldn't soldier on and pretend they know what they are doing when they don't. Consider the value of saying "I don't know" at the right time. Most importantly, make sure you are respectful and kind at all times, to all the people you interact with. You should also learn to give back as well as take; web developers who reciprocate and provide help are much more likely to build strong relationships and keep getting help. ## Make a learning plan It is a good idea to create a plan to help you learn what you want to learn. ### Start with a goal statement It might sound silly, but you should start with a single sentence that says what you want to achieve. You should be ambitious but also realistic. The following have different scopes, but are all realistic and achievable: - I want to become a professional web developer in two years' time. - I want to learn enough to build a website for my local amateur tennis club. - I want to learn HTML and CSS so I can expand my job role to take over updating the content on our company website. The following are not quite as realistic: - I want to go from a complete beginner to becoming a senior web developer in three months. - I want to start my own company and build a social network that will out-perform Facebook, in two years. ### Define what you'll need to get there Once you've worked out your goal, it is a good idea to research what you'll need to achieve the goal. For example: - Materials I need: - A computer - Internet access - Pens and paper - Knowledge I need: - How to use HTML, CSS, JavaScript, and associated tools and best practices to build websites and web applications (we can definitely help you with this one!). - How to get a domain, hosting, and use them to put a website or application online. - How to run a small business. - How to advertise my business and attract clients. ### Estimate how much time you'll need Next, you should start writing out a time plan to achieve your goal. It can be as simple as: "It'll take me about 500 hours to learn what I need to know, and I have a year to do it. If I assume 2 weeks of holiday, I'll need to work on this for 10 hours per week. I am free on evenings and weekends, so I'll plan my time around those." Don't agonize over how accurate the plan is. A rough estimate will do it is more important to get something written down to work towards. How much time you can spend on this of course depends on what your circumstances are. If you are at school, then you've got way more free time than if you have a job and children to provide for. It is still possible to achieve your goals, but you have to be realistic about how quickly you can do it. If you are doing a university or college course to learn web development, then most of this planning is done for you lucky you! It is a good idea to write down some sub-goals below your main goal, to allow you to keep track of where you are more easily. For example: - HTML and CSS basics learned by summer - JavaScript basics learned by December - Example website project built by next April - etc. If you are not sure what items to put on your goals list, our [curriculum](/en-US/curriculum/) can help. When you have worked out a weekly schedule then you should keep a record of what you manage to do each week in a simple spreadsheet or even in a notebook! Keep thinking about how much progress you are making, and adjust your plan if required. ## How to stay motivated It is hard to stay motivated, especially if you are trying to learn a complex skill like programming or web development. What follows are some tips to stay motivated and keep learning: - **Try to make your work environment as productive as possible**. Get a comfortable desk and chair to work in, make sure you have enough light to see what you are doing, and try to include things that help you concentrate (for example, mellow music, fragrances, whatever else you need). Don't try to work in a room with distractions for example a television on, with your friends watching football! Also, leave your mobile phone out of the room most people are distracted by their phone a lot, so you should leave it somewhere else. - **Eat, exercise, and sleep**. Eat healthily, get regular exercise, and make sure you get enough sleep. This sounds obvious, but it is easy to forget when you get really into coding. Factor these essential ingredients into your schedule, and make sure you are not scheduling more learning time instead of these things. - **Give yourself rewards**. Schedule fun things to do after each learning session, which you'll only have when the learning is over and complete. If you are really into gaming, for example, there is something quite motivating about saying "no gaming tonight unless I get through my 3 hours of learning". Now all you need is willpower. Good luck! - **Co-learning and demoing**. This won't be an option for everyone, but if at all possible try to learn alongside others. Again, this is easier if you are doing a college course on web development, but perhaps you could convince a friend to learn along with you, or someone at your local meetup? It is really useful and motivating to have someone to discuss ideas with and ask for help, and you should also take time to demo your work. Those shouts of appreciation will spur you on. ## Taking breaks, and rubber ducking If you are having trouble figuring out the answer to a question or problem, or concentrating on your learning, it is useful to have a break. Go for a walk for 15 minutes, make yourself a cup of tea, or listen to some music. Or combine all three! It is also physically bad to work for too long without a break; looking at a monitor for too long can hurt your eyes, and sitting still for too long can be bad for your back or legs. You can lower the risk of eye strain with the "20-20-20" rule; every 20 minutes, look at something 20 meters away for 20 seconds. We also recommend taking a proper 15-minute break every hour to 90 minutes. While you are on your break, try discussing your issue with a colleague, friend, or family member. Even if they can't provide you with the answer, the chances are that you'll find a solution on your own by reasoning the problem in your own head. It even works when you explain the problem out loud, to yourself or to an inanimate object. This technique is known as [rubber duck debugging](https://rubberduckdebugging.com/) (or [rubber ducking](https://en.wikipedia.org/wiki/Rubber_duck_debugging)), named because traditionally the idea was that you explain your coding problem to a rubber duck. ## Effective problem-solving There is no one effective way to solve all problems (and learn all things) associated with web development, but there is some general advice that will serve you well in most cases. ### Break problems down into chunks When you are trying to implement something specific and it seems really hard to get your head around, you should try to break it down into multiple smaller problems or chunks. For example, if you are looking at a task of "Build a simple two-column website", you could break it down as follows: - Create the HTML structure. - Work out basic site typography. - Work out a basic color scheme. - Implement a high-level layout header, horizontal navigation menu, main content area with main and side columns, and footer. - Implement a horizontal navigation menu. - etc. Then you could break it down further. For example, "Implement a horizontal navigation menu" could be written out as: - Mark up a list of menu items. - Lay out the menu items horizontally in a line. - Remove unneeded defaults, like list spacing and bullet points. - Style hover/focus/active states of menu items appropriately. - Evenly space the menu items. - Give the menu items enough vertical spacing. - Make sure the text is centered inside each menu item. - etc. Each of these problems doesn't seem nearly as difficult to solve as the one big problem you had initially. ### Learn to recognize patterns Web development is mostly about problem-solving and patterns. Once you have written out what you'll need to do to solve a specific problem, you can start to figure out what technology features to use to solve it. For example, professional web developers have created lots of horizontal navigation menus, so they'll immediately start thinking of a solution like this: A nav menu is usually created from a list of links, something like: ```html <ul> <li><a href="">First menu item</a></li> <li><a href="">Second menu item</a></li> <li><a href="">Third menu item</a></li> <li><a href="">etc.</a></li> </ul> ``` To make all the items sit horizontally on a line, the easiest modern way is to use flexbox: ```css ul { display: flex; } ``` To remove unneeded spacing and bullet points, we can do this: ```css ul { list-style-type: none; padding: 0; } ``` etc. How much research you need to do to find the solution will depend on how experienced you are, and if you've implemented something similar previously. You may recognize the pattern from a previous project, and only have to look up a few bits of the syntax that you forgot since then. When you find solutions to such problems, it is worth writing down notes on what you did, and keeping some minimal code examples in a directory somewhere so you can look back on previous work. ### Look at other people's code The web has [developer tools](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/What_are_browser_developer_tools) that allow you to look at the code used to build any site. If you don't have a solution to hand, one good research method is to find websites with similar features in the wild, and look at how they did it. ## Constant learning mindset You should get into the mindset of constant learning. The web is constantly evolving and technologies and trends are always changing, and you need to constantly update your knowledge to keep up. You will never learn everything! To make things easier for yourself: - Be curious. If you see a website feature that looks cool, interesting, and/or you don't understand it, do some research to find out how it's done. It might form the basis of your next research project or learning task. - You should engage in reading tasks or small research projects regularly. Set aside specific learning time for acquiring new skills. - Get into the habit of regularly reading technical news, blogs, and browser release notes to see what new things are coming up for you to learn about. - Keep practicing. The more you practice solving a problem, the stronger your brain's neural pathways are in that area, the easier it becomes to recall the details and the logic of that particular problem, and the more comfortable you will be when learning about similar solutions in the future. {{NextMenu("Learn_web_development/Getting_started/Soft_skills/Collaboration_and_teamwork", "Learn_web_development/Getting_started/Soft_skills")}}