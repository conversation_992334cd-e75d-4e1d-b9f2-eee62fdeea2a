Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > web_standards > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > web_standards > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > web_standards > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > web_standards > index.md Path: mdn-web-docs > files > en-us > learn_web_development > getting_started > web_standards > index.md --- title: Web standards slug: Learn_web_development/Getting_started/Web_standards page-type: landing-page sidebar: learnsidebar --- {{PreviousMenuNext("Learn_web_development/Getting_started/Your_first_website/Publishing_your_website", "Learn_web_development/Getting_started/Web_standards/How_the_web_works", "Learn_web_development/Getting_started")}} This module covers the fundamentals of how the web works at a high level including the model used for communication, the core technologies involved, how those technologies are created, and how a web browser renders and displays websites to a user. General resources: - [Resilient Web Design](https://resilientwebdesign.com/), <PERSON> ## Prerequisites This module is mainly theoretical, and assumes no specific practical knowledge of web technologies. However, you will understand it better and get more out of it if you understand the basics of what the main web technologies are used for. We'd suggest that you work through the [Your first website](/en-US/docs/Learn_web_development/Getting_started/Your_first_website) module first, if you have not done so already. ## Tutorials - [How the web works](/en-US/docs/Learn_web_development/Getting_started/Web_standards/How_the_web_works) - : This article provides a high-level description of what happens when you use a web browser to navigate to a web page, explaining the magic that goes on behind the scenes to deliver the relevant code to your computer for the browser to assemble into something you can look at. - [The web standards model](/en-US/docs/Learn_web_development/Getting_started/Web_standards/The_web_standards_model) - : This article provides some useful background on the web and web standards how they came about, what web standard technologies are, and how they work together. - [How browsers load websites](/en-US/docs/Learn_web_development/Getting_started/Web_standards/How_browsers_load_websites) - : In this article, we talk through the rendering process of a website when a browser has received the files and assets that make up a website, how are they put together to create the finished experience that the user interacts with? {{PreviousMenuNext("Learn_web_development/Getting_started/Your_first_website/Publishing_your_website", "Learn_web_development/Getting_started/Web_standards/How_the_web_works", "Learn_web_development/Getting_started")}}