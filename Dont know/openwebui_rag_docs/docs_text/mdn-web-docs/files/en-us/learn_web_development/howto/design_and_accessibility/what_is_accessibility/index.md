Path: mdn-web-docs > files > en-us > learn_web_development > howto > design_and_accessibility > what_is_accessibility > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > howto > design_and_accessibility > what_is_accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > design_and_accessibility > what_is_accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > design_and_accessibility > what_is_accessibility > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > design_and_accessibility > what_is_accessibility > index.md --- title: What is accessibility? slug: Learn_web_development/Howto/Design_and_accessibility/What_is_accessibility page-type: learn-faq sidebar: learn-how-to --- This article introduces the basic concepts behind web accessibility. <table class="standard-table"> <tbody> <tr> <th scope="row">Prerequisites:</th> <td>None.</td> </tr> <tr> <th scope="row">Objective:</th> <td>Learn what accessibility is and why it matters.</td> </tr> </tbody> </table> ## Summary Because of physical or technical limitations, maybe your visitors can't experience your website the way you hoped. In this article we give general accessibility principles and explain a few rules. ## Active learning _There is no active learning available yet. [Please, consider contributing](/en-US/docs/MDN/Community/Getting_started)._ ## Dig deeper ### Accessibility: general principles We might associate accessibility at first with negative limitations. This building has to be accessible, so it must follow these regulations for door width and toilet size and elevator placement. That's a narrow way to think of accessibility. Think of it as a wonderful way to empower people and serve more customers. What can the people in Brazil do with your English website? Can the people with smartphones browse a heavy, cluttered website designed for a large desktop monitor and unlimited bandwidth? They'll go somewhere else. In general, _we must think about our product from the viewpoints of all our target customers, and adapt accordingly._ Hence accessibility. ### Web accessibility In the specific context of the web, accessibility means that anyone can benefit from your content, regardless of disability, location, technical limitations, or other circumstances. Let's consider video: - Hearing impairment - : How does a hearing-impaired person benefit from a video? You have to provide subtitles or even better, a full text transcript. Also, make sure people can adjust the volume to accommodate their unique needs. - Visual impairment - : Again, provide a text transcript that a user can consult without needing to play the video, and an audio-description (an off-screen voice that describes what is happening in the video). - Pausing capacity - : Users may have trouble understanding someone in a video. Let them pause the video to read the subtitles or process the information. - Keyboard capacity - : Let the user tab into/out of a video, play it, and pause it without being trapped in it. #### The basics of Web accessibility A few necessities for basic Web accessibility include: - Whenever your site needs an image to convey meaning, include text as an alternative for visually-challenged users or those with slow connections. - Make sure all users can operate graphical interfaces (like unfolding menus) solely with a keyboard (e.g., with Tab and the Return key). - Provide an attribute explicitly specifying your content's language, so that screen readers read your text properly. - Make sure that a user can navigate to all widgets on a page solely with the keyboard, without getting trapped. (At least let them Tab in and out.) And that's just the beginning. ### Accessibility champions Since 1999, the {{Glossary("W3C")}} has operated a working group called the {{Glossary("WAI","Web Accessibility Initiative")}} (WAI) promoting accessibility through guidelines, support material, and international resources. ## More details Please refer to: - [Wikipedia article](https://en.wikipedia.org/wiki/Accessibility) about accessibility - [WAI (W3C's Web Accessibility Initiative)](https://www.w3.org/WAI/) ## Next steps Accessibility can impact both a website's design and technical structure. - From a design point of view, we suggest learning about [designing for all types of users](/en-US/docs/Learn_web_development/Howto/Design_and_accessibility/Design_for_all_types_of_users). - If the technical side interests you more, you could learn how to [embed images in webpages](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_images).