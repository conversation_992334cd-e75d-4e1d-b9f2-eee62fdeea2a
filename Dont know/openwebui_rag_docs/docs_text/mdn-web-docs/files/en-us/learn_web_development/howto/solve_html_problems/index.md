Path: mdn-web-docs > files > en-us > learn_web_development > howto > solve_html_problems > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > howto > solve_html_problems > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > solve_html_problems > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > solve_html_problems > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > solve_html_problems > index.md --- title: Solve common HTML problems short-title: Common HTML problems slug: Learn_web_development/Howto/Solve_HTML_problems page-type: landing-page sidebar: learn-how-to --- The following links point to solutions to common everyday problems you'll need to solve with HTML. ### Basic structure The most basic application of HTML is document structure. If you're new to HTML you should start with this. - [How to create a basic HTML document](/en-US/docs/Learn_web_development/Core/Structuring_content/Basic_HTML_syntax#anatomy_of_an_html_document) - [How to divide a webpage into logical sections](/en-US/docs/Learn_web_development/Core/Structuring_content/Structuring_documents) - [How to set up a proper structure of headings and paragraphs](/en-US/docs/Learn_web_development/Core/Structuring_content/Headings_and_paragraphs) ### Basic text-level semantics HTML specializes in providing semantic information for a document, so HTML answers many questions you might have about how to get your message across best in your document. - [How to create a list of items with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content/Lists) - [How to stress or emphasize content](/en-US/docs/Learn_web_development/Core/Structuring_content/Emphasis_and_importance) - [How to indicate that text is important](/en-US/docs/Learn_web_development/Core/Structuring_content/Emphasis_and_importance) - [How to display computer code with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#representing_computer_code) - [How to annotate images and graphics](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_images#annotating_images_with_figures_and_figure_captions) - [How to mark abbreviations and make them understandable](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#abbreviations) - [How to add quotations and citations to web pages](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#quotations) - [How to define terms with HTML](/en-US/docs/Web/HTML/How_to/Define_terms_with_HTML) ### Hyperlinks One of the main reasons for HTML is making navigation easy with {{Glossary("hyperlink", "hyperlinks")}}, which can be used in many different ways: - [How to create a link](/en-US/docs/Learn_web_development/Core/Structuring_content/Creating_links) - [How to create a table of contents with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content/Creating_links#active_learning_creating_a_navigation_menu) ### Images & multimedia - [How to add images to a webpage](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_images#how_do_we_put_an_image_on_a_webpage) - [How to add video content to a webpage](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_video_and_audio) ### Scripting & styling HTML only sets up document structure. To solve presentation issues, use {{glossary("CSS")}}, or use scripting to make your page interactive. - [How to use CSS within a webpage](/en-US/docs/Learn_web_development/Core/Styling_basics/Getting_started#adding_css_to_our_document) - [How to use JavaScript within a webpage](/en-US/docs/Web/HTML/How_to/Add_JavaScript_to_your_web_page) ### Embedded content - [How to embed a webpage within another webpage](/en-US/docs/Learn_web_development/Core/Structuring_content/General_embedding_technologies) ## Uncommon or advanced problems Beyond the basics, HTML is very rich and offers advanced features for solving complex problems. These articles help you tackle the less common use cases you may face: ### Forms Forms are a complex HTML structure made to send data from a webpage to a web server. We encourage you to go over our [full dedicated guide](/en-US/docs/Learn_web_development/Extensions/Forms). Here is where you should start: - [How to create a simple Web form](/en-US/docs/Learn_web_development/Extensions/Forms/Your_first_form) - [How to structure a Web form](/en-US/docs/Learn_web_development/Extensions/Forms/How_to_structure_a_web_form) ### Tabular information Some information, called tabular data, needs to be organized into tables with columns and rows. It's one of the most complex HTML structures, and mastering it is not easy: - [How to create a data table](/en-US/docs/Learn_web_development/Core/Structuring_content/HTML_table_basics) - [How to make HTML tables accessible](/en-US/docs/Learn_web_development/Core/Structuring_content/Table_accessibility) ### Data representation - How to represent numeric and code values with HTML see [Superscript and Subscript](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#superscript_and_subscript), and [Representing computer code](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#representing_computer_code). - [How to use data attributes](/en-US/docs/Web/HTML/How_to/Use_data_attributes) ### Advanced text semantics - [How to take control of HTML line breaking](/en-US/docs/Web/HTML/Reference/Elements/br) - How to mark changes (added and removed text) see the {{htmlelement("ins")}} and {{htmlelement("del")}} elements. ### Advanced images & multimedia - [How to add a responsive image to a webpage](/en-US/docs/Web/HTML/Guides/Responsive_images) - [How to add vector image to a webpage](/en-US/docs/Learn_web_development/Core/Structuring_content/Including_vector_graphics_in_HTML) - [How to add a hit map on top of an image](/en-US/docs/Web/HTML/How_to/Add_a_hit_map_on_top_of_an_image) ### Internationalization HTML is not monolingual. It provides tools to handle common internationalization issues. - [How to add multiple languages into a single webpage](/en-US/docs/Learn_web_development/Core/Structuring_content/Webpage_metadata#setting_the_primary_language_of_the_document) - [How to display time and date with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content/Advanced_text_features#marking_up_times_and_dates) ### Performance - [How to author fast-loading HTML pages](/en-US/docs/Web/HTML/How_to/Author_fast-loading_HTML_pages) ## See also - [HTML Cheatsheet](/en-US/docs/Web/HTML/Guides/Cheatsheet)