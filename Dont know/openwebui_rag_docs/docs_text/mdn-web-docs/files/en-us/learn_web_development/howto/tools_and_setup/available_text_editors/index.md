Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > available_text_editors > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > available_text_editors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > available_text_editors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > available_text_editors > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > available_text_editors > index.md --- title: What text editors are available? slug: Learn_web_development/Howto/Tools_and_setup/Available_text_editors page-type: learn-faq sidebar: learn-how-to --- In this article we highlight some things to think about when installing a text editor for web development. <table class="standard-table"> <tbody> <tr> <th scope="row">Prerequisites:</th> <td> You should already know about <a href="/en-US/docs/Learn_web_development/Howto/Tools_and_setup/What_software_do_I_need" > various software you need to build a website</a >. </td> </tr> <tr> <th scope="row">Objective:</th> <td> Learn how to choose a text editor that best suits your needs as a web developer. </td> </tr> </tbody> </table> ## Summary A website consists mostly of text files, so for a fun, pleasant development experience you should choose your text editor wisely. The sheer number of choices is a bit overwhelming, since a text editor is so basic to computer science (yes, web development is computer science). Ideally, you'd try as many editors as you can and get a feel for what suits your workflow. But we'll give you some pointers for getting started. Here are the primary questions you should consider: - Which OS (operating system) do I want to work with? - What kind of technologies do I want to manipulate? - What kind of basic features do I expect from my text editor? - Do I want to add extra features to my text editor? - Do I need support/help while using my text editor? - Does my text editor's look-and-feel matter to me? Notice we didn't mention price. Obviously, that matters too, but a product's cost has little connection with its quality or capability. There's a big chance you'll find a suitable text editor for free. Here are some popular editors: <table class="standard-table"> <thead> <tr> <th scope="col">Editor</th> <th scope="col">License</th> <th scope="col">Price</th> <th scope="col">OS</th> <th scope="col">Support</th> <th scope="col">Doc.</th> <th scope="col">Extensible</th> </tr> </thead> <tbody> <tr> <td><a href="https://bluefish.openoffice.nl">Bluefish</a></td> <td>GPL 3</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://bfwiki.tellefsen.net/index.php/Mailinglists" >Mailing list</a >, <a href="https://bfwiki.tellefsen.net/index.php/Main_Page">wiki</a> </td> <td><a href="https://bluefish.openoffice.nl/manual/">Online Manual</a></td> <td>Yes</td> </tr> <tr> <td><a href="https://brackets.io/" rel="external">Brackets</a></td> <td>MIT/BSD</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://webchat.freenode.net/?channels=brackets" rel="external" >IRC</a > </td> <td> <a href="https://github.com/adobe/brackets/wiki" rel="external" >GitHub Wiki</a > </td> <td> <a href="https://ingo-richter.io/BracketsExtensionTweetBot/" rel="external" >Yes</a > </td> </tr> <tr> <td><a href="https://nova.app/" rel="external">Nova</a></td> <td>Closed source</td> <td>$99</td> <td>Mac</td> <td> <a href="https://x.com/panic">Twitter</a>, <a href="https://help.panic.com/" rel="external">Forum</a>, <a href="https://nova.app/help/">Online</a> </td> <td><a href="https://help.panic.com/nova/">eBook</a></td> <td><a href="https://extensions.panic.com/">Yes</a></td> </tr> <tr> <td><a href="https://www.codelobster.com">CodeLobster</a></td> <td>Closed source</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://www.codelobster.com/forum/index.php" rel="external">Forum</a >, <a href="mailto:<EMAIL>">Email</a> </td> <td><a href="https://www.codelobsteride.com/help/">Online Manual</a></td> <td>Yes</td> </tr> <tr> <td> <a href="https://www.gnu.org/software/emacs/" rel="external">Emacs</a> </td> <td>GPL 3</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://www.gnu.org/software/emacs/manual/efaq.html" rel="external" >FAQ</a >, <a href="https://mail.gnu.org/mailman/listinfo/help-gnu-emacs" rel="external" >Mailing list</a >, <a href="news://gnu.emacs.help" rel="external">News Group</a> </td> <td> <a href="https://www.gnu.org/software/emacs/manual/html_node/emacs/index.html" >Online Manual</a > </td> <td>Yes</td> </tr> <tr> <td><a href="https://espressoapp.com/">Espresso</a></td> <td>Closed source</td> <td>$99</td> <td>Mac</td> <td> <a href="mailto:<EMAIL>">Email</a> </td> <td> <a href="https://help.espressoapp.com/">Online Manual</a> </td> <td>Yes</td> </tr> <tr> <td><a href="https://wiki.gnome.org/Apps/Gedit">Gedit</a></td> <td>GPL</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://discourse.gnome.org/tag/gedit" rel="external">Discourse</a>, <a href="irc://irc.gnome.org/%23gedit">IRC</a> </td> <td> <a href="https://help.gnome.org/users/gedit/stable/">Online Manual</a> </td> <td><a href="https://wiki.gnome.org/Apps/Gedit/ThirdPartyPlugins">Yes</a></td> </tr> <tr> <td><a href="https://kate-editor.org/">Kate</a></td> <td>LGPL, GPL</td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="mailto:<EMAIL>">Mailing list</a>, <a href="irc://irc.kde.org/kate">IRC</a> </td> <td> <a href="https://docs.kde.org/index.php?application=kate&language=en" >Online Manual</a > </td> <td>Yes</td> </tr> <tr> <td> <a href="https://notepad-plus-plus.org/" rel="external" >Notepad++</a > </td> <td>GPL</td> <td>Free</td> <td>Windows</td> <td> <a href="https://sourceforge.net/p/notepad-plus/discussion/">Forum</a> </td> <td> <a href="https://npp-user-manual.org/" rel="external" >Online Manual</a > </td> <td> <a href="https://github.com/notepad-plus-plus/nppPluginList" rel="external" >Yes</a > </td> </tr> <tr> <td><a href="https://www.pspad.com/">PSPad</a></td> <td>Closed source</td> <td>Free</td> <td>Windows</td> <td> <a href="https://www.pspad.com/en/faq.htm">FAQ</a>, <a href="https://forum.pspad.com/" rel="external">Forum</a> </td> <td><a href="https://www.pspad.com/en/helpfiles.htm">Online Help</a></td> <td><a href="https://www.pspad.com/en/pspad-extensions.php">Yes</a></td> </tr> <tr> <td> <a href="https://www.sublimetext.com/" rel="external">Sublime Text</a> </td> <td>Closed source</td> <td>$70</td> <td>Windows, Mac, Linux</td> <td> <a href="https://forum.sublimetext.com/c/technical-support" rel="external" >Forum</a > </td> <td> <a href="https://www.sublimetext.com/docs/">Official</a>, <a href="https://docs.sublimetext.io/" > Unofficial</a > </td> <td><a href="https://packagecontrol.io/">Yes</a></td> </tr> <tr> <td><a href="https://macromates.com/" rel="external">TextMate</a></td> <td>Closed source</td> <td>$50</td> <td>Mac</td> <td> <a href="https://x.com/macromates">Twitter</a>, <a href="https://webchat.freenode.net/?channels=textmate">IRC</a>, <a href="https://lists.macromates.com/postorius/lists/textmate.lists.macromates.com/" rel="external" >Mailing list</a >, <a href="mailto:<EMAIL>">Email</a> </td> <td> <a href="https://macromates.com/manual/en/">Online Manual</a> </td> <td> <a href="https://macromates.com/textmate/manual/bundles" rel="external" >Yes</a > </td> </tr> <tr> <td> <a href="https://www.barebones.com/products/bbedit/" rel="external" >BBEdit</a> </td> <td>Closed source</td> <td>Free</td> <td>Mac</td> <td> <a href="https://www.barebones.com/support/bbedit/" rel="external" >FAQ</a > </td> <td> <a href="https://www.barebones.com/products/bbedit/features.html" rel="external" >Online Manual</a > </td> <td>No</td> </tr> <tr> <td><a href="https://www.vim.org/" rel="external">VIM</a></td> <td> <a href="https://vimdoc.sourceforge.net/htmldoc/uganda.html#license" rel="external" >Specific open license</a > </td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://www.vim.org/maillist.php#vim" rel="external" >Mailing list</a > </td> <td><a href="https://vimdoc.sourceforge.net/">Online Manual</a></td> <td> <a href="https://www.vim.org/scripts/script_search_results.php?order_by=creation_date&#x26;direction=descending" rel="external" >Yes</a > </td> </tr> <tr> <td> <a href="https://code.visualstudio.com/download">Visual Studio Code</a> </td> <td> <a href="https://github.com/microsoft/vscode">Open Source</a> under MIT license/ Specific license for product </td> <td>Free</td> <td>Windows, Mac, Linux</td> <td> <a href="https://code.visualstudio.com/docs/supporting/faq">FAQ</a> </td> <td><a href="https://code.visualstudio.com/docs">Documentation</a></td> <td><a href="https://marketplace.visualstudio.com/vscode">Yes</a></td> </tr> </tbody> </table> ## Active Learning In this active learning section, we would like you to try using and/or installing a text editor of your choice. Your computer may already be installed with one of the editors suggested above (e.g., Gedit if you use GNOME desktop, Kate if you use KDE etc.), if not then you should try installing one or more text editors of your choosing. Try digging through the settings of your editor and read the manual or documentation to see what its capabilities are. In particular (if possible in your editor), try to: - Change syntax highlighting settings and colors - Play with [indentation](<https://en.wikipedia.org/wiki/Indentation_(typesetting)#Indentation_in_programming>) width, setting it to an appropriate setting for your needs - Check autosave and session saving settings - Configure any available [plugins](<https://en.wikipedia.org/wiki/Plug-in_(computing)>) and investigate how to get new ones - Change color schemes - Adjust view settings and see how you can change the layout of the views - Check what programming languages/technologies your editor supports While you're learning the default settings of most text editors should be fine to use, but it is important to become familiar with your chosen tools, so you can select the best one for your usage. You will learn more about customizing your editors and tools as you gain experience, and more importantly you will learn what features are more useful to your purposes. ## Dig deeper ### Choice criteria So, in more detail, what should you be thinking about when you choose a text editor? #### Which OS (operating system) do I want to work with? Of course it's your choice. However, some editors are only available for certain OSs, so if you like switching back and forth, that would narrow down the possibilities. Any text editor _can_ get the job done, if it runs on your system, but a cross-platform editor eases migration from OS to OS. So first find out which OS you're using, and then check if a given editor supports your OS. Most editors specify on their website whether they support Windows or Mac, though some editors only support certain versions. If you're running Ubuntu, your best bet is to search within the Ubuntu Software Center. In general, of course, the Linux/UNIX world is a pretty diverse place where different distros work with different, incompatible packaging systems. That means, if you've set your heart on an obscure text editor, you may have to compile it from source yourself (not for the faint-hearted). #### What kind of technologies do I want to manipulate? Generally speaking, any text editor can open any text file. That works great for writing notes to yourself, but when you're doing web development and writing in {{Glossary("HTML")}}, {{Glossary("CSS")}}, and {{Glossary("JavaScript")}}, you can produce some pretty large, complex files. Make it easier on yourself by choosing a text editor that understands the technologies you're working with. Many text editors help you out with features like: - **[Syntax highlighting](https://en.wikipedia.org/wiki/Syntax_highlighting).** Make your file more legible by color-coding keywords based on the technology you're using. - **[Code completion](https://en.wikipedia.org/wiki/Autocomplete#In_source_code_editors).** Save you time by auto-completing recurring structures (for example, automatically close HTML tags, or suggesting valid values for a given CSS property). - **[Code snippets](<https://en.wikipedia.org/wiki/Snippet_(programming)>).** As you saw when starting a new HTML document, many technologies use the same document structure over and over. Save yourself the hassle of retyping all this by using a code snippet to pre-fill your document. Most text editors now support syntax highlighting, but not necessarily the other two features. Make sure in particular that your text editor supports highlighting for {{Glossary("HTML")}}, {{Glossary("CSS")}}, and {{Glossary("JavaScript")}}. #### What kind of basic features do I expect from my text editor? It depends on your needs and plans. These functionalities are often helpful: - Search-and-replace, in one or multiple documents, based on {{Glossary("Regular Expression", "regular expressions")}} or other patterns as needed - Quickly jump to a given line - View two parts of a large document separately - View HTML as it will look in the browser - Select text in multiple places at once - View your project's files and directories - Format your code automatically with code beautifier - Check spelling - Auto-indent code based on indentation settings #### Do I want to add extra features to my text editor? An extensible editor comes with fewer built-in features, but can be extended based on your needs. If you aren't sure which features you want, or your favorite editor lacks those features out of the box, look for an extensible editor. The best editors provide many plugins, and ideally a way to look for and install new plugins automatically. If you like _lots_ of features and your editor is slowing down because of all your plugins, try using an [IDE](https://en.wikipedia.org/wiki/Integrated_development_environment) (integrated development environment). An IDE provides many tools in one interface and it's a bit daunting for beginners, but always an option if your text editor feels too limited. Here are some popular IDEs: - [Aptana Studio](https://www.axway.com/en/aptana) - [Eclipse](https://www.eclipse.org/) - [NetBeans IDE](https://netbeans.apache.org/) - [Visual Studio](https://visualstudio.microsoft.com/) - [WebStorm](https://www.jetbrains.com/webstorm/) #### Do I need support/help while using my text editor? It's always good to know if you can get help or not when using software. For text editors, check for two different kinds of support: 1. User-oriented content (FAQ, manual, online help) 2. Discussion with developers and other users (forum, email, IRC) Use the written documentation when you're learning how to use the editor. Get in touch with other users if you're troubleshooting while installing or using the editor. #### Does my text editor's look-and-feel matter to me? Well, that's a matter of taste, but some people like customizing every bit of the UI (user interface), from colors to button positions. Editors vary widely in flexibility, so check beforehand. It's not hard to find a text editor that can change color scheme, but if you want hefty customizing you may be better off with an IDE. ### Install and set up Installing a text editor is usually quite straightforward. The method varies based on your platform but it shouldn't be too hard: - **Windows.** The developers will give you an `.exe` or `.msi` file. Sometimes the software comes in a compressed archive like `.zip`, `.7z`, or `.rar`, and in that case you'll need to install an additional program to extract the content from the archive. Windows supports `.zip` by default. - **Mac.** On the editor's website you can download a `.dmg` file. Some text editors you can find directly in the Apple Store to make installation even simpler. - **Linux.** In the most popular distros you can start with your graphical package manager (Ubuntu Software Center, mintInstall, GNOME Software, \&c.). You can often find a `.deb` or `.rpm` file for prepackaged software, but most of the time you'll have to use your distro's repository server or, in worst case scenario, compile your editor from source. Take the time to carefully check the installation instructions on the text editor's website. When you install a new text editor, your OS will probably continue to open text files with its default editor until you change the _[file association](https://en.wikipedia.org/wiki/File_association)._ These instructions will help you specify that your OS should open files in your preferred editor when you double-click them: - [Windows](https://support.microsoft.com/en-us/windows) - [macOS](https://support.apple.com/guide/mac-help/choose-an-app-to-open-a-file-on-mac-mh35597/mac) - Linux - [Ubuntu Unity](https://askubuntu.com/questions/289337/how-can-i-change-file-association-globally) - [GNOME](https://help.gnome.org/users/gnome-help/stable/files-open.html.en) - [KDE](https://userbase.kde.org/System_Settings/File_Associations) ## Next steps Now that you have a good text editor, you could take some time to finalize [your basic working environment](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/set_up_a_local_testing_server), or, if you want to play with it right away, write [your very first web page](/en-US/docs/Learn_web_development/Getting_started/Your_first_website).