Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > tools_and_setup > index.md --- title: Tools and setup slug: Learn_web_development/Howto/Tools_and_setup page-type: landing-page sidebar: learn-how-to --- This section lists questions related to the tools/software you can use to build websites. - [What software do I need to build a website?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/What_software_do_I_need) - : In this article we explain which software components you need to edit, upload, or view a website. - [How much does it cost to do something on the web?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/How_much_does_it_cost) - : When you're launching a website, you may spend nothing or your costs may go through the roof. In this article we discuss how much everything costs and what you get for what you pay (or don't pay). - [What text editors are available?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Available_text_editors) - : In this article we highlight some things to think about when choosing and installing a text editor for web development. - [What are browser developer tools?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/What_are_browser_developer_tools) - : Every browser features a set of devtools for debugging HTML, CSS, and other web code. This article explains how to use the basic functions of your browser's devtools. - [How do you make sure your website works properly?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Checking_that_your_web_site_is_working_properly) - : So you've published your website online very good! But are you sure it works properly? This article provides some basic troubleshooting steps. - [How do you set up a local testing server?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/set_up_a_local_testing_server) - : This article explains how to set up a simple local testing server on your machine, and the basics of how to use it. - [How do you upload files to a web server?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Upload_files_to_a_web_server) - : This article shows how to publish your site online with {{Glossary("FTP")}} tools one of the most common ways to get a website online so others can access it from their computers. - [How do I use GitHub Pages?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/Using_GitHub_pages) - : This article provides a basic guide to publishing content using GitHub's gh-pages feature. - [How do you host your website on Google App Engine?](/en-US/docs/Learn_web_development/Howto/Tools_and_setup/How_do_you_host_your_website_on_Google_App_Engine) - : Looking for a place to host your website? Here's a step-by-step guide to hosting your website on Google App Engine. - [What tools are available to debug and improve website performance?](https://firefox-source-docs.mozilla.org/devtools-user/performance/index.html) - : This set of articles shows you how to use the Developer Tools in Firefox to debug and improve performance of your website, using the tools to check memory usage, the JavaScript call tree, the number of DOM nodes being rendered, and more.