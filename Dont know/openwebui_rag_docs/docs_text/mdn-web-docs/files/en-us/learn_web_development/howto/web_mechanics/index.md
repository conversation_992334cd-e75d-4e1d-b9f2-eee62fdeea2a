Path: mdn-web-docs > files > en-us > learn_web_development > howto > web_mechanics > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > howto > web_mechanics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > web_mechanics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > web_mechanics > index.md Path: mdn-web-docs > files > en-us > learn_web_development > howto > web_mechanics > index.md --- title: Web mechanics slug: Learn_web_development/Howto/Web_mechanics page-type: landing-page sidebar: learn-how-to --- This section covers questions relating to general knowledge of the web ecosystem and how it works. - [How does the Internet work?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/How_does_the_Internet_work) - : The **Internet** is the backbone of the web, the technical infrastructure that makes the web possible. At its most basic, the internet is a massive network of computers communicating with each other. This article discusses how it works, in broad terms. - [What is the difference between webpage, website, web server, and search engine?](/en-US/docs/Learn_web_development/Getting_started/Environment_setup/Browsing_the_web) - : In this article we describe various web-related concepts: webpages, websites, web servers, and search engines. These terms are often a source of confusion for newcomers to the web, or are used incorrectly. Let's discover what they actually mean! - [What are hyperlinks?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_are_hyperlinks) - : In this article, we'll go over what hyperlinks are and why they matter. - [What is a URL?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_URL) - : With {{Glossary("Hypertext")}} and {{Glossary("HTTP")}}, URL is a key concept when it comes to the Internet. It is the mechanism used by {{Glossary("Browser","browsers")}} to retrieve any published resource on the web. - [What is a domain name?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_domain_name) - : Domain names are a key component of the Internet infrastructure. They provide a human-readable address for any web server available on the Internet. - [What is a web server?](/en-US/docs/Learn_web_development/Howto/Web_mechanics/What_is_a_web_server) - : The term "web server" can refer to the hardware or software that serves websites to clients across the web or both of them working together. In this article we go over how web servers work, and why they're important.