Path: mdn-web-docs > files > en-us > learn_web_development > index.md

Path: mdn-web-docs > files > en-us > learn_web_development > index.md Path: mdn-web-docs > files > en-us > learn_web_development > index.md Path: mdn-web-docs > files > en-us > learn_web_development > index.md Path: mdn-web-docs > files > en-us > learn_web_development > index.md --- title: Learn web development slug: Learn_web_development page-type: landing-page sidebar: learnsidebar --- ## The essential skill set for new front-end developers Welcome to MDN **Learning Web Development** (also known as **Learn**). This resource provides a structured set of tutorials teaching the essential skills and practices for being a successful front-end developer, along with challenges and further recommended resources. ## About Learn web development - Teaches the essential skills and knowledge every front-end developer needs for career success and industry relevance, as defined in the [MDN Curriculum](/en-US/curriculum/). - Created by the MDN community and refined with insights from students, educators, and developers from the broader web community. - Designed to take you from "beginner" to "comfortable" (not "beginner" to "expert"), giving you enough knowledge to use more advanced resources (such as [the rest of MDN](/en-US/)). > [!NOTE] > Last updated: December 2024 ([see changelog](/en-US/docs/Learn_web_development/Changelog)). ## Don't know where to get started? - Never coded before? - : Our [Getting started modules](/en-US/docs/Learn_web_development/Getting_started) provide setup tutorials and essential concepts and background information for complete beginners. You should start here if you are a complete beginner (i.e., you've not installed a code editor or written any code yet). - Want to master the essentials? - : Our [Core modules](/en-US/docs/Learn_web_development/Core) provide a structured set of tutorials teaching the essential skills and practices for being a successful front-end developer. - Beyond the basics? - : Our [Extension modules](/en-US/docs/Learn_web_development/Extensions) cover useful additional skills to learn as you start to expand your knowledge and develop specialisms. Go onto these after you finish our Core. - Working at a school? - : Use our modules to guide your teaching, check out our [Educators page](/en-US/docs/Learn_web_development/Educators) for more ideas, or enroll your students in Scrimba's [Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j?via=mdn)<sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup>. ## Getting our code examples The code examples you'll encounter in the Learning Area are all [available on GitHub](https://github.com/mdn/learning-area/): - The easiest way to get them is to [download a ZIP of the latest main code branch](https://codeload.github.com/mdn/learning-area/zip/main). - If you are familiar with Git and GitHub, you could also choose to clone the repository. ## Contact us If you want to get in touch with us about anything, use the [communication channels](/en-US/docs/MDN/Community/Communication_channels). We'd love to hear from you about anything you think is wrong or missing on the site, requests for new learning topics, requests for help with items you don't understand, or any other questions or concerns. If you're interested in helping develop/improve the content, take a look at [how you can help](/en-US/docs/MDN/Community) and get in touch! We are more than happy to talk to you, whether you are a learner, teacher, experienced web developer, or someone else interested in helping to improve the learning experience. ## See also - [The Frontend Developer Career Path](https://scrimba.com/the-frontend-developer-career-path-c0j?via=mdn) <sup>[_MDN learning partner_](/en-US/docs/MDN/Writing_guidelines/Learning_content#partner_links_and_embeds)</sup> - : [Scrimba's](https://scrimba.com?via=mdn) _Frontend Developer Career Path_ teaches all you need to know to be a competent front-end web developer, with fun interactive lessons and challenges, knowledgeable teachers, and a supportive community. Go from zero to landing your first front-end job! Many of the course components are available as standalone free versions. - [Codecademy](https://www.codecademy.com/) - : A great interactive site for learning programming languages from scratch. - [freeCodeCamp.org](https://www.freecodecamp.org/) - : Interactive site with tutorials and projects to learn web development. - [Learn JavaScript](https://learnjavascript.online/) - : An excellent resource for aspiring web developers Learn JavaScript in an interactive environment, with short lessons and interactive tests, guided by automated assessment. The first 40 lessons are free, and the complete course is available for a small one-time payment.