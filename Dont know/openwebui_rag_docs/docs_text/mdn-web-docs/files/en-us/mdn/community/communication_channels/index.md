Path: mdn-web-docs > files > en-us > mdn > community > communication_channels > index.md

Path: mdn-web-docs > files > en-us > mdn > community > communication_channels > index.md Path: mdn-web-docs > files > en-us > mdn > community > communication_channels > index.md Path: mdn-web-docs > files > en-us > mdn > community > communication_channels > index.md Path: mdn-web-docs > files > en-us > mdn > community > communication_channels > index.md --- title: Communication channels slug: MDN/Community/Communication_channels page-type: mdn-community-guide sidebar: mdnsidebar --- There are various communication channels that the community can use to contact MDN Web Docs staff and participate in discussions. ## Chat rooms Chat rooms are for meeting other people, asking questions and sharing information. They are a great place to get to know other community members, share your work, and get help with your contributions. ### Discord server The MDN Web Docs community Discord server is open to the public. This server is a great place to see what staff and members of the community are doing on a daily basis. You can ask questions, seek clarifications, find out how to get involved, and join specific channels based on your areas of interest. Join the MDN Web Docs community via our [Discord invite](/discord). ### Matrix chat rooms The Matrix chat rooms are an alternative to the Discord server. - [MDN room](https://chat.mozilla.org/#/room/#mdn:mozilla.org) on Mozilla Element web app - [MDN room](https://app.element.io/#/room/#mdn:mozilla.org) on Matrix Element web app - [Add-ons room](https://chat.mozilla.org/#/room/#addons:mozilla.org) - [List of all Mozilla rooms](https://wiki.mozilla.org/Matrix#Commonly_used_rooms) ## GitHub Discussions [GitHub Discussions](https://github.com/orgs/mdn/discussions) on MDN Web Docs is the place to look out for project-wide [announcements](https://github.com/orgs/mdn/discussions/categories/announcements), to share the work you're planning to do if you think you will need a consensus on a good course of action. You can also use GitHub Discussions for brainstorming your ideas to define an actionable piece of work, especially when the changes have a wide-ranging impact. Use GitHub discussions to post your questions and propose your suggestions. Choose the appropriate [discussion category](https://github.com/mdn/mdn-community#github-discussions) when starting a thread. Your answered questions can be really useful for other people looking for similar information in the future. Check out the MDN-specific [discussion guidelines](/en-US/docs/MDN/Community/Discussions) before you create a new submission. ## Social media You can follow MDN Web Docs on [Mastodon](https://mastodon.social/@mdn) and [X](https://x.com/MozDevNet). Feel free to tag us in your posts if you want to share something with us or say hello, although we can't guarantee that we can respond to everything. ## Forums You can use the forums listed below for discussing code problems. - [MDN Discourse forum](https://discourse.mozilla.org/c/mdn/236) - [MDN Discourse forum learning category](https://discourse.mozilla.org/c/mdn/learn/250) - [Add-ons Discourse](https://discourse.mozilla.org/c/add-ons/35) ## Localization channels Each localization team has its own [method of communication](/en-US/docs/MDN/Community/Translated_content). ## Email For any nonpublic communication, send an email to [<EMAIL>](mailto:<EMAIL>).