Path: mdn-web-docs > files > en-us > mdn > community > community_participation_guidelines > index.md

Path: mdn-web-docs > files > en-us > mdn > community > community_participation_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > community > community_participation_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > community > community_participation_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > community > community_participation_guidelines > index.md --- title: Code of conduct enforcement guidelines short-title: Community Participation Guidelines slug: MDN/Community/Community_Participation_Guidelines page-type: mdn-community-guide sidebar: mdnsidebar --- Participating in MDN Web Docs projects means adhering to the [Mozilla Community Participation Guidelines (CPG)](https://www.mozilla.org/en-US/about/governance/policies/participation/). These guidelines exist to foster a welcoming, inclusive, and safe environment, and to ensure that every community member feels valued, heard, and protected. This document describes how to report violations and what happens depending on the severity of a violation. ## Guiding principles - **Transparency:** Address every report clearly and consistently. - **Confidentiality:** Protect reporter privacy unless explicit consent is given to share information. - **Proportionality:** Responses to violations are proportionate to the harm caused. - **Restorative Justice:** Repair harm and foster learning. ## Code of Conduct Violations and Enforcement Ladder The MDN Community Management team ([`@mdn/community`](https://github.com/orgs/mdn/teams/community) on GitHub) investigates the severity of behavior based on a report, then determines and carries out any actions, if appropriate. Violations follow a step-by-step escalation process, and the reportee is informed of consequences at leach level. In serious cases, enforcement may skip levels up to and including an immediate, permanent ban. Below is a summary of severity levels and enforcement actions. ### Level 0: No Action - **Severity:** There is no clear indication a violation of the CPG. - **Actions:** No formal action taken. Feedback is provided to the reporter, reportee, or both, to promote good behavior and positive interactions. ### Level 1: Simple Warning Issued - **Severity:** Minor violations of the CPG. - **Actions:** A private, written warning from project leadership, with clarity of the violation and consequences of continued behavior. ### Level 2: Warning - **Severity:** Moderate violation of the CPG, or repeated minor violations. - **Actions:** - A private, written warning with clarity of the violation and consequences of continued behavior. - Guidance on rectifying the situation, if possible. - Communication of next-level consequences if behaviors are repeated. ### Level 3: Warning + Mandatory Cooling Off Period (Tool Access Revoked - GH & Discord) - **Severity:** Continued violations or escalation in severity. - **Actions:** - A private warning with clarity of the violation and consequences of continued behavior. - No interaction on community messaging platforms, such as public forums, commenting on bugs. - No interacting with people involved in the report or others suspected to be involved. - Avoid interactions in Mozilla channels, physical spaces and offices, and online channels like social media (e.g., following/liking/re-posting). - The reportee has the opportunity to participate in the project again after onboarding that clarifies expected behavior. ### Level 4: Ban across MDN and Mozilla Spaces, All Areas Permanent Ban - **Severity:** Serious [Mozilla Community Participation Guidelines (CPG)](https://www.mozilla.org/en-US/about/governance/policies/participation/) breaches. - **Actions:** - Private communication of a ban from project leadership, with clarity of the violation and warning of consequences. Should the behavior continue, it would lead to a ban from all Mozilla community spaces. - Required to avoid interaction on community messaging platforms and external channels. - Deactivation of any and all Mozilla accounts. - Permanently banned from entering Mozilla digital and event spaces. - Permanently suspended from any and all community leadership roles. - Revocation of any and all permissions to use the Mozilla trademark. ## Reporting Process ### How to Report a violation Reports can be <NAME_EMAIL>. To report a violation or abuse on GitHub, see [GitHub's content reporting guide](https://docs.github.com/en/communities/maintaining-your-safety-on-github/reporting-abuse-or-spam). The **"Report to repository admins"** option must be checked for the MDN team to see a content report. Include any relevant details, including date, time, description of the violation. Include identifying information of the offender and screenshots, if possible. ### Investigation MDN Community Management investigates all reports confidentially. When necessary, further information may be requested from the involved parties. ### Decision Making MDN Community Management assess the severity of the violation, apply the enforcement actions as outlined, and communicate the outcome to relevant parties. ### Appeals If a reported individual believes the decision was unjust, they may appeal through the [appeals process detailed on the community page]. Appeals are reviewed by a separate, impartial panel. ### Documentation and Record Keeping All incidents will be documented to ensure consistency and fairness. Records of violations and actions taken are kept private, and accessible only by MDN Community Management. ## Frequently Asked Questions (FAQs) Common questions are answered below. If anything else about CPG reports and processes is unclear or unanswered, get in touch via [our Communication channels](/en-US/docs/MDN/Community/Communication_channels). ### What happens if a violation involves an edge-case scenario? Behavior that's inappropriate but is not explicitly described by the project's Code of Conduct are assessed individually. The enforcement team will make a judgment based on the context and the principles of fairness, transparency, and proportionality. Examples include: - **Anonymous Reports:** These reports are investigated with extra care to validate the claims while maintaining fairness. - **Reports Involving Leadership Members:** A separate impartial panel will handle these cases to avoid conflicts of interest. - **Interactions During Cooling-Off Periods:** Violations during a cooling-off period, such as unintentional online interactions, may be considered differently, with an emphasis on intent and harm caused. ### Updates to CPG The CPG process is a living document that evolves with the community. Feedback is welcome on [GitHub Discussions](https://github.com/orgs/mdn/discussions), [Discord](https://mdn.dev/discord), and changes will be communicated on the community page.