Path: mdn-web-docs > files > en-us > mdn > community > discussions > index.md

Path: mdn-web-docs > files > en-us > mdn > community > discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > index.md --- title: GitHub Discussions slug: MDN/Community/Discussions page-type: mdn-community-guide sidebar: mdnsidebar --- On MDN Web Docs, we encourage our community to start and engage in discussions around topics related to the project. We ask that you keep each discussion focused on the topic at hand instead of covering multiple subjects in a discussion. > [!NOTE] > Don't use GitHub Discussions to report bugs. > If you see something wrong on MDN Web Docs, open a GitHub issue in the [the relevant GitHub repository](/en-US/docs/MDN/Community/Our_repositories). If you're not sure whether to open a GitHub issue or discussion, here's what each are for: - **Issues** are for reporting bugs or tracking a work item with defined and actionable tasks and outcomes. - **Discussions** are for reaching consensus about how we're working and for defining tasks. If your discussion doesn't progress or you're unsure of the next steps, refer to the [Guidelines for managing and resolving discussions](/en-US/docs/MDN/Community/Discussions/Managing_and_resolving_discussions) for advice on moving forward, including expectations on timelines. Check out the subject of each discussion category below so that you can start your discussion in the proper place. | Discussion Category | Subject | | ------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | | [Announcements][announcements] | Reserved for MDN Web Docs staff. We ask that you choose one of the other available categories. | | [Browser compatibility data][bcd] | The [browser-compat-data][bcd-repo] project which documents compatibility data for browsers. | | [Content][content] | The [content repository][content-repo] on MDN Web Docs. Don't ask for coding help here - if you're stuck on a problem, try the [Learn Web Development][learn-web-dev] area. | | [Localization][localization] | The [translated-content repository][translated-content] covering our [supported locales][locales]. This is also where [deprecation notices][macro-deprecation] happen. | | [MDN Plus][mdn-plus] | The [MDN Plus features][mdn-plus-feature] as well as your ideas. For MDN Plus support such as subscriptions, refer to Mozilla's [official support channel][mdn-plus-support]. | | [Platform][platform] | The frontend and build system of MDN Web Docs. If you found a bug, report it on [the relevant repository][mdn-repos]. **NOTE:** There is a separate discussion category for MDN Plus. | [announcements]: https://github.com/orgs/mdn/discussions/categories/announcements [bcd]: https://github.com/orgs/mdn/discussions/categories/browser-compatibility-data [bcd-repo]: https://github.com/mdn/browser-compat-data [content]: https://github.com/orgs/mdn/discussions/categories/content [content-repo]: https://github.com/mdn/content [learn-web-dev]: /en-US/docs/Learn_web_development [localization]: https://github.com/orgs/mdn/discussions/categories/localization [translated-content]: https://github.com/mdn/translated-content/ [locales]: https://github.com/mdn/translated-content/#locales [macro-deprecation]: https://github.com/orgs/mdn/discussions/67 [mdn-plus]: https://github.com/orgs/mdn/discussions/categories/mdn-plus [mdn-plus-feature]: /en-US/plus [mdn-plus-support]: https://support.mozilla.org/en-US/products/mdn-plus [platform]: https://github.com/orgs/mdn/discussions/categories/platform [mdn-repos]: /en-US/docs/MDN/Community/Our_repositories