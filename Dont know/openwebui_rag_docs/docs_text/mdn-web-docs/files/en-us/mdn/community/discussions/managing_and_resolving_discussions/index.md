Path: mdn-web-docs > files > en-us > mdn > community > discussions > managing_and_resolving_discussions > index.md

Path: mdn-web-docs > files > en-us > mdn > community > discussions > managing_and_resolving_discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > managing_and_resolving_discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > managing_and_resolving_discussions > index.md Path: mdn-web-docs > files > en-us > mdn > community > discussions > managing_and_resolving_discussions > index.md --- title: Managing and resolving discussions slug: MDN/Community/Discussions/Managing_and_resolving_discussions page-type: mdn-community-guide sidebar: mdnsidebar --- The MDN community is encouraged to [initiate and engage in discussions](/en-US/docs/MDN/Community/Discussions) related to the MDN Web Docs documentation. Some discussions don't need a resolution or agreement, but if they do, it is natural for those starting the discussion to expect their proposed ideas to reach a logical conclusion. Most of these discussions reach a broad agreement quickly. However, some discussions risk becoming stalled due to an unclear path to a resolution, often because of differing opinions. This document offers guidelines; suggesting processes and strategies to help you move toward a resolution within a reasonable timeframe without the conversation getting stalled. ## Moving a discussion to a resolution Most discussions do not need a formal resolution process. These MDN discussion guidelines are here for the discussions that need a timely resolution, are stalled, are at risk of becoming stalled, or are otherwise not moving toward a conclusion and would benefit from a formal process: 1. Each discussion is held / rooted in a [discussion on GitHub](https://github.com/orgs/mdn/discussions). This GitHub discussion serves as the "source of truth" for the topic. - To maintain continuity, remember to capture summaries and notes from any meetings and async discussions in this GitHub discussion thread. 2. Each topic of discussion needs a driver. The driver is often the author of the discussion, but can be another person committed to resolving the discussion. The driver is responsible for: - Guiding the conversation. - Making people aware the discussion exists. - Setting the feedback, timeline, informing people of it, changing the timeline as necessary, and sticking to the timeline as appropriate. - Informing all the appropriate channels - Slack, Discord, tagging people on GitHub, and other channels if appropriate - of needed feedback by specific dates. - Bring up the topic in community and weekly meetings. - Organizing a synchronous, online face-to-face meeting if required (if there is disagreement). Face-to-face meetings should be rare and only if required (See #3). - Summarizing face-to-face conclusions in the relevant discussion on [Discussions](https://github.com/orgs/mdn/discussions). - Guiding the implementation of the discussion results or working with the appropriate team lead to ensure that the results are implemented. 3. Face-to-face meetings regarding a discussion should only be called if there is disagreement. - Face-to-face meetings must be announced in all relevant [communication channels](/en-US/docs/MDN/Community/Communication_channels), such as Slack, Discord, GitHub discussion, etc. - Conclusions of each face-to-face meeting must be entered into the GitHub discussion, which is the source of truth for the discussion. - The driver is responsible for calling the meeting, informing all parties, and reporting results back into the GitHub discussion. Once agreement has been achieved, the resolution can be announced, the discussion can be closed, and the plan for implementing the resolution can be put into action! ## Discussion progression and timeline Every discussion will have a different timeline depending on the complexity of the topic and the consensus levels. Ideally, most discussions should be resolved within two months, allowing for the topic to be addressed at various internal meetings. This timeframe ensures diverse viewpoints are considered and everyone interested has the opportunity to contribute to the discussion. 1. Post the discussion. 2. Assign a driver. Determine the driver if it's not the same as the discussion author. 3. Identify any key stakeholders and needed approvers (the individuals who need to weigh in on the topic and give their approval), if any. 4. Inform the approvers and other essential voices of the discussion and your proposed timeline. If needed, reach out to them again after 2 weeks, and weekly thereafter, until they provide feedback. 5. Add the discussion topic to the agendas of relevant meetings. 6. After a month, sort through the feedback, discussions, and agreements, and formulate an initial plan merging the feedback into a possible action plan. 7. Re-inform and re-request feedback from all interested parties, including everyone who participated in the discussion in any way. 8. During the second month, keep reaching out the community for feedback on the proposed plan, making updates to the plan in light of any new feedback. Rinse. Repeat. 9. If there are points of contention, arrange an online, synchronous, face-to-face meeting to resolve any remaining disagreements (as captured in the discussion thread). 10. Keep the discussion threads alive during the second month as you work with the community towards the resolution. 11. [Create the issue](/en-US/docs/MDN/Community/Issues) for the resolution implementation plan and put it into action. ([Issue reporting guidelines](/en-US/docs/MDN/Community/Issues#guidelines_for_reporting_an_issue)) 12. Close the discussion. If the discussion involves reaching out to and receiving feedback from experts, the timeline above can be extended as needed. ### Inconclusive resolutions It's important to come to a resolution, but it's also important to remember that not all discussions will come to an implementable resolution. A discussion's resolution may be "no decision is being made" or "Let's revisit this in a year". These are both valid resolutions! When a discussion ends with no implementable resolution, note that in the discussion, then close the discussion as resolved.