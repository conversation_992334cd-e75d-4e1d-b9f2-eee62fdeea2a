Path: mdn-web-docs > files > en-us > mdn > community > getting_started > index.md

Path: mdn-web-docs > files > en-us > mdn > community > getting_started > index.md Path: mdn-web-docs > files > en-us > mdn > community > getting_started > index.md Path: mdn-web-docs > files > en-us > mdn > community > getting_started > index.md Path: mdn-web-docs > files > en-us > mdn > community > getting_started > index.md --- title: Getting started with MDN Web Docs short-title: Getting started slug: MDN/Community/Getting_started page-type: mdn-community-guide sidebar: mdnsidebar --- We are an open community of developers, technical writers, and learners building resources for a better Web, regardless of brand, browser, or platform. Anyone can contribute, and each person who does contribute has an impact on millions of readers. Learn how to contribute and drive innovation on the Open Web. ## What can I do to help? There are multiple avenues you can take to contribute to MDN, depending on your skill set and interests. Therefore, along with each task, we provide a short description and an approximate time each type of task typically takes. If you're unsure what to do, you can always ask for help in one of [our communication channels](/en-US/docs/MDN/Community/Communication_channels). We have created a [contributors task board](https://github.com/orgs/mdn/projects/25/views/1) to help you find contribution opportunities that will meaningfully impact the project. The board has an overview and separate views for specific contribution types. Note that our small but mighty docs team maintains this repo. To preserve our bandwidth, off-topic conversations will be closed. ## What do I need to get started? To contribute, you will need a GitHub account. If you do not already have one, go ahead and [sign up](https://github.com/signup) for an account before continuing. If you are new to GitHub, we encourage you to take the following free, self-paced courses and reading material offered by GitHub. With this knowledge, you can focus on your contributions without the burden of learning a new tool at the same time. - [Introduction to GitHub](https://github.com/skills/introduction-to-github) - [Setting up Git](https://docs.github.com/en/get-started/git-basics/set-up-git) - [GitHub workflow](https://docs.github.com/en/get-started/using-github/github-flow) - [Using Markdown](https://github.com/skills/communicate-using-markdown) > [!NOTE] > The "Introduction to GitHub" course should be enough to get you started. > Feel free to jump forward and return to the other documents later on. We also recommend reading the following learning material: - [Basic etiquette for open source projects](/en-US/docs/MDN/Community/Open_source_etiquette): If you've never contributed to an open source project before, we encourage you to read this document. - [Learn web development](/en-US/docs/Learn_web_development): If you are new to HTML, CSS, JavaScript, we have some great content to help you get started. - [Deep dive into collaborating with pull requests](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests) Some writing-specific contribution opportunities will require a reasonable understanding of the English language. That said, don't worry if your grammar isn't perfect: we have a team of people who will review your writing and help improve it. We need your technical expertise, and we'll help polish your English in order to get it. ## Choosing what to work on and getting started Once you've decided what sort of task you want to work on, it is time to head over to the [contributors task board](https://github.com/orgs/mdn/projects/25/views/1), pick an issue, and let us know by commenting on the issue and tagging the `@mdn/mdn-community-engagement` team. Someone from the team will respond with some comments, hints, or guidance. Make sure you're not working on something that's already in-progress - it can be frustrating to duplicate or lose work. If in doubt, ask questions on the issue. ## Ways to contribute Here's a list of ways you can contribute to MDN Web Docs: - [Work on good first issues](https://github.com/orgs/mdn/projects/25/views/1) - [Review pull requests](/en-US/docs/MDN/Community/Pull_requests) - [Help beginners in our community](/en-US/docs/MDN/Community) - [Help translate MDN Web Docs](/en-US/docs/MDN/Community/Translated_content) - [Help fix known backend issues](https://github.com/mdn/rari/issues) and [known frontend issues](https://github.com/mdn/yari/issues) - [Help us keep browser compatibility data up to date](https://github.com/mdn/browser-compat-data) ## Contributions When contributing, you agree to make your contributions available under the [Attribution-ShareAlike license](https://creativecommons.org/licenses/by-sa/4.0/) (or an alternative license already specified by the page you are editing). In addition, code samples are available under [Creative Commons CC-0](https://creativecommons.org/public-domain/cc0/) (a Public Domain dedication). If you have any questions or concerns about anything discussed here, please [contact us](/en-US/docs/MDN/Community/Communication_channels).