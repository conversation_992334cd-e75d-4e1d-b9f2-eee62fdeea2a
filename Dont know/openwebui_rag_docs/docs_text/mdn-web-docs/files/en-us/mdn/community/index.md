Path: mdn-web-docs > files > en-us > mdn > community > index.md

Path: mdn-web-docs > files > en-us > mdn > community > index.md Path: mdn-web-docs > files > en-us > mdn > community > index.md Path: mdn-web-docs > files > en-us > mdn > community > index.md Path: mdn-web-docs > files > en-us > mdn > community > index.md --- title: Community resources short-title: Resources slug: MDN/Community page-type: mdn-community-guide sidebar: mdnsidebar --- Welcome to MDN Web Docs, an open-source, collaborative project that documents web platform technologies, including [HTML](/en-US/docs/Web/HTML), [CSS](/en-US/docs/Web/CSS), [JavaScript](/en-US/docs/Web/JavaScript), and [Web APIs](/en-US/docs/Web/API). We also provide extensive [learning resources](/en-US/docs/Learn_web_development) for early-stage developers and students. - [Getting started](/en-US/docs/MDN/Community/Getting_started) - : This section explains how you can start contributing and the type of contributions we accept. Anyone can contribute, and each person who does contribute has an impact on millions of readers. Learn how to contribute and drive innovation on the Open Web. - [Our repositories](/en-US/docs/MDN/Community/Our_repositories) - : This document describes the GitHub repositories (repos) you may need when contributing to MDN Web Docs. - [GitHub Issues](/en-US/docs/MDN/Community/Issues) - : Issues are used to track all bugs and work that has a clear actionable outcome. This article contains guidelines on opening and working on issues and also covers issue triage and content suggestions. - [Pull requests](/en-US/docs/MDN/Community/Pull_requests) - : This section covers our guidelines for submitting pull requests and what you should expect from the review process. - [Roles and teams](/en-US/docs/MDN/Community/Roles_teams) - : This section provides an overview of the users and teams that are part of the MDN Web Docs project and details what it means to be part of a team. - [Translated content](/en-US/docs/MDN/Community/Translated_content) - : MDN Web Docs Localization information, with details about teams, communication channels, and how to get involved. - [Open source etiquette](/en-US/docs/MDN/Community/Open_source_etiquette) - : This article gives guidance on how to behave when contributing to our open source project including rules for contributing, etiquette, and how to handle conflicts. - [Communication channels](/en-US/docs/MDN/Community/Communication_channels) - : This page lists communication channels used by the MDN team and our community, with hints on which might be best for you. - [Community Participation Guidelines (CPG)](/en-US/docs/MDN/Community/Community_Participation_Guidelines) - : This page lists all the Community Participation Guidelines we have that helps us foster a welcoming, inclusive, and safe community. It also covers [How to Report](/en-US/docs/MDN/Community/Community_Participation_Guidelines#reporting_process) a CPG Violation. ## Code of conduct By participating in and contributing to any of our projects in-person or online, you acknowledge that you have read and agree to the [Mozilla Community Participation Guidelines](/en-US/docs/MDN/Community/Community_Participation_Guidelines) (CPG). ## General support We are a small team working hard to keep up with the documentation demands of a continuously changing web ecosystem. Unfortunately, we can't help with general support questions, such as troubleshooting code. If you're learning, the [Learn web development](/en-US/docs/Learn_web_development) section is a great start, and you can post coding questions or search for answers on [Stack Overflow](https://stackoverflow.com/questions/). Issues, discussions, or pull requests on MDN repositories asking for general support will be directed here and may be closed and locked.