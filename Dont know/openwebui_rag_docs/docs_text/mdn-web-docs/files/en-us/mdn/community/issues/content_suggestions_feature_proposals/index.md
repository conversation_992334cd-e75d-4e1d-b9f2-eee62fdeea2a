Path: mdn-web-docs > files > en-us > mdn > community > issues > content_suggestions_feature_proposals > index.md

Path: mdn-web-docs > files > en-us > mdn > community > issues > content_suggestions_feature_proposals > index.md Path: mdn-web-docs > files > en-us > mdn > community > issues > content_suggestions_feature_proposals > index.md Path: mdn-web-docs > files > en-us > mdn > community > issues > content_suggestions_feature_proposals > index.md Path: mdn-web-docs > files > en-us > mdn > community > issues > content_suggestions_feature_proposals > index.md --- title: Proposing new content or features slug: MDN/Community/Issues/Content_suggestions_feature_proposals page-type: mdn-community-guide sidebar: mdnsidebar --- We are always interested in hearing from our community about new content or feature suggestions you may have for MDN Web Docs. However, even though we are open to suggestions, we have to keep the following in mind: - MDN Web Docs is run and managed by a small internal team. We also rely heavily on our partners and community to help us keep MDN Web Docs the best resource for web developers on the web. As such, we will sometimes have to say no to new content or features because we will simply be unable to maintain them long-term. - MDN Web Docs is also focused on documenting open web standards; some content might not be a good fit. This does not mean the idea or content is not good, just that MDN Web Docs is not the best place for it. Keeping that in mind, if you _do_ want to propose content or features for MDN Web Docs, please follow the below steps. ## Open a content suggestions and feature proposal issue When you go to open a [new issue](https://github.com/mdn/mdn/issues/new/choose), you will find a template called "New content or feature suggestions." This is the issue template to use when suggesting new content or features. The issue template does require quite a bit of information but is very deliberate. 1. It ensures we have all the information we need to review your proposal without much back and forth. 2. It helps you to think through your proposal as you complete the form. Once you have completed the form and submitted the issue, a core team member will get back to you within a week to two weeks, depending on the complexity of your proposal. ## Participate in the discussion and wait for approval If we feel the proposal might be a good fit, we will [start a discussion](https://github.com/orgs/mdn/discussions) on our MDN community discussions repository. This is to get input from our partners and the wider community. We encourage you to monitor the discussion and join in as appropriate. ## Opening an issue Suppose a consensus is reached that this is content we want to add or a feature we want to build. In that case, we will open an issue against the appropriate repository referencing the original proposal and the discussion and fill in any gaps so the issue is clearly actionable. ## Work is assigned At this point, the work will be prioritized and assigned to those responsible for ensuring it is implemented and reviewed. ## Open pull request Once the work is ready for review, a pull request should be opened, which again references the proposal, discussion, and issue. This ensures that we always have the full context of the work. Finally, the required people will be assigned, and the review process will start. ## Work is reviewed and merged Here again, depending on the complexity of the content or feature, the review stage can be lengthy. We ask for your patience and that you continue to be involved as appropriate. Once we have approval from at least two internal team members, we are ready to merge the pull request. This will conclude the entire process, and the content or feature will be available on MDN Web Docs.