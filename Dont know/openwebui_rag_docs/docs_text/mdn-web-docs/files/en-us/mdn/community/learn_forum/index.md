Path: mdn-web-docs > files > en-us > mdn > community > learn_forum > index.md

Path: mdn-web-docs > files > en-us > mdn > community > learn_forum > index.md Path: mdn-web-docs > files > en-us > mdn > community > learn_forum > index.md Path: mdn-web-docs > files > en-us > mdn > community > learn_forum > index.md Path: mdn-web-docs > files > en-us > mdn > community > learn_forum > index.md --- title: Learn forum slug: MDN/Community/Learn_forum page-type: mdn-community-guide sidebar: mdnsidebar --- Our [Learn web development](/en-US/docs/Learn_web_development) pages get over a million views per month, and has an [active forum](https://discourse.mozilla.org/c/mdn/learn/250) where people go to ask for assistance. We'd love some help with answering posts, and growing our learning community. ## What we need help with We receive a high volume of questions asking for help based on topics that appear on the Learn web development section on MDN. ## How you can benefit - Helping people with their code problems is a great way to learn more about web technologies as you research a solution and write an answer to someone's question, you will gain a deeper understanding of the subject, and improve your skills. - As you get more involved in the MDN community, you'll get to know Mozilla staff and other community members, giving you a valuable network of contacts for getting help with your own issues and increasing your visibility. - Helping to answer coding questions is largely its own reward, but it will also demonstrate your expertise in web technologies and possibly even help you with your course, or job opportunities. ## What skills you need - You should be knowledgeable in web technologies such as HTML, CSS, and JavaScript. Ideally you should also be good at explaining technical topics, and enjoy helping beginners learn to code. - The language of the forum is English you should have a reasonable proficiency with the English language, but it really doesn't need to be perfect. We have people from all over the world visiting our forums, and we want everyone who visits us to feel as comfortable and included as possible. ## How to help 1. Sign up for [Mozilla Discourse](https://discourse.mozilla.org/), if you haven't already. 2. Have a look at [Learn web development](/en-US/docs/Learn_web_development) section and gain a basic level of familiarity with what's there. After you are set up, have a look at the [learning forum](https://discourse.mozilla.org/c/mdn/learn/250) and see if there are any posts that have no replies this is the best place to start. If the post you are replying to is a general ask for help, reply to them, and give them as much help as you've got time for. If you need assistance with anything, ask for help in one of our [Communication channels](/en-US/docs/MDN/Community/Communication_channels). > [!NOTE] > Important: Above all, be patient, be friendly, be kind. Remember most of these folks are beginners.