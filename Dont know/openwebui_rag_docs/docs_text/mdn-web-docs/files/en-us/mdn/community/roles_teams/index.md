Path: mdn-web-docs > files > en-us > mdn > community > roles_teams > index.md

Path: mdn-web-docs > files > en-us > mdn > community > roles_teams > index.md Path: mdn-web-docs > files > en-us > mdn > community > roles_teams > index.md Path: mdn-web-docs > files > en-us > mdn > community > roles_teams > index.md Path: mdn-web-docs > files > en-us > mdn > community > roles_teams > index.md --- title: MDN Web Docs roles and teams short-title: Roles and teams slug: MDN/Community/Roles_teams page-type: mdn-community-guide sidebar: mdnsidebar --- The success and growth of the MDN Web Docs project is, in large part, due to our community of contributors. Some contributors have committed a portion of their time to assist with the daily tasks of running MDN Web Docs. Changes to the site, including maintenance tasks, are performed by employees, contractors, and a network of partners who are all dedicated to the health, growth, and maintenance of MDN Web Docs. The project relies heavily on [roles](#roles) and [teams](#teams) in the [MDN organization on GitHub](https://github.com/mdn) to manage and incorporate changes from these different groups. A list of the organization's current members can be found at [github.com/orgs/mdn](https://github.com/orgs/mdn/people). Community contributions help this open source project immensely. Contributors can use their work on MDN Web Docs to show their writing, technical, and collaboration skills, and the ability to work with people from diverse backgrounds. This section describes the roles you can take on while volunteering on the MDN Web Docs project. ## Roles In the MDN Web Docs project, you can take on the role of a [contributor](#contributor), an [organization member](#organization_member), a [maintainer](#maintainer), or an [owner](#owner). The progression from one role to the next is a step-by-step journey. With the advancement in your responsibilities, you could serve more than one role at the same time. Roles such as [invited expert](#invited_expert) can be directly obtained if you've demonstrated expertise in a particular area. Irrespective of the role you take on in this project, you are always a [contributor](#contributor). A **contributor** is the base role and all other roles are built on top of it. So while working on this project in any capacity, you must satisfy the requirements of the contributor role. ### Contributor Contributors, or _community participants_, add to the project with their time, skills, opinions, and ideas. Contributors work on the project directly and add value to it. Apart from writing and testing code, contributions include creating and updating documentation, researching, fixing bugs, and helping other community members. Depending on the frequency of your contributions, you can be someone who contributes occasionally or an active contributor. If you demonstrate a large impact on the project, you may be nominated as a [spotlight contributor](#spotlight_contributor) or be promoted to an [organization member](#organization_member). If you're new here and you would like to become a contributor, take a look at our [contribution guide](https://github.com/mdn/content/blob/main/CONTRIBUTING.md) and the [repositories in the MDN GitHub organization](https://github.com/orgs/mdn/repositories). As a contributor, you can get involved with the project by engaging in the following activities: - Participating in community discussions on the [communication channels](/en-US/docs/MDN/Community/Communication_channels). - Helping other contributors with their pull requests and issues or mentoring new contributors. - Submitting bug reports. Check out [the main repositories](/en-US/docs/MDN/Community/Our_repositories) for more information. - Commenting on issues to move conversations towards a fruitful resolution. - Addressing open issues (for example, in the [`content`](https://github.com/mdn/content/issues) repository) by submitting [pull requests](/en-US/docs/MDN/Community/Pull_requests). - Attending community events. - Helping to promote the MDN project. **Requirements:** To be a contributor, you must follow: - [Mozilla's code of conduct](https://www.mozilla.org/en-US/about/governance/policies/participation/) - Contribution guidelines (check the `CONTRIBUTING.md` file in each repository; for example, these are the [contribution guidelines](https://github.com/mdn/content/blob/main/CONTRIBUTING.md) for the `mdn/content` repository). **Privileges:** Contributors enjoy the following privileges: - Invitations to contributor events. - Eligibility to become an [organization member](#organization_member). ### Organization member [Organization members](https://github.com/orgs/mdn/people) are established [contributors](#contributor) who participate in and contribute to the MDN Web Docs project regularly. They are expected to act in the interest of the project. **Requirements:** To be an organization member, you must meet one or more of the following requirements: - Opened two or more pull requests that have been merged that resolve two or more issues. - Contributed to MDN Web Docs projects for at least two months. - Contributed actively to at least one project area. The following two requirements are mandatory: - Enabled [two-factor authentication](https://docs.github.com/en/authentication/securing-your-account-with-two-factor-authentication-2fa/configuring-two-factor-authentication) for your GitHub account. - Enabled [signed commits](https://docs.github.com/en/authentication/managing-commit-signature-verification/signing-commits). **Privileges:** Organization members have privileges at the [organization level](https://github.com/mdn) on GitHub. ### Maintainer Maintainers are established [contributors](#contributor) who are responsible for one or more projects on MDN. They are expected to participate in making decisions about the policies and priorities of the project. See the [process](#nominating_a_maintainer) for nominating someone as a maintainer. As a maintainer, you engage in the following activities: - Determining priorities for the project you are responsible for. - Participating in community meetings. - Mentoring new and existing contributors across all other roles. - Based on the skill set, proposing, approving, or implementing in your project area: - Code and infrastructure improvements - Content improvements - Process improvements <!-- TODO: Compare with https://developer.mozilla.org/en-US/docs/MDN/Community/Roles_teams#volunteer_and_partner_maintainers. What info needs to be retained? Should we use the term 'partner maintainer' or is 'maintainer' good enough? --> **Requirements:** To be eligible to be a maintainer, you must meet one or more of the following requirements: - Gained experience as an [invited expert](#invited_expert) for at least six months. - Demonstrated a broad knowledge of the project across multiple areas. - Demonstrated the ability to exercise judgment for the good of the project, independent of the influence of other members. - Exhibited the quality of mentoring other contributors. - Consented to commit spending at least 16 hours per month working on the project. - Attended the community meeting that takes place once every two months. > [!NOTE] > If there is someone you think is eligible for this role, you may [nominate a maintainer](#nominating_a_maintainer). **Privileges:** Maintainers have the permissions to approve and merge pull requests. ### Owner Owners have wide permissions to manage users and [GitHub teams](https://github.com/orgs/mdn/teams), maintain access across repositories in the [MDN organization](https://github.com/mdn), maintain repository settings, and deploy to production. Owners are bound by all the requirements of other contributor roles. > [!NOTE] > The role of an owner is currently limited to Mozilla staff. **Requirements:** In addition to the responsibilities of other contributor roles, owners have the following responsibilities: - Following and enforcing MDN team norms, including the [Community Participation Guidelines](https://www.mozilla.org/en-US/about/governance/policies/participation/) and [Mozilla Policies](https://www.mozilla.org/en-US/about/governance/policies/). - Following the MDN organization policies and leading by example. - Suggesting, documenting, and implementing new policies through the [pull request process](/en-US/docs/MDN/Community/Pull_requests). - Following and contributing to issues and discussions across the MDN organization. - Ensuring that an issue or pull request gets feedback from one or more members within one week. - [Archiving](https://docs.github.com/en/repositories/archiving-a-github-repository/archiving-repositories) or deleting unmaintained repositories. - Discussing GitHub features, selecting the ones to use, and documenting decisions. **Privileges:** Owners can: - Add and remove organization owners and members as needed. - Add and remove collaborators to specific repositories as needed. - Add repositories (as fresh projects or transfers) as needed. ### Summary of the roles | Role | Requirements | Privileges | | :---------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------- | | [**Contributor**](#contributor) | Follow the code of conduct and contribution guidelines | - Invitations to contributor events<br>- Eligibility to become an organization member | | [**Organization member**](#organization_member) | - Enable 2FA for GitHub account<br>- Enable signed commits<br><br>One or more of:<br>- Resolve two or more issues<br>- Contribute for at least two months<br>- Active contribution in a project area | Access rights at the organization level | | [**Maintainer**](#maintainer) | One or more of:<br>- Invited expert for at least six months<br>- Knowledge across multiple project areas<br>- Act towards overall health of the project<br>- Mentor other contributors<br>- Spend at least 16 hours per month on the project<br>- Attend community meetings | Approve and merge pull requests | | [**Owner**](#owner) | Limited to Mozilla staff | - Manage access of different roles to various repositories<br>- Add or archive repositories and projects | ## Special roles Some contributor roles have more nuanced responsibilities and have special eligibility conditions. These include [spotlight contributor](#spotlight_contributor), [invited expert](#invited_expert), and [community manager](#community_manager). ### Spotlight contributor Spotlight contributors are people who have gone above and beyond with their contributions to MDN Web Docs. Their contributions are in the form of pull requests to improve the project, helping community members on various [communication channels](/en-US/docs/MDN/Community/Communication_channels) or learn forums, or providing feedback on GitHub issues and pull requests. We feature a spotlight contributor on the [MDN website](/en-US/) once every month. See the [process](#nominating_a_spotlight_contributor) to nominate someone as a spotlight contributor. ### Invited expert Invited experts have a track record on MDN for their contributions, participation in discussions and reviews, or have proven knowledge in a certain area of expertise. Invited experts are responsible for a specific topic area or a component of the MDN project. They are responsible for reviewing and approving pull requests in their topic or project area, answering technical questions, and maintaining the general health of their particular project. See the [process](#nominating_an_invited_expert) to nominate someone as an invited expert. In addition to the responsibilities of an [organization member](#organization_member), invited experts are responsible for: - Following the [reviewing guide](https://github.com/mdn/content/blob/main/REVIEWING.md). - Reviewing pull requests in their topic area. - Helping other contributors become reviewers. Invited experts are automatically assigned for review when pull requests are opened in their topic area. If there is more than one expert in a topic area, they are assigned to pull requests using a [load-balancing strategy](https://docs.github.com/en/organizations/organizing-members-into-teams/managing-code-review-settings-for-your-team#about-auto-assignment). **Requirements:** To be eligible to be an invited expert, you must meet one or more of the following requirements: - Demonstrated an in-depth knowledge of a particular topic area. - Committed to being responsible for their assigned topic area. - Supported new and occasional contributors and helped to get pull requests ready to merge. - Attended the community meeting which takes place once every two months. **Privileges:** Invited experts get added to the [invited experts team](https://github.com/orgs/mdn/teams/invited-experts-and-co-maintainers) and to the appropriate topic or project team. Invited experts can: - Access the required repository for commits and pull request approvals and merges. - Recommend and vote for other members to become invited experts. - Attend weekly MDN Web Docs editorial call. ### Community manager Community managers have a distinct role in many respects. Community managers share many of the same responsibilities as a [maintainer](#maintainer). In addition, community managers have the following responsibilities: - Addressing reports of violation of our [code of conduct](https://github.com/mdn/mdn-community/blob/main/CODE_OF_CONDUCT.md) which is [Mozilla's Community Participation Guidelines](https://www.mozilla.org/en-US/about/governance/policies/participation/) and deciding on the appropriate action. - Organizing and running community events. - Organizing community-related project meetings. - Determining media strategies to promote the MDN project. - Defining and implementing the contributor onboarding experience. - Onboarding new contributors and users. - Ensuring the health and well-being of the MDN project and all participants. - Identifying and assisting with the implementation of automation to improve project sustainability. - Meeting and ensuring a healthy relationship with contributors and partners. - Assisting with issue triage and pull request review where appropriate. - Monitoring all the [communication channels](/en-US/docs/MDN/Community/Communication_channels). - Highlighting contributors that have done exceptional work and/or have shown dedication to the MDN project. ## Processes ### Nominating a maintainer See who can be a [maintainer](#maintainer). To nominate someone as a maintainer, open an issue on GitHub: 1. On the `Issues` tab in the `mdn/mdn` repository, click the [**New issue**](https://github.com/mdn/mdn/issues/new/choose) button on the right. 2. Under 'Nominate a maintainer', click the **Get started** button. 3. Fill in the form with details of the contributions of the person you are nominating and submit the form. <!-- TODO: Update links in these steps TODO: Edit the section after updating the links - The nominator will open a pull request using the appropriate [template](https://github.com/mdn/mdn-community/roles/) against the [MDN Web Docs community repository](https://github.com/mdn/mdn-community/). - The nominee will add a comment to the pull request agreeing to all responsibilities of becoming a Maintainer. - At least three current Maintainers must approve the pull request. - Once the pull request is approved, the new Maintainer is added to the [Maintainers team](https://github.com/orgs/mdn/teams/maintainers) and any other relevant topic or project teams. --> ### Nominating a spotlight contributor See who can be a [spotlight contributor](#spotlight_contributor). To nominate someone as a spotlight contributor, open an issue on GitHub: 1. On the `Issues` tab in the `mdn/mdn` repository, click the [**New issue**](https://github.com/mdn/mdn/issues/new/choose) button on the right. 2. Under 'Nominate a spotlight contributor', click the **Get started** button. 3. Fill in the form with details of the contributions of the person you are nominating and submit the form. The MDN team will get in touch with the nominated person to get their information to be published on the [website](/en-US/) under "Contributor Spotlight". ### Nominating an invited expert See who can be an [invited expert](#invited_expert). To nominate someone as an invited expert, open an issue on GitHub: 1. On the `Issues` tab in the `mdn/mdn` repository, click the [**New issue**](https://github.com/mdn/mdn/issues/new/choose) button on the right. 2. Under 'Nominate an invited expert', click the **Get started** button. 3. Fill in the form with details of the contributions of the person you are nominating and submit the form. <!-- TODO: Revisit this PR process - The nominator will open a pull request using the appropriate [template](https://github.com/mdn/mdn-community/roles/) against the [MDN Web Docs community repository](https://github.com/mdn/mdn-community/). - The nominee will add a comment to the pull request agreeing to all responsibilities of becoming an Invited Expert. - To be accepted, the pull request needs three approvals. This can be any combination of Invited Experts, or Maintainers. - Once the pull request is approved, the new Invited Expert is added to the [invited-experts team](https://github.com/orgs/mdn/teams/invited-experts) and the appropriate topic team. --> ### Stepping down or applying for emeritus status Life happens and your commitment levels as a contributor could change over time. Depending on your situation, you might want to: - Take a break from the project temporarily. - Downgrade to a less-demanding role. - Step away from the project completely (apply for an emeritus status). In all these situations, feel free to discuss your situation and current commitment levels with the [MDN team](#contact_the_mdn_team). ### Demoting or removing inactive contributors A contributor can be demoted or removed as a contributor when responsibilities and requirements aren't being met, including repeated patterns of inactivity or a violation of the [code of conduct](https://github.com/mdn/mdn-community/blob/main/CODE_OF_CONDUCT.md). Demotion or removal of a contributor is proposed by a participant during a maintainers meeting. The participant provides supporting information for the demotion or removal request. After discussion, maintainers and community managers vote on the matter to make a decision. Removing inactive contributors protects the project and its deliverables and also opens up opportunities for new contributors to step in. We define inactivity as: - No contributions to the project for at least six months. - No response to communication for at least three months. Inactivity harms the project; it may lead to unexpected delays, contributor attrition, and a loss of trust in the project. Contributors must be active to set an example and show commitment to the project. Please communicate with the community team to avoid demotion or removal should your time commitments change; instead you can proactively choose to [step down for a while or move to emeritus status](#stepping_down_or_applying_for_emeritus_status). ## Teams We manage teams using the [GitHub teams](https://docs.github.com/en/organizations/organizing-members-into-teams/about-teams) feature. When you are added to a team, it means that you have communicated your intent to be more closely involved in the project. This also means that you have some additional responsibilities and rights, as explained below: - A person on a team is commonly added to the [CODEOWNERS](https://github.com/mdn/content/blob/main/.github/CODEOWNERS) file for their respective topic area(s) of interest. - When a pull request touches files in your area of responsibility, based on the CODEOWNERS file, you will be added as a reviewer to a pull request automatically using GitHub's [load-balancing algorithm](https://docs.github.com/en/organizations/organizing-members-into-teams/managing-code-review-settings-for-your-team#routing-algorithms). - Members of a team have a higher-level repository access. Repository permissions are assigned to only those repositories where a member needs access. The teams in the [MDN GitHub organization](https://github.com/orgs/mdn/teams) include: - `@Core`: Core MDN Web Docs team - `@mdn-community-engagement`: People responsible for community engagement across our repositories - `@mdn-product`: People responsible for the MDN Plus product - `@localization-team-leads`: People who lead our individual localization teams - `@OWD`: Contributors from the Open Web Docs non-profit organization - `@sre`: Site reliability engineers who support MDN Web Docs - `@yari-content`: The umbrella team for all MDN Web Docs content reviewers - There is a subteam for the different topic areas accessibility, Add-ons, CSS, HTML, HTTP, JavaScript, SVG, Web API, and WebAssembly. For example, there's `@yari-content-css` and `@yari-content-svg`. - There are also subteams for different languages Brazilian Portuguese, Chinese, French, Japanese, Korean, Russian, and Spanish. For example, there's `@yari-content-fr` and `@yari-content-ko`. To become a member of a team, you must: - Agree to abide by our [Community Participation Guidelines](https://www.mozilla.org/en-US/about/governance/policies/participation/). - Agree to Mozilla's [Commit Access Requirements](https://www.mozilla.org/en-US/about/governance/policies/commit/requirements/). - Set up [two-factor authentication](https://docs.github.com/en/authentication/securing-your-account-with-two-factor-authentication-2fa/configuring-two-factor-authentication) (2FA) on your GitHub account. <!-- TODO: Update this process because currently there is no issue category on mdn/mdn to request for getting added to a team If you wish to become a member of a team, you should start by [filing an issue in this repository](https://github.com/mdn/mdn/issues/new/choose). After you have filed your issue, someone from our team will review it and give you the necessary privileges, provided our requirements are satisfied. --> ## Contact the MDN team For inquiries and feedback, please reach out to mdn-web-docs-community (at) mozilla (.com).