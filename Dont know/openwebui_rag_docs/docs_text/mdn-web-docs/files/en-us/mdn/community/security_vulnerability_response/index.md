Path: mdn-web-docs > files > en-us > mdn > community > security_vulnerability_response > index.md

Path: mdn-web-docs > files > en-us > mdn > community > security_vulnerability_response > index.md Path: mdn-web-docs > files > en-us > mdn > community > security_vulnerability_response > index.md Path: mdn-web-docs > files > en-us > mdn > community > security_vulnerability_response > index.md Path: mdn-web-docs > files > en-us > mdn > community > security_vulnerability_response > index.md --- title: Security vulnerability response steps slug: MDN/Community/Security_vulnerability_response page-type: mdn-community-guide sidebar: mdnsidebar --- ## A little history On ~27 November 2018 an npm security vulnerability was announced for all users that depend, either directly or indirectly, on the [event-stream](https://snyk.io/blog/malicious-code-found-in-npm-package-event-stream/) package. It was a very targeted attack, that only activated if the Copay bitcoin wallet was installed, whereupon it tried to steal the contents. Two of our projects, namely [interactive-examples](https://github.com/mdn/interactive-examples/) and [BoB](https://github.com/mdn/bob/), depend on an npm package called [npm-run-all](https://www.npmjs.com/package/npm-run-all), which in turn depended on the event-stream package. This meant that not only was staff at risk, but people who have forked either of these repositories might have been affected as well. Thankfully the maintainers of the affected package reacted swiftly and released an update to address the issue. Because we have the [Renovate bot](https://github.com/marketplace/renovate) running against these repositories, there was a [pull request](https://github.com/mdn/interactive-examples/pull/1239/) ready to merge. This only resolved one part of the problem though. Our users still needed to be notified. ## Steps taken The community for especially the interactive-examples project was rather large, and not everyone active, but we still needed a way to reach out to everyone. The first step was then to open an issue against each of the repositories detailing the problem: - [interactive-examples](https://github.com/mdn/interactive-examples/issues/1242) - [bob](https://github.com/mdn/bob/issues/184) That by itself is not enough as users do not necessarily actively monitor issues. We, therefore, needed to look at all of the [forks](https://github.com/mdn/interactive-examples/network/members) of the project. We then copied all of the usernames for these users and pinged them on the above issue, for example by [commenting](https://github.com/mdn/interactive-examples/issues/1242#issuecomment-442110598) in it. This was very effective, judging from the responses the issue received, but we could not leave it there. The next step was to post a comment on each of the open pull requests informing the user of the problem and what their next steps should be. Here is an [example](https://github.com/mdn/interactive-examples/pull/1144) of our answer. With this, we felt rather confident that between us reaching out, and coverage of the issue online by npm and other channels, would ensure that we ensured our users are safe. As a final step, we tweeted from our official Twitter account to raise awareness of the issue. ### In closing Hopefully, these types of incidents will be few and far between. Should this happen again however, the above provides a solid guideline on how to respond.