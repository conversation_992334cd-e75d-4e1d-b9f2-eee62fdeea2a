Path: mdn-web-docs > files > en-us > mdn > guides > index.md

Path: mdn-web-docs > files > en-us > mdn > guides > index.md Path: mdn-web-docs > files > en-us > mdn > guides > index.md Path: mdn-web-docs > files > en-us > mdn > guides > index.md Path: mdn-web-docs > files > en-us > mdn > guides > index.md --- title: Web developer guides short-title: Guides slug: MDN/Guides page-type: landing-page sidebar: mdnsidebar --- There are many guides on MDN Web Docs that teach you how to use features or APIs through practical examples. This page is a curated list of some of the most popular goal-oriented learning material. ## HTML - [Structuring the web with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) - : The HTML learning area offers tutorials to help you learn HTML from the ground up. - [HTML basics](/en-US/docs/Learn_web_development/Getting_started/Your_first_website/Creating_the_content) - : This article will give you a basic understanding of HTML. After following this guide, you can further explore the material in the HTML Learning Area. ## CSS - [Learn to style HTML using CSS](/en-US/docs/Learn_web_development/Core/Styling_basics) - : Our complete CSS tutorial, taking you from first steps through styling text, creating layouts, and more. - [CSS Layout Guides](/en-US/docs/Web/CSS/Guides) - : There are a large number of guides to CSS Layout across MDN, this page collects them all together. - [Using CSS animations](/en-US/docs/Web/CSS/CSS_animations/Using_CSS_animations) - : CSS animations make it possible to animate transitions from one CSS style configuration to another. This guide will help you get started with the animation properties. ## JavaScript - [JavaScript learning area](/en-US/docs/Learn_web_development/Core/Scripting) - : Whether you are a complete beginner, or hoping to refresh your skills, this is the place to start. ## Media - [Audio and video delivery](/en-US/docs/Web/Media/Guides/Audio_and_video_delivery) - : We can deliver audio and video on the web in several ways, ranging from 'static' media files to adaptive live streams. This article is intended as a starting point for exploring the various delivery mechanisms of web-based media and compatibility with popular browsers. - [Audio and video manipulation](/en-US/docs/Web/Media/Guides/Audio_and_video_manipulation) - : The beauty of the web is that you can combine technologies to create new forms. Having native audio and video in the browser means we can use these data streams with technologies such as {{htmlelement("canvas")}}, [WebGL](/en-US/docs/Web/API/WebGL_API) or [Web Audio API](/en-US/docs/Web/API/Web_Audio_API) to modify audio and video directly, for example adding reverb/compression effects to audio, or grayscale/sepia filters to video. This article provides a reference to explain what you need to do. ## APIs - [Using FormData objects](/en-US/docs/Web/API/XMLHttpRequest_API/Using_FormData_Objects) - : The [`FormData`](/en-US/docs/Web/API/FormData) object lets you compile a set of key/value pairs to send using {{domxref("Window/fetch", "fetch()")}}. It's primarily intended for sending form data, but can be used independently of forms to transmit keyed data. The transmission is in the same format that the form's `submit()` method would use to send the data if the form's encoding type were set to "multipart/form-data". - [Progressive web apps](/en-US/docs/Web/Progressive_web_apps) - : Progressive web apps (PWAs) use modern web APIs along with traditional progressive enhancement strategy to create cross-platform web applications. These apps work everywhere and provide several features that give them the same user experience advantages as native apps. This set of guides tells you all you need to know about PWAs. - [Parsing and serializing XML](/en-US/docs/Web/XML/Guides/Parsing_and_serializing_XML) - : The web platform provides different methods of parsing and serializing XML, each with its pros and cons. ## Performance - [Optimization and performance](/en-US/docs/Web/Performance) - : When building modern web apps and sites, it's important to make your content work quickly and efficiently. This lets it perform effectively for both powerful desktop systems and weaker handheld devices. ## Mobile web development - [Learn: Responsive design](/en-US/docs/Learn_web_development/Core/CSS_layout/Responsive_Design) - : This article provides an overview of some main techniques needed to design websites that work well on mobile devices. ## Fonts - [Variable fonts guide](/en-US/docs/Web/CSS/CSS_fonts/Variable_fonts_guide) - : Find out how to use variable fonts in your designs. - [The Web Open Font Format (WOFF)](/en-US/docs/Web/CSS/CSS_fonts/WOFF) - : WOFF (Web Open Font Format) is a font file format that is free for anyone to use on the web. ## User interface development - [User input methods and controls](/en-US/docs/Learn_web_development/Extensions/Forms/User_input_methods) - : User input goes beyond just a mouse and keyboard: think of touchscreens for example. This article provides recommendations for managing user input and implementing controls in open web apps, along with FAQs, real-world examples, and links to further information for anyone needing more detailed information on the underlying technologies.