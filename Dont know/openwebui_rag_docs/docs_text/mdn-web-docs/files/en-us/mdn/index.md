Path: mdn-web-docs > files > en-us > mdn > index.md

Path: mdn-web-docs > files > en-us > mdn > index.md Path: mdn-web-docs > files > en-us > mdn > index.md Path: mdn-web-docs > files > en-us > mdn > index.md Path: mdn-web-docs > files > en-us > mdn > index.md --- title: MDN Web Docs slug: MDN page-type: landing-page sidebar: mdnsidebar --- **MDN Web Docs** is a free-to-use resource that documents open web technologies. Our mission is to provide developers with the information they need to easily build projects on the web platform. The resources below describe how the site works, how we write documentation, the guidelines and conventions we follow, and how you can get involved. - [Web development tutorials](/en-US/docs/MDN/Tutorials) - : A curated list of tutorials on MDN Web Docs and learning materials for beginners, intermediate-level, or expert web developers. Explore detailed tutorials for web technologies like CSS, JavaScript, HTML, and more. - [Developer guides](/en-US/docs/MDN/Guides) - : A collection of focused, goal-oriented guides that teach you how to use features or APIs through practical examples. These guides provide hands-on explanations to help you understand and apply new concepts. - [MDN Web Docs Community](/en-US/docs/MDN/Community) - : These pages describe how to get started contributing to MDN Web Docs. You can find out where to look for answers, how to work on GitHub issues, open discussions, or suggest new content. If you need help or want to get in touch with us, you'll find all the information here. - [Writing guidelines](/en-US/docs/MDN/Writing_guidelines) - : These guides describe how to write for MDN Web Docs. They contain the editorial policies defining the types of content we write and the types of content we don't write. You'll find our writing style guide, how-to guides for specific tasks, and information about the structure of different types of pages. - [About MDN Web Docs](/en-US/about) - : MDN Web Docs serves millions of readers every month and connects developers with the tools and information they need to easily build projects on the open web. Learn about the history of MDN, the team working on MDN Web Docs, our values, and our partners.