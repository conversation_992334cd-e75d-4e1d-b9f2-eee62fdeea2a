Path: mdn-web-docs > files > en-us > mdn > tutorials > index.md

Path: mdn-web-docs > files > en-us > mdn > tutorials > index.md Path: mdn-web-docs > files > en-us > mdn > tutorials > index.md Path: mdn-web-docs > files > en-us > mdn > tutorials > index.md Path: mdn-web-docs > files > en-us > mdn > tutorials > index.md --- title: Web development tutorials short-title: Tutorials slug: MDN/Tutorials page-type: landing-page sidebar: mdnsidebar --- The links on this page lead to a variety of tutorials and learning materials. Whether you are a beginner, intermediate, or expert at web development, you will find something helpful here. These tutorials are created by forward-thinking companies and web developers who have embraced open standards and good practices, and allow or provide translations through an open content license such as Creative Commons. ## For complete beginners - [Getting started with the Web](/en-US/docs/Learn_web_development/Getting_started/Your_first_website) - : _Getting started with the Web_ is a concise series introducing you to the practicalities of web development. You'll set up the tools you need to construct a basic webpage and publish your own code. ## HTML tutorials ### Introduction to HTML - [Structuring content with HTML](/en-US/docs/Learn_web_development/Core/Structuring_content) - : Our introductory HTML learning module teaches HTML from the ground up no previous knowledge required. It sets the stage, getting you used to important concepts and syntax, looking at applying HTML to text, how to create hyperlinks, and how to use HTML to structure a webpage. - [HTML for Beginners](https://www.theblogstarter.com/html-for-beginners/) - : An HTML guide for beginners that includes explanations of common tags, including HTML tags. Also includes a step-by-step guide to creating a basic web page with code examples. - [HTML Challenges](https://en.wikiversity.org/wiki/Web_Design/HTML_Challenges) - : Use these challenges to hone your HTML skills (for example, "Should I use an `<h2>` element or a `<strong>` element?"), focusing on meaningful markup. ### Advanced HTML topics - [Web forms](/en-US/docs/Learn_web_development/Extensions/Forms) - : Forms are a very important part of the Web these provide much of the functionality you need for interacting with websites, such as registering and logging in, sending feedback, buying products, and more. This module gets you started with creating the client-side parts of forms. - [Tips for authoring fast-loading HTML pages](/en-US/docs/Web/HTML/How_to/Author_fast-loading_HTML_pages) - : Optimize web pages to provide a more responsive site for visitors and reduce the load on your web server and Internet connection. ## CSS tutorials ### Introduction to CSS - [CSS basics](/en-US/docs/Learn_web_development/Getting_started/Your_first_website/Styling_the_content) - : CSS (Cascading Style Sheets) is the code you use to style your webpage. _CSS Basics_ takes you through what you need to get started. We'll answer questions like: How do I make my text black or red? How do I make my content show up in such-and-such a place on the screen? How do I decorate my webpage with background images and colors? - [CSS Styling basics](/en-US/docs/Learn_web_development/Core/Styling_basics) - : CSS (Cascading Style Sheets) is used to style and lay out web pages for example, to alter the font, color, size, and spacing of your content, split it into multiple columns, or add animations and other decorative features. This module provides a gentle beginning to your path toward CSS mastery with the basics of how it works, what the syntax looks like, and how you can start using it to add styling to HTML. - [Selectors](/en-US/docs/Learn_web_development/Core/Styling_basics/Basic_selectors) - : Target HTML elements, including based on element state, with CSS. - [Specificity](/en-US/docs/Web/CSS/CSS_cascade/Specificity) - : Understanding the browser algorithm to determine which CSS declarations get applied to an element when there are competing declarations, with a [specificity quiz](https://estelle.github.io/CSS/selectors/exercises/specificity.html). - [Handling conflicts](/en-US/docs/Learn_web_development/Core/Styling_basics/Handling_conflicts) - : The cascade, specificity, and inheritance control how CSS is applied to HTML and how conflicts between style declarations are resolved. - [CSS text Styling](/en-US/docs/Learn_web_development/Core/Text_styling) - : Here we look at text styling fundamentals, including setting font, boldness, and italics, line and letter spacing, and drop shadows and other text features. We round off the module by looking at applying custom fonts to your page, and styling lists and links. - [Solve common CSS problems](/en-US/docs/Learn_web_development/Howto/Solve_CSS_problems/CSS_FAQ) - : Common questions and answers for beginners. ### Intermediate CSS topics - [CSS layout](/en-US/docs/Learn_web_development/Core/CSS_layout) - : At this point we've already looked at CSS fundamentals, how to style text, and how to style and manipulate the boxes that your content sits inside. Now it's time to look at how to place your boxes in the right place in relation to the viewport, and one another. We have covered the necessary prerequisites so can now dive deep into CSS layout, looking at different display settings, traditional layout methods involving float and positioning, and new fangled layout tools like flexbox. - [CSS reference](/en-US/docs/Web/CSS/Reference) - : Complete reference to CSS, with details on support by Firefox and other browsers. - [Fluid Grids](https://alistapart.com/article/fluidgrids/) - : Design layouts that fluidly resize with the browser window, while still using a typographic grid. - [CSS Challenges](https://en.wikiversity.org/wiki/Web_Design/CSS_challenges) - : Flex your CSS skills, and see where you need more practice. ### Advanced CSS topics - [Using CSS transforms](/en-US/docs/Web/CSS/CSS_transforms/Using_CSS_transforms) - : Apply rotation, skewing, scaling, and translation using CSS. - [CSS transitions](/en-US/docs/Web/CSS/CSS_transitions/Using_CSS_transitions) - : CSS transitions provide a way to animate changes to CSS properties, instead of having the changes take effect instantly. - [Canvas tutorial](/en-US/docs/Web/API/Canvas_API/Tutorial) - : Learn how to draw graphics using scripting using the canvas element. ## JavaScript tutorials ### Introduction to JavaScript - [Dynamic scripting with JavaScript](/en-US/docs/Learn_web_development/Core/Scripting) - : In this module, we continue our coverage of all JavaScript's key fundamental features, turning our attention to commonly-encountered types of code blocks such as conditional statements, loops, functions, and events. You've seen this stuff already in the course, but only in passing here we'll discuss it all explicitly. - [Getting started with JavaScript](/en-US/docs/Learn_web_development/Getting_started/Your_first_website/Adding_interactivity) - : What is JavaScript and how can it help you? - [Codecademy](https://www.codecademy.com/) - : Codecademy is an easy way to learn how to code JavaScript. It's interactive and you can do it with your friends. - [freeCodeCamp](https://www.freecodecamp.org/) - : freeCodeCamp teaches a variety of languages and frameworks for web development. It also has a [forum](https://forum.freecodecamp.org/), an [internet radio station](https://coderadio.freecodecamp.org/), and a [blog](https://www.freecodecamp.org/news). ### Intermediate JavaScript topics - [Introducing JavaScript objects](/en-US/docs/Learn_web_development/Extensions/Advanced_JavaScript_objects) - : In JavaScript, most things are objects, from core JavaScript features like strings and arrays to the browser APIs built on top of JavaScript. You can even create your own objects to encapsulate related functions and variables into efficient packages. The object-oriented nature of JavaScript is important to understand if you want to go further with your knowledge of the language and write more efficient code, therefore we've provided this module to help you. Here we teach object theory and syntax in detail, look at how to create your own objects, and explain what JSON data is and how to work with it. - [Client-side web APIs](/en-US/docs/Learn_web_development/Extensions/Client-side_APIs) - : When writing client-side JavaScript for websites or applications, you won't go very far before you start to use APIs interfaces for manipulating different aspects of the browser and operating system the site is running on, or even data from other websites or services. In this module, we will explore what APIs are, and how to use some of the most common APIs you'll come across often in your development work. - [Eloquent JavaScript](https://eloquentjavascript.net/) - : A comprehensive guide to intermediate and advanced JavaScript methodologies. - [Speaking JavaScript](https://exploringjs.com/es5/) - : For programmers who want to learn JavaScript quickly and properly, and for JavaScript programmers who want to deepen their skills and/or look up specific topics. - [Essential JavaScript Design Patterns](https://addyosmani.com/resources/essentialjsdesignpatterns/book/) - : An introduction to essential JavaScript design patterns. - [JavaScript.info - The Modern JavaScript Tutorial](https://javascript.info/) - : Part 1: The Language. Part 2: Working with Browsers. ### Advanced JavaScript topics - [JavaScript Guide](/en-US/docs/Web/JavaScript/Guide) - : A comprehensive, regularly updated guide to JavaScript for all levels of learning from beginner to advanced. - [You Don't Know JS](https://github.com/getify/You-Dont-Know-JS) - : A series of books diving deep into the core mechanisms of the JavaScript language. - [JavaScript Garden](https://github.com/BonsaiDen/JavaScript-Garden) - : Documentation of the most quirky parts of JavaScript. - [Exploring ES6](https://exploringjs.com/es6/) - : Reliable and in-depth information on ECMAScript 2015. - [JavaScript Patterns](https://github.com/chuanxshi/javascript-patterns) - : A JavaScript pattern and anti-pattern collection that covers function patterns, jQuery patterns, jQuery plugin patterns, design patterns, general patterns, literals and constructor patterns, object creation patterns, code reuse patterns, DOM. - [How browsers work](https://web.dev/articles/howbrowserswork) - : A detailed research article describing different modern browsers, their engines, page rendering etc. - [JavaScript Videos](https://github.com/bolshchikov/js-must-watch) - : A collection of JavaScript videos to watch. ### Extension Development - [WebExtensions](/en-US/docs/Mozilla/Add-ons/WebExtensions) - : WebExtensions is a cross-browser system for developing browser add-ons. To a large extent, the system is compatible with the [extension API](https://developer.chrome.com/docs/extensions/reference/) supported by Google Chrome and Opera. Extensions written for these browsers will in most cases run in Firefox or [Microsoft Edge](https://learn.microsoft.com/en-us/archive/microsoft-edge/legacy/developer/) with [just a few changes](https://extensionworkshop.com/documentation/develop/porting-a-google-chrome-extension/). The API is also fully compatible with [multiprocess Firefox](https://wiki.mozilla.org/Firefox/multiprocess).