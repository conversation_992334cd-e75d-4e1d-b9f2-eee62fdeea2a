Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > attrib_copyright_license > index.md

Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > attrib_copyright_license > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > attrib_copyright_license > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > attrib_copyright_license > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > attrib_copyright_license > index.md --- title: Attribution and copyright licensing slug: MDN/Writing_guidelines/Attrib_copyright_license page-type: mdn-writing-guide sidebar: mdnsidebar --- MDN Web Docs content is available free of charge and is available under various open-source licenses. ## Using MDN Web Docs content This section covers the types of content we provide and the copyrights and licenses that are in effect for each type if you choose to reuse any of it. ### Documentation > [!NOTE] > The content on MDN Web Docs has been prepared with the contributions of authors from both inside and outside Mozilla. Unless otherwise indicated, the content is available under the terms of the [Creative Commons Attribution-ShareAlike license](https://creativecommons.org/licenses/by-sa/2.5/) (CC-BY-SA), v2.5 or any later version. Your reuse of the content here is published under the same license as the original content CC-BY-SA v2.5 or any later version. When reusing the content on MDN Web Docs, you need to ensure that [attribution is given](https://creativecommons.org/licenses/by/2.5/deed.en#ref-appropriate-credit) to the material as well as to "Mozilla Contributors". Good attribution is the **title** of the document, with a hyperlink (online) or URL (in print) to the specific page of the content being sourced, and any modifications you've made briefly described. For example, to provide attribution for this page, you can write: > ["Attributions and copyright licensing"](/en-US/docs/MDN/Writing_guidelines/Attrib_copyright_license) by Mozilla Contributors, licensed under [CC-BY-SA 2.5](https://creativecommons.org/licenses/by-sa/2.5/). You may also want to link "Mozilla Contributors" to a `contributors.txt` file linked in the page footer you're referencing for a list of authors, if reasonable. See [Recommended practices for attribution](https://wiki.creativecommons.org/wiki/Recommended_practices_for_attribution) for more details. ### Code samples Code samples added on or after August 20, 2010 are in the [public domain CC0](https://creativecommons.org/publicdomain/zero/1.0/). No licensing notice is necessary but if you need one, you can use: `Any copyright is dedicated to the Public Domain: https://creativecommons.org/publicdomain/zero/1.0/` Code samples added before August 20, 2010 are available under the [MIT license](https://opensource.org/license/mit); you should insert the following attribution information into the MIT template: " \<date of last wiki page revision> \<name of person who put it in the wiki>". Since the launch of the new Yari MDN platform on December 14 2020, there is currently no way to determine which one you need. We are working on this and will update this content soon. <!--do we still need this here?--> ### Your contributions If you wish to contribute to MDN Web Docs, you agree that your documentation is available under the Attribution-ShareAlike license (or occasionally an alternative license already specified by the page you are editing) and that your code samples are available under [Creative Commons CC-0](https://creativecommons.org/publicdomain/zero/1.0/) (a Public Domain dedication). > [!WARNING] > No new pages may be created using alternate licenses. **Copyright for contributed materials remains with the author unless the author assigns it to someone else.** If you have any questions or concerns about anything discussed here, please contact the [MDN Web Docs team](/en-US/docs/MDN/Community/Communication_channels). ### Logos, trademarks, service marks, and wordmarks The rights in the logos, trademarks, and service marks of the Mozilla Foundation, as well as the look and feel of this website, are not licensed under the Creative Commons license, and to the extent they are works of authorship (like logos and graphic design), they are not included in the work that is licensed under those terms. If you use the text of documents and wish to also use any of these rights, or if you have any other questions about complying with our licensing terms for this collection, you should contact the Mozilla Foundation here: [<EMAIL>](mailto:<EMAIL>). ## Using content from elsewhere on MDN Web Docs In general, we do not approve of copying content from other sources and putting it on MDN. MDN should be made up of original content wherever possible. If we receive a pull request and discover that it contains plagiarized content, we will close it and request that the submitter resubmit the change with the content rewritten into their own words. ### Reusing or republishing your content on MDN > [!NOTE] > Unless there is a good reason to republish the content, we will probably say "no". > The MDN writing team's decision is final. If someone wants to donate an article to MDN that they previously published on their blog or it makes sense to copy a complex reference sheet to MDN, there may be justification for republishing it. For these cases, discuss your plan with the MDN team beforehand: - [Create a GitHub issue](https://github.com/mdn/mdn/issues/new/choose) that explains your intention. - Describe what you would like to copy or republish. - Provide a URL to the resource. - Explain why you think it's appropriate. **If the content is published under a closed license:** - If you hold the rights to the content, state this and your express agreement to republish it on MDN. - If you do not hold the rights to the content, include the author/publisher on the issue if possible, or include details of how they could be contacted so we can ask them for permission to republish the content. **If the content is published under an open license:** - Say what it is, and link to the license so we can check whether it is compatible with [MDN's license](https://github.com/mdn/content/blob/main/LICENSE.md). ## Linking to MDN Web Docs articles We regularly get users asking us questions about how to link to MDN Web Docs and whether or not it is even allowed. The short answer is: **yes, you can link to MDN Web Docs!** Not only is the hypertext link the essence of the web, it is both a way to point your users to valuable resources as well as a show of trust toward the work our community does.