Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > index.md

Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > index.md Path: mdn-web-docs > files > en-us > mdn > writing_guidelines > index.md --- title: Writing guidelines slug: MDN/Writing_guidelines page-type: mdn-writing-guide sidebar: mdnsidebar --- MDN Web Docs is an open-source project. The sections outlined below describe our guidelines for _what_ we document and _how_ we do it on MDN Web Docs. To learn about _how to contribute_, see our [contribution guidelines](/en-US/docs/MDN/Community). - [What we write](/en-US/docs/MDN/Writing_guidelines/What_we_write) - : This section covers what we include on MDN Web Docs and what we don't, as well as a number of other policies, such as when we write about new technologies, the content suggestion process, and whether we accept external links. This is a good place to start if you're considering writing or updating content for us. This section also includes: - [Inclusion criteria](/en-US/docs/MDN/Writing_guidelines/Criteria_for_inclusion) - : Provides an in-depth criteria for content to be included on MDN Web Docs, the application process for getting new documentation added on MDN Web Docs, and the expectations and guidelines for a party applying. - [Our writing style guide](/en-US/docs/MDN/Writing_guidelines/Writing_style_guide) - : The writing style guide covers the language and style we use to write on MDN Web Docs. It also covers how to [format code examples](/en-US/docs/MDN/Writing_guidelines/Code_style_guide). - [Learn web development writing guidelines](/en-US/docs/MDN/Writing_guidelines/Learning_content) - : The [Learn web development](/en-US/docs/Learn_web_development) section of MDN is aimed specifically at folks learning the basic fundamentals of web development, and as such, requires a different approach to the rest of MDN's content. This articles provides guidelines for writing learning content. - [How to write for MDN Web Docs](/en-US/docs/MDN/Writing_guidelines/Howto) - : This section covers all the information for creating and editing pages, including certain processes and techniques we adhere to. This section provides information about getting started, a general overview into how pages are structured, and where to find how-tos on specific tasks. This section includes topics such as: - [How to research a technology](/en-US/docs/MDN/Writing_guidelines/Howto/Research_technology) - : This section provides some handy tips for researching a technology you are documenting. - [How to create, move, and delete pages](/en-US/docs/MDN/Writing_guidelines/Howto/Creating_moving_deleting) - : This section explains how we create, move, or delete a page on MDN Web Docs. It also explains how we redirect a page when moving or deleting the page. - [How to use markdown](/en-US/docs/MDN/Writing_guidelines/Howto/Markdown_in_MDN) - : The markdown format we use derives from [GitHub flavored markdown (GFM)](https://github.github.com/gfm/). This section is a guide to the markdown we use on MDN Web Docs, including formats for specific in-page components, such as notes and definition lists. - [Adding images and media](/en-US/docs/MDN/Writing_guidelines/Howto/Images_media) - : This section describes the requirements for including media in pages, such as images. - [How to document a CSS property](/en-US/docs/MDN/Writing_guidelines/Howto/Document_a_CSS_property) - : This article explains how to write a CSS property page, including layout and content. - [How to document an API reference](/en-US/docs/MDN/Writing_guidelines/Howto/Write_an_api_reference) - : This section explains how to approach documenting a Web API. - [How to document an HTTP header](/en-US/docs/MDN/Writing_guidelines/Howto/Document_an_HTTP_header) - : This article explains how to create a new reference page for an HTTP header. - [How to add an entry to the glossary](/en-US/docs/MDN/Writing_guidelines/Howto/Write_a_new_entry_in_the_glossary) - : This article explains how to add and link to entries in the MDN Web Docs glossary. It also provides guidelines about glossary entry layout and content. - [Page types on MDN Web Docs](/en-US/docs/MDN/Writing_guidelines/Page_structures/Page_types) - : Each page on MDN Web Docs has a specific page type, whether that's a CSS reference page or a JavaScript guide page. This section lists the different page types and provides templates for each type. It's a good idea to browse these to understand which page type you are writing. - [Page structures on MDN Web Docs](/en-US/docs/MDN/Writing_guidelines/Page_structures) - : This section covers the various page structures that we use to provide consistent presentation of information on MDN Web Docs. This includes: - [Syntax sections](/en-US/docs/MDN/Writing_guidelines/Page_structures/Syntax_sections) - : The syntax section of a reference page on MDN Web Docs contains a syntax box defining the exact syntax of a feature. This article explains how to write syntax boxes for reference articles. - [Code examples](/en-US/docs/MDN/Writing_guidelines/Page_structures/Code_examples) - : There are a lot of different ways to include code examples on pages. This section outlines them and provides syntax guidelines for the different languages. - [Banners and notices](/en-US/docs/MDN/Writing_guidelines/Page_structures/Banners_and_notices) - : Sometimes, an article needs a special notice added to it. This might happen if the page covers deprecated technology or other material that shouldn't be used in production code. This article covers the most common cases and how to handle them. - [Specification tables](/en-US/docs/MDN/Writing_guidelines/Page_structures/Specification_tables) - : Every reference page on MDN Web Docs should provide information about the specification or specifications in which that API or technology was defined. This article demonstrates what these tables look like and explains how to add them. - [Compatibility tables](/en-US/docs/MDN/Writing_guidelines/Page_structures/Compatibility_tables) - : MDN Web Docs has a standard format for compatibility tables for our open web documentation. This article explains how to add to and maintain the database that is used to generate the compatibility tables as well as how to integrate the tables into articles. - [Macros](/en-US/docs/MDN/Writing_guidelines/Page_structures/Macros) - : Macros are shortcuts that are used in pages to generate content, such as sidebars. This section lists the macros we use and what they do. - [Attributions and copyright licensing information](/en-US/docs/MDN/Writing_guidelines/Attrib_copyright_license) - : Describes our policy on using MDN Web Docs content elsewhere on the web, how to get permission to republish content on MDN, and hints for linking to MDN content. - [How to label a technology](/en-US/docs/MDN/Writing_guidelines/Experimental_deprecated_obsolete) - : This section covers our definitions for the terms obsolete, deprecated, and experimental and provides guidelines on how to label a technology with them, and when we remove content from MDN Web Docs.