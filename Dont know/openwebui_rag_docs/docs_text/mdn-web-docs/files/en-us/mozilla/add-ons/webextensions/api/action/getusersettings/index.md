Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > action > getusersettings > index.md --- title: action.getUserSettings() slug: Mozilla/Add-ons/WebExtensions/API/action/getUserSettings page-type: webextension-api-function browser-compat: webextensions.api.action.getUserSettings --- {{AddonSidebar}} Gets the user-specified settings for the browser action. > [!NOTE] > This API is available in Manifest V3 or higher. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let userSettings = await browser.action.getUserSettings(); ``` ### Parameters This function takes no parameters. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that fulfills with an object with these properties: - `userSettings` - : An object containing the user-specified settings for the browser action with these properties: - `isOnToolbar` {{optional_inline}} - : `boolean`. Whether the user has pinned the action's icon to the browser UI. This setting does not indicate whether the action icon is visible. The icon's visibility depends on the size of the browser window and the layout of the browser UI. ## Examples This code logs a message indicating whether the action is pinned or not: ```js function gotSettings(userSettings) { if (userSettings.isOnToolbar) { console.log("Action is pinned to toolbar."); } else { console.log("Action is not pinned to toolbar."); } } let gettingUserSettings = browser.action.getUserSettings(); gettingUserSettings.then(gotSettings); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.action`](https://developer.chrome.com/docs/extensions/reference/api/action#method-getUserSettings) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->