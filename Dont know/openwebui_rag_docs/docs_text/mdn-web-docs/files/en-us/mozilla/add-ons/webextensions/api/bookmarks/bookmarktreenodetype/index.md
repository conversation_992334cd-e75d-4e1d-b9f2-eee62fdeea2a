Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > bookmarktreenodetype > index.md --- title: bookmarks.BookmarkTreeNodeType slug: Mozilla/Add-ons/WebExtensions/API/bookmarks/BookmarkTreeNodeType page-type: webextension-api-type browser-compat: webextensions.api.bookmarks.BookmarkTreeNodeType --- {{AddonSidebar}} The **`bookmarks.BookmarkTreeNodeType`** type is used to describe whether a node in the bookmark tree is a bookmark, a folder, or a separator. ## Type `bookmarks.BookmarkTreeNodeType` is a {{jsxref("string")}} which can have one of the following three values: - `"bookmark"`: the node is a bookmark. - `"folder"`: the node is a folder. - `"separator"`: the node is a separator. ## Browser compatibility {{Compat}} {{WebExtExamples}}