Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > getrecent > index.md --- title: bookmarks.getRecent() slug: Mozilla/Add-ons/WebExtensions/API/bookmarks/getRecent page-type: webextension-api-function browser-compat: webextensions.api.bookmarks.getRecent --- {{AddonSidebar}} The `bookmarks.getRecent()` method retrieves a specified number of the most recently added bookmarks as an array of {{WebExtAPIRef('bookmarks.BookmarkTreeNode', 'BookmarkTreeNode')}} objects. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let gettingRecent = browser.bookmarks.getRecent( numberOfItems // integer ) ``` ### Parameters - `numberOfItems` - : A number representing the maximum number of items to return. The returned list will contain up to this many of the most recently added items. The minimum allowed value here is 1. If you pass 0 or less, the function will throw an error. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with an array of [`BookmarkTreeNode`](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/bookmarks/BookmarkTreeNode) objects. ## Examples This example logs the URL for the most recently added bookmark: ```js function onFulfilled(bookmarks) { for (const bookmark of bookmarks) { console.log(bookmark.url); } } function onRejected(error) { console.log(`An error: ${error}`); } browser.bookmarks.getRecent(1).then(onFulfilled, onRejected); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.bookmarks`](https://developer.chrome.com/docs/extensions/reference/api/bookmarks#method-getRecent) API. This documentation is derived from [`bookmarks.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/bookmarks.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->