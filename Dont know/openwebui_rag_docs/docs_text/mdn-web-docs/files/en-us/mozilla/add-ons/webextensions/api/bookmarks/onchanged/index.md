Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > bookmarks > onchanged > index.md --- title: bookmarks.onChanged slug: Mozilla/Add-ons/WebExtensions/API/bookmarks/onChanged page-type: webextension-api-event browser-compat: webextensions.api.bookmarks.onChanged --- {{AddonSidebar}} Fired when there is a change to: - the title or URL of a bookmark - the name of a folder. ## Syntax ```js-nolint browser.bookmarks.onChanged.addListener(listener) browser.bookmarks.onChanged.removeListener(listener) browser.bookmarks.onChanged.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed these arguments: - `id` - : `string`. ID of the item that changed. - `changeInfo` - : [`object`](#changeinfo). Object containing two properties: `title`, a string containing the item's title, and `url`, a string containing the item's URL. If the item is a folder, `url` is omitted. > [!NOTE] > Multiple events may occur when a bookmark changes, and that changeInfo object may contain only the data that has changed, rather than all the data for the bookmark. In other words, if the `url` for a bookmark changes, the changeInfo may only contain the new `url` information. ## Examples ```js function handleChanged(id, changeInfo) { console.log(`Item: ${id} changed`); console.log(`Title: ${changeInfo.title}`); console.log(`URL: ${changeInfo.url}`); } function handleClick() { browser.bookmarks.onChanged.addListener(handleChanged); } browser.browserAction.onClicked.addListener(handleClick); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.bookmarks`](https://developer.chrome.com/docs/extensions/reference/api/bookmarks#event-onChanged) API. This documentation is derived from [`bookmarks.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/bookmarks.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->