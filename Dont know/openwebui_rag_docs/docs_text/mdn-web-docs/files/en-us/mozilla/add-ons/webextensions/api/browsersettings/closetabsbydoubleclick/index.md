Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > browsersettings > closetabsbydoubleclick > index.md --- title: browserSettings.closeTabsByDoubleClick slug: Mozilla/Add-ons/WebExtensions/API/browserSettings/closeTabsByDoubleClick page-type: webextension-api-property browser-compat: webextensions.api.browserSettings.closeTabsByDoubleClick --- {{AddonSidebar}} A {{WebExtAPIRef("types.BrowserSetting", "BrowserSetting")}} object that can be used to enable or disable the user's ability to close a tab using double-click. The underlying value is a boolean. By default, closeTabsByDoubleClick is false. The setting can be changed by the user in about:config. ## Browser compatibility {{Compat}}