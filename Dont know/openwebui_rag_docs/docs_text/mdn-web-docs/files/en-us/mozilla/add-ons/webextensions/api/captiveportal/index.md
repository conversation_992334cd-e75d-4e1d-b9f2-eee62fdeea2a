Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > captiveportal > index.md --- title: captivePortal slug: Mozilla/Add-ons/WebExtensions/API/captivePortal page-type: webextension-api browser-compat: webextensions.api.captivePortal --- {{AddonSidebar}} Determine the captive portal state of the user's connection. A captive portal is a web page displayed when a user first connects to a Wi-Fi network. The user provides information or acts on the captive portal web page to gain broader access to network resources, such as accepting terms and conditions or making a payment. To use this API you need to have the "captivePortal" [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions). ## Properties - {{WebExtAPIRef("captivePortal.canonicalURL")}} - : Return the canonical URL of the captive-portal detection page. Read-only. ## Functions - {{WebExtAPIRef("captivePortal.getLastChecked()")}} - : Returns the time, in milliseconds, since the last request was completed. - {{WebExtAPIRef("captivePortal.getState()")}} - : Returns the portal state as one of `unknown`, `not_captive`, `unlocked_portal`, or `locked_portal`. ## Events - {{WebExtAPIRef("captivePortal.onConnectivityAvailable")}} - : Fires when the captive portal service determines that the user can connect to the internet. - {{WebExtAPIRef("captivePortal.onStateChanged")}} - : Fires when the captive portal state changes. ## Browser compatibility {{Compat}} {{WebExtExamples("h2")}} <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->