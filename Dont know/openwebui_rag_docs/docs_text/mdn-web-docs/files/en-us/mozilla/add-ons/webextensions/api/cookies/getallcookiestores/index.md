Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > getallcookiestores > index.md --- title: cookies.getAllCookieStores() slug: Mozilla/Add-ons/WebExtensions/API/cookies/getAllCookieStores page-type: webextension-api-function browser-compat: webextensions.api.cookies.getAllCookieStores --- {{AddonSidebar}} The **`getAllCookieStores()`** method of the {{WebExtAPIRef("cookies")}} API returns a list of all cookie stores. To use this method, an extension must have the `"cookies"` permission. See [`cookie` permissions](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/cookies#permissions) for more details. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let gettingStores = browser.cookies.getAllCookieStores() ``` ### Parameters None. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that is fulfilled with an `array` of {{WebExtAPIRef('cookies.CookieStore')}} objects representing all the cookie stores. ## Examples In this snippet, the `getAllCookieStores()` method is used to retrieve all the cookie stores available in the browser, and print out each cookie store ID, and the tabs that share each cookie store. ```js function logStores(cookieStores) { for (const store of cookieStores) { console.log(`Cookie store: ${store.id}\n Tab IDs: ${store.tabIds}`); } } browser.cookies.getAllCookieStores().then(logStores); ``` Each member of the `cookieStores` array is a {{WebExtAPIRef("cookies.CookieStore")}} object. {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.cookies`](https://developer.chrome.com/docs/extensions/reference/api/cookies#method-getAllCookieStores) API. This documentation is derived from [`cookies.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/cookies.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->