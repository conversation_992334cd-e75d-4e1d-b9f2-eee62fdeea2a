Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > cookies > onchangedcause > index.md --- title: cookies.OnChangedCause slug: Mozilla/Add-ons/WebExtensions/API/cookies/OnChangedCause page-type: webextension-api-type browser-compat: webextensions.api.cookies.OnChangedCause --- {{AddonSidebar}} The `OnChangedCause` type of the {{WebExtAPIRef("cookies")}} API represents the reason a cookie changed. ## Type Values of this type are strings. Possible values are: - `evicted` - : A cookie has been automatically removed due to garbage collection. - `expired` - : A cookie has been automatically removed due to expiry. - `explicit` - : A cookie has been inserted or removed via an explicit call to {{WebExtAPIRef("cookies.remove()")}}. - `expired_overwrite` - : A cookie has been overwritten by a cookie with an already-expired expiration date. - `overwrite` - : A call to {{WebExtAPIRef("cookies.set()")}} overwrote this cookie with a different one. ## Browser compatibility {{Compat}} ## Examples You can listen to the {{WebExtAPIRef("cookies.onChanged")}} event to be notified when cookies change. The listener is passed a `changeInfo` object that contains a `cause` property, whose value is the `OnChangeCaused` string: ```js browser.cookies.onChanged.addListener((changeInfo) => { console.log( `Cookie changed: \n` + ` * Cookie: ${JSON.stringify(changeInfo.cookie)}\n` + ` * Cause: ${changeInfo.cause}\n` + ` * Removed: ${changeInfo.removed}`, ); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.cookies`](https://developer.chrome.com/docs/extensions/reference/api/cookies#type-OnChangedCause) API. This documentation is derived from [`cookies.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/cookies.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->