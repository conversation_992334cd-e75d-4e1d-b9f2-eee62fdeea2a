Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > network > onrequestfinished > index.md --- title: devtools.network.onRequestFinished slug: Mozilla/Add-ons/WebExtensions/API/devtools/network/onRequestFinished page-type: webextension-api-event browser-compat: webextensions.api.devtools.network.onRequestFinished --- {{AddonSidebar}} Fired when a network request has finished and its details are available to the extension. The request is given as a [HAR entry object](http://www.softwareishard.com/blog/har-12-spec/#entries), which is also given an asynchronous `getContent()` method that gets the response body content. Note that although your extension can add a listener at any time, it will only start firing after the user has activated the browser's [network panel](https://firefox-source-docs.mozilla.org/devtools-user/network_monitor/index.html) at least once. ## Syntax ```js-nolint browser.devtools.network.onRequestFinished.addListener(listener) browser.devtools.network.onRequestFinished.removeListener(listener) browser.devtools.network.onRequestFinished.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed this argument: - `request` - : `object`. An object representing the request. This object is a single [HAR entry](http://www.softwareishard.com/blog/har-12-spec/#entries) object. It also defines an asynchronous `getContent()` method, which returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that resolves with an array of two elements. The first element is the HTTP response body as a string, while the second element is the [MIME type](/en-US/docs/Glossary/MIME_type) of the HTTP response also as a string. ## Browser compatibility {{Compat}} ## Examples Add a listener that logs the server IP address and response body for every network request. ```js function handleRequestFinished(request) { console.log("Server IP: ", request.serverIPAddress); request.getContent().then(([content, mimeType]) => { console.log("Content: ", content); console.log("MIME type: ", mimeType); }); } browser.devtools.network.onRequestFinished.addListener(handleRequestFinished); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.devtools`](https://developer.chrome.com/docs/extensions/how-to/devtools/extend-devtools) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->