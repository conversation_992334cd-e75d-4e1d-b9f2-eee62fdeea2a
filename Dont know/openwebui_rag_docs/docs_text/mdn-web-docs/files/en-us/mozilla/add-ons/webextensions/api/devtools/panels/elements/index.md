Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > elements > index.md --- title: devtools.panels.elements slug: Mozilla/Add-ons/WebExtensions/API/devtools/panels/elements page-type: webextension-api-property browser-compat: webextensions.api.devtools.panels.elements --- {{AddonSidebar}} An [`ElementsPanel`](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/devtools/panels/ElementsPanel) object, which represents the browser's HTML/CSS inspector. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.devtools.panels`](https://developer.chrome.com/docs/extensions/reference/api/devtools/panels) API.