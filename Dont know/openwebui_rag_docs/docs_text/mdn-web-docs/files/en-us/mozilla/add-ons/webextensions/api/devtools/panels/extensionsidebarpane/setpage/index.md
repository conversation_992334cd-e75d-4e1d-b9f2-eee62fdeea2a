Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > extensionsidebarpane > setpage > index.md --- title: devtools.panels.ExtensionSidebarPane.setPage() slug: Mozilla/Add-ons/WebExtensions/API/devtools/panels/ExtensionSidebarPane/setPage page-type: webextension-api-function browser-compat: webextensions.api.devtools.panels.ExtensionSidebarPane.setPage --- {{AddonSidebar}} Sets an HTML page to be displayed in the sidebar pane. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint browser.devtools.panels.setPage( path // string containing relative path to page ) ``` ### Parameters - extensionPageURL - : `string`. The relative path of an HTML page to display within the sidebar. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with no arguments, once the URL has been set. The selected page will not be loaded until the user selects the devtools sidebar. ## Examples Create a new pane, and populate it with an HTML page. You could run this code in a script loaded by your extension's [devtools page](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/devtools_page). ```js function onCreated(sidebarPane) { sidebarPane.setPage("sidebar/sidebar.html"); } ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.devtools.panels`](https://developer.chrome.com/docs/extensions/reference/api/devtools/panels) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->