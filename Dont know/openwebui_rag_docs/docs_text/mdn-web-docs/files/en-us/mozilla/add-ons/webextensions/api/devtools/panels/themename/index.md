Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > devtools > panels > themename > index.md --- title: devtools.panels.themeName slug: Mozilla/Add-ons/WebExtensions/API/devtools/panels/themeName page-type: webextension-api-property browser-compat: webextensions.api.devtools.panels.themeName --- {{AddonSidebar}} The name of the currently selected devtools theme. This is a string whose possible values are: - "light" - "dark" - "firebug" ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.devtools.panels`](https://developer.chrome.com/docs/extensions/reference/api/devtools/panels) API.