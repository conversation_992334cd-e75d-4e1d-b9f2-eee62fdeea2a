Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > dom > index.md --- title: dom slug: Mozilla/Add-ons/WebExtensions/API/dom page-type: webextension-api browser-compat: webextensions.api.dom --- {{AddonSidebar}} Access special extension only DOM features. ## Functions - {{WebExtAPIRef("dom.openOrClosedShadowRoot()")}} - : Gets the open shadow root or the closed shadow root hosted by the specified element. If the shadow root isn't attached to the element, it will return `null`. {{WebExtExamples("h2")}} ## Browser compatibility {{Compat}}