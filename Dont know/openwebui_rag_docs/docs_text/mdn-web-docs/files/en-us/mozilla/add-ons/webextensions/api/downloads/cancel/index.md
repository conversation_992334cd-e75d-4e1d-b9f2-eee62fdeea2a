Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > cancel > index.md --- title: downloads.cancel() slug: Mozilla/Add-ons/WebExtensions/API/downloads/cancel page-type: webextension-api-function browser-compat: webextensions.api.downloads.cancel --- {{AddonSidebar}} The **`cancel()`** function of the {{WebExtAPIRef("downloads")}} API cancels a download. The call will fail if the download is not active: for example, because it has completed downloading. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let canceling = browser.downloads.cancel( downloadId // integer ) ``` ### Parameters - `downloadId` - : `integer`. The id of the download to cancel. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). If the request was successful, the promise will be fulfilled with no arguments. If the request failed, the promise will be rejected with an error message. ## Browser compatibility {{Compat}} ## Examples ```js let downloadId = 13; function onCanceled() { console.log(`Canceled download`); } function onError(error) { console.log(`Error: ${error}`); } let canceling = browser.downloads.cancel(downloadId); canceling.then(onCanceled, onError); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#method-cancel) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->