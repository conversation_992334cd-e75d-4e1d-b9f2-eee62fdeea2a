Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > downloadtime > index.md --- title: downloads.DownloadTime slug: Mozilla/Add-ons/WebExtensions/API/downloads/DownloadTime page-type: webextension-api-type browser-compat: webextensions.api.downloads.DownloadTime --- {{AddonSidebar}} The `DownloadTime` type of the {{WebExtAPIRef("downloads")}} API represents the time a download took to complete. ## Type A `DownloadTime` can be one of three different types: - a JavaScript [`Date`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date) object. - a string: - if this contains only digits, it's interpreted as the number of milliseconds since the UNIX epoch. - otherwise, it's interpreted as an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) string. - a number: this is interpreted as the number of milliseconds since the UNIX epoch. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#type-DownloadTime) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->