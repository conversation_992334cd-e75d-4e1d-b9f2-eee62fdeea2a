Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > onerased > index.md --- title: downloads.onErased slug: Mozilla/Add-ons/WebExtensions/API/downloads/onErased page-type: webextension-api-event browser-compat: webextensions.api.downloads.onErased --- {{AddonSidebar}} The **`onErased()`** event of the {{WebExtAPIRef("downloads")}} API fires when a download is erased from the browser history. The listener is passed the `downloadId` of the {{WebExtAPIRef('downloads.DownloadItem')}} object in question as a parameter. ## Syntax ```js-nolint browser.downloads.onErased.addListener(listener) browser.downloads.onErased.removeListener(listener) browser.downloads.onErased.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether a given `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. This function is passed this argument: - `downloadId` - : An `integer` representing the `id` of the {{WebExtAPIRef('downloads.DownloadItem')}} that was erased. ## Browser compatibility {{Compat}} ## Examples Add a listener for `onErased` events, then erase the most recent download: ```js function handleErased(item) { console.log(`Erased: ${item}`); } browser.downloads.onErased.addListener(handleErased); let erasing = browser.downloads.erase({ limit: 1, orderBy: ["-startTime"], }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#event-onErased) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->