Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > pause > index.md --- title: downloads.pause() slug: Mozilla/Add-ons/WebExtensions/API/downloads/pause page-type: webextension-api-function browser-compat: webextensions.api.downloads.pause --- {{AddonSidebar}} The **`pause()`** function of the {{WebExtAPIRef("downloads")}} API pauses a download. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let pausing = browser.downloads.pause( downloadId // integer ) ``` ### Parameters - `downloadId` - : An `integer` representing the `id` of the download to pause. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). If the call was successful, the download will be put in a paused state, and the promise will be fulfilled with no arguments. If the call fails, the promise will be rejected with an error message. The call will fail if the download is not active: for example, because it has finished downloading. ## Browser compatibility {{Compat}} ## Examples ```js function onPaused() { console.log(`Paused download`); } function onError(error) { console.log(`Error: ${error}`); } let pausing = browser.downloads.pause(downloadId); pausing.then(onPaused, onError); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#method-pause) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->