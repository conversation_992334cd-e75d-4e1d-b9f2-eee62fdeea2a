Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > search > index.md --- title: downloads.search() slug: Mozilla/Add-ons/WebExtensions/API/downloads/search page-type: webextension-api-function browser-compat: webextensions.api.downloads.search --- {{AddonSidebar}} The **`search()`** function of the {{WebExtAPIRef("downloads")}} API queries the {{WebExtAPIRef("downloads.DownloadItem", "DownloadItems")}} available in the browser's downloads manager, and returns those that match the specified search criteria. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let searching = browser.downloads.search(query); ``` ### Parameters - `query` - : A {{WebExtAPIRef('downloads.DownloadQuery')}} object. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). The promise is fulfilled with an `array` of {{WebExtAPIRef('downloads.DownloadItem')}} objects that match the given criteria. ## Browser compatibility {{Compat}} ## Examples In general, you restrict the items retrieved using the `query` parameter. ### Get downloads matching "query" ```js function logDownloads(downloads) { for (const download of downloads) { console.log(download.id); console.log(download.url); } } function onError(error) { console.log(`Error: ${error}`); } browser.downloads .search({ query: ["imgur"], }) .then(logDownloads, onError); ``` ### Get a specific item To get a specific {{WebExtAPIRef("downloads.DownloadItem", "DownloadItem")}}, the easiest way is to set only the `id` field, as seen in the snippet below: ```js function logDownloads(downloads) { for (const download of downloads) { console.log(download.id); console.log(download.url); } } function onError(error) { console.log(`Error: ${error}`); } const id = 13; browser.downloads.search({ id }).then(logDownloads, onError); ``` ### Get all downloads If you want to return all {{WebExtAPIRef("downloads.DownloadItem", "DownloadItems")}}, set `query` to an empty object. ```js function logDownloads(downloads) { for (const download of downloads) { console.log(download.id); console.log(download.url); } } function onError(error) { console.log(`Error: ${error}`); } browser.downloads.search({}).then(logDownloads, onError); ``` ### Get the most recent download You can get the most recent download by specifying the following search parameters: ```js function logDownloads(downloads) { for (const download of downloads) { console.log(download.id); console.log(download.url); } } function onError(error) { console.log(`Error: ${error}`); } browser.downloads .search({ limit: 1, orderBy: ["-startTime"], }) .then(logDownloads, onError); ``` You can see this code in action in our [latest-download](https://github.com/mdn/webextensions-examples/blob/main/latest-download/popup/latest_download.js) example. {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#method-search) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->