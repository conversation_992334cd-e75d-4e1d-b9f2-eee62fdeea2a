Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > downloads > show > index.md --- title: downloads.show() slug: Mozilla/Add-ons/WebExtensions/API/downloads/show page-type: webextension-api-function browser-compat: webextensions.api.downloads.show --- {{AddonSidebar}} The **`show()`** function of the {{WebExtAPIRef("downloads")}} API shows the downloaded file in its containing folder in the underlying platform's file manager. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let showing = browser.downloads.show( downloadId // integer ) ``` ### Parameters - `downloadId` - : An `integer` representing the ID of the {{WebExtAPIRef("downloads.DownloadItem", "DownloadItem")}} to show. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). If the request succeeds, the promise will be fulfilled with a boolean about whether the request was successful. If the request fails, the promise will be rejected with an error message. ## Browser compatibility {{Compat}} ## Examples This example shows the most recently downloaded item: ```js function onShowing(success) { console.log(`Showing download item: ${success}`); } function onError(error) { console.log(`Error opening item: ${error}`); } function openDownload(downloadItems) { if (downloadItems.length > 0) { latestDownloadId = downloadItems[0].id; let showing = browser.downloads.show(latestDownloadId); showing.then(onShowing, onError); } } let searching = browser.downloads.search({ limit: 1, orderBy: ["-startTime"], }); searching.then(openDownload, onError); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.downloads`](https://developer.chrome.com/docs/extensions/reference/api/downloads#method-show) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->