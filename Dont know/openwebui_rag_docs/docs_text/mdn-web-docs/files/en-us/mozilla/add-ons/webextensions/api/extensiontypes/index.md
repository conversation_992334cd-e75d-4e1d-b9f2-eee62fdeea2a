Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > index.md --- title: extensionTypes slug: Mozilla/Add-ons/WebExtensions/API/extensionTypes page-type: webextension-api browser-compat: webextensions.api.extensionTypes --- {{AddonSidebar}} Some common types used in other WebExtension APIs. ## Types - {{WebExtAPIRef("extensionTypes.ImageDetails")}} - : Details about the format and quality of an image. - {{WebExtAPIRef("extensionTypes.ImageFormat")}} - : The format of an image. - {{WebExtAPIRef("extensionTypes.InjectDetails")}} - : Injects details into a page. - {{WebExtAPIRef("extensionTypes.RunAt")}} - : The soonest that the JavaScript or CSS will be injected into the tab. - `extensionTypes.CSSOrigin` - : Indicates whether a CSS stylesheet injected by [`tabs.insertCSS`](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/tabs/insertCSS) should be treated as an "author" or "user" stylesheet. ## Browser compatibility {{Compat}} {{WebExtExamples("h2")}} > [!NOTE] > This API is based on Chromium's [`chrome.extensionTypes`](https://developer.chrome.com/docs/extensions/reference/api/extensionTypes) API. This documentation is derived from [`extension_types.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/extension_types.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->