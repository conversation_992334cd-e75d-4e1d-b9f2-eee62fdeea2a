Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > extensiontypes > injectdetails > index.md --- title: extensionTypes.InjectDetails slug: Mozilla/Add-ons/WebExtensions/API/extensionTypes/InjectDetails page-type: webextension-api-type --- {{AddonSidebar}} This type is given as a parameter to the `tabs.executeScript()`, `tabs.insertCSS()`, and `tabs.removeCSS()` methods. For the details of its properties and what they are used for, see the documentation pages for those methods: - {{WebExtAPIRef("tabs.executeScript()")}} - {{WebExtAPIRef("tabs.insertCSS()")}} - {{WebExtAPIRef("tabs.removeCSS()")}}