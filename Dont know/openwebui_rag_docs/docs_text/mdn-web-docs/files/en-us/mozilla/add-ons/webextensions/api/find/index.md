Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > find > index.md --- title: find slug: Mozilla/Add-ons/WebExtensions/API/find page-type: webextension-api browser-compat: webextensions.api.find --- {{AddonSidebar}} Finds text in a web page, and highlights matches. To use this API you need to have the "find" [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions). ## Functions - {{WebExtAPIRef("find.find()")}} - : Find text in a web page. - {{WebExtAPIRef("find.highlightResults()")}} - : Highlight the last set of matches found. - {{WebExtAPIRef("find.removeHighlighting()")}} - : Remove any highlighting. ## Browser compatibility {{WebExtExamples("h2")}} {{Compat}}