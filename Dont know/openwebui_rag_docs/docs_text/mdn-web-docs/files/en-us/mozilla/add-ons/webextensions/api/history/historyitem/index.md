Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > historyitem > index.md --- title: history.HistoryItem slug: Mozilla/Add-ons/WebExtensions/API/history/HistoryItem page-type: webextension-api-type browser-compat: webextensions.api.history.HistoryItem --- {{AddonSidebar}} A `HistoryItem` object provides information about a page in the browser history. ## Type This is an object with the following properties: - `id` - : `string`. Unique identifier for the item. - `url` {{optional_inline}} - : `string`. The URL of the page. - `title` {{optional_inline}} - : `string`. The title of the page. - `lastVisitTime` {{optional_inline}} - : `number`. The date and time the page was last loaded, represented in milliseconds since the epoch. - `visitCount` {{optional_inline}} - : `number`. The number of times the user has visited the page. - `typedCount` {{optional_inline}} - : `number`. The number of times the user has navigated to this page by typing in the address. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.history`](https://developer.chrome.com/docs/extensions/reference/api/history#type-HistoryItem) API. This documentation is derived from [`history.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/history.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->