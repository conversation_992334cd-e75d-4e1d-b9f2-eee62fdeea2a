Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > history > ontitlechanged > index.md --- title: history.onTitleChanged slug: Mozilla/Add-ons/WebExtensions/API/history/onTitleChanged page-type: webextension-api-event browser-compat: webextensions.api.history.onTitleChanged --- {{AddonSidebar}} Fired when the title of a page visited by the user is recorded. To listen for visits to a page you use {{WebExtAPIRef("history.onVisited")}}. However, the {{WebExtAPIRef("history.HistoryItem")}} that this event passes to its listener does not include the page title, because the page title is typically not known at the time `history.onVisited` is sent. Instead, the stored {{WebExtAPIRef("history.HistoryItem")}} is updated with the page title after the page has loaded, once the title is known. The `history.onTitleChanged` event is fired at that time. So if you need to know the titles of pages as they are visited, listen for `history.onTitleChanged`. ## Syntax ```js-nolint browser.history.onTitleChanged.addListener(listener) browser.history.onTitleChanged.removeListener(listener) browser.history.onTitleChanged.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed an object with these properties: - `id` - : `String`. The unique identifier for the {{WebExtAPIRef("history.HistoryItem")}} associated with this visit. - `url` - : `String`. URL of the page visited. - `title` - : `String`. Title of the page visited. ## Browser compatibility {{Compat}} ## Examples Listen for title change events, and log the ID, URL, and title of the visited pages. ```js function handleTitleChanged(item) { console.log(item.id); console.log(item.title); console.log(item.url); } browser.history.onTitleChanged.addListener(handleTitleChanged); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.history`](https://developer.chrome.com/docs/extensions/reference/api/history#event-onVisited) API. This documentation is derived from [`history.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/history.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->