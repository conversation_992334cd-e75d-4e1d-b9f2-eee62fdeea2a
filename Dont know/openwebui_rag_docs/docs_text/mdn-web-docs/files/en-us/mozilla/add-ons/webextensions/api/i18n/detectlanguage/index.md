Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > i18n > detectlanguage > index.md --- title: i18n.detectLanguage() slug: Mozilla/Add-ons/WebExtensions/API/i18n/detectLanguage page-type: webextension-api-function browser-compat: webextensions.api.i18n.detectLanguage --- {{AddonSidebar}} Detects the language of the provided text using the [Compact Language Detector](https://github.com/CLD2Owners/cld2) (CLD). This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). See the [Internationalization](/en-US/docs/Mozilla/Add-ons/WebExtensions/Internationalization) page for a guide on using this function. ## Syntax ```js-nolint let detectingLanguages = browser.i18n.detectLanguage( text // string ) ``` ### Parameters - `text` - : `string`. User input string to be translated. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with a result object. The result object has two properties: - `isReliable` - : `boolean`. Whether the language was detected reliably. - `languages` - : `array` of objects, each of which has two properties: - `language` - : {{WebExtAPIRef('i18n.LanguageCode')}}. The detected language. - `percentage` - : `integer`. The percentage of the input string that was in the detected language. ## Browser compatibility {{Compat}} ## Examples ```js function onLanguageDetected(langInfo) { for (const lang of langInfo.languages) { console.log(`Language is: ${lang.language}`); console.log(`Percentage is: ${lang.percentage}`); } } let text = "L'homme est né libre, et partout il est dans les fers."; let detecting = browser.i18n.detectLanguage(text); detecting.then(onLanguageDetected); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.i18n`](https://developer.chrome.com/docs/extensions/reference/api/i18n#method-detectLanguage) API. This documentation is derived from [`i18n.json`](https://chromium.googlesource.com/chromium/src/+/refs/heads/main/extensions/common/api/i18n.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->