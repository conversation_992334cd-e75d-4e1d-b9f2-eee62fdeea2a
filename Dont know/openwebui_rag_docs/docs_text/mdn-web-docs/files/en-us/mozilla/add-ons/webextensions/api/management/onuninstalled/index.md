Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > management > onuninstalled > index.md --- title: management.onUninstalled() slug: Mozilla/Add-ons/WebExtensions/API/management/onUninstalled page-type: webextension-api-event browser-compat: webextensions.api.management.onUninstalled --- {{AddonSidebar}} Fired when an add-on is uninstalled. This API requires the "management" [API permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions). ## Syntax ```js-nolint browser.management.onUninstalled.addListener(listener) browser.management.onUninstalled.removeListener(listener) browser.management.onUninstalled.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Checks whether a `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed this argument: - `info` - : [`ExtensionInfo`](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/management/ExtensionInfo): info about the add-on that was uninstalled. ## Browser compatibility {{Compat}} ## Examples Log the names of add-ons when they are uninstalled: ```js browser.management.onUninstalled.addListener((info) => { console.log(`${info.name} was uninstalled`); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.management`](https://developer.chrome.com/docs/extensions/reference/api/management#event-onUninstalled) API. This documentation is derived from [`management.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/management.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->