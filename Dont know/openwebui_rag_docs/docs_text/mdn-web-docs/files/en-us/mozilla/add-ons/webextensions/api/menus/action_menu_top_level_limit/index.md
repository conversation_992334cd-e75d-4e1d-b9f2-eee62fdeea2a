Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > action_menu_top_level_limit > index.md --- title: menus.ACTION_MENU_TOP_LEVEL_LIMIT slug: Mozilla/Add-ons/WebExtensions/API/menus/ACTION_MENU_TOP_LEVEL_LIMIT page-type: webextension-api-property browser-compat: webextensions.api.menus.ACTION_MENU_TOP_LEVEL_LIMIT --- {{AddonSidebar}} The maximum number of top level extension items that can be added to a menu item whose {{WebExtAPIRef("contextMenus.ContextType", "ContextType")}} is "browser_action" or "page_action". Any items beyond this limit will be ignored. Its value is `6` on Firefox and Chrome. For compatibility with other browsers, Firefox makes this property available via the `contextMenus` namespace as well as the `menus` namespace. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.contextMenus`](https://developer.chrome.com/docs/extensions/reference/api/contextMenus#property-ACTION_MENU_TOP_LEVEL_LIMIT) API. This documentation is derived from [`context_menus.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/context_menus.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->