Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > menus > remove > index.md --- title: menus.remove() slug: Mozilla/Add-ons/WebExtensions/API/menus/remove page-type: webextension-api-function browser-compat: webextensions.api.menus.remove --- {{AddonSidebar}} Removes a menu item. For compatibility with other browsers, Firefox makes this method available via the `contextMenus` namespace as well as the `menus` namespace. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let removing = browser.menus.remove( menuItemId // integer or string ) ``` ### Parameters - `menuItemId` - : `integer` or `string`. The ID of the menu item to remove. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with no arguments if removal was successful, or rejected with an error message if removal failed (for example, because the item could not be found). ## Examples This extension adds a menu item labeled "Remove me!". If you click the item, the extension removes it. ```js function onRemoved() { console.log("item removed successfully"); } function onError() { console.log("error removing item:", browser.runtime.lastError); } browser.menus.create({ id: "remove-me", title: "Remove me!", contexts: ["all"], }); browser.menus.onClicked.addListener((info, tab) => { if (info.menuItemId === "remove-me") { let removing = browser.menus.remove(info.menuItemId); removing.then(onRemoved, onError); } }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.contextMenus`](https://developer.chrome.com/docs/extensions/reference/api/contextMenus#method-remove) API. This documentation is derived from [`context_menus.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/context_menus.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->