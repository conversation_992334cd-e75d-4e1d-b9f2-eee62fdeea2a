Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > notifications > onbuttonclicked > index.md --- title: notifications.onButtonClicked slug: Mozilla/Add-ons/WebExtensions/API/notifications/onButtonClicked page-type: webextension-api-event browser-compat: webextensions.api.notifications.onButtonClicked --- {{AddonSidebar}} Fired when the user clicks one of the notification's buttons. ## Syntax ```js-nolint browser.notifications.onButtonClicked.addListener(listener) browser.notifications.onButtonClicked.removeListener(listener) browser.notifications.onButtonClicked.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed these arguments: - `notificationId` - : `string`. ID of the notification whose button was clicked. - `buttonIndex` - : `integer`. The [zero-based](https://en.wikipedia.org/wiki/Zero-based_numbering) index of the button that was clicked. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.notifications`](https://developer.chrome.com/docs/extensions/reference/api/notifications) API.