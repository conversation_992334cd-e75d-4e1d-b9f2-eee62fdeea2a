Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > pageaction > settitle > index.md --- title: pageAction.setTitle() slug: Mozilla/Add-ons/WebExtensions/API/pageAction/setTitle page-type: webextension-api-function browser-compat: webextensions.api.pageAction.setTitle --- {{AddonSidebar}} Sets the title of the page action. The title is displayed in a tooltip when the user hovers over the page action. ## Syntax ```js-nolint browser.pageAction.setTitle( details // object ) ``` ### Parameters - `details` - : `object`. - `tabId` - : `integer`. The ID of the tab whose title you want to set. - `title` - : `string` or `null`. The tooltip text. If `null` is passed here, the title is reset to the title that was specified in the [`page_action`](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/page_action) manifest key. ## Browser compatibility {{Compat}} ## Examples Whenever a tab is updated, show the page action for that tab, and set its title to show the tab's ID: ```js browser.tabs.onUpdated.addListener((tabId, changeInfo, tabInfo) => { browser.pageAction.show(tabId); browser.pageAction.setTitle({ tabId, title: `Tab ID: ${tabId}`, }); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.pageAction`](https://developer.chrome.com/docs/extensions/mv2/reference/pageAction#method-setTitle) API. This documentation is derived from [`page_action.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/page_action.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->