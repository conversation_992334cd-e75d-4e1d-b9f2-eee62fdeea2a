Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > permissions > onremoved > index.md --- title: permissions.onRemoved slug: Mozilla/Add-ons/WebExtensions/API/permissions/onRemoved page-type: webextension-api-event browser-compat: webextensions.api.permissions.onRemoved --- {{AddonSidebar}} Fired when some permissions are removed from the extension. ## Syntax ```js-nolint browser.permissions.onRemoved.addListener(listener) browser.permissions.onRemoved.removeListener(listener) browser.permissions.onRemoved.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed this argument: - `permissions` - : {{WebExtAPIRef("permissions.Permissions")}} object containing the permissions that were removed. ## Browser compatibility {{Compat}} ## Examples ```js function handleRemoved(permissions) { console.log(`Removed API permissions: ${permissions.permissions}`); console.log(`Removed host permissions: ${permissions.origins}`); } browser.permissions.onRemoved.addListener(handleRemoved); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.permissions`](https://developer.chrome.com/docs/extensions/reference/api/permissions) API.