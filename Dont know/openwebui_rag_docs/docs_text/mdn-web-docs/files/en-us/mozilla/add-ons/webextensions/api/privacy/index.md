Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > privacy > index.md --- title: privacy slug: Mozilla/Add-ons/WebExtensions/API/privacy page-type: webextension-api browser-compat: webextensions.api.privacy --- {{AddonSidebar}} Access and modify various privacy-related browser settings. To use the privacy API, you must have the "privacy" [API permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#api_permissions). ## Properties - {{WebExtAPIRef("privacy.network")}} - : Access and modify privacy settings relating to the network. - {{WebExtAPIRef("privacy.services")}} - : Access and modify privacy settings relating to the services provided by the browser or third parties. - {{WebExtAPIRef("privacy.websites")}} - : Access and modify privacy settings relating to the behavior of websites. ## Browser compatibility {{Compat}} {{WebExtExamples("h2")}} > [!NOTE] > This API is based on Chromium's [`chrome.privacy`](https://developer.chrome.com/docs/extensions/reference/api/privacy) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->