Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > getplatforminfo > index.md --- title: runtime.getPlatformInfo() slug: Mozilla/Add-ons/WebExtensions/API/runtime/getPlatformInfo page-type: webextension-api-function browser-compat: webextensions.api.runtime.getPlatformInfo --- {{AddonSidebar}} Returns information about the current platform. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let getting = browser.runtime.getPlatformInfo() ``` ### Parameters None. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with a {{WebExtAPIRef('runtime.PlatformInfo')}} value representing the current platform. ## Browser compatibility {{Compat}} ## Examples Get and log the platform OS: ```js function gotPlatformInfo(info) { console.log(info.os); } let gettingInfo = browser.runtime.getPlatformInfo(); gettingInfo.then(gotPlatformInfo); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#method-getPlatformInfo) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->