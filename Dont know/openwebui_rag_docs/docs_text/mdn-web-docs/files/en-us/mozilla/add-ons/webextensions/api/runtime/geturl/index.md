Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > geturl > index.md --- title: runtime.getURL() slug: Mozilla/Add-ons/WebExtensions/API/runtime/getURL page-type: webextension-api-function browser-compat: webextensions.api.runtime.getURL --- {{AddonSidebar}} Given a relative path from the [manifest.json](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json) to a resource packaged with the extension, return a fully-qualified URL. This function does _not_ check that the resource actually exists at that URL. ## Syntax ```js-nolint browser.runtime.getURL( path // string ) ``` ### Parameters - `path` - : `string`. A relative path from the [manifest.json](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json) to a resource packaged with the extension. ### Return value `string`. The fully-qualified URL to the resource within the extension. ## Browser compatibility {{Compat}} ## Examples Given a file packaged with the extension at "beasts/frog.html", get the full URL like this: ```js let fullURL = browser.runtime.getURL("beasts/frog.html"); console.log(fullURL); // Returns something like: // moz-extension://2c127fa4-62c7-7e4f-90e5-472b45eecfdc/beasts/frog.html ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#method-getURL) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->