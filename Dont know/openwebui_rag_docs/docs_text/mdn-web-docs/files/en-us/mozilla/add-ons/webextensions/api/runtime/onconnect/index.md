Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnect > index.md --- title: runtime.onConnect slug: Mozilla/Add-ons/WebExtensions/API/runtime/onConnect page-type: webextension-api-event browser-compat: webextensions.api.runtime.onConnect --- {{AddonSidebar}} Fired when a connection is made with either an extension process or a content script. ## Syntax ```js-nolint browser.runtime.onConnect.addListener(listener) browser.runtime.onConnect.removeListener(listener) browser.runtime.onConnect.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Checks whether a `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `function` - : The function called when this event occurs. The function is passed this argument: - `port` - : A {{WebExtAPIRef('runtime.Port')}} object connecting the current script to the other context it is connecting to. ## Browser compatibility {{Compat}} ## Examples This content script: - connects to the background script, and stores the `Port` in a variable `myPort` - listens for messages on `myPort`, and logs them - sends messages to the background script, using `myPort`, when the user clicks the document ```js // content-script.js let myPort = browser.runtime.connect({ name: "port-from-cs" }); myPort.postMessage({ greeting: "hello from content script" }); myPort.onMessage.addListener((m) => { console.log("In content script, received message from background script: "); console.log(m.greeting); }); document.body.addEventListener("click", () => { myPort.postMessage({ greeting: "they clicked the page!" }); }); ``` The corresponding background script: - listens for connection attempts from the content script - when it receives a connection attempt: - stores the port in a variable named `portFromCS` - sends the content script a message using the port - starts listening to messages received on the port, and logs them - sends messages to the content script, using `portFromCS`, when the user clicks the extension's browser action ```js // background-script.js let portFromCS; function connected(p) { portFromCS = p; portFromCS.postMessage({ greeting: "hi there content script!" }); portFromCS.onMessage.addListener((m) => { console.log("In background script, received message from content script"); console.log(m.greeting); }); } browser.runtime.onConnect.addListener(connected); browser.browserAction.onClicked.addListener(() => { portFromCS.postMessage({ greeting: "they clicked the button!" }); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#event-onConnect) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->