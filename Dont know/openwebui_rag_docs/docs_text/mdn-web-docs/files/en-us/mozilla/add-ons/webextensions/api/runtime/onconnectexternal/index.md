Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onconnectexternal > index.md --- title: runtime.onConnectExternal slug: Mozilla/Add-ons/WebExtensions/API/runtime/onConnectExternal page-type: webextension-api-event browser-compat: webextensions.api.runtime.onConnectExternal --- {{AddonSidebar}} Fired when an extension receives a connection request from a different extension. To send a message which will be received by the `onConnectExternal` listener, use {{WebExtAPIRef("runtime.connect()")}}, passing the ID of the recipient in the `extensionId` parameter. The listener is passed a {{WebExtAPIRef('runtime.Port')}} object which it can then use to send and receive messages. The `Port` object also contains a `sender` property, which is a {{WebExtAPIRef("runtime.MessageSender")}} object, and which the recipient can use to check the sender's ID. ## Syntax ```js-nolint browser.runtime.onConnectExternal.addListener(listener) browser.runtime.onConnectExternal.removeListener(listener) browser.runtime.onConnectExternal.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Checks whether a `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `function` - : The function called when this event occurs. The function is passed this argument: - `port` - : A {{WebExtAPIRef('runtime.Port')}} object connecting the current script to the other extension it is connecting to. ## Browser compatibility {{Compat}} ## Examples In this example the extension Hansel connects to the extension Gretel: ```js console.log("connecting to Gretel"); let myPort = browser.runtime.connect("<EMAIL>"); myPort.onMessage.addListener((message) => { console.log(`From Gretel: ${message.content}`); }); browser.browserAction.onClicked.addListener(() => { myPort.postMessage({ content: "Hello from Hansel" }); }); ``` Gretel listens for the connection and checks that the sender is really Hansel: ```js let portFromHansel; browser.runtime.onConnectExternal.addListener((port) => { console.log(port); if (port.sender.id === "<EMAIL>") { console.log("connection attempt from Hansel"); portFromHansel = port; portFromHansel.onMessage.addListener((message) => { console.log(`From Hansel: ${message.content}`); }); } }); browser.browserAction.onClicked.addListener(() => { portFromHansel.postMessage({ content: "Message from Gretel" }); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#event-onConnectExternal) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->