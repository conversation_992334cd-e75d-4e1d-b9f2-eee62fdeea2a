Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningcategory > index.md --- title: runtime.OnPerformanceWarningCategory slug: Mozilla/Add-ons/WebExtensions/API/runtime/OnPerformanceWarningCategory page-type: webextension-api-type browser-compat: webextensions.api.runtime.OnPerformanceWarningCategory --- {{AddonSidebar}} The category of warning that dispatched the {{WebExtAPIRef("runtime.onPerformanceWarning")}} event. ## Type Values of this type are strings. Possible values are: - `"content_script"`: The performance warning is for a slow content script in the listening extension. {{WebExtExamples("h2")}} ## Browser compatibility {{Compat}}