Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onperformancewarningseverity > index.md --- title: runtime.OnPerformanceWarningSeverity slug: Mozilla/Add-ons/WebExtensions/API/runtime/OnPerformanceWarningSeverity page-type: webextension-api-type browser-compat: webextensions.api.runtime.OnPerformanceWarningSeverity --- {{AddonSidebar}} The severity of warning that dispatched the {{WebExtAPIRef("runtime.onPerformanceWarning")}} event. ## Type Values of this type are strings. Possible values are: - `"low"` - `"medium"` - `"high"` {{WebExtExamples("h2")}} ## Browser compatibility {{Compat}}