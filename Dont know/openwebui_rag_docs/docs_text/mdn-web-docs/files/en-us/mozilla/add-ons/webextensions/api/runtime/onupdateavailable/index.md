Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > onupdateavailable > index.md --- title: runtime.onUpdateAvailable slug: Mozilla/Add-ons/WebExtensions/API/runtime/onUpdateAvailable page-type: webextension-api-event browser-compat: webextensions.api.runtime.onUpdateAvailable --- {{AddonSidebar}} Fired when an update to the extension is available. This event enables an extension to delay an update: for example, because it is in the middle of some operation which should not be interrupted. If the extension is not listening for this event when an update becomes available, the extension is reloaded immediately and the update is applied. If the extension is listening, then the update will be applied the next time the extension is reloaded. This happens if: - the browser is restarted - the extension is disabled and re-enabled - the extension explicitly reloads itself by calling {{WebExtAPIRef('runtime.reload()')}}. ## Syntax ```js-nolint browser.runtime.onUpdateAvailable.addListener() browser.runtime.onUpdateAvailable.removeListener(listener) browser.runtime.onUpdateAvailable.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Checks whether a `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed this argument: - `details` - : `object`. Contains a single property, a string named `version`, which represents the version number of the update. ## Browser compatibility {{Compat}} ## Examples Listen for `UpdateAvailable` events: ```js function handleUpdateAvailable(details) { console.log(details.version); // Proceed to upgrade the add-on browser.runtime.reload(); } browser.runtime.onUpdateAvailable.addListener(handleUpdateAvailable); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#event-onUpdateAvailable) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->