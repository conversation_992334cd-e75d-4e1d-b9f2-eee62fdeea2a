Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > runtime > platformarch > index.md --- title: runtime.PlatformArch slug: Mozilla/Add-ons/WebExtensions/API/runtime/PlatformArch page-type: webextension-api-type browser-compat: webextensions.api.runtime.PlatformArch --- {{AddonSidebar}} The machine's processor architecture. ## Type Values of this type are strings. Possible values are: - `"arm"` - : The platform is based on arm architecture. - `"x86-32"` - : The platform is based on x86 32-bit architecture. - `"x86-64"` - : The platform is based on x86 64-bit architecture. ## Browser compatibility {{Compat}} {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.runtime`](https://developer.chrome.com/docs/extensions/reference/api/runtime#type-PlatformArch) API. This documentation is derived from [`runtime.json`](https://chromium.googlesource.com/chromium/src/+/master/extensions/common/api/runtime.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->