Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > scripting > unregistercontentscripts > index.md --- title: scripting.unregisterContentScripts() slug: Mozilla/Add-ons/WebExtensions/API/scripting/unregisterContentScripts page-type: webextension-api-function browser-compat: webextensions.api.scripting.unregisterContentScripts --- {{AddonSidebar}} Unregisters one or more content scripts. > [!NOTE] > This method is available in Manifest V3 or higher in Chrome and Firefox 101. In Firefox 102+, this method is also available in Manifest V2. To use this API you must have the `"scripting"` [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions) and permission for the page's URL, either explicitly as a [host permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#host_permissions) or using the [activeTab permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#activetab_permission). This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint await browser.scripting.unregisterContentScripts( scripts // object ) ``` ### Parameters - `scripts` {{optional_inline}} - : {{WebExtAPIRef("scripting.ContentScriptFilter")}}. A filter to identify the dynamic content scripts to unregistered. If not specified, all dynamic content scripts are unregistered. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that fulfills with no arguments when all the scripts are unregistered. If any error occurs, the promise is rejected. ## Examples This example unregisters a registered content script with ID `a-script`: ```js try { await browser.scripting.unregisterContentScripts({ ids: ["a-script"], }); } catch (err) { console.error(`failed to unregister content scripts: ${err}`); } ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.scripting`](https://developer.chrome.com/docs/extensions/reference/api/scripting#method-unregisterContentScripts) API.