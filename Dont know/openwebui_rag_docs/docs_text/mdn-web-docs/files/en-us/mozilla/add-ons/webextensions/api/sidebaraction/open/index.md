Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > open > index.md --- title: sidebarAction.open() slug: Mozilla/Add-ons/WebExtensions/API/sidebarAction/open page-type: webextension-api-function browser-compat: webextensions.api.sidebarAction.open --- {{AddonSidebar}} Open the sidebar in the active window. You can only call this function from inside the handler for a [user action](/en-US/docs/Mozilla/Add-ons/WebExtensions/User_actions). This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint browser.sidebarAction.open() ``` ### Parameters None. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that is resolved with no arguments. ## Browser compatibility {{Compat}} ## Examples Open the sidebar when the user selects a context menu item: ```js browser.menus.create({ id: "open-sidebar", title: "open sidebar", contexts: ["all"], }); browser.menus.onClicked.addListener(() => { browser.sidebarAction.open(); }); ``` {{WebExtExamples}}