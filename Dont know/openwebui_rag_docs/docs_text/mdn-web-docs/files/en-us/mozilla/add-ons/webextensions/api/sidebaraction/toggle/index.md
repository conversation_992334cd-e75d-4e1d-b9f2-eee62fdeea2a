Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > sidebaraction > toggle > index.md --- title: sidebarAction.toggle() slug: Mozilla/Add-ons/WebExtensions/API/sidebarAction/toggle page-type: webextension-api-function browser-compat: webextensions.api.sidebarAction.toggle --- {{AddonSidebar}} Toggles the visibility of the sidebar in the active window, if the sidebar belongs to the extension. You can only call this function from inside the handler for a [user action](/en-US/docs/Mozilla/Add-ons/WebExtensions/User_actions). This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint browser.sidebarAction.toggle() ``` ### Parameters None. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that is resolved with no arguments. ## Browser compatibility {{Compat}} ## Examples Toggles the sidebar when the user selects an item from the context menu: ```js browser.menus.create({ id: "toggle-sidebar", title: "Toggle sidebar", contexts: ["all"], }); browser.menus.onClicked.addListener(() => { browser.sidebarAction.toggle(); }); ``` {{WebExtExamples}}