Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > storage > session > quota_bytes > index.md --- title: storage.session.QUOTA_BYTES slug: Mozilla/Add-ons/WebExtensions/API/storage/session/QUOTA_BYTES page-type: webextension-api-property browser-compat: webextensions.api.storage.session.QUOTA_BYTES --- {{AddonSidebar}} The maximum amount of data (in bytes) that can be stored in session storage. Use {{WebExtAPIRef("storage.StorageArea.getBytesInUse()", "storage.session.getBytesInUse()")}} to determine the amount of stored data. Its value is `10485760`. {{WebExtExamples("h2")}} ## Browser compatibility {{Compat}}