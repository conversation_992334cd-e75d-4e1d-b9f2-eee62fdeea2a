Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturetab > index.md --- title: tabs.captureTab() slug: Mozilla/Add-ons/WebExtensions/API/tabs/captureTab page-type: webextension-api-function browser-compat: webextensions.api.tabs.captureTab --- {{AddonSidebar}} Creates a data URL encoding the image of an area of the given tab. You must have the `<all_urls>` [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions) to use this method. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let capturing = browser.tabs.captureTab( tabId, // optional integer options // optional extensionTypes.ImageDetails ) ``` ### Parameters - `tabId` {{optional_inline}} - : `integer`. ID of the tab to capture. Defaults to the active tab in the current window. - `options` {{optional_inline}} - : {{WebExtAPIRef('extensionTypes.ImageDetails')}}. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with a data URL which encodes the captured image. May be assigned to the 'src' property of an HTML Image element for display. If any error occurs the promise will be rejected with an error message. ## Examples Capture an image of the active tab in the current window, with default settings: ```js function onCaptured(imageUri) { console.log(imageUri); } function onError(error) { console.log(`Error: ${error}`); } browser.browserAction.onClicked.addListener(() => { let capturing = browser.tabs.captureTab(); capturing.then(onCaptured, onError); }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-captureVisibleTab) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->