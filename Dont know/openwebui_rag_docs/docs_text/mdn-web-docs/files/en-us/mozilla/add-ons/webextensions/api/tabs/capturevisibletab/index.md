Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > capturevisibletab > index.md --- title: tabs.captureVisibleTab() slug: Mozilla/Add-ons/WebExtensions/API/tabs/captureVisibleTab page-type: webextension-api-function browser-compat: webextensions.api.tabs.captureVisibleTab --- {{AddonSidebar}} Creates a data URL encoding the image of an area of the active tab in the specified window. You must have the `<all_urls>` or `activeTab` [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions). > [!NOTE] > In Firefox 125 and earlier, this method was only available with the `<all_urls>` permission. In addition to sites that extensions can normally access, this method allows extensions to capture sensitive sites that are otherwise restricted, including browser UI pages and other extensions' pages. These sensitive sites can only be captured with the `activeTab` permission. Chrome also permits file URLs to be captured when the extension has been granted file access. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let capturing = browser.tabs.captureVisibleTab( windowId, // optional integer options // optional extensionTypes.ImageDetails ) ``` ### Parameters - `windowId` {{optional_inline}} - : `integer`. The target window. Defaults to the current window. - `options` {{optional_inline}} - : {{WebExtAPIRef('extensionTypes.ImageDetails')}}. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that is fulfilled with a data URL that encodes the captured image. It can be assigned to the 'src' property of an HTML image element for display. If any error occurs, the promise is rejected with an error message. ## Examples Capture an image of the active tab in the current window with default image settings: ```js function onCaptured(imageUri) { console.log(imageUri); } function onError(error) { console.log(`Error: ${error}`); } browser.browserAction.onClicked.addListener(() => { let capturing = browser.tabs.captureVisibleTab(); capturing.then(onCaptured, onError); }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-captureVisibleTab) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->