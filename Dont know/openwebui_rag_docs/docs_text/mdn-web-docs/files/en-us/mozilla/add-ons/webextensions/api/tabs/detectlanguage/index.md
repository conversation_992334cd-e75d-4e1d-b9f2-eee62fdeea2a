Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > detectlanguage > index.md --- title: tabs.detectLanguage() slug: Mozilla/Add-ons/WebExtensions/API/tabs/detectLanguage page-type: webextension-api-function browser-compat: webextensions.api.tabs.detectLanguage --- {{AddonSidebar}} Detects the primary language of the content in a tab, using the [Compact Language Detector](https://github.com/CLD2Owners/cld2) (CLD). This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let detecting = browser.tabs.detectLanguage( tabId, // optional integer callback // optional function ) ``` ### Parameters - `tabId` {{optional_inline}} - : `integer`. Defaults to the active tab of the current window. - `callback` {{optional_inline}} - : `function`. Currently, if a `tabId` is specified, this method uses this callback to return the results instead of returning a promise. The callback receives as its only input parameter a string containing the detected language code such as `en` or `fr`. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with a string representing an ISO language code such as `en` or `fr`. For a complete list of languages supported by this method, see [kLanguageInfoTable](https://source.chromium.org/chromium/chromium/src/+/main:third_party/ced/src/util/languages/languages.cc;l=35). For an unknown language, `"und"` will be returned (but see [bug 1288263](https://bugzil.la/1288263)). If any error occurs the promise will be rejected with an error message. ## Examples Detect and log the language of the active tab when the user clicks a browser action: ```js function onLanguageDetected(lang) { console.log(`Language is: ${lang}`); } function onError(error) { console.log(`Error: ${error}`); } browser.browserAction.onClicked.addListener(() => { browser.tabs.detectLanguage().then(onLanguageDetected, onError); }); ``` Detect and log the language of every open tab when the user clicks a browser action (note that this example requires the "tabs" [permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions)): ```js function onLanguageDetected(url, lang) { console.log(`Language in ${url} is: ${lang}`); } function onError(error) { console.log(`Error: ${error}`); } function detectLanguages(tabs) { for (const tab of tabs) { browser.tabs .detectLanguage(tab.id) .then((lang) => onLanguageDetected(tab.url, lang), onError); } } browser.browserAction.onClicked.addListener(() => { browser.tabs.query({}).then(detectLanguages, onError); }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-detectLanguage) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->