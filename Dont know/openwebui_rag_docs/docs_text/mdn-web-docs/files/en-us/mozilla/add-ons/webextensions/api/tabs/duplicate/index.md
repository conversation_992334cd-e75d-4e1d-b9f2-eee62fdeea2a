Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > duplicate > index.md --- title: tabs.duplicate() slug: Mozilla/Add-ons/WebExtensions/API/tabs/duplicate page-type: webextension-api-function browser-compat: webextensions.api.tabs.duplicate --- {{AddonSidebar}} Duplicates a tab, given its ID. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let duplicating = browser.tabs.duplicate( tabId, // integer duplicateProperties // optional object ) ``` ### Parameters - `tabId` - : `integer`. The ID of the tab to be duplicated. - `duplicateProperties` {{optional_inline}} - : `object`. An object describing how the tab is duplicated. It contains the following properties: - `index` {{optional_inline}} - : `integer`. The position of the new tab in the window. The value is constrained to the range zero to the number of tabs in the window. - `active` {{optional_inline}} - : `boolean`. Whether the tab becomes the active tab in the window. Does not affect whether the window is focused. Defaults to `true`. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with a {{WebExtAPIRef('tabs.Tab')}} object containing details about the duplicated tab. The `Tab` object only contains `url`, `title` and `favIconUrl` if the extension has the [`"tabs"` permission](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions) or matching [host permissions](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#host_permissions). If any error occurs the promise will be rejected with an error message. > [!NOTE] > Beginning with Firefox 68, the promise returned by browser.tabs.duplicate() resolves as soon as the tab has been duplicated. Previously, the promise only resolved once the tab had fully been loaded. ## Examples Duplicate the first tab, and then log the ID of the newly created tab: ```js function onDuplicated(tabInfo) { console.log(tabInfo.id); } function onError(error) { console.log(`Error: ${error}`); } // Duplicate the first tab in the array function duplicateFirstTab(tabs) { console.log(tabs); if (tabs.length > 0) { let duplicating = browser.tabs.duplicate(tabs[0].id); duplicating.then(onDuplicated, onError); } } // Query for all open tabs let querying = browser.tabs.query({}); querying.then(duplicateFirstTab, onError); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-duplicate) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->