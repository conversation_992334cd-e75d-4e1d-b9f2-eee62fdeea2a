Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > remove > index.md --- title: tabs.remove() slug: Mozilla/Add-ons/WebExtensions/API/tabs/remove page-type: webextension-api-function browser-compat: webextensions.api.tabs.remove --- {{AddonSidebar}} Closes one or more tabs. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let removing = browser.tabs.remove( tabIds // integer or integer array ) ``` ### Parameters - `tabIds` - : `integer` or `array` of `integer` The ids of the tab or tabs to close. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with no arguments when all the specified tabs have been removed or their `beforeunload` prompts have been handled. If any error occurs, the promise will be rejected with an error message. ## Examples Close a single tab: ```js function onRemoved() { console.log(`Removed`); } function onError(error) { console.log(`Error: ${error}`); } let removing = browser.tabs.remove(2); removing.then(onRemoved, onError); ``` Close multiple tabs: ```js function onRemoved() { console.log(`Removed`); } function onError(error) { console.log(`Error: ${error}`); } let removing = browser.tabs.remove([15, 14, 1]); removing.then(onRemoved, onError); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-remove) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->