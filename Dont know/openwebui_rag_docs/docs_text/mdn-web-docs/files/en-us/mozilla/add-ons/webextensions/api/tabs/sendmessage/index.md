Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > tabs > sendmessage > index.md --- title: tabs.sendMessage() slug: Mozilla/Add-ons/WebExtensions/API/tabs/sendMessage page-type: webextension-api-function browser-compat: webextensions.api.tabs.sendMessage --- {{AddonSidebar}} Sends a single message from the extension's background scripts (or other privileged scripts, such as popup scripts or options page scripts) to any [content scripts](/en-US/docs/Mozilla/Add-ons/WebExtensions/Content_scripts) or extension pages/iframes that belong to the extension and are running in the specified tab. The message will be received in the extension context by any listeners to the {{WebExtAPIRef("runtime.onMessage")}} event. Listeners may then optionally return something as a response back to the sender. This is an asynchronous function that returns a {{jsxref("Promise")}}. > [!NOTE] > You can also use a [connection-based approach to exchange messages](/en-US/docs/Mozilla/Add-ons/WebExtensions/Content_scripts#connection-based_messaging). ## Syntax ```js-nolint const sending = browser.tabs.sendMessage( tabId, // integer message, // any options // optional object ) ``` ### Parameters - `tabId` - : `integer`. ID of the tab whose content scripts we want to send a message to. - `message` - : `any`. An object that can be serialized (see [Data cloning algorithm](/en-US/docs/Mozilla/Add-ons/WebExtensions/Chrome_incompatibilities#data_cloning_algorithm)). - `options` {{optional_inline}} - : `object`. - `frameId` {{optional_inline}} - : `integer`. Sends the message to a specific frame identified by `frameId` instead of all frames in the tab. Whether the content script is executed in all frames depends on the `all_frames` setting in the [`content_scripts`](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/content_scripts) section of `manifest.json`. ### Return value A {{jsxref("Promise")}} that will be fulfilled with the response object sent by the handler of the message in the content script, or with no arguments if the content script did not send a response. If an error occurs while connecting to the specified tab or any other error occurs, the promise will be rejected with an error message. If several frames respond to the message, the promise is resolved to one of answers. ## Examples Here's an example of a background script that sends a message to the content scripts running in the active tab when the user clicks the browser action. The background script also expects the content script to send a response: ```js // background-script.js "use strict"; function onError(error) { console.error(`Error: ${error}`); } function sendMessageToTabs(tabs) { for (const tab of tabs) { browser.tabs .sendMessage(tab.id, { greeting: "Hi from background script" }) .then((response) => { console.log("Message from the content script:"); console.log(response.response); }) .catch(onError); } } browser.browserAction.onClicked.addListener(() => { browser.tabs .query({ currentWindow: true, active: true, }) .then(sendMessageToTabs) .catch(onError); }); ``` Here's the corresponding content script: ```js // content-script.js "use strict"; browser.runtime.onMessage.addListener((request) => { console.log("Message from the background script:"); console.log(request.greeting); return Promise.resolve({ response: "Hi from content script" }); }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.tabs`](https://developer.chrome.com/docs/extensions/reference/api/tabs#method-sendMessage) API. This documentation is derived from [`tabs.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/tabs.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->