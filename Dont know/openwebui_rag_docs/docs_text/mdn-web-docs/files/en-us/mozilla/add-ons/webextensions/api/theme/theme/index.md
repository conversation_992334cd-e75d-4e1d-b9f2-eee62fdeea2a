Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > theme > theme > index.md --- title: Theme slug: Mozilla/Add-ons/WebExtensions/API/theme/Theme page-type: webextension-api-type browser-compat: webextensions.api.theme.Theme --- {{AddonSidebar}} A Theme object represents the specification of a theme. ## Type A JSON [object](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object) that takes the same properties as the ["theme"](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/theme) manifest key. {{WebExtExamples}} ## Browser compatibility {{Compat}}