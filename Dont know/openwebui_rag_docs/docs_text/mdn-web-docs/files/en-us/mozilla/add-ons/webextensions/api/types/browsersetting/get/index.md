Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > types > browsersetting > get > index.md --- title: get() slug: Mozilla/Add-ons/WebExtensions/API/types/BrowserSetting/get page-type: webextension-api-function --- {{AddonSidebar}} The `BrowserSetting.get()` method gets the current value of the browser setting, and an enumeration indicating how the setting's value is currently controlled. This is an asynchronous function that returns a [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise). ## Syntax ```js-nolint let getting = setting.get( details // object ) ``` ### Parameters - `details` - : An empty object. ### Return value A [`Promise`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise) that will be fulfilled with an object with the following properties: - `value` - : The value of the setting. The type of this property is determined by the particular setting. - `levelOfControl` - : `string`. This represents the way the setting is currently controlled. You can use it to check whether you can modify the setting. See [`BrowserSetting.set()`](/en-US/docs/Mozilla/Add-ons/WebExtensions/API/types/BrowserSetting/set) for details. Its value may be any of the following:<table class="fullwidth-table standard-table"> <tbody> <tr> <td><code>"not_controllable"</code></td> <td>Extensions are not allowed to modify this setting.</td> </tr> <tr> <td><code>"controlled_by_other_extensions"</code></td> <td> Another extension that was installed after this one has modified this setting. </td> </tr> <tr> <td><code>"controllable_by_this_extension"</code></td> <td>This extension is allowed to modify the setting.</td> </tr> <tr> <td><code>"controlled_by_this_extension"</code></td> <td>This extension has already modified the setting.</td> </tr> </tbody> </table> ## Browser compatibility See {{WebExtAPIRef("types.BrowserSetting")}}. ## Example Log the value and level of control for the `networkPredictionEnabled` property of the {{WebExtAPIRef("privacy.network")}} object, for private browsing windows. Note that this requires the "privacy" browser permission. ```js let getting = browser.privacy.network.networkPredictionEnabled.get({}); getting.then((got) => { console.log(`Value: ${got.value}`); console.log(`Control: ${got.levelOfControl}`); }); ``` {{WebExtExamples}} > [!NOTE] > This API is based on Chromium's [`chrome.types`](https://developer.chrome.com/docs/extensions/reference/api/types) API. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->