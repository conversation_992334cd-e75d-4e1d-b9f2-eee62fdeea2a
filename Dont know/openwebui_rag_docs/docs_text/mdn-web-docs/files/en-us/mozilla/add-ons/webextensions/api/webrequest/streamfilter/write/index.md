Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > webrequest > streamfilter > write > index.md --- title: webRequest.StreamFilter.write() slug: Mozilla/Add-ons/WebExtensions/API/webRequest/StreamFilter/write page-type: webextension-api-function browser-compat: webextensions.api.webRequest.StreamFilter.write --- {{AddonSidebar}} Writes some response data to the output stream. You can only call this function after the {{WebExtAPIRef("webRequest.StreamFilter.onstart", "onstart")}} event has fired. ## Syntax ```js-nolint filter.write( data // ArrayBuffer or Uint8Array ) ``` ### Parameters - `data` - : [`Uint8Array`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array) or [`ArrayBuffer`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer): array of bytes containing the data to pass to the browser's rendering engine. ### Return value None. ## Browser compatibility {{Compat}} ## Examples This example uses `write()`, to replace "Example" in the first chunk of the response with "WebExtension Example". ```js function listener(details) { let filter = browser.webRequest.filterResponseData(details.requestId); let decoder = new TextDecoder("utf-8"); let encoder = new TextEncoder(); filter.ondata = (event) => { let str = decoder.decode(event.data, { stream: true }); // Just change any instance of Example in the HTTP response // to WebExtension Example. str = str.replaceAll("Example", "WebExtension Example"); filter.write(encoder.encode(str)); filter.disconnect(); }; // return {}; // not needed } browser.webRequest.onBeforeRequest.addListener( listener, { urls: ["https://example.com/*"], types: ["main_frame"] }, ["blocking"], ); ``` {{WebExtExamples}}