Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > api > windows > onboundschanged > index.md --- title: windows.onBoundsChanged slug: Mozilla/Add-ons/WebExtensions/API/windows/onBoundsChanged page-type: webextension-api-event browser-compat: webextensions.api.windows.onBoundsChanged --- {{AddonSidebar}} Fired when a window is resized or moved. This event is fired when the new bounds are committed. It doesn't fire for in-progress changes. ## Syntax ```js-nolint browser.windows.onBoundsChanged.addListener(listener) browser.windows.onBoundsChanged.removeListener(listener) browser.windows.onBoundsChanged.hasListener(listener) ``` Events have three functions: - `addListener(listener)` - : Adds a listener to this event. - `removeListener(listener)` - : Stop listening to this event. The `listener` argument is the listener to remove. - `hasListener(listener)` - : Check whether a `listener` is registered for this event. Returns `true` if it is listening, `false` otherwise. ## addListener syntax ### Parameters - `listener` - : The function called when this event occurs. The function is passed this argument: - `window` - : A {{WebExtAPIRef('windows.Window')}} object containing details of the window that was resized or moved. ## Examples Log the IDs of windows that are moved or resized: ```js browser.windows.onBoundsChanged.addListener((window) => { console.log(`New window: ${window.id}`); }); ``` {{WebExtExamples}} ## Browser compatibility {{Compat}} > [!NOTE] > This API is based on Chromium's [`chrome.windows`](https://developer.chrome.com/docs/extensions/reference/api/windows#event-onBoundsChanged) API. This documentation is derived from [`windows.json`](https://chromium.googlesource.com/chromium/src/+/master/chrome/common/extensions/api/windows.json) in the Chromium code. <!-- // Copyright 2015 The Chromium Authors. All rights reserved. // // Redistribution and use in source and binary forms, with or without // modification, are permitted provided that the following conditions are // met: // // * Redistributions of source code must retain the above copyright // notice, this list of conditions and the following disclaimer. // * Redistributions in binary form must reproduce the above // copyright notice, this list of conditions and the following disclaimer // in the documentation and/or other materials provided with the // distribution. // * Neither the name of Google Inc. nor the names of its // contributors may be used to endorse or promote products derived from // this software without specific prior written permission. // // THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS // "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT // LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR // A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT // OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, // SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT // LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, // DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY // THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT // (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE // OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. -->