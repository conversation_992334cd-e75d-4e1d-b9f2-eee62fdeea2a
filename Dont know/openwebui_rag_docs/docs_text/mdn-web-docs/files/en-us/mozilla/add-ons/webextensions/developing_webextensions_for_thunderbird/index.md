Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > developing_webextensions_for_thunderbird > index.md --- title: Developing WebExtensions for Thunderbird slug: Mozilla/Add-ons/WebExtensions/Developing_WebExtensions_for_Thunderbird page-type: guide --- {{AddonSidebar}} You'll approach the coding of an extension for Thunderbird in the same way as you would for a Firefox extension; using a text editor or tool of your choice to write the code. ## API differences > [!NOTE] > See [ReadTheDocs](https://webextension-api.thunderbird.net/en/latest/) for Thunderbird specific WebExtension API documentation. Being both Gecko based, Thunderbird supports many of the APIs Firefox supports, with some differences, see [browser compatibility for manifest.json](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json#browser_compatibility) and [browser support for JavaScript APIs](/en-US/docs/Mozilla/Add-ons/WebExtensions/Browser_support_for_JavaScript_APIs) for details. ## See also - [Introduction to Thunderbird Add-On development](https://developer.thunderbird.net/add-ons/about-add-ons) - [Thunderbird specific WebExtension API documentation](https://webextension-api.thunderbird.net/en/latest/) - [Browser support for JavaScript APIs](/en-US/docs/Mozilla/Add-ons/WebExtensions/Browser_support_for_JavaScript_APIs) - [Browser compatibility for manifest.json](/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json#browser_compatibility)