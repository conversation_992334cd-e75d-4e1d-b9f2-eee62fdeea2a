Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md

Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md Path: mdn-web-docs > files > en-us > mozilla > add-ons > webextensions > manifest.json > manifest_version > index.md --- title: manifest_version slug: Mozilla/Add-ons/WebExtensions/manifest.json/manifest_version page-type: webextension-manifest-key browser-compat: webextensions.manifest.manifest_version --- {{AddonSidebar}} <table class="fullwidth-table standard-table"> <tbody> <tr> <th scope="row">Type</th> <td><code>Number</code></td> </tr> <tr> <th scope="row">Mandatory</th> <td>Yes</td> </tr> <tr> <th scope="row">Example</th> <td><pre class="brush: json">"manifest_version": 3</pre></td> </tr> </tbody> </table> This key specifies the version of manifest.json used by this extension. ## Example ```json "manifest_version": 3 ``` ## Browser compatibility {{Compat}}