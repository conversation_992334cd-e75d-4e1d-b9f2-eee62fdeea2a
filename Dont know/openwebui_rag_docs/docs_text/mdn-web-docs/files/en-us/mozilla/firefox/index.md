Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md

Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md Path: mdn-web-docs > files > en-us > mozilla > firefox > index.md --- title: Firefox slug: Mozilla/Firefox page-type: landing-page --- {{FirefoxSidebar}} [Firefox](https://www.mozilla.org/en-US/firefox/new/) is Mozilla's popular Web browser, available for multiple platforms including Windows, macOS, and Linux on the desktop and all Android and iOS mobile devices. With broad compatibility, the latest in Web technologies, and powerful development tools, Firefox is a great choice for both Web developers and end users. Firefox is an open source project; much of the code is contributed by our huge community of volunteers. Here you can learn about how to contribute to the Firefox project and you will also find links to information about the construction of Firefox add-ons, using the developer tools in Firefox, and other topics. Learn how to create add-ons for [Firefox](https://www.mozilla.org/en-US/firefox/new/), how to develop and build Firefox itself, and how the internals of Firefox and its subprojects work. ## Key resources - Firefox developer guide - : Our [developer guide](https://firefox-source-docs.mozilla.org/contributing/index.html) explains how to get Firefox source code, how to build it on Linux, macOS and Windows, how to find your way around, and how to contribute to the project. - Firefox add-on guide - : The [Add-on guide](/en-US/docs/Mozilla/Add-ons) provides information about developing and deploying Firefox extensions. - Developer release notes - : [Developer-focused release notes](/en-US/docs/Mozilla/Firefox/Releases); learn what new capabilities for both websites and add-ons arrive in each version of Firefox. ## Firefox channels Firefox is available in five **channels**. ### Firefox Nightly Each night we build Firefox from the latest code in [mozilla-central](https://hg-edge.mozilla.org/mozilla-central/). These builds are for Firefox developers or those who want to try out the very latest cutting edge features while they're still under active development. [Download Firefox Nightly](https://www.mozilla.org/en-US/firefox/channel/desktop/#nightly) ### Firefox Developer Edition This is a version of Firefox tailored for developers. Firefox Developer Edition has all the latest developer tools that have reached beta. We also add some extra features for developers that are only available in this channel. It uses its own path and profile, so that you can run it alongside Release or Beta Firefox. [Download Firefox Developer Edition](https://www.mozilla.org/en-US/firefox/developer/) ### Firefox Beta Every four weeks, we take the features that are stable enough, and create a new version of Firefox Beta. Firefox Beta builds are for Firefox enthusiasts to test what's destined to become the next released Firefox version. [Download Firefox Beta](https://www.mozilla.org/en-US/firefox/channel/desktop/#beta) ### Firefox After stabilizing for another four weeks in Beta, we're ready to ship the new features to hundreds of millions of users in a new release version of Firefox. [Download Firefox](https://www.mozilla.org/en-US/firefox/new/) ### Firefox Extended Support Release (ESR) Firefox ESR is the long-term support edition of Firefox for desktop for use by organizations including schools, universities, businesses and others who need extended support for mass deployments. [Download Firefox ESR](https://www.mozilla.org/en-US/firefox/all/#product-desktop-esr) ## Contents {{SubpagesWithSummaries}} ## See also - [Mailing list](https://groups.google.com/a/mozilla.org/g/firefox-dev) - [Release schedule](https://whattrainisitnow.com/calendar/)