Path: mdn-web-docs > files > en-us > web > accessibility > aria > guides > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > guides > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > guides > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > guides > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > guides > index.md --- title: ARIA guides slug: Web/Accessibility/ARIA/Guides page-type: landing-page sidebar: accessibilitysidebar --- Accessible Rich Internet Applications (**ARIA**) define ways to make the web more accessible to people with disabilities. This page lists the guides that you can use to help improve the accessibility of web page features such as tables, forms, and keyboard-navigation. {{SubPagesWithSummaries}} ## See also - [Designing accessible forms](https://www.w3.org/WAI/tutorials/forms/) - [Understanding WAI-ARIA basics](/en-US/docs/Learn_web_development/Core/Accessibility/WAI-ARIA_basics) - [Creating keyboard-navigable JavaScript widgets](/en-US/docs/Web/Accessibility/Guides/Keyboard-navigable_JavaScript_widgets) - [Using ARIA for labels with embedded fields inside them](/en-US/docs/Web/Accessibility/ARIA/Guides/Multipart_labels) - [Managing focus in composite widgets](https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/) (`aria-activedescendant` vs. roving tabindex) - [Testing ARIA](https://www.w3.org/WAI/test-evaluate/) - [Displaying accessible tables](https://www.w3.org/WAI/tutorials/tables/) - [Labeling widgets](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) - [Landmark roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/landmark_role) - [ARIA live regions](/en-US/docs/Web/Accessibility/ARIA/Guides/Live_regions) - [HTML Drag and Drop API](/en-US/docs/Web/API/HTML_Drag_and_Drop_API) - [ARIA: presentation role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/presentation_role)