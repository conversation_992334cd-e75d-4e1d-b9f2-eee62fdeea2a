Path: mdn-web-docs > files > en-us > web > accessibility > aria > how_to > file_aria-related_bugs > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > how_to > file_aria-related_bugs > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > how_to > file_aria-related_bugs > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > how_to > file_aria-related_bugs > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > how_to > file_aria-related_bugs > index.md --- title: How to file ARIA-related bugs slug: Web/Accessibility/ARIA/How_to/File_ARIA-related_bugs page-type: guide sidebar: accessibilitysidebar --- The state of ARIA technology has always depended on the community. If you notice an implementation issue, please take a little time and let the developers know. Here's where to file bugs: ### Screen Readers <table style="width: 100%;"> <thead> <tr> <th>Software</th> <th>Where to file</th> <th>Notes</th> </tr> </thead> <tbody> <tr> <td><a href="https://www.freedomscientific.com/products/software/jaws/">Freedom Scientific JAWS</a></td> <td><a href="https://support.freedomscientific.com/Forms/TechSupport">JAWS technical support form</a></td> <td></td> </tr> <tr> <td><a href="https://www.nvaccess.org/">Non Visual Desktop Access (NVDA)</a></td> <td><a href="https://github.com/nvaccess/nvda">File NVDA bugs</a></td> <td>Discuss NVDA issues</td> </tr> </tbody> </table> ### Browsers <table style="width: 100%;"> <thead> <tr> <th>Software</th> <th>Where to file</th> <th>Notes</th> </tr> </thead> <tbody> <tr> <td>Apple Safari</td> <td><a href="https://www.webkit.org/reporting-bugs/">File WebKit.org bugs</a></td> <td></td> </tr> <tr> <td>Google Chrome</td> <td><a href="https://issues.chromium.org/issues">File Chromium bugs</a></td> <td></td> </tr> <tr> <td>Mozilla Firefox</td> <td><a href="https://bugzilla.mozilla.org/">File Firefox bugs </a></td> <td>Use Component: Disability Access APIs</td> </tr> <tr> <td>Opera</td> <td><a href="https://bugs.opera.com/wizard/">File Opera bugs</a></td> <td>Use [ARIA] in the summary field</td> </tr> </tbody> </table>