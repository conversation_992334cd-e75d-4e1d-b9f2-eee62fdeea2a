Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > command_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > command_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > command_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > command_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > command_role > index.md --- title: "ARIA: command role" slug: Web/Accessibility/ARIA/Reference/Roles/command_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#command sidebar: accessibilitysidebar --- The `command` role defines a widget that performs an action but does not receive input data. > [!NOTE] > The `command` role is an abstract role. It is included here for completeness of documentation. It should not be used by web authors. ## Description A command is form of widget that performs an action but does not receive input data. It is a superclass for the widget roles [`button`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/button_role), [`link`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/link_role), and [`menuitem`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/menuitem_role) ## Best Practices Do not use. ## Specifications {{Specifications}} ## See also - [ARIA: `widget` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/widget_role) - [ARIA: `button` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/button_role) - [ARIA: `link` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/link_role) - [ARIA: `menuitem` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/menuitem_role)