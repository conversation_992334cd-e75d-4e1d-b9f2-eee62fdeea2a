Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > composite_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > composite_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > composite_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > composite_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > composite_role > index.md --- title: "ARIA: composite role" slug: Web/Accessibility/ARIA/Reference/Roles/composite_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#composite sidebar: accessibilitysidebar --- The `composite` [abstract role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#6._abstract_roles) indicates a widget that may contain navigable descendants or owned children. > [!WARNING] > The `composite` role is an abstract role. It is included here for completeness of documentation. It should not be used by web authors. ## Description `Composite` is an abstract role used for the ontology. Don't use this role in content. Instead, use the composite subclasses of [`grid`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/grid_role), [`select`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/select_role), [`spinbutton`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/spinbutton_role), and [`tablist`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tablist_role). ## Specifications {{Specifications}} ## See also - [ARIA: `widget` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/widget_role) - [ARIA: `grid` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/grid_role) - [ARIA: `select` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/select_role) - [ARIA: `spinbutton` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/spinbutton_role) - [ARIA: `tablist` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tablist_role)