Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > input_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > input_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > input_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > input_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > input_role > index.md --- title: "ARIA: input role" slug: Web/Accessibility/ARIA/Reference/Roles/input_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#input sidebar: accessibilitysidebar --- The `input` abstract role is a generic type of widget that allows user input. > [!WARNING] > The `input` role is an [abstract role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#6._abstract_roles). It is included here for completeness of documentation. It is not to be used by web authors. ## Description The `input` role is an abstract role. It must not be used by web authors. It is the superclass for input widgets that provide for user input, including [`checkbox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/checkbox_role), [`radio`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/radio_role), and [`textbox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/textbox_role). For all three, consider using the {{HTMLElement("input")}} element of type [`checkbox`](/en-US/docs/Web/HTML/Reference/Elements/input/checkbox), [`radio`](/en-US/docs/Web/HTML/Reference/Elements/input/radio) and [`text`](/en-US/docs/Web/HTML/Reference/Elements/input/text), respectively. ## Specifications {{Specifications}} ## See also - [ARIA: `widget` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/widget_role) - [ARIA: `checkbox` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/checkbox_role) - [ARIA: `combobox` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/combobox_role) - [ARIA: `option` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/option_role) - [ARIA: `radio` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/radio_role) - [ARIA: `slider` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/slider_role) - [ARIA: `spinbutton` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/spinbutton_role) - [ARIA: `textbox` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/textbox_role) - [HTML: the `input` element](/en-US/docs/Web/HTML/Reference/Elements/input)