Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > none_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > none_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > none_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > none_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > none_role > index.md --- title: "ARIA: none role" slug: Web/Accessibility/ARIA/Reference/Roles/none_role page-type: aria-role sidebar: accessibilitysidebar --- The `none` role is a synonym for the [`presentation`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/presentation_role) role; they both remove an element's implicit ARIA semantics from being exposed to the accessibility tree. See the [`presentation`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/presentation_role) role for more information.