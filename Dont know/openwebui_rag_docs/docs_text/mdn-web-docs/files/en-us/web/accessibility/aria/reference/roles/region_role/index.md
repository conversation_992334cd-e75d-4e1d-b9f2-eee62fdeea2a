Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > region_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > region_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > region_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > region_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > region_role > index.md --- title: "ARIA: region role" slug: Web/Accessibility/ARIA/Reference/Roles/region_role page-type: aria-role spec-urls: - https://w3c.github.io/aria/#region - https://www.w3.org/WAI/ARIA/apg/patterns/landmarks/examples/region.html sidebar: accessibilitysidebar --- The **`region`** role is used to identify document areas the author deems significant. It is a generic landmark available to aid in navigation when none of the other landmark roles are appropriate. ```html <div role="region" aria-label="Example"> <!-- region content --> </div> ``` ## Description The `region` role is an [ARIA landmark](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#3._landmark_roles) role. The `region` role should be reserved for sections of content sufficiently important that users will likely want to navigate to the section easily and to have it listed in a summary of the page. A region role is a more generic term, and should only be used if the section needing to be identified is not accurately described by one of the other landmark roles, such as [`banner`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/banner_role), [`main`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/main_role), [`contentinfo`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/contentinfo_role), [`complementary`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/complementary_role), or [`navigation`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/navigation_role). Every element with a `region` role should include a label that describes the purpose of the content in the region, preferably with an [`aria-labelledby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby) referencing a visible header. If no visible appropriate header is present, [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) should be used. The `region` landmark role's content should make sense if separated from the main content of the document. Using the {{HTMLElement('section')}} element will automatically communicate a section has a role of `region` if it is given an accessible name. Developers should always prefer using the correct semantic HTML element, in this case `<section>`, over using ARIA. ### Associated WAI-ARIA roles, states, and properties - [`aria-labelledby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby) or [`aria-label`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-label) - : Use this attribute to label the region. Often, the value of the `aria-labelledby` attribute will be the id of the element used to title the section. If no visible appropriate header is present, `aria-label` should be used. ## Examples ```html <div role="region" aria-labelledby="region-heading"> <h2 id="region-heading"> This heading's `id` attribute helps this region have an accessible name </h2> <!-- region content --> </div> ``` ## Accessibility concerns Use sparingly! [Landmark roles](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#3._landmark_roles) are intended to be used sparingly, to identify larger overall sections of the document. Using too many landmark roles can create "noise" in screen readers, making it difficult to understand the overall layout of the page. Only use the `region` role if no other relevant [content sectioning](/en-US/docs/Web/HTML/Reference/Elements#content_sectioning) element or [landmark role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#3._landmark_roles) applies. If multiple regions exist on a page, it may be worth reinvestigating the page's overall structure. ## Best practices ### Prefer HTML Using the {{HTMLElement('section')}} element will automatically communicate that a section has a role of `region` if it is given an accessible name. If at all possible, prefer using {{HTMLElement('section')}} instead. ### Labeling landmarks If there is more than one `region` landmark role in a document, provide a unique label for each one. This label will allow an assistive technology user to be able to quickly understand the purpose of each landmark. ```html <div role="region" aria-labelledby="use-discretion"> <h3 id="use-discretion">Please use the `region` role with discretion</h3> <!-- content --> </div> <div role="region" aria-labelledby="please-reconsider"> <h3 id="please-reconsider">Please reconsider your document structure</h3> <!-- content --> </div> ``` In this example, the region's label is being generated by the [`aria-labelledby` attribute](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-labelledby). ### Scrolling content areas with overflow text If there is a content area with `tabindex="0"`, add `role="region"` to convey to screen reader users that is a generic region. This is done to allow keyboard-only users to scroll regions with overflow text. ### SVG `role="region"` can be declared on areas of {{SVGElement('svg')}} along with an `aria-label` to allow individual sections of the SVG to be described. ## Specifications {{Specifications}} ## See also - The {{HTMLElement('section')}} element - [region (role): Accessible Rich Internet Applications (WAI-ARIA) 1.1](https://www.w3.org/TR/wai-aria/#region) - [ARIA: `banner` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/banner_role) - [ARIA: `main` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/main_role) - [ARIA: `contentinfo` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/contentinfo_role) - [ARIA: `complementary` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/complementary_role) - [ARIA: `navigation` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/navigation_role) - [Landmark roles: Using ARIA: Roles, States, and Properties](/en-US/docs/Web/Accessibility/ARIA/Guides/Techniques#landmark_roles) - [Using WAI-ARIA Landmarks 2013 | The Paciello Group](https://www.tpgi.com/using-wai-aria-landmarks-2013/) - [Accessible Landmarks | scottohara.me](https://www.scottohara.me/blog/2018/03/03/landmarks.html)