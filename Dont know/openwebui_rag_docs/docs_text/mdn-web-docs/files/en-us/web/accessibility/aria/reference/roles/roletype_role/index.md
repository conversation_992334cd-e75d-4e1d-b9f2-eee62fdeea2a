Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > roletype_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > roletype_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > roletype_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > roletype_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > roletype_role > index.md --- title: "ARIA: roletype role" slug: Web/Accessibility/ARIA/Reference/Roles/roletype_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#roletype sidebar: accessibilitysidebar --- The **`roletype`** role, an [abstract role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#6._abstract_roles), is the base role from which all other ARIA roles inherit. > [!WARNING] > The `roletype` role is an abstract role used for the ontology. It is included here for completeness of documentation. It should not be used by web authors. ## Description The `roletype` role's properties describe the structural and functional purpose of objects that are assigned this role, or "instances". A role is a concept that can be used to understand and operate instances. ## Specifications {{Specifications}} ## See also - [ARIA: `structure` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/structure_role) - [ARIA: `widget` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/widget_role) - [ARIA: `window` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/window_role) - The HTML [`rel`](/en-US/docs/Web/HTML/Reference/Attributes/rel) attribute