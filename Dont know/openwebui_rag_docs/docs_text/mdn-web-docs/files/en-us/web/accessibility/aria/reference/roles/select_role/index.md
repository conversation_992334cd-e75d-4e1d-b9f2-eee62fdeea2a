Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > select_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > select_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > select_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > select_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > select_role > index.md --- title: "ARIA: select role" slug: Web/Accessibility/ARIA/Reference/Roles/select_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#select sidebar: accessibilitysidebar --- The **`select` role**, an abstract role, is superclass role for form widgets that allows the user to make selections from a set of choices. > [!WARNING] > The `select` role is an [abstract role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles#6._abstract_roles). It is included here for completeness of documentation. It should not be used by web authors. ## Description The structural `select` role, an abstract role, is superclass role for four form widgets, [`listbox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/listbox_role), [`menu`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/menu_role), [`radiogroup`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/radiogroup_role), and [`tree`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tree_role), which allow users to make selections from a set of choices. ## Specifications {{Specifications}} ## See also - [ARIA: `composite` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/composite_role) - [ARIA: `group` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/group_role) - [ARIA: `listbox` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/listbox_role) - [ARIA: `menu` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/menu_role) - [ARIA: `radiogroup` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/radiogroup_role) - [ARIA: `tree` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/tree_role)