Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > window_role > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > window_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > window_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > window_role > index.md Path: mdn-web-docs > files > en-us > web > accessibility > aria > reference > roles > window_role > index.md --- title: "ARIA: window role" slug: Web/Accessibility/ARIA/Reference/Roles/window_role page-type: aria-role spec-urls: https://w3c.github.io/aria/#window sidebar: accessibilitysidebar --- The `window` role defines a browser or app window. > [!WARNING] > The `window` role is an abstract role. It is included here for completeness of documentation. It should not be used by web authors. ## Description The `window` role, an abstract role, is a superclass for roles defining a browser or app window. The sub-class roles, currently only the [`dialog`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/dialog_role) role, have a window-like <abbr>GUI</abbr>, or graphical user interface, whether it's a full native window or just a section of a document styled to look like a window, where `role="dialog"` would be appropriate. ## Specifications {{Specifications}} ## See also - [ARIA: `roletype` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/roletype_role) - [ARIA: `dialog` role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/dialog_role)