Path: mdn-web-docs > files > en-us > web > accessibility > guides > understanding_wcag > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > guides > understanding_wcag > index.md Path: mdn-web-docs > files > en-us > web > accessibility > guides > understanding_wcag > index.md Path: mdn-web-docs > files > en-us > web > accessibility > guides > understanding_wcag > index.md Path: mdn-web-docs > files > en-us > web > accessibility > guides > understanding_wcag > index.md --- title: Understanding the Web Content Accessibility Guidelines (WCAG) short-title: Understanding WCAG slug: Web/Accessibility/Guides/Understanding_WCAG page-type: guide sidebar: accessibilitysidebar --- This set of articles provides quick explanations to help you understand the steps that need to be taken to conform to the recommendations outlined in the {{glossary("WCAG", "Web Content Accessibility Guidelines")}} (WCAG). The WCAG guidelines are a set of recommendations for making web content more accessible developed by W3C's {{Glossary("WAI")}}, primarily for people with disabilities but also for all user agents, including some highly limited devices or services, such as digital assistants. WCAG 2.2 is the most recent version of the guidelines with WCAG 2.1 and 2.0 also widely used. WCAG 3.0 is the working draft. Newer versions of WCAG are not meant to supersede previous versions, but WAI recommends using the most recent version to ensure the best possible accessibility for your website. ## The four principles WCAG is broadly broken down into four principles major things that web content **must be** to be considered accessible (see [Understanding the Four Principles of Accessibility](https://www.w3.org/WAI/WCAG22/Understanding/intro#understanding-the-four-principles-of-accessibility) for the WCAG definitions). Each of the links below will take you to pages that further expand on these areas, giving you practical advice on how to write your web content so it conforms to the success criteria outlined in the WCAG 2 four guiding principles. - [Perceivable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Perceivable): Users must be able to perceive it in some way, using one or more of their senses. - [Operable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Operable): Users must be able to control UI elements (e.g., buttons must be clickable in some way mouse, keyboard, voice command, etc.). - [Understandable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Understandable): The content must be understandable to its users. - [Robust](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Robust): The content must be developed using well-adopted web standards that will work across different browsers, now and in the future. We also included two additional WCAG resources focused on making sites [keyboard accessible](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Keyboard) and provided descriptive names or labels with [text labels and names](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Text_labels_and_names). ## Legal standing This introductory documentation is intended to provide practical information to help you build better, more accessible websites. However, we are not lawyers, and none of this constitutes legal advice. If you are worried about the legal implications of web accessibility, we recommend that you check the specific legislation governing accessibility for the web and public resources in your country or locale and seek the advice of a qualified lawyer. [What is accessibility?](/en-US/docs/Learn_web_development/Core/Accessibility/What_is_accessibility) and particularity the [Accessibility guidelines and the law](/en-US/docs/Learn_web_development/Core/Accessibility/What_is_accessibility#accessibility_guidelines_and_the_law) section provide more related information. ## See also - [WCAG: Perceivable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Perceivable) principle - [WCAG: Operable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Operable) principle - [WCAG: Understandable](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Understandable) principle - [WCAG: Robust](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Robust) principle - [WCAG text labels and names](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Text_labels_and_names) - [WCAG keyboard accessibility](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG/Keyboard) - [WCAG Overview](https://www.w3.org/WAI/standards-guidelines/wcag/) - [WCAG 2.2](https://www.w3.org/TR/WCAG22/)