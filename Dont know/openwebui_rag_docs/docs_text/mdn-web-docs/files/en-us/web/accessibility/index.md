Path: mdn-web-docs > files > en-us > web > accessibility > index.md

Path: mdn-web-docs > files > en-us > web > accessibility > index.md Path: mdn-web-docs > files > en-us > web > accessibility > index.md Path: mdn-web-docs > files > en-us > web > accessibility > index.md Path: mdn-web-docs > files > en-us > web > accessibility > index.md --- title: Accessibility slug: Web/Accessibility page-type: landing-page sidebar: accessibilitysidebar --- **Accessibility** (often abbreviated to **A11y** as in, "a", then 11 characters, and then "y") in web development means enabling as many people as possible to use websites, even when those people's abilities are limited in some way. For many people, technology makes things easier. For people with disabilities, technology makes things possible. Accessibility means developing content to be as accessible as possible, no matter an individual's physical and cognitive abilities and how they access the web. > **The Web is fundamentally designed to work for all people**, whatever their hardware, software, language, location, or ability. > When the Web meets this goal, it is accessible to people with a diverse range of hearing, movement, sight, and cognitive ability. \ > ([W3C - Accessibility](https://www.w3.org/standards/webdesign/accessibility)) ## Accessibility guides - [Accessibility information for web authors](/en-US/docs/Web/Accessibility/Guides/Information_for_Web_authors) - : This document lists guidelines and regulations, how-to's, and tools for checking and repairing accessibility problems with websites. - [Personalization to help browse safely](/en-US/docs/Web/Accessibility/Guides/Browsing_safely) - : This article discusses making web content accessible for those with vestibular disorders, and those who support them, by taking advantage of personalization and accessibility settings built into the operating systems. - [Accessible web applications and widgets](/en-US/docs/Web/Accessibility/Guides/Accessible_web_applications_and_widgets) - : Most JavaScript libraries offer a library of client-side widgets that mimic the behavior of familiar desktop interfaces. While this results in a widget that looks like its desktop counterpart, there usually isn't enough semantic information in the markup to be usable by an assistive technology. This document describes techniques to improve accessibility of such widgets. - [Keyboard-navigable JavaScript widgets](/en-US/docs/Web/Accessibility/Guides/Keyboard-navigable_JavaScript_widgets) - : Until now, web developers who wanted to make their styled `<div>` and `<span>` based widgets accessible have lacked proper techniques. **Keyboard accessibility** is part of the minimum accessibility requirements, which a developer should be aware of. This document describes techniques to make JavaScript widgets accessible with the keyboard. - [Mobile accessibility checklist](/en-US/docs/Web/Accessibility/Guides/Mobile_accessibility_checklist) - : This document provides a concise checklist of accessibility requirements for mobile app developers. - [Understanding the Web Content Accessibility Guidelines (WCAG)](/en-US/docs/Web/Accessibility/Guides/Understanding_WCAG) - : A set of articles that provide quick explanations to help you understand the steps that need to be taken to conform to the recommendations outlined in the Web Content Accessibility Guidelines (WCAG). - [Cognitive accessibility](/en-US/docs/Web/Accessibility/Guides/Cognitive_accessibility) - : Cognitive accessibility covers accessibility considerations for people with cognition and learning disabilities. This document introduces cognitive accessibility and improving accessibility of the web for people with cognitive and learning differences. - [Accessibility and spatial patterns](/en-US/docs/Web/Accessibility/Guides/Accessibility_and_Spatial_Patterns) - : This document describes visual patterns that can induce physical symptoms in people who have photosensitive epilepsy, vestibular disorders, or other perceptual issues. - [Web Accessibility: Understanding Colors and Luminance](/en-US/docs/Web/Accessibility/Guides/Colors_and_Luminance) - : While understanding color, luminance, and saturation is important for design and readability for all sighted users, they are essential for those with reduced vision and color-deficient vision and those with specific neurological, cognitive, and other impairments. - [Web accessibility for seizures and physical reactions](/en-US/docs/Web/Accessibility/Guides/Seizure_disorders) - : Some types of visual web content can induce seizures in people with certain brain disorders. This article helps you understand the types of content that can be problematic and find tools and strategies to help you avoid them. - [ARIA](/en-US/docs/Web/Accessibility/ARIA) - : This is a collection of articles to learn how to use Accessible Rich Internet Applications (ARIA) to make your HTML documents more accessible. ## Tutorials for beginners The MDN [Accessibility Learning Area](/en-US/docs/Learn_web_development/Core/Accessibility) contains modern, up-to-date tutorials covering the following accessibility essentials: - [What is accessibility?](/en-US/docs/Learn_web_development/Core/Accessibility/What_is_accessibility) - : This article starts off the module with a good look at what accessibility actually is this includes what groups of people we need to consider and why, what tools different people use to interact with the Web, and how we can make accessibility part of our web development workflow. - [HTML: A good basis for accessibility](/en-US/docs/Learn_web_development/Core/Accessibility/HTML) - : A great deal of web content can be made accessible just by making sure that the correct HTML elements are used for the correct purpose at all times. This article looks in detail at how HTML can be used to ensure maximum accessibility. - [CSS and JavaScript accessibility best practices](/en-US/docs/Learn_web_development/Core/Accessibility/CSS_and_JavaScript) - : CSS and JavaScript, when used properly, also have the potential to allow for accessible web experiences. They can significantly harm accessibility if misused. This article outlines some CSS and JavaScript best practices that should be considered to ensure that even complex content is as accessible as possible. - [WAI-ARIA basics](/en-US/docs/Learn_web_development/Core/Accessibility/WAI-ARIA_basics) - : Following on from the previous article, sometimes making complex UI controls that involve unsemantic HTML and dynamic JavaScript-updated content can be difficult. WAI-ARIA is a technology that can help with such problems by adding in further semantics that browsers and assistive technologies can recognize and let users know what is going on. Here we'll show how to use it at a basic level to improve accessibility. - [Accessible multimedia](/en-US/docs/Learn_web_development/Core/Accessibility/Multimedia) - : Another category of content that can create accessibility problems is multimedia video, audio, and image content need to be given proper textual alternatives so that they can be understood by assistive technologies and their users. This article shows how. - [Mobile accessibility](/en-US/docs/Learn_web_development/Core/Accessibility/Mobile) - : With web access on mobile devices being so popular and popular platforms such as iOS and Android having fully-fledged accessibility tools, it is important to consider the accessibility of your web content on these platforms. This article looks at mobile-specific accessibility considerations. ## References - [ARIA reference](/en-US/docs/Web/Accessibility/ARIA/Reference) - : Reference documentation for Accessible Rich Internet Applications (ARIA) attributes and roles. ## See also - [Developer guides](/en-US/docs/MDN/Guides) - [WAI Interest Group](https://www.w3.org/WAI/about/groups/waiig/)