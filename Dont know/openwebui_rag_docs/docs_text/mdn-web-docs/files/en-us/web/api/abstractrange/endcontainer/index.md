Path: mdn-web-docs > files > en-us > web > api > abstractrange > endcontainer > index.md

Path: mdn-web-docs > files > en-us > web > api > abstractrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > endcontainer > index.md --- title: "AbstractRange: endContainer property" short-title: endContainer slug: Web/API/AbstractRange/endContainer page-type: web-api-instance-property browser-compat: api.AbstractRange.endContainer --- {{APIRef("DOM")}} The read-only **`endContainer`** property of the {{domxref("AbstractRange")}} interface returns the {{domxref("Node")}} in which the end of the range is located. ## Value The {{domxref("Node")}} which contains the last character of the range. ## Example ```js let endNode = range.endContainer; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}