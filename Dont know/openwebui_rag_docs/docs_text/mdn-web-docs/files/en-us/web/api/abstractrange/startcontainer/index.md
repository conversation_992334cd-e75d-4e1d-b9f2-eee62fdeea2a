Path: mdn-web-docs > files > en-us > web > api > abstractrange > startcontainer > index.md

Path: mdn-web-docs > files > en-us > web > api > abstractrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startcontainer > index.md --- title: "AbstractRange: startContainer property" short-title: startContainer slug: Web/API/AbstractRange/startContainer page-type: web-api-instance-property browser-compat: api.AbstractRange.startContainer --- {{APIRef("DOM")}} The read-only **`startContainer`** property of the {{domxref("AbstractRange")}} interface returns the start {{domxref("Node")}} for the range. ## Value The {{domxref("Node")}} inside which the start position of the range is found. ## Example ```js let startNode = range.startContainer; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}