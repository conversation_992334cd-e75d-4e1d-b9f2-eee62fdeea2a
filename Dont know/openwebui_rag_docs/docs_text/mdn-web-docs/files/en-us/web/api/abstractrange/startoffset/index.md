Path: mdn-web-docs > files > en-us > web > api > abstractrange > startoffset > index.md

Path: mdn-web-docs > files > en-us > web > api > abstractrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > abstractrange > startoffset > index.md --- title: "AbstractRange: startOffset property" short-title: startOffset slug: Web/API/AbstractRange/startOffset page-type: web-api-instance-property browser-compat: api.AbstractRange.startOffset --- {{APIRef("DOM")}} The read-only **`startOffset`** property of the {{domxref("AbstractRange")}} interface returns the offset into the start node of the range's start position. ## Value An integer value indicating the number of characters into the {{domxref("Node")}} indicated by {{domxref("AbstractRange.startContainer", "startContainer")}} at which the first character of the range is located. ## Example ```js let startOffset = range.startOffset; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}