Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md

Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md Path: mdn-web-docs > files > en-us > web > api > animation > effect > index.md --- title: "Animation: effect property" short-title: effect slug: Web/API/Animation/effect page-type: web-api-instance-property browser-compat: api.Animation.effect --- {{ APIRef("Web Animations") }} The **`Animation.effect`** property of the [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) gets and sets the target effect of an animation. The target effect may be either an effect object of a type based on {{domxref("AnimationEffect")}}, such as {{domxref("KeyframeEffect")}}, or `null`. ## Value A {{domxref("AnimationEffect")}} object describing the target animation effect for the animation, or `null` to indicate no active effect. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - {{domxref("AnimationEffect")}} - {{domxref("Animation")}}