Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md

Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md Path: mdn-web-docs > files > en-us > web > api > animation > pending > index.md --- title: "Animation: pending property" short-title: pending slug: Web/API/Animation/pending page-type: web-api-instance-property browser-compat: api.Animation.pending --- {{APIRef("Web Animations")}} The read-only **`Animation.pending`** property of the [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) indicates whether the animation is currently waiting for an asynchronous operation such as initiating playback or pausing a running animation. ## Value **`true`** if the animation is pending, **`false`** otherwise. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Animation")}} for other methods and properties you can use to control web page animation. - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API)