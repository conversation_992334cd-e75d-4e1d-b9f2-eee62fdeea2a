Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md

Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md Path: mdn-web-docs > files > en-us > web > api > animationevent > animationname > index.md --- title: "AnimationEvent: animationName property" short-title: animationName slug: Web/API/AnimationEvent/animationName page-type: web-api-instance-property browser-compat: api.AnimationEvent.animationName --- {{APIRef("Web Animations")}} The **`AnimationEvent.animationName`** read-only property is a string containing the value of the {{cssxref("animation-name")}} CSS property associated with the transition. ## Value A string containing the value of the {{cssxref("animation-name")}} CSS property. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using CSS animations](/en-US/docs/Web/CSS/CSS_animations/Using_CSS_animations) - Animation-related CSS properties and at-rules: {{cssxref("animation")}}, {{cssxref("animation-delay")}}, {{cssxref("animation-direction")}}, {{cssxref("animation-duration")}}, {{cssxref("animation-fill-mode")}}, {{cssxref("animation-iteration-count")}}, {{cssxref("animation-name")}}, {{cssxref("animation-play-state")}}, {{cssxref("animation-timing-function")}}, {{cssxref("@keyframes")}}. - The {{domxref("AnimationEvent")}} interface it belongs to.