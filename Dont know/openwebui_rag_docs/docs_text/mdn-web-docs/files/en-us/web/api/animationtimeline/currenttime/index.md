Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md

Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md Path: mdn-web-docs > files > en-us > web > api > animationtimeline > currenttime > index.md --- title: "AnimationTimeline: currentTime property" short-title: currentTime slug: Web/API/AnimationTimeline/currentTime page-type: web-api-instance-property browser-compat: api.AnimationTimeline.currentTime --- {{ APIRef("Web Animations") }} The **`currentTime`** read-only property of the [Web Animations API](/en-US/docs/Web/API/Web_Animations_API)'s {{domxref("AnimationTimeline")}} interface returns the timeline's current time in milliseconds, or `null` if the timeline is inactive. ## Value A number representing the timeline's current time in milliseconds, or `null` if the timeline is inactive. ## Reduced time precision To offer protection against timing attacks and [fingerprinting](/en-US/docs/Glossary/Fingerprinting), the precision of `animationTimeline.currentTime` might get rounded depending on browser settings. In Firefox, the `privacy.reduceTimerPrecision` preference is enabled by default and defaults to 2ms. You can also enable `privacy.resistFingerprinting`, in which case the precision will be 100ms or the value of `privacy.resistFingerprinting.reduceTimerPrecision.microseconds`, whichever is larger. For example, with reduced time precision, the result of `animationTimeline.currentTime` will always be a multiple of 0.002, or a multiple of 0.1 (or `privacy.resistFingerprinting.reduceTimerPrecision.microseconds`) with `privacy.resistFingerprinting` enabled. ```js // reduced time precision (2ms) in Firefox 60 animationTimeline.currentTime; // Might be: // 23.404 // 24.192 // 25.514 // // reduced time precision with `privacy.resistFingerprinting` enabled animationTimeline.currentTime; // Might be: // 49.8 // 50.6 // 51.7 // ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - {{domxref("AnimationTimeline")}} - {{domxref("DocumentTimeline")}} inherits this property - {{domxref("Document.timeline")}} returns a timeline object which inherits this property