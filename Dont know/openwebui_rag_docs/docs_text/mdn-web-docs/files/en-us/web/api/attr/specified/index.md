Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md

Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md Path: mdn-web-docs > files > en-us > web > api > attr > specified > index.md --- title: "Attr: specified property" short-title: specified slug: Web/API/Attr/specified page-type: web-api-instance-property status: - deprecated browser-compat: api.Attr.specified --- {{APIRef("DOM")}}{{Deprecated_header}} The read-only **`specified`** property of the {{domxref("Attr")}} interface always returns `true`. ## Value Always returns `true`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}