Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md

Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > duration > index.md --- title: "AudioData: duration property" short-title: duration slug: Web/API/AudioData/duration page-type: web-api-instance-property browser-compat: api.AudioData.duration --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`duration`** read-only property of the {{domxref("AudioData")}} interface returns the duration in microseconds of this `AudioData` object. ## Value An integer. ## Examples The below example prints the value of `duration` to the console. ```js console.log(AudioData.duration); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}