Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md

Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofchannels > index.md --- title: "AudioData: numberOfChannels property" short-title: numberOfChannels slug: Web/API/AudioData/numberOfChannels page-type: web-api-instance-property browser-compat: api.AudioData.numberOfChannels --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`numberOfChannels`** read-only property of the {{domxref("AudioData")}} interface returns the number of channels in the `AudioData` object. ## Value An integer. ## Examples The below example prints the value of `numberOfChannels` to the console. ```js console.log(AudioData.numberOfChannels); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}