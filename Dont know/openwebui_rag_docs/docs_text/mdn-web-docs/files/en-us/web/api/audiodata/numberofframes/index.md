Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md

Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > numberofframes > index.md --- title: "AudioData: numberOfFrames property" short-title: numberOfFrames slug: Web/API/AudioData/numberOfFrames page-type: web-api-instance-property browser-compat: api.AudioData.numberOfFrames --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`numberOfFrames`** read-only property of the {{domxref("AudioData")}} interface returns the number of frames in the `AudioData` object. ## Value An integer. ## Examples The below example prints the value of `numberOfFrames` to the console. ```js console.log(AudioData.numberOfFrames); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}