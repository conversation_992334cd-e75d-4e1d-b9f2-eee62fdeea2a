Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md

Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > samplerate > index.md --- title: "AudioData: sampleRate property" short-title: sampleRate slug: Web/API/AudioData/sampleRate page-type: web-api-instance-property browser-compat: api.AudioData.sampleRate --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`sampleRate`** read-only property of the {{domxref("AudioData")}} interface returns the sample rate in Hz. ## Value A decimal value. ## Examples The below example prints the value of `sampleRate` to the console. ```js console.log(audioData.sampleRate); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}