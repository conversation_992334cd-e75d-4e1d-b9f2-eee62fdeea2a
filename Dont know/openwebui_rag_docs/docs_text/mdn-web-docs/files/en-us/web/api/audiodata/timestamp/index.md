Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > audiodata > timestamp > index.md --- title: "AudioData: timestamp property" short-title: timestamp slug: Web/API/AudioData/timestamp page-type: web-api-instance-property browser-compat: api.AudioData.timestamp --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`timestamp`** read-only property of the {{domxref("AudioData")}} interface returns the timestamp of this `AudioData` object. ## Value An integer. ## Examples The below example prints the value of `timestamp` to the console. ```js console.log(AudioData.timestamp); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}