Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md

Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md Path: mdn-web-docs > files > en-us > web > api > audiosinkinfo > type > index.md --- title: "AudioSinkInfo: type property" short-title: type slug: Web/API/AudioSinkInfo/type page-type: web-api-instance-property status: - experimental browser-compat: api.AudioSinkInfo.type --- {{APIRef("Web Audio API")}}{{SeeCompatTable}} The **`type`** read-only property of the {{domxref("AudioSinkInfo")}} interface returns the type of the audio output device. ## Value A string. Currently the only value is `none`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [SetSinkId test example](https://set-sink-id.glitch.me/) - {{domxref("AudioContext.setSinkId()")}} - {{domxref("AudioContext.sinkId")}} - {{domxref("AudioContext/sinkchange_event", "sinkchange")}}