Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md

Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorattestationresponse > index.md --- title: AuthenticatorAttestationResponse slug: Web/API/AuthenticatorAttestationResponse page-type: web-api-interface browser-compat: api.AuthenticatorAttestationResponse --- {{APIRef("Web Authentication API")}}{{securecontext_header}} The **`AuthenticatorAttestationResponse`** interface of the [Web Authentication API](/en-US/docs/Web/API/Web_Authentication_API) is the result of a WebAuthn credential registration. It contains information about the credential that the server needs to perform WebAuthn assertions, such as its credential ID and public key. An `AuthenticatorAttestationResponse` object instance is available in the {{domxref("PublicKeyCredential.response", "response")}} property of a {{domxref("PublicKeyCredential")}} object returned by a successful {{domxref("CredentialsContainer.create()")}} call. This interface inherits from {{domxref("AuthenticatorResponse")}}. {{InheritanceDiagram}} > [!NOTE] > This interface is restricted to top-level contexts. Use of its features from within an {{HTMLElement("iframe")}} element will not have any effect. ## Instance properties _Also inherits properties from its parent, {{domxref("AuthenticatorResponse")}}._ - {{domxref("AuthenticatorAttestationResponse.attestationObject")}} {{ReadOnlyInline}} - : An {{jsxref("ArrayBuffer")}} containing authenticator data and an attestation statement for a new key pair generated by the authenticator. - {{domxref("AuthenticatorResponse.clientDataJSON")}} {{ReadOnlyInline}} - : Inherited from {{domxref("AuthenticatorResponse")}}, this property contains the JSON-compatible serialization of the data passed from the browser to the authenticator in order to generate this credential i.e., when {{domxref("CredentialsContainer.create()")}} is called with a `publicKey` option. This data contains some information from the options passed into the `create()` call, and some information controlled by the browser. ## Instance methods - {{domxref("AuthenticatorAttestationResponse.getAuthenticatorData()")}} - : Returns an {{jsxref("ArrayBuffer")}} containing the authenticator data contained within the {{domxref("AuthenticatorAttestationResponse.attestationObject")}} property. - {{domxref("AuthenticatorAttestationResponse.getPublicKey()")}} - : Returns an {{jsxref("ArrayBuffer")}} containing the DER `SubjectPublicKeyInfo` of the new credential (see [Subject Public Key Info](https://www.rfc-editor.org/rfc/rfc5280#section-4.1.2.7)), or `null` if this is not available. - {{domxref("AuthenticatorAttestationResponse.getPublicKeyAlgorithm()")}} - : Returns a number that is equal to a [COSE Algorithm Identifier](https://www.iana.org/assignments/cose/cose.xhtml#algorithms), representing the cryptographic algorithm used for the new credential. - {{domxref("AuthenticatorAttestationResponse.getTransports()")}} - : Returns an array of strings describing which transport methods (e.g., `usb`, `nfc`) are believed to be supported with the authenticator. The array may be empty if the information is not available. ## Examples See [Creating a public key credential](/en-US/docs/Web/API/CredentialsContainer/create#creating_a_public_key_credential) for a detailed example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("AuthenticatorAssertionResponse")}}: the interface for the type of response given when retrieving an existing credential - {{domxref("AuthenticatorResponse")}}: the parent interface