Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md

Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md Path: mdn-web-docs > files > en-us > web > api > authenticatorresponse > index.md --- title: AuthenticatorResponse slug: Web/API/AuthenticatorResponse page-type: web-api-interface browser-compat: api.AuthenticatorResponse --- {{APIRef("Web Authentication API")}}{{securecontext_header}} The **`AuthenticatorResponse`** interface of the [Web Authentication API](/en-US/docs/Web/API/Web_Authentication_API) is the base interface for interfaces that provide a cryptographic root of trust for a key pair. The child interfaces include information from the browser such as the challenge origin and either may be returned from {{domxref("PublicKeyCredential.response")}}. ## Interfaces based on AuthenticatorResponse Below is a list of interfaces based on the AuthenticatorResponse interface. - {{domxref("AuthenticatorAssertionResponse")}} - {{domxref("AuthenticatorAttestationResponse")}} ## Instance properties - {{domxref("AuthenticatorResponse.clientDataJSON")}} - : A [JSON](/en-US/docs/Learn_web_development/Core/Scripting/JSON) string in an {{jsxref("ArrayBuffer")}}, representing the client data that was passed to {{domxref("CredentialsContainer.create()")}} or {{domxref("CredentialsContainer.get()")}}. ## Instance methods None. ## Examples ### Getting an AuthenticatorAssertionResponse ```js const options = { challenge: new Uint8Array([ /* bytes sent from the server */ ]), }; navigator.credentials .get({ publicKey: options }) .then((credentialInfoAssertion) => { const assertionResponse = credentialInfoAssertion.response; // send assertion response back to the server // to proceed with the control of the credential }) .catch((err) => console.error(err)); ``` ### Getting an AuthenticatorAttestationResponse ```js const publicKey = { challenge: new Uint8Array([ 21, 31, 105 /* 29 more random bytes generated by the server */, ]), rp: { name: "Example CORP", id: "login.example.com", }, user: { id: new Uint8Array(16), name: "<EMAIL>", displayName: "John Doe", }, pubKeyCredParams: [ { type: "public-key", alg: -7, }, ], }; navigator.credentials .create({ publicKey }) .then((newCredentialInfo) => { const attestationResponse = newCredentialInfo.response; }) .catch((err) => console.error(err)); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("AuthenticatorAttestationResponse")}} - {{domxref("AuthenticatorAssertionResponse")}} - {{domxref("PublicKeyCredential.response")}}