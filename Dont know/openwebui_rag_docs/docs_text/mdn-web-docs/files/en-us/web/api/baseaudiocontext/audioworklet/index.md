Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md

Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > audioworklet > index.md --- title: "BaseAudioContext: audioWorklet property" short-title: audioWorklet slug: Web/API/BaseAudioContext/audioWorklet page-type: web-api-instance-property browser-compat: api.BaseAudioContext.audioWorklet --- {{ APIRef("Web Audio API") }}{{securecontext_header}} The `audioWorklet` read-only property of the {{domxref("BaseAudioContext")}} interface returns an instance of {{domxref("AudioWorklet")}} that can be used for adding {{domxref("AudioWorkletProcessor")}}-derived classes which implement custom audio processing. ## Value An {{domxref("AudioWorklet")}} instance. ## Examples _For a complete example demonstrating user-defined audio processing, see the {{domxref("AudioWorkletNode")}} page._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Web Audio API](/en-US/docs/Web/API/Web_Audio_API/Using_Web_Audio_API) - {{domxref("AudioWorkletNode")}}