Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md

Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md Path: mdn-web-docs > files > en-us > web > api > baseaudiocontext > listener > index.md --- title: "BaseAudioContext: listener property" short-title: listener slug: Web/API/BaseAudioContext/listener page-type: web-api-instance-property browser-compat: api.BaseAudioContext.listener --- {{ APIRef("Web Audio API") }} The `listener` property of the {{ domxref("BaseAudioContext") }} interface returns an {{ domxref("AudioListener") }} object that can then be used for implementing 3D audio spatialization. ## Value An {{ domxref("AudioListener") }} object. ## Examples > [!NOTE] > For a full Web Audio spatialization example, see our [panner-node](https://github.com/mdn/webaudio-examples/tree/main/panner-node) demo. ```js const audioCtx = new AudioContext(); // Older webkit/blink browsers require a prefix // const myListener = audioCtx.listener; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Web Audio API](/en-US/docs/Web/API/Web_Audio_API/Using_Web_Audio_API)