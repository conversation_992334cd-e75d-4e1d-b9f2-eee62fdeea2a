Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md

Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > blobevent > data > index.md --- title: "BlobEvent: data property" short-title: data slug: Web/API/BlobEvent/data page-type: web-api-instance-property browser-compat: api.BlobEvent.data --- {{APIRef("MediaStream Recording")}} The **`data`** read-only property of the {{domxref("BlobEvent")}} interface represents a {{domxref("Blob")}} associated with the event. ## Value A {{domxref("Blob")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("BlobEvent")}} interface it belongs to.