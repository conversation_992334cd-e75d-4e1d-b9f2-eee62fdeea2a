Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > authenticatedsignedwrites > index.md --- title: "BluetoothCharacteristicProperties: authenticatedSignedWrites property" short-title: authenticatedSignedWrites slug: Web/API/BluetoothCharacteristicProperties/authenticatedSignedWrites page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.authenticatedSignedWrites --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`authenticatedSignedWrites`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if signed writing to the characteristic value is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}