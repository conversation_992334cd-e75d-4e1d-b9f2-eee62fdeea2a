Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > broadcast > index.md --- title: "BluetoothCharacteristicProperties: broadcast property" short-title: broadcast slug: Web/API/BluetoothCharacteristicProperties/broadcast page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.broadcast --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`broadcast`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if the broadcast of the characteristic value is permitted using the Server Characteristic Configuration Descriptor. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}