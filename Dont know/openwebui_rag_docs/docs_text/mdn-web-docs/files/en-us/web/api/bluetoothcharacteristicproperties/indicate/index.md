Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > indicate > index.md --- title: "BluetoothCharacteristicProperties: indicate property" short-title: indicate slug: Web/API/BluetoothCharacteristicProperties/indicate page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.indicate --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`indicate`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if indications of the characteristic value with acknowledgement is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}