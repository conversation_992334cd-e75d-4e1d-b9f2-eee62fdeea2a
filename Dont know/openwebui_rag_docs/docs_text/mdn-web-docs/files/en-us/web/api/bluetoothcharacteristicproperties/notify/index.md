Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > notify > index.md --- title: "BluetoothCharacteristicProperties: notify property" short-title: notify slug: Web/API/BluetoothCharacteristicProperties/notify page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.notify --- {{securecontext_header}}{{APIRef("")}}{{SeeCompatTable}} The **`notify`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if notifications of the characteristic value without acknowledgement is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}