Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > read > index.md --- title: "BluetoothCharacteristicProperties: read property" short-title: read slug: Web/API/BluetoothCharacteristicProperties/read page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.read --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`read`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if the reading of the characteristic value is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}