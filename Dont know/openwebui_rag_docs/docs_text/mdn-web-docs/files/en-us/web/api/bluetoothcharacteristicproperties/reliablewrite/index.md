Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > reliablewrite > index.md --- title: "BluetoothCharacteristicProperties: reliableWrite property" short-title: reliableWrite slug: Web/API/BluetoothCharacteristicProperties/reliableWrite page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.reliableWrite --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`reliableWrite`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if reliable writes to the characteristic is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}