Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writableauxiliaries > index.md --- title: "BluetoothCharacteristicProperties: writableAuxiliaries property" short-title: writableAuxiliaries slug: Web/API/BluetoothCharacteristicProperties/writableAuxiliaries page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.writableAuxiliaries --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`writableAuxiliaries`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if reliable writes to the characteristic descriptor is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}