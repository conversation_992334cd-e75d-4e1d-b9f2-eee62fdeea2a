Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > write > index.md --- title: "BluetoothCharacteristicProperties: write property" short-title: write slug: Web/API/BluetoothCharacteristicProperties/write page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.write --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`write`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if the writing to the characteristic with response is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}