Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothcharacteristicproperties > writewithoutresponse > index.md --- title: "BluetoothCharacteristicProperties: writeWithoutResponse property" short-title: writeWithoutResponse slug: Web/API/BluetoothCharacteristicProperties/writeWithoutResponse page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothCharacteristicProperties.writeWithoutResponse --- {{securecontext_header}}{{APIRef("Bluetooth API")}}{{SeeCompatTable}} The **`writeWithoutResponse`** read-only property of the {{domxref("BluetoothCharacteristicProperties")}} interface returns a `boolean` that is `true` if the writing to the characteristic without response is permitted. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}