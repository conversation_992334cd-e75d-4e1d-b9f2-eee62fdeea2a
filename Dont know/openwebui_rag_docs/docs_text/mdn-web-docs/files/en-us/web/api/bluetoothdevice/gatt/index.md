Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > gatt > index.md --- title: "BluetoothDevice: gatt property" short-title: gatt slug: Web/API/BluetoothDevice/gatt page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothDevice.gatt --- {{APIRef("Bluetooth API") }}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothDevice.gatt`** read-only property returns a reference to the device's {{DOMxRef("BluetoothRemoteGATTServer")}}. ## Value A reference to the device's {{DOMxRef("BluetoothRemoteGATTServer")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}