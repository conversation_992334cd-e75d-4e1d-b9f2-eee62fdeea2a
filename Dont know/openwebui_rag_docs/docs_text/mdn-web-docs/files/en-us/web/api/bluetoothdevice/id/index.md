Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > id > index.md --- title: "BluetoothDevice: id property" short-title: id slug: Web/API/BluetoothDevice/id page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothDevice.id --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothDevice.id`** read-only property returns a string that uniquely identifies a device. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}