Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothdevice > name > index.md --- title: "BluetoothDevice: name property" short-title: name slug: Web/API/BluetoothDevice/name page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothDevice.name --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothDevice.name`** read-only property returns a string that provides a human-readable name for the device. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}