Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > properties > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > properties > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > properties > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > properties > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > properties > index.md --- title: "BluetoothRemoteGATTCharacteristic: properties property" short-title: properties slug: Web/API/BluetoothRemoteGATTCharacteristic/properties page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.properties --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.properties`** read-only property returns a {{domxref('BluetoothCharacteristicProperties')}} instance containing the properties of this characteristic. ## Value The properties of this characteristic. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}