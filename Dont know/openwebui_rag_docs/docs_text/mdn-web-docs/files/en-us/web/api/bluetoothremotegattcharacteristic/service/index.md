Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > service > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > service > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > service > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > service > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > service > index.md --- title: "BluetoothRemoteGATTCharacteristic: service property" short-title: service slug: Web/API/BluetoothRemoteGATTCharacteristic/service page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.service --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.service`** read-only property returns the {{domxref("BluetoothRemoteGATTService")}} this characteristic belongs to. ## Value An instance {{domxref("BluetoothRemoteGATTService")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}