Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > startnotifications > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > startnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > startnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > startnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > startnotifications > index.md --- title: "BluetoothRemoteGATTCharacteristic: startNotifications() method" short-title: startNotifications() slug: Web/API/BluetoothRemoteGATTCharacteristic/startNotifications page-type: web-api-instance-method status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.startNotifications --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.startNotifications()`** method returns a {{jsxref("Promise")}} to the BluetoothRemoteGATTCharacteristic instance when there is an active notification on it. ## Syntax ```js-nolint startNotifications() ``` ### Parameters None. ### Return value A {{jsxref("Promise")}} to the BluetoothRemoteGATTCharacteristic instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}