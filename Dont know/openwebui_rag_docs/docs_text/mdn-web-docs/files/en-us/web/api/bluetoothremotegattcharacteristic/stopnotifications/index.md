Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > stopnotifications > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > stopnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > stopnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > stopnotifications > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > stopnotifications > index.md --- title: "BluetoothRemoteGATTCharacteristic: stopNotifications() method" short-title: stopNotifications() slug: Web/API/BluetoothRemoteGATTCharacteristic/stopNotifications page-type: web-api-instance-method status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.stopNotifications --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.stopNotifications()`** method returns a {{jsxref("Promise")}} to the BluetoothRemoteGATTCharacteristic instance when there is no longer an active notification on it. ## Syntax ```js-nolint stopNotifications() ``` ### Parameters None. ### Return value A {{jsxref("Promise")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}