Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > uuid > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > uuid > index.md --- title: "BluetoothRemoteGATTCharacteristic: uuid property" short-title: uuid slug: Web/API/BluetoothRemoteGATTCharacteristic/uuid page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.uuid --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.uuid`** read-only property returns a string containing the UUID of the characteristic, for example `'00002a37-0000-1000-8000-00805f9b34fb'` for the Heart Rate Measurement characteristic. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}