Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > value > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattcharacteristic > value > index.md --- title: "BluetoothRemoteGATTCharacteristic: value property" short-title: value slug: Web/API/BluetoothRemoteGATTCharacteristic/value page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTCharacteristic.value --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTCharacteristic.value`** read-only property returns currently cached characteristic value. This value gets updated when the value of the characteristic is read or updated via a notification or indication. ## Value The currently cached characteristic value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}