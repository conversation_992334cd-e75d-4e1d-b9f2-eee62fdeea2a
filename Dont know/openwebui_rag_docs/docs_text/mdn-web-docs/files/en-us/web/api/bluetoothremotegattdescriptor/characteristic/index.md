Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > characteristic > index.md --- title: "BluetoothRemoteGATTDescriptor: characteristic property" short-title: characteristic slug: Web/API/BluetoothRemoteGATTDescriptor/characteristic page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTDescriptor.characteristic --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTDescriptor.characteristic`** read-only property returns the {{domxref("BluetoothRemoteGATTCharacteristic")}} this descriptor belongs to. ## Value An instance of {{domxref("BluetoothRemoteGATTCharacteristic")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}