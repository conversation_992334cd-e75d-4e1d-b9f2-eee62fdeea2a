Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > uuid > index.md --- title: "BluetoothRemoteGATTDescriptor: uuid property" short-title: uuid slug: Web/API/BluetoothRemoteGATTDescriptor/uuid page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTDescriptor.uuid --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTDescriptor.uuid`** read-only property returns the {{Glossary("UUID")}} of the characteristic descriptor. For example `"00002902-0000-1000-8000-00805f9b34fb"` for the Client Characteristic Configuration descriptor. ## Value A UUID. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}