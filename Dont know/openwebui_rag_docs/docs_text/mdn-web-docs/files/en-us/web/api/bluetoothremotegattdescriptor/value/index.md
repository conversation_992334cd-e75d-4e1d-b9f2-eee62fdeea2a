Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattdescriptor > value > index.md --- title: "BluetoothRemoteGATTDescriptor: value property" short-title: value slug: Web/API/BluetoothRemoteGATTDescriptor/value page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTDescriptor.value --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTDescriptor.value`** read-only property returns an {{jsxref("ArrayBuffer")}} containing the currently cached descriptor value. This value gets updated when the value of the descriptor is read. ## Value An {{jsxref("ArrayBuffer")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}