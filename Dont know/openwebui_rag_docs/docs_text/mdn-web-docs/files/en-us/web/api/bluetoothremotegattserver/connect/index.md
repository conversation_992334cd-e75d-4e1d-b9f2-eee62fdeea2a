Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connect > index.md --- title: "BluetoothRemoteGATTServer: connect() method" short-title: connect() slug: Web/API/BluetoothRemoteGATTServer/connect page-type: web-api-instance-method status: - experimental browser-compat: api.BluetoothRemoteGATTServer.connect --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTServer.connect()`** method causes the script execution environment to connect to `this.device`. ## Syntax ```js-nolint connect() ``` ### Parameters None. ### Return value A {{jsxref("Promise")}} that resolves to a {{domxref("BluetoothRemoteGATTServer")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}