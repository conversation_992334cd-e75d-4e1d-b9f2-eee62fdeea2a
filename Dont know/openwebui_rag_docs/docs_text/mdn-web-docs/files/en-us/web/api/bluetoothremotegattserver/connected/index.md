Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > connected > index.md --- title: "BluetoothRemoteGATTServer: connected property" short-title: connected slug: Web/API/BluetoothRemoteGATTServer/connected page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTServer.connected --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTServer.connected`** read-only property returns a boolean value that returns true while this script execution environment is connected to `this.device`. It can be false while the user agent is physically connected. ## Value A `boolean`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}