Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > device > index.md --- title: "BluetoothRemoteGATTServer: device property" short-title: device slug: Web/API/BluetoothRemoteGATTServer/device page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTServer.device --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTServer.device`** read-only property returns a reference to the {{domxref("BluetoothDevice")}} running the server. ## Value A reference to the {{domxref("BluetoothDevice")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}