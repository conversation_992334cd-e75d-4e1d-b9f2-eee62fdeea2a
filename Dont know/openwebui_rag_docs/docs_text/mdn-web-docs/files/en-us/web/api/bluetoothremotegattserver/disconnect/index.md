Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattserver > disconnect > index.md --- title: "BluetoothRemoteGATTServer: disconnect() method" short-title: disconnect() slug: Web/API/BluetoothRemoteGATTServer/disconnect page-type: web-api-instance-method status: - experimental browser-compat: api.BluetoothRemoteGATTServer.disconnect --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothRemoteGATTServer.disconnect()`** method causes the script execution environment to disconnect from `this.device`. ## Syntax ```js-nolint disconnect() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}