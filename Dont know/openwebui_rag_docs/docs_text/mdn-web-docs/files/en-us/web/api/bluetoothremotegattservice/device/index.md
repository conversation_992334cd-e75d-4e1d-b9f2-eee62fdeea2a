Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > device > index.md --- title: "BluetoothRemoteGATTService: device property" short-title: device slug: Web/API/BluetoothRemoteGATTService/device page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTService.device --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothGATTService.device`** read-only property returns information about a Bluetooth device through an instance of {{domxref("BluetoothDevice")}}. ## Value An instance of {{domxref("BluetoothDevice")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}