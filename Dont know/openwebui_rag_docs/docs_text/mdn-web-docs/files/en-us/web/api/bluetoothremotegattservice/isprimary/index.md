Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > isprimary > index.md --- title: "BluetoothRemoteGATTService: isPrimary property" short-title: isPrimary slug: Web/API/BluetoothRemoteGATTService/isPrimary page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTService.isPrimary --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothGATTService.isPrimary`** read-only property returns a boolean value that indicates whether this is a primary service. If it is not a primary service, it is a secondary service. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}