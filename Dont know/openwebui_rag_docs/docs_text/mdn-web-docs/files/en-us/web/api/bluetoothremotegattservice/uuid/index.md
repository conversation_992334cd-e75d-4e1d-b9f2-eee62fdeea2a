Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md

Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md Path: mdn-web-docs > files > en-us > web > api > bluetoothremotegattservice > uuid > index.md --- title: "BluetoothRemoteGATTService: uuid property" short-title: uuid slug: Web/API/BluetoothRemoteGATTService/uuid page-type: web-api-instance-property status: - experimental browser-compat: api.BluetoothRemoteGATTService.uuid --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`BluetoothGATTService.uuid`** read-only property returns a string representing the UUID of this service. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}