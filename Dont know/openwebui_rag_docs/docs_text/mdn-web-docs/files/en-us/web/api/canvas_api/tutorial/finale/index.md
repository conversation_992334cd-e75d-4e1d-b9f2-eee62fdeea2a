Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > finale > index.md

Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > finale > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > finale > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > finale > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > finale > index.md --- title: Finale slug: Web/API/Canvas_API/Tutorial/Finale page-type: guide --- {{DefaultAPISidebar("Canvas API")}} {{PreviousNext("Web/API/Canvas_API/Tutorial/Optimizing_canvas")}} Congratulations! You finished the [Canvas tutorial](/en-US/docs/Web/API/Canvas_API/Tutorial)! This knowledge will help you to make great 2D graphics on the web. ## More examples and tutorials There are a variety of demos and further explanations about canvas on these sites: - [Canvas Codepens](https://codepen.io/search/pens?q=canvas) - : Front End Developer Playground & Code Editor in the Browser. - [Game development](/en-US/docs/Games) - : Gaming is one of the most popular computer activities. New technologies are constantly arriving to make it possible to develop better and more powerful games that can be run in any standards-compliant web browser. ## Other Web APIs These APIs might be useful when working further with canvas and graphics: - [WebGL](/en-US/docs/Web/API/WebGL_API) - : Advanced API for rendering complex graphics, including 3D. - [SVG](/en-US/docs/Web/SVG) - : Scalable Vector Graphics let you describe images as sets of vectors (lines) and shapes in order to allow them to scale smoothly regardless of the size at which they're drawn. - [Web Audio](/en-US/docs/Web/API/Web_Audio_API) - : The Web Audio API provides a powerful and versatile system for controlling audio on the Web, allowing developers to choose audio sources, add effects to audio, create audio visualizations, apply spatial effects (such as panning) and much more. ## Questions - [Stack Overflow](https://stackoverflow.com/questions/tagged/canvas) - : Questions tagged with "canvas". - [Comments about this tutorial the MDN documentation community](/en-US/docs/MDN) - : If you have any comments about this tutorial or want to thank us, feel free to reach out to us! {{PreviousNext("Web/API/Canvas_API/Tutorial/Optimizing_canvas")}}