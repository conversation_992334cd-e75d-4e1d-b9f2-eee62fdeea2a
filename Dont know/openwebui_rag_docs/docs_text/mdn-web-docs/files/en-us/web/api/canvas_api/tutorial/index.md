Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > index.md

Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > index.md Path: mdn-web-docs > files > en-us > web > api > canvas_api > tutorial > index.md --- title: Canvas tutorial slug: Web/API/Canvas_API/Tutorial page-type: guide --- {{DefaultAPISidebar("Canvas API")}} This tutorial describes how to use the [**`<canvas>`**](/en-US/docs/Web/HTML/Reference/Elements/canvas) element to draw 2D graphics, starting with the basics. The examples provided should give you some clear ideas about what you can do with canvas, and will provide code snippets that may get you started in building your own content. `<canvas>` is an [HTML](/en-US/docs/Web/HTML) element which can be used to draw graphics via scripting (usually [JavaScript](/en-US/docs/Glossary/JavaScript)). This can, for instance, be used to draw graphs, combine photos, or create simple animations. First introduced in WebKit by Apple for the macOS Dashboard, `<canvas>` has since been implemented in browsers. Today, all major browsers support it. ## Before you start Using the `<canvas>` element is not very difficult, but you do need a basic understanding of [HTML](/en-US/docs/Web/HTML) and [JavaScript](/en-US/docs/Web/JavaScript). The `<canvas>` element is not supported in some older browsers, but is supported in recent versions of all major browsers. The default size of the canvas is 300 pixels 150 pixels (width height). But custom sizes can be defined using the HTML `height` and `width` property. In order to draw graphics on the canvas we use a JavaScript context object, which creates graphics on the fly. ## In this tutorial 1. [Basic usage](/en-US/docs/Web/API/Canvas_API/Tutorial/Basic_usage) 2. [Drawing shapes](/en-US/docs/Web/API/Canvas_API/Tutorial/Drawing_shapes) 3. [Applying styles and colors](/en-US/docs/Web/API/Canvas_API/Tutorial/Applying_styles_and_colors) 4. [Drawing text](/en-US/docs/Web/API/Canvas_API/Tutorial/Drawing_text) 5. [Using images](/en-US/docs/Web/API/Canvas_API/Tutorial/Using_images) 6. [Transformations](/en-US/docs/Web/API/Canvas_API/Tutorial/Transformations) 7. [Compositing and clipping](/en-US/docs/Web/API/Canvas_API/Tutorial/Compositing) 8. [Basic animations](/en-US/docs/Web/API/Canvas_API/Tutorial/Basic_animations) 9. [Advanced animations](/en-US/docs/Web/API/Canvas_API/Tutorial/Advanced_animations) 10. [Pixel manipulation](/en-US/docs/Web/API/Canvas_API/Tutorial/Pixel_manipulation_with_canvas) 11. [Optimizing the canvas](/en-US/docs/Web/API/Canvas_API/Tutorial/Optimizing_canvas) 12. [Finale](/en-US/docs/Web/API/Canvas_API/Tutorial/Finale) ## See also - [Canvas topic page](/en-US/docs/Web/API/Canvas_API) ## A note to contributors Due to an unfortunate technical error that occurred the week of June 17, 2013, we lost the history of this tutorial, including attributions to all past contributors to its content. We apologize for this, and hope you'll forgive this unfortunate mishap. {{ Next("Web/API/Canvas_API/Tutorial/Basic_usage") }}