Path: mdn-web-docs > files > en-us > web > api > canvasrenderingcontext2d > miterlimit > index.md

Path: mdn-web-docs > files > en-us > web > api > canvasrenderingcontext2d > miterlimit > index.md Path: mdn-web-docs > files > en-us > web > api > canvasrenderingcontext2d > miterlimit > index.md Path: mdn-web-docs > files > en-us > web > api > canvasrenderingcontext2d > miterlimit > index.md Path: mdn-web-docs > files > en-us > web > api > canvasrenderingcontext2d > miterlimit > index.md --- title: "CanvasRenderingContext2D: miterLimit property" short-title: miterLimit slug: Web/API/CanvasRenderingContext2D/miterLimit page-type: web-api-instance-property browser-compat: api.CanvasRenderingContext2D.miterLimit --- {{APIRef}} The **`CanvasRenderingContext2D.miterLimit`** property of the Canvas 2D API sets the miter limit ratio. > [!NOTE] > For more info about miters, see [Applying styles and color](/en-US/docs/Web/API/Canvas_API/Tutorial/Applying_styles_and_colors) in the [Canvas tutorial](/en-US/docs/Web/API/Canvas_API/Tutorial). ## Value A number specifying the miter limit ratio, in coordinate space units. Zero, negative, {{jsxref("Infinity")}}, and {{jsxref("NaN")}} values are ignored. The default value is `10.0`. ## Examples ### Using the `miterLimit` property See the chapter [Applying styles and color](/en-US/docs/Web/API/Canvas_API/Tutorial/Applying_styles_and_colors#a_demo_of_the_miterlimit_property) in the [Canvas tutorial](/en-US/docs/Web/API/Canvas_API/Tutorial) for more information. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The interface defining this property: {{domxref("CanvasRenderingContext2D")}} - {{domxref("CanvasRenderingContext2D.lineCap")}} - {{domxref("CanvasRenderingContext2D.lineJoin")}}