Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md

Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > starttime > index.md --- title: "ChapterInformation: startTime property" short-title: startTime slug: Web/API/ChapterInformation/startTime page-type: web-api-instance-property status: - experimental browser-compat: api.ChapterInformation.startTime --- {{APIRef("Media Session API")}}{{SeeCompatTable}} The **`startTime`** read-only property of the {{domxref("ChapterInformation")}} interface returns a number representing the start time of the chapter in seconds. ## Value A number. ## Examples See the main {{domxref("ChapterInformation")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("ChapterInformation")}}