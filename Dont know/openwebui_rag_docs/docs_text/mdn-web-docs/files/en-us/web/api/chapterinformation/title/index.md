Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md

Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md Path: mdn-web-docs > files > en-us > web > api > chapterinformation > title > index.md --- title: "ChapterInformation: title property" short-title: title slug: Web/API/ChapterInformation/title page-type: web-api-instance-property status: - experimental browser-compat: api.ChapterInformation.title --- {{APIRef("Media Session API")}}{{SeeCompatTable}} The **`title`** read-only property of the {{domxref("ChapterInformation")}} interface returns a string representing the title of the chapter. ## Value A string. ## Examples See the main {{domxref("ChapterInformation")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("ChapterInformation")}}