Path: mdn-web-docs > files > en-us > web > api > client > frametype > index.md

Path: mdn-web-docs > files > en-us > web > api > client > frametype > index.md Path: mdn-web-docs > files > en-us > web > api > client > frametype > index.md Path: mdn-web-docs > files > en-us > web > api > client > frametype > index.md Path: mdn-web-docs > files > en-us > web > api > client > frametype > index.md --- title: "Client: frameType property" short-title: frameType slug: Web/API/Client/frameType page-type: web-api-instance-property browser-compat: api.Client.frameType --- {{APIRef("Service Workers API")}}{{AvailableInWorkers("service")}} The **`frameType`** read-only property of the {{domxref("Client")}} interface indicates the type of browsing context of the current {{domxref("Client")}}. This value can be one of `"auxiliary"`, `"top-level"`, `"nested"`, or `"none"`. ## Value One of these four strings: `"auxiliary"`, `"top-level"`, `"nested"`, or `"none"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}