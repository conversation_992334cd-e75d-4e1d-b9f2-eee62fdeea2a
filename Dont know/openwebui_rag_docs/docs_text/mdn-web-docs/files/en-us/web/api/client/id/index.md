Path: mdn-web-docs > files > en-us > web > api > client > id > index.md

Path: mdn-web-docs > files > en-us > web > api > client > id > index.md Path: mdn-web-docs > files > en-us > web > api > client > id > index.md Path: mdn-web-docs > files > en-us > web > api > client > id > index.md Path: mdn-web-docs > files > en-us > web > api > client > id > index.md --- title: "Client: id property" short-title: id slug: Web/API/Client/id page-type: web-api-instance-property browser-compat: api.Client.id --- {{APIRef("Service Workers API")}}{{AvailableInWorkers("service")}} The **`id`** read-only property of the {{domxref("Client")}} interface returns the universally unique identifier of the {{domxref("Client")}} object. ## Value A string uniquely identifying the object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}