Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md

Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md Path: mdn-web-docs > files > en-us > web > api > clipboardevent > clipboarddata > index.md --- title: "ClipboardEvent: clipboardData property" short-title: clipboardData slug: Web/API/ClipboardEvent/clipboardData page-type: web-api-instance-property browser-compat: api.ClipboardEvent.clipboardData --- {{APIRef("Clipboard API")}} The **`clipboardData`** property of the {{domxref("ClipboardEvent")}} interface holds a {{domxref("DataTransfer")}} object, which can be used to: - specify what data should be put into the clipboard from the {{domxref("Element/cut_event", "cut")}} and {{domxref("Element/copy_event", "copy")}} event handlers, typically with a {{domxref("DataTransfer.setData", "setData(format, data)")}} call; - obtain the data to be pasted from the {{domxref("Element/paste_event", "paste")}} event handler, typically with a {{domxref("DataTransfer.getData", "getData(format)")}} call. See the {{domxref("Element/cut_event", "cut")}}, {{domxref("Element/copy_event", "copy")}}, and {{domxref("Element/paste_event", "paste")}} events documentation for more information. ## Value A {{domxref("DataTransfer")}} object. The property can be `null` when the event is created using the constructor. It is never `null` when dispatched by the browser. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Copy-related events: {{domxref("Element/copy_event", "copy")}}, {{domxref("Element/cut_event", "cut")}}, {{domxref("Element/paste_event", "paste")}} - The {{domxref("ClipboardEvent")}} interface it belongs to. - [Clipboard API](/en-US/docs/Web/API/Clipboard_API)