Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md

Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > compositionevent > index.md --- title: "CompositionEvent: CompositionEvent() constructor" short-title: CompositionEvent() slug: Web/API/CompositionEvent/CompositionEvent page-type: web-api-constructor browser-compat: api.CompositionEvent.CompositionEvent --- {{APIRef("UI Events")}} The **`CompositionEvent()`** constructor creates a new {{domxref("CompositionEvent")}} object. ## Syntax ```js-nolint new CompositionEvent(type) new CompositionEvent(type, options) ``` ### Parameters - `type` - : A string with the name of the event. It is case-sensitive and browsers set it to `compositionstart`, `compositionupdate`, or `compositionend`. - `options` {{optional_inline}} - : An object that, _in addition of the properties defined in {{domxref("UIEvent/UIEvent", "UIEvent()")}}_, has the following properties: - `data` {{optional_inline}} - : A string used to initialize the {{domxref("CompositionEvent.data", "data")}} property of the new {{domxref("CompositionEvent")}}. Browser-generated events set it to the characters generated by the IME composition. ### Return value A new {{domxref("CompositionEvent")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("CompositionEvent")}}, the interface of the objects it constructs.