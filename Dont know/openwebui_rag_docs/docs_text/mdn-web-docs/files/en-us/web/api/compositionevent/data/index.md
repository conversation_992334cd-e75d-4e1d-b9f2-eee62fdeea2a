Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md

Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > data > index.md --- title: "CompositionEvent: data property" short-title: data slug: Web/API/CompositionEvent/data page-type: web-api-instance-property browser-compat: api.CompositionEvent.data --- {{APIRef("UI Events")}} The **`data`** read-only property of the {{domxref("CompositionEvent")}} interface returns the characters generated by the input method that raised the event; its exact nature varies depending on the type of event that generated the `CompositionEvent` object. ## Value A string representing the event data: - For `compositionstart` events, this is the currently selected text that will be replaced by the string being composed. This value doesn't change even if content changes the selection range; rather, it indicates the string that was selected when composition started. - For `compositionupdate`, this is the string as it stands currently as editing is ongoing. - For `compositionend` events, this is the string as committed to the editor. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("CompositionEvent")}}