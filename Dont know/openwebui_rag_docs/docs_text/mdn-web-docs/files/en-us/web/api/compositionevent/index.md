Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md

Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md Path: mdn-web-docs > files > en-us > web > api > compositionevent > index.md --- title: CompositionEvent slug: Web/API/CompositionEvent page-type: web-api-interface browser-compat: api.CompositionEvent --- {{APIRef("UI Events")}} The DOM **`CompositionEvent`** represents events that occur due to the user indirectly entering text. {{InheritanceDiagram}} ## Constructor - {{domxref("CompositionEvent.CompositionEvent()", "CompositionEvent()")}} - : Creates a new `CompositionEvent` object instance. ## Instance properties _This interface also inherits properties of its parent, {{domxref("UIEvent")}}, and its ancestor {{domxref("Event")}}._ - {{domxref("CompositionEvent.data")}} {{ReadOnlyInline}} - : Returns the characters generated by the input method that raised the event; its varies depending on the type of event that generated the `CompositionEvent` object. - {{domxref("CompositionEvent.locale")}} {{ReadOnlyInline}} {{deprecated_inline}} {{Non-standard_Inline}} - : Returns the locale of the current input method (for example, the keyboard layout locale if the composition is associated with an {{glossary("Input method editor")}}). ## Instance methods _This interface also inherits methods of its parent, {{domxref("UIEvent")}}, and its ancestor {{domxref("Event")}}._ - {{domxref("CompositionEvent.initCompositionEvent()")}} {{deprecated_inline}} - : Initializes the attributes of a `CompositionEvent` object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [compositionstart](/en-US/docs/Web/API/Element/compositionstart_event) - [compositionend](/en-US/docs/Web/API/Element/compositionend_event) - [compositionupdate](/en-US/docs/Web/API/Element/compositionupdate_event) - [UIEvent](/en-US/docs/Web/API/UIEvent) - [Event](/en-US/docs/Web/API/Event)