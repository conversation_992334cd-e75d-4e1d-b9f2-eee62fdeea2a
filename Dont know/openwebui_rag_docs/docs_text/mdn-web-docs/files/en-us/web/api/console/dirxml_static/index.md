Path: mdn-web-docs > files > en-us > web > api > console > dirxml_static > index.md

Path: mdn-web-docs > files > en-us > web > api > console > dirxml_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > dirxml_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > dirxml_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > dirxml_static > index.md --- title: "console: dirxml() static method" short-title: dirxml() slug: Web/API/console/dirxml_static page-type: web-api-static-method browser-compat: api.console.dirxml_static --- {{APIRef("Console API")}} The **`console.dirxml()`** static method displays an interactive tree of the descendant elements of the specified XML/HTML element. If it is not possible to display as an element the JavaScript Object view is shown instead. The output is presented as a hierarchical listing of expandable nodes that let you see the contents of child nodes. ## Syntax ```js-nolint console.dirxml(object) ``` ### Parameters - `object` - : A JavaScript object whose properties should be output. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Microsoft Edge's documentation for `console.dirxml()`](https://learn.microsoft.com/en-us/microsoft-edge/devtools-guide-chromium/console/api#dirxml) - [Node.js documentation for `console.dirxml()`](https://nodejs.org/docs/latest/api/console.html#consoledirxmldata) - [Google Chrome's documentation for `console.dirxml()`](https://developer.chrome.com/docs/devtools/console/api/#dirxml)