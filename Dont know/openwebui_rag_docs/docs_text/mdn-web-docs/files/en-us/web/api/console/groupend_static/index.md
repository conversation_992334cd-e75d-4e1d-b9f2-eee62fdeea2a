Path: mdn-web-docs > files > en-us > web > api > console > groupend_static > index.md

Path: mdn-web-docs > files > en-us > web > api > console > groupend_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > groupend_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > groupend_static > index.md Path: mdn-web-docs > files > en-us > web > api > console > groupend_static > index.md --- title: "console: groupEnd() static method" short-title: groupEnd() slug: Web/API/console/groupEnd_static page-type: web-api-static-method browser-compat: api.console.groupEnd_static --- {{APIRef("Console API")}} {{AvailableInWorkers}} The **`console.groupEnd()`** static method exits the current inline group in the console. See [Using groups in the console](/en-US/docs/Web/API/console#using_groups_in_the_console) in the {{domxref("console")}} documentation for details and examples. ## Syntax ```js-nolint console.groupEnd() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("console/group_static", "console.group()")}} - {{domxref("console/groupCollapsed_static", "console.groupCollapsed()")}} - [Microsoft Edge's documentation for `console.groupEnd()`](https://learn.microsoft.com/en-us/microsoft-edge/devtools-guide-chromium/console/api#groupend) - [Node.js documentation for `console.groupEnd()`](https://nodejs.org/docs/latest/api/console.html#consolegroupend) - [Google Chrome's documentation for `console.groupEnd()`](https://developer.chrome.com/docs/devtools/console/api/#groupend)