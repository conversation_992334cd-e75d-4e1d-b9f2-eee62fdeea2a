Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md

Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > city > index.md --- title: "ContactAddress: city property" short-title: city slug: Web/API/ContactAddress/city page-type: web-api-instance-property status: - experimental browser-compat: api.ContactAddress.city --- {{securecontext_header}}{{APIRef("Contact Picker API")}}{{SeeCompatTable}} The **`city`** read-only property of the {{domxref("ContactAddress")}} interface returns a string containing the city or town portion of the address. ## Value A string indicating the city or town portion of the address described by the {{domxref("ContactAddress")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}