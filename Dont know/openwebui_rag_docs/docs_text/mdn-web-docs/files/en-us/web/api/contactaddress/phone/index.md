Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md

Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > phone > index.md --- title: "ContactAddress: phone property" short-title: phone slug: Web/API/ContactAddress/phone page-type: web-api-instance-property status: - experimental browser-compat: api.ContactAddress.phone --- {{securecontext_header}}{{APIRef("Contact Picker API")}}{{SeeCompatTable}} The read-only **`phone`** property of the {{domxref("ContactAddress")}} interface returns a string containing the telephone number of the recipient or contact person at the address. ## Value A string containing the telephone number for the recipient of the shipment. If no phone number is available, this value is an empty string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}