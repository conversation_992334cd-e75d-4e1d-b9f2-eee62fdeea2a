Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md

Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > recipient > index.md --- title: "ContactAddress: recipient property" short-title: recipient slug: Web/API/ContactAddress/recipient page-type: web-api-instance-property status: - experimental browser-compat: api.ContactAddress.recipient --- {{securecontext_header}}{{APIRef("Contact Picker API")}}{{SeeCompatTable}} The read-only **`recipient`** property of the {{domxref("ContactAddress")}} interface returns a string containing the name of the recipient, purchaser, or contact person at the address. ## Value A string giving the name of the person=, or the name of a contact person in other contexts. If no name is available, this string is empty. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}