Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md

Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > sortingcode > index.md --- title: "ContactAddress: sortingCode property" short-title: sortingCode slug: Web/API/ContactAddress/sortingCode page-type: web-api-instance-property status: - experimental browser-compat: api.ContactAddress.sortingCode --- {{securecontext_header}}{{APIRef("Contact Picker API")}}{{SeeCompatTable}} The **`sortingCode`** read-only property of the {{domxref("ContactAddress")}} interface returns a string containing a postal sorting code such as is used in France. ## Value A string containing the sorting code portion of the address. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}