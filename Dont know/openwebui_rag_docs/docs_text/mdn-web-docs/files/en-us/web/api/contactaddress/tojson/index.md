Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md

Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > contactaddress > tojson > index.md --- title: "ContactAddress: toJSON() method" short-title: toJSON() slug: Web/API/ContactAddress/toJSON page-type: web-api-instance-method status: - experimental browser-compat: api.ContactAddress.toJSON --- {{securecontext_header}}{{APIRef("Contact Picker API")}}{{SeeCompatTable}} The **`toJSON()`** method of the {{domxref("ContactAddress")}} interface is a standard serializer that returns a JSON representation of the `<PERSON>Address` object's properties. ## Syntax ```js-nolint toJSON() ``` ### Return value A JSON object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}