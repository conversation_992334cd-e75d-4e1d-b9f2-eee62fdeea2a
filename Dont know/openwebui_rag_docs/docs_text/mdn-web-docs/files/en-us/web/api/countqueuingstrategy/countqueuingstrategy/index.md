Path: mdn-web-docs > files > en-us > web > api > countqueuingstrategy > countqueuingstrategy > index.md

Path: mdn-web-docs > files > en-us > web > api > countqueuingstrategy > countqueuingstrategy > index.md Path: mdn-web-docs > files > en-us > web > api > countqueuingstrategy > countqueuingstrategy > index.md Path: mdn-web-docs > files > en-us > web > api > countqueuingstrategy > countqueuingstrategy > index.md Path: mdn-web-docs > files > en-us > web > api > countqueuingstrategy > countqueuingstrategy > index.md --- title: "CountQueuingStrategy: CountQueuingStrategy() constructor" short-title: CountQueuingStrategy() slug: Web/API/CountQueuingStrategy/CountQueuingStrategy page-type: web-api-constructor browser-compat: api.CountQueuingStrategy.CountQueuingStrategy --- {{APIRef("Streams")}}{{AvailableInWorkers}} The **`CountQueuingStrategy()`** constructor creates and returns a `CountQueuingStrategy` object instance. ## Syntax ```js-nolint new CountQueuingStrategy(options) ``` ### Parameters - `options` - : An object with the following property: - `highWaterMark` - : The total number of chunks that can be contained in the internal queue before backpressure is applied. ### Return value An instance of the {{domxref("CountQueuingStrategy")}} object. ### Exceptions None. ## Examples ```js const queuingStrategy = new CountQueuingStrategy({ highWaterMark: 1 }); const writableStream = new WritableStream( { // Implement the sink write(chunk) { // }, close() { // }, abort(err) { console.log("Sink error:", err); }, }, queuingStrategy, ); const size = queuingStrategy.size(); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("CountQueuingStrategy")}}