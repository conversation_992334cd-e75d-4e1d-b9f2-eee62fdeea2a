Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md

Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md Path: mdn-web-docs > files > en-us > web > api > credential > id > index.md --- title: "Credential: id property" short-title: id slug: Web/API/Credential/id page-type: web-api-instance-property browser-compat: api.Credential.id --- {{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`id`** read-only property of the {{domxref("Credential")}} interface returns a string containing the credential's identifier. This might be a GUID, username, or email address, or some other value, depending on the type of credential. ## Value A string containing the credential's identifier. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}