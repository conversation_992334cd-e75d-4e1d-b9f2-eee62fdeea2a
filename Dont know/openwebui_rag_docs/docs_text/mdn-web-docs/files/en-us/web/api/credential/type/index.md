Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md

Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md Path: mdn-web-docs > files > en-us > web > api > credential > type > index.md --- title: "Credential: type property" short-title: type slug: Web/API/Credential/type page-type: web-api-instance-property browser-compat: api.Credential.type --- {{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`type`** read-only property of the {{domxref("Credential")}} interface returns a string containing the credential's type. Valid values are `password`, `federated`, `public-key`, `identity` and `otp`. ## Value A string contains a credential's given name. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}