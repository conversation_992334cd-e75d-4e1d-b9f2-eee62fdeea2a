Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md

Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md Path: mdn-web-docs > files > en-us > web > api > cssfontfacerule > style > index.md --- title: "CSSFontFaceRule: style property" short-title: style slug: Web/API/CSSFontFaceRule/style page-type: web-api-instance-property browser-compat: api.CSSFontFaceRule.style --- {{APIRef("CSSOM")}} The read-only **`style`** property of the {{domxref("CSSFontFaceRule")}} interface returns the style information from the {{cssxref("@font-face")}} [at-rule](/en-US/docs/Web/CSS/CSS_syntax/At-rule). This will be in the form of a {{domxref("CSSStyleDeclaration")}} object. ## Value A {{domxref("CSSStyleDeclaration")}}. ## Examples This example uses the CSS found as an example on the {{cssxref("@font-face")}} page. The first {{domxref("CSSRule")}} returned will be a `CSSFontFaceRule`. The `style` property returns a {{domxref("CSSStyleDeclaration")}} with the properties `fontFamily`, `fontWeight`, and `src` populated with the information from the rule. ```css @font-face { font-family: MyHelvetica; src: local("Helvetica Neue Bold"), local("HelveticaNeue-Bold"), url(MgOpenModernaBold.ttf); font-weight: bold; } ``` ```js const myRules = document.styleSheets[0].cssRules; console.log(myRules[0].style); // A CSSStyleDeclaration ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}