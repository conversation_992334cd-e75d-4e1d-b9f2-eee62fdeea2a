Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > cssmathinvert > index.md --- title: "CSSMathInvert: CSSMathInvert() constructor" short-title: CSSMathInvert() slug: Web/API/CSSMathInvert/CSSMathInvert page-type: web-api-constructor browser-compat: api.CSSMathInvert.CSSMathInvert --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathInvert()`** constructor creates a new {{domxref("CSSMathInvert")}} object which represents a CSS {{CSSXref('calc','calc()')}} used as `calc(1 / value)` ## Syntax ```js-nolint new CSSMathInvert(arg) ``` ### Parameters - `arg` - : A {{domxref('CSSNumericValue')}}. ### Exceptions - [`RangeError`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/RangeError) - : Raised if the arg is 0 or -0. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}