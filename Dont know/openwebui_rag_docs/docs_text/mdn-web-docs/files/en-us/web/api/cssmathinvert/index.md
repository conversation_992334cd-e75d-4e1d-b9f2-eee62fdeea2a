Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > index.md --- title: CSSMathInvert slug: Web/API/CSSMathInvert page-type: web-api-interface browser-compat: api.CSSMathInvert --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathInvert`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents a CSS {{CSSXref('calc','calc()')}} used as `calc(1 / <value>).` It inherits properties and methods from its parent {{domxref('CSSNumericValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSMathInvert.CSSMathInvert", "CSSMathInvert()")}} - : Creates a new `CSSMathInvert` object. ## Instance properties - {{domxref('CSSMathInvert.value')}} {{ReadOnlyInline}} - : Returns a {{domxref('CSSNumericValue')}} object. ## Static methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Instance methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}