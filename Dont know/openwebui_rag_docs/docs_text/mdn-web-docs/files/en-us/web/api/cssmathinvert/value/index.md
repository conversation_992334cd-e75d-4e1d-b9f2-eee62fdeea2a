Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathinvert > value > index.md --- title: "CSSMathInvert: value property" short-title: value slug: Web/API/CSSMathInvert/value page-type: web-api-instance-property browser-compat: api.CSSMathInvert.value --- {{APIRef("CSS Typed Object Model API")}} The CSSMathInvert.value read-only property of the {{domxref("CSSMathInvert")}} interface returns a {{domxref('CSSNumericValue')}} object. ## Value A {{domxref('CSSNumericValue')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}