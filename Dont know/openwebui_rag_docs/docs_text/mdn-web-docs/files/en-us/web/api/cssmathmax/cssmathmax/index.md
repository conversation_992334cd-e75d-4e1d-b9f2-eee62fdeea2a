Path: mdn-web-docs > files > en-us > web > api > cssmathmax > cssmathmax > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmax > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > cssmathmax > index.md --- title: "CSSMathMax: CSSMathMax() constructor" short-title: CSSMathMax() slug: Web/API/CSSMathMax/CSSMathMax page-type: web-api-constructor status: - experimental browser-compat: api.CSSMathMax.CSSMathMax --- {{SeeCompatTable}}{{APIRef("CSS Typed Object Model API")}} The **`CSSMathMax()`** constructor creates a new {{domxref("CSSMathMax")}} object which represents the CSS {{CSSXref('max', 'max()')}} function. ## Syntax ```js-nolint new CSSMathMax(args) ``` ### Parameters - `args` - : A list of values for the {{domxref('CSSMathProduct')}} object to be either a double integer or a {{domxref('CSSNumericValue')}}. ### Exceptions - [`TypeError`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError) - : Thrown if there is a _failure_ when adding all of the values in args. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}