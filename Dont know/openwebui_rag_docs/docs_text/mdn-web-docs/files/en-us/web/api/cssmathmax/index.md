Path: mdn-web-docs > files > en-us > web > api > cssmathmax > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > index.md --- title: CSSMathMax slug: Web/API/CSSMathMax page-type: web-api-interface browser-compat: api.CSSMathMax --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathMax`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the CSS {{CSSXref('max','max()')}} function. It inherits properties and methods from its parent {{domxref('CSSNumericValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSMathMax.CSSMathMax", "CSSMathMax()")}} {{Experimental_Inline}} - : Creates a new `CSSMathMax` object. ## Instance properties - {{domxref('CSSMathMax.values')}} {{ReadOnlyInline}} - : Returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Static methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Instance methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}