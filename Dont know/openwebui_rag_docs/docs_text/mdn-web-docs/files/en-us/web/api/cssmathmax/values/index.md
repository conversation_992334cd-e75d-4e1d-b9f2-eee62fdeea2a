Path: mdn-web-docs > files > en-us > web > api > cssmathmax > values > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmax > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmax > values > index.md --- title: "CSSMathMax: values property" short-title: values slug: Web/API/CSSMathMax/values page-type: web-api-instance-property browser-compat: api.CSSMathMax.values --- {{APIRef("CSS Typed Object Model API")}} The CSSMathMax.values read-only property of the {{domxref("CSSMathMax")}} interface returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Value A {{domxref('CSSNumericArray')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}