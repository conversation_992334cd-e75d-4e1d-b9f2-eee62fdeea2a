Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > cssmathmin > index.md --- title: "CSSMathMin: CSSMathMin() constructor" short-title: CSSMathMin() slug: Web/API/CSSMathMin/CSSMathMin page-type: web-api-constructor status: - experimental browser-compat: api.CSSMathMin.CSSMathMin --- {{SeeCompatTable}}{{APIRef("CSS Typed Object Model API")}} The **`CSSMathMin()`** constructor creates a new {{domxref("CSSMathMin")}} object which represents the CSS {{CSSXref('min','min()')}} function. ## Syntax ```js-nolint new CSSMathMin(args) ``` ### Parameters - `args` - : A list of values for the {{domxref('CSSMathProduct')}} object to be either a double integer or a {{domxref('CSSNumericValue')}}. ### Exceptions - [`TypeError`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypeError) - : Thrown if there is a _failure_ when adding all of the values in args. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}