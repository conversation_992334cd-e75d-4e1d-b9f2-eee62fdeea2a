Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > index.md --- title: CSSMathMin slug: Web/API/CSSMathMin page-type: web-api-interface browser-compat: api.CSSMathMin --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathMin`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the CSS {{CSSXref('min','min()')}} function. It inherits properties and methods from its parent {{domxref('CSSNumericValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSMathMin.CSSMathMin", "CSSMathMin()")}} {{Experimental_Inline}} - : Creates a new `CSSMathMin` object. ## Instance properties - {{domxref('CSSMathMin.values')}} {{ReadOnlyInline}} - : Returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Static methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Instance methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}