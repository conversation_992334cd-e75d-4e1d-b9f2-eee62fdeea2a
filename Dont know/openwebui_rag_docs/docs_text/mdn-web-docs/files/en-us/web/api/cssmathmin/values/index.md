Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathmin > values > index.md --- title: "CSSMathMin: values property" short-title: values slug: Web/API/CSSMathMin/values page-type: web-api-instance-property browser-compat: api.CSSMathMin.values --- {{APIRef("CSS Typed Object Model API")}} The CSSMathMin.values read-only property of the {{domxref("CSSMathMin")}} interface returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Value A {{domxref('CSSNumericArray')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}