Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > cssmathnegate > index.md --- title: "CSSMathNegate: CSSMathNegate() constructor" short-title: CSSMathNegate() slug: Web/API/CSSMathNegate/CSSMathNegate page-type: web-api-constructor browser-compat: api.CSSMathNegate.CSSMathNegate --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathNegate()`** constructor creates a new {{domxref("CSSMathNegate")}} object which negates the value passed into it. ## Syntax ```js-nolint new CSSMathNegate(arg) ``` ### Parameters - `arg` - : A {{domxref('CSSNumericValue')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}