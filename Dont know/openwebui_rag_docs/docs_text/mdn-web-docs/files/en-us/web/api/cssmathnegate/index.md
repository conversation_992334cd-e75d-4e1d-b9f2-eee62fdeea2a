Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > index.md --- title: CSSMathNegate slug: Web/API/CSSMathNegate page-type: web-api-interface browser-compat: api.CSSMathNegate --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathNegate`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) negates the value passed into it. It inherits properties and methods from its parent {{domxref('CSSNumericValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSMathNegate.CSSMathNegate", "CSSMathNegate()")}} - : Creates a new `CSSMathNegate` object. ## Instance properties - {{domxref('CSSMathNegate.value')}} {{ReadOnlyInline}} - : Returns a {{domxref('CSSNumericValue')}} object. ## Static methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Instance methods _The interface may also inherit methods from its parent interface, {{domxref("CSSMathValue")}}._ ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}