Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathnegate > value > index.md --- title: "CSSMathNegate: value property" short-title: value slug: Web/API/CSSMathNegate/value page-type: web-api-instance-property browser-compat: api.CSSMathNegate.value --- {{APIRef("CSS Typed Object Model API")}} The CSSMathNegate.value read-only property of the {{domxref("CSSMathNegate")}} interface returns a {{domxref('CSSNumericValue')}} object. ## Value A {{domxref('CSSNumericValue')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}