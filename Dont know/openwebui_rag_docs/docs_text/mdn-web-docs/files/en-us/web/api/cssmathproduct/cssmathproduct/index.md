Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > cssmathproduct > index.md --- title: "CSSMathProduct: CSSMathProduct() constructor" short-title: CSSMathProduct() slug: Web/API/CSSMathProduct/CSSMathProduct page-type: web-api-constructor status: - experimental browser-compat: api.CSSMathProduct.CSSMathProduct --- {{APIRef("CSS Typed Object Model API")}}{{SeeCompatTable}} The **`CSSMathProduct()`** constructor creates a new {{domxref("CSSMathProduct")}} object which creates a new {{domxref('CSSMathProduct')}} object which multiplies the arguments passed into it. ## Syntax ```js-nolint new CSSMathProduct(args) ``` ### Parameters - `args` - : A list of values for the {{domxref('CSSMathProduct')}} object to be either a double integer or a {{domxref('CSSNumericValue')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}