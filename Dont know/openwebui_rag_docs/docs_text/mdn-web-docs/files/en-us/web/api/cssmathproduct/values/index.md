Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathproduct > values > index.md --- title: "CSSMathProduct: values property" short-title: values slug: Web/API/CSSMathProduct/values page-type: web-api-instance-property browser-compat: api.CSSMathProduct.values --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathProduct.values`** read-only property of the {{domxref("CSSMathProduct")}} interface returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Value A {{domxref('CSSNumericArray')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}