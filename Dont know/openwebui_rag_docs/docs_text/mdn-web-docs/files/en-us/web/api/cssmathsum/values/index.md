Path: mdn-web-docs > files > en-us > web > api > cssmathsum > values > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathsum > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathsum > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathsum > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathsum > values > index.md --- title: "CSSMathSum: values property" short-title: values slug: Web/API/CSSMathSum/values page-type: web-api-instance-property browser-compat: api.CSSMathSum.values --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathSum.values`** read-only property of the {{domxref("CSSMathSum")}} interface returns a {{domxref('CSSNumericArray')}} object which contains one or more {{domxref('CSSNumericValue')}} objects. ## Value A {{domxref('CSSNumericArray')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}