Path: mdn-web-docs > files > en-us > web > api > cssmathvalue > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmathvalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathvalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathvalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssmathvalue > index.md --- title: CSSMathValue slug: Web/API/CSSMathValue page-type: web-api-interface browser-compat: api.CSSMathValue --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMathValue`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) a base class for classes representing complex numeric values. {{InheritanceDiagram}} ## Interfaces based on CSSMathValue Below is a list of interfaces based on the CSSMathValue interface. - {{domxref('CSSMathInvert')}} - {{domxref('CSSMathMax')}} - {{domxref('CSSMathMin')}} - {{domxref('CSSMathNegate')}} - {{domxref('CSSMathProduct')}} - {{domxref('CSSMathSum')}} ## Instance properties - {{domxref('CSSMathValue.operator')}} - : Indicates the operator that the current subtype represents. ## Static methods _The interface may also inherit methods from its parent interface, {{domxref("CSSNumericValue")}}._ ## Instance methods _The interface may also inherit methods from its parent interface, {{domxref("CSSNumericValue")}}._ ## Examples We create an element with a [`width`](/en-US/docs/Web/CSS/width) determined using a [`calc()`](/en-US/docs/Web/CSS/calc) function, then {{domxref("console/log_static", "console.log()")}} the `operator`. ```html <div>has width</div> ``` We assign a `width` with a calculation ```css div { width: calc(30% - 20px); } ``` We add the JavaScript ```js const styleMap = document.querySelector("div").computedStyleMap(); console.log(styleMap.get("width")); // CSSMathSum {values: CSSNumericArray, operator: "sum"} console.log(styleMap.get("width").operator); // 'sum' console.log(styleMap.get("width").values[1].value); // -20 ``` {{EmbedLiveSample("Examples", 120, 300)}} The `CSSMathValue.operator` returns `"sum"` because `styleMap.get("width").values[1].value );` is `-20`: adding a negative number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}