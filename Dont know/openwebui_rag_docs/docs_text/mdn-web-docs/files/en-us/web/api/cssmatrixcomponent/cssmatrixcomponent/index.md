Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > cssmatrixcomponent > index.md --- title: "CSSMatrixComponent: CSSMatrixComponent() constructor" short-title: CSSMatrixComponent() slug: Web/API/CSSMatrixComponent/CSSMatrixComponent page-type: web-api-constructor browser-compat: api.CSSMatrixComponent.CSSMatrixComponent --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMatrixComponent()`** constructor creates a new {{domxref("CSSMatrixComponent")}} object representing the [matrix()](/en-US/docs/Web/CSS/transform-function/matrix) and [matrix3d()](/en-US/docs/Web/CSS/transform-function/matrix3d) values of the individual {{CSSXRef('transform')}} property in CSS. ## Syntax ```js-nolint new CSSMatrixComponent(matrix) new CSSMatrixComponent(matrix, options) ``` ### Parameters - {{domxref('CSSMatrixComponent.matrix','matrix')}} - : A 2d or 3d matrix. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}