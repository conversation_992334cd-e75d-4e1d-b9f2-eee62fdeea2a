Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > index.md --- title: CSSMatrixComponent slug: Web/API/CSSMatrixComponent page-type: web-api-interface browser-compat: api.CSSMatrixComponent --- {{APIRef("CSS Typed Object Model API")}} The **`CSSMatrixComponent`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [matrix()](/en-US/docs/Web/CSS/transform-function/matrix) and [matrix3d()](/en-US/docs/Web/CSS/transform-function/matrix3d) values of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref('CSSTransformValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSMatrixComponent.CSSMatrixComponent", "CSSMatrixComponent()")}} - : Creates a new `CSSMatrixComponent` object. ## Instance properties - {{domxref('CSSMatrixComponent.matrix','matrix')}} - : A {{domxref("DOMMatrix")}} object. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}