Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md

Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md Path: mdn-web-docs > files > en-us > web > api > cssmatrixcomponent > matrix > index.md --- title: "CSSMatrixComponent: matrix property" short-title: matrix slug: Web/API/CSSMatrixComponent/matrix page-type: web-api-instance-property browser-compat: api.CSSMatrixComponent.matrix --- {{APIRef("CSS Typed Object Model API")}} The **`matrix`** property of the {{domxref("CSSMatrixComponent")}} interface gets and sets a 2d or 3d matrix. See the [matrix()](/en-US/docs/Web/CSS/transform-function/matrix) and [matrix3d()](/en-US/docs/Web/CSS/transform-function/matrix3d) pages for examples. ## Value a matrix. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}