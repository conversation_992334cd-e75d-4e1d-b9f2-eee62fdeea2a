Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > index.md

Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > index.md --- title: CSSNumericArray slug: Web/API/CSSNumericArray page-type: web-api-interface browser-compat: api.CSSNumericArray --- {{APIRef("CSS Typed Object Model API")}} The **`CSSNumericArray`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) contains a list of {{domxref("CSSNumericValue")}} objects. ## Instance properties - {{domxref("CSSNumericArray.length")}} {{ReadOnlyInline}} - : Returns how many {{domxref("CSSNumericValue")}} objects are contained within the `CSSNumericArray`. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}