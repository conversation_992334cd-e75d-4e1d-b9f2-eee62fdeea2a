Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > length > index.md

Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssnumericarray > length > index.md --- title: "CSSNumericArray: length property" short-title: length slug: Web/API/CSSNumericArray/length page-type: web-api-instance-property browser-compat: api.CSSNumericArray.length --- {{APIRef("CSS Typed OM")}} The read-only **`length`** property of the {{domxref("CSSNumericArray")}} interface returns the number of {{domxref("CSSNumericValue")}} objects in the list. ## Value An integer representing the number of {{domxref("CSSNumericValue")}} objects in the list. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}