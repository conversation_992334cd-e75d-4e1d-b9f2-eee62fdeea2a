Path: mdn-web-docs > files > en-us > web > api > cssperspective > index.md

Path: mdn-web-docs > files > en-us > web > api > cssperspective > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > index.md --- title: CSSPerspective slug: Web/API/CSSPerspective page-type: web-api-interface browser-compat: api.CSSPerspective --- {{APIRef("CSS Typed Object Model API")}} The **`CSSPerspective`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [perspective()](/en-US/docs/Web/CSS/transform-function/perspective) value of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref('CSSTransformValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSPerspective.CSSPerspective", "CSSPerspective()")}} - : Creates a new `CSSPerspective` object. ## Instance properties - {{domxref('CSSPerspective.length','length')}} - : Returns or sets the distance from z=0. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}