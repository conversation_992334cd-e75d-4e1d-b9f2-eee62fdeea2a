Path: mdn-web-docs > files > en-us > web > api > cssperspective > length > index.md

Path: mdn-web-docs > files > en-us > web > api > cssperspective > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > length > index.md Path: mdn-web-docs > files > en-us > web > api > cssperspective > length > index.md --- title: "CSSPerspective: length property" short-title: length slug: Web/API/CSSPerspective/length page-type: web-api-instance-property browser-compat: api.CSSPerspective.length --- {{APIRef("CSS Typed OM")}} The **`length`** property of the {{domxref("CSSPerspective")}} interface sets the distance from z=0. It is used to apply a perspective transform to the element and its content. If the value is 0 or a negative number, no perspective transform is applied. ## Value A {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}