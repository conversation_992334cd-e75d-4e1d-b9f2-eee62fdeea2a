Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md

Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > angle > index.md --- title: "CSSRotate: angle property" short-title: angle slug: Web/API/CSSRotate/angle page-type: web-api-instance-property browser-compat: api.CSSRotate.angle --- {{APIRef("CSS Typed OM")}} The **`angle`** property of the {{domxref("CSSRotate")}} interface gets and sets the angle of rotation. A positive angle denotes a clockwise rotation, a negative angle a counter-clockwise one. ## Value A {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}