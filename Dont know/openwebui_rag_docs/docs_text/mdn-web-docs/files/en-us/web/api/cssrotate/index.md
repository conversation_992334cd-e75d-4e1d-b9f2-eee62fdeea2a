Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md

Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > index.md --- title: CSSRotate slug: Web/API/CSSRotate page-type: web-api-interface browser-compat: api.CSSRotate --- {{APIRef("CSS Typed Object Model API")}} The **`CSSRotate`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the rotate value of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref('CSSTransformValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSRotate.CSSRotate", "CSSRotate()")}} - : Creates a new `CSSRotate` object. ## Instance properties - {{domxref('CSSRotate.x','x')}} - : Returns or sets the x-axis value. - {{domxref('CSSRotate.y','y')}} - : Returns or sets the y-axis value. - {{domxref('CSSRotate.z','z')}} - : Returns or sets the z-axis value. - {{domxref('CSSRotate.angle','angle')}} - : Returns or sets the angle value. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}