Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md

Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > x > index.md --- title: "CSSRotate: x property" short-title: x slug: Web/API/CSSRotate/x page-type: web-api-instance-property browser-compat: api.CSSRotate.x --- {{APIRef("CSS Typed OM")}} The **`x`** property of the {{domxref("CSSRotate")}} interface gets and sets the abscissa or x-axis of the translating vector. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}