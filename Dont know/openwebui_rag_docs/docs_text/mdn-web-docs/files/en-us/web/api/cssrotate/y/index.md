Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md

Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > y > index.md --- title: "CSSRotate: y property" short-title: y slug: Web/API/CSSRotate/y page-type: web-api-instance-property browser-compat: api.CSSRotate.y --- {{APIRef("CSS Typed OM")}} The **`y`** property of the {{domxref("CSSRotate")}} interface gets and sets the ordinate or y-axis of the translating vector. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}