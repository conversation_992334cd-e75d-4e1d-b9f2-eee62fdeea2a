Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md

Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssrotate > z > index.md --- title: "CSSRotate: z property" short-title: z slug: Web/API/CSSRotate/z page-type: web-api-instance-property browser-compat: api.CSSRotate.z --- {{APIRef("CSS Typed OM")}} The **`z`** property of the {{domxref("CSSRotate")}} interface representing the z-component of the translating vector. A positive value moves the element towards the viewer, and a negative value farther away. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}