Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > index.md --- title: CSSScale slug: Web/API/CSSScale page-type: web-api-interface browser-compat: api.CSSScale --- {{APIRef("CSS Typed Object Model API")}} The **`CSSScale`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [scale()](/en-US/docs/Web/CSS/transform-function/scale) and [scale3d()](/en-US/docs/Web/CSS/transform-function/scale3d) values of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref('CSSTransformValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSScale.CSSScale", "CSSScale()")}} - : Creates a new `CSSScale` object. ## Instance properties - {{domxref('CSSScale.x','x')}} - : Returns or sets the x-axis value. - {{domxref('CSSScale.y','y')}} - : Returns or sets the y-axis value. - {{domxref('CSSScale.z','z')}} - : Returns or sets the z-axis value. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}