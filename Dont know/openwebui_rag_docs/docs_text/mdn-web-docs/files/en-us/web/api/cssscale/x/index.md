Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > x > index.md --- title: "CSSScale: x property" short-title: x slug: Web/API/CSSScale/x page-type: web-api-instance-property browser-compat: api.CSSScale.x --- {{APIRef("CSS Typed OM")}} The **`x`** property of the {{domxref("CSSScale")}} interface gets and sets the abscissa or x-axis of the translating vector. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}