Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > y > index.md --- title: "CSSScale: y property" short-title: y slug: Web/API/CSSScale/y page-type: web-api-instance-property browser-compat: api.CSSScale.y --- {{APIRef("CSS Typed OM")}} The **`y`** property of the {{domxref("CSSScale")}} interface gets and sets the ordinate or y-axis of the translating vector. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}