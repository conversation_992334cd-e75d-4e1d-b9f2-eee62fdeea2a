Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md Path: mdn-web-docs > files > en-us > web > api > cssscale > z > index.md --- title: "CSSScale: z property" short-title: z slug: Web/API/CSSScale/z page-type: web-api-instance-property browser-compat: api.CSSScale.z --- {{APIRef("CSS Typed OM")}} The **`z`** property of the {{domxref("CSSScale")}} interface representing the z-component of the translating vector. A positive value moves the element towards the viewer, and a negative value farther away. If this value is present then the transform is a 3D transform and the `is2D` property will be set to false. ## Value A double integer or a {{domxref("CSSNumericValue")}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}