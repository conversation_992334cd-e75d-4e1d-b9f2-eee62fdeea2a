Path: mdn-web-docs > files > en-us > web > api > cssscoperule > end > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscoperule > end > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > end > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > end > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > end > index.md --- title: "CSSScopeRule: end property" short-title: end slug: Web/API/CSSScopeRule/end page-type: web-api-instance-property browser-compat: api.CSSScopeRule.end --- {{APIRef("CSSOM")}} The **`end`** property of the {{domxref("CSSScopeRule")}} interface returns a string containing the value of the `@scope` at-rule's scope limit. ## Value A string, or `null` if the `@scope` at-rule has no scope limit defined. ## Example See the main {{domxref("CSSScopeRule")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{CSSxRef("@scope")}}