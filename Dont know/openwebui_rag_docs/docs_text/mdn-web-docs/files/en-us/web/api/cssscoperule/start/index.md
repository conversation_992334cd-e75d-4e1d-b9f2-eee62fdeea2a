Path: mdn-web-docs > files > en-us > web > api > cssscoperule > start > index.md

Path: mdn-web-docs > files > en-us > web > api > cssscoperule > start > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > start > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > start > index.md Path: mdn-web-docs > files > en-us > web > api > cssscoperule > start > index.md --- title: "CSSScopeRule: start property" short-title: start slug: Web/API/CSSScopeRule/start page-type: web-api-instance-property browser-compat: api.CSSScopeRule.start --- {{APIRef("CSSOM")}} The **`start`** property of the {{domxref("CSSScopeRule")}} interface returns a string containing the value of the `@scope` at-rule's scope root. ## Value A string, or `null` if the `@scope` at-rule has no scope root defined. ## Example See the main {{domxref("CSSScopeRule")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{CSSxRef("@scope")}}