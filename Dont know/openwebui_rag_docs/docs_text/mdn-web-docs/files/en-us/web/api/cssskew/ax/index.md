Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ax > index.md --- title: "CSSSkew: ax property" short-title: ax slug: Web/API/CSSSkew/ax page-type: web-api-instance-property browser-compat: api.CSSSkew.ax --- {{APIRef("CSS Typed OM")}} The **`ax`** property of the {{domxref("CSSSkew")}} interface gets and sets the angle used to distort the element along the x-axis (or abscissa). ## Value A {{domxref("CSSNumericValue")}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}