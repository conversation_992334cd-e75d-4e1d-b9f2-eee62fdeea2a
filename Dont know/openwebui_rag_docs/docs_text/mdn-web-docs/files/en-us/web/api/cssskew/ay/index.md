Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > ay > index.md --- title: "CSSSkew: ay property" short-title: ay slug: Web/API/CSSSkew/ay page-type: web-api-instance-property browser-compat: api.CSSSkew.ay --- {{APIRef("CSS Typed OM")}} The **`ay`** property of the {{domxref("CSSSkew")}} interface gets and sets the angle used to distort the element along the y-axis (or ordinate). ## Value A {{domxref("CSSNumericValue")}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}