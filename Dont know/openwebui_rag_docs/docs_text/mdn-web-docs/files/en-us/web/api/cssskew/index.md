Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md Path: mdn-web-docs > files > en-us > web > api > cssskew > index.md --- title: CSSSkew slug: Web/API/CSSSkew page-type: web-api-interface browser-compat: api.CSSSkew --- {{APIRef("CSS Typed OM")}} The **`CSSSkew`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) is part of the {{domxref('CSSTransformValue')}} interface. It represents the [`skew()`](/en-US/docs/Web/CSS/transform-function/skew) value of the individual {{CSSXRef('transform')}} property in CSS. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSSkew.CSSSkew", "CSSSkew()")}} - : Creates a new `CSSSkew` object. ## Instance properties - {{domxref('CSSSkew.ax','ax')}} - : Returns or sets the x-axis value. - {{domxref('CSSSkew.ay','ay')}} - : Returns or sets the y-axis value. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}