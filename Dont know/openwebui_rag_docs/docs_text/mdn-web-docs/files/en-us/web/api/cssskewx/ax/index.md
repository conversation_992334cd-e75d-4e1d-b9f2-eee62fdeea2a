Path: mdn-web-docs > files > en-us > web > api > cssskewx > ax > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewx > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > ax > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > ax > index.md --- title: "CSSSkewX: ax property" short-title: ax slug: Web/API/CSSSkewX/ax page-type: web-api-instance-property browser-compat: api.CSSSkewX.ax --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`ax`** property of the {{domxref("CSSSkewX")}} interface gets and sets the angle used to distort the element along the x-axis (or abscissa). ## Value A {{domxref("CSSNumericValue")}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}