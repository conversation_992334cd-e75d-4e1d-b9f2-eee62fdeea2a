Path: mdn-web-docs > files > en-us > web > api > cssskewx > cssskewx > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewx > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > cssskewx > index.md --- title: "CSSSkewX: CSSSkewX() constructor" short-title: CSSSkewX() slug: Web/API/CSSSkewX/CSSSkewX page-type: web-api-constructor browser-compat: api.CSSSkewX.CSSSkewX --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`CSSSkewX()`** constructor creates a new {{domxref("CSSSkewX")}} object which represents the [`skewX()`](/en-US/docs/Web/CSS/transform-function/skewX) value of the individual {{CSSXRef('transform')}} property in CSS. ## Syntax ```js-nolint new CSSSkewX(ax) ``` ### Parameters - {{domxref('CSSSkewx.ax','ax')}} - : A value for the `ax` angle of the {{domxref('CSSSkewX')}} object to be constructed. This must be a {{domxref('CSSNumericValue')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}