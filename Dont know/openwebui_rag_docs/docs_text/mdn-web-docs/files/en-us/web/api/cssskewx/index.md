Path: mdn-web-docs > files > en-us > web > api > cssskewx > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewx > index.md --- title: CSSSkewX slug: Web/API/CSSSkewX page-type: web-api-interface browser-compat: api.CSSSkewX --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`CSSSkewX`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [`skewX()`](/en-US/docs/Web/CSS/transform-function/skewX) value of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref("CSSTransformValue")}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSSkewX.CSSSkewX", "CSSSkewX()")}} - : Creates a new `CSSSkewX` object. ## Instance properties - {{domxref('CSSSkewX.ax','ax')}} - : Returns or sets the x-axis value. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}