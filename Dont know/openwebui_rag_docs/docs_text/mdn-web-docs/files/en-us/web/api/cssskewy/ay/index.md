Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > ay > index.md --- title: "CSSSkewY: ay property" short-title: ay slug: Web/API/CSSSkewY/ay page-type: web-api-instance-property browser-compat: api.CSSSkewY.ay --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`ay`** property of the {{domxref("CSSSkewY")}} interface gets and sets the angle used to distort the element along the y-axis (or ordinate). ## Value A {{domxref("CSSNumericValue")}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}