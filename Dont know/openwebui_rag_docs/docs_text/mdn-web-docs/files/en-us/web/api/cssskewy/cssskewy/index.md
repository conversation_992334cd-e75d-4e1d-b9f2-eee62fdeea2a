Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > cssskewy > index.md --- title: "CSSSkewY: CSSSkewY() constructor" short-title: CSSSkewY() slug: Web/API/CSSSkewY/CSSSkewY page-type: web-api-constructor browser-compat: api.CSSSkewY.CSSSkewY --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`CSSSkewY()`** constructor creates a new {{domxref("CSSSkewY")}} object which represents the [`skewY()`](/en-US/docs/Web/CSS/transform-function/skewY) value of the individual {{CSSXRef('transform')}} property in CSS. ## Syntax ```js-nolint new CSSSkewY(ay) ``` ### Parameters - {{domxref('CSSSkewY.ay','ay')}} - : A value for the `ay` angle of the {{domxref('CSSSkewY')}} object to be constructed. This must be a {{domxref('CSSNumericValue')}}. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}