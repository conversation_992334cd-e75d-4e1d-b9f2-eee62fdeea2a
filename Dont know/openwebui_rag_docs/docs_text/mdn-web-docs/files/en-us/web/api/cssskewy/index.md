Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md

Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md Path: mdn-web-docs > files > en-us > web > api > cssskewy > index.md --- title: CSSSkewY slug: Web/API/CSSSkewY page-type: web-api-interface browser-compat: api.CSSSkewY --- {{APIRef("CSS Typed OM")}}{{AvailableInWorkers}} The **`CSSSkewY`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [`skewY()`](/en-US/docs/Web/CSS/transform-function/skewY) value of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref("CSSTransformValue")}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSSkewY.CSSSkewY", "CSSSkewY()")}} - : Creates a new `CSSSkewY` object. ## Instance properties _Inherits properties from its ancestor_ {{domxref("CSSTransformValue")}}. - {{domxref('CSSSkewY.ay','ay')}} - : Returns or sets the y-axis value. ## Instance methods _Inherits methods from its ancestor_ {{domxref("CSSTransformValue")}}. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}