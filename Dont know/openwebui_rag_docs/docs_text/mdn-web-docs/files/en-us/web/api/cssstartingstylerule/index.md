Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md

Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md Path: mdn-web-docs > files > en-us > web > api > cssstartingstylerule > index.md --- title: CSSStartingStyleRule slug: Web/API/CSSStartingStyleRule page-type: web-api-interface browser-compat: api.CSSStartingStyleRule --- {{ APIRef("CSSOM") }} The **`CSSStartingStyleRule`** interface of the [CSS Object Model](/en-US/docs/Web/API/CSS_Object_Model) represents a CSS {{CSSxRef("@starting-style")}} at-rule. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its ancestors {{domxref("CSSGroupingRule")}} and {{domxref("CSSRule")}}._ ## Instance methods _Inherits methods from its ancestors {{domxref("CSSGroupingRule")}} and {{domxref("CSSRule")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{CSSxRef("@starting-style")}} - [CSS transitions](/en-US/docs/Web/CSS/CSS_transitions) module - [Using dynamic styling information](/en-US/docs/Web/API/CSS_Object_Model/Using_dynamic_styling_information)