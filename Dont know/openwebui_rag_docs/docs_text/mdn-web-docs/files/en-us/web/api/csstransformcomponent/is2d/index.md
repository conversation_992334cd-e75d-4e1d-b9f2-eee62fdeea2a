Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformcomponent > is2d > index.md --- title: "CSSTransformComponent: is2D property" short-title: is2D slug: Web/API/CSSTransformComponent/is2D page-type: web-api-instance-property browser-compat: api.CSSTransformComponent.is2D --- {{APIRef("CSS Typed OM")}} The **`is2D`** read-only property of the {{domxref("CSSTransformComponent")}} interface indicates where the transform is 2D or 3D. ## Value A boolean. True indicating the transform is a 2D transform, false if it is a 3D transform. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}