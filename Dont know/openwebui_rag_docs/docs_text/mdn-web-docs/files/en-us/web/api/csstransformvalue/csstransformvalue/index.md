Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > csstransformvalue > index.md --- title: "CSSTransformValue: CSSTransformValue() constructor" short-title: CSSTransformValue() slug: Web/API/CSSTransformValue/CSSTransformValue page-type: web-api-constructor browser-compat: api.CSSTransformValue.CSSTransformValue --- {{APIRef("CSS Typed OM")}} The **`CSSTransformValue()`** constructor creates a new {{domxref("CSSTransformValue")}} object which represents a list of individual transform objects. ## Syntax ```js-nolint new CSSTransformValue(transforms) ``` ### Parameters - `transforms` - : A list of {{domxref("CSSTransformComponent")}} objects to iterate over. ### Return value A new {{domxref("CSSTransformValue")}}. ### Exceptions - {{jsxref("TypeError")}} - : Raised if transforms is empty. ## Examples To do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}