Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > entries > index.md --- title: "CSSTransformValue: entries() method" short-title: entries() slug: Web/API/CSSTransformValue/entries page-type: web-api-instance-method browser-compat: api.CSSTransformValue.entries --- {{APIRef("CSS Typed OM")}} The **`CSSTransformValue.entries()`** method returns an array of a given object's own enumerable property `[key, value]` pairs in the same order as that provided by a [`for...in`](/en-US/docs/Web/JavaScript/Reference/Statements/for...in) loop (the difference being that a for-in loop enumerates properties in the prototype chain as well). ## Syntax ```js-nolint entries(obj) ``` ### Parameters - `obj` - : The {{domxref('CSSTransformValue')}} whose enumerable own property `[key, value]` pairs are to be returned. ### Return value An array of the given `CSSTransformValue` object's own enumerable property `[key, value]` pairs. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}