Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > keys > index.md --- title: "CSSTransformValue: keys() method" short-title: keys() slug: Web/API/CSSTransformValue/keys page-type: web-api-instance-method browser-compat: api.CSSTransformValue.keys --- {{APIRef("CSS Typed OM")}} The **`CSSTransformValue.keys()`** method returns a new _array iterator_ object that contains the keys for each index in the array. ## Syntax ```js-nolint keys() ``` ### Parameters None. ### Return value A new {{jsxref("Array")}}. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}