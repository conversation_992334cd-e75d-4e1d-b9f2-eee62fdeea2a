Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > length > index.md --- title: "CSSTransformValue: length property" short-title: length slug: Web/API/CSSTransformValue/length page-type: web-api-instance-property browser-compat: api.CSSTransformValue.length --- {{APIRef("CSS Typed OM")}} The read-only **`length`** property of the {{domxref("CSSTransformValue")}} interface returns the number of transform components in the list. ## Value An integer representing the number of transform components in the list. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}