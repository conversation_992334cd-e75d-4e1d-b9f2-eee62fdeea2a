Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > tomatrix > index.md --- title: "CSSTransformValue: toMatrix() method" short-title: toMatrix() slug: Web/API/CSSTransformValue/toMatrix page-type: web-api-instance-method browser-compat: api.CSSTransformValue.toMatrix --- {{APIRef("CSS Typed OM")}} The **`toMatrix()`** method of the {{domxref("CSSTransformValue")}} interface returns a {{domxref('DOMMatrix')}} object. ## Syntax ```js-nolint toMatrix() ``` ### Parameters None. ### Return value A {{domxref('DOMMatrix')}} object. ### Exceptions - {{jsxref("TypeError")}} - : Raised if any lengths involved in generating the matrix are not compatible units with px (such as relative lengths or percentages). ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}