Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md

Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > csstransformvalue > values > index.md --- title: "CSSTransformValue: values() method" short-title: values() slug: Web/API/CSSTransformValue/values page-type: web-api-instance-method browser-compat: api.CSSTransformValue.values --- {{APIRef("CSS Typed OM")}} The **`CSSTransformValue.values()`** returns a new _array iterator_ object that contains the values for each index in the CSSTransformValue object. ## Syntax ```js-nolint values() ``` ### Parameters None. ### Return value A new {{jsxref("Array")}}. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}