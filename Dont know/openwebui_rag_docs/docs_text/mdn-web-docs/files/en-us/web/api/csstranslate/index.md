Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md

Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > index.md --- title: CSSTranslate slug: Web/API/CSSTranslate page-type: web-api-interface browser-compat: api.CSSTranslate --- {{APIRef("CSS Typed Object Model API")}} The **`CSSTranslate`** interface of the [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Object_Model) represents the [translate()](/en-US/docs/Web/CSS/transform-function/translate) value of the individual {{CSSXRef('transform')}} property in CSS. It inherits properties and methods from its parent {{domxref('CSSTransformValue')}}. {{InheritanceDiagram}} ## Constructor - {{domxref("CSSTranslate.CSSTranslate", "CSSTranslate()")}} - : Creates a new `CSSTranslate` object. ## Instance properties - {{domxref('CSSTranslate.x','x')}} - : Returns or sets the x-axis value. - {{domxref('CSSTranslate.y','y')}} - : Returns or sets the y-axis value. - {{domxref('CSSTranslate.z','z')}} - : Returns or sets the z-axis value. ## Examples To do. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}