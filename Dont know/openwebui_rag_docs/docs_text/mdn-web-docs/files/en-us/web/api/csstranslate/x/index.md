Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md

Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > x > index.md --- title: "CSSTranslate: x property" short-title: x slug: Web/API/CSSTranslate/x page-type: web-api-instance-property browser-compat: api.CSSTranslate.x --- {{APIRef("CSS Typed OM")}} The **`x`** property of the {{domxref("CSSTranslate")}} interface gets and sets the abscissa or x-axis of the translating vector. ## Value A {{cssxref('length-percentage')}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}