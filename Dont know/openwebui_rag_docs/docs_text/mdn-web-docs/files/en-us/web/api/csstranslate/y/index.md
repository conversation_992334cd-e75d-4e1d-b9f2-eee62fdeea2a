Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md

Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > y > index.md --- title: "CSSTranslate: y property" short-title: y slug: Web/API/CSSTranslate/y page-type: web-api-instance-property browser-compat: api.CSSTranslate.y --- {{APIRef("CSS Typed OM")}} The **`y`** property of the {{domxref("CSSTranslate")}} interface gets and sets the ordinate or y-axis of the translating vector. ## Value A {{cssxref('length-percentage')}} ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}