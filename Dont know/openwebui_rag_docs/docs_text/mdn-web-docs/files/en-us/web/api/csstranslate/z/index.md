Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md

Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md Path: mdn-web-docs > files > en-us > web > api > csstranslate > z > index.md --- title: "CSSTranslate: z property" short-title: z slug: Web/API/CSSTranslate/z page-type: web-api-instance-property browser-compat: api.CSSTranslate.z --- {{APIRef("CSS Typed OM")}} The **`z`** property of the {{domxref("CSSTranslate")}} interface representing the z-component of the translating vector. A positive value moves the element towards the viewer, and a negative value farther away. If this value is present then the transform is a 3D transform and the `is2D` property will be set to false. ## Value A {{cssxref('length')}}. ## Examples To Do ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}