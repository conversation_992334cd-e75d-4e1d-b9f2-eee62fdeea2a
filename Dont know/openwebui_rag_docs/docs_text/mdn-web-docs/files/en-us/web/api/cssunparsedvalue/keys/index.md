Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > keys > index.md

Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > keys > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > keys > index.md --- title: "CSSUnparsedValue: keys() method" short-title: keys() slug: Web/API/CSSUnparsedValue/keys page-type: web-api-instance-method browser-compat: api.CSSUnparsedValue.keys --- {{APIRef("CSS Typed OM")}} The **`CSSUnparsedValue.keys()`** method returns a new _array iterator_ object that contains the keys for each index in the array. ## Syntax ```js-nolint keys() ``` ### Parameters None. ### Return value A new {{jsxref("Array")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("CSSUnparsedValue.CSSUnparsedValue", "CSSUnparsedValue()")}} - {{domxref("CSSUnparsedValue.entries")}} - {{domxref("CSSUnparsedValue.forEach")}} - {{domxref("CSSUnparsedValue.length")}} - {{domxref("CSSUnparsedValue.values")}} - [Using the CSS Typed OM](/en-US/docs/Web/API/CSS_Typed_OM_API/Guide) - [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Typed_OM_API)