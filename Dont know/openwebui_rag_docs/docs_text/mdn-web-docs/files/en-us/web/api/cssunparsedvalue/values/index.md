Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > values > index.md

Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > values > index.md Path: mdn-web-docs > files > en-us > web > api > cssunparsedvalue > values > index.md --- title: "CSSUnparsedValue: values() method" short-title: values() slug: Web/API/CSSUnparsedValue/values page-type: web-api-instance-method browser-compat: api.CSSUnparsedValue.values --- {{APIRef("CSS Typed OM")}} The **`CSSUnparsedValue.values()`** method returns a new _array iterator_ object that contains the values for each index in the CSSUnparsedValue object. ## Syntax ```js-nolint values() ``` ### Parameters None. ### Return value A new {{jsxref("Array")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("CSSUnparsedValue.CSSUnparsedValue", "CSSUnparsedValue()")}} - {{domxref("CSSUnparsedValue.entries")}} - {{domxref("CSSUnparsedValue.forEach")}} - {{domxref("CSSUnparsedValue.keys")}} - {{domxref("CSSUnparsedValue.length")}} - [Using the CSS Typed OM](/en-US/docs/Web/API/CSS_Typed_OM_API/Guide) - [CSS Typed Object Model API](/en-US/docs/Web/API/CSS_Typed_OM_API)