Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md

Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > cssvariablereferencevalue > index.md --- title: "CSSVariableReferenceValue: CSSVariableReferenceValue() constructor" short-title: CSSVariableReferenceValue() slug: Web/API/CSSVariableReferenceValue/CSSVariableReferenceValue page-type: web-api-constructor browser-compat: api.CSSVariableReferenceValue.CSSVariableReferenceValue --- {{APIRef("CSSOM")}} Creates a new {{domxref('CSSVariableReferenceValue')}}. ## Syntax ```js-nolint new CSSVariableReferenceValue(variable) new CSSVariableReferenceValue(variable, fallback) ``` ### Parameters - `variable` - : A [custom property name](/en-US/docs/Web/CSS/--*). - `fallback` {{optional_inline}} - : A [custom property fallback value](/en-US/docs/Web/CSS/CSS_cascading_variables/Using_CSS_custom_properties#custom_property_fallback_values). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}