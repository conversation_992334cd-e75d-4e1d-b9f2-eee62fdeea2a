Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md

Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > fallback > index.md --- title: "CSSVariableReferenceValue: fallback property" short-title: fallback slug: Web/API/CSSVariableReferenceValue/fallback page-type: web-api-instance-property browser-compat: api.CSSVariableReferenceValue.fallback --- {{APIRef("CSSOM")}} The **`fallback`** read-only property of the {{domxref("CSSVariableReferenceValue")}} interface returns the [custom property fallback value](/en-US/docs/Web/CSS/CSS_cascading_variables/Using_CSS_custom_properties#custom_property_fallback_values) of the {{domxref("CSSVariableReferenceValue")}}. ## Value A {{domxref('CSSUnparsedValue')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}