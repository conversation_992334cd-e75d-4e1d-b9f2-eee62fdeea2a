Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md

Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md Path: mdn-web-docs > files > en-us > web > api > cssvariablereferencevalue > variable > index.md --- title: "CSSVariableReferenceValue: variable property" short-title: variable slug: Web/API/CSSVariableReferenceValue/variable page-type: web-api-instance-property browser-compat: api.CSSVariableReferenceValue.variable --- {{APIRef("CSSOM")}} The **`variable`** property of the {{domxref("CSSVariableReferenceValue")}} interface returns the [custom property name](/en-US/docs/Web/CSS/--*) of the {{domxref("CSSVariableReferenceValue")}}. ## Value A string beginning with `--` (that is, a [custom property name](/en-US/docs/Web/CSS/--*)). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}