Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > clear > index.md --- title: "CustomStateSet: clear() method" short-title: clear() slug: Web/API/CustomStateSet/clear page-type: web-api-instance-method browser-compat: api.CustomStateSet.clear --- {{APIRef("Web Components")}} The **`clear()`** method of the {{domxref("CustomStateSet")}} interface removes all elements from the `CustomStateSet` object. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value Undefined. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}