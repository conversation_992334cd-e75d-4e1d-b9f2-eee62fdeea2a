Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > delete > index.md --- title: "CustomStateSet: delete() method" short-title: delete() slug: Web/API/CustomStateSet/delete page-type: web-api-instance-method browser-compat: api.CustomStateSet.delete --- {{APIRef("Web Components")}} The **`delete()`** method of the {{domxref("CustomStateSet")}} interface deletes a single value from the `CustomStateSet`. ## Syntax ```js-nolint delete(value) ``` ### Parameters - `value` - : value to remove from the `CustomStateSet`. ### Return value Returns `true` if `value` was in the `CustomStateSet`; otherwise `false`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}