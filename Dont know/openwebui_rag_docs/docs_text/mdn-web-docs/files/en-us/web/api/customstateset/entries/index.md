Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > entries > index.md --- title: "CustomStateSet: entries() method" short-title: entries() slug: Web/API/CustomStateSet/entries page-type: web-api-instance-method browser-compat: api.CustomStateSet.entries --- {{APIRef("Web Components")}} The **`entries`** method of the {{domxref("CustomStateSet")}} interface returns a new [iterator](/en-US/docs/Web/JavaScript/Reference/Iteration_protocols) object, containing an array of `[value,value]` for each element in the `CustomStateSet`. ## Syntax ```js-nolint entries() ``` ### Parameters None. ### Return value A new iterator object that contains an array of `[value, value]` for each element in the `CustomStateSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}