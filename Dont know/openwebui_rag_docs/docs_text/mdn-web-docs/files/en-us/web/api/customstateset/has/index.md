Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > has > index.md --- title: "CustomStateSet: has() method" short-title: has() slug: Web/API/CustomStateSet/has page-type: web-api-instance-method browser-compat: api.CustomStateSet.has --- {{APIRef("Web Components")}} The **`has()`** method of the {{domxref("CustomStateSet")}} interface returns a {{jsxref("Boolean")}} asserting whether an element is present with the given value. ## Syntax ```js-nolint has(value) ``` ### Parameters - `value` - : The value to test for in the `CustomStateSet` object. ### Return value A {{jsxref("Boolean")}}, `true` if `value` exists in the `CustomStateSet`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}