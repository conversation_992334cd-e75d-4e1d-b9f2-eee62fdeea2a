Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > keys > index.md --- title: "CustomStateSet: keys() method" short-title: keys() slug: Web/API/CustomStateSet/keys page-type: web-api-instance-method browser-compat: api.CustomStateSet.keys --- {{APIRef("Web Components")}} The **`keys()`** method of the {{domxref("CustomStateSet")}} interface is an alias for {{domxref("CustomStateSet.values")}}. ## Syntax ```js-nolint keys() ``` ### Parameters None. ### Return value A new iterator object containing the values for each element in the given `CustomStateSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}