Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > size > index.md --- title: "CustomStateSet: size property" short-title: size slug: Web/API/CustomStateSet/size page-type: web-api-instance-property browser-compat: api.CustomStateSet.size --- {{APIRef("Web Components")}} The **`size`** property of the {{domxref("CustomStateSet")}} interface returns the number of values in the `CustomStateSet`. ## Value An integer indicating how many properties the `CustomStateSet` has. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}