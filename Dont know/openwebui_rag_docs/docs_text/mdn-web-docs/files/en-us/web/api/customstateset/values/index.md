Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md

Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md Path: mdn-web-docs > files > en-us > web > api > customstateset > values > index.md --- title: "CustomStateSet: values() method" short-title: values() slug: Web/API/CustomStateSet/values page-type: web-api-instance-method browser-compat: api.CustomStateSet.values --- {{APIRef("Web Components")}} The **`values()`** method of the {{domxref("CustomStateSet")}} interface returns a new iterator object that yields the values for each element in the `CustomStateSet` object in insertion order. ## Syntax ```js-nolint values() ``` ### Parameters None. ### Return value A new iterator object containing the values for each element in the given `CustomStateSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}