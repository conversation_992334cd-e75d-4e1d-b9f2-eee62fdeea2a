Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md

Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md Path: mdn-web-docs > files > en-us > web > api > datatransfer > datatransfer > index.md --- title: "DataTransfer: DataTransfer() constructor" short-title: DataTransfer() slug: Web/API/DataTransfer/DataTransfer page-type: web-api-constructor browser-compat: api.DataTransfer.DataTransfer --- {{APIRef("HTML Drag and Drop API")}} The **`DataTransfer`** constructor creates a new {{domxref("DataTransfer")}} object instance. ## Syntax ```js-nolint new DataTransfer() ``` ### Parameters None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}