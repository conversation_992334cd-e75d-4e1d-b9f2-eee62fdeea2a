Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotionevent > interval > index.md --- title: "DeviceMotionEvent: interval property" short-title: interval slug: Web/API/DeviceMotionEvent/interval page-type: web-api-instance-property browser-compat: api.DeviceMotionEvent.interval --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`interval`** read-only property of the {{domxref("DeviceMotionEvent")}} interface returns the interval, in milliseconds, at which data is obtained from the underlying hardware. You can use this to determine the granularity of motion events. ## Value A number representing the interval of time, in milliseconds. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Device orientation events/Detecting device orientation", "Detecting device orientation", "", "nocode")}} - {{domxref("Device orientation events/Orientation and motion data explained", "Orientation and motion data explained", "", "nocode")}} - {{DOMxRef("Window/devicemotion_event", "devicemotion")}} event