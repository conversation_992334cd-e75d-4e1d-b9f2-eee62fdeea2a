Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > index.md --- title: DeviceMotionEventAcceleration slug: Web/API/DeviceMotionEventAcceleration page-type: web-api-interface browser-compat: api.DeviceMotionEventAcceleration --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`DeviceMotionEventAcceleration`** interface of the {{domxref("Device Orientation Events", "", "", "nocode")}} provides information about the amount of acceleration the device is experiencing along all three axes. ## Instance properties - {{domxref("DeviceMotionEventAcceleration.x")}} {{ReadOnlyInline}} - : The amount of acceleration along the X axis. - {{domxref("DeviceMotionEventAcceleration.y")}} {{ReadOnlyInline}} - : The amount of acceleration along the Y axis. - {{domxref("DeviceMotionEventAcceleration.z")}} {{ReadOnlyInline}} - : The amount of acceleration along the Z axis. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}