Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > x > index.md --- title: "DeviceMotionEventAcceleration: x property" short-title: x slug: Web/API/DeviceMotionEventAcceleration/x page-type: web-api-instance-property browser-compat: api.DeviceMotionEventAcceleration.x --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`x`** read-only property of the {{domxref("DeviceMotionEventAcceleration")}} interface indicates the amount of acceleration that occurred along the X axis in a [`DeviceMotionEventAcceleration`](/en-US/docs/Web/API/DeviceMotionEventAcceleration) object. ## Value A `double` indicating the amount of acceleration along the X axis. See [Accelerometer values explained](/en-US/docs/Web/API/Device_orientation_events/Detecting_device_orientation) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}