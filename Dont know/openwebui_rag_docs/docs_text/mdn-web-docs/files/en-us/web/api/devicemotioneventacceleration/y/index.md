Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > y > index.md --- title: "DeviceMotionEventAcceleration: y property" short-title: y slug: Web/API/DeviceMotionEventAcceleration/y page-type: web-api-instance-property browser-compat: api.DeviceMotionEventAcceleration.y --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`y`** read-only property of the {{domxref("DeviceMotionEventAcceleration")}} interface indicates the amount of acceleration that occurred along the Y axis in a [`DeviceMotionEventAcceleration`](/en-US/docs/Web/API/DeviceMotionEventAcceleration) object. ## Value A `double` indicating the amount of acceleration along the Y axis. See [Accelerometer values explained](/en-US/docs/Web/API/Device_orientation_events/Detecting_device_orientation) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}