Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventacceleration > z > index.md --- title: "DeviceMotionEventAcceleration: z property" short-title: z slug: Web/API/DeviceMotionEventAcceleration/z page-type: web-api-instance-property browser-compat: api.DeviceMotionEventAcceleration.z --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`z`** read-only property of the {{domxref("DeviceMotionEventAcceleration")}} interface indicates the amount of acceleration that occurred along the Z axis in a [`DeviceMotionEventAcceleration`](/en-US/docs/Web/API/DeviceMotionEventAcceleration) object. ## Value A `double` indicating the amount of acceleration along the Z axis. See [Accelerometer values explained](/en-US/docs/Web/API/Device_orientation_events/Detecting_device_orientation) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}