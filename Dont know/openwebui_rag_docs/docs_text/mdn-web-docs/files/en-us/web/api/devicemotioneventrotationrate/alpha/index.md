Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > alpha > index.md --- title: "DeviceMotionEventRotationRate: alpha property" short-title: alpha slug: Web/API/DeviceMotionEventRotationRate/alpha page-type: web-api-instance-property browser-compat: api.DeviceMotionEventRotationRate.alpha --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`alpha`** read-only property of the {{domxref("DeviceMotionEventRotationRate")}} interface indicates the rate of rotation around the Z axis, in degrees per second. ## Value A `double` indicating the rate of rotation around the Z axis, in degrees per second. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}