Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > beta > index.md --- title: "DeviceMotionEventRotationRate: beta property" short-title: beta slug: Web/API/DeviceMotionEventRotationRate/beta page-type: web-api-instance-property browser-compat: api.DeviceMotionEventRotationRate.beta --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`beta`** read-only property of the {{domxref("DeviceMotionEventRotationRate")}} interface indicates the rate of rotation around the X axis, in degrees per second. ## Value A `double` indicating the rate of rotation around the X axis, in degrees per second. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}