Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > gamma > index.md --- title: "DeviceMotionEventRotationRate: gamma property" short-title: gamma slug: Web/API/DeviceMotionEventRotationRate/gamma page-type: web-api-instance-property browser-compat: api.DeviceMotionEventRotationRate.gamma --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`gamma`** read-only property of the {{domxref("DeviceMotionEventRotationRate")}} interface indicates the rate of rotation around the Y axis, in degrees per second. ## Value A `double` indicating the rate of rotation around the Y axis, in degrees per second. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}