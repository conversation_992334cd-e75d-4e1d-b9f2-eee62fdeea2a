Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md

Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md Path: mdn-web-docs > files > en-us > web > api > devicemotioneventrotationrate > index.md --- title: DeviceMotionEventRotationRate slug: Web/API/DeviceMotionEventRotationRate page-type: web-api-interface browser-compat: api.DeviceMotionEventRotationRate --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} A **`DeviceMotionEventRotationRate`** interface of the {{domxref("Device Orientation Events", "", "", "nocode")}} provides information about the rate at which the device is rotating around all three axes. ## Instance properties - {{ domxref("DeviceMotionEventRotationRate.alpha") }} {{ReadOnlyInline}} - : The amount of rotation around the Z axis, in degrees per second. - {{ domxref("DeviceMotionEventRotationRate.beta") }} {{ReadOnlyInline}} - : The amount of rotation around the X axis, in degrees per second. - {{ domxref("DeviceMotionEventRotationRate.gamma") }} {{ReadOnlyInline}} - : The amount of rotation around the Y axis, in degrees per second. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}