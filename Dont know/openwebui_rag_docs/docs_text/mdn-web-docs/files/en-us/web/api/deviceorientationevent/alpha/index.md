Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > alpha > index.md

Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > alpha > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > alpha > index.md --- title: "DeviceOrientationEvent: alpha property" short-title: alpha slug: Web/API/DeviceOrientationEvent/alpha page-type: web-api-instance-property browser-compat: api.DeviceOrientationEvent.alpha --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`alpha`** read-only property of the {{domxref("DeviceOrientationEvent")}} interface returns the rotation of the device around the Z axis; that is, the number of degrees by which the device is being twisted around the center of the screen. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Device orientation events/Detecting device orientation", "Detecting device orientation", "", "nocode")}} - {{domxref("Device orientation events/Orientation and motion data explained", "Orientation and motion data explained", "", "nocode")}} - {{domxref("Window.deviceorientation_event", "deviceorientation")}} event - {{domxref("Window.deviceorientationabsolute_event", "deviceorientationabsolute")}} event