Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > beta > index.md

Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > beta > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > beta > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > beta > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > beta > index.md --- title: "DeviceOrientationEvent: beta property" short-title: beta slug: Web/API/DeviceOrientationEvent/beta page-type: web-api-instance-property browser-compat: api.DeviceOrientationEvent.beta --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`beta`** read-only property of the {{domxref("DeviceOrientationEvent")}} interface returns the rotation of the device around the X axis; that is, the number of degrees, ranged between -180 and 180, by which the device is tipped forward or backward. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Device orientation events/Detecting device orientation", "Detecting device orientation", "", "nocode")}} - {{domxref("Device orientation events/Orientation and motion data explained", "Orientation and motion data explained", "", "nocode")}} - {{domxref("Window.deviceorientation_event", "deviceorientation")}} event - {{domxref("Window.deviceorientationabsolute_event", "deviceorientationabsolute")}} event