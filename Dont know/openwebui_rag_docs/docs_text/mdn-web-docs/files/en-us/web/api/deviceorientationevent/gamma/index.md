Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > gamma > index.md

Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > gamma > index.md Path: mdn-web-docs > files > en-us > web > api > deviceorientationevent > gamma > index.md --- title: "DeviceOrientationEvent: gamma property" short-title: gamma slug: Web/API/DeviceOrientationEvent/gamma page-type: web-api-instance-property browser-compat: api.DeviceOrientationEvent.gamma --- {{APIRef("Device Orientation Events")}}{{securecontext_header}} The **`gamma`** read-only property of the {{domxref("DeviceOrientationEvent")}} interface returns the rotation of the device around the Y axis; that is, the number of degrees, ranged between `-90` and `90`, by which the device is tilted left or right. See [Orientation and motion data explained](/en-US/docs/Web/API/Device_orientation_events/Orientation_and_motion_data_explained) for details. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Device orientation events/Detecting device orientation", "Detecting device orientation", "", "nocode")}} - {{domxref("Device orientation events/Orientation and motion data explained", "Orientation and motion data explained", "", "nocode")}} - {{domxref("Window.deviceorientation_event", "deviceorientation")}} event - {{domxref("Window.deviceorientationabsolute_event", "deviceorientationabsolute")}} event