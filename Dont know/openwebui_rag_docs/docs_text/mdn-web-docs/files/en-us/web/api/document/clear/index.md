Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md Path: mdn-web-docs > files > en-us > web > api > document > clear > index.md --- title: "Document: clear() method" short-title: clear() slug: Web/API/Document/clear page-type: web-api-instance-method status: - deprecated browser-compat: api.Document.clear --- {{APIRef("DOM")}}{{Deprecated_Header}} The **`Document.clear()`** method does nothing, but doesn't raise any error. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}