Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md

Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md Path: mdn-web-docs > files > en-us > web > api > document > contenttype > index.md --- title: "Document: contentType property" short-title: contentType slug: Web/API/Document/contentType page-type: web-api-instance-property browser-compat: api.Document.contentType --- {{APIRef}} The **`Document.contentType`** read-only property returns the MIME type that the document is being rendered as. This may come from HTTP headers or other sources of MIME information, and might be affected by automatic type conversions performed by either the browser or extensions. > [!NOTE] > This property is unaffected by {{HTMLElement("meta")}} > elements. ## Value `contentType` is a read-only property. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}