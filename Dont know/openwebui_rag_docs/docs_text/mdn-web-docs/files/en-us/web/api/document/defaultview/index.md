Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md

Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md Path: mdn-web-docs > files > en-us > web > api > document > defaultview > index.md --- title: "Document: defaultView property" short-title: defaultView slug: Web/API/Document/defaultView page-type: web-api-instance-property browser-compat: api.Document.defaultView --- {{ApiRef}} In browsers, **`document.defaultView`** returns the {{domxref("Window", "window")}} object associated with {{Glossary("Browsing_context", "a document")}}, or `null` if none is available. This property is read-only. ## Value The {{domxref("Window", "window")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}