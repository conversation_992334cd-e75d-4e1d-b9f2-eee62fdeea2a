Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md

Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md Path: mdn-web-docs > files > en-us > web > api > document > dir > index.md --- title: "Document: dir property" short-title: dir slug: Web/API/Document/dir page-type: web-api-instance-property browser-compat: api.Document.dir --- {{ApiRef("HTML DOM")}} The **`Document.dir`** property is a string representing the directionality of the text of the document, whether left to right (default) or right to left. Possible values are `'rtl'`, right to left, and `'ltr'`, left to right. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [`dir`](/en-US/docs/Web/HTML/Reference/Global_attributes/dir) global attribute