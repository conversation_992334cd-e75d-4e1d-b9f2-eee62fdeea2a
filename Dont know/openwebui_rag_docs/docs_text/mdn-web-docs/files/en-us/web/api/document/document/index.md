Path: mdn-web-docs > files > en-us > web > api > document > document > index.md

Path: mdn-web-docs > files > en-us > web > api > document > document > index.md Path: mdn-web-docs > files > en-us > web > api > document > document > index.md Path: mdn-web-docs > files > en-us > web > api > document > document > index.md Path: mdn-web-docs > files > en-us > web > api > document > document > index.md Path: mdn-web-docs > files > en-us > web > api > document > document > index.md --- title: "Document: Document() constructor" short-title: Document() slug: Web/API/Document/Document page-type: web-api-constructor browser-compat: api.Document.Document --- {{APIRef}} The **`Document`** constructor creates a new {{domxref("Document")}} object that is a web page loaded in the browser and serving as an entry point into the page's content. ## Syntax ```js-nolint new Document() ``` ### Parameters None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}