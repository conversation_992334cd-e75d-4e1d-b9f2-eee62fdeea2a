Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md

Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md Path: mdn-web-docs > files > en-us > web > api > document > embeds > index.md --- title: "Document: embeds property" short-title: embeds slug: Web/API/Document/embeds page-type: web-api-instance-property browser-compat: api.Document.embeds --- {{ApiRef}} The **`embeds`** read-only property of the {{domxref("Document")}} interface returns a list of the embedded {{htmlelement("embed")}} elements within the current document. ## Value An {{domxref("HTMLCollection")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}