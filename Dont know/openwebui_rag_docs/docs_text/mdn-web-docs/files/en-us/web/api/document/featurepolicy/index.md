Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md

Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > document > featurepolicy > index.md --- title: "Document: featurePolicy property" short-title: featurePolicy slug: Web/API/Document/featurePolicy page-type: web-api-instance-property status: - experimental browser-compat: api.Document.featurePolicy --- {{APIRef("Feature Policy")}}{{SeeCompatTable}} The **`featurePolicy`** read-only property of the {{domxref("Document")}} interface returns the {{domxref("FeaturePolicy")}} interface which provides a simple API for inspecting the [Permissions Policies](/en-US/docs/Web/HTTP/Guides/Permissions_Policy) applied to a specific document. ## Value A {{domxref("FeaturePolicy")}} object that can be used to inspect the Permissions Policy settings applied to the document. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}