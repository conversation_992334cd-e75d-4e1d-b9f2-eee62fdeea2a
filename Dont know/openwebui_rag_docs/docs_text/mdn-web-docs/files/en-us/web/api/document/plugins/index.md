Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md

Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md Path: mdn-web-docs > files > en-us > web > api > document > plugins > index.md --- title: "Document: plugins property" short-title: plugins slug: Web/API/Document/plugins page-type: web-api-instance-property browser-compat: api.Document.plugins --- {{APIRef("DOM")}} The **`plugins`** read-only property of the {{domxref("Document")}} interface returns an {{domxref("HTMLCollection")}} object containing one or more {{domxref("HTMLEmbedElement")}}s representing the {{HTMLElement("embed")}} elements in the current document. > [!NOTE] > For a list of installed plugins, use [Navigator.plugins](/en-US/docs/Web/API/Navigator/plugins) > instead. ## Value An {{domxref("HTMLCollection")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}