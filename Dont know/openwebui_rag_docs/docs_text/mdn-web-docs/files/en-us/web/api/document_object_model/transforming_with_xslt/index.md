Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md

Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md Path: mdn-web-docs > files > en-us > web > api > document_object_model > transforming_with_xslt > index.md --- title: Transforming with XSLT slug: Web/API/Document_Object_Model/Transforming_with_XSLT page-type: guide --- {{DefaultAPISidebar("DOM")}} One noticeable trend in W3C standards has been the effort to separate content from style. This would allow the same style to be reused for multiple content, as well as simplify maintenance and allow a quick (only modify one file) way to change the look of content. CSS (Cascade Style Sheets) was one of the first ways proposed by the W3C. CSS is a way to apply style rules to a web document. These style rules define how the document (the content) should be laid out. However, it has several limitations, such as lack of programming structures and ability to create complex layout models. CSS also has limited support for changing the position of an element. XSL (Extensible Stylesheet Language) Transformations are composed of two parts: XSL elements, which allow the transformation of an XML tree into another markup tree and XPath, a selection language for trees. XSLT takes an XML document (the content) and creates a brand new document based on the rules in the XSL stylesheet. This allows XSLT to add, remove and reorganize elements from the original XML document and thus allows more fine-grain control of the resulting document's structure. Transformations in XSLT are based on rules that consist of templates. Each template matches (using XPath) a certain fragment of the input XML document and then applies the substitution part on that fragment to create the new resulting document. ## Basic example This first example demonstrates the basics of setting up an XSLT transformation in a browser. The example takes an XML document that contains information about an article (title, list of authors and body text) and presents it in a human-readable form. The XML document (**example.xml**) is shown below. ```xml <?xml version="1.0"?> <?xml-stylesheet type="text/xsl" href="example.xsl"?> <Article> <Title>My Article</Title> <Authors> <Author>Mr. Foo</Author> <Author>Mr. Bar</Author> </Authors> <Body>This is my article text.</Body> </Article> ``` The `?xml-stylesheet` processing instruction in the XML file specifies the XSLT stylesheet to apply in its `href` attribute. This XSL stylesheet file (**example.xsl**) is shown below: ```xml <?xml version="1.0"?> <xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"> <xsl:output method="text"/> <xsl:template match="/"> Article - <xsl:value-of select="/Article/Title"/> Authors: <AUTHORS>