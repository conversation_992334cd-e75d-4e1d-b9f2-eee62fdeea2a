Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md

Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md Path: mdn-web-docs > files > en-us > web > api > documentfragment > documentfragment > index.md --- title: "DocumentFragment: DocumentFragment() constructor" short-title: DocumentFragment() slug: Web/API/DocumentFragment/DocumentFragment page-type: web-api-constructor browser-compat: api.DocumentFragment.DocumentFragment --- {{ApiRef("DOM")}} The **`DocumentFragment()`** constructor returns a new, empty {{domxref("DocumentFragment")}} object. ## Syntax ```js-nolint new DocumentFragment() ``` ### Parameters None. ### Return value A new {{domxref("DocumentFragment")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}