Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md

Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md Path: mdn-web-docs > files > en-us > web > api > documentpictureinpicture > enter_event > index.md --- title: "DocumentPictureInPicture: enter event" short-title: enter slug: Web/API/DocumentPictureInPicture/enter_event page-type: web-api-event status: - experimental browser-compat: api.DocumentPictureInPicture.enter_event --- {{APIRef("Document Picture-in-Picture API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`enter`** event of the {{domxref("DocumentPictureInPicture")}} interface is fired when the Picture-in-Picture window is successfully opened. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("enter", (event) => { }) onenter = (event) => { } ``` ## Event type A {{domxref("DocumentPictureInPictureEvent")}}. ## Examples ```js documentPictureInPicture.addEventListener("enter", (event) => { const pipWindow = event.window; console.log("Video player has entered the pip window"); const pipMuteButton = pipWindow.document.createElement("button"); pipMuteButton.textContent = "Mute"; pipMuteButton.addEventListener("click", () => { const pipVideo = pipWindow.document.querySelector("#video"); if (!pipVideo.muted) { pipVideo.muted = true; pipMuteButton.textContent = "Unmute"; } else { pipVideo.muted = false; pipMuteButton.textContent = "Mute"; } }); pipWindow.document.body.append(pipMuteButton); }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Document Picture-in-Picture API", "Document Picture-in-Picture API", "", "nocode")}} - [Using the Document Picture-in-Picture API](/en-US/docs/Web/API/Document_Picture-in-Picture_API/Using)