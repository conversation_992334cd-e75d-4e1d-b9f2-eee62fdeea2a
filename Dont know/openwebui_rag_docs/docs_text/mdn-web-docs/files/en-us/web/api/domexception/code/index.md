Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md

Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > code > index.md --- title: "DOMException: code property" short-title: code slug: Web/API/DOMException/code page-type: web-api-instance-property status: - deprecated browser-compat: api.DOMException.code --- {{APIRef("DOM")}}{{AvailableInWorkers}}{{deprecated_header}} The **`code`** read-only property of the {{domxref("DOMException")}} interface returns one of the legacy [error code constants](/en-US/docs/Web/API/DOMException#error_names), or `0` if none match. This field is used for historical reasons. New DOM exceptions don't use this anymore: they put this info in the {{domxref("DOMException.name")}} attribute. ## Value One of the [error code constants](/en-US/docs/Web/API/DOMException#error_names), or `0` if none match. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}