Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md

Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > message > index.md --- title: "DOMException: message property" short-title: message slug: Web/API/DOMException/message page-type: web-api-instance-property browser-compat: api.DOMException.message --- {{APIRef("DOM")}}{{AvailableInWorkers}} The **`message`** read-only property of the {{domxref("DOMException")}} interface returns a string representing a message or description associated with the given [error name](/en-US/docs/Web/API/DOMException#error_names). ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}