Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md

Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md Path: mdn-web-docs > files > en-us > web > api > domexception > name > index.md --- title: "DOMException: name property" short-title: name slug: Web/API/DOMException/name page-type: web-api-instance-property browser-compat: api.DOMException.name --- {{APIRef("DOM")}}{{AvailableInWorkers}} The **`name`** read-only property of the {{domxref("DOMException")}} interface returns a string that contains one of the strings associated with an [error name](/en-US/docs/Web/API/DOMException#error_names). ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}