Path: mdn-web-docs > files > en-us > web > api > domparser > domparser > index.md

Path: mdn-web-docs > files > en-us > web > api > domparser > domparser > index.md Path: mdn-web-docs > files > en-us > web > api > domparser > domparser > index.md Path: mdn-web-docs > files > en-us > web > api > domparser > domparser > index.md Path: mdn-web-docs > files > en-us > web > api > domparser > domparser > index.md --- title: "DOMParser: DOMParser() constructor" short-title: DOMParser() slug: Web/API/DOMParser/DOMParser page-type: web-api-constructor browser-compat: api.DOMParser.DOMParser --- {{APIRef("DOM")}} The **`DOMParser()`** constructor creates a new [`DOMParser`](/en-US/docs/Web/API/DOMParser) object. This object can be used to parse the text of a document using the `parseFromString()` method. ## Syntax ```js-nolint new DOMParser() ``` ### Parameters None. ### Return value A new [`DOMParser`](/en-US/docs/Web/API/DOMParser) object. This object can be used to parse the text of a document using the `parseFromString()` method. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}