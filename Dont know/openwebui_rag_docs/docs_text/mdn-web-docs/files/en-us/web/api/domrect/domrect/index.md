Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md

Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > domrect > index.md --- title: "DOMRect: DOMRect() constructor" short-title: DOMRect() slug: Web/API/DOMRect/DOMRect page-type: web-api-constructor browser-compat: api.DOMRect.DOMRect --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`DOMRect()`** constructor creates a new {{domxref("DOMRect")}} object. ## Syntax ```js-nolint new DOMRect(x, y, width, height) ``` ### Parameters - {{domxref("DOMRect.x", "x")}} - : The `x` coordinate of the `DOMRect`'s origin. - {{domxref("DOMRect.y", "y")}} - : The `y` coordinate of the `DOMRect`'s origin. - {{domxref("DOMRect.width", "width")}} - : The width of the `DOMRect`. - {{domxref("DOMRect.height", "height")}} - : The height of the `DOMRect`. ### Return value A new {{domxref("DOMRect")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMPoint")}} - {{domxref("DOMRect")}} - {{domxref("DOMRect.fromRect_static", "DOMRect.fromRect()")}}