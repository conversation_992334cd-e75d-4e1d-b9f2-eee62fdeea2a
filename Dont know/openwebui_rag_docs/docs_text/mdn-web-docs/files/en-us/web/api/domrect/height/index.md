Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md

Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > height > index.md --- title: "DOMRect: height property" short-title: height slug: Web/API/DOMRect/height page-type: web-api-instance-property browser-compat: api.DOMRect.height --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`height`** property of the {{domxref("DOMRect")}} interface represents the height of the rectangle. The value can be negative. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRectReadOnly")}}