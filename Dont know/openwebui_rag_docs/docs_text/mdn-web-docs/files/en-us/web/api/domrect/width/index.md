Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md

Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > width > index.md --- title: "DOMRect: width property" short-title: width slug: Web/API/DOMRect/width page-type: web-api-instance-property browser-compat: api.DOMRect.width --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`width`** property of the {{domxref("DOMRect")}} interface represents the width of the rectangle. The value can be negative. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRectReadOnly")}}