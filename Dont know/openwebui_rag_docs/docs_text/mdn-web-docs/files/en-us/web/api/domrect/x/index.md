Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md

Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > x > index.md --- title: "DOMRect: x property" short-title: x slug: Web/API/DOMRect/x page-type: web-api-instance-property browser-compat: api.DOMRect.x --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`x`** property of the {{domxref("DOMRect")}} interface represents the x-coordinate of the rectangle, which is the horizontal distance between the viewport's left edge and the rectangle's origin. When the rectangle's width is non-negative, the rectangle's horizontal origin is the viewport's left edge. If the width is negative, the rectangle's horizontal origin is the viewport's right edge. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRectReadOnly")}}