Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md

Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrect > y > index.md --- title: "DOMRect: y property" short-title: y slug: Web/API/DOMRect/y page-type: web-api-instance-property browser-compat: api.DOMRect.y --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`y`** property of the {{domxref("DOMRect")}} interface represents the y-coordinate of the rectangle, which is the vertical distance between the viewport's top edge and the rectangle's origin. When the rectangle's height is non-negative, the rectangle's vertical origin is the viewport's top edge. If the height has a negative height, the rectangle's vertical origin is the viewport's bottom edge. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRectReadOnly")}}