Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > bottom > index.md --- title: "DOMRectReadOnly: bottom property" short-title: bottom slug: Web/API/DOMRectReadOnly/bottom page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.bottom --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`bottom`** read-only property of the **`DOMRectReadOnly`** interface returns the bottom coordinate value of the `DOMRect`. (Has the same value as `y + height`, or `y` if `height` is negative.) ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}