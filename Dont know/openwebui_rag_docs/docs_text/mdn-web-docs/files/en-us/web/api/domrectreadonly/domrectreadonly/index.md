Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > domrectreadonly > index.md --- title: "DOMRectReadOnly: DOMRectReadOnly() constructor" short-title: DOMRectReadOnly() slug: Web/API/DOMRectReadOnly/DOMRectReadOnly page-type: web-api-constructor browser-compat: api.DOMRectReadOnly.DOMRectReadOnly --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`DOMRectReadOnly()`** constructor creates a new {{domxref("DOMRectReadOnly")}} object. ## Syntax ```js-nolint new DOMRectReadOnly(x, y, width, height) ``` ### Parameters - `x` - : The `x` coordinate of the `DOMRectReadOnly`'s origin. - `y` - : The `y` coordinate of the `DOMRectReadOnly`'s origin. - `width` - : The width of the `DOMRectReadOnly`. - `height` - : The height of the `DOMRectReadOnly`. ## Examples To create a new `DOMRectReadOnly`, you could run a line of code like so: ```js const myDOMRect = new DOMRectReadOnly(0, 0, 100, 100); // running 'myDOMRect' in the console would then return // DOMRectReadOnly { x: 0, y: 0, width: 100, height: 100, top: 0, right: 100, bottom: 100, left: 0 } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMPoint")}} - {{domxref("DOMRect")}}