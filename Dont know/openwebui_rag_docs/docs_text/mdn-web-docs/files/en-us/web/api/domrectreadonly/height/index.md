Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > height > index.md --- title: "DOMRectReadOnly: height property" short-title: height slug: Web/API/DOMRectReadOnly/height page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.height --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`height`** read-only property of the **`DOMRectReadOnly`** interface represents the height of the `DOMRect`. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}