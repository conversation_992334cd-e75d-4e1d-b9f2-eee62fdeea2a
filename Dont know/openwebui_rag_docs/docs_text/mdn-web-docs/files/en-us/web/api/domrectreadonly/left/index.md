Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > left > index.md --- title: "DOMRectReadOnly: left property" short-title: left slug: Web/API/DOMRectReadOnly/left page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.left --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`left`** read-only property of the **`DOMRectReadOnly`** interface returns the left coordinate value of the `DOMRect`. (Has the same value as `x`, or `x + width` if `width` is negative.) ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}