Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > right > index.md --- title: "DOMRectReadOnly: right property" short-title: right slug: Web/API/DOMRectReadOnly/right page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.right --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`right`** read-only property of the **`DOMRectReadOnly`** interface returns the right coordinate value of the `DOMRect`. (Has the same value as `x + width`, or `x` if `width` is negative.) ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}