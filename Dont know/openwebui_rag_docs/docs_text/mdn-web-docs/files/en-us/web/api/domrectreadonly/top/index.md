Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > top > index.md --- title: "DOMRectReadOnly: top property" short-title: top slug: Web/API/DOMRectReadOnly/top page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.top --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`top`** read-only property of the **`DOMRectReadOnly`** interface returns the top coordinate value of the `DOMRect`. (Has the same value as `y`, or `y + height` if `height` is negative.) ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}