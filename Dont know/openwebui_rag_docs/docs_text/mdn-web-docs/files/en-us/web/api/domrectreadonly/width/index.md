Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > width > index.md --- title: "DOMRectReadOnly: width property" short-title: width slug: Web/API/DOMRectReadOnly/width page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.width --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`width`** read-only property of the **`DOMRectReadOnly`** interface represents the width of the `DOMRect`. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}