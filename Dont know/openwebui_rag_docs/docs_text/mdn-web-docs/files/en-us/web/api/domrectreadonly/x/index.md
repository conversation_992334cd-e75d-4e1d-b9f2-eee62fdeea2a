Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > x > index.md --- title: "DOMRectReadOnly: x property" short-title: x slug: Web/API/DOMRectReadOnly/x page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.x --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`x`** read-only property of the **`DOMRectReadOnly`** interface represents the x coordinate of the `DOMRect`'s origin. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}