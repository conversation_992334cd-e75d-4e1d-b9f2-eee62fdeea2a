Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md

Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md Path: mdn-web-docs > files > en-us > web > api > domrectreadonly > y > index.md --- title: "DOMRectReadOnly: y property" short-title: y slug: Web/API/DOMRectReadOnly/y page-type: web-api-instance-property browser-compat: api.DOMRectReadOnly.y --- {{APIRef("Geometry Interfaces")}}{{AvailableInWorkers}} The **`y`** read-only property of the **`DOMRectReadOnly`** interface represents the y coordinate of the `DOMRect`'s origin. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}