Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md

Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > contains > index.md --- title: "DOMStringList: contains() method" short-title: contains() slug: Web/API/DOMStringList/contains page-type: web-api-instance-method browser-compat: api.DOMStringList.contains --- {{APIRef("DOM")}}{{AvailableInWorkers}} The **`contains()`** method returns a boolean indicating whether the given string is in the list. ## Syntax ```js-nolint contains(string) ``` ### Parameters - `string` - : A string you want to check for the existence of in the list. ### Return value A boolean indicating whether the given string is in the list. ### Exceptions - {{jsxref("TypeError")}} - : Thrown if no argument is provided. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}