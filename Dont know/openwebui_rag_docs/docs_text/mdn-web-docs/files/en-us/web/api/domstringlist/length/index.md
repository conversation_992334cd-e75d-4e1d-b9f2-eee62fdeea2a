Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > domstringlist > length > index.md --- title: "DOMStringList: length property" short-title: length slug: Web/API/DOMStringList/length page-type: web-api-instance-property browser-compat: api.DOMStringList.length --- {{APIRef("DOM")}}{{AvailableInWorkers}} The read-only **`length`** property indicates the number of strings in the {{domxref("DOMStringList")}}. ## Value A {{jsxref("Number")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}