Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md

Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md Path: mdn-web-docs > files > en-us > web > api > domstringmap > index.md --- title: DOMStringMap slug: Web/API/DOMStringMap page-type: web-api-interface browser-compat: api.DOMStringMap --- {{ APIRef("HTML DOM") }} The **`DOMStringMap`** interface is used for the {{domxref("HTMLElement.dataset")}} attribute, to represent data for custom attributes added to elements. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLElement.dataset")}} - [Global attributes - `data-*`](/en-US/docs/Web/HTML/Reference/Global_attributes/data-*)