Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md

Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > compositionstart_event > index.md --- title: "Element: compositionstart event" short-title: compositionstart slug: Web/API/Element/compositionstart_event page-type: web-api-event browser-compat: api.Element.compositionstart_event --- {{APIRef}} The **`compositionstart`** event is fired when a text composition system such as an {{glossary("input method editor")}} starts a new composition session. For example, this event could be fired after a user starts entering a Chinese character using a [Pinyin](https://en.wikipedia.org/wiki/Pinyin) {{glossary("Input method editor")}}. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("compositionstart", (event) => { }) oncompositionstart = (event) => { } ``` ## Event type A {{domxref("CompositionEvent")}}. Inherits from {{domxref("UIEvent")}} and {{domxref("Event")}}. {{InheritanceDiagram("CompositionEvent")}} ## Event properties _This interface also inherits properties of its parent, {{domxref("UIEvent")}}, and its ancestor {{domxref("Event")}}._ - {{domxref("CompositionEvent.data")}} {{ReadOnlyInline}} - : Returns the characters generated by the input method that raised the event; its varies depending on the type of event that generated the `CompositionEvent` object. - {{domxref("CompositionEvent.locale")}} {{ReadOnlyInline}} {{deprecated_inline}} - : Returns the locale of current input method (for example, the keyboard layout locale if the composition is associated with IME). ## Examples ```js const inputElement = document.querySelector('input[type="text"]'); inputElement.addEventListener("compositionstart", (event) => { console.log(`generated characters were: ${event.data}`); }); ``` ### Live example #### HTML ```html <div class="control"> <label for="example"> Focus the text-input control, then open your IME and begin typing. </label> <input type="text" id="example" name="example" /> </div> <div class="event-log"> <label for="eventLog">Event log:</label> <textarea readonly class="event-log-contents" rows="8" cols="25" id="eventLog"></textarea> <button class="clear-log">Clear</button> </div> ``` ```css hidden body { padding: 0.2rem; display: grid; grid-template-areas: "control log"; } .control { grid-area: control; } .event-log { grid-area: log; } .event-log-contents { resize: none; } label, button { display: block; } input[type="text"] { margin: 0.5rem 0; } kbd { border-radius: 3px; padding: 1px 2px 0; border: 1px solid black; } ``` #### JavaScript ```js const inputElement = document.querySelector('input[type="text"]'); const log = document.querySelector(".event-log-contents"); const clearLog = document.querySelector(".clear-log"); clearLog.addEventListener("click", () => { log.textContent = ""; }); function handleEvent(event) { log.textContent += `${event.type}: ${event.data}\n`; } inputElement.addEventListener("compositionstart", handleEvent); inputElement.addEventListener("compositionupdate", handleEvent); inputElement.addEventListener("compositionend", handleEvent); ``` #### Result {{ EmbedLiveSample('Live_example', '100%', '180px') }} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Related events: {{domxref("Element/compositionend_event", "compositionend")}}, {{domxref("Element/compositionupdate_event", "compositionupdate")}}.