Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md

Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mouseenter_event > index.md --- title: "Element: mouseenter event" short-title: mouseenter slug: Web/API/Element/mouseenter_event page-type: web-api-event browser-compat: api.Element.mouseenter_event --- {{APIRef}} The **`mouseenter`** event is fired at an {{domxref("Element")}} when a pointing device (usually a mouse) is initially moved so that its hotspot is within the element at which the event was fired. Note that "moving into an element" refers to the element's position in the DOM tree, not to its visual position. For example, if a child element is positioned so it is placed outside its parent, then moving into the child element will trigger `mouseenter` on the parent element, even though the pointer is still outside the bounds of the parent element. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("mouseenter", (event) => { }) onmouseenter = (event) => { } ``` ## Event type A {{domxref("MouseEvent")}}. Inherits from {{domxref("UIEvent")}} and {{domxref("Event")}}. {{InheritanceDiagram("MouseEvent")}} ## Event properties _This interface also inherits properties of its parents, {{domxref("UIEvent")}} and {{domxref("Event")}}._ - {{domxref("MouseEvent.altKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>alt</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.button")}} {{ReadOnlyInline}} - : The button number that was pressed (if applicable) when the mouse event was fired. - {{domxref("MouseEvent.buttons")}} {{ReadOnlyInline}} - : The buttons being pressed (if any) when the mouse event was fired. - {{domxref("MouseEvent.clientX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer in [viewport coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#viewport). - {{domxref("MouseEvent.clientY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer in [viewport coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#viewport). - {{domxref("MouseEvent.ctrlKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>control</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.layerX")}} {{Non-standard_inline}} {{ReadOnlyInline}} - : Returns the horizontal coordinate of the event relative to the current layer. - {{domxref("MouseEvent.layerY")}} {{Non-standard_inline}} {{ReadOnlyInline}} - : Returns the vertical coordinate of the event relative to the current layer. - {{domxref("MouseEvent.metaKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>meta</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.movementX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the position of the last {{domxref("Element/mousemove_event", "mousemove")}} event. - {{domxref("MouseEvent.movementY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the position of the last {{domxref("Element/mousemove_event", "mousemove")}} event. - {{domxref("MouseEvent.offsetX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the position of the padding edge of the target node. - {{domxref("MouseEvent.offsetY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the position of the padding edge of the target node. - {{domxref("MouseEvent.pageX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the whole document. - {{domxref("MouseEvent.pageY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the whole document. - {{domxref("MouseEvent.relatedTarget")}} {{ReadOnlyInline}} - : The secondary target for the event, if there is one. - {{domxref("MouseEvent.screenX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer in [screen coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#screen). - {{domxref("MouseEvent.screenY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer in [screen coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#screen). - {{domxref("MouseEvent.shiftKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>shift</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.mozInputSource")}} {{non-standard_inline()}} {{ReadOnlyInline}} - : The type of device that generated the event (one of the `MOZ_SOURCE_*` constants). This lets you, for example, determine whether a mouse event was generated by an actual mouse or by a touch event (which might affect the degree of accuracy with which you interpret the coordinates associated with the event). - {{domxref("MouseEvent.webkitForce")}} {{non-standard_inline()}} {{ReadOnlyInline}} - : The amount of pressure applied when clicking. - {{domxref("MouseEvent.x")}} {{ReadOnlyInline}} - : Alias for {{domxref("MouseEvent.clientX")}}. - {{domxref("MouseEvent.y")}} {{ReadOnlyInline}} - : Alias for {{domxref("MouseEvent.clientY")}}. ## Usage notes Though similar to {{domxref("Element/mouseover_event", "mouseover")}}, `mouseenter` differs in that it doesn't [bubble](/en-US/docs/Web/API/Event/bubbles) and it isn't sent to any descendants when the pointer is moved from one of its descendants' physical space to its own physical space. Other than that, enter and over events for the same situation are dispatched at the same time, if appropriate. ### Behavior of `mouseenter` events This describes the mouseenter events received by each of four concentric divs with no padding or margin, so the events all happen at the same time: ![Mouseenter behavior diagram](mouseenter.png) One `mouseenter` event is sent to each element of the hierarchy when entering them. Here 4 events are sent to the four elements of the hierarchy when the pointer reaches the text. ### Behavior of `mouseover` events ![Mouseover behavior diagram](mouseover.png) A single `mouseover` event is sent to the deepest element of the DOM tree, then it bubbles up the hierarchy until it is canceled by a handler or reaches the root. With deep hierarchies, the number of `mouseenter` events sent can be quite huge and cause significant performance problems. In such cases, it is better to listen for `mouseover` events. Combined with the corresponding `mouseleave` (which is fired at the element when the mouse exits its content area), the `mouseenter` event acts in a very similar way to the CSS {{cssxref(':hover')}} pseudo-class. ## Examples The [`mouseover`](/en-US/docs/Web/API/Element/mouseover_event#examples) documentation has an example illustrating the difference between `mouseover` and `mouseenter`. ### mouseenter The following trivial example uses the `mouseenter` event to change the border on the `div` when the mouse enters the space allotted to it. It then adds an item to the list with the number of the `mouseenter` or `mouseleave` event. #### HTML ```html <div id="mouseTarget"> <ul id="unorderedList"> <li>No events yet!</li> </ul> </div> ``` #### CSS Styling the `div` to make it more visible. ```css #mouseTarget { box-sizing: border-box; width: 15rem; border: 1px solid #333; } ``` #### JavaScript ```js let enterEventCount = 0; let leaveEventCount = 0; const mouseTarget = document.getElementById("mouseTarget"); const unorderedList = document.getElementById("unorderedList"); mouseTarget.addEventListener("mouseenter", (e) => { mouseTarget.style.border = "5px dotted orange"; enterEventCount++; addListItem(`This is mouseenter event ${enterEventCount}.`); }); mouseTarget.addEventListener("mouseleave", (e) => { mouseTarget.style.border = "1px solid #333"; leaveEventCount++; addListItem(`This is mouseleave event ${leaveEventCount}.`); }); function addListItem(text) { // Create a new text node using the supplied text const newTextNode = document.createTextNode(text); // Create a new li element const newListItem = document.createElement("li"); // Add the text node to the li element newListItem.appendChild(newTextNode); // Add the newly created list item to list unorderedList.appendChild(newListItem); } ``` ### Result {{EmbedLiveSample('mouseenter')}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Learn: Introduction to events](/en-US/docs/Learn_web_development/Core/Scripting/Events) - {{domxref("Element/mousedown_event", "mousedown")}} - {{domxref("Element/mouseup_event", "mouseup")}} - {{domxref("Element/mousemove_event", "mousemove")}} - {{domxref("Element/click_event", "click")}} - {{domxref("Element/dblclick_event", "dblclick")}} - {{domxref("Element/mouseover_event", "mouseover")}} - {{domxref("Element/mouseout_event", "mouseout")}} - {{domxref("Element/mouseleave_event", "mouseleave")}} - {{domxref("Element/contextmenu_event", "contextmenu")}} - {{domxref("Element/pointerenter_event", "pointerenter")}}