Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md

Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > mousemove_event > index.md --- title: "Element: mousemove event" short-title: mousemove slug: Web/API/Element/mousemove_event page-type: web-api-event browser-compat: api.Element.mousemove_event --- {{APIRef}} The `mousemove` event is fired at an element when a pointing device (usually a mouse) is moved while the cursor's hotspot is inside it. These events happen whether or not any mouse buttons are pressed. They can fire at a very high rate, depends on how fast the user moves the mouse, how fast the machine is, what other tasks and processes are happening, etc. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("mousemove", (event) => { }) onmousemove = (event) => { } ``` ## Event type A {{domxref("MouseEvent")}}. Inherits from {{domxref("UIEvent")}} and {{domxref("Event")}}. {{InheritanceDiagram("MouseEvent")}} ## Event properties _This interface also inherits properties of its parents, {{domxref("UIEvent")}} and {{domxref("Event")}}._ - {{domxref("MouseEvent.altKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>alt</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.button")}} {{ReadOnlyInline}} - : The button number that was pressed (if applicable) when the mouse event was fired. - {{domxref("MouseEvent.buttons")}} {{ReadOnlyInline}} - : The buttons being pressed (if any) when the mouse event was fired. - {{domxref("MouseEvent.clientX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer in [viewport coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#viewport). - {{domxref("MouseEvent.clientY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer in [viewport coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#viewport). - {{domxref("MouseEvent.ctrlKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>control</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.layerX")}} {{Non-standard_inline}} {{ReadOnlyInline}} - : Returns the horizontal coordinate of the event relative to the current layer. - {{domxref("MouseEvent.layerY")}} {{Non-standard_inline}} {{ReadOnlyInline}} - : Returns the vertical coordinate of the event relative to the current layer. - {{domxref("MouseEvent.metaKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>meta</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.movementX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the position of the last `mousemove` event. - {{domxref("MouseEvent.movementY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the position of the last `mousemove` event. - {{domxref("MouseEvent.offsetX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the position of the padding edge of the target node. - {{domxref("MouseEvent.offsetY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the position of the padding edge of the target node. - {{domxref("MouseEvent.pageX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer relative to the whole document. - {{domxref("MouseEvent.pageY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer relative to the whole document. - {{domxref("MouseEvent.relatedTarget")}} {{ReadOnlyInline}} - : The secondary target for the event, if there is one. - {{domxref("MouseEvent.screenX")}} {{ReadOnlyInline}} - : The X coordinate of the mouse pointer in [screen coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#screen). - {{domxref("MouseEvent.screenY")}} {{ReadOnlyInline}} - : The Y coordinate of the mouse pointer in [screen coordinates](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems#screen). - {{domxref("MouseEvent.shiftKey")}} {{ReadOnlyInline}} - : Returns `true` if the <kbd>shift</kbd> key was down when the mouse event was fired. - {{domxref("MouseEvent.mozInputSource")}} {{non-standard_inline()}} {{ReadOnlyInline}} - : The type of device that generated the event (one of the `MOZ_SOURCE_*` constants). This lets you, for example, determine whether a mouse event was generated by an actual mouse or by a touch event (which might affect the degree of accuracy with which you interpret the coordinates associated with the event). - {{domxref("MouseEvent.webkitForce")}} {{non-standard_inline()}} {{ReadOnlyInline}} - : The amount of pressure applied when clicking. - {{domxref("MouseEvent.x")}} {{ReadOnlyInline}} - : Alias for {{domxref("MouseEvent.clientX")}}. - {{domxref("MouseEvent.y")}} {{ReadOnlyInline}} - : Alias for {{domxref("MouseEvent.clientY")}}. ## Examples The following example uses the {{domxref("Element/mousedown_event", "mousedown")}}, `mousemove`, and {{domxref("Element/mouseup_event", "mouseup")}} events to allow the user to draw on an HTML [canvas](/en-US/docs/Web/API/Canvas_API). Its functionality is simple: the thickness of the line is set to 1, and the color is always black. When the page loads, constants `myPics` and `context` are created to store a reference to the canvas and the 2d context we will use to draw. Drawing begins when the `mousedown` event fires. First we store the x and y coordinates of the mouse pointer in the variables `x` and `y`, and then set `isDrawing` to true. As the mouse moves over the page, the `mousemove` event fires. If `isDrawing` is true, the event handler calls the `drawLine` function to draw a line from the stored `x` and `y` values to the current location. When the `drawLine()` function returns, we adjust the coordinates and then save them in `x` and `y`. The `mouseup` event draws the final line segment, sets `x` and `y` to `0`, and stops further drawing by setting `isDrawing` to `false`. ### HTML ```html <h1>Drawing with mouse events</h1> <canvas id="myPics" width="560" height="360"></canvas> ``` ### CSS ```css canvas { border: 1px solid black; width: 560px; height: 360px; } ``` ### JavaScript ```js // When true, moving the mouse draws on the canvas let isDrawing = false; let x = 0; let y = 0; const myPics = document.getElementById("myPics"); const context = myPics.getContext("2d"); // event.offsetX, event.offsetY gives the (x,y) offset from the edge of the canvas. // Add the event listeners for mousedown, mousemove, and mouseup myPics.addEventListener("mousedown", (e) => { x = e.offsetX; y = e.offsetY; isDrawing = true; }); myPics.addEventListener("mousemove", (e) => { if (isDrawing) { drawLine(context, x, y, e.offsetX, e.offsetY); x = e.offsetX; y = e.offsetY; } }); window.addEventListener("mouseup", (e) => { if (isDrawing) { drawLine(context, x, y, e.offsetX, e.offsetY); x = 0; y = 0; isDrawing = false; } }); function drawLine(context, x1, y1, x2, y2) { context.beginPath(); context.strokeStyle = "black"; context.lineWidth = 1; context.moveTo(x1, y1); context.lineTo(x2, y2); context.stroke(); context.closePath(); } ``` ### Result {{EmbedLiveSample("Examples", 640, 450)}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Learn: Introduction to events](/en-US/docs/Learn_web_development/Core/Scripting/Events) - {{domxref("Element/mousedown_event", "mousedown")}} - {{domxref("Element/mouseup_event", "mouseup")}} - {{domxref("Element/click_event", "click")}} - {{domxref("Element/dblclick_event", "dblclick")}} - {{domxref("Element/mouseover_event", "mouseover")}} - {{domxref("Element/mouseout_event", "mouseout")}} - {{domxref("Element/mouseenter_event", "mouseenter")}} - {{domxref("Element/mouseleave_event", "mouseleave")}} - {{domxref("Element/contextmenu_event", "contextmenu")}} - {{domxref("Element/pointermove_event", "pointermove")}}