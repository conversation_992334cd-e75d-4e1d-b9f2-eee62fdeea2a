Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md

Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md Path: mdn-web-docs > files > en-us > web > api > element > pointermove_event > index.md --- title: "Element: pointermove event" short-title: pointermove slug: Web/API/Element/pointermove_event page-type: web-api-event browser-compat: api.Element.pointermove_event --- {{APIRef}} The `pointermove` event is fired when a pointer changes coordinates, and the pointer has not been [canceled](/en-US/docs/Web/API/Element/pointercancel_event) by a browser [touch-action](/en-US/docs/Web/CSS/touch-action). It's very similar to the {{domxref("Element/mousemove_event", "mousemove")}} event, but with more features. These events happen whether or not any pointer buttons are pressed. They can fire at a very high rate, depends on how fast the user moves the pointer, how fast the machine is, what other tasks and processes are happening, etc. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("pointermove", (event) => { }) onpointermove = (event) => { } ``` ## Event type A {{domxref("PointerEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("PointerEvent")}} ## Event properties _This interface inherits properties from {{domxref("MouseEvent")}} and {{domxref("Event")}}._ - {{domxref('PointerEvent.altitudeAngle')}} {{ReadOnlyInline}} {{experimental_inline}} - : Represents the angle between a transducer (a pointer or stylus) axis and the X-Y plane of a device screen. - {{domxref('PointerEvent.azimuthAngle')}} {{ReadOnlyInline}} {{experimental_inline}} - : Represents the angle between the Y-Z plane and the plane containing both the transducer (a pointer or stylus) axis and the Y axis. - {{domxref('PointerEvent.persistentDeviceId')}} {{ReadOnlyInline}} {{experimental_inline}} - : A unique identifier for the pointing device generating the `PointerEvent`. - {{domxref('PointerEvent.pointerId')}} {{ReadOnlyInline}} - : A unique identifier for the pointer causing the event. - {{domxref('PointerEvent.width')}} {{ReadOnlyInline}} - : The width (magnitude on the X axis), in CSS pixels, of the contact geometry of the pointer. - {{domxref('PointerEvent.height')}} {{ReadOnlyInline}} - : The height (magnitude on the Y axis), in CSS pixels, of the contact geometry of the pointer. - {{domxref('PointerEvent.pressure')}} {{ReadOnlyInline}} - : The normalized pressure of the pointer input in the range `0` to `1`, where `0` and `1` represent the minimum and maximum pressure the hardware is capable of detecting, respectively. - {{domxref('PointerEvent.tangentialPressure')}} {{ReadOnlyInline}} - : The normalized tangential pressure of the pointer input (also known as barrel pressure or [cylinder stress](https://en.wikipedia.org/wiki/Cylinder_stress)) in the range `-1` to `1`, where `0` is the neutral position of the control. - {{domxref('PointerEvent.tiltX')}} {{ReadOnlyInline}} - : The plane angle (in degrees, in the range of `-90` to `90`) between the Y Z plane and the plane containing both the pointer (e.g., pen stylus) axis and the Y axis. - {{domxref('PointerEvent.tiltY')}} {{ReadOnlyInline}} - : The plane angle (in degrees, in the range of `-90` to `90`) between the X Z plane and the plane containing both the pointer (e.g., pen stylus) axis and the X axis. - {{domxref('PointerEvent.twist')}} {{ReadOnlyInline}} - : The clockwise rotation of the pointer (e.g., pen stylus) around its major axis in degrees, with a value in the range `0` to `359`. - {{domxref('PointerEvent.pointerType')}} {{ReadOnlyInline}} - : Indicates the device type that caused the event (mouse, pen, touch, etc.). - {{domxref('PointerEvent.isPrimary')}} {{ReadOnlyInline}} - : Indicates if the pointer represents the primary pointer of this pointer type. ## Usage notes The event, which is of type {{domxref("PointerEvent")}}, provides all the information you need to know about the user's interaction with the pointing device, including the position, movement distance, button states, and much more. ## Examples To add a handler for `pointermove` events using {{domxref("EventTarget.addEventListener", "addEventListener()")}}: ```js const para = document.querySelector("p"); para.addEventListener("pointermove", (event) => { console.log("Pointer moved"); }); ``` You can also use the `onpointermove` event handler property: ```js const para = document.querySelector("p"); para.onpointermove = (event) => { console.log("Pointer moved"); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Related events - {{domxref('Element/gotpointercapture_event', 'gotpointercapture')}} - {{domxref('Element/lostpointercapture_event', 'lostpointercapture')}} - {{domxref('Element/pointerover_event', 'pointerover')}} - {{domxref('Element/pointerenter_event', 'pointerenter')}} - {{domxref('Element/pointerdown_event', 'pointerdown')}} - {{domxref('Element/pointerup_event', 'pointerup')}} - {{domxref('Element/pointercancel_event', 'pointercancel')}} - {{domxref('Element/pointerout_event', 'pointerout')}} - {{domxref('Element/pointerleave_event', 'pointerleave')}} - {{domxref('Element/pointerrawupdate_event', 'pointerrawupdate')}} - {{domxref("Element/mousemove_event", "mousemove")}}