Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md

Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrollleftmax > index.md --- title: "Element: scrollLeftMax property" short-title: scrollLeftMax slug: Web/API/Element/scrollLeftMax page-type: web-api-instance-property status: - non-standard browser-compat: api.Element.scrollLeftMax --- {{APIRef("DOM")}}{{Non-standard_header}} The **`Element.scrollLeftMax`** read-only property returns a number representing the maximum left scroll offset possible for the element. ## Value A number. ## Specifications _This property is not part of any specification._ ## Browser compatibility {{Compat}} ## See also - {{domxref("Element.scrollTopMax")}} giving the same information for the other axis.