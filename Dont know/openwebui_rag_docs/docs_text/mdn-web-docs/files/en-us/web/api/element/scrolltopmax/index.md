Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md

Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md Path: mdn-web-docs > files > en-us > web > api > element > scrolltopmax > index.md --- title: "Element: scrollTopMax property" short-title: scrollTopMax slug: Web/API/Element/scrollTopMax page-type: web-api-instance-property status: - non-standard browser-compat: api.Element.scrollTopMax --- {{APIRef("DOM")}}{{Non-standard_header}} The **`Element.scrollTopMax`** read-only property returns a number representing the maximum top scroll offset possible for the element. ## Value A number. ## Specifications _This property is not part of any specification._ ## Browser compatibility {{Compat}} ## See also - {{domxref("Element.scrollLeftMax")}} giving the same information for the other axis.