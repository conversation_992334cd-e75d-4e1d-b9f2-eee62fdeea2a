Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariaactivedescendantelement > index.md

Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariaactivedescendantelement > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariaactivedescendantelement > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariaactivedescendantelement > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariaactivedescendantelement > index.md --- title: "Element: ariaActiveDescendantElement property" short-title: ariaActiveDescendantElement slug: Web/API/ElementInternals/ariaActiveDescendantElement page-type: web-api-instance-property browser-compat: api.ElementInternals.ariaActiveDescendantElement --- {{APIRef("DOM")}} The **`ariaActiveDescendantElement`** property of the {{domxref("ElementInternals")}} interface represents the current active element when focus is on a [`composite`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/composite_role) widget, [`combobox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/combobox_role), [`textbox`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/textbox_role), [`group`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/group_role), or [`application`](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/application_role). The [`aria-activedescendant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-activedescendant) topic contains additional information about how the attribute and property should be used. ## Value An subclass of {{domxref("HTMLElement")}} that represents the active descendant, or `null` if there is no active descendant. ## Description The property is a flexible alternative to using the [`aria-activedescendant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-activedescendant) attribute. Unlike `aria-activedescendant`, the element assigned to this property does not have to have an [`id`](/en-US/docs/Web/HTML/Reference/Global_attributes/id) attribute. The property reflects the element's [`aria-activedescendant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-errormessage) attribute when it is defined, but only for reference `id` values that match valid in-scope elements. If the property is set, then the corresponding attribute is cleared. For more information about reflected element references and scope see [Reflected element references](/en-US/docs/Web/API/Document_Object_Model/Reflected_attributes#reflected_element_references) in the _Reflected attributes_ guide. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [`aria-activedescendant`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-activedescendant) attribute - {{domxref("Element.ariaActiveDescendantElement")}} - [Reflected element references](/en-US/docs/Web/API/Document_Object_Model/Reflected_attributes#reflected_element_references) in the _Attribute reflection_ guide.