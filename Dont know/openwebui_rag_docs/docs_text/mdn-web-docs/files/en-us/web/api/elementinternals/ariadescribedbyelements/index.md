Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariadescribedbyelements > index.md

Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariadescribedbyelements > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariadescribedbyelements > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariadescribedbyelements > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariadescribedbyelements > index.md --- title: "ElementInternals: ariaDescribedByElements property" short-title: ariaDescribedByElements slug: Web/API/ElementInternals/ariaDescribedByElements page-type: web-api-instance-property browser-compat: api.ElementInternals.ariaDescribedByElements --- {{APIRef("DOM")}} The **`ariaDescribedByElements`** property of the {{domxref("ElementInternals")}} interface is an array containing the element (or elements) that provide an accessible description for the element it is applied to. The accessible description is similar to the accessible label (see {{domxref("Element/ariaLabelledByElements","ariaLabelledByElements")}}), but provides more verbose information. The [`aria-describedby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-describedby) topic contains additional information about how the attribute and property should be used. ## Value An array of subclasses of {{domxref("HTMLElement")}}. The inner text of these elements can be joined with spaces to get the accessible description. When read, the returned array is a static and read-only. When written, the assigned array is copied: subsequent changes to the array do not affect the value of the property. ## Description The property is a flexible alternative to using the [`aria-describedby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-describedby) attribute to set the accessible description. Unlike `aria-describedby`, the elements assigned to this property do not have to have an [`id`](/en-US/docs/Web/HTML/Reference/Global_attributes/id) attribute. The property reflects the element's [`aria-describedby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-describedby) attribute when it is defined, but only for listed reference `id` values that match valid in-scope elements. If the property is set, then the corresponding attribute is cleared. For more information about reflected element references and scope see [Reflected element references](/en-US/docs/Web/API/Document_Object_Model/Reflected_attributes#reflected_element_references) in the _Reflected attributes_ guide. ## Examples The examples in the following documents are relevant: - {{domxref("Element.ariaDescribedByElements")}} is the DOM equivalent of this property. It is used in the same way, but within the DOM instead of a shadow DOM and/or custom element. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [`aria-describedby`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-describedby) attribute - {{domxref("Element.ariaDescribedByElements")}} - [Reflected element references](/en-US/docs/Web/API/Document_Object_Model/Reflected_attributes#reflected_element_references) in the _Attribute reflection_ guide