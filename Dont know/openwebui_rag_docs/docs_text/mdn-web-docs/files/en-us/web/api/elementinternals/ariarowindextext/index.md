Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariarowindextext > index.md

Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariarowindextext > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariarowindextext > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariarowindextext > index.md Path: mdn-web-docs > files > en-us > web > api > elementinternals > ariarowindextext > index.md --- title: ElementInternals.ariaRowIndexText slug: Web/API/ElementInternals/ariaRowIndexText page-type: web-api-instance-property browser-compat: api.ElementInternals.ariaRowIndexText --- {{APIRef("Web Components")}} The **`ariaRowIndexText`** property of the {{domxref("ElementInternals")}} interface reflects the value of the [`aria-rowindextext`](/en-US/docs/Web/Accessibility/ARIA/Reference/Attributes/aria-rowindextext) attribute, which defines a human readable text alternative of aria-rowindex. > [!NOTE] > Setting aria attributes on `ElementInternals` allows default semantics to be defined on a custom element. These may be overwritten by author-defined attributes, but ensure that default semantics are retained should the author delete those attributes, or fail to add them at all. For more information see the [Accessibility Object Model explainer](https://wicg.github.io/aom/explainer.html#default-semantics-for-custom-elements-via-the-elementinternals-object). ## Value A string. ## Examples In this example the value of `ariaRowIndexText` is set to "Heading row". ```js class CustomEl extends HTMLElement { constructor() { super(); this.internals_ = this.attachInternals(); this.internals_.ariaRowIndexText = "Heading row"; } // } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [ARIA: table role](/en-US/docs/Web/Accessibility/ARIA/Reference/Roles/table_role)