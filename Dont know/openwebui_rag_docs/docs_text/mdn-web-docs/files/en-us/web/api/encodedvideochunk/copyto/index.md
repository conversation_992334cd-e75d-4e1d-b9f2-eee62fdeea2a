Path: mdn-web-docs > files > en-us > web > api > encodedvideochunk > copyto > index.md

Path: mdn-web-docs > files > en-us > web > api > encodedvideochunk > copyto > index.md Path: mdn-web-docs > files > en-us > web > api > encodedvideochunk > copyto > index.md Path: mdn-web-docs > files > en-us > web > api > encodedvideochunk > copyto > index.md Path: mdn-web-docs > files > en-us > web > api > encodedvideochunk > copyto > index.md --- title: "EncodedVideoChunk: copyTo() method" short-title: copyTo() slug: Web/API/EncodedVideoChunk/copyTo page-type: web-api-instance-method browser-compat: api.EncodedVideoChunk.copyTo --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`copyTo()`** method of the {{domxref("EncodedVideoChunk")}} interface copies the encoded chunk of video data. ## Syntax ```js-nolint copyTo(destination) ``` ### Parameters - `destination` - : An {{jsxref("ArrayBuffer")}}, a {{jsxref("TypedArray")}}, or a {{jsxref("DataView")}} that the data can be copied to. ### Return value None ({{jsxref("undefined")}}). ## Examples In the following example an {{domxref("EncodedVideoChunk")}} is created then copied. ```js const init = { type: "key", data: videoBuffer, timestamp: 23000000, duration: 2000000, }; const chunk = new EncodedVideoChunk(init); chunk.copyTo(newBuffer); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}