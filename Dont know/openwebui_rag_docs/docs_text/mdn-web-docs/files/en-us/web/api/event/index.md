Path: mdn-web-docs > files > en-us > web > api > event > index.md

Path: mdn-web-docs > files > en-us > web > api > event > index.md Path: mdn-web-docs > files > en-us > web > api > event > index.md Path: mdn-web-docs > files > en-us > web > api > event > index.md Path: mdn-web-docs > files > en-us > web > api > event > index.md --- title: Event slug: Web/API/Event page-type: web-api-interface browser-compat: api.Event --- {{APIRef("DOM")}}{{AvailableInWorkers}} The **`Event`** interface represents an event which takes place on an [`EventTarget`](/en-US/docs/Web/API/EventTarget). An event can be triggered by the user action e.g., clicking the mouse button or tapping keyboard, or generated by APIs to represent the progress of an asynchronous task. It can also be triggered programmatically, such as by calling the [`HTMLElement.click()`](/en-US/docs/Web/API/HTMLElement/click) method of an element, or by defining the event, then sending it to a specified target using [`EventTarget.dispatchEvent()`](/en-US/docs/Web/API/EventTarget/dispatchEvent). There are many types of events, some of which use other interfaces based on the main `Event` interface. `Event` itself contains the properties and methods which are common to all events. Many DOM elements can be set up to accept (or "listen" for) these events, and execute code in response to process (or "handle") them. Event-handlers are usually connected (or "attached") to various [HTML elements](/en-US/docs/Web/HTML/Reference/Elements) (such as `<button>`, `<div>`, `<span>`, etc.) using [`EventTarget.addEventListener()`](/en-US/docs/Web/API/EventTarget/addEventListener), and this generally replaces using the old HTML [event handler attributes](/en-US/docs/Web/HTML/Reference/Global_attributes). Further, when properly added, such handlers can also be disconnected if needed using [`removeEventListener()`](/en-US/docs/Web/API/EventTarget/removeEventListener). > [!NOTE] > One element can have several such handlers, even for the exact same event particularly if separate, independent code modules attach them, each for its own independent purposes. (For example, a webpage with an advertising-module and statistics-module both monitoring video-watching.) When there are many nested elements, each with its own handler(s), event processing can become very complicated especially where a parent element receives the very same event as its child elements because "spatially" they overlap so the event technically occurs in both, and the processing order of such events depends on the [Event bubbling](/en-US/docs/Learn_web_development/Core/Scripting/Event_bubbling) settings of each handler triggered. ## Interfaces based on Event Below is a list of interfaces which are based on the main `Event` interface, with links to their respective documentation in the MDN API reference. Note that all event interfaces have names which end in "Event". - {{domxref("AnimationEvent")}} - {{domxref("AudioProcessingEvent")}} {{Deprecated_Inline}} - {{domxref("BeforeUnloadEvent")}} - {{domxref("BlobEvent")}} - {{domxref("ClipboardEvent")}} - {{domxref("CloseEvent")}} - {{domxref("CompositionEvent")}} - {{domxref("CustomEvent")}} - {{domxref("DeviceMotionEvent")}} - {{domxref("DeviceOrientationEvent")}} - {{domxref("DragEvent")}} - {{domxref("ErrorEvent")}} - {{domxref("FetchEvent")}} - {{domxref("FocusEvent")}} - {{domxref("FontFaceSetLoadEvent")}} - {{domxref("FormDataEvent")}} - {{domxref("GamepadEvent")}} - {{domxref("HashChangeEvent")}} - {{domxref("HIDInputReportEvent")}} - {{domxref("IDBVersionChangeEvent")}} - {{domxref("InputEvent")}} - {{domxref("KeyboardEvent")}} - {{domxref("MediaStreamEvent")}} {{Deprecated_Inline}} - {{domxref("MessageEvent")}} - {{domxref("MouseEvent")}} - {{domxref("MutationEvent")}} {{Deprecated_Inline}} - {{domxref("OfflineAudioCompletionEvent")}} - {{domxref("PageTransitionEvent")}} - {{domxref("PaymentRequestUpdateEvent")}} - {{domxref("PointerEvent")}} - {{domxref("PopStateEvent")}} - {{domxref("ProgressEvent")}} - {{domxref("RTCDataChannelEvent")}} - {{domxref("RTCPeerConnectionIceEvent")}} - {{domxref("StorageEvent")}} - {{domxref("SubmitEvent")}} - {{domxref("TimeEvent")}} - {{domxref("TouchEvent")}} - {{domxref("TrackEvent")}} - {{domxref("TransitionEvent")}} - {{domxref("UIEvent")}} - {{domxref("WebGLContextEvent")}} - {{domxref("WheelEvent")}} ## Constructor - {{domxref("Event.Event", "Event()")}} - : Creates an `Event` object, returning it to the caller. ## Instance properties - {{domxref("Event.bubbles")}} {{ReadOnlyInline}} - : A boolean value indicating whether or not the event bubbles up through the DOM. - {{domxref("Event.cancelable")}} {{ReadOnlyInline}} - : A boolean value indicating whether the event is cancelable. - {{domxref("Event.composed")}} {{ReadOnlyInline}} - : A boolean indicating whether or not the event can bubble across the boundary between the shadow DOM and the regular DOM. - {{domxref("Event.currentTarget")}} {{ReadOnlyInline}} - : A reference to the currently registered target for the event. This is the object to which the event is currently slated to be sent. It's possible this has been changed along the way through _retargeting_. - {{domxref("Event.defaultPrevented")}} {{ReadOnlyInline}} - : Indicates whether or not the call to {{domxref("event.preventDefault()")}} canceled the event. - {{domxref("Event.eventPhase")}} {{ReadOnlyInline}} - : Indicates which phase of the event flow is being processed. It is one of the following numbers: `NONE`, `CAPTURING_PHASE`, `AT_TARGET`, `BUBBLING_PHASE`. - {{domxref("Event.isTrusted")}} {{ReadOnlyInline}} - : Indicates whether or not the event was initiated by the browser (after a user click, for instance) or by a script (using an event creation method, for example). - {{domxref("Event.srcElement")}} {{ReadOnlyInline}} {{Deprecated_Inline}} - : An alias for the {{domxref("Event.target")}} property. Use {{domxref("Event.target")}} instead. - {{domxref("Event.target")}} {{ReadOnlyInline}} - : A reference to the object to which the event was originally dispatched. - {{domxref("Event.timeStamp")}} {{ReadOnlyInline}} - : The time at which the event was created (in milliseconds). By specification, this value is time since epoch but in reality, browsers' definitions vary. In addition, work is underway to change this to be a {{domxref("DOMHighResTimeStamp")}} instead. - {{domxref("Event.type")}} {{ReadOnlyInline}} - : The name identifying the type of the event. ### Legacy and non-standard properties - {{domxref("Event.cancelBubble")}} {{deprecated_inline}} - : A historical alias to {{domxref("Event.stopPropagation()")}} that should be used instead. Setting its value to `true` before returning from an event handler prevents propagation of the event. - {{domxref("Event.explicitOriginalTarget")}} {{non-standard_inline}} {{ReadOnlyInline}} - : The explicit original target of the event. - {{domxref("Event.originalTarget")}} {{non-standard_inline}} {{ReadOnlyInline}} - : The original target of the event, before any retargetings. - {{domxref("Event.returnValue")}} {{deprecated_inline}} - : A historical property still supported in order to ensure existing sites continue to work. Use {{domxref("Event.preventDefault()")}} and {{domxref("Event.defaultPrevented")}} instead. - {{domxref("Event.composed", "Event.scoped")}} {{ReadOnlyInline}} {{deprecated_inline}} - : A boolean value indicating whether the given event will bubble across through the shadow root into the standard DOM. Use {{domxref("Event.composed", "composed")}} instead. ## Instance methods - {{domxref("Event.composedPath()")}} - : Returns the event's path (an array of objects on which listeners will be invoked). This does not include nodes in shadow trees if the shadow root was created with its {{domxref("ShadowRoot.mode")}} closed. - {{domxref("Event.preventDefault()")}} - : Cancels the event (if it is cancelable). - {{domxref("Event.stopImmediatePropagation()")}} - : For this particular event, prevent all other listeners from being called. This includes listeners attached to the same element as well as those attached to elements that will be traversed later (during the capture phase, for instance). - {{domxref("Event.stopPropagation()")}} - : Stops the propagation of events further along in the DOM. ### Deprecated methods - {{domxref("Event.initEvent()")}} {{deprecated_inline}} - : Initializes the value of an Event created. If the event has already been dispatched, this method does nothing. Use the constructor ({{domxref("Event.Event", "Event()")}} instead). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Types of events available: [Event reference](/en-US/docs/Web/Events) - [Learn: Introduction to events](/en-US/docs/Learn_web_development/Core/Scripting/Events) - [Learn: Event bubbling](/en-US/docs/Learn_web_development/Core/Scripting/Event_bubbling) - [Creating and triggering custom events](/en-US/docs/Web/Events/Creating_and_triggering_events)