Path: mdn-web-docs > files > en-us > web > api > event > istrusted > index.md

Path: mdn-web-docs > files > en-us > web > api > event > istrusted > index.md Path: mdn-web-docs > files > en-us > web > api > event > istrusted > index.md Path: mdn-web-docs > files > en-us > web > api > event > istrusted > index.md Path: mdn-web-docs > files > en-us > web > api > event > istrusted > index.md --- title: "Event: isTrusted property" short-title: isTrusted slug: Web/API/Event/isTrusted page-type: web-api-instance-property browser-compat: api.Event.isTrusted --- {{APIRef("DOM")}}{{AvailableInWorkers}} The **`isTrusted`** read-only property of the {{domxref("Event")}} interface is a boolean value that is `true` when the event was generated by the user agent (including via user actions and programmatic methods such as {{domxref("HTMLElement.focus()")}}), and `false` when the event was dispatched via {{domxref("EventTarget.dispatchEvent()")}}. The only exception is the `click` event, which initializes the `isTrusted` property to `false` in user agents. ## Value A boolean value. ## Example ```js if (e.isTrusted) { /* The event is trusted */ } else { /* The event is not trusted */ } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}