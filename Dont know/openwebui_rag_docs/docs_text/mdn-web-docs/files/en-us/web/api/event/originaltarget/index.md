Path: mdn-web-docs > files > en-us > web > api > event > originaltarget > index.md

Path: mdn-web-docs > files > en-us > web > api > event > originaltarget > index.md Path: mdn-web-docs > files > en-us > web > api > event > originaltarget > index.md Path: mdn-web-docs > files > en-us > web > api > event > originaltarget > index.md Path: mdn-web-docs > files > en-us > web > api > event > originaltarget > index.md --- title: "Event: originalTarget property" short-title: originalTarget slug: Web/API/Event/originalTarget page-type: web-api-instance-property status: - non-standard browser-compat: api.Event.originalTarget --- {{APIRef("DOM")}}{{Non-standard_header}}{{AvailableInWorkers}} The read-only **`originalTarget`** property of the {{domxref("Event")}} interface returns the original target of the event before any retargetings. Unlike {{domxref("Event.explicitOriginalTarget")}} it can also be native anonymous content. ## Specifications _This is a Mozilla-specific property and is not part of any current specification. It is not on track to become a standard._ ## Browser compatibility {{Compat}}