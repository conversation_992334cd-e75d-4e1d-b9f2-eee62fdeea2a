Path: mdn-web-docs > files > en-us > web > api > event > srcelement > index.md

Path: mdn-web-docs > files > en-us > web > api > event > srcelement > index.md Path: mdn-web-docs > files > en-us > web > api > event > srcelement > index.md Path: mdn-web-docs > files > en-us > web > api > event > srcelement > index.md Path: mdn-web-docs > files > en-us > web > api > event > srcelement > index.md --- title: "Event: srcElement property" short-title: srcElement slug: Web/API/Event/srcElement page-type: web-api-instance-property status: - deprecated browser-compat: api.Event.srcElement --- {{APIRef("DOM")}}{{deprecated_header}}{{AvailableInWorkers}} The deprecated **`Event.srcElement`** is an alias for the {{domxref("Event.target")}} property. Use {{domxref("Event.target")}} instead. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Window.event")}}