Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md

Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > eventsource > message_event > index.md --- title: "EventSource: message event" short-title: message slug: Web/API/EventSource/message_event page-type: web-api-event browser-compat: api.EventSource.message_event --- {{APIRef("Server Sent Events")}}{{AvailableInWorkers}} The **`message`** event of the {{domxref("EventSource")}} interface is fired when data is received through an event source. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("message", (event) => { }) onmessage = (event) => { } ``` ## Event type A {{domxref("MessageEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("MessageEvent")}} ## Event properties _This interface also inherits properties from its parent, {{domxref("Event")}}._ - {{domxref("MessageEvent.data")}} {{ReadOnlyInline}} - : The data sent by the message emitter. - {{domxref("MessageEvent.origin")}} {{ReadOnlyInline}} - : A string representing the origin of the message emitter. - {{domxref("MessageEvent.lastEventId")}} {{ReadOnlyInline}} - : A string representing a unique ID for the event. - {{domxref("MessageEvent.source")}} {{ReadOnlyInline}} - : A `MessageEventSource` (which can be a {{glossary("WindowProxy")}}, {{domxref("MessagePort")}}, or {{domxref("ServiceWorker")}} object) representing the message emitter. - {{domxref("MessageEvent.ports")}} {{ReadOnlyInline}} - : An array of {{domxref("MessagePort")}} objects representing the ports associated with the channel the message is being sent through (where appropriate, e.g., in channel messaging or when sending a message to a shared worker). ## Examples In this basic example, an `EventSource` is created to receive events from the server; a page with the name `sse.php` is responsible for generating the events. ```js const evtSource = new EventSource("sse.php"); const eventList = document.querySelector("ul"); evtSource.addEventListener("message", (e) => { const newElement = document.createElement("li"); newElement.textContent = `message: ${e.data}`; eventList.appendChild(newElement); }); ``` ### onmessage equivalent ```js evtSource.onmessage = (e) => { const newElement = document.createElement("li"); newElement.textContent = `message: ${e.data}`; eventList.appendChild(newElement); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using server-sent events](/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events) - [`open`](/en-US/docs/Web/API/EventSource/open_event) - [`error`](/en-US/docs/Web/API/EventSource/error_event)