Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md

Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md Path: mdn-web-docs > files > en-us > web > api > ext_disjoint_timer_query > deletequeryext > index.md --- title: "EXT_disjoint_timer_query: deleteQueryEXT() method" short-title: deleteQueryEXT() slug: Web/API/EXT_disjoint_timer_query/deleteQueryEXT page-type: webgl-extension-method browser-compat: api.EXT_disjoint_timer_query.deleteQueryEXT --- {{APIRef("WebGL")}} The **`EXT_disjoint_timer_query.deleteQueryEXT()`** method of the [WebGL API](/en-US/docs/Web/API/WebGL_API) deletes a given {{domxref("WebGLQuery")}} object. ## Syntax ```js-nolint deleteQueryEXT(query) ``` ### Parameters - `query` - : A {{domxref("WebGLQuery")}} object to delete. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js const ext = gl.getExtension("EXT_disjoint_timer_query"); const query = ext.createQueryEXT(); // ext.deleteQueryEXT(query); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("WebGLRenderingContext.getExtension()")}} - {{domxref("WebGLQuery")}} - {{domxref("EXT_disjoint_timer_query")}}