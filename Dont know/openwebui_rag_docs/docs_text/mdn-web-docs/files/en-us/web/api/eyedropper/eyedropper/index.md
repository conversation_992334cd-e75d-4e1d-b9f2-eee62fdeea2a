Path: mdn-web-docs > files > en-us > web > api > eyedropper > eyedropper > index.md

Path: mdn-web-docs > files > en-us > web > api > eyedropper > eyedropper > index.md Path: mdn-web-docs > files > en-us > web > api > eyedropper > eyedropper > index.md Path: mdn-web-docs > files > en-us > web > api > eyedropper > eyedropper > index.md Path: mdn-web-docs > files > en-us > web > api > eyedropper > eyedropper > index.md --- title: "EyeDropper: EyeDropper() constructor" short-title: EyeDropper() slug: Web/API/EyeDropper/EyeDropper page-type: web-api-constructor status: - experimental browser-compat: api.EyeDropper.EyeDropper --- {{securecontext_header}}{{APIRef("EyeDropper API")}}{{SeeCompatTable}} The **`EyeDropper()`** constructor returns a new {{DOMxRef("EyeDropper")}} object. ## Syntax ```js-nolint new EyeDropper() ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{DOMxRef("EyeDropper")}} interface it belongs to.