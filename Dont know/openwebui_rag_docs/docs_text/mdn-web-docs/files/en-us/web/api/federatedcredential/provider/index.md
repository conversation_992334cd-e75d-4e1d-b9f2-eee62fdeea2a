Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md

Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md Path: mdn-web-docs > files > en-us > web > api > federatedcredential > provider > index.md --- title: "FederatedCredential: provider property" short-title: provider slug: Web/API/FederatedCredential/provider page-type: web-api-instance-property status: - experimental browser-compat: api.FederatedCredential.provider --- {{SeeCompatTable}}{{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`provider`** property of the {{domxref("FederatedCredential")}} interface returns a string containing a credential's federated identity provider. ## Value A string containing a credential's federated identity provider. ## Examples ```js // TBD ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}