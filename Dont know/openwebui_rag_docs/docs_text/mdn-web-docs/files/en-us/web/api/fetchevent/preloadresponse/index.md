Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md

Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md Path: mdn-web-docs > files > en-us > web > api > fetchevent > preloadresponse > index.md --- title: "FetchEvent: preloadResponse property" short-title: preloadResponse slug: Web/API/FetchEvent/preloadResponse page-type: web-api-instance-property browser-compat: api.FetchEvent.preloadResponse --- {{APIRef("Service Workers API")}}{{AvailableInWorkers("service")}} The **`preloadResponse`** read-only property of the {{domxref("FetchEvent")}} interface returns a {{jsxref("Promise")}} that resolves to the navigation preload {{domxref("Response")}} if [navigation preload](/en-US/docs/Web/API/NavigationPreloadManager) was triggered, or `undefined` otherwise. Navigation preload is triggered if [navigation preload is enabled](/en-US/docs/Web/API/NavigationPreloadManager/enable), the request is a `GET` request, and the request is a navigation request (generated by the browser when loading pages and iframes). A service worker can wait on this promise in its fetch event handler in order to track completion of a fetch request made during service-worker boot. ## Value A {{jsxref("Promise")}} that resolves to a {{domxref("Response")}} or otherwise to `undefined`. ## Examples This code snippet is from [Speed up Service Worker with Navigation Preloads](https://web.dev/blog/navigation-preload). The {{domxref("ServiceWorkerGlobalScope.fetch_event", "onfetch")}} event handler listens for the `fetch` event. When fired, the handler calls {{domxref("FetchEvent.respondWith", "FetchEvent.respondWith()")}} to pass a promise back to the controlled page. This promise will resolve with the requested resource. If there is a matching URL request in the {{domxref("Cache")}} object, then the code returns a promise for fetching the response from the cache. If no match is found in the cache, the code returns the promise in `preloadResponse`. If there is no matching cache or preloaded response, the code fetches the response from the network and returns the associated promise. ```js addEventListener("fetch", (event) => { event.respondWith( (async () => { // Respond from the cache if we can const cachedResponse = await caches.match(event.request); if (cachedResponse) return cachedResponse; // Else, use the preloaded response, if it's there const response = await event.preloadResponse; if (response) return response; // Else try the network. return fetch(event.request); })(), ); }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Speed up Service Worker with Navigation Preloads](https://web.dev/blog/navigation-preload) - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers) - [Service workers basic code example](https://github.com/mdn/dom-examples/tree/main/service-worker/simple-service-worker) - [Using web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers)