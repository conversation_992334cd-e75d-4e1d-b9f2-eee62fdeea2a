Path: mdn-web-docs > files > en-us > web > api > filereader > error > index.md

Path: mdn-web-docs > files > en-us > web > api > filereader > error > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error > index.md --- title: "FileReader: error property" short-title: error slug: Web/API/FileReader/error page-type: web-api-instance-property browser-compat: api.FileReader.error --- {{APIRef("File API")}}{{AvailableInWorkers}} The **`error`** read-only property of the {{domxref("FileReader")}} interface returns the error that occurred while reading the file. ## Value A {{domxref("DOMException")}} containing the relevant error. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("FileReader")}}