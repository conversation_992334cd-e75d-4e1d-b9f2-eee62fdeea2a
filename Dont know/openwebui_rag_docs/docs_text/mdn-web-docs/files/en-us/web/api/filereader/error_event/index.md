Path: mdn-web-docs > files > en-us > web > api > filereader > error_event > index.md

Path: mdn-web-docs > files > en-us > web > api > filereader > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > error_event > index.md --- title: "FileReader: error event" short-title: error slug: Web/API/FileReader/error_event page-type: web-api-event browser-compat: api.FileReader.error_event --- {{APIRef("File API")}}{{AvailableInWorkers}} The **`error`** event of the {{domxref("FileReader")}} interface is fired when the read failed due to an error (for example, because the file was not found or not readable). This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("error", (event) => { }) onerror = (event) => { } ``` ## Event type A {{domxref("ProgressEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("ProgressEvent")}} ## Event properties _Also inherits properties from its parent {{domxref("Event")}}_. - {{domxref("ProgressEvent.lengthComputable")}} {{ReadOnlyInline}} - : A boolean flag indicating if the total work to be done, and the amount of work already done, by the underlying process is calculable. In other words, it tells if the progress is measurable or not. - {{domxref("ProgressEvent.loaded")}} {{ReadOnlyInline}} - : A 64-bit unsigned integer value indicating the amount of work already performed by the underlying process. The ratio of work done can be calculated by dividing `total` by the value of this property. When downloading a resource using HTTP, this only counts the body of the HTTP message, and doesn't include headers and other overhead. - {{domxref("ProgressEvent.total")}} {{ReadOnlyInline}} - : A 64-bit unsigned integer representing the total amount of work that the underlying process is in the progress of performing. When downloading a resource using HTTP, this is the `Content-Length` (the size of the body of the message), and doesn't include the headers and other overhead. ## Examples ```js const fileInput = document.querySelector('input[type="file"]'); const reader = new FileReader(); function handleSelected(e) { const selectedFile = fileInput.files[0]; if (selectedFile) { reader.addEventListener("error", () => { console.error(`Error occurred reading file: ${selectedFile.name}`); }); reader.addEventListener("load", () => { console.log(`File: ${selectedFile.name} read successfully`); }); reader.readAsDataURL(selectedFile); } } fileInput.addEventListener("change", handleSelected); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Related events: {{domxref("FileReader.loadstart_event", "loadstart")}}, {{domxref("FileReader.loadend_event", "loadend")}}, {{domxref("FileReader.progress_event", "progress")}}, {{domxref("FileReader.load_event", "load")}}, {{domxref("FileReader.abort_event", "abort")}}