Path: mdn-web-docs > files > en-us > web > api > filereader > filereader > index.md

Path: mdn-web-docs > files > en-us > web > api > filereader > filereader > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > filereader > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > filereader > index.md Path: mdn-web-docs > files > en-us > web > api > filereader > filereader > index.md --- title: "FileReader: FileReader() constructor" short-title: FileReader() slug: Web/API/FileReader/FileReader page-type: web-api-constructor browser-compat: api.FileReader.FileReader --- {{APIRef("File API")}}{{AvailableInWorkers}} The **`FileReader()`** constructor creates a new `FileReader`. For details about how to use `FileReader`, see [Using files from web applications](/en-US/docs/Web/API/File_API/Using_files_from_web_applications). ## Syntax ```js-nolint new FileReader() ``` ### Parameters None. ## Examples The following code snippet shows creation of a {{domxref("FileReader")}} object using the `FileReader()` constructor and subsequent usage of the object: ```js function printFile(file) { const reader = new FileReader(); reader.onload = (evt) => { console.log(evt.target.result); }; reader.readAsText(file); } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using files from web applications](/en-US/docs/Web/API/File_API/Using_files_from_web_applications)