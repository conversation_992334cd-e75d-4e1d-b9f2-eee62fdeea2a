Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md

Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > name > index.md --- title: "FileSystem: name property" short-title: name slug: Web/API/FileSystem/name page-type: web-api-instance-property browser-compat: api.FileSystem.name --- {{APIRef("File and Directory Entries API")}} The read-only **`name`** property of the {{domxref("FileSystem")}} interface indicates the file system's name. This string is unique among all file systems currently exposed by the [File and Directory Entries API](/en-US/docs/Web/API/File_and_Directory_Entries_API). ## Value A string representing the file system's name. ## Examples ```js // tbd ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [File and Directory Entries API](/en-US/docs/Web/API/File_and_Directory_Entries_API) - {{domxref("FileSystem")}}