Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md

Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md Path: mdn-web-docs > files > en-us > web > api > filesystem > root > index.md --- title: "FileSystem: root property" short-title: root slug: Web/API/FileSystem/root page-type: web-api-instance-property browser-compat: api.FileSystem.root --- {{APIRef("File and Directory Entries API")}} The read-only **`root`** property of the {{domxref("FileSystem")}} interface specifies a {{domxref("FileSystemDirectoryEntry")}} object representing the root directory of the file system, for use with the [File and Directory Entries API](/en-US/docs/Web/API/File_and_Directory_Entries_API). ## Value A {{domxref("FileSystemDirectoryEntry")}} representing the file system's root directory. ## Examples ```js // tbd ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [File and Directory Entries API](/en-US/docs/Web/API/File_and_Directory_Entries_API) - {{domxref("FileSystem")}}