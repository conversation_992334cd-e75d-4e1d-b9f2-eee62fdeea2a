Path: mdn-web-docs > files > en-us > web > api > filesystemdirectoryhandle > index.md

Path: mdn-web-docs > files > en-us > web > api > filesystemdirectoryhandle > index.md Path: mdn-web-docs > files > en-us > web > api > filesystemdirectoryhandle > index.md Path: mdn-web-docs > files > en-us > web > api > filesystemdirectoryhandle > index.md Path: mdn-web-docs > files > en-us > web > api > filesystemdirectoryhandle > index.md --- title: FileSystemDirectoryHandle slug: Web/API/FileSystemDirectoryHandle page-type: web-api-interface browser-compat: api.FileSystemDirectoryHandle --- {{securecontext_header}}{{APIRef("File System API")}}{{AvailableInWorkers}} The **`FileSystemDirectoryHandle`** interface of the {{domxref("File System API", "File System API", "", "nocode")}} provides a handle to a file system directory. The interface can be accessed via the {{domxref('window.showDirectoryPicker()')}}, {{domxref('StorageManager.getDirectory()')}}, {{domxref('DataTransferItem.getAsFileSystemHandle()')}}, and {{domxref('FileSystemDirectoryHandle.getDirectoryHandle()')}} methods. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{DOMxRef("FileSystemHandle")}}._ ## Instance methods _Inherits methods from its parent, {{DOMxRef("FileSystemHandle")}}._ Regular methods: - {{domxref('FileSystemDirectoryHandle.getDirectoryHandle()')}} - : Returns a {{jsxref('Promise')}} fulfilled with a `FileSystemDirectoryHandle` for a subdirectory with the specified name within the directory handle on which the method is called. - {{domxref('FileSystemDirectoryHandle.getFileHandle()')}} - : Returns a {{jsxref('Promise')}} fulfilled with a {{domxref('FileSystemFileHandle')}} for a file with the specified name, within the directory the method is called. - {{domxref('FileSystemDirectoryHandle.removeEntry()')}} - : Attempts to asynchronously remove an entry if the directory handle contains a file or directory called the name specified. - {{domxref('FileSystemDirectoryHandle.resolve()')}} - : Returns a {{jsxref('Promise')}} fulfilled with an {{jsxref('Array')}} of directory names from the parent handle to the specified child entry, with the name of the child entry as the last array item. [Asynchronous iterator](/en-US/docs/Web/JavaScript/Reference/Iteration_protocols#the_async_iterator_and_async_iterable_protocols) methods: - {{domxref('FileSystemDirectoryHandle.entries()')}} - : Returns a new _async iterator_ of a given object's own enumerable property `[key, value]` pairs. - {{domxref('FileSystemDirectoryHandle.keys()')}} - : Returns a new _async iterator_ containing the keys for each item in `FileSystemDirectoryHandle`. - {{domxref('FileSystemDirectoryHandle.values()')}} - : Returns a new _async iterator_ containing the values for each index in the `FileSystemDirectoryHandle` object. - `FileSystemDirectoryHandle[Symbol.asyncIterator]()` - : Returns a new _async iterator_ of a given object's own enumerable property `[key, value]` pairs. ## Examples ### Return directory handle The following example returns a directory handle with the specified name; if the directory does not already exist it is created. ```js const dirName = "directoryToGetName"; // assuming we have a directory handle: 'currentDirHandle' const subDir = currentDirHandle.getDirectoryHandle(dirName, { create: true }); ``` ### Return file path The following asynchronous function uses `resolve()` to find the path to a chosen file, relative to a specified directory handle. ```js async function returnPathDirectories(directoryHandle) { // Get a file handle by showing a file picker: const handle = await self.showOpenFilePicker(); if (!handle) { // User cancelled, or otherwise failed to open a file. return; } // Check if handle exists inside our directory handle const relativePaths = await directoryHandle.resolve(handle); if (relativePath === null) { // Not inside directory handle } else { // relativePath is an array of names, giving the relative path for (const name of relativePaths) { // log each entry console.log(name); } } } ``` ### Return handles for all files in a directory The following example scans recursively through a directory to return {{domxref('FileSystemFileHandle')}} objects for each file in that directory: ```js async function* getFilesRecursively(entry) { if (entry.kind === "file") { const file = await entry.getFile(); if (file !== null) { file.relativePath = getRelativePath(entry); yield file; } } else if (entry.kind === "directory") { for await (const handle of entry.values()) { yield* getFilesRecursively(handle); } } } for await (const fileHandle of getFilesRecursively(directoryHandle)) { console.log(fileHandle); } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [File System API](/en-US/docs/Web/API/File_System_API) - [The File System Access API: simplifying access to local files](https://developer.chrome.com/docs/capabilities/web-apis/file-system-access)