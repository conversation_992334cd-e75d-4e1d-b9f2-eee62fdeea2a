Path: mdn-web-docs > files > en-us > web > api > fontface > featuresettings > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > featuresettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > featuresettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > featuresettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > featuresettings > index.md --- title: "FontFace: featureSettings property" short-title: featureSettings slug: Web/API/FontFace/featureSettings page-type: web-api-instance-property browser-compat: api.FontFace.featureSettings --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`featureSettings`** property of the {{domxref("FontFace")}} interface retrieves or sets infrequently used font features that are not available from a font's variant properties. This property is equivalent to the {{cssxref("font-feature-settings")}} descriptor. ## Value A string containing a descriptor. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}