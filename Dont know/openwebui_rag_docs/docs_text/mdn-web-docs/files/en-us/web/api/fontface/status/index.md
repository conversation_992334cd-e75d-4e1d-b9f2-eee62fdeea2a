Path: mdn-web-docs > files > en-us > web > api > fontface > status > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > status > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > status > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > status > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > status > index.md --- title: "FontFace: status property" short-title: status slug: Web/API/FontFace/status page-type: web-api-instance-property browser-compat: api.FontFace.status --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`status`** read-only property of the {{domxref("FontFace")}} interface returns an enumerated value indicating the status of the font, one of `"unloaded"`, `"loading"`, `"loaded"`, or `"error"`. ## Value One of `"unloaded"`, `"loading"`, `"loaded"`, or `"error"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}