Path: mdn-web-docs > files > en-us > web > api > fontface > stretch > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > stretch > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > stretch > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > stretch > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > stretch > index.md --- title: "FontFace: stretch property" short-title: stretch slug: Web/API/FontFace/stretch page-type: web-api-instance-property browser-compat: api.FontFace.stretch --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`stretch`** property of the {{domxref("FontFace")}} interface retrieves or sets how the font stretches. This property is equivalent to the {{cssxref("@font-face/font-stretch", "font-stretch")}} descriptor. ## Value A string containing a descriptor as it would be defined in a style sheet's `@font-face` rule. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}