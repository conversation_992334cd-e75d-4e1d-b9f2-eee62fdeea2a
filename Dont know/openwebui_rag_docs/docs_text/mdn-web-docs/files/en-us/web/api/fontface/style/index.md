Path: mdn-web-docs > files > en-us > web > api > fontface > style > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > style > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > style > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > style > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > style > index.md --- title: "FontFace: style property" short-title: style slug: Web/API/FontFace/style page-type: web-api-instance-property browser-compat: api.FontFace.style --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`style`** property of the {{domxref("FontFace")}} interface retrieves or sets the font's style. This property is equivalent to the {{cssxref("@font-face/font-style", "font-style")}} descriptor. ## Value A string containing the descriptors defined in the style sheet's `@font-face` rule. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}