Path: mdn-web-docs > files > en-us > web > api > fontface > unicoderange > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > unicoderange > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > unicoderange > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > unicoderange > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > unicoderange > index.md --- title: "FontFace: unicodeRange property" short-title: unicodeRange slug: Web/API/FontFace/unicodeRange page-type: web-api-instance-property browser-compat: api.FontFace.unicodeRange --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`unicodeRange`** property of the {{domxref("FontFace")}} interface retrieves or sets the range of unicode code points encompassing the font. This property is equivalent to the {{cssxref("@font-face/unicode-range", "unicode-range")}} descriptor. ## Value A string containing a descriptor as it would appear in a style sheet's `@font-face` rule. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}