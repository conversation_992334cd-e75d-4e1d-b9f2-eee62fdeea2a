Path: mdn-web-docs > files > en-us > web > api > fontface > variant > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > variant > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variant > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variant > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variant > index.md --- title: "FontFace: variant property" short-title: variant slug: Web/API/FontFace/variant page-type: web-api-instance-property status: - non-standard browser-compat: api.FontFace.variant --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}}{{non-standard_header}} The **`variant`** property of the {{domxref("FontFace")}} interface programmatically retrieves or sets font variant values. ## Value A string containing a descriptor as it would be defined in a style sheet's `@font-face` rule. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}