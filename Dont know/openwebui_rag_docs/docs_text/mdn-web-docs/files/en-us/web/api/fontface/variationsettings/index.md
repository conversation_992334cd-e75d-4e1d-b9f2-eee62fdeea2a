Path: mdn-web-docs > files > en-us > web > api > fontface > variationsettings > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > variationsettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variationsettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variationsettings > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > variationsettings > index.md --- title: "FontFace: variationSettings property" short-title: variationSettings slug: Web/API/FontFace/variationSettings page-type: web-api-instance-property status: - experimental browser-compat: api.FontFace.variationSettings --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}}{{SeeCompatTable}} The **`variationSettings`** property of the {{domxref("FontFace")}} interface retrieves or sets low-level OpenType or TrueType font variations. This property is equivalent to the {{cssxref("@font-face/font-variation-settings", "font-variation-settings")}} descriptor. ## Value A string containing a descriptor. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}