Path: mdn-web-docs > files > en-us > web > api > fontface > weight > index.md

Path: mdn-web-docs > files > en-us > web > api > fontface > weight > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > weight > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > weight > index.md Path: mdn-web-docs > files > en-us > web > api > fontface > weight > index.md --- title: "FontFace: weight property" short-title: weight slug: Web/API/FontFace/weight page-type: web-api-instance-property browser-compat: api.FontFace.weight --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`weight`** property of the {{domxref("FontFace")}} interface retrieves or sets the weight of the font. This property is equivalent to the {{cssxref("@font-face/font-weight", "font-weight")}} descriptor. ## Value A string containing a descriptor as it would be defined in a style sheet's `@font-face` rule. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}