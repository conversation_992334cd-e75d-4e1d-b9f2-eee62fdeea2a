Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > clear > index.md --- title: "FontFaceSet: clear() method" short-title: clear() slug: Web/API/FontFaceSet/clear page-type: web-api-instance-method browser-compat: api.FontFaceSet.clear --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`clear()`** method of the {{domxref("FontFaceSet")}} interface removes all fonts added via this interface. Fonts added with the {{cssxref("@font-face")}} rule are not removed. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}