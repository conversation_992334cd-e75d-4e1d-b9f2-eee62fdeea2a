Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > entries > index.md --- title: "FontFaceSet: entries() method" short-title: entries() slug: Web/API/FontFaceSet/entries page-type: web-api-instance-method browser-compat: api.FontFaceSet.entries --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`entries()`** method of the {{domxref("FontFaceSet")}} interface returns a new {{jsxref("Iterator")}} object, containing an array of `[value,value]` for each element in the `FontFaceSet`. ## Syntax ```js-nolint entries() ``` ### Parameters None. ### Return value A new iterator object that contains an array of `[value, value]` for each element in the `CustomStateSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}