Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > has > index.md --- title: "FontFaceSet: has() method" short-title: has() slug: Web/API/FontFaceSet/has page-type: web-api-instance-method browser-compat: api.FontFaceSet.has --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`has()`** method of the {{domxref("FontFaceSet")}} interface returns a {{jsxref("Boolean")}} asserting whether an element is present with the given value. ## Syntax ```js-nolint has(value) ``` ### Parameters - `value` - : The value to test for in the `FontFaceSet` object. ### Return value A {{jsxref("Boolean")}}, `true` if `value` exists in the `FontFaceSet`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}