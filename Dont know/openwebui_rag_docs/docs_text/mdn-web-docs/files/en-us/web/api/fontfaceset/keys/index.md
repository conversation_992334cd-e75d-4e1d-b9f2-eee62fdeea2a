Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > keys > index.md --- title: "FontFaceSet: keys() method" short-title: keys() slug: Web/API/FontFaceSet/keys page-type: web-api-instance-method browser-compat: api.FontFaceSet.keys --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`keys()`** method of the {{domxref("FontFaceSet")}} interface is an alias for {{domxref("FontFaceSet.values")}}. ## Syntax ```js-nolint keys() ``` ### Parameters None. ### Return value A new iterator object containing the values for each element in the given `FontFaceSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}