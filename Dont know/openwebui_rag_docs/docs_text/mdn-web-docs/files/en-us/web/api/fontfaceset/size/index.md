Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > size > index.md --- title: "FontFaceSet: size property" short-title: size slug: Web/API/FontFaceSet/size page-type: web-api-instance-property browser-compat: api.FontFaceSet.size --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`size`** property of the {{domxref("FontFaceSet")}} interface returns the number of items in the `FontFaceSet`. ## Value An integer indicating the number of items in the `FontFaceSet`. ## Examples In the following example the `size` of the `FontFaceSet` is printed to the console. ```js console.log(document.fonts.size); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}