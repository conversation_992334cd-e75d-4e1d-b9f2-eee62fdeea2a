Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md Path: mdn-web-docs > files > en-us > web > api > fontfaceset > values > index.md --- title: "FontFaceSet: values() method" short-title: values() slug: Web/API/FontFaceSet/values page-type: web-api-instance-method browser-compat: api.FontFaceSet.values --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`values()`** method of the {{domxref("FontFaceSet")}} interface returns a new iterator object that yields the values for each element in the `FontFaceSet` object in insertion order. ## Syntax ```js-nolint values() ``` ### Parameters None. ### Return value A new iterator object containing the values for each element in the given `FontFaceSet`, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}