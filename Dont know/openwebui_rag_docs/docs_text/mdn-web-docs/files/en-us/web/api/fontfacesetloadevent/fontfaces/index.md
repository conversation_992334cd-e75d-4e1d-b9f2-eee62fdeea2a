Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md

Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md Path: mdn-web-docs > files > en-us > web > api > fontfacesetloadevent > fontfaces > index.md --- title: "FontFaceSetLoadEvent: fontfaces property" short-title: fontfaces slug: Web/API/FontFaceSetLoadEvent/fontfaces page-type: web-api-instance-property browser-compat: api.FontFaceSetLoadEvent.fontfaces --- {{APIRef("CSS Font Loading API")}}{{AvailableInWorkers}} The **`fontfaces`** read-only property of the {{domxref("FontFaceSetLoadEvent")}} interface returns an array of {{domxref("FontFace")}} instances, each of which represents a single usable font. ## Value An array of {{domxref("FontFace")}} instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}