Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md

Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadevent > gamepadevent > index.md --- title: "GamepadEvent: GamepadEvent() constructor" short-title: GamepadEvent() slug: Web/API/GamepadEvent/GamepadEvent page-type: web-api-constructor browser-compat: api.GamepadEvent.GamepadEvent --- {{APIRef("Gamepad API")}}{{SecureContext_Header}} The **`GamepadEvent()`** constructor creates a new {{domxref("GamepadEvent")}} object. ## Syntax ```js-nolint new GamepadEvent(type, options) ``` ### Parameters - `type` - : A string with the name of the event. It is case-sensitive and browsers set it to `gamepadconnected` or `gamepaddisconnected`. - `options` - : An object that, _in addition of the properties defined in {{domxref("Event/Event", "Event()")}}_, can have the following properties: - `gamepad` - : A {{domxref("Gamepad")}} object describing the gamepad associated with the event. ### Return value A new {{domxref("GamepadEvent")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}