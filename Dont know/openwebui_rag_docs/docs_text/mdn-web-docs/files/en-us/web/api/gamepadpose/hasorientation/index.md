Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md

Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md Path: mdn-web-docs > files > en-us > web > api > gamepadpose > hasorientation > index.md --- title: "GamepadPose: hasOrientation property" short-title: hasOrientation slug: Web/API/GamepadPose/hasOrientation page-type: web-api-instance-property status: - experimental browser-compat: api.GamepadPose.hasOrientation --- {{APIRef("WebVR API")}}{{SeeCompatTable}} The **`hasOrientation`** read-only property of the {{domxref("GamepadPose")}} interface returns a boolean value stating whether the {{domxref("Gamepad")}} can track and return orientation information. ## Value A boolean value. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API) - [Gamepad API](/en-US/docs/Web/API/Gamepad_API)