Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > accuracy > index.md --- title: "GeolocationCoordinates: accuracy property" short-title: accuracy slug: Web/API/GeolocationCoordinates/accuracy page-type: web-api-instance-property browser-compat: api.GeolocationCoordinates.accuracy --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`accuracy`** read-only property of the {{domxref("GeolocationCoordinates")}} interface is a strictly positive `double` representing the accuracy, with a 95% confidence level, of the {{domxref("GeolocationCoordinates.latitude")}} and {{domxref("GeolocationCoordinates.longitude")}} properties expressed in meters. ## Value A positive `double` representing the accuracy, with a 95% confidence level, of the {{domxref("GeolocationCoordinates.latitude")}} and {{domxref("GeolocationCoordinates.longitude")}} properties expressed in meters. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationCoordinates")}}