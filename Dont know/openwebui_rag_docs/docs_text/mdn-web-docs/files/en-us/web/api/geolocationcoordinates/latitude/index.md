Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > latitude > index.md --- title: "GeolocationCoordinates: latitude property" short-title: latitude slug: Web/API/GeolocationCoordinates/latitude page-type: web-api-instance-property browser-compat: api.GeolocationCoordinates.latitude --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`latitude`** read-only property of the {{domxref("GeolocationCoordinates")}} interface is a `double` representing the latitude of the position in decimal degrees. ## Value A `double` representing the latitude of the position in decimal degrees. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationCoordinates")}}