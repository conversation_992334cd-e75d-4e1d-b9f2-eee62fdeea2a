Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationcoordinates > speed > index.md --- title: "GeolocationCoordinates: speed property" short-title: speed slug: Web/API/GeolocationCoordinates/speed page-type: web-api-instance-property browser-compat: api.GeolocationCoordinates.speed --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`speed`** read-only property of the {{domxref("GeolocationCoordinates")}} interface is a `double` representing the velocity of the device in meters per second. This value is `null` if the implementation is not able to measure it. ## Value A `double` representing the velocity of the device in meters per second. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationCoordinates")}}