Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > coords > index.md --- title: "GeolocationPosition: coords property" short-title: coords slug: Web/API/GeolocationPosition/coords page-type: web-api-instance-property browser-compat: api.GeolocationPosition.coords --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`coords`** read-only property of the {{domxref("GeolocationPosition")}} interface returns a {{domxref("GeolocationCoordinates")}} object representing a geographic position. It contains the location, that is longitude and latitude on the Earth, the altitude, and the speed of the object concerned, regrouped inside the returned value. It also contains accuracy information about these values. ## Value A {{domxref("GeolocationCoordinates")}} object instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationPosition")}}