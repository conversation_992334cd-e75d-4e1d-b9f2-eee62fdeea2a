Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationposition > timestamp > index.md --- title: "GeolocationPosition: timestamp property" short-title: timestamp slug: Web/API/GeolocationPosition/timestamp page-type: web-api-instance-property browser-compat: api.GeolocationPosition.timestamp --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`timestamp`** read-only property of the {{domxref("GeolocationPosition")}} interface represents the date and time that the position was acquired by the device. ## Value A number containing a timestamp, given as {{Glossary("Unix time")}} in milliseconds. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationPosition")}}