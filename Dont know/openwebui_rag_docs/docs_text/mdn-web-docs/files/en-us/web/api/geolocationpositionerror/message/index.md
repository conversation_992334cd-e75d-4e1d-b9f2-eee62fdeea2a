Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md

Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md Path: mdn-web-docs > files > en-us > web > api > geolocationpositionerror > message > index.md --- title: "GeolocationPositionError: message property" short-title: message slug: Web/API/GeolocationPositionError/message page-type: web-api-instance-property browser-compat: api.GeolocationPositionError.message --- {{securecontext_header}}{{APIRef("Geolocation API")}} The **`message`** read-only property of the {{domxref("GeolocationPositionError")}} interface returns a human-readable string describing the details of the error. ## Value A human-readable string describing the details of the error. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Geolocation API](/en-US/docs/Web/API/Geolocation_API/Using_the_Geolocation_API) - {{domxref("GeolocationPositionError")}}