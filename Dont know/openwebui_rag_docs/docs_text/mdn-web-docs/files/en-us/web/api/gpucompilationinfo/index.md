Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md

Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > index.md --- title: GPUCompilationInfo slug: Web/API/GPUCompilationInfo page-type: web-api-interface status: - experimental browser-compat: api.GPUCompilationInfo --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`GPUCompilationInfo`** interface of the {{domxref("WebGPU API", "WebGPU API", "", "nocode")}} represents an array of {{domxref("GPUCompilationMessage")}} objects generated by the GPU shader module compiler to help diagnose problems with shader code. `GPUCompilationInfo` is accessed via {{domxref("GPUShaderModule.getCompilationInfo()")}}. {{InheritanceDiagram}} ## Instance properties - {{domxref("GPUCompilationInfo.messages", "messages")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : An array of {{domxref("GPUCompilationMessage")}} objects, each one containing the details of an individual shader compilation message. Messages can be informational, warnings, or errors. ## Examples In the example below, we have deliberately left a parenthesis out of a function declaration in our shader code: ```js const shaders = ` struct VertexOut { @builtin(position) position : vec4f, @location(0) color : vec4f } @vertex fn vertex_main(@location(0) position: vec4f, @location(1) color: vec4f -> VertexOut { var output : VertexOut; output.position = position; output.color = color; return output; } @fragment fn fragment_main(fragData: VertexOut) -> @location(0) vec4f { return fragData.color; } `; ``` When we compile the shader module, we use `getCompilationInfo()` to grab some information about the resulting error: ```js async function init() { // const shaderModule = device.createShaderModule({ code: shaders, }); const shaderInfo = await shaderModule.getCompilationInfo(); const firstMessage = shaderInfo.messages[0]; console.log(firstMessage.lineNum); // 9 console.log(firstMessage.message); // "expected ')' for function declaration" console.log(firstMessage.type); // "error" // } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)