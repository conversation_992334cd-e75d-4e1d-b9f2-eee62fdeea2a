Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md

Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationinfo > messages > index.md --- title: "GPUCompilationInfo: messages property" short-title: messages slug: Web/API/GPUCompilationInfo/messages page-type: web-api-instance-property status: - experimental browser-compat: api.GPUCompilationInfo.messages --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`messages`** read-only property of the {{domxref("GPUCompilationInfo")}} interface is an array of {{domxref("GPUCompilationMessage")}} objects, each one containing the details of an individual shader compilation message. Messages can be informational, warnings, or errors. ## Value An array of {{domxref("GPUCompilationMessage")}} objects. ## Examples See the main [`GPUCompilationInfo` page](/en-US/docs/Web/API/GPUCompilationInfo#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)