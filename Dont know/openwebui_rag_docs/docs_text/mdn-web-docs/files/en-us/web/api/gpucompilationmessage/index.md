Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md

Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > gpucompilationmessage > index.md --- title: GPUCompilationMessage slug: Web/API/GPUCompilationMessage page-type: web-api-interface status: - experimental browser-compat: api.GPUCompilationMessage --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`GPUCompilationMessage`** interface of the {{domxref("WebGPU API", "WebGPU API", "", "nocode")}} represents a single informational, warning, or error message generated by the GPU shader module compiler. An array of `GPUCompilationMessage` objects is available in the `messages` property of the {{domxref("GPUCompilationInfo")}} object accessed via {{domxref("GPUShaderModule.getCompilationInfo()")}}. {{InheritanceDiagram}} ## Instance properties - {{domxref("GPUCompilationMessage.length", "length")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : A number representing the length of the substring that the message corresponds to. - {{domxref("GPUCompilationMessage.lineNum", "lineNum")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : A number representing the line number in the shader code that the message corresponds to. - {{domxref("GPUCompilationMessage.linePos", "linePos")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : A number representing the position in the code line that the message corresponds to. This could be an exact point, or the start of the relevant substring. - {{domxref("GPUCompilationMessage.message", "message")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : A string representing human-readable message text. - {{domxref("GPUCompilationMessage.offset", "offset")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : A number representing the offset from the start of the shader code to the exact point, or the start of the relevant substring, that the message corresponds to. - {{domxref("GPUCompilationMessage.type", "type")}} {{Experimental_Inline}} {{ReadOnlyInline}} - : An enumerated value representing the type of the message `"error"`, `"info"`, or `"warning"`. ## Examples See the main [`GPUCompilationInfo` page](/en-US/docs/Web/API/GPUCompilationInfo#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)