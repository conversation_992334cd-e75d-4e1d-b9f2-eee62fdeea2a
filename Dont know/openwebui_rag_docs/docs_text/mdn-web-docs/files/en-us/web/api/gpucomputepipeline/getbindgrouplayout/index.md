Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md

Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > getbindgrouplayout > index.md --- title: "GPUComputePipeline: getBindGroupLayout() method" short-title: getBindGroupLayout() slug: Web/API/GPUComputePipeline/getBindGroupLayout page-type: web-api-instance-method status: - experimental browser-compat: api.GPUComputePipeline.getBindGroupLayout --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`getBindGroupLayout()`** method of the {{domxref("GPUComputePipeline")}} interface returns the pipeline's {{domxref("GPUBindGroupLayout")}} object with the given index (i.e., included in the originating {{domxref("GPUDevice.createComputePipeline()")}} or {{domxref("GPUDevice.createComputePipelineAsync()")}} call's pipeline layout). If the {{domxref("GPUComputePipeline")}} was created with `layout: "auto"`, this method is the only way to retrieve the {{domxref("GPUBindGroupLayout")}}s generated by the pipeline. ## Syntax ```js-nolint getBindGroupLayout(index) ``` ### Parameters - `index` - : A number representing the index of the {{domxref("GPUBindGroupLayout")}} to return. ### Return value A {{domxref("GPUBindGroupLayout")}} object instance. ### Validation The following criteria must be met when calling **`getBindGroupLayout()`**, otherwise a {{domxref("GPUValidationError")}} is generated and an invalid {{domxref("GPUBindGroupLayout")}} object is returned: - `index` is less than the number of {{domxref("GPUBindGroupLayout")}} objects used in the pipeline layout. ## Examples > [!NOTE] > You can see complete working examples with `getBindGroupLayout()` in action in the [WebGPU samples](https://webgpu.github.io/webgpu-samples/). ```js // // Create a compute pipeline using layout: "auto" to automatically generate // appropriate bind group layouts const computePipeline = device.createComputePipeline({ layout: "auto", compute: { module: shaderModule, entryPoint: "main", }, }); // Create a bind group with the auto-generated layout from the compute pipeline const computeBindGroup = device.createBindGroup({ layout: computePipeline.getBindGroupLayout(0), entries: [ { binding: 0, resource: { buffer: storageBuffer }, }, ], }); // ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)