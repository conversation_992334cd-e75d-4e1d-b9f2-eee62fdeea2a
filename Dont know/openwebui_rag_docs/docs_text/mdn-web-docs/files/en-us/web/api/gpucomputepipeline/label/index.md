Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md

Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md Path: mdn-web-docs > files > en-us > web > api > gpucomputepipeline > label > index.md --- title: "GPUComputePipeline: label property" short-title: label slug: Web/API/GPUComputePipeline/label page-type: web-api-instance-property status: - experimental browser-compat: api.GPUComputePipeline.label --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`label`** property of the {{domxref("GPUComputePipeline")}} interface provides a label that can be used to identify the object, for example in {{domxref("GPUError")}} messages or console warnings. This can be set by providing a `label` property in the descriptor object passed into the originating {{domxref("GPUDevice.createComputePipeline()")}} or {{domxref("GPUDevice.createComputePipelineAsync()")}} call, or you can get and set it directly on the `GPUComputePipeline` object. ## Value A string. If this has not been previously set as described above, it will be an empty string. ## Examples Setting and getting a label via `GPUComputePipeline.label`: ```js // const computePipeline = device.createComputePipeline({ layout: device.createPipelineLayout({ bindGroupLayouts: [bindGroupLayout], }), compute: { module: shaderModule, entryPoint: "main", }, }); computePipeline.label = "my_compute_pipeline"; console.log(computePipeline.label); // "my_compute_pipeline" ``` Setting a label via a {{domxref("GPUDevice.createComputePipeline()")}} call, and then getting it via `GPUComputePipeline.label`: ```js // const computePipeline = device.createComputePipeline({ layout: device.createPipelineLayout({ bindGroupLayouts: [bindGroupLayout], }), compute: { module: shaderModule, entryPoint: "main", }, label: "my_compute_pipeline", }); console.log(computePipeline.label); // "my_compute_pipeline" ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)