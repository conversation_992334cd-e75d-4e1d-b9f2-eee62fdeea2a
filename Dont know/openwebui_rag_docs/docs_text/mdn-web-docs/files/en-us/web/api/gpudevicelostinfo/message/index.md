Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md

Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md Path: mdn-web-docs > files > en-us > web > api > gpudevicelostinfo > message > index.md --- title: "GPUDeviceLostInfo: message property" short-title: message slug: Web/API/GPUDeviceLostInfo/message page-type: web-api-instance-property status: - experimental browser-compat: api.GPUDeviceLostInfo.message --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`message`** read-only property of the {{domxref("GPUDeviceLostInfo")}} interface provides a human-readable message that explains why the device was lost. ## Value A string. ## Examples See the main [`GPUDevice.lost` page](/en-US/docs/Web/API/GPUDevice/lost#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)