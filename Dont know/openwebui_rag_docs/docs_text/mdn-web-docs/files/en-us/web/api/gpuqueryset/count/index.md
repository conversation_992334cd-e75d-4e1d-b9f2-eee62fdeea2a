Path: mdn-web-docs > files > en-us > web > api > gpuqueryset > count > index.md

Path: mdn-web-docs > files > en-us > web > api > gpuqueryset > count > index.md Path: mdn-web-docs > files > en-us > web > api > gpuqueryset > count > index.md Path: mdn-web-docs > files > en-us > web > api > gpuqueryset > count > index.md Path: mdn-web-docs > files > en-us > web > api > gpuqueryset > count > index.md --- title: "GPUQuerySet: count property" short-title: count slug: Web/API/GPUQuerySet/count page-type: web-api-instance-property status: - experimental browser-compat: api.GPUQuerySet.count --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`count`** read-only property of the {{domxref("GPUQuerySet")}} interface is a number specifying the number of queries managed by the `GPUQuerySet`. ## Value A number. ## Examples See the main [`GPUQuerySet`](/en-US/docs/Web/API/GPUQuerySet#examples) page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)