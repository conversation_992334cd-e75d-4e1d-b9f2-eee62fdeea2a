Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md

Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md Path: mdn-web-docs > files > en-us > web > api > gpurenderpipeline > getbindgrouplayout > index.md --- title: "GPURenderPipeline: getBindGroupLayout() method" short-title: getBindGroupLayout() slug: Web/API/GPURenderPipeline/getBindGroupLayout page-type: web-api-instance-method status: - experimental browser-compat: api.GPURenderPipeline.getBindGroupLayout --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`getBindGroupLayout()`** method of the {{domxref("GPURenderPipeline")}} interface returns the pipeline's {{domxref("GPUBindGroupLayout")}} object with the given index (i.e., included in the originating {{domxref("GPUDevice.createRenderPipeline()")}} or {{domxref("GPUDevice.createRenderPipelineAsync()")}} call's pipeline layout). If the {{domxref("GPURenderPipeline")}} was created with `layout: "auto"`, this method is the only way to retrieve the {{domxref("GPUBindGroupLayout")}}s generated by the pipeline. ## Syntax ```js-nolint getBindGroupLayout(index) ``` ### Parameters - `index` - : A number representing the index of the {{domxref("GPUBindGroupLayout")}} to return. ### Return value A {{domxref("GPUBindGroupLayout")}} object instance. ### Validation The following criteria must be met when calling **`getBindGroupLayout()`**, otherwise a {{domxref("GPUValidationError")}} is generated and an invalid {{domxref("GPUBindGroupLayout")}} object is returned: - `index` is less than the number of {{domxref("GPUBindGroupLayout")}} objects used in the pipeline layout. ## Examples > [!NOTE] > You can see complete working examples with `getBindGroupLayout()` in action in the [WebGPU samples](https://webgpu.github.io/webgpu-samples/). ```js // // Create a render pipeline using layout: "auto" to automatically generate // appropriate bind group layouts const fullscreenQuadPipeline = device.createRenderPipeline({ layout: "auto", vertex: { module: device.createShaderModule({ code: fullscreenTexturedQuadWGSL, }), entryPoint: "vert_main", }, fragment: { module: device.createShaderModule({ code: fullscreenTexturedQuadWGSL, }), entryPoint: "frag_main", targets: [ { format: presentationFormat, }, ], }, primitive: { topology: "triangle-list", }, }); // // Create a bind group with the auto-generated layout from the render pipeline const showResultBindGroup = device.createBindGroup({ layout: fullscreenQuadPipeline.getBindGroupLayout(0), entries: [ { binding: 0, resource: sampler, }, { binding: 1, resource: textures[1].createView(), }, ], }); // ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API)