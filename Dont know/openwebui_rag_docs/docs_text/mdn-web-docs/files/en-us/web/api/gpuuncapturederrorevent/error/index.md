Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md

Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > gpuuncapturederrorevent > error > index.md --- title: "GPUUncapturedErrorEvent: error property" short-title: error slug: Web/API/GPUUncapturedErrorEvent/error page-type: web-api-instance-property status: - experimental browser-compat: api.GPUUncapturedErrorEvent.error --- {{APIRef("WebGPU API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`error`** read-only property of the {{domxref("GPUUncapturedErrorEvent")}} interface is a {{domxref("GPUError")}} object instance providing access to the details of the error. ## Value A {{domxref("GPUError")}} object instance. ## Examples See the main [`GPUUncapturedErrorEvent`](/en-US/docs/Web/API/GPUUncapturedErrorEvent#examples) page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The [WebGPU API](/en-US/docs/Web/API/WebGPU_API) - [WebGPU Error Handling best practices](https://toji.dev/webgpu-best-practices/error-handling)