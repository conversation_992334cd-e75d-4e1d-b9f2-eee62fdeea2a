Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md

Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md Path: mdn-web-docs > files > en-us > web > api > hashchangeevent > newurl > index.md --- title: "HashChangeEvent: newURL property" short-title: newURL slug: Web/API/HashChangeEvent/newURL page-type: web-api-instance-property browser-compat: api.HashChangeEvent.newURL --- {{APIRef("HTML DOM")}} The **`newURL`** read-only property of the {{domxref("HashChangeEvent")}} interface returns the new URL to which the window is navigating. ## Value A string. ## Examples ```js window.addEventListener("hashchange", (event) => { console.log(`Hash changed to ${event.newURL}`); }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}