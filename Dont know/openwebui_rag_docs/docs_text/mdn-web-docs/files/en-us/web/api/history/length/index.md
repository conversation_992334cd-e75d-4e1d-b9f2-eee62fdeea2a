Path: mdn-web-docs > files > en-us > web > api > history > length > index.md

Path: mdn-web-docs > files > en-us > web > api > history > length > index.md Path: mdn-web-docs > files > en-us > web > api > history > length > index.md Path: mdn-web-docs > files > en-us > web > api > history > length > index.md Path: mdn-web-docs > files > en-us > web > api > history > length > index.md Path: mdn-web-docs > files > en-us > web > api > history > length > index.md --- title: "History: length property" short-title: length slug: Web/API/History/length page-type: web-api-instance-property browser-compat: api.History.length --- {{APIRef("History API")}} The **`length`** read-only property of the {{DOMxRef("History")}} interface returns an integer representing the number of entries in the session history, including the currently loaded page. For example, for a page loaded in a new tab this property returns `1`. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("History")}} interface it belongs to.