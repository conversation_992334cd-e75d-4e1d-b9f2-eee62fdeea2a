Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md Path: mdn-web-docs > files > en-us > web > api > htmlallcollection > length > index.md --- title: "HTMLAllCollection: length property" short-title: length slug: Web/API/HTMLAllCollection/length page-type: web-api-instance-property browser-compat: api.HTMLAllCollection.length --- {{APIRef("DOM")}} The **`HTMLAllCollection.length`** property returns the number of items in this {{domxref("HTMLAllCollection")}}. ## Value An integer value representing the number of items in this `HTMLAllCollection`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLCollection.length")}}