Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > download > index.md --- title: "HTMLAnchorElement: download property" short-title: download slug: Web/API/HTMLAnchorElement/download page-type: web-api-instance-property browser-compat: api.HTMLAnchorElement.download --- {{APIRef("HTML DOM")}} The **`HTMLAnchorElement.download`** property is a string indicating that the linked resource is intended to be downloaded rather than displayed in the browser. The value, if any, specifies the default file name for use in labeling the resource in a local file system. If the name is not a valid file name in the underlying OS, the browser will adjust it. > [!NOTE] > This value might not be used for download. This value cannot > be used to determine whether the download will occur. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}