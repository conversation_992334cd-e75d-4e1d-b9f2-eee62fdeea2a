Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md Path: mdn-web-docs > files > en-us > web > api > htmlanchorelement > href > index.md --- title: "HTMLAnchorElement: href property" short-title: href slug: Web/API/HTMLAnchorElement/href page-type: web-api-instance-property browser-compat: api.HTMLAnchorElement.href --- {{ApiRef("HTML DOM")}} The **`HTMLAnchorElement.href`** property is a {{Glossary("stringifier")}} that returns a string containing the whole URL, and allows the href to be updated. ## Value A string. ## Examples ```js // An <a id="myAnchor" href="https://developer.mozilla.org/en-US/HTMLAnchorElement"> element is in the document const anchor = document.getElementById("myAnchor"); anchor.href; // returns 'https://developer.mozilla.org/en-US/HTMLAnchorElement' ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("HTMLAnchorElement")}} interface it belongs to.