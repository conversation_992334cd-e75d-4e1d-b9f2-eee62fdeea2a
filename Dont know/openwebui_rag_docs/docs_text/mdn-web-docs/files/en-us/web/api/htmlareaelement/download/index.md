Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md Path: mdn-web-docs > files > en-us > web > api > htmlareaelement > download > index.md --- title: "HTMLAreaElement: download property" short-title: download slug: Web/API/HTMLAreaElement/download page-type: web-api-instance-property browser-compat: api.HTMLAreaElement.download --- {{APIRef("HTML DOM")}} The **`download`** property of the {{domxref("HTMLAreaElement")}} interface is a string indicating that the linked resource is intended to be downloaded rather than displayed in the browser. The value represent the proposed name of the file. If the name is not a valid filename of the underlying OS, browser will adjust it accordingly. It reflects the `download` attribute of the {{HTMLElement("area")}} element. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}