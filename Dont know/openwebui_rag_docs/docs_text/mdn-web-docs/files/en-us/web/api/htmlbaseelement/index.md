Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbaseelement > index.md --- title: HTMLBaseElement slug: Web/API/HTMLBaseElement page-type: web-api-interface browser-compat: api.HTMLBaseElement --- {{APIRef("HTML DOM")}} The **`HTMLBaseElement`** interface contains the base URI for a document. This object inherits all of the properties and methods as described in the {{domxref("HTMLElement")}} interface. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLBaseElement.href")}} - : A string that reflects the [`href`](/en-US/docs/Web/HTML/Reference/Elements/base#href) HTML attribute, containing a base URL for relative URLs in the document. - {{domxref("HTMLBaseElement.target")}} - : A string that reflects the [`target`](/en-US/docs/Web/HTML/Reference/Elements/base#target) HTML attribute, containing a default target browsing context or frame for elements that do not have a target reference specified. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - HTML element implementing this interface: {{ HTMLElement("base") }}