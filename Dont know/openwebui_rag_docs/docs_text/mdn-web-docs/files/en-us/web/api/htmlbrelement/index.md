Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbrelement > index.md --- title: HTMLBRElement slug: Web/API/HTMLBRElement page-type: web-api-interface browser-compat: api.HTMLBRElement --- {{APIRef("HTML DOM")}} The **`HTMLBRElement`** interface represents an HTML line break element ({{htmlelement("br")}}). It inherits from {{domxref("HTMLElement")}}. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLBRElement.clear")}} {{deprecated_inline}} - : A string indicating the flow of text around floating objects. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}_. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - HTML element implementing this interface: {{HTMLElement("br")}}