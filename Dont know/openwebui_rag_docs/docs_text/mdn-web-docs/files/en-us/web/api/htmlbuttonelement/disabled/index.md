Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > disabled > index.md --- title: "HTMLButtonElement: disabled property" short-title: disabled slug: Web/API/HTMLButtonElement/disabled page-type: web-api-instance-property browser-compat: api.HTMLButtonElement.disabled --- {{APIRef("HTML DOM")}} The **`HTMLButtonElement.disabled`** property indicates whether the control is disabled, meaning that it does not accept any clicks. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}