Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlbuttonelement > form > index.md --- title: "HTMLButtonElement: form property" short-title: form slug: Web/API/HTMLButtonElement/form page-type: web-api-instance-property browser-compat: api.HTMLButtonElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLButtonElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("button")}}, or `null` if this button is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLButtonElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("button")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/button#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)