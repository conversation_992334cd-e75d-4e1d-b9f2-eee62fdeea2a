Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > index.md --- title: HTMLDataElement slug: Web/API/HTMLDataElement page-type: web-api-interface browser-compat: api.HTMLDataElement --- {{APIRef("HTML DOM")}} The **`HTMLDataElement`** interface provides special properties (beyond the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating {{HTMLElement("data")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLDataElement.value")}} - : A string reflecting the [`value`](/en-US/docs/Web/HTML/Reference/Elements/data#value) HTML attribute, containing a machine-readable form of the element's value. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("data")}}.