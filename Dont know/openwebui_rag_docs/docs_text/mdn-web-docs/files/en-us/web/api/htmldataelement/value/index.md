Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md Path: mdn-web-docs > files > en-us > web > api > htmldataelement > value > index.md --- title: "HTMLDataElement: value property" short-title: value slug: Web/API/HTMLDataElement/value page-type: web-api-instance-property browser-compat: api.HTMLDataElement.value --- {{APIRef("HTML DOM")}} The **`value`** property of the {{domxref("HTMLDataElement")}} interface returns a string reflecting the [`value`](/en-US/docs/Web/HTML/Reference/Elements/data#value) HTML attribute. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}