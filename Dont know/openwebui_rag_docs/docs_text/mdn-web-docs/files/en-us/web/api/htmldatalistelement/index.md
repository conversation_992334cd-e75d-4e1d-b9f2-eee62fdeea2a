Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > index.md --- title: HTMLDataListElement slug: Web/API/HTMLDataListElement page-type: web-api-interface browser-compat: api.HTMLDataListElement --- {{APIRef("HTML DOM")}} The **`HTMLDataListElement`** interface provides special properties (beyond the {{domxref("HTMLElement")}} object interface it also has available to it by inheritance) to manipulate {{ HTMLElement("datalist") }} elements and their content. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}_. - {{domxref("HTMLDataListElement.options")}} {{ReadOnlyInline}} - : A {{domxref("HTMLCollection")}} representing a collection of the contained option elements. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("datalist") }}