Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md Path: mdn-web-docs > files > en-us > web > api > htmldatalistelement > options > index.md --- title: "HTMLDataListElement: options property" short-title: options slug: Web/API/HTMLDataListElement/options page-type: web-api-instance-property browser-compat: api.HTMLDataListElement.options --- {{APIRef("HTML DOM")}} The **`options`** read-only property of the {{domxref("HTMLDataListElement")}} interface returns an {{domxref("HTMLCollection")}} of {{domxref("HTMLOptionElement")}} elements contained in a {{htmlelement("datalist")}}. The descendant {{htmlelement("option")}} elements provide predefined options for the {{htmlelement("input")}} control associated with the `<datalist>`. ## Value An {{domxref("HTMLCollection")}} of {{domxref("HTMLOptionElement")}} elements. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLDataListElement")}} - {{domxref("HTMLOptionElement")}} - {{htmlelement("datalist")}} - {{htmlelement("option")}}