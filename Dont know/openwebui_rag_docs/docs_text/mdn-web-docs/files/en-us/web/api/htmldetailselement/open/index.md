Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md Path: mdn-web-docs > files > en-us > web > api > htmldetailselement > open > index.md --- title: "HTMLDetailsElement: open property" short-title: open slug: Web/API/HTMLDetailsElement/open page-type: web-api-instance-property browser-compat: api.HTMLDetailsElement.open --- {{ APIRef("HTML DOM") }} The **`open`** property of the {{domxref("HTMLDetailsElement")}} interface is a boolean value reflecting the [`open`](/en-US/docs/Web/HTML/Reference/Elements/details#open) HTML attribute, indicating whether the {{htmlelement("details")}}'s contents (not counting the {{htmlelement("summary")}}) is to be shown to the user. ## Value A boolean value. The boolean value represents the state of the [`open`](/en-US/docs/Web/HTML/Reference/Elements/details#open) HTML attribute. The value of `true` means it is set with both the summary and the additional information shown to the user. The `false` value means it not set, with only the summary shown. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{htmlelement("details")}} and {{htmlelement("summary")}} elements