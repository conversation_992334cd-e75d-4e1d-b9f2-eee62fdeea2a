Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md Path: mdn-web-docs > files > en-us > web > api > htmldialogelement > close > index.md --- title: "HTMLDialogElement: close() method" short-title: close() slug: Web/API/HTMLDialogElement/close page-type: web-api-instance-method browser-compat: api.HTMLDialogElement.close --- {{ APIRef("HTML DOM") }} The **`close()`** method of the {{domxref("HTMLDialogElement")}} interface closes the {{htmlelement("dialog")}}. An optional string may be passed as an argument, updating the `returnValue` of the dialog. ## Syntax ```js-nolint close() close(returnValue) ``` ### Parameters - `returnValue` {{optional_inline}} - : A string representing an updated value for the {{domxref("HTMLDialogElement.returnValue")}} of the dialog. ### Return value None ({{jsxref("undefined")}}). ## Examples The following example shows a simple button that, when clicked, opens a {{htmlelement("dialog")}} containing a form via the `showModal()` method. From there you can click the _X_ button to close the dialog (via the `HTMLDialogElement.close()` method), or submit the form via the submit button. ```html <!-- Simple pop-up dialog box, containing a form --> <dialog id="favDialog"> <form method="dialog"> <button id="close" aria-label="close" formnovalidate>X</button> <section> <p> <label for="favAnimal">Favorite animal:</label> <select id="favAnimal" name="favAnimal"> <option></option> <option>Brine shrimp</option> <option>Red panda</option> <option>Spider monkey</option> </select> </p> </section> <menu> <button type="reset">Reset</button> <button type="submit">Confirm</button> </menu> </form> </dialog> <menu> <button id="updateDetails">Update details</button> </menu> <script> (() => { const updateButton = document.getElementById("updateDetails"); const closeButton = document.getElementById("close"); const dialog = document.getElementById("favDialog"); dialog.returnValue = "favAnimal"; function openCheck(dialog) { if (dialog.open) { console.log("Dialog open"); } else { console.log("Dialog closed"); } } // Update button opens a modal dialog updateButton.addEventListener("click", () => { dialog.showModal(); openCheck(dialog); }); // Form close button closes the dialog box closeButton.addEventListener("click", () => { dialog.close("animalNotChosen"); openCheck(dialog); }); })(); </script> ``` If the "X" button was of `type="submit"`, the dialog would have closed without requiring JavaScript. A form submission closes the `<dialog>` it is nested within if the [form's method is `dialog`](/en-US/docs/Web/HTML/Reference/Elements/form#method), so no "close" button is required. ### Result {{ EmbedLiveSample('Examples', '100%', '200px') }} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("dialog") }}.