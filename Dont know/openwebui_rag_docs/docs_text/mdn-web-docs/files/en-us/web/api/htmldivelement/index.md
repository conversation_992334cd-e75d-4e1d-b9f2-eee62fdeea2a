Path: mdn-web-docs > files > en-us > web > api > htmldivelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldivelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldivelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldivelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldivelement > index.md --- title: HTMLDivElement slug: Web/API/HTMLDivElement page-type: web-api-interface browser-compat: api.HTMLDivElement --- {{ APIRef("HTML DOM") }} The **`HTMLDivElement`** interface provides special properties (beyond the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating {{HtmlElement("div")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLDivElement.align")}} {{deprecated_inline}} - : A string representing an enumerated property indicating alignment of the element's contents with respect to the surrounding context. The possible values are `"left"`, `"right"`, `"justify"`, and `"center"`. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("div") }}.