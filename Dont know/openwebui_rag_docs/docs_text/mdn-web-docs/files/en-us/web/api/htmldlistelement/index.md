Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmldlistelement > index.md --- title: HTMLDListElement slug: Web/API/HTMLDListElement page-type: web-api-interface browser-compat: api.HTMLDListElement --- {{ApiRef("HTML DOM")}} The **`HTMLDListElement`** interface provides special properties (beyond those of the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating definition list ({{HtmlElement("dl")}}) elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLDListElement.compact")}} {{deprecated_inline}} - : A boolean value indicating that spacing between list items should be reduced. ## Instance methods _No specific methods; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("dl") }}