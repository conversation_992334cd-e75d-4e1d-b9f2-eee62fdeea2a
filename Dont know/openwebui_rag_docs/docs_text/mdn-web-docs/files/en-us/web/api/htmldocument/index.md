Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md

Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmldocument > index.md --- title: HTMLDocument slug: Web/API/HTMLDocument page-type: web-api-interface browser-compat: api.HTMLDocument --- {{APIRef("HTML DOM")}} For historical reasons, {{domxref("Window")}} objects have a `window.HTMLDocument` property whose value is the {{DOMxRef("Document")}} interface. So you can think of `HTMLDocument` as an alias for {{DOMxRef("Document")}}, and you can find documentation for `HTMLDocument` members under the documentation for the {{DOMxRef("Document")}} interface. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}