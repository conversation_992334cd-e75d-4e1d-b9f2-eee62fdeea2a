Path: mdn-web-docs > files > en-us > web > api > htmlelement > accesskey > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > accesskey > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > accesskey > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > accesskey > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > accesskey > index.md --- title: "HTMLElement: accessKey property" short-title: accessKey slug: Web/API/HTMLElement/accessKey page-type: web-api-instance-property browser-compat: api.HTMLElement.accessKey --- {{APIRef("DOM")}} The **`HTMLElement.accessKey`** property sets the keystroke which a user can press to jump to a given element. > [!NOTE] > The `HTMLElement.accessKey` property is seldom used because of its multiple conflicts with already present key bindings in browsers. To work around this, browsers implement accesskey behavior if the keys are pressed with other "qualifying" keys (such as <kbd>Alt</kbd> + accesskey). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [HTML global attribute: accesskey](/en-US/docs/Web/HTML/Reference/Global_attributes/accesskey)