Path: mdn-web-docs > files > en-us > web > api > htmlelement > drag_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > drag_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > drag_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > drag_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > drag_event > index.md --- title: "HTMLElement: drag event" short-title: drag slug: Web/API/HTMLElement/drag_event page-type: web-api-event browser-compat: api.HTMLElement.drag_event --- {{APIRef}} The `drag` event is fired every few hundred milliseconds as an element or text selection is being dragged by the user. This event is cancelable and may bubble up to the {{domxref("Document")}} and {{domxref("Window")}} objects. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("drag", (event) => { }) ondrag = (event) => { } ``` ## Event type A {{domxref("DragEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("DragEvent")}} ## Event properties _In addition to the properties listed below, properties from the parent interface, {{domxref("Event")}}, are available._ - {{domxref('DragEvent.dataTransfer')}} {{ReadOnlyInline}} - : The data that is transferred during a drag and drop interaction. ## Examples ### Drag and drop example #### HTML ```html <div class="dropzone"> <div id="draggable" draggable="true">This div is draggable</div> </div> <div class="dropzone" id="drop-target"></div> ``` #### CSS ```css body { /* Prevent the user selecting text in the example */ user-select: none; } #draggable { text-align: center; background: white; } .dropzone { width: 200px; height: 20px; background: blueviolet; margin: 10px; padding: 10px; } .dropzone.dragover { background-color: purple; } .dragging { opacity: 0.5; } ``` #### JavaScript ```js let dragged; /* events fired on the draggable target */ const source = document.getElementById("draggable"); source.addEventListener("drag", (event) => { console.log("dragging"); }); source.addEventListener("dragstart", (event) => { // store a ref. on the dragged elem dragged = event.target; // make it half transparent event.target.classList.add("dragging"); }); source.addEventListener("dragend", (event) => { // reset the transparency event.target.classList.remove("dragging"); }); /* events fired on the drop targets */ const target = document.getElementById("drop-target"); target.addEventListener( "dragover", (event) => { // prevent default to allow drop event.preventDefault(); }, false, ); target.addEventListener("dragenter", (event) => { // highlight potential drop target when the draggable element enters it if (event.target.classList.contains("dropzone")) { event.target.classList.add("dragover"); } }); target.addEventListener("dragleave", (event) => { // reset background of potential drop target when the draggable element leaves it if (event.target.classList.contains("dropzone")) { event.target.classList.remove("dragover"); } }); target.addEventListener("drop", (event) => { // prevent default action (open as link for some elements) event.preventDefault(); // move dragged element to the selected drop target if (event.target.classList.contains("dropzone")) { event.target.classList.remove("dragover"); event.target.appendChild(dragged); } }); ``` #### Result {{EmbedLiveSample('Drag and drop example')}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLElement/dragstart_event", "dragstart")}} - {{domxref("HTMLElement/dragend_event", "dragend")}} - {{domxref("HTMLElement/dragover_event", "dragover")}} - {{domxref("HTMLElement/dragenter_event", "dragenter")}} - {{domxref("HTMLElement/dragleave_event", "dragleave")}} - {{domxref("HTMLElement/drop_event", "drop")}}