Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragenter_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragenter_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragenter_event > index.md --- title: "HTMLElement: dragenter event" short-title: dragenter slug: Web/API/HTMLElement/dragenter_event page-type: web-api-event browser-compat: api.HTMLElement.dragenter_event --- {{APIRef}} The `dragenter` event is fired when a dragged element or text selection enters a valid drop target. The target object is the _immediate user selection_ (the element directly indicated by the user as the drop target), or the {{HTMLElement("body")}} element. This event is cancelable and may bubble up to the {{domxref("Document")}} and {{domxref("Window")}} objects. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("dragenter", (event) => { }) ondragenter = (event) => { } ``` ## Event type A {{domxref("DragEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("DragEvent")}} ## Event properties _In addition to the properties listed below, properties from the parent interface, {{domxref("Event")}}, are available._ - {{domxref('DragEvent.dataTransfer')}} {{ReadOnlyInline}} - : The data that is transferred during a drag-and-drop interaction. ## Examples ### Styling drop zones on dragenter In this example, we have a draggable element inside a container. Try grabbing the element, dragging it over the other container, and releasing it. We listen for the `dragenter` event to give the other container a purple background while the draggable element is over it to signal that the draggable element could be dropped onto the container. However, in this partial example, we haven't implemented dropping: for a complete example of drag and drop, see the page for the [`drag`](/en-US/docs/Web/API/HTMLElement/drag_event) event. #### HTML ```html <div class="dropzone"> <div id="draggable" draggable="true">This div is draggable</div> </div> <div class="dropzone" id="drop-target"></div> ``` #### CSS ```css body { /* Prevent the user from selecting text in the example */ user-select: none; } #draggable { text-align: center; background: white; } .dropzone { width: 200px; height: 20px; background: blueviolet; margin: 10px; padding: 10px; } .dropzone.dragover { background-color: purple; } ``` #### JavaScript ```js const target = document.getElementById("drop-target"); target.addEventListener("dragenter", (event) => { // highlight potential drop target when the draggable element enters it if (event.target.classList.contains("dropzone")) { event.target.classList.add("dragover"); } }); target.addEventListener("dragleave", (event) => { // reset background of potential drop target when the draggable element leaves it if (event.target.classList.contains("dropzone")) { event.target.classList.remove("dragover"); } }); ``` #### Result {{EmbedLiveSample('Styling drop zones on dragenter')}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Other drag and drop events: - {{domxref("HTMLElement/drag_event", "drag")}} - {{domxref("HTMLElement/dragstart_event", "dragstart")}} - {{domxref("HTMLElement/dragend_event", "dragend")}} - {{domxref("HTMLElement/dragover_event", "dragover")}} - {{domxref("HTMLElement/dragleave_event", "dragleave")}} - {{domxref("HTMLElement/drop_event", "drop")}}