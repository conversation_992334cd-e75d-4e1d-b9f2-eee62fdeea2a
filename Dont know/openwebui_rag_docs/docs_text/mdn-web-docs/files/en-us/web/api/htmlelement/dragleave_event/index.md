Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragleave_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragleave_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragleave_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragleave_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragleave_event > index.md --- title: "HTMLElement: dragleave event" short-title: dragleave slug: Web/API/HTMLElement/dragleave_event page-type: web-api-event browser-compat: api.HTMLElement.dragleave_event --- {{APIRef}} The `dragleave` event is fired when a dragged element or text selection leaves a valid drop target. This event is not cancelable and may bubble up to the {{domxref("Document")}} and {{domxref("Window")}} objects. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("dragleave", (event) => { }) ondragleave = (event) => { } ``` ## Event type A {{domxref("DragEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("DragEvent")}} ## Event properties _In addition to the properties listed below, properties from the parent interface, {{domxref("Event")}}, are available._ - {{domxref('DragEvent.dataTransfer')}} {{ReadOnlyInline}} - : The data that is transferred during a drag-and-drop interaction. ## Examples ### Resetting drop zone styles on dragleave In this example, we have a draggable element inside a container. Try grabbing the element, dragging it over the other container, and releasing it. We give the other container a purple background while the draggable element is over to signal that it could be dropped onto the container. We listen for the `dragleave` event to reset the container background when dragging the draggable element off the container. However, in this partial example, we haven't implemented dropping: for a complete example of drag and drop, see the page for the [`drag`](/en-US/docs/Web/API/HTMLElement/drag_event) event. #### HTML ```html <div class="dropzone"> <div id="draggable" draggable="true">This div is draggable</div> </div> <div class="dropzone" id="drop-target"></div> ``` #### CSS ```css body { /* Prevent the user from selecting text in the example */ user-select: none; } #draggable { text-align: center; background: white; } .dropzone { width: 200px; height: 20px; background: blueviolet; margin: 10px; padding: 10px; } .dropzone.dragover { background-color: purple; } ``` #### JavaScript ```js const target = document.getElementById("drop-target"); target.addEventListener("dragenter", (event) => { // highlight potential drop target when the draggable element enters it if (event.target.classList.contains("dropzone")) { event.target.classList.add("dragover"); } }); target.addEventListener("dragleave", (event) => { // reset background of potential drop target when the draggable element leaves it if (event.target.classList.contains("dropzone")) { event.target.classList.remove("dragover"); } }); ``` #### Result {{EmbedLiveSample('Resetting drop zone styles on dragleave')}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Other drag and drop events: - {{domxref("HTMLElement/drag_event", "drag")}} - {{domxref("HTMLElement/dragstart_event", "dragstart")}} - {{domxref("HTMLElement/dragend_event", "dragend")}} - {{domxref("HTMLElement/dragover_event", "dragover")}} - {{domxref("HTMLElement/dragenter_event", "dragenter")}} - {{domxref("HTMLElement/drop_event", "drop")}}