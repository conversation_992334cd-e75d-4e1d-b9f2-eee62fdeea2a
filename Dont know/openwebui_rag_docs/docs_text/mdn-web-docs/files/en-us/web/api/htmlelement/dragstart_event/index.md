Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragstart_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragstart_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > dragstart_event > index.md --- title: "HTMLElement: dragstart event" short-title: dragstart slug: Web/API/HTMLElement/dragstart_event page-type: web-api-event browser-compat: api.HTMLElement.dragstart_event --- {{APIRef}} The `dragstart` event is fired when the user starts dragging an element or text selection. This event is cancelable and may bubble up to the {{domxref("Document")}} and {{domxref("Window")}} objects. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("dragstart", (event) => { }) ondragstart = (event) => { } ``` ## Event type A {{domxref("DragEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("DragEvent")}} ## Event properties _In addition to the properties listed below, properties from the parent interface, {{domxref("Event")}}, are available._ - {{domxref('DragEvent.dataTransfer')}} {{ReadOnlyInline}} - : The data that is transferred during a drag-and-drop interaction. ## Examples ### Setting opacity on drag start In this example, we have a draggable element inside a container. Try grabbing the element, dragging it, and then releasing it. We listen for the `dragstart` event to make the element half transparent while dragged. For a complete example of drag and drop, see the page for the [`drag`](/en-US/docs/Web/API/HTMLElement/drag_event) event. #### HTML ```html <div id="container"> <div id="draggable" draggable="true">This div is draggable</div> </div> <div class="dropzone"></div> ``` #### CSS ```css body { /* Prevent the user from selecting text in the example */ user-select: none; } #draggable { text-align: center; background: white; } #container { width: 200px; height: 20px; background: blueviolet; padding: 10px; } .dragging { opacity: 0.5; } ``` #### JavaScript ```js const source = document.getElementById("draggable"); source.addEventListener("dragstart", (event) => { // make it half transparent event.target.classList.add("dragging"); }); source.addEventListener("dragend", (event) => { // reset the transparency event.target.classList.remove("dragging"); }); ``` #### Result {{EmbedLiveSample('Setting opacity on drag start')}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Other drag and drop events: - {{domxref("HTMLElement/drag_event", "drag")}} - {{domxref("HTMLElement/dragend_event", "dragend")}} - {{domxref("HTMLElement/dragover_event", "dragover")}} - {{domxref("HTMLElement/dragenter_event", "dragenter")}} - {{domxref("HTMLElement/dragleave_event", "dragleave")}} - {{domxref("HTMLElement/drop_event", "drop")}}