Path: mdn-web-docs > files > en-us > web > api > htmlelement > error_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlelement > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlelement > error_event > index.md --- title: "HTMLElement: error event" short-title: error slug: Web/API/HTMLElement/error_event page-type: web-api-event browser-compat: api.HTMLElement.error_event --- {{APIRef}} The `error` event is fired on an element when a resource failed to load, or can't be used. For example, if a script has an execution error or an image can't be found or is invalid. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("error", (event) => { }) onerror = (event) => { } ``` ## Event type The event object is a {{domxref("UIEvent")}} instance if it was generated from a user interface element, or an {{domxref("Event")}} instance otherwise. {{InheritanceDiagram("UIEvent")}} ## Examples ### Live example #### HTML ```html <div class="controls"> <button id="img-error" type="button">Generate image error</button> <img class="bad-img" /> </div> <div class="event-log"> <label for="eventLog">Event log:</label> <textarea readonly class="event-log-contents" rows="8" cols="30" id="eventLog"></textarea> </div> ``` ```css hidden body { display: grid; grid-template-areas: "control log"; } .controls { grid-area: control; display: flex; align-items: center; justify-content: center; } .event-log { grid-area: log; } .event-log-contents { resize: none; } label, button { display: block; } button { height: 2rem; margin: 0.5rem; } img { width: 0; height: 0; } ``` #### JavaScript ```js const log = document.querySelector(".event-log-contents"); const badImg = document.querySelector(".bad-img"); badImg.addEventListener("error", (event) => { log.textContent += `${event.type}: Loading image\n`; console.log(event); }); const imgError = document.querySelector("#img-error"); imgError.addEventListener("click", () => { badImg.setAttribute("src", "i-dont-exist"); }); ``` #### Result {{ EmbedLiveSample('Live_example', '100%', '150px') }} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Related events - Window: {{domxref("Window/error_event", "error")}} event - HTMLElement: {{domxref("HTMLElement/load_event", "load")}} event