Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > form > index.md --- title: "HTMLFieldSetElement: form property" short-title: form slug: Web/API/HTMLFieldSetElement/form page-type: web-api-instance-property browser-compat: api.HTMLFieldSetElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLFieldSetElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("fieldset")}}, or `null` if this fieldset is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLFieldSetElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("fieldset")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/fieldset#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)