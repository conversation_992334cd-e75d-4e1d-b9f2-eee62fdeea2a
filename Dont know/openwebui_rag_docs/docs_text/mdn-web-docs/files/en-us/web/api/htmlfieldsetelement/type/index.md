Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > type > index.md --- title: "HTMLFieldSetElement: type property" short-title: type slug: Web/API/HTMLFieldSetElement/type page-type: web-api-instance-property browser-compat: api.HTMLFieldSetElement.type --- {{ApiRef("HTML DOM")}} The **`type`** read-only property of the {{domxref("HTMLFieldSetElement")}} interface returns the string `"fieldset"`. ## Value The string `"fieldset"`. ## Example ```js const fs = document.querySelector("fieldset"); console.log(fs.type); // "fieldset" ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLFieldSetElement")}} - {{HTMLElement("fieldset")}}