Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfieldsetelement > willvalidate > index.md --- title: "HTMLFieldSetElement: willValidate property" short-title: willValidate slug: Web/API/HTMLFieldSetElement/willValidate page-type: web-api-instance-property browser-compat: api.HTMLFieldSetElement.willValidate --- {{APIRef("HTML DOM")}} The **`willValidate`** read-only property of the {{domxref("HTMLFieldSetElement")}} interface returns `false`, because {{HTMLElement("fieldset")}} elements are not candidates for [constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation). ## Value The boolean value `false`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLFieldSetElement.checkValidity()")}} - {{HTMLElement("fieldset")}} - {{HTMLElement("form")}} - [Learn: Client-side form validation](/en-US/docs/Learn_web_development/Extensions/Forms/Form_validation) - [Guide: Constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation)