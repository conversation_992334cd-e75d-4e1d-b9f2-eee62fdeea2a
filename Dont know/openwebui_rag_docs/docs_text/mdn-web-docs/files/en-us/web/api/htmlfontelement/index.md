Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlfontelement > index.md --- title: HTMLFontElement slug: Web/API/HTMLFontElement page-type: web-api-interface status: - deprecated browser-compat: api.HTMLFontElement --- {{APIRef("HTML DOM")}}{{Deprecated_Header}} Implements the document object model (DOM) representation of the font element. The HTML Font Element {{HTMLElement("font")}} defines the font size, font face and color of text. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLFontElement.color")}} {{Deprecated_Inline}} - : A string that reflects the [`color`](/en-US/docs/Web/HTML/Reference/Elements/font#color) HTML attribute, containing either a named color or a color specified in the hexadecimal #RRGGBB format. - {{domxref("HTMLFontElement.face")}} {{Deprecated_Inline}} - : A string that reflects the [`face`](/en-US/docs/Web/HTML/Reference/Elements/font#face) HTML attribute, containing a comma-separated list of one or more font names. - {{domxref("HTMLFontElement.size")}} {{Deprecated_Inline}} - : A string that reflects the [`size`](/en-US/docs/Web/HTML/Reference/Elements/font#size) HTML attribute, containing either a font size ranging from 1 to 7 or a number relative to the default value 3, for example -2 or +1. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("font")}}.