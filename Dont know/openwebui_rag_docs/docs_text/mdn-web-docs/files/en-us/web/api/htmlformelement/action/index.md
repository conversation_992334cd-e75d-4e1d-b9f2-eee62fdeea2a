Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > action > index.md --- title: "HTMLFormElement: action property" short-title: action slug: Web/API/HTMLFormElement/action page-type: web-api-instance-property browser-compat: api.HTMLFormElement.action --- {{APIRef("HTML DOM")}} The **`HTMLFormElement.action`** property represents the action of the {{HTMLElement("form")}} element. The action of a form is the program that is executed on the server when the form is submitted. This property can be retrieved or set. ## Value A string. ## Examples ```js form.action = "/cgi-bin/publish"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}