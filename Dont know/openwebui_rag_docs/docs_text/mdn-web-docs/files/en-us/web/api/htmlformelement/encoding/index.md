Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > encoding > index.md --- title: "HTMLFormElement: encoding property" short-title: encoding slug: Web/API/HTMLFormElement/encoding page-type: web-api-instance-property browser-compat: api.HTMLFormElement.encoding --- {{APIRef("HTML DOM")}} The **`HTMLFormElement.encoding`** property is an alternative name for the {{domxref("HTMLFormElement.enctype","enctype")}} element on the DOM {{domxref("HTMLFormElement")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}