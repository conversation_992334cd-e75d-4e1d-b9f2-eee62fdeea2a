Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md Path: mdn-web-docs > files > en-us > web > api > htmlformelement > target > index.md --- title: "HTMLFormElement: target property" short-title: target slug: Web/API/HTMLFormElement/target page-type: web-api-instance-property browser-compat: api.HTMLFormElement.target --- {{APIRef("HTML DOM")}} The **`target`** property of the {{domxref("HTMLFormElement")}} interface represents the target of the form's action (i.e., the frame in which to render its output). ## Value A string. ## Examples ```js myForm.target = document.frames[1].name; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}