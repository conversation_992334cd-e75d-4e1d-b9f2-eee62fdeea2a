Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadelement > index.md --- title: HTMLHeadElement slug: Web/API/HTMLHeadElement page-type: web-api-interface browser-compat: api.HTMLHeadElement --- {{APIRef("HTML DOM")}} The **`HTMLHeadElement`** interface contains the descriptive information, or metadata, for a document. This object inherits all of the properties and methods described in the {{domxref("HTMLElement")}} interface. {{InheritanceDiagram}} ## Instance properties _No specific properties; inherits properties from its parent, {{domxref("HTMLElement")}}._ ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("head")}}