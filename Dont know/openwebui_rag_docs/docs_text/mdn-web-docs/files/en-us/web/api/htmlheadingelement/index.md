Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlheadingelement > index.md --- title: HTMLHeadingElement slug: Web/API/HTMLHeadingElement page-type: web-api-interface browser-compat: api.HTMLHeadingElement --- {{ APIRef("HTML DOM") }} The **`HTMLHeadingElement`** interface represents the different heading elements, [`<h1>` through `<h6>`](/en-US/docs/Web/HTML/Reference/Elements/Heading_Elements). It inherits methods and properties from the {{domxref("HTMLElement")}} interface. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLHeadingElement.align")}} {{deprecated_inline}} - : A string representing an enumerated attribute indicating alignment of the heading with respect to the surrounding context. The possible values are `"left"`, `"right"`, `"justify"`, and `"center"`. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{HTMLElement("Heading_Elements", "HTML heading elements")}}, `<h1>` to `<h6>`, which correspond to this interface.