Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhrelement > index.md --- title: HTMLHRElement slug: Web/API/HTMLHRElement page-type: web-api-interface browser-compat: api.HTMLHRElement --- {{APIRef("HTML DOM")}} The **`HTMLHRElement`** interface provides special properties (beyond those of the {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating {{HTMLElement("hr")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLHRElement.align")}} {{deprecated_inline}} - : A string, an enumerated attribute indicating alignment of the rule with respect to the surrounding context. - {{domxref("HTMLHRElement.color")}} {{deprecated_inline}} - : A string representing the name of the color of the rule. - {{domxref("HTMLHRElement.noShade")}} {{deprecated_inline}} - : A boolean value that sets the rule to have no shading. - {{domxref("HTMLHRElement.size")}} {{deprecated_inline}} - : A string representing the height of the rule. - {{domxref("HTMLHRElement.width")}} {{deprecated_inline}} - : A string representing the width of the rule on the page. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}_. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("hr")}}