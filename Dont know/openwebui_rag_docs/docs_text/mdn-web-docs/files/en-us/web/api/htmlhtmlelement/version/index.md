Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md Path: mdn-web-docs > files > en-us > web > api > htmlhtmlelement > version > index.md --- title: "HTMLHtmlElement: version property" short-title: version slug: Web/API/HTMLHtmlElement/version page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLHtmlElement.version --- {{ APIRef("HTML DOM") }} {{deprecated_header}} > [!NOTE] > This property has been declared as deprecated by the W3C technical recommendation for HTML 4.01 in favor of use of the DTD for obtaining version information for a document. Returns version information about the document type definition (DTD) of a document. While this property is recognized by Mozilla, the return value for this property is always an empty string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}