Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > allowpaymentrequest > index.md

Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > allowpaymentrequest > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > allowpaymentrequest > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > allowpaymentrequest > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > allowpaymentrequest > index.md --- title: "HTMLIFrameElement: allowPaymentRequest property" short-title: allowPaymentRequest slug: Web/API/HTMLIFrameElement/allowPaymentRequest page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.HTMLIFrameElement.allowPaymentRequest --- {{APIRef("HTML DOM")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`allowPaymentRequest`** property of the {{domxref("HTMLIFrameElement")}} interface returns a boolean value indicating whether the [Payment Request API](/en-US/docs/Web/API/Payment_Request_API) may be invoked on a cross-origin iframe. ## Value A boolean value. ## Browser compatibility {{Compat}} ## See also - [Payment Request API](/en-US/docs/Web/API/Payment_Request_API)