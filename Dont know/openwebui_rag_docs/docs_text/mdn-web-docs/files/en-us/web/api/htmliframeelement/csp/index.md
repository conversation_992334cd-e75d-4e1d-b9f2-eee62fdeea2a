Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > csp > index.md

Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > csp > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > csp > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > csp > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > csp > index.md --- title: "HTMLIFrameElement: csp property" short-title: csp slug: Web/API/HTMLIFrameElement/csp page-type: web-api-instance-property status: - experimental browser-compat: api.HTMLIFrameElement.csp --- {{APIRef("HTML DOM")}}{{SeeCompatTable}} The **`csp`** property of the {{domxref("HTMLIFrameElement")}} interface specifies the [Content Security Policy](/en-US/docs/Web/HTTP/Guides/CSP) that an embedded document must agree to enforce upon itself. ## Value A content security policy. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}