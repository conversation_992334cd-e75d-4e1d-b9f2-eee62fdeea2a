Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > featurepolicy > index.md

Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > featurepolicy > index.md Path: mdn-web-docs > files > en-us > web > api > htmliframeelement > featurepolicy > index.md --- title: "HTMLIFrameElement: featurePolicy property" short-title: featurePolicy slug: Web/API/HTMLIFrameElement/featurePolicy page-type: web-api-instance-property status: - experimental browser-compat: api.HTMLIFrameElement.featurePolicy --- {{APIRef("Feature Policy API")}}{{SeeCompatTable}} The **`featurePolicy`** read-only property of the {{DOMxRef("HTMLIFrameElement")}} interface returns the {{DOMxRef("FeaturePolicy")}} interface which provides a simple API for introspecting the [Permissions Policies](/en-US/docs/Web/HTTP/Guides/Permissions_Policy) applied to a specific frame. ## Value A [`FeaturePolicy`](/en-US/docs/Web/API/FeaturePolicy) object that can be used to inspect the Permissions Policy settings applied to the frame. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}