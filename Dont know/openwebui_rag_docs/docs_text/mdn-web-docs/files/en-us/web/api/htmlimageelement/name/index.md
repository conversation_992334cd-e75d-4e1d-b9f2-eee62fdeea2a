Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlimageelement > name > index.md --- title: "HTMLImageElement: name property" short-title: name slug: Web/API/HTMLImageElement/name page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLImageElement.name --- {{APIRef("HTML DOM")}}{{deprecated_header}} The {{domxref("HTMLImageElement")}} interface's _deprecated_ **`name`** property specifies a name for the element. This has been replaced by the {{domxref("Element.id", "id")}} property available on all elements. ## Value A string providing a name by which the image can be referenced. > [!WARNING] > This property is deprecated and is only in the > specification still for backward compatibility purposes. Since it functions > identically to [`id`](/en-US/docs/Web/HTML/Reference/Global_attributes/id), you can and should use it instead. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}