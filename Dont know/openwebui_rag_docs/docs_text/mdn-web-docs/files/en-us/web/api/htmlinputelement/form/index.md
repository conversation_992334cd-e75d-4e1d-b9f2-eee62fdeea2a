Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > form > index.md --- title: "HTMLInputElement: form property" short-title: form slug: Web/API/HTMLInputElement/form page-type: web-api-instance-property browser-compat: api.HTMLInputElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLInputElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{HTMLElement("input")}}, or `null` if this input is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLInputElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("input")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/input#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)