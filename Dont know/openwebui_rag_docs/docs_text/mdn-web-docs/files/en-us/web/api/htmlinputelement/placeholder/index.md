Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > placeholder > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmlinputelement > placeholder > index.md --- title: "HTMLInputElement: placeholder property" short-title: placeholder slug: Web/API/HTMLInputElement/placeholder page-type: web-api-instance-property browser-compat: api.HTMLInputElement.placeholder --- {{ APIRef("HTML DOM") }} The **`placeholder`** property of the {{DOMxRef("HTMLInputElement")}} interface represents a hint to the user of what can be entered in the control. It reflects the {{htmlelement("input")}} element's [`placeholder`](/en-US/docs/Web/HTML/Reference/Elements/input#placeholder) attribute. ## Value A string. ## Examples ```js const inputElement = document.getElementById("phone"); console.log(input.placeholder); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{HTMLElement("input")}} - {{DOMXref("HTMLInputElement.value")}} - {{cssxref("::placeholder")}} pseudo-element - {{CSSXref(":placeholder-shown")}} pseudo-class