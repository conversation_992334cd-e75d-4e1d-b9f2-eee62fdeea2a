Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmllegendelement > form > index.md --- title: "HTMLLegendElement: form property" short-title: form slug: Web/API/HTMLLegendElement/form page-type: web-api-instance-property browser-compat: api.HTMLLegendElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLLegendElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns the {{domxref("HTMLFieldSetElement")}} associated with this {{htmlelement("legend")}}, or `null` if this legend is not associated with a {{htmlelement("fieldset")}} owned by a form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLLegendElement")}} - {{domxref("HTMLFieldSetElement.form")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("legend")}} - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)