Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controller > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controller > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controller > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controller > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controller > index.md --- title: "HTMLMediaElement: controller property" short-title: controller slug: Web/API/HTMLMediaElement/controller page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.HTMLMediaElement.controller --- {{APIRef("HTML DOM")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`HTMLMediaElement.controller`** property represents the media controller assigned to the element. ## Value A `MediaController` object or `null` if no media controller is assigned to the element. The default is `null`. ## Specifications In 2016, the whole Media Controller feature was [removed from the HTML specification](https://github.com/w3c/html/issues/246). It is no longer on track to become a standard. ## Browser compatibility {{Compat}}