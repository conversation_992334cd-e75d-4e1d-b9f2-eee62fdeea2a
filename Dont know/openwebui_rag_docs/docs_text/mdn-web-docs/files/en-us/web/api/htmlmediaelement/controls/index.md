Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controls > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controls > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controls > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controls > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > controls > index.md --- title: "HTMLMediaElement: controls property" short-title: controls slug: Web/API/HTMLMediaElement/controls page-type: web-api-instance-property browser-compat: api.HTMLMediaElement.controls --- {{APIRef("HTML DOM")}} The **`HTMLMediaElement.controls`** property reflects the [`controls`](/en-US/docs/Web/HTML/Reference/Elements/video#controls) HTML attribute, which controls whether user interface controls for playing the media item will be displayed. ## Value A boolean value. A value of `true` means controls will be displayed. ## Examples ```js const obj = document.createElement("video"); obj.controls = true; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLMediaElement")}}: Interface used to define the `HTMLMediaElement.controls` property