Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > durationchange_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > durationchange_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > durationchange_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > durationchange_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > durationchange_event > index.md --- title: "HTMLMediaElement: durationchange event" short-title: durationchange slug: Web/API/HTMLMediaElement/durationchange_event page-type: web-api-event browser-compat: api.HTMLMediaElement.durationchange_event --- {{APIRef("HTMLMediaElement")}} The `durationchange` event is fired when the `duration` attribute has been updated. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("durationchange", (event) => { }) ondurationchange = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Examples These examples add an event listener for the HTMLMediaElement's `durationchange` event, then post a message when that event handler has reacted to the event firing. Using `addEventListener()`: ```js const video = document.querySelector("video"); video.addEventListener("durationchange", (event) => { console.log("Not sure why, but the duration of the video has changed."); }); ``` Using the `ondurationchange` event handler property: ```js const video = document.querySelector("video"); video.ondurationchange = (event) => { console.log("Not sure why, but the duration of the video has changed."); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## Related Events - The HTMLMediaElement {{domxref("HTMLMediaElement.playing_event", 'playing')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.waiting_event", 'waiting')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeking_event", 'seeking')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeked_event", 'seeked')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ended_event", 'ended')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadedmetadata_event", 'loadedmetadata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadeddata_event", 'loadeddata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplay_event", 'canplay')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplaythrough_event", 'canplaythrough')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.timeupdate_event", 'timeupdate')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.play_event", 'play')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.pause_event", 'pause')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ratechange_event", 'ratechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.volumechange_event", 'volumechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.suspend_event", 'suspend')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.emptied_event", 'emptied')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.stalled_event", 'stalled')}} event ## See also - {{domxref("HTMLAudioElement")}} - {{domxref("HTMLVideoElement")}} - {{HTMLElement("audio")}} - {{HTMLElement("video")}}