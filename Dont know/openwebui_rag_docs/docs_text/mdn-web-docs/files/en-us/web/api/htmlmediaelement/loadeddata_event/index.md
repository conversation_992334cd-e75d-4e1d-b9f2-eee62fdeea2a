Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loadeddata_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loadeddata_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loadeddata_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loadeddata_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loadeddata_event > index.md --- title: "HTMLMediaElement: loadeddata event" short-title: loadeddata slug: Web/API/HTMLMediaElement/loadeddata_event page-type: web-api-event browser-compat: api.HTMLMediaElement.loadeddata_event --- {{APIRef("HTMLMediaElement")}} The **`loadeddata`** event is fired when the frame at the current playback position of the media has finished loading; often the first frame. > [!NOTE] > This event will not fire in mobile/tablet devices if data-saver is on in browser settings. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("loadeddata", (event) => { }) onloadeddata = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Examples These examples add an event listener for the HTMLMediaElement's `loadeddata` event, then post a message when that event handler has reacted to the event firing. Using `addEventListener()`: ```js const video = document.querySelector("video"); video.addEventListener("loadeddata", (event) => { console.log( "Yay! The readyState just increased to " + "HAVE_CURRENT_DATA or greater for the first time.", ); }); ``` Using the `onloadeddata` event handler property: ```js const video = document.querySelector("video"); video.onloadeddata = (event) => { console.log( "Yay! The readyState just increased to " + "HAVE_CURRENT_DATA or greater for the first time.", ); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## Related Events - The HTMLMediaElement {{domxref("HTMLMediaElement.playing_event", 'playing')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.waiting_event", 'waiting')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeking_event", 'seeking')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeked_event", 'seeked')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ended_event", 'ended')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadedmetadata_event", 'loadedmetadata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplay_event", 'canplay')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplaythrough_event", 'canplaythrough')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.durationchange_event", 'durationchange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.timeupdate_event", 'timeupdate')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.play_event", 'play')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.pause_event", 'pause')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ratechange_event", 'ratechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.volumechange_event", 'volumechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.suspend_event", 'suspend')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.emptied_event", 'emptied')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.stalled_event", 'stalled')}} event ## See also - {{domxref("HTMLAudioElement")}} - {{domxref("HTMLVideoElement")}} - {{HTMLElement("audio")}} - {{HTMLElement("video")}}