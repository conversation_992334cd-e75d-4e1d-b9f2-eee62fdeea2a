Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loop > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loop > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loop > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loop > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > loop > index.md --- title: "HTMLMediaElement: loop property" short-title: loop slug: Web/API/HTMLMediaElement/loop page-type: web-api-instance-property browser-compat: api.HTMLMediaElement.loop --- {{APIRef("HTML DOM")}} The **`HTMLMediaElement.loop`** property reflects the [`loop`](/en-US/docs/Web/HTML/Reference/Elements/video#loop) HTML attribute, which controls whether the media element should start over when it reaches the end. ## Value A boolean value. ## Examples ```js const obj = document.createElement("video"); obj.loop = true; // true ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLMediaElement")}}: Interface used to define the `HTMLMediaElement.loop` property