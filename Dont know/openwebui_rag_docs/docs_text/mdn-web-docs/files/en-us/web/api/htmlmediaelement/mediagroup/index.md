Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediagroup > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediagroup > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediagroup > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediagroup > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediagroup > index.md --- title: "HTMLMediaElement: mediaGroup property" short-title: mediaGroup slug: Web/API/HTMLMediaElement/mediaGroup page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.HTMLMediaElement.mediaGroup --- {{APIRef("HTML DOM")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`HTMLMediaElement.mediaGroup`** property reflects the `mediagroup` HTML attribute, which indicates the name of the group of elements it belongs to. A group of media elements shares a common `controller`. ## Value A string. ## Specifications In 2016, the whole Media Controller feature was [removed from the HTML specification](https://github.com/w3c/html/issues/246). It is no longer on track to become a standard. ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLMediaElement")}}: Interface used to define the `HTMLMediaElement.mediaGroup` property