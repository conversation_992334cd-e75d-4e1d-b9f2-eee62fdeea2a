Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediakeys > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > mediakeys > index.md --- title: "HTMLMediaElement: mediaKeys property" short-title: mediaKeys slug: Web/API/HTMLMediaElement/mediaKeys page-type: web-api-instance-property browser-compat: api.HTMLMediaElement.mediaKeys --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The read-only **`HTMLMediaElement.mediaKeys`** property returns a {{domxref("MediaKeys")}} object, that is a set of keys that the element can use for decryption of media data during playback. ## Value A {{domxref("MediaKeys")}} object, or `null` if no key is available. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaKeys")}}