Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > muted > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > muted > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > muted > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > muted > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > muted > index.md --- title: "HTMLMediaElement: muted property" short-title: muted slug: Web/API/HTMLMediaElement/muted page-type: web-api-instance-property browser-compat: api.HTMLMediaElement.muted --- {{APIRef("HTML DOM")}} The **`HTMLMediaElement.muted`** property indicates whether the media element is muted. ## Value A boolean value. `true` means muted and `false` means not muted. ## Examples ```js const obj = document.createElement("video"); console.log(obj.muted); // false ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLMediaElement")}}: Interface used to define the `HTMLMediaElement.muted` property - {{domxref("HTMLMediaElement.defaultMuted")}} - {{domxref("HTMLMediaElement.volume")}}