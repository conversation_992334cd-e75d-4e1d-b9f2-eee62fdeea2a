Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > pause > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > pause > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > pause > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > pause > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > pause > index.md --- title: "HTMLMediaElement: pause() method" short-title: pause() slug: Web/API/HTMLMediaElement/pause page-type: web-api-instance-method browser-compat: api.HTMLMediaElement.pause --- {{APIRef("HTML DOM")}} The **`HTMLMediaElement.pause()`** method will pause playback of the media, if the media is already in a paused state this method will have no effect. ## Syntax ```js-nolint pause() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ### Exceptions None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}