Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > paused > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > paused > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > paused > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > paused > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > paused > index.md --- title: "HTMLMediaElement: paused property" short-title: paused slug: Web/API/HTMLMediaElement/paused page-type: web-api-instance-property browser-compat: api.HTMLMediaElement.paused --- {{APIRef("HTML DOM")}} The read-only **`HTMLMediaElement.paused`** property tells whether the media element is paused. ## Value A boolean value. `true` is paused and `false` is not paused. ## Examples ```js const obj = document.createElement("video"); console.log(obj.paused); // true ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLMediaElement")}}: Interface used to define the `HTMLMediaElement.paused` property