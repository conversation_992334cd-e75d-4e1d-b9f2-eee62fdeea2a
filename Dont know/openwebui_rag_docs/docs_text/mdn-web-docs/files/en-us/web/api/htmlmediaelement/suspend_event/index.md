Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > suspend_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > suspend_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > suspend_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > suspend_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > suspend_event > index.md --- title: "HTMLMediaElement: suspend event" short-title: suspend slug: Web/API/HTMLMediaElement/suspend_event page-type: web-api-event browser-compat: api.HTMLMediaElement.suspend_event --- {{APIRef("HTMLMediaElement")}} The `suspend` event is fired when the user agent is intentionally not fetching media data, in which case {{domxref("HTMLMediaElement.networkState")}} is set to `HTMLMediaElement.NETWORK_IDLE`. This can happen if there's no more data to load, or if loading is unnecessary; for example, the browser may decide to only buffer 5 minutes of a video in advance, in which case loading is suspended until the user watches more of the video. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("suspend", (event) => { }) onsuspend = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Examples These examples add an event listener for the HTMLMediaElement's `suspend` event, then post a message when that event handler has reacted to the event firing. Using `addEventListener()`: ```js const video = document.querySelector("video"); video.addEventListener("suspend", (event) => { console.log("Data loading has been suspended."); }); ``` Using the `onsuspend` event handler property: ```js const video = document.querySelector("video"); video.onsuspend = (event) => { console.log("Data loading has been suspended."); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## Related Events - The HTMLMediaElement {{domxref("HTMLMediaElement.playing_event", 'playing')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.waiting_event", 'waiting')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeking_event", 'seeking')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeked_event", 'seeked')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ended_event", 'ended')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadedmetadata_event", 'loadedmetadata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadeddata_event", 'loadeddata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplay_event", 'canplay')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplaythrough_event", 'canplaythrough')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.durationchange_event", 'durationchange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.timeupdate_event", 'timeupdate')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.play_event", 'play')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.pause_event", 'pause')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ratechange_event", 'ratechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.volumechange_event", 'volumechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.emptied_event", 'emptied')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.stalled_event", 'stalled')}} event ## See also - {{domxref("HTMLAudioElement")}} - {{domxref("HTMLVideoElement")}} - {{HTMLElement("audio")}} - {{HTMLElement("video")}} - [Web Audio API](/en-US/docs/Web/API/Web_Audio_API)