Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > timeupdate_event > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > timeupdate_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > timeupdate_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > timeupdate_event > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmediaelement > timeupdate_event > index.md --- title: "HTMLMediaElement: timeupdate event" short-title: timeupdate slug: Web/API/HTMLMediaElement/timeupdate_event page-type: web-api-event browser-compat: api.HTMLMediaElement.timeupdate_event --- {{APIRef("HTMLMediaElement")}} The `timeupdate` event is fired when the time indicated by the `currentTime` attribute has been updated. The event frequency is dependent on the system load, but will be thrown between about 4Hz and 66Hz (assuming the event handlers don't take longer than 250ms to run). User agents are encouraged to vary the frequency of the event based on the system load and the average cost of processing the event each time, so that the UI updates are not any more frequent than the user agent can comfortably handle while decoding the video. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("timeupdate", (event) => { }) ontimeupdate = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Examples These examples add an event listener for the HTMLMediaElement's `timeupdate` event, then post a message when that event handler has reacted to the event firing. Remember, the event frequency is dependent on the system load. Using `addEventListener()`: ```js const video = document.querySelector("video"); video.addEventListener("timeupdate", (event) => { console.log("The currentTime attribute has been updated. Again."); }); ``` Using the `ontimeupdate` event handler property: ```js const video = document.querySelector("video"); video.ontimeupdate = (event) => { console.log("The currentTime attribute has been updated. Again."); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## Related Events - The HTMLMediaElement {{domxref("HTMLMediaElement.playing_event", 'playing')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.waiting_event", 'waiting')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeking_event", 'seeking')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.seeked_event", 'seeked')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ended_event", 'ended')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadedmetadata_event", 'loadedmetadata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.loadeddata_event", 'loadeddata')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplay_event", 'canplay')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.canplaythrough_event", 'canplaythrough')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.durationchange_event", 'durationchange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.play_event", 'play')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.pause_event", 'pause')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.ratechange_event", 'ratechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.volumechange_event", 'volumechange')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.suspend_event", 'suspend')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.emptied_event", 'emptied')}} event - The HTMLMediaElement {{domxref("HTMLMediaElement.stalled_event", 'stalled')}} event ## See also - {{domxref("HTMLAudioElement")}} - {{domxref("HTMLVideoElement")}} - {{HTMLElement("audio")}} - {{HTMLElement("video")}}