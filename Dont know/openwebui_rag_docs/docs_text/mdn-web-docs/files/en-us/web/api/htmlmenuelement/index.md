Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlmenuelement > index.md --- title: HTMLMenuElement slug: Web/API/HTMLMenuElement page-type: web-api-interface browser-compat: api.HTMLMenuElement --- {{APIRef("HTML DOM")}} The **`HTMLMenuElement`** interface provides additional properties (beyond those inherited from the {{domxref("HTMLElement")}} interface) for manipulating a {{HTMLElement("menu")}} element. `<menu>` is a semantic alternative to the {{HTMLElement("ul")}} element. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}, and its ancestors._ - {{domxref("HTMLMenuElement.compact")}} {{deprecated_inline}} - : A boolean determining if the menu displays compactly. ## Instance methods _Inherits methods from its parent, {{domxref("HTMLElement")}}, and its ancestors._ _`HTMLMenuElement` doesn't implement specific methods._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}