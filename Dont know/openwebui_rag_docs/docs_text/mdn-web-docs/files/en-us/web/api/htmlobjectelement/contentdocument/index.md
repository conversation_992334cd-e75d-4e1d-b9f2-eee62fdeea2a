Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentdocument > index.md --- title: "HTMLObjectElement: contentDocument property" short-title: contentDocument slug: Web/API/HTMLObjectElement/contentDocument page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.contentDocument --- {{APIRef("HTML DOM")}} The **`contentDocument`** read-only property of the {{domxref("HTMLObjectElement")}} interface Returns a {{domxref("Document")}} representing the active document of the object element's nested browsing context, if any; otherwise null. ## Value A {{domxref('Document')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}