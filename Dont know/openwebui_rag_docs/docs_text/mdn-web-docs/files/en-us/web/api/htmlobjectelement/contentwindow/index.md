Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > contentwindow > index.md --- title: "HTMLObjectElement: contentWindow property" short-title: contentWindow slug: Web/API/HTMLObjectElement/contentWindow page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.contentWindow --- {{APIRef("HTML DOM")}} The **`contentWindow`** read-only property of the {{domxref("HTMLObjectElement")}} interface returns a {{glossary("WindowProxy")}} representing the window proxy of the object element's nested browsing context, if any; otherwise null. ## Value A {{domxref('Window')}}, or `null` if there are none. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}