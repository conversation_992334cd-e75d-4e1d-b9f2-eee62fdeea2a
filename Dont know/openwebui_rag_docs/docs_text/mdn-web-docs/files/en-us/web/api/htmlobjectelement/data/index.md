Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > data > index.md --- title: "HTMLObjectElement: data property" short-title: data slug: Web/API/HTMLObjectElement/data page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.data --- {{APIRef("HTML DOM")}} The **`data`** property of the {{domxref("HTMLObjectElement")}} interface returns a string that reflects the [`data`](/en-US/docs/Web/HTML/Reference/Elements/object#data) HTML attribute, specifying the address of a resource's data. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}