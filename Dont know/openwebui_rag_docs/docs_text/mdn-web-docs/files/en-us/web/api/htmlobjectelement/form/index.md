Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > form > index.md --- title: "HTMLObjectElement: form property" short-title: form slug: Web/API/HTMLObjectElement/form page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLObjectElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("object")}}, or `null` if this object element is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLObjectElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("object")}} - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)