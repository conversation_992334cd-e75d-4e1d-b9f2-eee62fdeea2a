Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > height > index.md --- title: "HTMLObjectElement: height property" short-title: height slug: Web/API/HTMLObjectElement/height page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.height --- {{APIRef("HTML DOM")}} The **`height`** property of the {{domxref("HTMLObjectElement")}} interface Returns a string that reflects the [`height`](/en-US/docs/Web/HTML/Reference/Elements/object#height) HTML attribute, specifying the displayed height of the resource in CSS pixels. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLCanvasElement.height")}} - {{domxref("HTMLEmbedElement.height")}} - {{domxref("HTMLIFrameElement.height")}} - {{domxref("HTMLImageElement.height")}} - {{domxref("HTMLSourceElement.height")}} - {{domxref("HTMLVideoElement.height")}}