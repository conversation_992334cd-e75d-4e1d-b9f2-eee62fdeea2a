Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > name > index.md --- title: "HTMLObjectElement: name property" short-title: name slug: Web/API/HTMLObjectElement/name page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.name --- {{APIRef("HTML DOM")}} The **`name`** property of the {{domxref("HTMLObjectElement")}} interface returns a string that reflects the [`name`](/en-US/docs/Web/HTML/Reference/Elements/object#name) HTML attribute, specifying the name of the browsing context. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}