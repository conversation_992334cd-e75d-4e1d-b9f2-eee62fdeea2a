Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > type > index.md --- title: "HTMLObjectElement: type property" short-title: type slug: Web/API/HTMLObjectElement/type page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.type --- {{APIRef("HTML DOM")}} The **`type`** property of the {{domxref("HTMLObjectElement")}} interface returns a string that reflects the [`type`](/en-US/docs/Web/HTML/Reference/Elements/object#type) HTML attribute, specifying the MIME type of the resource. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}