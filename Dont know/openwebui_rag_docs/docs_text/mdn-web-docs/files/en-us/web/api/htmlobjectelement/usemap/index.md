Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > usemap > index.md --- title: "HTMLObjectElement: useMap property" short-title: useMap slug: Web/API/HTMLObjectElement/useMap page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLObjectElement.useMap --- {{APIRef("HTML DOM")}}{{deprecated_header}} The **`useMap`** property of the {{domxref("HTMLObjectElement")}} interface returns a string that reflects the [`usemap`](/en-US/docs/Web/HTML/Reference/Elements/object#usemap) HTML attribute, specifying a {{HTMLElement("map")}} element to use. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}