Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > validationmessage > index.md --- title: "HTMLObjectElement: validationMessage property" short-title: validationMessage slug: Web/API/HTMLObjectElement/validationMessage page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.validationMessage --- {{APIRef("HTML DOM")}} The **`validationMessage`** read-only property of the {{domxref("HTMLObjectElement")}} interface returns a string representing a localized message that describes the validation constraints that the control does not satisfy (if any). This is the empty string if the control is not a candidate for constraint validation (willValidate is false), or it satisfies its constraints. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}