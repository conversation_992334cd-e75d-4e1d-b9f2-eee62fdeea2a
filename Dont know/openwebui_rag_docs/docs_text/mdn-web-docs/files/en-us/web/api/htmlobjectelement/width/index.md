Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > width > index.md --- title: "HTMLObjectElement: width property" short-title: width slug: Web/API/HTMLObjectElement/width page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.width --- {{APIRef("HTML DOM")}} The **`width`** property of the {{domxref("HTMLObjectElement")}} interface returns a string that reflects the [`width`](/en-US/docs/Web/HTML/Reference/Elements/object#width) HTML attribute, specifying the displayed width of the resource in CSS pixels. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLCanvasElement.width")}} - {{domxref("HTMLEmbedElement.width")}} - {{domxref("HTMLIFrameElement.width")}} - {{domxref("HTMLImageElement.width")}} - {{domxref("HTMLSourceElement.width")}} - {{domxref("HTMLVideoElement.width")}}