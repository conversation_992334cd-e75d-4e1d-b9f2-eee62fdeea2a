Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmlobjectelement > willvalidate > index.md --- title: "HTMLObjectElement: willValidate property" short-title: willValidate slug: Web/API/HTMLObjectElement/willValidate page-type: web-api-instance-property browser-compat: api.HTMLObjectElement.willValidate --- {{APIRef("HTML DOM")}} The **`willValidate`** read-only property of the {{domxref("HTMLObjectElement")}} interface returns `false`, because {{HTMLElement("object")}} elements are not candidates for [constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation). ## Value The boolean value `false`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLObjectElement.checkValidity()")}} - {{HTMLElement("object")}} - {{HTMLElement("form")}} - [Learn: Client-side form validation](/en-US/docs/Learn_web_development/Extensions/Forms/Form_validation) - [Guide: Constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation)