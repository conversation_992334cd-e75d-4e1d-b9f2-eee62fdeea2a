Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptgroupelement > index.md --- title: HTMLOptGroupElement slug: Web/API/HTMLOptGroupElement page-type: web-api-interface browser-compat: api.HTMLOptGroupElement --- {{ APIRef("HTML DOM") }} The **`HTMLOptGroupElement`** interface provides special properties and methods (beyond the regular {{domxref("HTMLElement")}} object interface they also have available to them by inheritance) for manipulating the layout and presentation of {{HTMLElement("optgroup")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLOptGroupElement.disabled")}} - : A boolean value representing whether or not the whole list of children {{HTMLElement("option")}} is disabled (true) or not (false). - {{domxref("HTMLOptGroupElement.label")}} - : A string representing the label for the group. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("optgroup") }}.