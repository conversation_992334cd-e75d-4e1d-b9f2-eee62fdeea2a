Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloptionelement > form > index.md --- title: "HTMLOptionElement: form property" short-title: form slug: Web/API/HTMLOptionElement/form page-type: web-api-instance-property browser-compat: api.HTMLOptionElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLOptionElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns the {{domxref("HTMLSelectElement")}} associated with this {{htmlelement("option")}}, or `null` if this option is not associated with a {{htmlelement("select")}} owned by a form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLOptionElement")}} - {{domxref("HTMLSelectElement.form")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("option")}} - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)