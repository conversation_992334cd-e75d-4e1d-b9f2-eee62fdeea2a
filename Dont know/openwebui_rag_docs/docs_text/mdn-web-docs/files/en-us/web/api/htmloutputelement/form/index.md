Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > form > index.md --- title: "HTMLOutputElement: form property" short-title: form slug: Web/API/HTMLOutputElement/form page-type: web-api-instance-property browser-compat: api.HTMLOutputElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLOutputElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("output")}}, or `null` if this output is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLOutputElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("output")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/output#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)