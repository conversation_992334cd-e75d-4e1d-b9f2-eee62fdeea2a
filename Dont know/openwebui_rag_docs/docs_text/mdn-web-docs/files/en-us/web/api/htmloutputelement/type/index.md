Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > type > index.md --- title: "HTMLOutputElement: type property" short-title: type slug: Web/API/HTMLOutputElement/type page-type: web-api-instance-property browser-compat: api.HTMLOutputElement.type --- {{ApiRef("HTML DOM")}} The **`type`** read-only property of the {{domxref("HTMLOutputElement")}} interface returns the string `"output"`. ## Value The string `"output"`. ## Example ```js const output = document.querySelector("output"); console.log(output.type); // "output" ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLOutputElement")}} - {{HTMLElement("output")}}