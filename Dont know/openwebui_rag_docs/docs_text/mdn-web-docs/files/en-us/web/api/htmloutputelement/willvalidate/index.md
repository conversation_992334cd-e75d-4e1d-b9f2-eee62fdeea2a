Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md

Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md Path: mdn-web-docs > files > en-us > web > api > htmloutputelement > willvalidate > index.md --- title: "HTMLOutputElement: willValidate property" short-title: willValidate slug: Web/API/HTMLOutputElement/willValidate page-type: web-api-instance-property browser-compat: api.HTMLOutputElement.willValidate --- {{APIRef("HTML DOM")}} The **`willValidate`** read-only property of the {{domxref("HTMLOutputElement")}} interface returns `false`, because {{HTMLElement("output")}} elements are not candidates for [constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation). ## Value The boolean value `false`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLOutputElement.checkValidity()")}} - {{HTMLElement("output")}} - {{HTMLElement("form")}} - [Learn: Client-side form validation](/en-US/docs/Learn_web_development/Extensions/Forms/Form_validation) - [Guide: Constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation)