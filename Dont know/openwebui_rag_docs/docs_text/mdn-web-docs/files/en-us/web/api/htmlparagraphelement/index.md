Path: mdn-web-docs > files > en-us > web > api > htmlparagraphelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlparagraphelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlparagraphelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlparagraphelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlparagraphelement > index.md --- title: HTMLParagraphElement slug: Web/API/HTMLParagraphElement page-type: web-api-interface browser-compat: api.HTMLParagraphElement --- {{ APIRef("HTML DOM") }} The **`HTMLParagraphElement`** interface provides special properties (beyond those of the regular {{domxref("HTMLElement")}} object interface it inherits) for manipulating {{HTMLElement("p")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLParagraphElement.align")}} {{deprecated_inline}} - : A string representing an enumerated property indicating alignment of the element's contents with respect to the surrounding context. The possible values are `"left"`, `"right"`, `"justify"`, and `"center"`. ## Instance methods _No specific methods, inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{ HTMLElement("p") }}.