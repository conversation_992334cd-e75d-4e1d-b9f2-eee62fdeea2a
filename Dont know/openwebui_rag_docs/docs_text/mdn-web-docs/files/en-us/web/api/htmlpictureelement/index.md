Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpictureelement > index.md --- title: HTMLPictureElement slug: Web/API/HTMLPictureElement page-type: web-api-interface browser-compat: api.HTMLPictureElement --- {{APIRef("HTML DOM")}} The **`HTMLPictureElement`** interface represents a {{HTMLElement("picture")}} HTML element. It doesn't implement specific properties or methods. {{InheritanceDiagram}} ## Instance properties _No specific property, but inherits properties from its parent, {{domxref("HTMLElement")}}._ ## Instance methods _No specific method, but inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{HTMLElement("picture")}} HTML element that implements it. - The {{domxref("HTMLImageElement")}} and {{domxref("HTMLSourceElement")}} interfaces, often used in conjunction with a {{HTMLElement("picture")}} element.