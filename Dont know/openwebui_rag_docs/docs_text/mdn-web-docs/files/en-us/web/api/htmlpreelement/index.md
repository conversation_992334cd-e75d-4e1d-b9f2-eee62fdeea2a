Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlpreelement > index.md --- title: HTMLPreElement slug: Web/API/HTMLPreElement page-type: web-api-interface browser-compat: api.HTMLPreElement --- {{APIRef("HTML DOM")}} The **`HTMLPreElement`** interface exposes specific properties and methods (beyond those of the {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating a block of preformatted text ({{HtmlElement("pre")}}). {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLPreElement.width")}} {{deprecated_inline}} - : A `long` value reflecting the obsolete [`width`](/en-US/docs/Web/HTML/Reference/Elements/pre#width) attribute, containing a fixed-size length for the {{HTMLElement("pre")}} element. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("pre")}}