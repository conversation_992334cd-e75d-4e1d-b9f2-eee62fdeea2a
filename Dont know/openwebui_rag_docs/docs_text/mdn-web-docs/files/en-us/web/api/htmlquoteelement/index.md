Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlquoteelement > index.md --- title: HTMLQuoteElement slug: Web/API/HTMLQuoteElement page-type: web-api-interface browser-compat: api.HTMLQuoteElement --- {{APIRef("HTML DOM")}} The **`HTMLQuoteElement`** interface provides special properties and methods (beyond the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating quoting elements, like {{HTMLElement("blockquote")}} and {{HTMLElement("q")}}, but not the {{HTMLElement("cite")}} element. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLQuoteElement.cite")}} - : A string reflecting the [`cite`](/en-US/docs/Web/HTML/Reference/Elements/blockquote#cite) HTML attribute, containing a URL for the source of the quotation. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML elements implementing this interface: {{HTMLElement("blockquote")}} and {{HTMLElement("q")}}, but not {{HTMLElement("cite")}}.