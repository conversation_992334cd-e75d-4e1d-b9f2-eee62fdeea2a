Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectedcontentelement > index.md --- title: HTMLSelectedContentElement slug: Web/API/HTMLSelectedContentElement page-type: web-api-interface browser-compat: api.HTMLSelectedContentElement --- {{APIRef("HTML DOM")}} The **`HTMLSelectedContentElement`** interface represents a {{HTMLElement("selectedcontent")}} element in the [DOM](/en-US/docs/Web/API/Document_Object_Model). {{InheritanceDiagram}} ## Instance properties _This interface has no properties, but inherits properties from: {{DOMxRef("HTMLElement")}}._ ## Instance methods _This interface has no methods, but inherits methods from: {{DOMxRef("HTMLElement")}}._ ## Specifications Not currently part of a specification. See https://github.com/whatwg/html/pull/10633 for the relevant specification PR. ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("selectedcontent")}}.