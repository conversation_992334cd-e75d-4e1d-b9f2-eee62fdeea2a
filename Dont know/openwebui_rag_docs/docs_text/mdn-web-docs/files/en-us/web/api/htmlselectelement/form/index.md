Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmlselectelement > form > index.md --- title: "HTMLSelectElement: form property" short-title: form slug: Web/API/HTMLSelectElement/form page-type: web-api-instance-property browser-compat: api.HTMLSelectElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLSelectElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("select")}}, or `null` if this select is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLSelectElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("select")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/select#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)