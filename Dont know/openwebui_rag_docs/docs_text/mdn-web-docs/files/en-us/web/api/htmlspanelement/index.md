Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlspanelement > index.md --- title: HTMLSpanElement slug: Web/API/HTMLSpanElement page-type: web-api-interface browser-compat: api.HTMLSpanElement --- {{APIRef("HTML DOM")}} The **`HTMLSpanElement`** interface represents a {{HTMLElement("span")}} element and derives from the {{DOMxRef("HTMLElement")}} interface, but without implementing any additional properties or methods. {{InheritanceDiagram}} ## Instance properties _This interface has no properties, but inherits properties from: {{DOMxRef("HTMLElement")}}._ ## Instance methods _This interface has no methods, but inherits methods from: {{DOMxRef("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("span")}}.