Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmlstyleelement > type > index.md --- title: "HTMLStyleElement: type property" short-title: type slug: Web/API/HTMLStyleElement/type page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLStyleElement.type --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The **`HTMLStyleElement.type`** property returns the type of the current style. The value mirrors the [HTML `<style>` element's `type` attribute](/en-US/docs/Web/HTML/Reference/Elements/style#type). Authors should not use this property or rely on the value. ## Value The permitted values are an empty string or a case-insensitive match for "text/css". ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGStyleElement.type")}}