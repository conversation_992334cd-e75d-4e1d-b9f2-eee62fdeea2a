Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecaptionelement > index.md --- title: HTMLTableCaptionElement slug: Web/API/HTMLTableCaptionElement page-type: web-api-interface browser-compat: api.HTMLTableCaptionElement --- {{ APIRef("HTML DOM") }} The **`HTMLTableCaptionElement`** interface provides special properties (beyond the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating table {{HTMLElement("caption")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLTableCaptionElement.align")}} {{deprecated_inline}} - : A string which represents an enumerated attribute indicating alignment of the caption with respect to the table. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}_. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("caption")}}.