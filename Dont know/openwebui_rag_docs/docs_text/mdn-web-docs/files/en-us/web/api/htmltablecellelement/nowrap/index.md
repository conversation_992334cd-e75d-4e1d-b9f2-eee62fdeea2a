Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecellelement > nowrap > index.md --- title: "HTMLTableCellElement: noWrap property" short-title: noWrap slug: Web/API/HTMLTableCellElement/noWrap page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableCellElement.noWrap --- {{APIRef("HTML DOM API")}}{{deprecated_header}} The **`noWrap`** property of the {{domxref("HTMLTableCellElement")}} interface returns a Boolean value indicating if the text of the cell may be wrapped on several lines or not. > [!NOTE] > This property is deprecated and you should use the CSS {{cssxref("white-space")}} property with the value `nowrap` instead. ## Value A Boolean value. ## Examples Use CSS `white-space` instead. An [example](/en-US/docs/Web/CSS/white-space#controlling_line_wrapping_in_tables) is available on the {{cssxref("white-space")}} page. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}