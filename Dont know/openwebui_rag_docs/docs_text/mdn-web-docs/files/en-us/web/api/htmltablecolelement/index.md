Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablecolelement > index.md --- title: HTMLTableColElement slug: Web/API/HTMLTableColElement page-type: web-api-interface browser-compat: api.HTMLTableColElement --- {{ APIRef("HTML DOM") }} The **`HTMLTableColElement`** interface provides properties for manipulating single or grouped table column elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}_. - {{domxref("HTMLTableColElement.align")}} {{deprecated_inline}} - : A string that indicates the horizontal alignment of the cell data in the column. - {{domxref("HTMLTableColElement.ch")}} {{deprecated_inline}} - : A string representing the alignment character for cell data. - {{domxref("HTMLTableColElement.chOff")}} {{deprecated_inline}} - : A string representing the offset for the alignment character. - {{domxref("HTMLTableColElement.span")}} - : A positive number that reflects the [`span`](/en-US/docs/Web/HTML/Reference/Elements/col#span) HTML attribute, indicating the number of columns to apply this object's attributes to. - {{domxref("HTMLTableColElement.vAlign")}} {{deprecated_inline}} - : A string that indicates the vertical alignment of the cell data in the column. - {{domxref("HTMLTableColElement.width")}} {{deprecated_inline}} - : A string representing the default column width. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}_. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("col")}} and {{HTMLElement("colgroup")}}.