Path: mdn-web-docs > files > en-us > web > api > htmltableelement > align > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > align > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > align > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > align > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > align > index.md --- title: "HTMLTableElement: align property" short-title: align slug: Web/API/HTMLTableElement/align page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableElement.align --- {{APIRef("HTML DOM")}}{{Deprecated_Header}} The **`HTMLTableElement.align`** property represents the alignment of the table. ## Value One of the following string values: - `left` - `center` - `right` ## Examples ```js // Set the alignment of a table const t = document.getElementById("TableA"); t.align = "center"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}