Path: mdn-web-docs > files > en-us > web > api > htmltableelement > border > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > border > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > border > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > border > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > border > index.md --- title: "HTMLTableElement: border property" short-title: border slug: Web/API/HTMLTableElement/border page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableElement.border --- {{APIRef("HTML DOM")}}{{Deprecated_Header}} The **`HTMLTableElement.border`** property represents the border width of the {{HtmlElement("table")}} element. ## Value A string representing the width of the border in pixels. ## Examples ```js // Set the width of a table border to 2 pixels const t = document.getElementById("TableA"); t.border = "2"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}