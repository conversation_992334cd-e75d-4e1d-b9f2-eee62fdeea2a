Path: mdn-web-docs > files > en-us > web > api > htmltableelement > caption > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > caption > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > caption > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > caption > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > caption > index.md --- title: "HTMLTableElement: caption property" short-title: caption slug: Web/API/HTMLTableElement/caption page-type: web-api-instance-property browser-compat: api.HTMLTableElement.caption --- {{APIRef("HTML DOM")}} The **`HTMLTableElement.caption`** property represents the table caption. If no caption element is associated with the table, this property is `null`. ## Value A string. ## Examples ```js if (table.caption) { // Do something with the caption } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The interface implementing this property: {{domxref("HTMLTableElement")}}.