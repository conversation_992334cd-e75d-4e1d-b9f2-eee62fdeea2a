Path: mdn-web-docs > files > en-us > web > api > htmltableelement > cellpadding > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > cellpadding > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > cellpadding > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > cellpadding > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > cellpadding > index.md --- title: "HTMLTableElement: cellPadding property" short-title: cellPadding slug: Web/API/HTMLTableElement/cellPadding page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableElement.cellPadding --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The **`HTMLTableElement.cellPadding`** property represents the padding around the individual cells of the table. ## Value A string representing pixels (e.g., `"10"`) or a percentage value (e.g., `"10%"`). When set to the `null` value, that `null` value is converted to the empty string (`""`), so `elt.cellPadding = null` is equivalent to `elt.cellPadding = ""`. ## Examples ```js // Set cell padding to 10 pixels let t = document.getElementById("TableA"); t.cellPadding = "10"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}