Path: mdn-web-docs > files > en-us > web > api > htmltableelement > summary > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > summary > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > summary > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > summary > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > summary > index.md --- title: "HTMLTableElement: summary property" short-title: summary slug: Web/API/HTMLTableElement/summary page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableElement.summary --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The **`HTMLTableElement.summary`** property represents the table description. ## Value A string. ## Examples ```js HTMLTableElement.summary = "Usage statistics"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}