Path: mdn-web-docs > files > en-us > web > api > htmltableelement > tfoot > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > tfoot > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > tfoot > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > tfoot > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > tfoot > index.md --- title: "HTMLTableElement: tFoot property" short-title: tFoot slug: Web/API/HTMLTableElement/tFoot page-type: web-api-instance-property browser-compat: api.HTMLTableElement.tFoot --- {{APIRef("HTML DOM")}} The **`HTMLTableElement.tFoot`** property represents the {{HTMLElement("tfoot")}} element of a {{HTMLElement("table")}}. Its value will be `null` if there is no such element. ## Value A {{HTMLElement("tfoot")}} element or `null`. ## Examples ```js if (table.tFoot === my_foot) { // } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The interface implementing this property: {{domxref("HTMLTableElement")}}.