Path: mdn-web-docs > files > en-us > web > api > htmltableelement > width > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltableelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > htmltableelement > width > index.md --- title: "HTMLTableElement: width property" short-title: width slug: Web/API/HTMLTableElement/width page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableElement.width --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The **`HTMLTableElement.width`** property represents the desired width of the table. ## Value A string representing the width in number of pixels or as a percentage value. ## Examples ```js myTable.width = "75%"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}