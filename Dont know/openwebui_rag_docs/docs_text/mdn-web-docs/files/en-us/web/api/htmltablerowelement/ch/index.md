Path: mdn-web-docs > files > en-us > web > api > htmltablerowelement > ch > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablerowelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablerowelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablerowelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablerowelement > ch > index.md --- title: "HTMLTableRowElement: ch property" short-title: ch slug: Web/API/HTMLTableRowElement/ch page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableRowElement.ch --- {{APIRef("HTML DOM")}}{{deprecated_header}} The **`ch`** property of the {{domxref("HTMLTableRowElement")}} interface does nothing. It reflects the `char` attribute of the {{HTMLElement("tr")}} element. > [!NOTE] > This property was designed to participate to the ability to align table cell content on a specific character (typically the decimal point), but was never implemented by browsers. > > To achieve such alignment, watch for the support of a string value with the {{cssxref("text-align")}} CSS property. ## Value A single character. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{cssxref("text-align")}}