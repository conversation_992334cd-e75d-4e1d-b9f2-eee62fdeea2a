Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > ch > index.md --- title: "HTMLTableSectionElement: ch property" short-title: ch slug: Web/API/HTMLTableSectionElement/ch page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableSectionElement.ch --- {{APIRef("HTML DOM")}}{{deprecated_header}} The **`ch`** property of the {{domxref("HTMLTableSectionElement")}} interface does nothing. It reflects the `char` attribute of the section element. > [!NOTE] > This property was designed to participate to the ability to align table cell content on a specific character (typically the decimal point), but was never implemented by browsers. > > To achieve such alignment, watch for the support of a string value with the {{cssxref("text-align")}} CSS property. ## Value A single character. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{cssxref("text-align")}}