Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md Path: mdn-web-docs > files > en-us > web > api > htmltablesectionelement > choff > index.md --- title: "HTMLTableSectionElement: chOff property" short-title: chOff slug: Web/API/HTMLTableSectionElement/chOff page-type: web-api-instance-property status: - deprecated browser-compat: api.HTMLTableSectionElement.chOff --- {{APIRef("HTML DOM")}}{{deprecated_header}} The **`chOff`** property of the {{domxref("HTMLTableSectionElement")}} interface does nothing. It reflects the `charoff` attribute of the section element. > [!NOTE] > This property was designed to participate in an ability to align table cell content on a specific character (typically the decimal point), but was never implemented by browsers. > > To achieve such alignment, watch for the support of a string value with the {{cssxref("text-align")}} CSS property. ## Value An integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{cssxref("text-align")}}