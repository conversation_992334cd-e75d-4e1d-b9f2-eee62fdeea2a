Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > form > index.md --- title: "HTMLTextAreaElement: form property" short-title: form slug: Web/API/HTMLTextAreaElement/form page-type: web-api-instance-property browser-compat: api.HTMLTextAreaElement.form --- {{APIRef("HTML DOM")}} The **`form`** read-only property of the {{domxref("HTMLTextAreaElement")}} interface returns an {{domxref("HTMLFormElement")}} object that owns this {{htmlelement("textarea")}}, or `null` if this textarea is not owned by any form. ## Value An {{domxref("HTMLFormElement")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLTextAreaElement")}} - {{domxref("HTMLFormElement")}} - {{HTMLElement("textarea")}} - HTML [`form`](/en-US/docs/Web/HTML/Reference/Elements/textarea#form) attribute - [HTML forms guide](/en-US/docs/Learn_web_development/Extensions/Forms)