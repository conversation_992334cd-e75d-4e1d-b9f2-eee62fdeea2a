Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > placeholder > index.md --- title: "HTMLTextAreaElement: placeholder property" short-title: placeholder slug: Web/API/HTMLTextAreaElement/placeholder page-type: web-api-instance-property browser-compat: api.HTMLTextAreaElement.placeholder --- {{ APIRef("HTML DOM") }} The **`placeholder`** property of the {{DOMxRef("HTMLTextAreaElement")}} interface represents a hint to the user of what can be entered in the control. It reflects the {{htmlelement("textarea")}} element's [`placeholder`](/en-US/docs/Web/HTML/Reference/Elements/textarea#placeholder) attribute. ## Value A string. ## Examples ```js const textareaElement = document.getElementById("comment"); console.log(textArea.placeholder); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{HTMLElement("textarea")}} - {{DOMXref("HTMLTextAreaElement.value")}}