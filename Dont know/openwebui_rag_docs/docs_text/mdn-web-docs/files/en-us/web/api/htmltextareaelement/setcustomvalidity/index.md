Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > setcustomvalidity > index.md --- title: "HTMLTextAreaElement: setCustomValidity() method" short-title: setCustomValidity() slug: Web/API/HTMLTextAreaElement/setCustomValidity page-type: web-api-instance-method browser-compat: api.HTMLTextAreaElement.setCustomValidity --- {{ APIRef("HTML DOM") }} The **`setCustomValidity()`** method of the {{DOMxRef("HTMLTextAreaElement")}} interface sets the custom validity message for the {{htmlelement("textarea")}} element. Use the empty string to indicate that the element does _not_ have a custom validity error. ## Syntax ```js-nolint setCustomValidity(string) ``` ### Parameters - `string` - : The string containing the error message. The empty string removes any custom validity errors. ### Return value None ({{jsxref("undefined")}}). ## Examples In this example, if the `<textarea>`'s doesn't pass constraint validation, we provide custom errors based on the constraint that is failing validation. If the value is valid, we set the custom error to an empty string: ```js const comment = document.getElementById("comment"); if (comment.validity.valueMissing) { comment.setCustomValidity("We can't submit a blank comment!"); } else if (comment.validity.tooShort) { comment.setCustomValidity("Tell us more! Your comment is too short."); } else if (comment.validity.tooLong) { comment.setCustomValidity( "Loquacious much? Keep it to under 800 characters!", ); } else { comment.setCustomValidity(""); } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{HTMLelement("textarea")}} - {{domxref("HTMLTextAreaElement")}} - {{domxref("HTMLTextAreaElement.validity")}} - {{domxref("HTMLTextAreaElement.checkValidity()")}} - {{domxref("HTMLTextAreaElement.reportValidity()")}} - [Form validation](/en-US/docs/Web/HTML/Guides/Constraint_validation). - [Learn: Client-side form validation](/en-US/docs/Learn_web_development/Extensions/Forms/Form_validation) - [Guide: Constraint validation](/en-US/docs/Web/HTML/Guides/Constraint_validation) - CSS {{cssxref(":valid")}} and {{cssxref(":invalid")}} pseudo-classes