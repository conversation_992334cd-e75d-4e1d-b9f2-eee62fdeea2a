Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > htmltextareaelement > type > index.md --- title: "HTMLTextAreaElement: type property" short-title: type slug: Web/API/HTMLTextAreaElement/type page-type: web-api-instance-property browser-compat: api.HTMLTextAreaElement.type --- {{ApiRef("HTML DOM")}} The **`type`** read-only property of the {{domxref("HTMLTextAreaElement")}} interface returns the string `"textarea"`. ## Value The string `"textarea"`. ## Example ```js const textArea = document.querySelector("textarea"); console.log(textArea.type); // "textarea" ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("HTMLTextAreaElement")}} - {{HTMLElement("textarea")}}