Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmltimeelement > index.md --- title: HTMLTimeElement slug: Web/API/HTMLTimeElement page-type: web-api-interface browser-compat: api.HTMLTimeElement --- {{ APIRef("HTML DOM") }} The **`HTMLTimeElement`** interface provides special properties (beyond the regular {{domxref("HTMLElement")}} interface it also has available to it by inheritance) for manipulating {{HTMLElement("time")}} elements. {{InheritanceDiagram}} ## Instance properties _Inherits properties from its parent, {{domxref("HTMLElement")}}._ - {{domxref("HTMLTimeElement.dateTime")}} - : A string that reflects the [`datetime`](/en-US/docs/Web/HTML/Reference/Elements/time#datetime) HTML attribute, containing a machine-readable form of the element's date and time value. ## Instance methods _No specific method; inherits methods from its parent, {{domxref("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The HTML element implementing this interface: {{HTMLElement("time")}}.