Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md

Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md Path: mdn-web-docs > files > en-us > web > api > htmlunknownelement > index.md --- title: HTMLUnknownElement slug: Web/API/HTMLUnknownElement page-type: web-api-interface browser-compat: api.HTMLUnknownElement --- {{APIRef("HTML DOM")}} The **`HTMLUnknownElement`** interface represents an invalid HTML element and derives from the {{DOMxRef("HTMLElement")}} interface, but without implementing any additional properties or methods. {{InheritanceDiagram}} ## Instance properties _No specific property; inherits properties from its parent, {{DOMxRef("HTMLElement")}}._ ## Instance methods _No specific method; inherits methods from its parent, {{DOMxRef("HTMLElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}