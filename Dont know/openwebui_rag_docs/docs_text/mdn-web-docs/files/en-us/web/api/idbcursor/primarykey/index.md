Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md

Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md Path: mdn-web-docs > files > en-us > web > api > idbcursor > primarykey > index.md --- title: "IDBCursor: primaryKey property" short-title: primaryKey slug: Web/API/IDBCursor/primaryKey page-type: web-api-instance-property browser-compat: api.IDBCursor.primaryKey --- {{APIRef("IDBCursor")}} {{AvailableInWorkers}} The **`primaryKey`** read-only property of the {{domxref("IDBCursor")}} interface returns the cursor's current effective key. If the cursor is currently being iterated or has iterated outside its range, this is set to undefined. The cursor's primary key can be any data type. ## Value A value of any data type. ### Exceptions - `InvalidStateError` {{domxref("DOMException")}} - : Thrown if the cursor is advancing or has finished. ## Examples In this simple fragment we create a transaction, retrieve an object store, then use a cursor to iterate through all the records in the object store. Within each iteration we log the primary key of the cursor to the console. The cursor does not require us to select the data based on a key; we can just grab all of it. Also note that in each iteration of the loop, you can grab data from the current record under the cursor object using `cursor.value.foo`. For a complete working example, see our [IDBCursor example](https://github.com/mdn/dom-examples/tree/main/indexeddb-examples/idbcursor) ([View the example live](https://mdn.github.io/dom-examples/indexeddb-examples/idbcursor/)). ```js function displayData() { const transaction = db.transaction(["rushAlbumList"], "readonly"); const objectStore = transaction.objectStore("rushAlbumList"); objectStore.openCursor().onsuccess = (event) => { const cursor = event.target.result; if (cursor) { const listItem = document.createElement("li"); listItem.textContent = `${cursor.value.albumTitle}, ${cursor.value.year}`; list.appendChild(listItem); console.log(cursor.primaryKey); cursor.continue(); } else { console.log("Entries all displayed."); } }; } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).