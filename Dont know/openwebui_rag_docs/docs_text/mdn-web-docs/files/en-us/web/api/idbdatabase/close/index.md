Path: mdn-web-docs > files > en-us > web > api > idbdatabase > close > index.md

Path: mdn-web-docs > files > en-us > web > api > idbdatabase > close > index.md Path: mdn-web-docs > files > en-us > web > api > idbdatabase > close > index.md Path: mdn-web-docs > files > en-us > web > api > idbdatabase > close > index.md Path: mdn-web-docs > files > en-us > web > api > idbdatabase > close > index.md --- title: "IDBDatabase: close() method" short-title: close() slug: Web/API/IDBDatabase/close page-type: web-api-instance-method browser-compat: api.IDBDatabase.close --- {{ APIRef("IndexedDB") }} {{AvailableInWorkers}} The **`close()`** method of the {{domxref("IDBDatabase")}} interface returns immediately and closes the connection in a separate thread. The connection is not actually closed until all transactions created using this connection are complete. No new transactions can be created for this connection once this method is called. Methods that create transactions throw an exception if a closing operation is pending. ## Syntax ```js-nolint close() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js // Let us open our database const DBOpenRequest = window.indexedDB.open("toDoList", 4); // opening a database. // Create event handlers for both success and failure of DBOpenRequest.onerror = (event) => { note.appendChild(document.createElement("li")).textContent = "Error loading database."; }; DBOpenRequest.onsuccess = (event) => { note.appendChild(document.createElement("li")).textContent = "Database initialized."; // store the result of opening the database in the db variable. db = DBOpenRequest.result; // now let's close the database again! db.close(); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).