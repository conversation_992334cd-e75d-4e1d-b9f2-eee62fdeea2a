Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md

Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > count > index.md --- title: "IDBIndex: count() method" short-title: count() slug: Web/API/IDBIndex/count page-type: web-api-instance-method browser-compat: api.IDBIndex.count --- {{ APIRef("IndexedDB") }} {{AvailableInWorkers}} The **`count()`** method of the {{domxref("IDBIndex")}} interface returns an {{domxref("IDBRequest")}} object, and in a separate thread, returns the number of records within a key range. ## Syntax ```js-nolint count() count(key) ``` ### Parameters - `key` {{optional_inline}} - : The key or key range that identifies the record to be counted. ### Return value A {{domxref("IDBRequest")}} object on which subsequent events related to this operation are fired. If the operation is successful, the value of the request's {{domxref("IDBRequest.result", "result")}} property is the number of records that match the given key or key range. ### Exceptions This method may raise a {{domxref("DOMException")}} of one of the following types: - `TransactionInactiveError` {{domxref("DOMException")}} - : Thrown if this {{domxref("IDBIndex")}}'s transaction is inactive. - `DataError` {{domxref("DOMException")}} - : Thrown if the key or key range provided contains an invalid key. - `InvalidStateError` {{domxref("DOMException")}} - : Thrown if the {{domxref("IDBIndex")}} has been deleted or removed. ## Examples In the following example we open a transaction and an object store, then get the index `lName` from a simple contacts database. We then open a basic cursor on the index using {{domxref("IDBIndex.openCursor")}} this works the same as opening a cursor directly on an `ObjectStore` using {{domxref("IDBObjectStore.openCursor")}} except that the returned records are sorted based on the index, not the primary key. `myIndex.count()` is then used to count the number of records in the index, and the result of that request is logged to the console when its success callback returns. Finally, we iterate through each record, and insert the data into an HTML table. For a complete working example, see our [IndexedDB-examples demo repo](https://github.com/mdn/dom-examples/tree/main/indexeddb-examples/idbindex) ([View the example live](https://mdn.github.io/dom-examples/indexeddb-examples/idbindex/)). ```js function displayDataByIndex() { tableEntry.textContent = ""; const transaction = db.transaction(["contactsList"], "readonly"); const objectStore = transaction.objectStore("contactsList"); const myIndex = objectStore.index("lName"); const countRequest = myIndex.count(); countRequest.onsuccess = () => { console.log(countRequest.result); }; myIndex.openCursor().onsuccess = (event) => { const cursor = event.target.result; if (cursor) { const tableRow = document.createElement("tr"); for (const cell of [ cursor.value.id, cursor.value.lName, cursor.value.fName, cursor.value.jTitle, cursor.value.company, cursor.value.eMail, cursor.value.phone, cursor.value.age, ]) { const tableCell = document.createElement("td"); tableCell.textContent = cell; tableRow.appendChild(tableCell); } tableEntry.appendChild(tableRow); cursor.continue(); } else { console.log("Entries all displayed."); } }; } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).