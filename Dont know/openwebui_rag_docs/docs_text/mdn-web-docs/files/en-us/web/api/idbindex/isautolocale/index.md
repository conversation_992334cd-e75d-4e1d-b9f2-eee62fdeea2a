Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md

Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > isautolocale > index.md --- title: "IDBIndex: isAutoLocale property" short-title: isAutoLocale slug: Web/API/IDBIndex/isAutoLocale page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.IDBIndex.isAutoLocale --- {{APIRef("IndexedDB")}}{{deprecated_header}}{{non-standard_header}} The **`isAutoLocale`** read-only property of the {{domxref("IDBIndex")}} interface returns a boolean value indicating whether the index had a `locale` value of `auto` specified upon its creation (see the [`options`](/en-US/docs/Web/API/IDBObjectStore/createIndex#options) parameter to [`IDBObjectStore.createIndex()`](/en-US/docs/Web/API/IDBObjectStore/createIndex).) ## Value A boolean value. ## Examples In the following example we open a transaction and an object store, then get the index `lName` from a simple contacts database. We then open a basic cursor on the index using {{domxref("IDBIndex.openCursor")}} this works the same as opening a cursor directly on an `ObjectStore` using {{domxref("IDBObjectStore.openCursor")}} except that the returned records are sorted based on the index, not the primary key. The `isAutoLocale` value is logged to the console. ```js function displayDataByIndex() { tableEntry.textContent = ""; const transaction = db.transaction(["contactsList"], "readonly"); const objectStore = transaction.objectStore("contactsList"); const myIndex = objectStore.index("lName"); console.log(myIndex.isAutoLocale); myIndex.openCursor().onsuccess = (event) => { const cursor = event.target.result; if (cursor) { const tableRow = document.createElement("tr"); for (const cell of [ cursor.value.id, cursor.value.lName, cursor.value.fName, cursor.value.jTitle, cursor.value.company, cursor.value.eMail, cursor.value.phone, cursor.value.age, ]) { const tableCell = document.createElement("td"); tableCell.textContent = cell; tableRow.appendChild(tableCell); } tableEntry.appendChild(tableRow); cursor.continue(); } else { console.log("Entries all displayed."); } }; } ``` ## Specifications Not currently part of any specification. ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).