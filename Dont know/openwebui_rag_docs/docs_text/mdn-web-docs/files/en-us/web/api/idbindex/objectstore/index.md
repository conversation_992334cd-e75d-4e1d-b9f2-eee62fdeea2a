Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md

Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md Path: mdn-web-docs > files > en-us > web > api > idbindex > objectstore > index.md --- title: "IDBIndex: objectStore property" short-title: objectStore slug: Web/API/IDBIndex/objectStore page-type: web-api-instance-property browser-compat: api.IDBIndex.objectStore --- {{ APIRef("IndexedDB") }} {{AvailableInWorkers}} The **`objectStore`** property of the {{domxref("IDBIndex")}} interface returns the object store referenced by the current index. ## Value An {{ domxref("IDBObjectStore") }}. ## Examples In the following example we open a transaction and an object store, then get the index `lName` from a simple contacts database. We then open a basic cursor on the index using {{domxref("IDBIndex.openCursor")}}. This works the same as opening a cursor directly on an `ObjectStore` using {{domxref("IDBObjectStore.openCursor")}} except that the returned records are sorted based on the index, not the primary key. The current object store is logged to the console: it should be returned something like this: ```json IDBObjectStore { name: "contactsList", keyPath: "id", indexNames: DOMStringList[7], transaction: IDBTransaction, autoIncrement: false } ``` Finally, we iterate through each record, and insert the data into an HTML table. For a complete working example, see our [IndexedDB-examples demo repo](https://github.com/mdn/dom-examples/tree/main/indexeddb-examples/idbindex) ([View the example live](https://mdn.github.io/dom-examples/indexeddb-examples/idbindex/)). ```js function displayDataByIndex() { tableEntry.textContent = ""; const transaction = db.transaction(["contactsList"], "readonly"); const objectStore = transaction.objectStore("contactsList"); const myIndex = objectStore.index("lName"); console.log(myIndex.objectStore); myIndex.openCursor().onsuccess = (event) => { const cursor = event.target.result; if (cursor) { const tableRow = document.createElement("tr"); for (const cell of [ cursor.value.id, cursor.value.lName, cursor.value.fName, cursor.value.jTitle, cursor.value.company, cursor.value.eMail, cursor.value.phone, cursor.value.age, ]) { const tableCell = document.createElement("td"); tableCell.textContent = cell; tableRow.appendChild(tableCell); } tableEntry.appendChild(tableRow); cursor.continue(); } else { console.log("Entries all displayed."); } }; } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).