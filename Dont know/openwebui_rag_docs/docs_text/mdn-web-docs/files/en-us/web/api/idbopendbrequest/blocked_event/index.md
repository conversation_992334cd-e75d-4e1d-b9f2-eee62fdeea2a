Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md

Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbopendbrequest > blocked_event > index.md --- title: "IDBOpenDBRequest: blocked event" short-title: blocked slug: Web/API/IDBOpenDBRequest/blocked_event page-type: web-api-event browser-compat: api.IDBOpenDBRequest.blocked_event --- {{APIRef("IndexedDB")}} The `blocked` handler is executed when an open connection to a database is blocking a `versionchange` transaction on the same database. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("blocked", (event) => { }) onblocked = (event) => { } ``` ## Event type An {{domxref("IDBVersionChangeEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("IDBVersionChangeEvent")}} ## Event properties _Also inherits properties from its parent, {{domxref("Event")}} interface._ - {{ domxref("IDBVersionChangeEvent.oldVersion") }} {{ReadOnlyInline}} - : Returns the old version of the database. - {{ domxref("IDBVersionChangeEvent.newVersion") }} {{ReadOnlyInline}} - : Returns the new version of the database. ## Examples Using `addEventListener()`: ```js // Open the database const DBOpenRequest = window.indexedDB.open("toDoList", 4); DBOpenRequest.onupgradeneeded = (event) => { const db = event.target.result; db.onerror = () => { console.log("Error creating database"); }; // Create an objectStore for this database const objectStore = db.createObjectStore("toDoList", { keyPath: "taskTitle", }); // define what data items the objectStore will contain objectStore.createIndex("hours", "hours", { unique: false }); objectStore.createIndex("minutes", "minutes", { unique: false }); objectStore.createIndex("day", "day", { unique: false }); objectStore.createIndex("month", "month", { unique: false }); objectStore.createIndex("year", "year", { unique: false }); }; DBOpenRequest.onsuccess = (event) => { // Let's try to open the same database with a higher revision version const req2 = indexedDB.open("toDoList", 5); // In this case the onblocked handler will be executed req2.addEventListener("blocked", () => { console.log("Request was blocked"); }); }; ``` Using the `onblocked` property: ```js // Open the database const DBOpenRequest = window.indexedDB.open("toDoList", 4); DBOpenRequest.onupgradeneeded = (event) => { const db = event.target.result; db.onerror = () => { console.log("Error creating database"); }; // Create an objectStore for this database const objectStore = db.createObjectStore("toDoList", { keyPath: "taskTitle", }); // define what data items the objectStore will contain objectStore.createIndex("hours", "hours", { unique: false }); objectStore.createIndex("minutes", "minutes", { unique: false }); objectStore.createIndex("day", "day", { unique: false }); objectStore.createIndex("month", "month", { unique: false }); objectStore.createIndex("year", "year", { unique: false }); }; DBOpenRequest.onsuccess = (event) => { // Let's try to open the same database with a higher revision version const req2 = indexedDB.open("toDoList", 5); // In this case the onblocked handler will be executed req2.onblocked = () => { console.log("Request was blocked"); }; }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB)