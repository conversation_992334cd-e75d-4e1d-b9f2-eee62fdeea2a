Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md

Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > error_event > index.md --- title: "IDBTransaction: error event" short-title: error slug: Web/API/IDBTransaction/error_event page-type: web-api-event browser-compat: api.IDBTransaction.error_event --- {{ APIRef("IndexedDB") }} The `error` event is fired on `IDBTransaction` when a request returns an error and the event bubbles up to the transaction object. > [!NOTE] > To handle all the ways a transaction can fail, consider listening for the {{domxref("IDBTransaction.abort_event", "abort")}} event instead. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("error", (event) => { }) onerror = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Bubbling This event bubbles to {{domxref("IDBDatabase")}}. The `event.target` property refers to the {{domxref('IDBTransaction')}} object that bubbles up. For more information, see [Event bubbling](/en-US/docs/Learn_web_development/Core/Scripting/Event_bubbling). ## Examples This example opens a database and tries to add a record, listening for the `error` event for the `add()` operation (this will occur if, for example, a record with the given `taskTitle` already exists): ```js // Open the database const dBOpenRequest = window.indexedDB.open("toDoList", 4); dBOpenRequest.onupgradeneeded = (event) => { const db = event.target.result; // Create an objectStore for this database const objectStore = db.createObjectStore("toDoList", { keyPath: "taskTitle", }); // define what data items the objectStore will contain objectStore.createIndex("hours", "hours", { unique: false }); objectStore.createIndex("minutes", "minutes", { unique: false }); objectStore.createIndex("day", "day", { unique: false }); objectStore.createIndex("month", "month", { unique: false }); objectStore.createIndex("year", "year", { unique: false }); }; dBOpenRequest.onsuccess = (event) => { const db = dBOpenRequest.result; // open a read/write db transaction, ready for adding the data const transaction = db.transaction(["toDoList"], "readwrite"); transaction.addEventListener("error", () => { console.log(`Error adding new item: ${newItem.taskTitle}`); }); const objectStore = transaction.objectStore("toDoList"); const newItem = { taskTitle: "my task", hours: 10, minutes: 10, day: 10, month: "January", year: 2020, }; const objectStoreRequest = objectStore.add(newItem); }; ``` The same example, using the `onerror` property instead of `addEventListener()`: ```js // Open the database const dBOpenRequest = window.indexedDB.open("toDoList", 4); dBOpenRequest.onupgradeneeded = (event) => { const db = event.target.result; // Create an objectStore for this database const objectStore = db.createObjectStore("toDoList", { keyPath: "taskTitle", }); // define what data items the objectStore will contain objectStore.createIndex("hours", "hours", { unique: false }); objectStore.createIndex("minutes", "minutes", { unique: false }); objectStore.createIndex("day", "day", { unique: false }); objectStore.createIndex("month", "month", { unique: false }); objectStore.createIndex("year", "year", { unique: false }); }; dBOpenRequest.onsuccess = (event) => { const db = dBOpenRequest.result; // open a read/write db transaction, ready for adding the data const transaction = db.transaction(["toDoList"], "readwrite"); transaction.onerror = () => { console.log(`Error adding new item: ${newItem.taskTitle}`); }; const objectStore = transaction.objectStore("toDoList"); const newItem = { taskTitle: "my task", hours: 10, minutes: 10, day: 10, month: "January", year: 2020, }; const objectStoreRequest = objectStore.add(newItem); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB)