Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md

Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md Path: mdn-web-docs > files > en-us > web > api > idbtransaction > objectstorenames > index.md --- title: "IDBTransaction: objectStoreNames property" short-title: objectStoreNames slug: Web/API/IDBTransaction/objectStoreNames page-type: web-api-instance-property browser-compat: api.IDBTransaction.objectStoreNames --- {{ APIRef("IndexedDB") }} The **`objectStoreNames`** read-only property of the {{domxref("IDBTransaction")}} interface returns a {{domxref("DOMStringList")}} of names of {{domxref("IDBObjectStore")}} objects. ## Value A {{domxref("DOMStringList")}} of names of {{domxref("IDBObjectStore")}} objects. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using IndexedDB](/en-US/docs/Web/API/IndexedDB_API/Using_IndexedDB) - Starting transactions: {{domxref("IDBDatabase")}} - Using transactions: {{domxref("IDBTransaction")}} - Setting a range of keys: {{domxref("IDBKeyRange")}} - Retrieving and making changes to your data: {{domxref("IDBObjectStore")}} - Using cursors: {{domxref("IDBCursor")}} - Reference example: [To-do Notifications](https://github.com/mdn/dom-examples/tree/main/to-do-notifications) ([View the example live](https://mdn.github.io/dom-examples/to-do-notifications/)).