Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md

Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md Path: mdn-web-docs > files > en-us > web > api > idledetector > idledetector > index.md --- title: "IdleDetector: IdleDetector() constructor" short-title: IdleDetector() slug: Web/API/IdleDetector/IdleDetector page-type: web-api-constructor status: - experimental browser-compat: api.IdleDetector.IdleDetector --- {{securecontext_header}}{{APIRef("Idle Detection API")}}{{SeeCompatTable}}{{AvailableInWorkers("window_and_dedicated")}} The **`IdleDetector()`** constructor creates a new {{domxref("IdleDetector")}} object which provides events indicating when the user is no longer interacting with their device or the screen has locked. ## Syntax ```js-nolint new IdleDetector() ``` ### Parameters None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}