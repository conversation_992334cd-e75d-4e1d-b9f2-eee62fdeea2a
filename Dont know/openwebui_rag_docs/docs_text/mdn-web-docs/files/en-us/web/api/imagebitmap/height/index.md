Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md

Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > height > index.md --- title: "ImageBitmap: height property" short-title: height slug: Web/API/ImageBitmap/height page-type: web-api-instance-property browser-compat: api.ImageBitmap.height --- {{APIRef("Canvas API")}}{{AvailableInWorkers}} The **`ImageBitmap.height`** read-only property returns the {{domxref("ImageBitmap")}} object's height in CSS pixels. ## Value A number represents the {{domxref("ImageBitmap")}} object's height in CSS pixels. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}