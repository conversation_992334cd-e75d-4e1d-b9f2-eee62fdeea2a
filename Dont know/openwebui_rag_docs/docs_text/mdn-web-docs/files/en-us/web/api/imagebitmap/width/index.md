Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md

Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md Path: mdn-web-docs > files > en-us > web > api > imagebitmap > width > index.md --- title: "ImageBitmap: width property" short-title: width slug: Web/API/ImageBitmap/width page-type: web-api-instance-property browser-compat: api.ImageBitmap.width --- {{APIRef("Canvas API")}}{{AvailableInWorkers}} The **`ImageBitmap.width`** read-only property returns the {{domxref("ImageBitmap")}} object's width in CSS pixels. ## Value A number represents the {{domxref("ImageBitmap")}} object's width in CSS pixels. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}