Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md

Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md Path: mdn-web-docs > files > en-us > web > api > imagecapture > track > index.md --- title: "ImageCapture: track property" short-title: track slug: Web/API/ImageCapture/track page-type: web-api-instance-property browser-compat: api.ImageCapture.track --- {{APIRef("Image Capture API")}} The **`track`** read-only property of the {{domxref("ImageCapture")}} interface returns a reference to the {{domxref("MediaStreamTrack")}} passed to the {{domxref("ImageCapture.ImageCapture","ImageCapture()")}} constructor. ## Value A {{domxref("MediaStreamTrack")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}