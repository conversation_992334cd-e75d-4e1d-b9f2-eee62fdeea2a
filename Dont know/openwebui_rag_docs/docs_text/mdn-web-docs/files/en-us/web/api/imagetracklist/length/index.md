Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md Path: mdn-web-docs > files > en-us > web > api > imagetracklist > length > index.md --- title: "ImageTrackList: length property" short-title: length slug: Web/API/ImageTrackList/length page-type: web-api-instance-property browser-compat: api.ImageTrackList.length --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`length`** property of the {{domxref("ImageTrackList")}} interface returns the length of the `ImageTrackList`. ## Value An integer. ## Examples The following example prints the value of `length` to the console. ```js let tracks = imageDecoder.tracks; console.log(tracks.length); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}