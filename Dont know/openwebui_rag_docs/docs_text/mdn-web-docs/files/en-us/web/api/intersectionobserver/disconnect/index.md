Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md

Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md Path: mdn-web-docs > files > en-us > web > api > intersectionobserver > disconnect > index.md --- title: "IntersectionObserver: disconnect() method" short-title: disconnect() slug: Web/API/IntersectionObserver/disconnect page-type: web-api-instance-method browser-compat: api.IntersectionObserver.disconnect --- {{APIRef("Intersection Observer API")}} The {{domxref("IntersectionObserver")}} method **`disconnect()`** stops watching all of its target elements for visibility changes. ## Syntax ```js-nolint disconnect() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("IntersectionObserver.observe", "observe()")}} - {{domxref("IntersectionObserver.unobserve", "unobserve()")}}