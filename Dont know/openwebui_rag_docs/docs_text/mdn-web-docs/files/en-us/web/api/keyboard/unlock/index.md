Path: mdn-web-docs > files > en-us > web > api > keyboard > unlock > index.md

Path: mdn-web-docs > files > en-us > web > api > keyboard > unlock > index.md Path: mdn-web-docs > files > en-us > web > api > keyboard > unlock > index.md Path: mdn-web-docs > files > en-us > web > api > keyboard > unlock > index.md Path: mdn-web-docs > files > en-us > web > api > keyboard > unlock > index.md --- title: "Keyboard: unlock() method" short-title: unlock() slug: Web/API/Keyboard/unlock page-type: web-api-instance-method status: - experimental browser-compat: api.Keyboard.unlock --- {{APIRef("Keyboard API")}}{{SeeCompatTable}}{{securecontext_header}} The **`unlock()`** method of the {{domxref("Keyboard")}} interface unlocks all keys captured by the {{domxref('Keyboard.lock()')}} method and returns synchronously. ## Syntax ```js-nolint unlock() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}