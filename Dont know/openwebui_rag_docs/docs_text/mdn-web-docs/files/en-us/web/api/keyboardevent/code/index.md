Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md

Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > code > index.md --- title: "KeyboardEvent: code property" short-title: code slug: Web/API/KeyboardEvent/code page-type: web-api-instance-property browser-compat: api.KeyboardEvent.code --- {{APIRef("UI Events")}} The `KeyboardEvent.code` property represents a physical key on the keyboard (as opposed to the character generated by pressing the key). In other words, this property returns a value that isn't altered by keyboard layout or the state of the modifier keys. If the input device isn't a physical keyboard, but is instead a virtual keyboard or accessibility device, the returned value will be set by the browser to match as closely as possible to what would happen with a physical keyboard, to maximize compatibility between physical and virtual input devices. This property is useful when you want to handle keys based on their physical positions on the input device rather than the characters associated with those keys; this is especially common when writing code to handle input for games that simulate a gamepad-like environment using keys on the keyboard. Be aware, however, that you can't use the value reported by `KeyboardEvent.code` to determine the character generated by the keystroke, because the keycode's name may not match the actual character that's printed on the key or that's generated by the computer when the key is pressed. For example, the `code` returned is `"KeyQ"` for the <kbd>Q</kbd> key on a QWERTY layout keyboard, but the same `code` value also represents the <kbd>'</kbd> key on Dvorak keyboards and the <kbd>A</kbd> key on AZERTY keyboards. That makes it impossible to use the value of `code` to determine what the name of the key is to users if they're not using an anticipated keyboard layout. To determine what character corresponds with the key event, use the {{domxref("KeyboardEvent.key")}} property instead. ## Value The code values for Windows, Linux, and macOS are listed on the [KeyboardEvent: code values](/en-US/docs/Web/API/UI_Events/Keyboard_event_code_values) page. ## Examples ### Exercising KeyboardEvent #### HTML ```html <p> Press keys on the keyboard to see what the KeyboardEvent's key and code values are for each one. </p> <div id="output" tabindex="0"></div> ``` #### CSS ```css #output { font-family: Arial, Helvetica, sans-serif; border: 1px solid black; width: 95%; margin: auto; } #output:focus-visible { outline: 3px solid dodgerblue; } ``` #### JavaScript ```js window.addEventListener( "keydown", (event) => { const p = document.createElement("p"); p.textContent = `KeyboardEvent: key='${event.key}' | code='${event.code}'`; document.getElementById("output").appendChild(p); window.scrollTo(0, document.body.scrollHeight); }, true, ); ``` #### Try it out To ensure that keystrokes go to the sample, click or focus the output box below before pressing keys. {{ EmbedLiveSample('Exercising_KeyboardEvent', 600, 300) }} ### Handle keyboard events in a game This example establishes an event listener for {{domxref("Element/keydown_event", "keydown")}} events that handle keyboard input for a game that uses the typical "WASD" keyboard layout for steering forward, left, backward, and right. This will use the same four keys physically regardless of what the actual corresponding characters are, such as if the user is using an AZERTY keyboard. #### HTML ```html <p>Use the WASD (ZQSD on AZERTY) keys to move and steer.</p> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" class="world" tabindex="0"> <polygon id="spaceship" points="15,0 0,30 30,30" /> </svg> ``` #### CSS ```css .world { margin: 0px; padding: 0px; background-color: black; width: 400px; height: 400px; } .world:focus-visible { outline: 5px solid dodgerblue; } #spaceship { fill: orange; stroke: red; stroke-width: 2px; } ``` #### JavaScript The first section of the JavaScript code establishes some variables we'll be using. `shipSize` contains the size of the ship the player is moving around, for convenience. `position` is used to track the position of the ship within the play field. `moveRate` is the number of pixels each keystroke moves the ship forward and backward, and `turnRate` is how many degrees of rotation the left and right steering controls apply per keystroke. `angle` is the current amount of rotation applied to the ship in degrees; it starts at 0 (pointing straight up). Finally, `spaceship` is set to refer to the element with the ID `"spaceship"`, which is the SVG polygon representing the ship the player controls. ```js let shipSize = { width: 30, height: 30, }; let position = { x: 200, y: 200, }; let moveRate = 9; let turnRate = 5; let angle = 0; let spaceship = document.getElementById("spaceship"); ``` Next comes the function `updatePosition()`. This function takes as input the distance the ship is to be moved, where positive is a forward movement and negative is a backward movement. This function computes the new position of the ship given the distance moved and the current direction the ship is facing. It also handles ensuring that the ship wraps across the boundaries of the play field instead of vanishing. ```js function updatePosition(offset) { let rad = angle * (Math.PI / 180); position.x += Math.sin(rad) * offset; position.y -= Math.cos(rad) * offset; if (position.x < 0) { position.x = 399; } else if (position.x > 399) { position.x = 0; } if (position.y < 0) { position.y = 399; } else if (position.y > 399) { position.y = 0; } } ``` The `refresh()` function handles applying the rotation and position by using an [SVG transform](/en-US/docs/Web/SVG/Reference/Attribute/transform). ```js function refresh() { let x = position.x - shipSize.width / 2; let y = position.y - shipSize.height / 2; let transform = `translate(${x} ${y}) rotate(${angle} 15 15) `; spaceship.setAttribute("transform", transform); } refresh(); ``` Finally, the `addEventListener()` method is used to start listening for {{domxref("Element/keydown_event", "keydown")}} events, acting on each key by updating the ship position and rotation angle, then calling `refresh()` to draw the ship at its new position and angle. ```js window.addEventListener( "keydown", (event) => { if (event.defaultPrevented) { return; // Do nothing if event already handled } switch (event.code) { case "KeyS": case "ArrowDown": // Handle "back" updatePosition(-moveRate); break; case "KeyW": case "ArrowUp": // Handle "forward" updatePosition(moveRate); break; case "KeyA": case "ArrowLeft": // Handle "turn left" angle -= turnRate; break; case "KeyD": case "ArrowRight": // Handle "turn right" angle += turnRate; break; } refresh(); if (event.code !== "Tab") { // Consume the event so it doesn't get handled twice, // as long as the user isn't trying to move focus away event.preventDefault(); } }, true, ); ``` #### Try it out To ensure that keystrokes go to the sample code, click or focus the black game play field below before pressing keys. {{EmbedLiveSample("Handle_keyboard_events_in_a_game", 420, 460)}} There are several ways this code can be made better. Most real games would watch for {{domxref("Element/keydown_event", "keydown")}} events, start motion when that happens, and stop the motion when the corresponding {{domxref("Element/keyup_event", "keyup")}} occurs, instead of relying on key repeats. That would allow both smoother and faster movement, but would also allow the player to be moving and steering at the same time. Transitions or animations could be used to make the ship's movement smoother, too. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}