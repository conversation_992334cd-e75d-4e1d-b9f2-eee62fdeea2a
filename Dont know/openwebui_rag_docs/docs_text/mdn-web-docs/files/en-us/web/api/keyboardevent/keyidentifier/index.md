Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md

Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > keyidentifier > index.md --- title: "KeyboardEvent: keyIdentifier property" short-title: keyIdentifier slug: Web/API/KeyboardEvent/keyIdentifier page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.KeyboardEvent.keyIdentifier --- {{APIRef("UI Events")}}{{non-standard_header}}{{deprecated_header}} The deprecated **`KeyboardEvent.keyIdentifier`** read-only property returns a "key identifier" string that can be used to determine what key was pressed. Its non-deprecated replacement is {{domxref("KeyboardEvent.key")}}. ## Specifications _Not part of any current specification._ This property was part of [an old draft of the DOM Level 3 Events Specification](https://www.w3.org/TR/2009/WD-DOM-Level-3-Events-20090908/#events-Events-KeyboardEvent-keyIdentifier), but it was removed in later drafts in favor of {{domxref("KeyboardEvent.key")}}. ## Browser compatibility {{Compat}}