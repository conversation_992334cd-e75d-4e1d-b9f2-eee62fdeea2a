Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md

Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md Path: mdn-web-docs > files > en-us > web > api > keyboardevent > repeat > index.md --- title: "KeyboardEvent: repeat property" short-title: repeat slug: Web/API/KeyboardEvent/repeat page-type: web-api-instance-property browser-compat: api.KeyboardEvent.repeat --- {{APIRef("UI Events")}} The **`repeat`** read-only property of the {{domxref("KeyboardEvent")}} interface returns a boolean value that is `true` if the given key is being held down such that it is automatically repeating. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}