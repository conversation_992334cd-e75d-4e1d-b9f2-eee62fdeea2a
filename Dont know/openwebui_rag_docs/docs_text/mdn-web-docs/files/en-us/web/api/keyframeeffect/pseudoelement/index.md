Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md

Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > pseudoelement > index.md --- title: "KeyframeEffect: pseudoElement property" short-title: pseudoElement slug: Web/API/KeyframeEffect/pseudoElement page-type: web-api-instance-property browser-compat: api.KeyframeEffect.pseudoElement --- {{ APIRef("Web Animations") }} The **`pseudoElement`** property of a {{domxref("KeyframeEffect")}} interface is a string representing the pseudo-element being animated. It may be `null` for animations that do not target a pseudo-element. It performs as both a getter and a setter, except with animations and transitions generated by CSS. > [!NOTE] > If set to the legacy single-colon syntax of {{cssxref("::before", ":before")}}, {{cssxref("::after", ":after")}}, {{cssxref("::first-letter", ":first-letter")}}, or {{cssxref("::first-line", ":first-line")}}, the string is transformed into its double-colon modern version ({{cssxref("::before")}}, {{cssxref("::after")}}, {{cssxref("::first-letter")}}, and {{cssxref("::first-line")}}, respectively). ## Value A string or `null`. ## Exceptions - `SyntaxError` {{domxref("DOMException")}} - : Thrown when trying to set this property to an element, an invalid pseudo-element (either non-existent or misspelled). The property is then left unchanged. ## Examples ```html <div id="text">Some text</div> <pre id="log"></pre> ``` ```css #text::after { content: " "; display: inline-block; /* Needed as the `transform` property does not apply to inline elements */ font-size: 2rem; } #text::before { content: " "; display: inline-block; font-size: 2rem; } ``` ```js const log = document.getElementById("log"); const text = document.getElementById("text"); // Create the keyframe and launch the animation const animation = text.animate([{ transform: "rotate(360deg)" }], { duration: 3000, iterations: Infinity, pseudoElement: "::after", }); // Get the value of KeyframeEffect.pseudoElement function logPseudoElement() { const keyframeEffect = animation.effect; log.textContent = `Value of pseudoElement animated: ${keyframeEffect.pseudoElement}`; requestAnimationFrame(logPseudoElement); } // Every 6 seconds, switch the pseudo-element animated function switchPseudoElement() { const keyframeEffect = animation.effect; keyframeEffect.pseudoElement = keyframeEffect.pseudoElement === "::after" ? "::before" : "::after"; setTimeout(switchPseudoElement, 6000); } switchPseudoElement(); logPseudoElement(); ``` {{EmbedLiveSample("Examples", "100", "90")}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - {{domxref("KeyframeEffect")}} interface - {{domxref("KeyframeEffect.KeyframeEffect", "KeyframeEffect()")}} constructor - {{domxref("KeyframeEffect.target", "target")}} property