Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md

Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md Path: mdn-web-docs > files > en-us > web > api > keyframeeffect > target > index.md --- title: "KeyframeEffect: target property" short-title: target slug: Web/API/KeyframeEffect/target page-type: web-api-instance-property browser-compat: api.KeyframeEffect.target --- {{ APIRef("Web Animations") }} The **`target`** property of a {{domxref("KeyframeEffect")}} interface represents the element or pseudo-element being animated. It may be `null` for animations that do not target a specific element. It performs as both a getter and a setter, except with animations and transitions generated by CSS. ## Value An {{domxref("Element")}} or `null`. ## Examples In the following example, `emoji` has been set as the `target` element to be animated: ```js const emoji = document.querySelector("div"); // element to animate const rollingKeyframes = new KeyframeEffect( emoji, [ { transform: "translateX(0) rotate(0)" }, // keyframe { transform: "translateX(200px) rotate(1.3turn)" }, // keyframe ], { // keyframe options duration: 2000, direction: "alternate", easing: "ease-in-out", iterations: "Infinity", }, ); const rollingAnimation = new Animation(rollingKeyframes, document.timeline); rollingAnimation.play(); // logs "<div> </div>" console.log(rollingKeyframes.target); ``` ```html <div> </div> ``` ```css hidden body { box-shadow: 0 5px 5px pink; } div { width: fit-content; margin-left: calc(50% - 132px); font-size: 64px; user-select: none; margin-top: 1rem; } ``` {{ EmbedLiveSample("Examples", "100%", "120") }} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - Property of {{domxref("KeyframeEffect")}} objects.