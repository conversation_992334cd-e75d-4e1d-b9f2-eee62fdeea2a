Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md

Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md Path: mdn-web-docs > files > en-us > web > api > location > protocol > index.md --- title: "Location: protocol property" short-title: protocol slug: Web/API/Location/protocol page-type: web-api-instance-property browser-compat: api.Location.protocol --- {{ApiRef("Location")}} The **`protocol`** property of the {{domxref("Location")}} interface is a string containing the protocol or scheme of the location's URL, including the final `":"`. This property can be set to change the protocol of the URL. A `":"` is appended to the provided string if not provided. The provided scheme has to be compatible with the rest of the URL to be considered valid. See {{domxref("URL.protocol")}} for more information. ## Value A string. ## Examples ```js // Let's an <a id="myAnchor" href="https://developer.mozilla.org/en-US/Location.protocol"> element be in the document const anchor = document.getElementById("myAnchor"); const result = anchor.protocol; // Returns:'https:' ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}