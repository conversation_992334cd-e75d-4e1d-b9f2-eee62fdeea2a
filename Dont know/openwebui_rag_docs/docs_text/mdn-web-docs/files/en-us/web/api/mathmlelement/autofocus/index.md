Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md

Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > mathmlelement > autofocus > index.md --- title: "MathMLElement: autofocus property" short-title: autofocus slug: Web/API/MathMLElement/autofocus page-type: web-api-instance-property browser-compat: api.MathMLElement.autofocus --- {{APIRef("MathML")}} The **`autofocus`** property of the {{domxref("MathMLElement")}} interface contains a boolean value reflecting the [`autofocus`](/en-US/docs/Web/HTML/Reference/Global_attributes/autofocus) HTML global attribute. It indicates whether the MathML element should be focused when the page loads or when the element becomes shown if the MathML element is inside a {{htmlelement("dialog")}} or a [popover](/en-US/docs/Web/HTML/Reference/Global_attributes/popover). ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MathMLElement.focus()")}} - {{domxref("HTMLElement.autofocus")}} - [Popover API](/en-US/docs/Web/API/Popover_API)