Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md

Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md Path: mdn-web-docs > files > en-us > web > api > media_capture_and_streams_api > index.md --- title: Media Capture and Streams API (Media Stream) slug: Web/API/Media_Capture_and_Streams_API page-type: web-api-overview browser-compat: - api.MediaStream - api.MediaStreamTrack - api.MediaDevices - api.MediaDeviceInfo - api.InputDeviceInfo - api.CanvasCaptureMediaStreamTrack spec-urls: - https://w3c.github.io/mediacapture-main/ - https://w3c.github.io/mediacapture-fromelement/ --- {{DefaultAPISidebar("Media Capture and Streams")}} The **Media Capture and Streams** API, often called the **Media Streams API** or **MediaStream API**, is an API related to [WebRTC](/en-US/docs/Web/API/WebRTC_API) which provides support for streaming audio and video data. It provides the interfaces and methods for working with the streams and their constituent tracks, the constraints associated with data formats, the success and error callbacks when using the data asynchronously, and the events that are fired during the process. ## Concepts and usage The API is based on the manipulation of a {{domxref("MediaStream")}} object representing a flux of audio- or video-related data. See an example in [Get the media stream](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Taking_still_photos#the_startup_function). A `MediaStream` consists of zero or more {{domxref("MediaStreamTrack")}} objects, representing various audio or video **tracks**. Each `MediaStreamTrack` may have one or more **channels**. The channel represents the smallest unit of a media stream, such as an audio signal associated with a given speaker, like _left_ or _right_ in a stereo audio track. `MediaStream` objects have a single **input** and a single **output**. A `MediaStream` object generated by {{domxref("MediaDevices.getUserMedia", "getUserMedia()")}} is called _local_, and has as its source input one of the user's cameras or microphones. A non-local `MediaStream` may be representing a media element, like {{HTMLElement("video")}} or {{HTMLElement("audio")}}, a stream originating over the network, and obtained via the WebRTC {{domxref("RTCPeerConnection")}} API, or a stream created using the [Web Audio API](/en-US/docs/Web/API/Web_Audio_API) {{domxref("MediaStreamAudioDestinationNode")}}. The output of the `MediaStream` object is linked to a **consumer**. It can be a media element, like {{HTMLElement("audio")}} or {{HTMLElement("video")}}, the WebRTC {{domxref("RTCPeerConnection")}} API or a [Web Audio API](/en-US/docs/Web/API/Web_Audio_API) {{domxref("MediaStreamAudioSourceNode")}}. ## Interfaces In these reference articles, you'll find the fundamental information you'll need to know about each of the interfaces that make up the Media Capture and Streams API. - {{domxref("CanvasCaptureMediaStreamTrack")}} - {{domxref("InputDeviceInfo")}} - {{domxref("MediaDeviceInfo")}} - {{domxref("MediaDevices")}} - {{domxref("MediaStream")}} - {{domxref("MediaStreamTrack")}} - {{domxref("MediaStreamTrackEvent")}} - {{domxref("MediaTrackConstraints")}} - {{domxref("MediaTrackSettings")}} - {{domxref("MediaTrackSupportedConstraints")}} - {{domxref("OverconstrainedError")}} ## Events - {{domxref("MediaStream/addtrack_event", "addtrack")}} - {{domxref("MediaStreamTrack/ended_event", "ended")}} - {{domxref("MediaStreamTrack/mute_event", "mute")}} - {{domxref("MediaStream/removetrack_event", "removetrack")}} - {{domxref("MediaStreamTrack/unmute_event", "unmute")}} ## Guides and tutorials The [Capabilities, constraints, and settings](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Constraints) article discusses the concepts of **constraints** and **capabilities**, as well as media settings, and includes a [Constraint Exerciser](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Constraints#example_constraint_exerciser) that lets you experiment with the results of different constraint sets being applied to the audio and video tracks coming from the computer's A/V input devices (such as its webcam and microphone). The [Taking still photos with getUserMedia()](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Taking_still_photos) article shows how to use [`getUserMedia()`](/en-US/docs/Web/API/MediaDevices/getUserMedia) to access the camera on a computer or mobile phone with `getUserMedia()` support and take a photo with it. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [WebRTC](/en-US/docs/Web/API/WebRTC_API) - the introductory page to the API - [Taking still photos with WebRTC](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Taking_still_photos): a demonstration and tutorial about using `getUserMedia()`.