Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md

Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md Path: mdn-web-docs > files > en-us > web > api > mediadeviceinfo > kind > index.md --- title: "MediaDeviceInfo: kind property" short-title: kind slug: Web/API/MediaDeviceInfo/kind page-type: web-api-instance-property browser-compat: api.MediaDeviceInfo.kind --- {{APIRef("Media Capture and Streams")}}{{securecontext_header}} The **`kind`** read-only property of the {{domxref("MediaDeviceInfo")}} interface returns an enumerated value, that is either `"videoinput"`, `"audioinput"` or `"audiooutput"`. ## Value One of `"videoinput"`, `"audioinput"` or `"audiooutput"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}