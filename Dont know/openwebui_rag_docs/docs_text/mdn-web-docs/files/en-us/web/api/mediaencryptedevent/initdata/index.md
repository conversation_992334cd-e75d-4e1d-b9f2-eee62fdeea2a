Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md

Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdata > index.md --- title: "MediaEncryptedEvent: initData property" short-title: initData slug: Web/API/MediaEncryptedEvent/initData page-type: web-api-instance-property browser-compat: api.MediaEncryptedEvent.initData --- {{APIRef("Encrypted Media Extensions")}} The read-only **`initData`** property of the {{domxref("MediaKeyMessageEvent")}} returns the initialization data contained in this event, if any. ## Value A {{jsxref("ArrayBuffer")}} with the initialization data, or `null` if there are none. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}