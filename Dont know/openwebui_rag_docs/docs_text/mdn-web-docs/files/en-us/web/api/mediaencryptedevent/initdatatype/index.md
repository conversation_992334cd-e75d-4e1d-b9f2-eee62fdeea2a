Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md

Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > initdatatype > index.md --- title: "MediaEncryptedEvent: initDataType property" short-title: initDataType slug: Web/API/MediaEncryptedEvent/initDataType page-type: web-api-instance-property browser-compat: api.MediaEncryptedEvent.initDataType --- {{APIRef("Encrypted Media Extensions")}} The read-only **`initDataType`** property of the {{domxref("MediaKeyMessageEvent")}} returns a case-sensitive string describing the type of the initialization data associated with this event. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}