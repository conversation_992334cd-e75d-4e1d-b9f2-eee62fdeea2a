Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md

Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediaencryptedevent > mediaencryptedevent > index.md --- title: "MediaEncryptedEvent: MediaEncryptedEvent() constructor" short-title: MediaEncryptedEvent() slug: Web/API/MediaEncryptedEvent/MediaEncryptedEvent page-type: web-api-constructor browser-compat: api.MediaEncryptedEvent.MediaEncryptedEvent --- {{APIRef("Encrypted Media Extensions")}} The **`MediaEncryptedEvent`** constructor creates a new {{domxref("MediaEncryptedEvent")}} object. > [!NOTE] > In normal cases, you don't need to call this constructor in your code as such events are usually generated by the browser when needed. ## Syntax ```js-nolint new MediaEncryptedEvent(type) new MediaEncryptedEvent(type, options) ``` ### Parameters - `type` - : A string with the name of the event. It is case-sensitive and browsers always set it to `encrypted`. - `options` {{optional_inline}} - : An object that, _in addition of the properties defined in {{domxref("Event/Event", "Event()")}}_, can have the following properties: - `initDataType` - : A string with the type of the initialization data contained in this object - `message` - : An {{jsxref("ArrayBuffer")}} with the initialization data, or `null` if there is none. ### Return value A new {{domxref("MediaEncryptedEvent")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}