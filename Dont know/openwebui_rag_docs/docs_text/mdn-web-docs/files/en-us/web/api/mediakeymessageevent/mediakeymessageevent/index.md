Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > mediakeymessageevent > index.md --- title: "MediaKeyMessageEvent: MediaKeyMessageEvent() constructor" short-title: MediaKeyMessageEvent() slug: Web/API/MediaKeyMessageEvent/MediaKeyMessageEvent page-type: web-api-constructor browser-compat: api.MediaKeyMessageEvent.MediaKeyMessageEvent --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`MediaKeyMessageEvent`** constructor creates a new {{domxref("MediaKeyMessageEvent")}} object. ## Syntax ```js-nolint new MediaKeyMessageEvent(type) new MediaKeyMessageEvent(type, options) ``` ### Parameters - `type` - : A string with the name of the event. It is case-sensitive and browsers always set it to `message`. - `options` {{optional_inline}} - : An object that, _in addition of the properties defined in {{domxref("Event/Event", "Event()")}}_, can have the following properties: - `messageType` - : A message type that allows applications to differentiate messages without parsing them. Allowed values are: `license-request`, `license-renewal`, `license-renewal`, or `individualization-request`. - `message` - : An array containing the message generated by the content decryption module. ### Return value A new {{domxref("MediaKeyMessageEvent")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}