Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > message > index.md --- title: "MediaKeyMessageEvent: message property" short-title: message slug: Web/API/MediaKeyMessageEvent/message page-type: web-api-instance-property browser-compat: api.MediaKeyMessageEvent.message --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`MediaKeyMessageEvent.message`** read-only property returns an {{jsxref("ArrayBuffer")}} with a message from the content decryption module. Messages vary by key system. ## Value An {{jsxref("ArrayBuffer")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}