Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeymessageevent > messagetype > index.md --- title: "MediaKeyMessageEvent: messageType property" short-title: messageType slug: Web/API/MediaKeyMessageEvent/messageType page-type: web-api-instance-property browser-compat: api.MediaKeyMessageEvent.messageType --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`MediaKeyMessageEvent.messageType`** read-only property indicates the type of message. It may be one of `license-request`, `license-renewal`, `license-release`, or `individualization-request`. ## Value One of the following: - `license-request` - `license-renewal` - `license-release` - `individualization-request` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}