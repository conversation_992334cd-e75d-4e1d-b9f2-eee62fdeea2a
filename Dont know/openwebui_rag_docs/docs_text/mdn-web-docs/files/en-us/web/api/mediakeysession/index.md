Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > index.md --- title: MediaKeySession slug: Web/API/MediaKeySession page-type: web-api-interface browser-compat: api.MediaKeySession --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`MediaKeySession`** interface of the [Encrypted Media Extensions API](/en-US/docs/Web/API/Encrypted_Media_Extensions_API) represents a context for message exchange with a content decryption module (CDM). {{InheritanceDiagram}} ## Instance properties - {{domxref("MediaKeySession.closed")}} {{ReadOnlyInline}} - : Returns a {{jsxref("Promise")}} signaling when a `MediaKeySession` closes. This promise can only be fulfilled and is never rejected. Closing a session means that licenses and keys associated with it are no longer valid for decrypting media data. - {{domxref("MediaKeySession.expiration")}} {{ReadOnlyInline}} - : The time after which the keys in the current session can no longer be used to decrypt media data, or `NaN` if no such time exists. This value is determined by the CDM and measured in milliseconds since January 1, 1970, UTC. This value may change during a session lifetime, such as when an action triggers the start of a window. - {{domxref("MediaKeySession.keyStatuses")}} {{ReadOnlyInline}} - : Contains a reference to a read-only {{domxref("MediaKeyStatusMap")}} of the current session's keys and their statuses. - {{domxref("MediaKeySession.sessionId")}} {{ReadOnlyInline}} - : Contains a unique string generated by the CDM for the current media object and its associated keys or licenses. ### Events - {{domxref("MediaKeySession.keystatuseschange_event", "keystatuseschange")}} - : Fires when there has been a change in the keys in a session or their statuses. - {{domxref("MediaKeySession.message_event", "message")}} - : Fires when the content decryption module has generated a message for the session. ## Instance methods - {{domxref("MediaKeySession.close()")}} - : Returns a {{jsxref("Promise")}} after notifying the current media session is no longer needed and that the CDM should release any resources associated with this object and close it. - {{domxref("MediaKeySession.generateRequest()")}} - : Returns a {{jsxref("Promise")}} after generating a licence request based on initialization data. - {{domxref("MediaKeySession.load()")}} - : Returns a {{jsxref("Promise")}} that resolves to a boolean value after loading data for a specified session object. - {{domxref("MediaKeySession.remove()")}} - : Returns a {{jsxref("Promise")}} after removing any session data associated with the current object. - {{domxref("MediaKeySession.update()")}} - : Returns a {{jsxref("Promise")}} after loading messages and licenses to the CDM. ## Examples ```js // TBD ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}