Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > keystatuses > index.md --- title: "MediaKeySession: keyStatuses property" short-title: keyStatuses slug: Web/API/MediaKeySession/keyStatuses page-type: web-api-instance-property browser-compat: api.MediaKeySession.keyStatuses --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`keyStatuses`** read-only property of the {{domxref('MediaKeySession')}} interface returns a reference to a read-only {{domxref('MediaKeyStatusMap')}} of the current session's keys and their statuses. ## Value A {{domxref('MediaKeyStatusMap')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}