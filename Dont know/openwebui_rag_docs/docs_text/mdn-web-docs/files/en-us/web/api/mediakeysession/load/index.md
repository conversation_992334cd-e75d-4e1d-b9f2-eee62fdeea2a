Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > load > index.md --- title: "MediaKeySession: load() method" short-title: load() slug: Web/API/MediaKeySession/load page-type: web-api-instance-method browser-compat: api.MediaKeySession.load --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The `load()` method of the {{domxref('MediaKeySession')}} interface returns a {{jsxref('Promise')}} that resolves to a boolean value after loading data for a specified session object. ## Syntax ```js-nolint load(sessionId) ``` ### Parameters - `sessionId` - : A unique string generated by the content description module for the current media object and its associated keys or licenses. ### Return value A {{jsxref('Promise')}} that resolves to a boolean indicating whether the load succeeded or failed. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}