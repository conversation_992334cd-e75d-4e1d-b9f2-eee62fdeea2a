Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > message_event > index.md --- title: "MediaKeySession: message event" short-title: message slug: Web/API/MediaKeySession/message_event page-type: web-api-event browser-compat: api.MediaKeySession.message_event --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`message`** event of the {{domxref("MediaKeySession")}} interface fires when a message is generated by the content decryption module. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("message", (event) => { }) onmessage = (event) => { } ``` ## Event type A {{domxref("MediaKeyMessageEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("MediaKeyMessageEvent")}} ## Event properties - {{domxref("MediaKeyMessageEvent.message")}} {{ReadOnlyInline}} - : Returns an {{jsxref("ArrayBuffer")}} with a message from the content decryption module. Messages vary by key system. - {{domxref("MediaKeyMessageEvent.messageType")}} {{ReadOnlyInline}} - : Indicates the type of message. May be one of `license-request`, `license-renewal`, `license-release`, or `individualization-request`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}