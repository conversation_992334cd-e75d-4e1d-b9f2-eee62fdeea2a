Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > remove > index.md --- title: "MediaKeySession: remove() method" short-title: remove() slug: Web/API/MediaKeySession/remove page-type: web-api-instance-method browser-compat: api.MediaKeySession.remove --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The `remove()` method of the {{domxref('MediaKeySession')}} interface returns a {{jsxref('Promise')}} after removing any session data associated with the current object. ## Syntax ```js-nolint remove() ``` ### Parameters None. ### Return value A {{jsxref('Promise')}} that resolves to {{jsxref("undefined")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}