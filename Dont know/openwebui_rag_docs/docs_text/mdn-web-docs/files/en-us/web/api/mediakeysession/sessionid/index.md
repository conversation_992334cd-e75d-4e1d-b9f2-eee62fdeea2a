Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysession > sessionid > index.md --- title: "MediaKeySession: sessionId property" short-title: sessionId slug: Web/API/MediaKeySession/sessionId page-type: web-api-instance-property browser-compat: api.MediaKeySession.sessionId --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`sessionId`** read-only property of the {{domxref('MediaKeySession')}} interface contains a unique string generated by the content decryption module (CDM) for the current media object and its associated keys or licenses. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}