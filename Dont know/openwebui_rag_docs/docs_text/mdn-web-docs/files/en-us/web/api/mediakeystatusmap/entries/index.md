Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > entries > index.md --- title: "MediaKeyStatusMap: entries() method" short-title: entries() slug: Web/API/MediaKeyStatusMap/entries page-type: web-api-instance-method browser-compat: api.MediaKeyStatusMap.entries --- {{APIRef("Encrypted Media Extensions")}} The **`entries()`** method of the {{domxref("MediaKeyStatusMap")}} interface returns a new Iterator object, containing an array of \[key, value] pairs for each element in the status map, in insertion order. ## Syntax ```js-nolint entries() ``` ### Parameters None. ### Return value A new Iterator object. Each element in the Iterator is an array containing a key and a value. ### Exceptions ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}