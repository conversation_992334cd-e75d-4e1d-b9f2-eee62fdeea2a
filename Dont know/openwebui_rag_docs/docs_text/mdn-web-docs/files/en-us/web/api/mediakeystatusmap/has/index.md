Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > has > index.md --- title: "MediaKeyStatusMap: has() method" short-title: has() slug: Web/API/MediaKeyStatusMap/has page-type: web-api-instance-method browser-compat: api.MediaKeyStatusMap.has --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`has()`** method of the {{domxref("MediaKeyStatusMap")}} interface returns a {{jsxref('Boolean')}}, asserting whether a value has been associated with the given key. ## Syntax ```js-nolint has(key) ``` ### Parameters - `key` - : The key whose value you want returned ### Return value A {{jsxref('Boolean')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}