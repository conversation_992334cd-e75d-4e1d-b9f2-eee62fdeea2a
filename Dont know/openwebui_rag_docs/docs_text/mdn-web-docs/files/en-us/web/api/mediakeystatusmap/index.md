Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > index.md --- title: MediaKeyStatusMap slug: Web/API/MediaKeyStatusMap page-type: web-api-interface browser-compat: api.MediaKeyStatusMap --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`MediaKeyStatusMap`** interface of the [Encrypted Media Extensions API](/en-US/docs/Web/API/Encrypted_Media_Extensions_API) is a read-only map of media key statuses by key IDs. ## Instance properties - {{domxref("MediaKeyStatusMap.size")}} {{ReadOnlyInline}} - : Returns the number of key/value pairs in the status map. ## Instance methods - {{domxref("MediaKeyStatusMap.entries()")}} {{ReadOnlyInline}} - : Returns a new `Iterator` object containing an array of `[key, value]` for each element in the status map, in insertion order. - {{domxref("MediaKeyStatusMap.forEach()")}} {{ReadOnlyInline}} - : Calls `callback` once for each key-value pair in the status map, in insertion order. If `argument` is present it will be passed to the callback. - {{domxref("MediaKeyStatusMap.get()")}} {{ReadOnlyInline}} - : Returns the value associated with the given key, or `undefined` if there is none. - {{domxref("MediaKeyStatusMap.has()")}} {{ReadOnlyInline}} - : Returns a boolean asserting whether a value has been associated with the given key. - {{domxref("MediaKeyStatusMap.keys()")}} {{ReadOnlyInline}} - : Returns a new `Iterator` object containing keys for each element in the status map, in insertion order. - {{domxref("MediaKeyStatusMap.values()")}} {{ReadOnlyInline}} - : Returns a new `Iterator` object containing values for each element in the status map, in insertion order. - `MediaKeyStatusMap[Symbol.iterator]()` {{ReadOnlyInline}} - : Returns a new `Iterator` object containing an array of `[key, value]` for each element in the status map, in insertion order. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}