Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > keys > index.md --- title: "MediaKeyStatusMap: keys() method" short-title: keys() slug: Web/API/MediaKeyStatusMap/keys page-type: web-api-instance-method browser-compat: api.MediaKeyStatusMap.keys --- {{APIRef("Encrypted Media Extensions")}} The **`keys()`** method of the {{domxref("MediaKeyStatusMap")}} interface returns a new Iterator object, containing keys for each element in the status map, in insertion order. ## Syntax ```js-nolint keys() ``` ### Parameters None. ### Return value A new iterator. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}