Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > size > index.md --- title: "MediaKeyStatusMap: size property" short-title: size slug: Web/API/MediaKeyStatusMap/size page-type: web-api-instance-property browser-compat: api.MediaKeyStatusMap.size --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`size`** read-only property of the {{domxref("MediaKeyStatusMap")}} interface returns the number of key/value paIrs in the status map. ## Value A long integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}