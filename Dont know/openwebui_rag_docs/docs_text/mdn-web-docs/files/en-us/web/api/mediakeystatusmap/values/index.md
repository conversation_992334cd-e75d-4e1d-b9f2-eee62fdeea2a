Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeystatusmap > values > index.md --- title: "MediaKeyStatusMap: values() method" short-title: values() slug: Web/API/MediaKeyStatusMap/values page-type: web-api-instance-method browser-compat: api.MediaKeyStatusMap.values --- {{APIRef("Encrypted Media Extensions")}} The **`values()`** method of the {{domxref("MediaKeyStatusMap")}} interface returns a new Iterator object, containing values for each element in the status map, in insertion order. ## Syntax ```js-nolint values() ``` ### Parameters None. ### Return value A new iterator. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}