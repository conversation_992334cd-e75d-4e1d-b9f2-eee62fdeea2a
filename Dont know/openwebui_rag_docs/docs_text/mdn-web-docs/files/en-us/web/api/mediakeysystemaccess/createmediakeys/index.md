Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > createmediakeys > index.md --- title: "MediaKeySystemAccess: createMediaKeys() method" short-title: createMediaKeys() slug: Web/API/MediaKeySystemAccess/createMediaKeys page-type: web-api-instance-method browser-compat: api.MediaKeySystemAccess.createMediaKeys --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The `MediaKeySystemAccess.createMediaKeys()` method returns a {{jsxref('Promise')}} that resolves to a new {{domxref('MediaKeys')}} object. ## Syntax ```js-nolint createMediaKeys() ``` ### Parameters None. ### Return value A {{jsxref('Promise')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}