Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md

Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md Path: mdn-web-docs > files > en-us > web > api > mediakeysystemaccess > keysystem > index.md --- title: "MediaKeySystemAccess: keySystem property" short-title: keySystem slug: Web/API/MediaKeySystemAccess/keySystem page-type: web-api-instance-property browser-compat: api.MediaKeySystemAccess.keySystem --- {{APIRef("Encrypted Media Extensions")}}{{SecureContext_Header}} The **`keySystem`** read-only property of the {{domxref("MediaKeySystemAccess")}} interface returns a string identifying the key system being used. This is the value that was passed to {{domxref("Navigator.requestMediaKeySystemAccess()")}} when system access is requested. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}