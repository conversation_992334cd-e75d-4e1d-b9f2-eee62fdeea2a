Path: mdn-web-docs > files > en-us > web > api > mediarecorder > audiobitspersecond > index.md

Path: mdn-web-docs > files > en-us > web > api > mediarecorder > audiobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > audiobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > audiobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > audiobitspersecond > index.md --- title: "MediaRecorder: audioBitsPerSecond property" short-title: audioBitsPerSecond slug: Web/API/MediaRecorder/audioBitsPerSecond page-type: web-api-instance-property browser-compat: api.MediaRecorder.audioBitsPerSecond --- {{APIRef("MediaStream Recording")}} The **`audioBitsPerSecond`** read-only property of the {{domxref("MediaRecorder")}} interface returns the audio encoding bit rate in use. This may differ from the bit rate specified in the constructor (if it was provided). ## Value A {{jsxref("Number")}} (unsigned long). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}