Path: mdn-web-docs > files > en-us > web > api > mediarecorder > resume_event > index.md

Path: mdn-web-docs > files > en-us > web > api > mediarecorder > resume_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > resume_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > resume_event > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > resume_event > index.md --- title: "MediaRecorder: resume event" short-title: resume slug: Web/API/MediaRecorder/resume_event page-type: web-api-event browser-compat: api.MediaRecorder.resume_event --- {{APIRef("MediaStream Recording")}} The **`resume`** event of the {{domxref("MediaRecorder")}} interface is fired when {{domxref("MediaRecorder.resume()")}} is called. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("resume", (event) => { }) onresume = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Example ```js pause.onclick = () => { if (MediaRecorder.state === "recording") { mediaRecorder.pause(); // recording paused } else if (MediaRecorder.state === "paused") { mediaRecorder.resume(); // resume recording } }; mediaRecorder.onpause = () => { // do something in response to // recording being paused }; mediaRecorder.onresume = () => { // do something in response to // recording being resumed }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the MediaStream Recording API](/en-US/docs/Web/API/MediaStream_Recording_API/Using_the_MediaStream_Recording_API) - [Web Dictaphone](https://mdn.github.io/dom-examples/media/web-dictaphone/): MediaRecorder + getUserMedia + Web Audio API visualization demo, by [Chris Mills](https://github.com/chrisdavidmills) ([source on GitHub](https://github.com/mdn/dom-examples/tree/main/media/web-dictaphone).) - [simpl.info MediaStream Recording demo](https://simpl.info/mediarecorder/), by [Sam Dutton](https://github.com/samdutton). - {{domxref("Navigator.getUserMedia")}}