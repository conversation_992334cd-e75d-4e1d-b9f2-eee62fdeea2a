Path: mdn-web-docs > files > en-us > web > api > mediarecorder > videobitspersecond > index.md

Path: mdn-web-docs > files > en-us > web > api > mediarecorder > videobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > videobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > videobitspersecond > index.md Path: mdn-web-docs > files > en-us > web > api > mediarecorder > videobitspersecond > index.md --- title: "MediaRecorder: videoBitsPerSecond property" short-title: videoBitsPerSecond slug: Web/API/MediaRecorder/videoBitsPerSecond page-type: web-api-instance-property browser-compat: api.MediaRecorder.videoBitsPerSecond --- {{APIRef("MediaStream Recording")}} The **`videoBitsPerSecond`** read-only property of the {{domxref("MediaRecorder")}} interface returns the video encoding bit rate in use. This may differ from the bit rate specified in the constructor, if it was provided. ## Value A {{jsxref("Number")}} (unsigned long). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}