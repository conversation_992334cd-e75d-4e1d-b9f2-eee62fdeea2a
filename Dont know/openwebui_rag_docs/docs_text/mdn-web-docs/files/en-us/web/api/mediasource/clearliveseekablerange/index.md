Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md

Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > clearliveseekablerange > index.md --- title: "MediaSource: clearLiveSeekableRange() method" short-title: clearLiveSeekableRange() slug: Web/API/MediaSource/clearLiveSeekableRange page-type: web-api-instance-method browser-compat: api.MediaSource.clearLiveSeekableRange --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`clearLiveSeekableRange()`** method of the {{domxref("MediaSource")}} interface clears a seekable range previously set with a call to {{domxref("MediaSource.setLiveSeekableRange()","setLiveSeekableRange()")}}. ## Syntax ```js-nolint clearLiveSeekableRange() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}