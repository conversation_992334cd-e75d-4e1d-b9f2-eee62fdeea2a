Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md

Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md Path: mdn-web-docs > files > en-us > web > api > mediasource > mediasource > index.md --- title: "MediaSource: MediaSource() constructor" short-title: MediaSource() slug: Web/API/MediaSource/MediaSource page-type: web-api-constructor browser-compat: api.MediaSource.MediaSource --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`MediaSource()`** constructor of the {{domxref("MediaSource")}} interface constructs and returns a new `MediaSource` object with no associated source buffers. ## Syntax ```js-nolint new MediaSource() ``` ### Parameters None. ## Examples The following snippet is taken from an example written by Nick Desaulniers ([view the full demo live](https://nickdesaulniers.github.io/netfix/demo/bufferAll.html), or [download the source](https://github.com/nickdesaulniers/netfix/blob/gh-pages/demo/bufferAll.html) for further investigation). ```js const video = document.querySelector("video"); const assetURL = "frag_bunny.mp4"; // Need to be specific for Blink regarding codecs // ./mp4info frag_bunny.mp4 | grep Codec const mimeCodec = 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"'; if ("MediaSource" in window && MediaSource.isTypeSupported(mimeCodec)) { const mediaSource = new MediaSource(); // console.log(mediaSource.readyState); // closed video.src = URL.createObjectURL(mediaSource); mediaSource.addEventListener("sourceopen", sourceOpen); } else { console.error("Unsupported MIME type or codec: ", mimeCodec); } // ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SourceBuffer")}} - {{domxref("SourceBufferList")}}