Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md

Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md Path: mdn-web-docs > files > en-us > web > api > mediastream_recording_api > index.md --- title: MediaStream Recording API slug: Web/API/MediaStream_Recording_API page-type: web-api-overview browser-compat: api.MediaRecorder spec-urls: https://w3c.github.io/mediacapture-record/ --- {{DefaultAPISidebar("MediaStream Recording")}} The **MediaStream Recording API**, sometimes referred to as the _Media Recording API_ or the _MediaRecorder API_, is closely affiliated with the [Media Capture and Streams API](/en-US/docs/Web/API/Media_Capture_and_Streams_API) and the [WebRTC API](/en-US/docs/Web/API/WebRTC_API). The MediaStream Recording API makes it possible to capture the data generated by a {{domxref("MediaStream")}} or {{domxref("HTMLMediaElement")}} object for analysis, processing, or saving to disk. It's also surprisingly easy to work with. ## Concepts and usage The MediaStream Recording API is comprised of a single major interface, {{domxref("MediaRecorder")}}, which does all the work of taking the data from a {{domxref("MediaStream")}} and delivering it to you for processing. The data is delivered by a series of {{domxref("MediaRecorder.dataavailable_event", "dataavailable")}} events, already in the format you specify when creating the `MediaRecorder`. You can then process the data further or write it to file as desired. ### Overview of the recording process The process of recording a stream is simple: 1. Set up a {{domxref("MediaStream")}} or {{domxref("HTMLMediaElement")}} (in the form of an {{HTMLElement("audio")}} or {{HTMLElement("video")}} element) to serve as the source of the media data. 2. Create a {{domxref("MediaRecorder")}} object, specifying the source stream and any desired options (such as the container's MIME type or the desired bit rates of its tracks). 3. Set {{domxref("MediaRecorder.dataavailable_event", "ondataavailable")}} to an event handler for the {{domxref("MediaRecorder.dataavailable_event", "dataavailable")}} event; this will be called whenever data is available for you. 4. Once the source media is playing and you've reached the point where you're ready to record video, call {{domxref("MediaRecorder.start()")}} to begin recording. 5. Your {{domxref("MediaRecorder.dataavailable_event", "dataavailable")}} event handler gets called every time there's data ready for you to do with as you will; the event has a `data` attribute whose value is a {{domxref("Blob")}} that contains the media data. You can force a `dataavailable` event to occur, thereby delivering the latest sound to you so you can filter it, save it, or whatever. 6. Recording stops automatically when the source media stops playing. 7. You can stop recording at any time by calling {{domxref("MediaRecorder.stop()")}}. > [!NOTE] > Individual {{domxref("Blob")}}s containing slices of the recorded media will not necessarily be individually playable. The media needs to be reassembled before playback. If anything goes wrong during recording, an {{domxref("MediaRecorder/error_event", "error")}} event is sent to the `MediaRecorder`. You can listen for `error` events by setting up a {{domxref("MediaRecorder.error_event", "onerror")}} event handler. Example here, we use an HTML Canvas as source of the {{domxref("MediaStream")}}, and stop recording after 9 seconds. ```js const canvas = document.querySelector("canvas"); // Optional frames per second argument. const stream = canvas.captureStream(25); const recordedChunks = []; console.log(stream); const options = { mimeType: "video/webm; codecs=vp9" }; const mediaRecorder = new MediaRecorder(stream, options); mediaRecorder.ondataavailable = handleDataAvailable; mediaRecorder.start(); function handleDataAvailable(event) { console.log("data-available"); if (event.data.size > 0) { recordedChunks.push(event.data); console.log(recordedChunks); download(); } else { // } } function download() { const blob = new Blob(recordedChunks, { type: "video/webm", }); const url = URL.createObjectURL(blob); const a = document.createElement("a"); document.body.appendChild(a); a.style = "display: none"; a.href = url; a.download = "test.webm"; a.click(); URL.revokeObjectURL(url); } // demo: to download after 9sec setTimeout((event) => { console.log("stopping"); mediaRecorder.stop(); }, 9000); ``` ### Examining and controlling the recorder status You can also use the properties of the `MediaRecorder` object to determine the state of the recording process, and its {{domxref("MediaRecorder.pause", "pause()")}} and {{domxref("MediaRecorder.resume", "resume()")}} methods to pause and resume recording of the source media. If you need or want to check to see if a specific MIME type is supported, that's possible as well. Just call {{domxref("MediaRecorder.isTypeSupported_static", "MediaRecorder.isTypeSupported()")}}. ### Examining potential input sources If your goal is to record camera and/or microphone input, you may wish to examine the available input devices before beginning the process of constructing the `MediaRecorder`. To do so, you'll need to call {{domxref("MediaDevices.enumerateDevices", "navigator.mediaDevices.enumerateDevices()")}} to get a list of the available media devices. You can then examine that list and identify the potential input sources, and even filter the list based on desired criteria. In this code snippet, `enumerateDevices()` is used to examine the available input devices, locate those which are audio input devices, and create {{HTMLElement("option")}} elements that are then added to a {{HTMLElement("select")}} element representing an input source picker. ```js navigator.mediaDevices.enumerateDevices().then((devices) => { devices.forEach((device) => { const menu = document.getElementById("input-devices"); if (device.kind === "audioinput") { const item = document.createElement("option"); item.textContent = device.label; item.value = device.deviceId; menu.appendChild(item); } }); }); ``` Code similar to this can be used to let the user restrict the set of devices they wish to use. ### For more information To learn more about using the MediaStream Recording API, see [Using the MediaStream Recording API](/en-US/docs/Web/API/MediaStream_Recording_API/Using_the_MediaStream_Recording_API), which shows how to use the API to record audio clips. A second article, [Recording a media element](/en-US/docs/Web/API/MediaStream_Recording_API/Recording_a_media_element), describes how to receive a stream from an {{HTMLElement("audio")}} or {{HTMLElement("video")}} element and use the captured stream (in this case, recording it and saving it to a local disk). ## Interfaces - {{domxref("BlobEvent")}} - : Each time a chunk of media data is finished being recorded, it's delivered to consumers in {{domxref("Blob")}} form using a {{domxref("BlobEvent")}} of type `dataavailable`. - {{domxref("MediaRecorder")}} - : The primary interface that implements the MediaStream Recording API. - {{domxref("MediaRecorderErrorEvent")}} {{Deprecated_Inline}} {{Non-standard_Inline}} - : The interface that represents errors thrown by the MediaStream Recording API. Its {{domxref("MediaRecorderErrorEvent.error", "error")}} property is a {{domxref("DOMException")}} that specifies that error occurred. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Media Capture and Streams API](/en-US/docs/Web/API/Media_Capture_and_Streams_API) landing page - {{domxref("MediaDevices.getUserMedia()")}} - [simpl.info MediaStream Recording demo](https://simpl.info/mediarecorder/), by [Sam Dutton](https://github.com/samdutton) - [HTML5's Media Recorder API in Action on Chrome and Firefox](https://blog.addpipe.com/mediarecorder-api/) - [MediaRecorder polyfill](https://github.com/ai/audio-recorder-polyfill) for Safari and Edge - [TutorRoom](https://github.com/chrisjohndigital/TutorRoom): HTML video capture/playback/download using getUserMedia and the MediaStream Recording API ([source on GitHub](https://github.com/chrisjohndigital/TutorRoom)) - [Simple video recording demo](https://codepen.io/anon/pen/gpmPzm) - [Advanced media stream recorder sample](https://quickblox.github.io/javascript-media-recorder/sample/) - [OpenLang](https://github.com/chrisjohndigital/OpenLang): HTML video language lab web application using MediaDevices and the MediaStream Recording API for video recording ([source on GitHub](https://github.com/chrisjohndigital/OpenLang)) - [MediaStream Recorder API Now Available in Safari Technology Preview 73](https://blog.addpipe.com/safari-technology-preview-73-adds-limited-mediastream-recorder-api-support/)