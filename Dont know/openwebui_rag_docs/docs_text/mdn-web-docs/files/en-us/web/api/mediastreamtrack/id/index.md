Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md

Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > id > index.md --- title: "MediaStreamTrack: id property" short-title: id slug: Web/API/MediaStreamTrack/id page-type: web-api-instance-property browser-compat: api.MediaStreamTrack.id --- {{APIRef("Media Capture and Streams")}} The **`id`** read-only property of the {{domxref("MediaStreamTrack")}} interface returns a string containing a unique identifier (GUID) for the track, which is generated by the {{glossary("user agent")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [WebRTC](/en-US/docs/Web/API/WebRTC_API)