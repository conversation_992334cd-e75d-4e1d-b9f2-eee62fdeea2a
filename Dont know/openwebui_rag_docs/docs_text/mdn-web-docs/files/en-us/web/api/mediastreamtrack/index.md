Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md

Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrack > index.md --- title: MediaStreamTrack slug: Web/API/MediaStreamTrack page-type: web-api-interface browser-compat: api.MediaStreamTrack --- {{APIRef("Media Capture and Streams")}} The **`MediaStreamTrack`** interface of the {{domxref("Media Capture and Streams API", "", "", "nocode")}} represents a single media track within a stream; typically, these are audio or video tracks, but other track types may exist as well. Some user agents subclass this interface to provide more precise information or functionality, such as {{domxref("CanvasCaptureMediaStreamTrack")}}. {{InheritanceDiagram}} ## Instance properties In addition to the properties listed below, `MediaStreamTrack` has constrainable properties which can be set using {{domxref("MediaStreamTrack.applyConstraints", "applyConstraints()")}} and accessed using {{domxref("MediaStreamTrack.getConstraints", "getConstraints()")}} and {{domxref("MediaStreamTrack.getSettings", "getSettings()")}}. See [Capabilities, constraints, and settings](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Constraints) to learn how to correctly work with constrainable properties. Not doing so correctly will result in your code being unreliable. - {{domxref("MediaStreamTrack.contentHint")}} - : A string that may be used by the web application to provide a hint as to what type of content the track contains to guide how it should be treated by API consumers. Allowable values depend on the value of the {{domxref("MediaStreamTrack.kind")}} property. - {{domxref("MediaStreamTrack.enabled")}} - : A Boolean whose value of `true` if the track is enabled, that is allowed to render the media source stream; or `false` if it is disabled, that is not rendering the media source stream but silence and blackness. If the track has been disconnected, this value can be changed but has no more effect. > [!NOTE] > You can implement standard "mute" functionality by setting `enabled` to `false`. The `muted` property refers to a condition in which there's no media because of a technical issue. - {{domxref("MediaStreamTrack.id")}} {{ReadOnlyInline}} - : Returns a string containing a unique identifier (GUID) for the track; it is generated by the browser. - {{domxref("MediaStreamTrack.kind")}} {{ReadOnlyInline}} - : Returns a string set to `"audio"` if the track is an audio track and to `"video"`, if it is a video track. It doesn't change if the track is disassociated from its source. - {{domxref("MediaStreamTrack.label")}} {{ReadOnlyInline}} - : Returns a string containing a user agent-assigned label that identifies the track source, as in `"internal microphone"`. The string may be left empty and is empty as long as no source has been connected. When the track is disassociated from its source, the label is not changed. - {{domxref("MediaStreamTrack.muted")}} {{ReadOnlyInline}} - : Returns a Boolean value indicating whether the track is unable to provide media data due to a technical issue. > [!NOTE] > You can implement standard "mute" functionality by setting `enabled` to `false`, and unmute the media by setting it back to `true` again. - {{domxref("MediaStreamTrack.readyState")}} {{ReadOnlyInline}} - : Returns an enumerated string giving the status of the track. This will be one of the following values: - `"live"` which indicates that an input is connected and does its best-effort in providing real-time data. In that case, the output of data can be switched on or off using the {{domxref("MediaStreamTrack.enabled", "enabled")}} attribute. - `"ended"` which indicates that the input is not giving any more data and will never provide new data. ## Instance methods - {{domxref("MediaStreamTrack.applyConstraints()")}} - : Lets the application specify the ideal and/or ranges of acceptable values for any number of the available constrainable properties of the `MediaStreamTrack`. - {{domxref("MediaStreamTrack.clone()")}} - : Returns a duplicate of the `MediaStreamTrack`. - {{domxref("MediaStreamTrack.getCapabilities()")}} - : Returns an object detailing the accepted values or value range for each constrainable property of the associated `MediaStreamTrack`. - {{domxref("MediaStreamTrack.getConstraints()")}} - : Returns a {{domxref('MediaTrackConstraints')}} object containing the currently set constraints for the track; the returned value matches the constraints last set using {{domxref("MediaStreamTrack.applyConstraints", "applyConstraints()")}}. - {{domxref("MediaStreamTrack.getSettings()")}} - : Returns a {{domxref("MediaTrackSettings")}} object containing the current values of each of the `MediaStreamTrack`'s constrainable properties. - {{domxref("MediaStreamTrack.stop()")}} - : Stops playing the source associated to the track, both the source and the track are disassociated. The track state is set to `ended`. ## Events Listen to these events using {{domxref("EventTarget.addEventListener", "addEventListener()")}} or by assigning an event listener to the `oneventname` property of this interface: - {{domxref("MediaStreamTrack/ended_event", "ended")}} - : Sent when playback of the track ends (when the value {{domxref("MediaStreamTrack.readyState", "readyState")}} changes to `ended`), except when the track is ended by calling {{domxref("MediaStreamTrack.stop")}}. - {{domxref("MediaStreamTrack/mute_event", "mute")}} - : Sent to the `MediaStreamTrack` when the value of the {{domxref("MediaStreamTrack.muted", "muted")}} property is changed to `true`, indicating that the track is unable to provide data temporarily (such as when the network is experiencing a service malfunction). - {{domxref("MediaStreamTrack/unmute_event", "unmute")}} - : Sent to the track when data becomes available again, ending the `muted` state. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Media Capture and Streams API](/en-US/docs/Web/API/Media_Capture_and_Streams_API) - {{domxref("MediaStream")}}