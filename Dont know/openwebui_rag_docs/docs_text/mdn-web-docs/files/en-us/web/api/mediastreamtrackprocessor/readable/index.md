Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md

Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md Path: mdn-web-docs > files > en-us > web > api > mediastreamtrackprocessor > readable > index.md --- title: "MediaStreamTrackProcessor: readable property" short-title: readable slug: Web/API/MediaStreamTrackProcessor/readable page-type: web-api-instance-property status: - experimental browser-compat: api.MediaStreamTrackProcessor.readable --- {{APIRef("Insertable Streams for MediaStreamTrack API")}}{{SeeCompatTable}} The **`readable`** property of the {{domxref("MediaStreamTrackProcessor")}} interface returns a {{domxref("ReadableStream")}} of {{domxref("VideoFrame")}}s. ## Value A {{domxref("ReadableStream")}}. ## Examples See the [Insertable Streams for MediaStreamTrack API](/en-US/docs/Web/API/Insertable_Streams_for_MediaStreamTrack_API#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}