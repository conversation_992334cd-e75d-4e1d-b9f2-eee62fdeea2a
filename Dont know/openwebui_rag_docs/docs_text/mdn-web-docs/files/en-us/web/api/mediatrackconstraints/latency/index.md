Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md

Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md Path: mdn-web-docs > files > en-us > web > api > mediatrackconstraints > latency > index.md --- title: "MediaTrackConstraints: latency property" short-title: latency slug: Web/API/MediaTrackConstraints/latency page-type: web-api-instance-property browser-compat: api.MediaStreamTrack.applyConstraints.latency_constraint --- {{APIRef("Media Capture and Streams")}} The {{domxref("MediaTrackConstraints")}} dictionary's **`latency`** property is a [`ConstrainDouble`](/en-US/docs/Web/API/MediaTrackConstraints#constraindouble) describing the requested or mandatory constraints placed upon the value of the {{domxref("MediaTrackSettings.latency", "latency")}} constrainable property. If needed, you can determine whether or not this constraint is supported by checking the value of {{domxref("MediaTrackSupportedConstraints.latency")}} as returned by a call to {{domxref("MediaDevices.getSupportedConstraints()")}}. However, typically this is unnecessary since browsers will ignore any constraints they're unfamiliar with. Because {{Glossary("RTP")}} doesn't include this information, tracks associated with a [WebRTC](/en-US/docs/Web/API/WebRTC_API) {{domxref("RTCPeerConnection")}} will never include this property. ## Value A [`ConstrainDouble`](/en-US/docs/Web/API/MediaTrackConstraints#constraindouble) describing the acceptable or required value(s) for an audio track's latency, with values specified in seconds. In audio processing, latency is the time between the start of processing (when sound occurs in the real world, or is generated by a hardware device) and the data being made available to the next step in the audio input or output process. In most cases, low latency is desirable for performance and user experience purposes, but when power consumption is a concern, or delays are otherwise acceptable, higher latency might be acceptable. If this property's value is a number, the user agent will attempt to obtain media whose latency tends to be as close as possible to this number given the capabilities of the hardware and the other constraints specified. Otherwise, the value of this [`ConstrainDouble`](/en-US/docs/Web/API/MediaTrackConstraints#constraindouble) will guide the user agent in its efforts to provide an exact match to the required latency (if `exact` is specified or both `min` and `max` are provided and have the same value) or to a best-possible value. > [!NOTE] > Latency is always prone to some variation due to hardware usage demands, network > constraints, and so forth, so even in an "exact" match, some variation should be > expected. ## Examples See the [Constraint exerciser](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Constraints#example_constraint_exerciser) example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Media Capture and Streams API](/en-US/docs/Web/API/Media_Capture_and_Streams_API) - [Capabilities, constraints, and settings](/en-US/docs/Web/API/Media_Capture_and_Streams_API/Constraints) - {{domxref("MediaTrackConstraints")}} - {{domxref("MediaDevices.getSupportedConstraints()")}} - {{domxref("MediaTrackSupportedConstraints")}} - {{domxref("MediaStreamTrack")}}