Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md

Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > messageport > message_event > index.md --- title: "MessagePort: message event" short-title: message slug: Web/API/MessagePort/message_event page-type: web-api-event browser-compat: api.MessagePort.message_event --- {{APIRef("Channel Messaging API")}} {{AvailableInWorkers}} The **`message`** event is fired on a {{domxref('MessagePort')}} object when a message arrives on that channel. This event is not cancellable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("message", (event) => { }) onmessage = (event) => { } ``` ## Event type A {{domxref("MessageEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("MessageEvent")}} ## Event properties _This interface also inherits properties from its parent, {{domxref("Event")}}._ - {{domxref("MessageEvent.data")}} {{ReadOnlyInline}} - : The data sent by the message emitter. - {{domxref("MessageEvent.origin")}} {{ReadOnlyInline}} - : A string representing the origin of the message emitter. - {{domxref("MessageEvent.lastEventId")}} {{ReadOnlyInline}} - : A string representing a unique ID for the event. - {{domxref("MessageEvent.source")}} {{ReadOnlyInline}} - : A `MessageEventSource` (which can be a {{glossary("WindowProxy")}}, {{domxref("MessagePort")}}, or {{domxref("ServiceWorker")}} object) representing the message emitter. - {{domxref("MessageEvent.ports")}} {{ReadOnlyInline}} - : An array containing all {{domxref("MessagePort")}} objects sent with the message, in order. ## Examples Suppose a script creates a [`MessageChannel`](/en-US/docs/Web/API/MessageChannel) and sends one of the ports to a different browsing context, such as another [`<iframe>`](/en-US/docs/Web/HTML/Reference/Elements/iframe), using code like this: ```js const channel = new MessageChannel(); const myPort = channel.port1; const targetFrame = window.top.frames[1]; const targetOrigin = "https://example.org"; const messageControl = document.querySelector("#message"); const channelMessageButton = document.querySelector("#channel-message"); channelMessageButton.addEventListener("click", () => { myPort.postMessage(messageControl.value); }); targetFrame.postMessage("init", targetOrigin, [channel.port2]); ``` The target can receive the port and start listening for messages on it using code like this: ```js window.addEventListener("message", (event) => { const myPort = event.ports[0]; myPort.addEventListener("message", (event) => { received.textContent = event.data; }); myPort.start(); }); ``` Note that the listener must call [`MessagePort.start()`](/en-US/docs/Web/API/MessagePort/start) before any messages will be delivered to this port. This is only needed when using the [`addEventListener()`](/en-US/docs/Web/API/EventTarget/addEventListener) method: if the receiver uses `onmessage` instead, `start()` is called implicitly: ```js window.addEventListener("message", (event) => { const myPort = event.ports[0]; myPort.onmessage = (event) => { received.textContent = event.data; }; }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - Related events: [`messageerror`](/en-US/docs/Web/API/MessagePort/messageerror_event). - [Using channel messaging](/en-US/docs/Web/API/Channel_Messaging_API/Using_channel_messaging)