Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md

Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midiinputmap > index.md --- title: MIDIInputMap slug: Web/API/MIDIInputMap page-type: web-api-interface browser-compat: api.MIDIInputMap --- {{APIRef("Web MIDI API")}}{{SecureContext_Header}} The **`MIDIInputMap`** read-only interface of the [Web MIDI API](/en-US/docs/Web/API/Web_MIDI_API) provides the set of MIDI input ports that are currently available. A `MIDIInputMap` instance is a read-only [`Map`-like object](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map#map-like_browser_apis), in which each key is the ID string for MIDI input, and the associated value is the corresponding {{domxref("MIDIInput")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}