Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md Path: mdn-web-docs > files > en-us > web > api > midioutput > clear > index.md --- title: "MIDIOutput: clear() method" short-title: clear() slug: Web/API/MIDIOutput/clear page-type: web-api-instance-method browser-compat: api.MIDIOutput.clear --- {{securecontext_header}}{{APIRef("Web MIDI API")}} The **`clear()`** method of the {{domxref("MIDIOutput")}} interface clears the queue of messages being sent to the output device. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}