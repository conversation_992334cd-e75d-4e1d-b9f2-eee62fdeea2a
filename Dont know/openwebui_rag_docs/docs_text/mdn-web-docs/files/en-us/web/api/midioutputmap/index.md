Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md

Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md Path: mdn-web-docs > files > en-us > web > api > midioutputmap > index.md --- title: MIDIOutputMap slug: Web/API/MIDIOutputMap page-type: web-api-interface browser-compat: api.MIDIOutputMap --- {{APIRef("Web MIDI API")}}{{SecureContext_Header}} The **`MIDIOutputMap`** read-only interface of the [Web MIDI API](/en-US/docs/Web/API/Web_MIDI_API) provides the set of MIDI output ports that are currently available. A `MIDIOutputMap` instance is a read-only [`Map`-like object](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map#map-like_browser_apis), in which each key is the ID string for MIDI output, and the associated value is the corresponding {{domxref("MIDIOutput")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}