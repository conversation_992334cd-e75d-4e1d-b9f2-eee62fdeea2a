Path: mdn-web-docs > files > en-us > web > api > mouseevent > mozinputsource > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > mozinputsource > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > mozinputsource > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > mozinputsource > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > mozinputsource > index.md --- title: "MouseEvent: mozInputSource property" short-title: mozInputSource slug: Web/API/MouseEvent/mozInputSource page-type: web-api-instance-property status: - non-standard --- {{APIRef("UI Events")}} {{ Non-standard_header() }} The **`MouseEvent.mozInputSource`** read-only property on {{domxref("MouseEvent")}} provides information indicating the type of device that generated the event. This lets you, for example, determine whether a mouse event was generated by an actual mouse or by a touch event (which might affect the degree of accuracy with which you interpret the coordinates associated with the event). ## Value The following values are possible. | Constant name | Value | Description | | --------------------- | ----- | ---------------------------------------------------------- | | `MOZ_SOURCE_UNKNOWN` | 0 | The input device is unknown. | | `MOZ_SOURCE_MOUSE` | 1 | The event was generated by a mouse (or mouse-like device). | | `MOZ_SOURCE_PEN` | 2 | The event was generated by a pen on a tablet. | | `MOZ_SOURCE_ERASER` | 3 | The event was generated by an eraser on a tablet. | | `MOZ_SOURCE_CURSOR` | 4 | The event was generated by a cursor. | | `MOZ_SOURCE_TOUCH` | 5 | The event was generated on a touch interface. | | `MOZ_SOURCE_KEYBOARD` | 6 | The event was generated by a keyboard. | ## Specifications Not part of any specification. ## See also - {{ domxref("MouseEvent") }}