Path: mdn-web-docs > files > en-us > web > api > mouseevent > offsety > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > offsety > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > offsety > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > offsety > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > offsety > index.md --- title: "MouseEvent: offsetY property" short-title: offsetY slug: Web/API/MouseEvent/offsetY page-type: web-api-instance-property browser-compat: api.MouseEvent.offsetY --- {{APIRef("UI Events")}} The **`offsetY`** read-only property of the {{domxref("MouseEvent")}} interface provides the offset in the Y coordinate of the mouse pointer between that event and the padding edge of the target node. ## Value A `double` floating point value in pixels. Early versions of the spec defined this as an integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{ domxref("MouseEvent") }} - [Coordinate systems](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems)