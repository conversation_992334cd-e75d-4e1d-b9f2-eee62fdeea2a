Path: mdn-web-docs > files > en-us > web > api > mouseevent > pagey > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > pagey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > pagey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > pagey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > pagey > index.md --- title: "MouseEvent: pageY property" short-title: pageY slug: Web/API/MouseEvent/pageY page-type: web-api-instance-property browser-compat: api.MouseEvent.pageY --- {{APIRef("UI Events")}} The **`pageY`** read-only property of the {{domxref("MouseEvent")}} interface returns the Y (vertical) coordinate (in pixels) at which the mouse was clicked, relative to the top edge of the entire document. This includes any portion of the document not currently visible. See {{domxref("MouseEvent.pageX")}} for more information. ## Value A `double` floating point value in pixels. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MouseEvent.pageX")}} - [Coordinate systems](/en-US/docs/Web/CSS/CSSOM_view/Coordinate_systems)