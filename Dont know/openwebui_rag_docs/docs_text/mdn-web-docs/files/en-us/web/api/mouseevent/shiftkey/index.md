Path: mdn-web-docs > files > en-us > web > api > mouseevent > shiftkey > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > shiftkey > index.md --- title: "MouseEvent: shiftKey property" short-title: shiftKey slug: Web/API/MouseEvent/shiftKey page-type: web-api-instance-property browser-compat: api.MouseEvent.shiftKey --- {{APIRef("UI Events")}} The **`MouseEvent.shiftKey`** read-only property is a boolean value that indicates whether the <kbd>shift</kbd> key was pressed or not when a given mouse event occurs. ## Value A boolean value, where `true` indicates that the key is pressed, and `false` indicates that the key is _not_ pressed. ## Examples This example logs the `shiftKey` property when you trigger a {{domxref("Element/click_event", "click")}} event. ### HTML ```html <p>Click anywhere to test the <code>shiftKey</code> property.</p> <p id="log"></p> ``` ### JavaScript ```js let log = document.querySelector("#log"); document.addEventListener("click", logKey); function logKey(e) { log.textContent = `The shift key is pressed: ${e.shiftKey}`; } ``` ### Result {{EmbedLiveSample("Examples")}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{ domxref("MouseEvent") }}