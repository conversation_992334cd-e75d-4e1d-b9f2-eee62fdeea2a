Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkit_force_at_mouse_down_static > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkit_force_at_mouse_down_static > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkit_force_at_mouse_down_static > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkit_force_at_mouse_down_static > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkit_force_at_mouse_down_static > index.md --- title: "MouseEvent: WEBKIT_FORCE_AT_MOUSE_DOWN static property" short-title: WEBKIT_FORCE_AT_MOUSE_DOWN slug: Web/API/MouseEvent/WEBKIT_FORCE_AT_MOUSE_DOWN_static page-type: web-api-static-property status: - non-standard --- {{APIRef("Force Touch Events")}}{{Non-standard_header}} **`MouseEvent.WEBKIT_FORCE_AT_MOUSE_DOWN`** is a proprietary, WebKit-specific, static numeric property whose value is the minimum force necessary for a normal click. Because `WEBKIT_FORCE_AT_MOUSE_DOWN` is a static property of `MouseEvent`, you always use it as `MouseEvent.WEBKIT_FORCE_AT_MOUSE_DOWN`, rather than as a property of a `MouseEvent` instance. ## Specifications _Not part of any specification._ Apple has [a description at the Mac Developer Library](https://developer.apple.com/library/archive/documentation/AppleApplications/Conceptual/SafariJSProgTopics/RespondingtoForceTouchEventsfromJavaScript.html). ## See also - {{domxref("MouseEvent.WEBKIT_FORCE_AT_FORCE_MOUSE_DOWN_static", "MouseEvent.WEBKIT_FORCE_AT_FORCE_MOUSE_DOWN")}} - {{domxref("MouseEvent.webkitForce")}}