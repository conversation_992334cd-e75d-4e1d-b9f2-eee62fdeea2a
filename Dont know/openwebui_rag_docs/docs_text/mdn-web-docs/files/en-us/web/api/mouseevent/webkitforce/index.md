Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkitforce > index.md

Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkitforce > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkitforce > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkitforce > index.md Path: mdn-web-docs > files > en-us > web > api > mouseevent > webkitforce > index.md --- title: "MouseEvent: webkitForce property" short-title: webkitForce slug: Web/API/MouseEvent/webkitForce page-type: web-api-instance-property status: - non-standard --- {{APIRef("Force Touch Events")}}{{Non-standard_header}} **`MouseEvent.webkitForce`** is a proprietary, WebKit-specific numeric property whose value represents the amount of pressure that is being applied on the touchpad or touchscreen. ## Specifications _Not part of any specification._ <PERSON> has [a description at the Mac Developer Library](https://developer.apple.com/library/archive/documentation/AppleApplications/Conceptual/SafariJSProgTopics/RespondingtoForceTouchEventsfromJavaScript.html). ## See also - {{domxref("MouseEvent.WEBKIT_FORCE_AT_MOUSE_DOWN_static", "MouseEvent.WEBKIT_FORCE_AT_MOUSE_DOWN")}} - {{domxref("MouseEvent.WEBKIT_FORCE_AT_FORCE_MOUSE_DOWN_static", "MouseEvent.WEBKIT_FORCE_AT_FORCE_MOUSE_DOWN")}}