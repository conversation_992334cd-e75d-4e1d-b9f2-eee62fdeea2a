Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > activevrdisplays > index.md --- title: "Navigator: activeVRDisplays property" short-title: activeVRDisplays slug: Web/API/Navigator/activeVRDisplays page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.Navigator.activeVRDisplays --- {{APIRef("WebVR API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The **`activeVRDisplays`** read-only property of the {{domxref("Navigator")}} interface returns an array containing every {{domxref("VRDisplay")}} object that is currently presenting ({{domxref("VRDisplay.isPresenting")}} is `true`). > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value An array of {{domxref("VRDisplay")}} objects. ## Examples ```js function showActive() { const displays = navigator.activeVRDisplays; for (const display of displays) { console.log(`Display ${display.displayId} is active.`); } } ``` ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)