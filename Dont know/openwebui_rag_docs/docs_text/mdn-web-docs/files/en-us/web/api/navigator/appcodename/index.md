Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appcodename > index.md --- title: "Navigator: appCodeName property" short-title: appCodeName slug: Web/API/Navigator/appCodeName page-type: web-api-instance-property status: - deprecated browser-compat: api.Navigator.appCodeName --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The value of the **`Navigator.appCodeName`** property is always `"Mozilla"`, in any browser. This property is kept only for compatibility purposes. > [!NOTE] > Do not rely on this property to return a real > product name. All browsers return `"Mozilla"` as the value of this property. ## Value The string `"Mozilla"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Navigator.appName")}} - {{domxref("Navigator.product")}}