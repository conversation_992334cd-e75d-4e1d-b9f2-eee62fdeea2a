Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > appname > index.md --- title: "Navigator: appName property" short-title: appName slug: Web/API/Navigator/appName page-type: web-api-instance-property status: - deprecated browser-compat: api.Navigator.appName --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The value of the **`Navigator.appName`** property is always `"Netscape"`, in any browser. This property is kept only for compatibility purposes. > [!NOTE] > Do not rely on this property to return a real browser name. All browsers return `"Netscape"` as the value of this property. ## Value The string `"Netscape"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Navigator.appCodeName")}} - {{domxref("Navigator.product")}}