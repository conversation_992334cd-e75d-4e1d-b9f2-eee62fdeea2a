Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > bluetooth > index.md --- title: "Navigator: bluetooth property" short-title: bluetooth slug: Web/API/Navigator/bluetooth page-type: web-api-instance-property status: - experimental browser-compat: api.Navigator.bluetooth --- {{APIRef("Bluetooth API")}}{{SeeCompatTable}}{{secureContext_header}} The **`bluetooth`** read-only property of the {{domxref("Navigator")}} interface returns a {{domxref("Bluetooth")}} object for the current document, providing access to [Web Bluetooth API](/en-US/docs/Web/API/Web_Bluetooth_API) functionality. ## Value A {{domxref("Bluetooth")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Bluetooth API](/en-US/docs/Web/API/Web_Bluetooth_API)