Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > keyboard > index.md --- title: "Navigator: keyboard property" short-title: keyboard slug: Web/API/Navigator/keyboard page-type: web-api-instance-property status: - experimental browser-compat: api.Navigator.keyboard --- {{SeeCompatTable}}{{APIRef("Keyboard API")}}{{SecureContext_Header}} The **`keyboard`** read-only property of the {{domxref("Navigator")}} interface returns a {{domxref('Keyboard')}} object which provides access to functions that retrieve keyboard layout maps and toggle capturing of key presses from the physical keyboard. ## Value A {{domxref('Keyboard')}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}