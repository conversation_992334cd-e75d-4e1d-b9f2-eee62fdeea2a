Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > locks > index.md --- title: "Navigator: locks property" short-title: locks slug: Web/API/Navigator/locks page-type: web-api-instance-property browser-compat: api.Navigator.locks --- {{APIRef("Web Locks API")}}{{securecontext_header}} The **`locks`** read-only property of the {{domxref("Navigator")}} interface returns a {{domxref("LockManager")}} object which provides methods for requesting a new {{domxref('Lock')}} object and querying for an existing `Lock` object. ## Value A {{domxref("LockManager")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}