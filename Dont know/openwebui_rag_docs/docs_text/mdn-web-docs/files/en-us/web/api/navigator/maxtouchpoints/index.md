Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > maxtouchpoints > index.md --- title: "Navigator: maxTouchPoints property" short-title: maxTouchPoints slug: Web/API/Navigator/maxTouchPoints page-type: web-api-instance-property browser-compat: api.Navigator.maxTouchPoints --- {{APIRef("HTML DOM")}} The **`maxTouchPoints`** read-only property of the {{domxref("Navigator")}} interface returns the maximum number of simultaneous touch contact points that are supported by the current device. ## Value A number. ## Examples ```js if (navigator.maxTouchPoints > 1) { // browser supports multi-touch } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}