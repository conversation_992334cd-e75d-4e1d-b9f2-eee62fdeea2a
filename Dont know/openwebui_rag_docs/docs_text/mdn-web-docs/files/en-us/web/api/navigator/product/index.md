Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > product > index.md --- title: "Navigator: product property" short-title: product slug: Web/API/Navigator/product page-type: web-api-instance-property status: - deprecated browser-compat: api.Navigator.product --- {{APIRef("HTML DOM")}} {{Deprecated_Header}} The value of the **`Navigator.product`** property is always `"Gecko"`, in any browser. This property is kept only for compatibility purposes. > [!NOTE] > Do not rely on this property to return a real product name. All browsers return `"Gecko"` as the value of this property. ## Value The string `"Gecko"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Navigator.appCodeName")}} - {{domxref("Navigator.appName")}}