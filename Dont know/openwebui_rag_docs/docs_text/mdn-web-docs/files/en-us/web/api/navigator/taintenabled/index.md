Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > taintenabled > index.md --- title: "Navigator: taintEnabled() method" short-title: taintEnabled() slug: Web/API/Navigator/taintEnabled page-type: web-api-instance-method status: - deprecated browser-compat: api.Navigator.taintEnabled --- {{APIRef("HTML DOM")}} {{deprecated_header}} The **`Navigator.taintEnabled()`** method always returns `false`. Tainting was a security method used by JavaScript 1.2. It has long been removed; this method only stays for maintaining compatibility with very old scripts. ## Syntax ```js-nolint taintEnabled() ``` ### Parameters None. ### Return value Always returns `false`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Navigator")}}