Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > usb > index.md --- title: "Navigator: usb property" short-title: usb slug: Web/API/Navigator/usb page-type: web-api-instance-property browser-compat: api.Navigator.usb --- {{APIRef("WebUSB API")}}{{SecureContext_Header}} The **`usb`** read-only property of the {{domxref("Navigator")}} interface returns a {{domxref("USB")}} object for the current document, providing access to [WebUSB API](/en-US/docs/Web/API/WebUSB_API) functionality. ## Value A {{domxref('USB')}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [WebUSB API](/en-US/docs/Web/API/WebUSB_API)