Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendor > index.md --- title: "Navigator: vendor property" short-title: vendor slug: Web/API/Navigator/vendor page-type: web-api-instance-property status: - deprecated browser-compat: api.Navigator.vendor --- {{APIRef("HTML DOM")}}{{Deprecated_Header}} The value of the {{DomXref("Navigator")}} **`vendor`** property is always either `"Google Inc."`, `"Apple Computer, Inc."`, or (in Firefox) the empty string. ## Value Either `"Google Inc."`, `"Apple Computer, Inc."`, or (in Firefox) the empty string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}