Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > vendorsub > index.md --- title: "Navigator: vendorSub property" short-title: vendorSub slug: Web/API/Navigator/vendorSub page-type: web-api-instance-property status: - deprecated browser-compat: api.Navigator.vendorSub --- {{ApiRef}} {{Deprecated_Header}} The value of the **`Navigator.vendorSub`** property is always the empty string, in any browser. ## Value - The empty string ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}