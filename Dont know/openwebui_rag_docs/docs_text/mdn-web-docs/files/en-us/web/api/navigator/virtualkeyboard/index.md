Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md

Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md Path: mdn-web-docs > files > en-us > web > api > navigator > virtualkeyboard > index.md --- title: "Navigator: virtualKeyboard property" short-title: virtualKeyboard slug: Web/API/Navigator/virtualKeyboard page-type: web-api-instance-property status: - experimental browser-compat: api.Navigator.virtualKeyboard --- {{APIRef("VirtualKeyboard")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`virtualKeyboard`** read-only property of the {{domxref("navigator")}} interface returns a reference to the {{domxref("VirtualKeyboard")}} instance object. The {{domxref("VirtualKeyboard_API", "VirtualKeyboard API", "", "nocode")}} gives developers control over the layout of their applications when the on-screen virtual keyboard appears and disappears on devices such as tablets, mobile phones, or other devices where a hardware keyboard may not be available. ## Value A {{domxref("VirtualKeyboard")}} object you can use to opt-out of the automatic virtual keyboard behavior, show or hide the virtual keyboard programmatically, and get the current position and size of the virtual keyboard. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}