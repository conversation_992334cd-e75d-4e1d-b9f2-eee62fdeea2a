Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md

Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md Path: mdn-web-docs > files > en-us > web > api > ndefreader > ndefreader > index.md --- title: "NDEFReader: NDEFReader() constructor" short-title: NDEFReader() slug: Web/API/NDEFReader/NDEFReader page-type: web-api-constructor status: - experimental browser-compat: api.NDEFReader.NDEFReader --- {{SecureContext_Header}}{{APIRef("Web NFC API")}}{{SeeCompatTable}} The **`NDEFReader()`** constructor of the {{domxref("NDEFReader")}} interface returns a new `NDEFReader` object, which is used to read NDEF messages from compatible NFC devices, e.g., NDEF tags, within the reader's magnetic induction field. ## Syntax ```js-nolint new NDEFReader() ``` ### Parameters None. ### Return value A new {{DOMxRef("NDEFReader")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}