Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md

Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > ndefrecord > encoding > index.md --- title: "NDEFRecord: encoding property" short-title: encoding slug: Web/API/NDEFRecord/encoding page-type: web-api-instance-property status: - experimental browser-compat: api.NDEFRecord.encoding --- {{SecureContext_Header}}{{SeeCompatTable}}{{APIRef("Web NFC API")}} The **`encoding`** property of the {{DOMxRef("NDEFRecord")}} interface returns the encoding of a textual payload, or `null` otherwise. ## Value A string which can be one of the following: `"utf-8"`, `"utf-16"`, `"utf-16le"`, or `"utf-16be"`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}