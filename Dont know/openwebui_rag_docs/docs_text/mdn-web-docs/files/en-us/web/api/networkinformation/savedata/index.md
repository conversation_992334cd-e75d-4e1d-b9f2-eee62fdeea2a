Path: mdn-web-docs > files > en-us > web > api > networkinformation > savedata > index.md

Path: mdn-web-docs > files > en-us > web > api > networkinformation > savedata > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > savedata > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > savedata > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > savedata > index.md --- title: "NetworkInformation: saveData property" short-title: saveData slug: Web/API/NetworkInformation/saveData page-type: web-api-instance-property browser-compat: api.NetworkInformation.saveData --- {{APIRef("Network Information API")}} {{AvailableInWorkers}} The **`saveData`** read-only property of the {{domxref("NetworkInformation")}} interface returns `true` if the user has set a reduced data usage option on the user agent. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{HTTPHeader("Save-Data")}}