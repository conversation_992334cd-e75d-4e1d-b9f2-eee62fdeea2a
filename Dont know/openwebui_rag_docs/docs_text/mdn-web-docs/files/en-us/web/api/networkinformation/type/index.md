Path: mdn-web-docs > files > en-us > web > api > networkinformation > type > index.md

Path: mdn-web-docs > files > en-us > web > api > networkinformation > type > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > type > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > type > index.md Path: mdn-web-docs > files > en-us > web > api > networkinformation > type > index.md --- title: "NetworkInformation: type property" short-title: type slug: Web/API/NetworkInformation/type page-type: web-api-instance-property status: - experimental browser-compat: api.NetworkInformation.type --- {{apiref("Network Information API")}}{{SeeCompatTable}} {{AvailableInWorkers}} The **`type`** read-only property of the {{domxref("NetworkInformation")}} interface returns the type of connection a device is using to communicate with the network. ## Value An enumerated value that is one of the following values: - `"bluetooth"` - `"cellular"` - `"ethernet"` - `"none"` - `"wifi"` - `"wimax"` - `"other"` - `"unknown"` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}