Path: mdn-web-docs > files > en-us > web > api > node > nodevalue > index.md

Path: mdn-web-docs > files > en-us > web > api > node > nodevalue > index.md Path: mdn-web-docs > files > en-us > web > api > node > nodevalue > index.md Path: mdn-web-docs > files > en-us > web > api > node > nodevalue > index.md Path: mdn-web-docs > files > en-us > web > api > node > nodevalue > index.md --- title: "Node: nodeValue property" short-title: nodeValue slug: Web/API/Node/nodeValue page-type: web-api-instance-property browser-compat: api.Node.nodeValue --- {{APIRef("DOM")}} The **`nodeValue`** property of the {{domxref("Node")}} interface returns or sets the value of the current node. ## Value A string containing the value of the current node, if any. For the document itself, `nodeValue` returns `null`. For text, comment, and CDATA nodes, `nodeValue` returns the content of the node. For attribute nodes, the value of the attribute is returned. The following table shows the return values for different types of nodes. | Node | Value of nodeValue | | ------------------------------------ | ----------------------------------- | | {{domxref("CDATASection")}} | Content of the CDATA section | | {{domxref("Comment")}} | Content of the comment | | {{domxref("Document")}} | `null` | | {{domxref("DocumentFragment")}} | `null` | | {{domxref("DocumentType")}} | `null` | | {{domxref("Element")}} | `null` | | {{domxref("NamedNodeMap")}} | `null` | | {{domxref("ProcessingInstruction")}} | Entire content excluding the target | | {{domxref("Text")}} | Content of the text node | > [!NOTE] > When `nodeValue` is defined to be `null`, setting it has no effect. ## Example ```html <div id="d1">Hello world</div> <!-- Example of comment --> <output id="result">Not calculated yet.</output> ``` and the following script: ```js let node = document.querySelector("body").firstChild; let result = "Node names are:\n"; while (node) { result += `Value of ${node.nodeName}: ${node.nodeValue}\n`; node = node.nextSibling; } const output = document.getElementById("result"); output.innerText = result; ``` {{ EmbedLiveSample("Example", "100%", "250")}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}