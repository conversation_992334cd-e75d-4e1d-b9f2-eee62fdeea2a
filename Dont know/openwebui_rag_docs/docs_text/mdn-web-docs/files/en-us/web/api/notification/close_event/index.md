Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md

Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md Path: mdn-web-docs > files > en-us > web > api > notification > close_event > index.md --- title: "Notification: close event" short-title: close slug: Web/API/Notification/close_event page-type: web-api-event browser-compat: api.Notification.close_event --- {{APIRef("Web Notifications")}}{{securecontext_header}} {{AvailableInWorkers}} The **`close`** event of the {{domxref("Notification")}} interface fires when a {{domxref("Notification")}} is closed. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("close", (event) => { }) onclose = (event) => { } ``` ## Event type A generic {{domxref("Event")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Notifications API](/en-US/docs/Web/API/Notifications_API/Using_the_Notifications_API)