Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md

Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md Path: mdn-web-docs > files > en-us > web > api > notification > image > index.md --- title: "Notification: image property" short-title: image slug: Web/API/Notification/image page-type: web-api-instance-property status: - experimental browser-compat: api.Notification.image --- {{APIRef("Web Notifications")}}{{SecureContext_Header}}{{SeeCompatTable}} {{AvailableInWorkers}} The **`image`** read-only property of the {{domxref("Notification")}} interface contains the URL of an image to be displayed as part of the notification, as specified in the `image` option of the {{domxref("Notification.Notification","Notification()")}} constructor. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Notifications API](/en-US/docs/Web/API/Notifications_API/Using_the_Notifications_API)