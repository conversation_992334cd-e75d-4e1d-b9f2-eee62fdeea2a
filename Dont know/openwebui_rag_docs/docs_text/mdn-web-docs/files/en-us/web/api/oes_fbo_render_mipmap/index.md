Path: mdn-web-docs > files > en-us > web > api > oes_fbo_render_mipmap > index.md

Path: mdn-web-docs > files > en-us > web > api > oes_fbo_render_mipmap > index.md Path: mdn-web-docs > files > en-us > web > api > oes_fbo_render_mipmap > index.md Path: mdn-web-docs > files > en-us > web > api > oes_fbo_render_mipmap > index.md Path: mdn-web-docs > files > en-us > web > api > oes_fbo_render_mipmap > index.md --- title: OES_fbo_render_mipmap extension short-title: OES_fbo_render_mipmap slug: Web/API/OES_fbo_render_mipmap page-type: webgl-extension browser-compat: api.OES_fbo_render_mipmap --- {{APIRef("WebGL")}} The `OES_fbo_render_mipmap` extension is part of the [WebGL API](/en-US/docs/Web/API/WebGL_API) and makes it possible to attach any level of a texture to a framebuffer object. WebGL extensions are available using the {{domxref("WebGLRenderingContext.getExtension()")}} method. For more information, see also [Using Extensions](/en-US/docs/Web/API/WebGL_API/Using_Extensions) in the [WebGL tutorial](/en-US/docs/Web/API/WebGL_API/Tutorial). > [!NOTE] > This extension is only available to {{domxref("WebGLRenderingContext", "WebGL 1", "", 1)}}. > In WebGL2, the functionality of this extension is available in the WebGL 2 context by default. ## Examples See the [sample code](https://registry.khronos.org/webgl/extensions/OES_fbo_render_mipmap/) in the Khronos specification. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("WebGLRenderingContext.getExtension()")}}