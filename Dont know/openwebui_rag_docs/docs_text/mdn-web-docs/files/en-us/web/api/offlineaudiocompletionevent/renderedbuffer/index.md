Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md

Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocompletionevent > renderedbuffer > index.md --- title: "OfflineAudioCompletionEvent: renderedBuffer property" short-title: renderedBuffer slug: Web/API/OfflineAudioCompletionEvent/renderedBuffer page-type: web-api-instance-property browser-compat: api.OfflineAudioCompletionEvent.renderedBuffer --- {{APIRef("Web Audio API")}} The **`renderedBuffer`** read-only property of the {{domxref("OfflineAudioCompletionEvent")}} interface is an {{domxref("AudioBuffer")}} containing the result of processing an {{domxref("OfflineAudioContext")}}. ## Value An {{domxref("AudioBuffer")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}