Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md

Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md Path: mdn-web-docs > files > en-us > web > api > offlineaudiocontext > length > index.md --- title: "OfflineAudioContext: length property" short-title: length slug: Web/API/OfflineAudioContext/length page-type: web-api-instance-property browser-compat: api.OfflineAudioContext.length --- {{ APIRef("Web Audio API") }} The **`length`** property of the {{domxref("OfflineAudioContext")}} interface returns an integer representing the size of the buffer in sample-frames. ## Value An integer representing the size of the buffer in sample-frames. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}