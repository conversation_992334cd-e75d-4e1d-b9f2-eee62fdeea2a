Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md

Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md Path: mdn-web-docs > files > en-us > web > api > overconstrainederror > constraint > index.md --- title: "OverconstrainedError: constraint property" short-title: constraint slug: Web/API/OverconstrainedError/constraint page-type: web-api-instance-property browser-compat: api.OverconstrainedError.constraint --- {{APIRef("Media Capture and Streams")}} The **`constraint`** read-only property of the {{domxref("OverconstrainedError")}} interface returns the constraint that was supplied in the constructor, meaning the constraint that was not satisfied. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}