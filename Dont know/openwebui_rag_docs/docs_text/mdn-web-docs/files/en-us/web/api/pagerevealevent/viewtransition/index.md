Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md

Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pagerevealevent > viewtransition > index.md --- title: "PageRevealEvent: viewTransition property" short-title: viewTransition slug: Web/API/PageRevealEvent/viewTransition page-type: web-api-instance-property browser-compat: api.PageRevealEvent.viewTransition --- {{APIRef("HTML DOM")}} The **`viewTransition`** read-only property of the {{domxref("PageRevealEvent")}} interface contains a {{domxref("ViewTransition")}} object representing the active view transition for the cross-document navigation. ## Value A {{domxref("ViewTransition")}} object, or `null` if no view transition is active when the event is fired. ## Examples See the main {{domxref("PageRevealEvent")}} page. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Navigation API](/en-US/docs/Web/API/Navigation_API) - [View Transition API](/en-US/docs/Web/API/View_Transition_API)