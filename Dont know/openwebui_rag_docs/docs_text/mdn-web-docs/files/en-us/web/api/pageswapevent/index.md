Path: mdn-web-docs > files > en-us > web > api > pageswapevent > index.md

Path: mdn-web-docs > files > en-us > web > api > pageswapevent > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > index.md --- title: PageSwapEvent slug: Web/API/PageSwapEvent page-type: web-api-interface browser-compat: api.PageSwapEvent --- {{APIRef("HTML DOM")}} The **`PageSwapEvent`** event object is made available inside handler functions for the {{domxref("Window.pageswap_event", "pageswap")}} event. The `pageswap` event is fired when you navigate across documents, when the previous document is about to unload. During a cross-document navigation, the `PageSwapEvent` event object allows you to manipulate the related [view transition](/en-US/docs/Web/API/View_Transition_API) (providing access to the relevant {{domxref("ViewTransition")}} object) from the document being navigated _from_, if a view transition was triggered by the navigation. It also provides access to information on the navigation type and current and destination documents. ## Constructor - {{domxref("PageSwapEvent.PageSwapEvent", "PageSwapEvent()")}} - : Creates a new `PageSwapEvent` object instance. ## Instance properties - {{domxref("PageSwapEvent.activation", "activation")}} {{ReadOnlyInline}} - : Contains a {{domxref("NavigationActivation")}} object containing the navigation type and current and destination document history entries for a same-origin navigation. If the navigation has a cross-origin URL anywhere in the redirect chain, it returns `null`. - {{domxref("PageSwapEvent.viewTransition", "viewTransition")}} {{ReadOnlyInline}} - : Contains a {{domxref("ViewTransition")}} object representing the active view transition for the cross-document navigation. ## Examples ```js window.addEventListener("pageswap", async (e) => { // Only run this if an active view transition exists if (e.viewTransition) { const currentUrl = e.activation.from?.url ? new URL(e.activation.from.url) : null; const targetUrl = new URL(e.activation.entry.url); // Going from profile page to homepage // ~> The big img and title are the ones! if (isProfilePage(currentUrl) && isHomePage(targetUrl)) { // Set view-transition-name values on the elements to animate document.querySelector(`#detail main h1`).style.viewTransitionName = "name"; document.querySelector(`#detail main img`).style.viewTransitionName = "avatar"; // Remove view-transition-names after snapshots have been taken // Stops naming conflicts resulting from the page state persisting in BFCache await e.viewTransition.finished; document.querySelector(`#detail main h1`).style.viewTransitionName = "none"; document.querySelector(`#detail main img`).style.viewTransitionName = "none"; } // Going to profile page // ~> The clicked items are the ones! if (isProfilePage(targetUrl)) { const profile = extractProfileNameFromUrl(targetUrl); // Set view-transition-name values on the elements to animate document.querySelector(`#${profile} span`).style.viewTransitionName = "name"; document.querySelector(`#${profile} img`).style.viewTransitionName = "avatar"; // Remove view-transition-names after snapshots have been taken // Stops naming conflicts resulting from the page state persisting in BFCache await e.viewTransition.finished; document.querySelector(`#${profile} span`).style.viewTransitionName = "none"; document.querySelector(`#${profile} img`).style.viewTransitionName = "none"; } } }); ``` > [!NOTE] > See [List of Chrome DevRel team members](https://view-transitions.chrome.dev/profiles/mpa/) for the live demo this code is taken from. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [View Transition API](/en-US/docs/Web/API/View_Transition_API)