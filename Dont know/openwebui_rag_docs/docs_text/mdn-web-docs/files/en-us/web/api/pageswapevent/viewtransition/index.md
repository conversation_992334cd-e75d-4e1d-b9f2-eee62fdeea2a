Path: mdn-web-docs > files > en-us > web > api > pageswapevent > viewtransition > index.md

Path: mdn-web-docs > files > en-us > web > api > pageswapevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > viewtransition > index.md Path: mdn-web-docs > files > en-us > web > api > pageswapevent > viewtransition > index.md --- title: "PageSwapEvent: viewTransition property" short-title: viewTransition slug: Web/API/PageSwapEvent/viewTransition page-type: web-api-instance-property browser-compat: api.PageSwapEvent.viewTransition --- {{APIRef("HTML DOM")}} The **`viewTransition`** read-only property of the {{domxref("PageRevealEvent")}} interface contains a {{domxref("ViewTransition")}} object representing the active view transition for the cross-document navigation. ## Value A {{domxref("ViewTransition")}} object, or `null` if no view transition is active when the event is fired. ## Examples See the main {{domxref("PageSwapEvent")}} page. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [View Transition API](/en-US/docs/Web/API/View_Transition_API)