Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md

Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md Path: mdn-web-docs > files > en-us > web > api > pagetransitionevent > persisted > index.md --- title: "PageTransitionEvent: persisted property" short-title: persisted slug: Web/API/PageTransitionEvent/persisted page-type: web-api-instance-property browser-compat: api.PageTransitionEvent.persisted --- {{APIRef("HTML DOM")}} The **`persisted`** read-only property indicates if a webpage is loading from a cache. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}