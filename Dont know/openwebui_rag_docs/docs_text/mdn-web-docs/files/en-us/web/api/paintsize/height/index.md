Path: mdn-web-docs > files > en-us > web > api > paintsize > height > index.md

Path: mdn-web-docs > files > en-us > web > api > paintsize > height > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > height > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > height > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > height > index.md --- title: "PaintSize: height property" short-title: height slug: Web/API/PaintSize/height page-type: web-api-instance-property browser-compat: api.PaintSize.height --- {{APIRef("CSS Painting API")}} The **`height`** read-only property of the {{domxref("PaintSize")}} interface returns the height of the output bitmap that the author should draw. ## Value A floating point number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API/Guide) - [CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API) - [Houdini APIs](/en-US/docs/Web/API/Houdini_APIs)