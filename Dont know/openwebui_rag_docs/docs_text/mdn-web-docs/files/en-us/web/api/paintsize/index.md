Path: mdn-web-docs > files > en-us > web > api > paintsize > index.md

Path: mdn-web-docs > files > en-us > web > api > paintsize > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > index.md --- title: PaintSize slug: Web/API/PaintSize page-type: web-api-interface browser-compat: api.PaintSize --- {{APIRef("CSS Painting API")}} The **`PaintSize`** interface of the [CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API) represents the size of the output bitmap that the author should draw. ## Instance properties - {{domxref('PaintSize.height')}} {{ReadOnlyInline}} - : Returns the height of the output bitmap that the author should draw. - {{domxref('PaintSize.width')}} {{ReadOnlyInline}} - : Returns the width of the output bitmap that the author should draw. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API/Guide) - [CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API) - [Houdini APIs](/en-US/docs/Web/API/Houdini_APIs)