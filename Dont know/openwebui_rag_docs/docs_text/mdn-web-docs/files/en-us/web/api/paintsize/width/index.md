Path: mdn-web-docs > files > en-us > web > api > paintsize > width > index.md

Path: mdn-web-docs > files > en-us > web > api > paintsize > width > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > width > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > width > index.md Path: mdn-web-docs > files > en-us > web > api > paintsize > width > index.md --- title: "PaintSize: width property" short-title: width slug: Web/API/PaintSize/width page-type: web-api-instance-property browser-compat: api.PaintSize.width --- {{APIRef("CSS Painting API")}} The **`width`** read-only property of the {{domxref("PaintSize")}} interface returns the width of the output bitmap that the author should draw. ## Value A floating point number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API/Guide) - [CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API) - [Houdini APIs](/en-US/docs/Web/API/Houdini_APIs)