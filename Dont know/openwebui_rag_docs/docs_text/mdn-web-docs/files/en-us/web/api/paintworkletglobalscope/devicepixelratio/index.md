Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md

Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md Path: mdn-web-docs > files > en-us > web > api > paintworkletglobalscope > devicepixelratio > index.md --- title: "PaintWorkletGlobalScope: devicePixelRatio property" short-title: devicePixelRatio slug: Web/API/PaintWorkletGlobalScope/devicePixelRatio page-type: web-api-instance-property status: - experimental browser-compat: api.PaintWorkletGlobalScope.devicePixelRatio --- {{APIRef("CSS Painting API")}}{{SeeCompatTable}} The **`devicePixelRatio`** read-only property of the {{domxref("PaintWorkletGlobalScope")}} interface returns the current device's ratio of physical pixels to logical pixels. ## Value A double-precision integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [CSS.paintWorklet](/en-US/docs/Web/API/CSS/paintWorklet_static) - [Worklet](/en-US/docs/Web/API/Worklet) - [CSS Painting API](/en-US/docs/Web/API/CSS_Painting_API) - [Houdini APIs](/en-US/docs/Web/API/Houdini_APIs) - [window.devicePixelRatio](/en-US/docs/Web/API/Window/devicePixelRatio)