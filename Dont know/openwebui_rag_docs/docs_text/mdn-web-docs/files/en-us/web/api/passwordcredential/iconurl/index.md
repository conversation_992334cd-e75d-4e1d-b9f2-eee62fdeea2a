Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md

Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > iconurl > index.md --- title: "PasswordCredential: iconURL property" short-title: iconURL slug: Web/API/PasswordCredential/iconURL page-type: web-api-instance-property status: - experimental browser-compat: api.PasswordCredential.iconURL --- {{SeeCompatTable}}{{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`iconURL`** read-only property of the {{domxref("PasswordCredential")}} interface returns a string containing a URL pointing to an image for an icon. This image is intended for display in a credential chooser. The URL must be accessible without authentication. ## Value A string containing a URL. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}