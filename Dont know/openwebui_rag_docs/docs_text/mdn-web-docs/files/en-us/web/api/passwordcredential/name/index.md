Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md

Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > name > index.md --- title: "PasswordCredential: name property" short-title: name slug: Web/API/PasswordCredential/name page-type: web-api-instance-property status: - experimental browser-compat: api.PasswordCredential.name --- {{SeeCompatTable}}{{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`name`** read-only property of the {{domxref("PasswordCredential")}} interface returns a string containing a human-readable public name for display in a credential chooser. ## Value A string containing a name. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}