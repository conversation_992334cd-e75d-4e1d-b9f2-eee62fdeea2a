Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md

Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md Path: mdn-web-docs > files > en-us > web > api > passwordcredential > password > index.md --- title: "PasswordCredential: password property" short-title: password slug: Web/API/PasswordCredential/password page-type: web-api-instance-property status: - experimental browser-compat: api.PasswordCredential.password --- {{SeeCompatTable}}{{APIRef("Credential Management API")}}{{SecureContext_Header}} The **`password`** read-only property of the {{domxref("PasswordCredential")}} interface returns a string containing the password of the credential. ## Value A string containing a password. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}