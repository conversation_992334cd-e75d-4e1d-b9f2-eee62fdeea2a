Path: mdn-web-docs > files > en-us > web > api > paymentaddress > city > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > city > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > city > index.md --- title: "PaymentAddress: city property" short-title: city slug: Web/API/PaymentAddress/city page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentAddress.city --- {{APIRef("Payment Request API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The **`city`** read-only property of the {{domxref('PaymentAddress')}} interface returns a string containing the city or town portion of the address. ## Value A string indicating the city or town portion of the address described by the {{domxref("PaymentAddress")}} object. ## Browser compatibility {{Compat}}