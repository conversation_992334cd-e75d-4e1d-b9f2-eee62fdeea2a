Path: mdn-web-docs > files > en-us > web > api > paymentaddress > phone > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > phone > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > phone > index.md --- title: "PaymentAddress: phone property" short-title: phone slug: Web/API/PaymentAddress/phone page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentAddress.phone --- {{APIRef("Payment Request API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The read-only **`phone`** property of the {{domxref('PaymentAddress')}} interface returns a string containing the telephone number of the recipient or contact person. ## Value A string containing the telephone number for the recipient of the shipment or of the responsible party for payment. If no phone number is available, this value is an empty string. ## Browser compatibility {{Compat}}