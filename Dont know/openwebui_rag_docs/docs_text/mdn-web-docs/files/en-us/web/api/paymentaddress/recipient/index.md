Path: mdn-web-docs > files > en-us > web > api > paymentaddress > recipient > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > recipient > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > recipient > index.md --- title: "PaymentAddress: recipient property" short-title: recipient slug: Web/API/PaymentAddress/recipient page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentAddress.recipient --- {{APIRef("Payment Request API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The read-only **`recipient`** property of the {{domxref('PaymentAddress')}} interface returns a string containing the name of the recipient, purchaser, or contact person at the payment address. ## Value A string giving the name of the person receiving or paying for the purchase, or the name of a contact person in other contexts. If no name is available, this string is empty. ## Browser compatibility {{Compat}}