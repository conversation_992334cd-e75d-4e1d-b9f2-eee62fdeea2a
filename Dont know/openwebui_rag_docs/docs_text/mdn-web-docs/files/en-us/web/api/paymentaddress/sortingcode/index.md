Path: mdn-web-docs > files > en-us > web > api > paymentaddress > sortingcode > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > sortingcode > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > sortingcode > index.md --- title: "PaymentAddress: sortingCode property" short-title: sortingCode slug: Web/API/PaymentAddress/sortingCode page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentAddress.sortingCode --- {{APIRef("Payment Request API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The **`sortingCode`** read-only property of the {{domxref('PaymentAddress')}} interface returns a string containing a postal sorting code such as is used in France. ## Value A string containing the sorting code portion of the address. ## Browser compatibility {{Compat}}