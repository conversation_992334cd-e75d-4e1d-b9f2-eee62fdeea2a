Path: mdn-web-docs > files > en-us > web > api > paymentaddress > tojson > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > paymentaddress > tojson > index.md --- title: "PaymentAddress: toJSON() method" short-title: toJSON() slug: Web/API/PaymentAddress/toJSON page-type: web-api-instance-method status: - deprecated - non-standard browser-compat: api.PaymentAddress.toJSON --- {{APIRef("Payment Request API")}}{{SecureContext_Header}}{{Deprecated_Header}}{{Non-standard_Header}} The **`toJSON()`** method of the {{domxref("PaymentAddress")}} interface is a standard serializer that returns a JSON representation of the PaymentAddress object's properties. ## Syntax ```js-nolint toJSON() ``` ### Return value A JSON object. ## Browser compatibility {{Compat}}