Path: mdn-web-docs > files > en-us > web > api > paymentmanager > userhint > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentmanager > userhint > index.md Path: mdn-web-docs > files > en-us > web > api > paymentmanager > userhint > index.md Path: mdn-web-docs > files > en-us > web > api > paymentmanager > userhint > index.md Path: mdn-web-docs > files > en-us > web > api > paymentmanager > userhint > index.md --- title: "PaymentManager: userHint property" short-title: userHint slug: Web/API/PaymentManager/userHint page-type: web-api-instance-property status: - experimental browser-compat: api.PaymentManager.userHint --- {{APIRef("Payment Handler API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`userHint`** property of the {{domxref("PaymentManager")}} interface provides a hint for the browser to display along with the payment app's name and icon in the Payment Handler UI. ## Value A string. ## Examples ```js navigator.serviceWorker.register("serviceworker.js").then((registration) => { registration.paymentManager.userHint = "Card number should be 16 digits"; registration.paymentManager .enableDelegations(["shippingAddress", "payerName"]) .then(() => { // }); // }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Payment Handler API", "Payment Handler API", "", "nocode")}} - [Web-based payment apps overview](https://web.dev/articles/web-based-payment-apps-overview) - [Setting up a payment method](https://web.dev/articles/setting-up-a-payment-method) - [Life of a payment transaction](https://web.dev/articles/life-of-a-payment-transaction) - [Using the Payment Request API](/en-US/docs/Web/API/Payment_Request_API/Using_the_Payment_Request_API) - [Payment processing concepts](/en-US/docs/Web/API/Payment_Request_API/Concepts)