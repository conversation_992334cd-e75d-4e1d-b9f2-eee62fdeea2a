Path: mdn-web-docs > files > en-us > web > api > paymentrequest > shippingtype > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentrequest > shippingtype > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequest > shippingtype > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequest > shippingtype > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequest > shippingtype > index.md --- title: "PaymentRequest: shippingType property" short-title: shippingType slug: Web/API/PaymentRequest/shippingType page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentRequest.shippingType --- {{securecontext_header}}{{APIRef("Payment Request API")}}{{Deprecated_header}}{{Non-standard_header}} The **`shippingType`** read-only property of the {{domxref("PaymentRequest")}} interface returns one of `"shipping"`, `"delivery"`, `"pickup"`, or `null` if one was not provided by the constructor. ## Value One of `"shipping"`, `"delivery"`, `"pickup"`, or `null`. ## Browser compatibility {{Compat}}