Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md Path: mdn-web-docs > files > en-us > web > api > paymentrequestevent > instrumentkey > index.md --- title: "PaymentRequestEvent: instrumentKey property" short-title: instrumentKey slug: Web/API/PaymentRequestEvent/instrumentKey page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PaymentRequestEvent.instrumentKey --- {{APIRef("Payment Handler API")}}{{deprecated_header}}{{non-standard_header}}{{AvailableInWorkers("service")}} The **`instrumentKey`** read-only property of the {{domxref("PaymentRequestEvent")}} interface returns a `PaymentInstrument` object reflecting the payment instrument selected by the user or an empty string if the user has not registered or chosen a payment instrument. ## Value A `PaymentInstrument` object. ## Specifications This feature is no longer part of any specification. ## Browser compatibility {{Compat}}