Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payeremail > index.md --- title: "PaymentResponse: payerEmail property" short-title: payerEmail slug: Web/API/PaymentResponse/payerEmail page-type: web-api-instance-property browser-compat: api.PaymentResponse.payerEmail --- {{securecontext_header}}{{APIRef("Payment Request API")}} The `payerEmail` read-only property of the {{domxref("PaymentResponse")}} interface returns the email address supplied by the user. This option is only present when the `requestPayerEmail` option is set to `true` in the `options` object passed to the {{domxref('PaymentRequest.PaymentRequest','PaymentRequest')}} constructor. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}