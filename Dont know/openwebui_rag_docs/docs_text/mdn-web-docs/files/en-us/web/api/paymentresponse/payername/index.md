Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md

Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md Path: mdn-web-docs > files > en-us > web > api > paymentresponse > payername > index.md --- title: "PaymentRequest: payerName property" short-title: payerName slug: Web/API/PaymentResponse/payerName page-type: web-api-instance-property browser-compat: api.PaymentResponse.payerName --- {{securecontext_header}}{{APIRef("Payment Request API")}} The **`payerName`** read-only property of the {{domxref("PaymentResponse")}} interface returns the name supplied by the user. This option is only present when the `requestPayerName` option is set to `true` in the options parameter of the {{domxref('PaymentRequest.PaymentRequest','PaymentRequest()')}} constructor. ## Value A string containing the payer name. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}