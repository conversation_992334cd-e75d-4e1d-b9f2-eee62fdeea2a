Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > firstuieventtimestamp > index.md --- title: "PerformanceLongAnimationFrameTiming: firstUIEventTimestamp property" short-title: firstUIEventTimestamp slug: Web/API/PerformanceLongAnimationFrameTiming/firstUIEventTimestamp page-type: web-api-instance-property status: - experimental browser-compat: api.PerformanceLongAnimationFrameTiming.firstUIEventTimestamp --- {{SeeCompatTable}}{{APIRef("Performance API")}} The **`firstUIEventTimestamp`** read-only property of the {{domxref("PerformanceLongAnimationFrameTiming")}} interface returns a {{domxref("DOMHighResTimeStamp")}} indicating the time of the first UI event such as a mouse or keyboard event to be queued during the current animation frame. ## Value A {{domxref("DOMHighResTimeStamp")}}. ## Examples See [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing#examples) for examples related to the Long Animation Frames API. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing) - {{domxref("PerformanceScriptTiming")}}