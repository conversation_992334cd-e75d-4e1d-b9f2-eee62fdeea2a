Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md

Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancelonganimationframetiming > styleandlayoutstart > index.md --- title: "PerformanceLongAnimationFrameTiming: styleAndLayoutStart property" short-title: styleAndLayoutStart slug: Web/API/PerformanceLongAnimationFrameTiming/styleAndLayoutStart page-type: web-api-instance-property status: - experimental browser-compat: api.PerformanceLongAnimationFrameTiming.styleAndLayoutStart --- {{SeeCompatTable}}{{APIRef("Performance API")}} The **`styleAndLayoutStart`** read-only property of the {{domxref("PerformanceLongAnimationFrameTiming")}} interface returns a {{domxref("DOMHighResTimeStamp")}} indicating the beginning of the time period spent in style and layout calculations for the current animation frame. ## Value A {{domxref("DOMHighResTimeStamp")}}. ## Examples See [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing#examples) for examples related to the Long Animation Frames API. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing) - {{domxref("PerformanceScriptTiming")}}