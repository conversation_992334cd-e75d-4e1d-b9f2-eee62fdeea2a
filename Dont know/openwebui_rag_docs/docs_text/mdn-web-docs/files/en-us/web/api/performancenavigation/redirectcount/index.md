Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md

Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md Path: mdn-web-docs > files > en-us > web > api > performancenavigation > redirectcount > index.md --- title: "PerformanceNavigation: redirectCount property" short-title: redirectCount slug: Web/API/PerformanceNavigation/redirectCount page-type: web-api-instance-property status: - deprecated browser-compat: api.PerformanceNavigation.redirectCount --- {{APIRef("Performance API")}}{{Deprecated_Header}} The legacy **`PerformanceNavigation.redirectCount`** read-only property returns an `unsigned short` representing the number of REDIRECTs done before reaching the page. > [!WARNING] > This interface of this property is deprecated in the [Navigation Timing Level 2 specification](https://w3c.github.io/navigation-timing/#obsolete). > Please use the {{domxref("PerformanceNavigationTiming")}} interface instead. ## Value An `unsigned short`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("PerformanceNavigation")}} interface it belongs to.