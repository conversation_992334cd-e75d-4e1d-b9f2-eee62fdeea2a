Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md

Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > performanceresourcetiming > tojson > index.md --- title: "PerformanceResourceTiming: toJSON() method" short-title: toJSON() slug: Web/API/PerformanceResourceTiming/toJSON page-type: web-api-instance-method browser-compat: api.PerformanceResourceTiming.toJSON --- {{APIRef("Performance API")}}{{AvailableInWorkers}} The **`toJSON()`** method of the {{domxref("PerformanceResourceTiming")}} interface is a {{Glossary("Serialization","serializer")}}; it returns a JSON representation of the {{domxref("PerformanceResourceTiming")}} object. ## Syntax ```js-nolint toJSON() ``` ### Parameters None. ### Return value A {{jsxref("JSON")}} object that is the serialization of the {{domxref("PerformanceResourceTiming")}} object. ## Examples ### Using the toJSON method In this example, calling `entry.toJSON()` returns a JSON representation of the `PerformanceResourceTiming` object. ```js const observer = new PerformanceObserver((list) => { list.getEntries().forEach((entry) => { console.log(entry.toJSON()); }); }); observer.observe({ type: "resource", buffered: true }); ``` This would log a JSON object like so: ```json { "name": "https://upload.wikimedia.org/wikipedia/en/thumb/4/4a/Commons-logo.svg/31px-Commons-logo.svg.png", "entryType": "resource", "startTime": 110.80000001192093, "duration": 11.599999994039536, "initiatorType": "img", "nextHopProtocol": "h2", "renderBlockingStatus": "non-blocking", "workerStart": 0, "redirectStart": 0, "redirectEnd": 0, "fetchStart": 110.80000001192093, "domainLookupStart": 110.80000001192093, "domainLookupEnd": 110.80000001192093, "connectStart": 110.80000001192093, "connectEnd": 110.80000001192093, "secureConnectionStart": 110.80000001192093, "requestStart": 117.30000001192093, "responseStart": 120.40000000596046, "responseStatus": 200, "responseEnd": 122.40000000596046, "transferSize": 0, "encodedBodySize": 880, "decodedBodySize": 880, "serverTiming": [ { "name": "cache", "duration": 0, "description": "hit-front" }, { "name": "host", "duration": 0, "description": "cp3061" } ] } ``` To get a JSON string, you can use [`JSON.stringify(entry)`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify) directly; it will call `toJSON()` automatically. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{jsxref("JSON")}}