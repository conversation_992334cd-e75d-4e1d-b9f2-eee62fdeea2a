Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md

Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md Path: mdn-web-docs > files > en-us > web > api > performancescripttiming > executionstart > index.md --- title: "PerformanceScriptTiming: executionStart property" short-title: executionStart slug: Web/API/PerformanceScriptTiming/executionStart page-type: web-api-instance-property status: - experimental browser-compat: api.PerformanceScriptTiming.executionStart --- {{SeeCompatTable}}{{APIRef("Performance API")}} The **`executionStart`** read-only property of the {{domxref("PerformanceScriptTiming")}} interface returns a {{domxref("DOMHighResTimeStamp")}} indicating the time when the script compilation finished and execution started. ## Value A {{domxref("DOMHighResTimeStamp")}}. ## Examples See [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing#examples) for examples related to the Long Animation Frames API. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Long animation frame timing](/en-US/docs/Web/API/Performance_API/Long_animation_frame_timing) - {{domxref("PerformanceLongAnimationFrameTiming")}}