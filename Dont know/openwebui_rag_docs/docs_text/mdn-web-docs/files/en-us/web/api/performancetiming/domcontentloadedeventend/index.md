Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md

Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md Path: mdn-web-docs > files > en-us > web > api > performancetiming > domcontentloadedeventend > index.md --- title: "PerformanceTiming: domContentLoadedEventEnd property" short-title: domContentLoadedEventEnd slug: Web/API/PerformanceTiming/domContentLoadedEventEnd page-type: web-api-instance-property status: - deprecated browser-compat: api.PerformanceTiming.domContentLoadedEventEnd --- {{APIRef("Performance API")}}{{Deprecated_Header}} > [!WARNING] > This interface of this property is deprecated in the [Navigation Timing Level 2 specification](https://w3c.github.io/navigation-timing/#obsolete). Please use the {{domxref("PerformanceNavigationTiming")}} > interface instead. The legacy **`PerformanceTiming.domContentLoadedEventEnd`** read-only property returns an `unsigned long long` representing the moment, in milliseconds since the UNIX epoch, right after all the scripts that need to be executed as soon as possible, in order or not, has been executed. ## Value An `unsigned long long`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("PerformanceTiming")}} interface it belongs to.