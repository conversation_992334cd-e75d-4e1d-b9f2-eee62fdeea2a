Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md

Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpictureevent > pictureinpicturewindow > index.md --- title: "PictureInPictureEvent: pictureInPictureWindow property" short-title: pictureInPictureWindow slug: Web/API/PictureInPictureEvent/pictureInPictureWindow page-type: web-api-instance-property browser-compat: api.PictureInPictureEvent.pictureInPictureWindow --- {{APIRef("Picture-in-Picture API")}} The read-only **`pictureInPictureWindow`** property of the {{domxref("PictureInPictureEvent")}} interface returns the {{domxref("PictureInPictureWindow")}} the event relates to. ## Value A {{domxref("PictureInPictureWindow")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Picture-in-Picture API](/en-US/docs/Web/API/Picture-in-Picture_API)