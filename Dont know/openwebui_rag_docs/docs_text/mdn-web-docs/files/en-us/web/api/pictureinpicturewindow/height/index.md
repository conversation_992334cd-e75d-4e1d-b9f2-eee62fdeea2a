Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > height > index.md

Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > height > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > height > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > height > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > height > index.md --- title: "PictureInPictureWindow: height property" short-title: height slug: Web/API/PictureInPictureWindow/height page-type: web-api-instance-property browser-compat: api.PictureInPictureWindow.height --- {{APIRef("Picture-in-Picture API")}} The read-only **`height`** property of the {{domxref("PictureInPictureWindow")}} interface returns the height of the floating video window in pixels. ## Value An integer value indicating the height of the floating video window in pixels if the Picture-in-Picture window is open. Otherwise, it returns `0`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Picture-in-Picture API](/en-US/docs/Web/API/Picture-in-Picture_API) - {{DOMxRef("PictureInPictureWindow.width")}}