Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > width > index.md

Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > width > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > width > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > width > index.md Path: mdn-web-docs > files > en-us > web > api > pictureinpicturewindow > width > index.md --- title: "PictureInPictureWindow: width property" short-title: width slug: Web/API/PictureInPictureWindow/width page-type: web-api-instance-property browser-compat: api.PictureInPictureWindow.width --- {{APIRef("Picture-in-Picture API")}} The read-only **`width`** property of the {{domxref("PictureInPictureWindow")}} interface returns the width of the floating video window in pixels. ## Value An integer value indicating the width of the floating video window in pixels if the Picture-in-Picture window is open. Otherwise, it returns `0`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Picture-in-Picture API](/en-US/docs/Web/API/Picture-in-Picture_API) - {{DOMxRef("PictureInPictureWindow.height")}}