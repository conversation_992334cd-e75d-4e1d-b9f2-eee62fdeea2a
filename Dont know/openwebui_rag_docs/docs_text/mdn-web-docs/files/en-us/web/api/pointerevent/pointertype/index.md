Path: mdn-web-docs > files > en-us > web > api > pointerevent > pointertype > index.md

Path: mdn-web-docs > files > en-us > web > api > pointerevent > pointertype > index.md Path: mdn-web-docs > files > en-us > web > api > pointerevent > pointertype > index.md Path: mdn-web-docs > files > en-us > web > api > pointerevent > pointertype > index.md Path: mdn-web-docs > files > en-us > web > api > pointerevent > pointertype > index.md --- title: "PointerEvent: pointerType property" short-title: pointerType slug: Web/API/PointerEvent/pointerType page-type: web-api-instance-property browser-compat: api.PointerEvent.pointerType --- {{ APIRef("Pointer Events") }} The **`pointerType`** read-only property of the {{domxref("PointerEvent")}} interface indicates the device type (mouse, pen, or touch) that caused a given pointer event. ## Value The event's pointer type. The supported values are the following strings: - `"mouse"` - : The event was generated by a mouse device. - `"pen"` - : The event was generated by a pen or stylus device. - `"touch"` - : The event was generated by a touch, such as a finger. If the device type cannot be detected by the browser, the value can be an empty string (`""`). If the browser supports pointer device types other than those listed above, the value should be _vendor-prefixed_ to avoid conflicting names for different types of devices. ## Examples This example illustrates using the value of the `pointerType` property to call the appropriate pointer type processing function. ```js targetElement.addEventListener( "pointerdown", (event) => { // Call the appropriate pointer type handler switch (event.pointerType) { case "mouse": process_pointer_mouse(event); break; case "pen": process_pointer_pen(event); break; case "touch": process_pointer_touch(event); break; default: console.log(`pointerType ${event.pointerType} is not supported`); } }, false, ); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}