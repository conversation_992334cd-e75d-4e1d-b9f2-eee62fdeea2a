Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md

Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md Path: mdn-web-docs > files > en-us > web > api > presentationavailability > value > index.md --- title: "PresentationAvailability: value property" short-title: value slug: Web/API/PresentationAvailability/value page-type: web-api-instance-property status: - experimental browser-compat: api.PresentationAvailability.value --- {{APIRef("Presentation API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`value`** attribute _MUST_ return the last value from which it was set. The value is updated by the [monitor the list of available presentation displays](https://www.w3.org/TR/presentation-api/#dfn-monitor-the-list-of-available-presentation-displays) algorithm. The `onchange` attribute is an [event handler](https://www.w3.org/TR/presentation-api/#dfn-event-handler) whose corresponding [event handler event type](https://www.w3.org/TR/presentation-api/#dfn-event-handler-event-type) is `change`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}