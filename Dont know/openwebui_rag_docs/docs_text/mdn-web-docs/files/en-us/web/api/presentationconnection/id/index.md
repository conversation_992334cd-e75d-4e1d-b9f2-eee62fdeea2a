Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md

Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > id > index.md --- title: "PresentationConnection: id property" short-title: id slug: Web/API/PresentationConnection/id page-type: web-api-instance-property status: - experimental browser-compat: api.PresentationConnection.id --- {{APIRef("Presentation API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`id`** attribute specifies the [presentation identifier](https://www.w3.org/TR/presentation-api/#dfn-presentation-identifier) of a [presentation connection](https://www.w3.org/TR/presentation-api/#dfn-presentation-connection). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}