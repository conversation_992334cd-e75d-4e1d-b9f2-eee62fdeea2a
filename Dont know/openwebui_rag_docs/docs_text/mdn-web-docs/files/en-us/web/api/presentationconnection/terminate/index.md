Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md

Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > terminate > index.md --- title: "PresentationConnection: terminate() method" short-title: terminate() slug: Web/API/PresentationConnection/terminate page-type: web-api-instance-method status: - experimental browser-compat: api.PresentationConnection.terminate --- {{APIRef("Presentation API")}}{{SeeCompatTable}}{{SecureContext_Header}} When the `terminate()` method is called on a {{domxref("PresentationConnection")}}, the {{Glossary("user agent")}} begins the process of terminating the presentation. The exact process differs depending on whether `terminate()` is called in the controlling or the presenting context. ## Syntax ```js-nolint terminate() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}