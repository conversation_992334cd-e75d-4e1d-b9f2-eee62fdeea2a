Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md

Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md Path: mdn-web-docs > files > en-us > web > api > presentationconnection > url > index.md --- title: "PresentationConnection: url property" short-title: url slug: Web/API/PresentationConnection/url page-type: web-api-instance-property status: - experimental browser-compat: api.PresentationConnection.url --- {{SeeCompatTable}}{{APIRef("Presentation API")}}{{SecureContext_Header}} The **`url`** read-only property of the {{domxref("PresentationConnection")}} interface returns the URL used to create or reconnect to the presentation. ## Value A string containing a URL. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}