Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md

Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md Path: mdn-web-docs > files > en-us > web > api > presentationrequest > presentationrequest > index.md --- title: "PresentationRequest: PresentationRequest() constructor" short-title: PresentationRequest() slug: Web/API/PresentationRequest/PresentationRequest page-type: web-api-constructor status: - experimental browser-compat: api.PresentationRequest.PresentationRequest --- {{APIRef("Presentation API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`PresentationRequest()`** constructor creates a new {{domxref("PresentationRequest")}} object which creates a new PresentationRequest. ## Syntax ```js-nolint new PresentationRequest(url) new PresentationRequest(urls) ``` ### Parameters - `url` or `urls\[]` - : A URL or array of URLs that are possible URLs used to create, or reconnect, a presentation for the PresentationRequest instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}