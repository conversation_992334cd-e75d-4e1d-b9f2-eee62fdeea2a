Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md

Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md Path: mdn-web-docs > files > en-us > web > api > progressevent > lengthcomputable > index.md --- title: "ProgressEvent: lengthComputable property" short-title: lengthComputable slug: Web/API/ProgressEvent/lengthComputable page-type: web-api-instance-property browser-compat: api.ProgressEvent.lengthComputable --- {{APIRef("XMLHttpRequest API")}}{{AvailableInWorkers}} The **`ProgressEvent.lengthComputable`** read-only property is a boolean flag indicating if the resource concerned by the {{domxref("ProgressEvent")}} has a length that can be calculated. If not, the {{domxref("ProgressEvent.total")}} property has no significant value. ## Value A boolean. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("ProgressEvent")}} interface it belongs to.