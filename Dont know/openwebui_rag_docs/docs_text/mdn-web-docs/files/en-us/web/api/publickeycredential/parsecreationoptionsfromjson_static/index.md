Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md

Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredential > parsecreationoptionsfromjson_static > index.md --- title: "PublicKeyCredential: parseCreationOptionsFromJSON() static method" short-title: parseCreationOptionsFromJSON() slug: Web/API/PublicKeyCredential/parseCreationOptionsFromJSON_static page-type: web-api-static-method browser-compat: api.PublicKeyCredential.parseCreationOptionsFromJSON_static --- {{APIRef("Web Authentication API")}}{{securecontext_header}} The **`parseCreationOptionsFromJSON()`** static method of the {{domxref("PublicKeyCredential")}} interface creates a {{domxref("PublicKeyCredentialCreationOptions")}} object from a JSON representation of its properties. The method is a convenience function for converting credential options information provided by a relying party server to the form that a web app can use to [create a credential](/en-US/docs/Web/API/Web_Authentication_API#creating_a_key_pair_and_registering_a_user). ## Syntax ```js-nolint PublicKeyCredential.parseCreationOptionsFromJSON(options) ``` ### Parameters - `options` - : An object with the same structure as a {{domxref("PublicKeyCredentialCreationOptions")}}, but with [base64url](/en-US/docs/Glossary/Base64)-encoded strings used in place of buffer properties. ### Return value A {{domxref("PublicKeyCredentialCreationOptions")}} object. ### Exceptions - `EncodingError` {{domxref("DOMException")}} - : Thrown the `options` object cannot be converted into a {{domxref("PublicKeyCredentialCreationOptions")}} object. - `SecurityError` {{domxref("DOMException")}} - : The RP domain is not valid. ## Description The Web Authentication process for [creating a key pair and registering a user](/en-US/docs/Web/API/Web_Authentication_API#creating_a_key_pair_and_registering_a_user) involves a relying party server sending the web app information needed to create a credential, including details about the user identity, the relying party, and a "challenge". The web app passes this information to an authenticator to create the credential, by calling [`navigator.credentials.create()`](/en-US/docs/Web/API/CredentialsContainer/create) with a {{domxref("PublicKeyCredentialCreationOptions")}} object as an argument. The specification does not define how the information needed for creating a credential is sent. A convenient approach is for the server to encapsulate the information in a {{glossary("JSON type representation")}} of the {{domxref("PublicKeyCredentialCreationOptions")}} object that mirrors its structure but encodes buffer properties such as the `challenge` and `user.id` as [base64url](/en-US/docs/Glossary/Base64) strings. This object can be serialized to a [JSON](/en-US/docs/Glossary/JSON) string, sent to the web app and deserialized, and then converted to a {{domxref("PublicKeyCredentialCreationOptions")}} object using **`parseCreationOptionsFromJSON()`**. ## Examples When registering a new user, a relying party server will supply information about the expected credentials to the web app. The code below defines this information in the form described in the [`options` parameter](#options) above (taken from the ["getting an AuthenticatorAttestationResponse"](/en-US/docs/Web/API/AuthenticatorResponse#getting_an_authenticatorattestationresponse) in `AuthenticatorResponse`): ```js const createCredentialOptionsJSON = { challenge: "21, 31, 105, " /* 29 more random bytes generated by the server in this string */, rp: { name: "Example CORP", id: "login.example.com", }, user: { id: "16", name: "<EMAIL>", displayName: "Carina Anand", }, pubKeyCredParams: [ { type: "public-key", alg: -7, }, ], }; ``` Because this object only uses JSON data types, it can be serialized to JSON using [`JSON.stringify()`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify) and sent to the web app. ```js JSON.stringify(createCredentialOptionsJSON); ``` The web app can deserialize the JSON string back to a `createCredentialOptionsJSON` object (not shown). The **`parseCreationOptionsFromJSON()`** method is used to convert that object to the form that can be used in `navigator.credentials.create()`: ```js // Convert options to form used by create() const createCredentialOptions = PublicKeyCredential.parseCreationOptionsFromJSON( createCredentialOptionsJSON, // JSON-type representation ); navigator.credentials .create({ publicKey: createCredentialOptions }) .then((newCredentialInfo) => { // Handle the new credential information here. }) .catch((err) => { console.error(err); }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Authentication API](/en-US/docs/Web/API/Web_Authentication_API) - {{domxref("PublicKeyCredential.parseRequestOptionsFromJSON_static", "PublicKeyCredential.parseRequestOptionsFromJSON()")}} - {{domxref("PublicKeyCredential.toJSON()")}}