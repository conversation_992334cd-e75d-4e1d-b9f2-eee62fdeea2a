Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md

Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md Path: mdn-web-docs > files > en-us > web > api > publickeycredentialcreationoptions > index.md --- title: PublicKeyCredentialCreationOptions slug: Web/API/PublicKeyCredentialCreationOptions page-type: web-api-interface browser-compat: api.CredentialsContainer.create.publicKey_option spec-urls: https://w3c.github.io/webauthn/#dictionary-makecredentialoptions --- {{APIRef("Web Authentication API")}}{{securecontext_header}} The **`PublicKeyCredentialCreationOptions`** dictionary represents the object passed to {{domxref("CredentialsContainer.create()")}} as the value of the `publicKey` option: that is, when using `create()` to create a public key credential using the [Web Authentication API](/en-US/docs/Web/API/Web_Authentication_API). ## Instance properties - `attestation` {{optional_inline}} - : A string specifying the relying party's preference for how the attestation statement (i.e., provision of verifiable evidence of the authenticity of the authenticator and its data) is conveyed during credential creation. The value can be one of the following: - `"none"` - : Specifies that the relying party is not interested in authenticator attestation. This might be to avoid additional user consent for round trips to the relying party server to relay identifying information, or round trips to an attestation certificate authority (CA), with the aim of making the authentication process smoother. If `"none"` is chosen as the `attestation` value, and the authenticator signals that it uses a CA to generate its attestation statement, the client app will replace it with a "None" attestation statement, indicating that no attestation statement is available. - `"direct"` - : Specifies that the relying party wants to receive the attestation statement as generated by the authenticator. - `"enterprise"` - : Specifies that the Relying Party wants to receive an attestation statement that may include uniquely identifying information. This is intended for controlled deployments within an enterprise where the organization wishes to tie registrations to specific authenticators. - `"indirect"` - : Specifies that the relying party wants to receive a verifiable attestation statement, but it will allow the client to decide how to receive it. For example, the client could choose to replace the authenticator's assertion statement with one generated by an Anonymization CA to protect user privacy. If `attestation` is omitted, it will default to `"none"`. - `attestationFormats` {{optional_inline}} - : An array of strings specifying the relying party's preference for the attestation statement format used by the authenticator. Values should be ordered from highest to lowest preference, and should be considered hints the authenticator may choose to issue an attestation statement in a different format. For a list of valid formats, see [WebAuthn Attestation Statement Format Identifiers](https://www.iana.org/assignments/webauthn/webauthn.xhtml#webauthn-attestation-statement-format-ids). If omitted, `attestationFormats` defaults to an empty array. - `authenticatorSelection` {{optional_inline}} - : An object whose properties are criteria used to filter out the potential authenticators for the credential creation operation. This object can contain the properties: - `authenticatorAttachment` {{optional_inline}} - : A string indicating which authenticator attachment type should be permitted for the chosen authenticator. Possible values are: - `"platform"` - : The authenticator is part of the device WebAuthn is running on (termed a **platform authenticator**), therefore WebAuthn will communicate with it using a transport available to that platform, such as a platform-specific API. A public key credential bound to a platform authenticator is called a **platform credential**. - `"cross-platform"` - : The authenticator is not a part of the device WebAuthn is running on (termed a **roaming authenticator** as it can roam between different devices), therefore WebAuthn will communicate with it using a cross-platform transport protocol such as Bluetooth or NFC. A public key credential bound to a roaming authenticator is called a **roaming credential**. If omitted, any type of authenticator, either platform or cross-platform, can be selected for the credential creation operation. - `requireResidentKey` {{optional_inline}} - : A boolean. If set to `true`, it indicates that a resident key is required (see `residentKey`) This property is deprecated, but still available in some implementations for backwards compatibility with WebAuthn Level 1. The value should be set to `true` if `residentKey` is set to `"required"`. If omitted, `requireResidentKey` defaults to `false`. - `residentKey` {{optional_inline}} - : A string that specifies the extent to which the relying party desires to create a client-side [discoverable credential](/en-US/docs/Web/API/Web_Authentication_API#discoverable_credentials_and_conditional_mediation) (that is, one that is usable in authentication requests where the relying party does not provide credential IDs {{domxref("CredentialsContainer.get()", "navigator.credentials.get()")}} is called with an empty `allowCredentials` value). The alternative is a **server-side credential**, where the relying party must provide credential IDs in the `get()` `allowCredentials` value. Possible values are: - `"discouraged"` - : The relying party prefers creation of a server-side credential, but will accept a client-side discoverable credential. - `"preferred"` - : The relying party strongly prefers creation of a client-side discoverable credential, but will accept a server-side credential. The user agent should guide the user through setting up user verification, if needed, to create a discoverable credential. This takes precedence over the `userVerification` setting. - `"required"` - : The relying party requires a client-side discoverable credential. If one cannot be created, a `NotAllowedError` {{domxref("DOMException")}} is thrown. See the [`create()` exceptions list](/en-US/docs/Web/API/CredentialsContainer/create#exceptions) for more details. If omitted, `residentKey` defaults to `"required"` if `requireResidentKey` is `true`, otherwise the default value is `"discouraged"`. - `userVerification` {{optional_inline}} - : A string that specifies the relying party's requirements for user verification for the `create()` operation. Possible values are: - `"discouraged"` - : The relying party prefers no user verification for the `create()` operation, in the interests of minimizing disruption to the user experience. - `"preferred"` - : The relying party prefers user verification for the `create()` operation, but it will not fail if user verification cannot be performed. - `"required"` - : The relying party requires user verification for the `create()` operation if user verification cannot be performed, an error is thrown. If omitted, `userVerification` defaults to `"preferred"`. - `challenge` - : An {{jsxref("ArrayBuffer")}}, {{jsxref("TypedArray")}}, or {{jsxref("DataView")}} provided by the relying party's server and used as a [cryptographic challenge](https://en.wikipedia.org/wiki/Challenge%E2%80%93response_authentication). This value will be signed by the authenticator and the signature will be sent back as part of {{domxref("AuthenticatorAttestationResponse.attestationObject")}}. - `excludeCredentials` {{optional_inline}} - : An {{jsxref("Array")}} of objects describing existing credentials that are already mapped to this user account (as identified by `user.id`). This is provided by the relying party, and checked by the user agent to avoid creating a new public key credential on an authenticator that already has a credential mapped to the specified user account. Each item should be of the form: - `id` - : An {{jsxref("ArrayBuffer")}}, {{jsxref("TypedArray")}}, or {{jsxref("DataView")}} representing the existing credential ID. - `transports` {{optional_inline}} - : An {{jsxref("Array")}} of strings representing allowed transports. Possible transports are: `"ble"`, `"hybrid"`, `"internal"`, `"nfc"`, and `"usb"` (see {{domxref("AuthenticatorAttestationResponse.getTransports", "getTransports()")}} for more details). - `type` - : A string defining the type of public key credential to create. This can currently take a single value, `"public-key"`, but more values may be added in the future. If the `create()` call is attempting to create a duplicate public key credential on an authenticator, the user agent will guide to user to create the credential using a different authenticator, or fail if that is not possible. If `excludeCredentials` is omitted, it defaults to an empty array. - `extensions` {{optional_inline}} - : An object containing properties representing the input values for any requested extensions. These extensions are used to specific additional processing by the client or authenticator during the credential creation process. Examples include specifying whether a returned credential is discoverable, or whether the relying party will be able to store large blob data associated with a credential. Extensions are optional and different browsers may recognize different extensions. Processing extensions is always optional for the client: if a browser does not recognize a given extension, it will just ignore it. For information on using extensions, and which ones are supported by which browsers, see [Web Authentication extensions](/en-US/docs/Web/API/Web_Authentication_API/WebAuthn_extensions). - `pubKeyCredParams` - : An {{jsxref("Array")}} of objects which specify the key types and signature algorithms the Relying Party supports, ordered from most preferred to least preferred. The client and authenticator will make a best-effort to create a credential of the most preferred type possible. These objects will contain the following properties: - `alg` - : A number that is equal to a [COSE Algorithm Identifier](https://www.iana.org/assignments/cose/cose.xhtml#algorithms), representing the cryptographic algorithm to use for this credential type. It is recommended that relying parties that wish to support a wide range of authenticators should include at least the following values in the provided choices: - `-8`: Ed25519 - `-7`: ES256 - `-257`: RS256 - `type` - : A string defining the type of public key credential to create. This can currently take a single value, `"public-key"`, but more values may be added in the future. If none of the listed credential types can be created, the `create()` operation fails. - `rp` - : An object describing the relying party that requested the credential creation. It can contain the following properties: - `id` {{optional_inline}} - : A string representing the ID of the relying party. A public key credential can only be used for authentication with the same relying party (as identified by the `publicKey.rpId` in a {{domxref("CredentialsContainer.get()", "navigator.credentials.get()")}} call) it was registered with the IDs need to match. The `id` cannot include a port or scheme like a standard origin, but the domain scheme must be `https` scheme. The `id` needs to equal the origin's effective domain, or a domain suffix thereof. So for example if the relying party's origin is `https://login.example.com:1337`, the following `id`s are valid: - `login.example.com` - `example.com` But not: - `m.login.example.com` - `com` If omitted, `id` defaults to the document origin which would be `login.example.com` in the above example. - `name` - : A string representing the name of the relying party (e.g., `"Facebook"`). This is the name the user will be presented with when creating or validating a WebAuthn operation. - `timeout` {{optional_inline}} - : A numerical hint, in milliseconds, which indicates the time the calling web app is willing to wait for the creation operation to complete. This hint may be overridden by the browser. - `user` - : An object describing the user account for which the credential is generated. It can contain the following properties: - `displayName` - : A string providing a human-friendly user display name (example: `"John Doe"`), which will have been set by user during initial registration with the relying party. - `id` - : An {{jsxref("ArrayBuffer")}}, {{jsxref("TypedArray")}}, or {{jsxref("DataView")}} representing a unique ID for the user account. This value has a maximum length of 64 bytes, and is not intended to be displayed to the user. - `name` - : A string providing a human-friendly identifier for the user's account, to help distinguish between different accounts with similar `displayName`s. This could be an email address (such as `"<EMAIL>"`), phone number (for example `"+***********"`), or some other kind of user account identifier (for example `"JohnDoe667"`). - `hints` {{optional_inline}} - : An array of strings providing hints as to what authentication UI the user-agent should provide for the user. The values can be any of the following: - `"security-key"` - : Authentication requires a separate dedicated physical device to provide the key. - `"client-device"` - : The user authenticates using their own device, such as a phone. - `"hybrid"` - : Authentication relies on a combination of authorization/authentication methods, potentially relying on both user and server-based mechanisms. ## Examples ### Creating a public key credential This example creates a `PublicKeyCredentialCreationOptions`, specifying only the required properties, and using defaults for the rest. It then passes the object into `navigator.credentials.create()`, to create a new public key credential. ```js const publicKey = { challenge: challengeFromServer, rp: { id: "acme.com", name: "ACME Corporation" }, user: { id: new Uint8Array([79, 252, 83, 72, 214, 7, 89, 26]), name: "jamiedoe", displayName: "Jamie Doe", }, pubKeyCredParams: [{ type: "public-key", alg: -7 }], }; const publicKeyCredential = await navigator.credentials.create({ publicKey }); ``` A successful `create()` call returns a promise that resolves with a {{domxref("PublicKeyCredential")}} object instance, representing a public key credential that can later be used to authenticate a user via a WebAuthn {{domxref("CredentialsContainer.get()", "get()")}} call. Its {{domxref("PublicKeyCredential.response")}} property contains an {{domxref("AuthenticatorAttestationResponse")}} object providing access to several useful pieces of information including the authenticator data, public key, transport mechanisms, and more. ```js navigator.credentials.create({ publicKey }).then((publicKeyCredential) => { const response = publicKeyCredential.response; // Access attestationObject ArrayBuffer const attestationObj = response.attestationObject; // Access client JSON const clientJSON = response.clientDataJSON; // Return authenticator data ArrayBuffer const authenticatorData = response.getAuthenticatorData(); // Return public key ArrayBuffer const pk = response.getPublicKey(); // Return public key algorithm identifier const pkAlgo = response.getPublicKeyAlgorithm(); // Return permissible transports array const transports = response.getTransports(); }); ``` Some of this data will need to be stored on the server for future authentication operations against this credential for example the public key, the algorithm used, and the permissible transports. See [Creating a key pair and registering a user](/en-US/docs/Web/API/Web_Authentication_API#creating_a_key_pair_and_registering_a_user) for more information about how the overall flow works. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}