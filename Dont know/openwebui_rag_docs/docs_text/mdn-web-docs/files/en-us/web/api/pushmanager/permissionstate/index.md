Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md

Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > permissionstate > index.md --- title: "PushManager: permissionState() method" short-title: permissionState() slug: Web/API/PushManager/permissionState page-type: web-api-instance-method browser-compat: api.PushManager.permissionState --- {{ApiRef("Push API")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`permissionState()`** method of the {{domxref("PushManager")}} interface returns a {{jsxref("Promise")}} that resolves to a string indicating the permission state of the push manager. Possible values are `'prompt'`, `'denied'`, or `'granted'`. > [!NOTE] > As of Firefox 44, the permissions for [Notifications](/en-US/docs/Web/API/Notifications_API) and [Push](/en-US/docs/Web/API/Push_API) have been merged. If permission is > granted for notifications, push will also be enabled. ## Syntax ```js-nolint permissionState() permissionState(options) ``` ### Parameters - `options` {{optional_inline}} - : An object containing optional configuration parameters. It can have the following properties: - `userVisibleOnly` - : A boolean indicating that the returned push subscription will only be used for messages whose effect is made visible to the user. - `applicationServerKey` - : A public key your push server will use to send messages to client apps via a push server. This value is part of a signing key pair generated by your application server and usable with elliptic curve digital signature (ECDSA) over the P-256 curve. ### Return value A {{jsxref("Promise")}} that resolves to a string with a value of `'prompt'`, `'denied'`, or `'granted'`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}