Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md

Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md Path: mdn-web-docs > files > en-us > web > api > pushmanager > supportedcontentencodings_static > index.md --- title: "PushManager: supportedContentEncodings static property" short-title: supportedContentEncodings slug: Web/API/PushManager/supportedContentEncodings_static page-type: web-api-static-property browser-compat: api.PushManager.supportedContentEncodings_static --- {{APIRef("Push API")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`supportedContentEncodings`** read-only static property of the {{domxref("PushManager")}} interface returns an array of supported content codings that can be used to encrypt the payload of a push message. User agents must support the `aes128gcm` content coding defined in {{rfc("8291")}}, and may also support content codings defined from previous versions of the specification. The returned array is frozen, and may not be modified by the recipient. The application server requires this coding in order to encrypt push messages for sending to the push server. The coding used for encryption is also included by the app server in the {{httpheader("Content-Encoding")}} HTTP header field of each push message. The specification does not define how the client code should send the application server the supported codings, or the information in the {{domxref("PushSubscription")}} that it also needs in order to encrypt and send a push message. One approach is shown in the examples section below. ## Value An array of strings. This usually contains just one value: `"aes128gcm"`. ## Exceptions - `TypeError` - : This is thrown when attempting to set a value in the returned array. ## Examples ### Sending coding information to the server Push messages are encrypted on the application server for sending to the push server, and decrypted by the browser before being passed to the application service worker. The public and private keys used are generated by the browser, and only the public key and an associated secret are shared with the web app, and hence the application server. This ensures that push messages remain private as they pass through the push server infrastructure. The [`p256dh`](/en-US/docs/Web/API/PushSubscription/getKey#p256dh) public key and [`auth`](/en-US/docs/Web/API/PushSubscription/getKey#auth) secret used for encrypting the message are provided to the service worker via its push subscription, using the {{domxref("PushSubscription.getKey()")}} method, along with the target endpoint for sending push messages in {{domxref("PushSubscription.endpoint")}}. The coding that should be used for encryption is provided by `supportedContentEncodings`. This information may be sent to the application server using any mechanism. One approach is to put the needed information from {{domxref("PushSubscription")}} and `supportedContentEncodings` into a JSON object, serialize it using [`JSON.stringify()`](/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify) and post the result to the application server. ```js // Get a PushSubscription object const pushSubscription = await serviceWorkerRegistration.pushManager.subscribe(); // Create an object containing the information needed by the app server const subscriptionObject = { endpoint: pushSubscription.endpoint, keys: { p256dh: pushSubscription.getKeys("p256dh"), auth: pushSubscription.getKeys("auth"), }, encoding: PushManager.supportedContentEncodings, /* other app-specific data, such as user identity */ }; // Stringify the object an post to the app server fetch("https://example.com/push/", { method: "POST", body: JSON.stringify(pushSubscription), }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}