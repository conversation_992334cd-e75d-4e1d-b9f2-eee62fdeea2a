Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md

Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md Path: mdn-web-docs > files > en-us > web > api > pushmessagedata > blob > index.md --- title: "PushMessageData: blob() method" short-title: blob() slug: Web/API/PushMessageData/blob page-type: web-api-instance-method browser-compat: api.PushMessageData.blob --- {{APIRef("Push API")}}{{SecureContext_Header}}{{AvailableInWorkers("service")}} The **`blob()`** method of the {{domxref("PushMessageData")}} interface extracts push message data as a {{domxref("Blob")}} object. ## Syntax ```js-nolint blob() ``` ### Parameters None. ### Return value A {{domxref("Blob")}}. ## Examples ```js self.addEventListener("push", (event) => { const blob = event.data.blob(); // do something with your Blob }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}