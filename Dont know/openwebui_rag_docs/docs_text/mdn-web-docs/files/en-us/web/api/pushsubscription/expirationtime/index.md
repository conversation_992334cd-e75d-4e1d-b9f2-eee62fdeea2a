Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md

Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > expirationtime > index.md --- title: "PushSubscription: expirationTime property" short-title: expirationTime slug: Web/API/PushSubscription/expirationTime page-type: web-api-instance-property browser-compat: api.PushSubscription.expirationTime --- {{APIRef("Push API")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`expirationTime`** read-only property of the {{domxref("PushSubscription")}} interface returns a {{domxref("DOMHighResTimeStamp")}} of the subscription expiration time associated with the push subscription, if there is one, or `null` otherwise. ## Value A {{domxref("DOMHighResTimeStamp")}} or `null`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}