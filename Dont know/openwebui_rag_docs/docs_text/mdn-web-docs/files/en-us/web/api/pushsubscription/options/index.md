Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md

Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > options > index.md --- title: "PushSubscription: options property" short-title: options slug: Web/API/PushSubscription/options page-type: web-api-instance-property browser-compat: api.PushSubscription.options --- {{APIRef("Push API")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`options`** read-only property of the {{domxref("PushSubscription")}} interface is an object containing the options used to create the subscription. ## Value An read-only {{domxref("PushSubscriptionOptions")}} object containing the following values: - `userVisibleOnly` - : A boolean, indicating that the returned push subscription will only be used for messages whose effect is made visible to the user. - `applicationServerKey` - : A public key your push server will use to send messages to client apps via a push server. This value is part of a signing key pair generated by your application server, and usable with elliptic curve digital signature (ECDSA), over the P-256 curve. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}