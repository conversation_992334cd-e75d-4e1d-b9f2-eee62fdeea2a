Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md

Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md Path: mdn-web-docs > files > en-us > web > api > pushsubscription > subscriptionid > index.md --- title: "PushSubscription: subscriptionId property" short-title: subscriptionId slug: Web/API/PushSubscription/subscriptionId page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.PushSubscription.subscriptionId --- {{ApiRef("Push API")}}{{deprecated_header}}{{non-standard_header}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`subscriptionId`** read-only property of the {{domxref("PushSubscription")}} interface returns a string containing the subscription ID associated with the push subscription. > [!WARNING] > Instead of this feature, use the {{domxref("PushSubscription.endpoint")}} property on the same interface. ## Value A string. ## Specifications This feature was removed from the [Push API](https://w3c.github.io/push-api/#pushsubscription-interface) specification. It is no longer on track to become a standard. ## Browser compatibility {{Compat}}