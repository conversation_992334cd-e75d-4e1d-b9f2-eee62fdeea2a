Path: mdn-web-docs > files > en-us > web > api > range > setendafter > index.md

Path: mdn-web-docs > files > en-us > web > api > range > setendafter > index.md Path: mdn-web-docs > files > en-us > web > api > range > setendafter > index.md Path: mdn-web-docs > files > en-us > web > api > range > setendafter > index.md Path: mdn-web-docs > files > en-us > web > api > range > setendafter > index.md --- title: "Range: setEndAfter() method" short-title: setEndAfter() slug: Web/API/Range/setEndAfter page-type: web-api-instance-method browser-compat: api.Range.setEndAfter --- {{ApiRef("DOM")}} The **`Range.setEndAfter()`** method sets the end position of a {{domxref("Range")}} relative to another {{domxref("Node")}}. The parent `Node` of end of the `Range` will be the same as that for the `referenceNode`. ## Syntax ```js-nolint setEndAfter(referenceNode) ``` ### Parameters - `referenceNode` - : The {{domxref("Node")}} to end the {{domxref("Range")}} after. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js const range = document.createRange(); const referenceNode = document.getElementsByTagName("div").item(0); range.setEndAfter(referenceNode); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [The DOM interfaces index](/en-US/docs/Web/API/Document_Object_Model)