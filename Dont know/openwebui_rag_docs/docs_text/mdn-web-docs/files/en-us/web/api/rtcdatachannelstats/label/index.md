Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > label > index.md --- title: "RTCDataChannelStats: label property" short-title: label slug: Web/API/RTCDataChannelStats/label page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_data-channel.label --- {{APIRef("WebRTC")}} The **`label`** property of the {{domxref("RTCDataChannelStats")}} dictionary returns the {{domxref("RTCDataChannel.label", "label")}} of the associated data channel. The value of the label need not be unique, and its meaning is defined by the website or app when it creates the data channel. ## Value A string containing the same value as the {{domxref("RTCDataChannel.label")}} property of the associated data channel. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("RTCDataChannel.label")}}