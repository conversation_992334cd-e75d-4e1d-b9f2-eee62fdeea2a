Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagesreceived > index.md --- title: "RTCDataChannelStats: messagesReceived property" short-title: messagesReceived slug: Web/API/RTCDataChannelStats/messagesReceived page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_data-channel.messagesReceived --- {{APIRef("WebRTC")}} The **`messagesReceived`** property of the {{domxref("RTCDataChannelStats")}} dictionary returns the total number of [`message` events](/en-US/docs/Web/API/RTCDataChannel/message_event) fired for received messages on the associated {{domxref("RTCDataChannel")}}. ## Value A positive integer value indicating the total number of `message` events for inbound data on the associated data channel. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}