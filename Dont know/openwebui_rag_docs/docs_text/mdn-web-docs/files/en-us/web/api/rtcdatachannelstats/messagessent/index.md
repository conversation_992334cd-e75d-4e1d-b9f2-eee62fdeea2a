Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdatachannelstats > messagessent > index.md --- title: "RTCDataChannelStats: messagesSent property" short-title: messagesSent slug: Web/API/RTCDataChannelStats/messagesSent page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_data-channel.messagesSent --- {{APIRef("WebRTC")}} The **`messagesSent`** property of the {{domxref("RTCDataChannelStats")}} dictionary returns the total number of [`message` events](/en-US/docs/Web/API/RTCDataChannel/message_event) fired for sent messages on the associated {{domxref("RTCDataChannel")}}. ## Value A positive integer value indicating the total number of `message` events for outbound data on the associated data channel. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}