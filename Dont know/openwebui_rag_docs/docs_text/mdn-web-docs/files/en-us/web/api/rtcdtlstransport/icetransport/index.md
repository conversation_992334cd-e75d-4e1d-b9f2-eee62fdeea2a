Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcdtlstransport > icetransport > index.md --- title: "RTCDtlsTransport: iceTransport property" short-title: iceTransport slug: Web/API/RTCDtlsTransport/iceTransport page-type: web-api-instance-property browser-compat: api.RTCDtlsTransport.iceTransport --- {{APIRef("WebRTC")}} The **`iceTransport`** read-only property of the **{{DOMxRef("RTCDtlsTransport")}}** interface contains a reference to the underlying {{DOMxRef("RTCIceTransport")}}. ## Value The underlying {{DOMxRef("RTCIceTransport")}} instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}