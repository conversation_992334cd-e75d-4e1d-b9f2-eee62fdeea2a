Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > rtcencodedvideoframe > timestamp > index.md --- title: "RTCEncodedVideoFrame: timestamp property" short-title: timestamp slug: Web/API/RTCEncodedVideoFrame/timestamp page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.RTCEncodedVideoFrame.timestamp --- {{APIRef("WebRTC")}}{{AvailableInWorkers("window_and_dedicated")}}{{deprecated_header}}{{non-standard_header}} The **`timestamp`** read-only property of the {{domxref("RTCEncodedVideoFrame")}} interface indicates the time at which frame sampling started. ## Value A positive integer containing the sampling instant of the first byte in this frame, in microseconds. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using WebRTC Encoded Transforms](/en-US/docs/Web/API/WebRTC_API/Using_Encoded_Transforms)