Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidate > rtcicecandidate > index.md --- title: "RTCIceCandidate: RTCIceCandidate() constructor" short-title: RTCIceCandidate() slug: Web/API/RTCIceCandidate/RTCIceCandidate page-type: web-api-constructor browser-compat: api.RTCIceCandidate.RTCIceCandidate --- {{APIRef("WebRTC")}} The **`RTCIceCandidate()`** constructor creates and returns a new {{domxref("RTCIceCandidate")}} object, which can be configured to represent a single {{Glossary("ICE")}} candidate. ## Syntax ```js-nolint new RTCIceCandidate() new RTCIceCandidate(candidateInfo) ``` ### Parameters - `candidateInfo` {{optional_inline}} - : An optional object that can be provided to configure the candidate. The object has the following properties: <!-- The spec calls this object an RTCIceCandidateInit --> - `candidate` {{optional_inline}} - : A string describing the properties of the candidate, taken directly from the [SDP](/en-US/docs/Web/API/WebRTC_API/Protocols#sdp) attribute `"candidate"`. The candidate string specifies the network connectivity information for the candidate. If the `candidate` is an empty string (`""`), the end of the candidate list has been reached; this candidate is known as the "end-of-candidates" marker. The syntax of the candidate string is described in {{RFC(5245, "", 15.1)}}. For an a-line (attribute line) that looks like this: ```plain a=candidate:4234997325 1 udp 2043278322 *********** 44323 typ host ``` the corresponding `candidate` string's value will be `"candidate:4234997325 1 udp 2043278322 *********** 44323 typ host"`. The {{Glossary("user agent")}} always prefers candidates with the highest {{domxref("RTCIceCandidate.priority", "priority")}}, all else being equal. In the example above, the priority is `2043278322`. The attributes are all separated by a single space character, and are in a specific order. The complete list of attributes for this example candidate is: - {{domxref("RTCIceCandidate.foundation", "foundation")}} = 4234997325 - {{domxref("RTCIceCandidate.component", "component")}} = `"rtp"` (the number 1 is encoded to this string; 2 becomes `"rtcp"`) - {{domxref("RTCIceCandidate.protocol", "protocol")}} = `"udp"` - {{domxref("RTCIceCandidate.priority", "priority")}} = 2043278322 - {{domxref("RTCIceCandidate/address", "ip")}} = `"***********"` - {{domxref("RTCIceCandidate.port", "port")}} = 44323 - {{domxref("RTCIceCandidate.type", "type")}} = `"host"` Additional information can be found in {{domxref("RTCIceCandidate.candidate")}}. > [!NOTE] > For backward compatibility with older versions of the WebRTC specification, the constructor also accepts this string directly as an argument. - `sdpMid` {{optional_inline}} - : A string containing the identification tag of the media stream with which the candidate is associated, or `null` if there is no associated media stream. The default is `null`. Additional information can be found in {{domxref("RTCIceCandidate.sdpMid")}}. - `sdpMLineIndex` {{optional_inline}} - : A number property containing the zero-based index of the m-line with which the candidate is associated, within the [SDP](/en-US/docs/Web/API/WebRTC_API/Protocols#sdp) of the media description, or `null` if no such associated exists. The default is `null`. Additional information can be found in {{domxref("RTCIceCandidate.sdpMLineIndex")}}. - `usernameFragment` {{optional_inline}} - : A string containing the username fragment (usually referred to in shorthand as "ufrag" or "ice-ufrag"). This fragment, along with the ICE password ("ice-pwd"), uniquely identifies a single ongoing ICE interaction (including for any communication with the {{Glossary("STUN")}} server). The string is generated by WebRTC at the beginning of the session. It may be up to 256 characters long, and at least 24 bits must contain random data. It has no default value and is not present unless set explicitly. Additional information can be found in {{domxref("RTCIceCandidate.usernameFragment")}}. ### Return value A newly-created {{domxref("RTCIceCandidate")}} object. If `candidateInfo` is provided, the new `RTCIceCandidate` is initialized as follows: - Each member of the `RTCIceCandidate` object is initialized to the value of the property by the same name from `candidateInfo`. This includes the {{domxref("RTCIceCandidate.candidate", "candidate")}}, {{domxref("RTCIceCandidate.sdpMid", "sdpMid")}}, {{domxref("RTCIceCandidate.sdpMLineIndex", "sdpMLineIndex")}}, and {{domxref("RTCIceCandidate.usernameFragment", "usernameFragment")}} properties. - The `candidate` string (which is SDP text) is parsed; each property found is stored in the corresponding field in the `RTCIceCandidate`. If any of the fields is invalid, parsing of the string silently aborts without throwing an exception. The default value of `candidate` is the empty string, which indicates that the candidate is an "end-of-candidates" message. - The following fields are initialized to `null` if they are not included in the {{domxref("RTCIceCandidate.candidate")}} property: {{domxref("RTCIceCandidate.foundation", "foundation")}}, {{domxref("RTCIceCandidate.component", "component")}}, {{domxref("RTCIceCandidate.priority", "priority")}}, {{domxref("RTCIceCandidate/address","address")}}, {{domxref("RTCIceCandidate.protocol", "protocol")}}, {{domxref("RTCIceCandidate.port", "port")}}, {{domxref("RTCIceCandidate.type", "type")}}, {{domxref("RTCIceCandidate.tcpType", "tcpType")}}, {{domxref("RTCIceCandidate.relatedAddress", "relatedAddress")}}, and {{domxref("RTCIceCandidate.relatedPort", "relatedPort")}}. > [!NOTE] > Parsing of the `candidate` string is performed using the [candidate-attribute grammar](https://w3c.github.io/webrtc-pc/#candidate-attribute-grammar) from the WebRTC Specification. ### Exceptions - {{jsxref("TypeError")}} - : Thrown if the specified `candidateInfo` has values of `null` in **both** the `sdpMid` and `sdpMLineIndex` properties. ## Usage notes This constructor does not do complete validation of the specified `candidateInfo` object or string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Introduction to WebRTC protocols](/en-US/docs/Web/API/WebRTC_API/Protocols#ice) - [WebRTC connectivity](/en-US/docs/Web/API/WebRTC_API/Connectivity) - [Lifetime of a WebRTC session](/en-US/docs/Web/API/WebRTC_API/Session_lifetime) - [Signaling and video calling](/en-US/docs/Web/API/WebRTC_API/Signaling_and_video_calling)