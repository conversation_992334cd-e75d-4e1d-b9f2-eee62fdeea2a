Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetsreceived > index.md --- title: "RTCIceCandidatePairStats: packetsReceived property" short-title: packetsReceived slug: Web/API/RTCIceCandidatePairStats/packetsReceived page-type: web-api-instance-property status: - experimental browser-compat: api.RTCStatsReport.type_candidate-pair.packetsReceived --- {{APIRef("WebRTC")}}{{SeeCompatTable}} The **`packetsReceived`** property of the {{domxref("RTCIceCandidatePairStats")}} dictionary indicates the total number of packets received on the candidate pair. ### Value An integer value indicating the total number of packets received on the pair. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("RTCIceCandidatePairStats.bytesReceived")}}