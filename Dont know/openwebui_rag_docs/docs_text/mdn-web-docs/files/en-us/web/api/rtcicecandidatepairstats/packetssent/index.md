Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > packetssent > index.md --- title: "RTCIceCandidatePairStats: packetsSent property" short-title: packetsSent slug: Web/API/RTCIceCandidatePairStats/packetsSent page-type: web-api-instance-property status: - experimental browser-compat: api.RTCStatsReport.type_candidate-pair.packetsSent --- {{APIRef("WebRTC")}}{{SeeCompatTable}} The **`packetsSent`** property of the {{domxref("RTCIceCandidatePairStats")}} dictionary indicates the total number of packets sent on the candidate pair. ### Value An integer value indicating the total number of packets sent on the pair. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("RTCIceCandidatePairStats.bytesSent")}}