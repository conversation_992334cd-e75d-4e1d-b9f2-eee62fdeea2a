Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > remotecandidateid > index.md --- title: "RTCIceCandidatePairStats: remoteCandidateId property" short-title: remoteCandidateId slug: Web/API/RTCIceCandidatePairStats/remoteCandidateId page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_candidate-pair.remoteCandidateId --- {{APIRef("WebRTC")}} The **`remoteCandidateId`** property of the {{domxref("RTCIceCandidatePairStats")}} dictionary uniquely identifies the remote {{Glossary("ICE")}} candidate which was analyzed to generate the {{domxref("RTCIceCandidateStats")}} used to compute the statistics for this pair of candidates. ## Value A string uniquely identifying the remote {{Glossary("ICE")}} candidate that is, the candidate describing a configuration for the remote peer which is represented by the remote end of these statistics. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}