Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatepairstats > responsesreceived > index.md --- title: "RTCIceCandidatePairStats: responsesReceived property" short-title: responsesReceived slug: Web/API/RTCIceCandidatePairStats/responsesReceived page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_candidate-pair.responsesReceived --- {{APIRef("WebRTC")}} The **`responsesReceived`** property in the {{domxref("RTCIceCandidatePairStats")}} dictionary indicates the total number of {{Glossary("STUN")}} connectivity check responses that have been received on the connection described by this pair of candidates. ## Value An integer value which specifies the number of STUN connectivity request responses that have been received on the connection described by this pair of candidates so far. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}