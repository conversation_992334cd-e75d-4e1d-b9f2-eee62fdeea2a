Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > candidatetype > index.md --- title: "RTCIceCandidateStats: candidateType property" short-title: candidateType slug: Web/API/RTCIceCandidateStats/candidateType page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_local-candidate.candidateType --- {{APIRef("WebRTC")}} The {{domxref("RTCIceCandidateStats")}} interface's **`candidateType`** property is a string that indicates the type of {{Glossary("ICE")}} candidate the object represents. ## Value A string whose value is one of the strings found in [`RTCIceCandidate.type`](/en-US/docs/Web/API/RTCIceCandidate/type#value). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}