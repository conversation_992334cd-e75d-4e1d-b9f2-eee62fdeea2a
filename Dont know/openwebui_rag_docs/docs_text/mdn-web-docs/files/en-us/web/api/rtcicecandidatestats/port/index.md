Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md Path: mdn-web-docs > files > en-us > web > api > rtcicecandidatestats > port > index.md --- title: "RTCIceCandidateStats: port property" short-title: port slug: Web/API/RTCIceCandidateStats/port page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_local-candidate.port --- {{APIRef("WebRTC")}} The {{domxref("RTCIceCandidateStats")}} dictionary's **`port`** property specifies the network port used by the candidate. ## Value An integer value indicating the network port used by the {{domxref("RTCIceCandidate")}} described by the `RTCIceCandidateStats` object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}