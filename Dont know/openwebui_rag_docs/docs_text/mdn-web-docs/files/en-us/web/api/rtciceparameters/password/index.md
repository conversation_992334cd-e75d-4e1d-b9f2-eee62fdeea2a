Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md

Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md Path: mdn-web-docs > files > en-us > web > api > rtciceparameters > password > index.md --- title: "RTCIceParameters: password property" short-title: password slug: Web/API/RTCIceParameters/password page-type: web-api-instance-property spec-urls: https://w3c.github.io/webrtc-pc/#dom-rtciceparameters-password --- {{APIRef("WebRTC")}} The **{{domxref("RTCIceParameters")}}** dictionary's **`password`** property specifies the ICE password that, in tandem with the {{domxref("RTCIceParameters.usernameFragment", "usernameFragment")}}, uniquely identifies an ICE session for its entire duration. ## Value A string containing the password that corresponds to the transport's `usernameFragment` string ## Specifications {{Specifications}}