Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > framesdecoded > index.md --- title: "RTCInboundRtpStreamStats: framesDecoded property" short-title: framesDecoded slug: Web/API/RTCInboundRtpStreamStats/framesDecoded page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_inbound-rtp.framesDecoded --- {{APIRef("WebRTC")}} The **`framesDecoded`** property of the {{domxref("RTCInboundRtpStreamStats")}} dictionary indicates the total number of frames which have been decoded successfully for this media source. ## Value An integer value indicating the total number of video frames which have been decoded for this stream so far. This represents the number of frames that would have been displayed assuming no frames were skipped. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}