Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md Path: mdn-web-docs > files > en-us > web > api > rtcinboundrtpstreamstats > receiverid > index.md --- title: "RTCInboundRtpStreamStats: receiverId property" short-title: receiverId slug: Web/API/RTCInboundRtpStreamStats/receiverId page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_inbound-rtp.receiverId --- {{APIRef("WebRTC")}} The **`receiverId`** property of the {{domxref("RTCInboundRtpStreamStats")}} dictionary specifies the {{domxref("RTCInboundRtpStreamStats.id", "id")}} of the {{domxref("RTCAudioReceiverStats")}} or {{domxref("RTCVideoReceiverStats")}} object representing the {{domxref("RTCRtpReceiver")}} receiving the stream. ## Value A string which contains the ID of the `RTCAudioReceiverStats` or `RTCVideoReceiverStats` object which provides information about the `RTCRtpReceiver` which is receiving the streamed media. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}