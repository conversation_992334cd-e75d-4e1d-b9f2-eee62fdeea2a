Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > framesencoded > index.md --- title: "RTCOutboundRtpStreamStats: framesEncoded property" short-title: framesEncoded slug: Web/API/RTCOutboundRtpStreamStats/framesEncoded page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_outbound-rtp.framesEncoded --- {{APIRef("WebRTC")}} The **`framesEncoded`** property of the {{domxref("RTCOutboundRtpStreamStats")}} dictionary indicates the total number of frames that have been encoded by this {{domxref("RTCRtpSender")}} for this media source. ## Value An integer value indicating the total number of video frames that this sender has encoded so far for this stream. > [!NOTE] > This property is only valid for video streams. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}