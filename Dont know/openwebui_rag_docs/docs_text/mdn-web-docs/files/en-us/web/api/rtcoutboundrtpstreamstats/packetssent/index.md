Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md Path: mdn-web-docs > files > en-us > web > api > rtcoutboundrtpstreamstats > packetssent > index.md --- title: "RTCOutboundRtpStreamStats: packetsSent property" short-title: packetsSent slug: Web/API/RTCOutboundRtpStreamStats/packetsSent page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_outbound-rtp.packetsSent --- {{APIRef("WebRTC")}} The **`packetsSent`** property of the {{domxref("RTCOutboundRtpStreamStats")}} dictionary represents the total number of RTP packets sent on this stream, including retransmissions. ## Value A positive integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}