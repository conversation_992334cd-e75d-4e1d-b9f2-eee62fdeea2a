Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md Path: mdn-web-docs > files > en-us > web > api > rtcpeerconnectionstats > datachannelsopened > index.md --- title: "RTCPeerConnectionStats: dataChannelsOpened property" short-title: dataChannelsOpened slug: Web/API/RTCPeerConnectionStats/dataChannelsOpened page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_peer-connection.dataChannelsOpened --- {{APIRef("WebRTC")}} The **`dataChannelsOpened`** property of the {{domxref("RTCPeerConnectionStats")}} dictionary indicates the number of unique {{domxref("RTCDataChannel")}} objects that have entered the [`open`](/en-US/docs/Web/API/RTCDataChannel/readyState#open) state during their lifetime. ## Value A positive integer that indicates the number of unique {{domxref("RTCDataChannel")}} objects that have entered the [`open`](/en-US/docs/Web/API/RTCDataChannel/readyState#open) state during their lifetime. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}