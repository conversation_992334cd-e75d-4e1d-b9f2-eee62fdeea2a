Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md Path: mdn-web-docs > files > en-us > web > api > rtcremoteoutboundrtpstreamstats > index.md --- title: RTCRemoteOutboundRtpStreamStats slug: Web/API/RTCRemoteOutboundRtpStreamStats page-type: web-api-interface browser-compat: api.RTCStatsReport.type_remote-outbound-rtp --- {{APIRef("WebRTC")}} The **`RTCRemoteOutboundRtpStreamStats`** dictionary of the [WebRTC API](/en-US/docs/Web/API/WebRTC_API) is used to report statistics from a remote endpoint about its outbound RTP stream. This will correspond to an inbound stream that is currently being received by the local {{domxref("RTCPeerConnection")}} object. The statistics can be obtained by iterating the {{domxref("RTCStatsReport")}} returned by {{domxref("RTCPeerConnection.getStats()")}} until you find a report with the [`type`](/en-US/docs/Web/API/RTCRemoteOutboundRtpStreamStats/type) of `remote-outbound-rtp`. ## Instance properties ### Remote outbound specific statistics - {{domxref("RTCRemoteOutboundRtpStreamStats.localId", "localId")}} {{optional_inline}} - : A string which is used to find the local {{domxref("RTCInboundRtpStreamStats")}} object that shares the same [synchronization source (SSRC)](/en-US/docs/Web/API/RTCRemoteOutboundRtpStreamStats/ssrc). - {{domxref("RTCRemoteOutboundRtpStreamStats.remoteTimestamp", "remoteTimestamp")}} {{optional_inline}} - : A {{domxref("DOMHighResTimeStamp")}} specifying the timestamp (on the remote device) at which the statistics in the `RTCRemoteOutboundRtpStreamStats` object were sent by the remote endpoint. This is different from the {{domxref("RTCRemoteOutboundRtpStreamStats.timestamp", "timestamp")}}; it represents the time at which the object's statistics were received or generated by the local endpoint. - `reportsSent` {{optional_inline}} {{experimental_inline}} - : A positive integer indicating the total number of {{glossary("RTCP")}} Sender Report (SR) blocks sent for this [synchronization source (SSRC)](/en-US/docs/Web/API/RTCRemoteOutboundRtpStreamStats/ssrc). - {{domxref("RTCRemoteOutboundRtpStreamStats.roundTripTimeMeasurements", "roundTripTimeMeasurements")}} {{optional_inline}} {{experimental_inline}} - : A positive number that represents the total number of valid round trip time measurements received for this [synchronization source (SSRC)](/en-US/docs/Web/API/RTCRemoteOutboundRtpStreamStats/ssrc). - {{domxref("RTCRemoteOutboundRtpStreamStats.totalRoundTripTime", "totalRoundTripTime")}} {{optional_inline}} {{experimental_inline}} - : A number indicating the cumulative sum of all round trip time measurements since the beginning of the session, in seconds. The average round trip time can be computed by dividing `totalRoundTripTime` by [`roundTripTimeMeasurements`](/en-US/docs/Web/API/RTCRemoteOutboundRtpStreamStats/roundTripTimeMeasurements). ### Sent RTP stream statistics <!-- RTCSentRtpStreamStats --> - `bytesSent` {{optional_inline}} - : A positive integer indicating the total number of bytes sent for this SSRC, including retransmissions. <!-- [RFC3550] section 6.4.1 --> - `packetsSent` {{optional_inline}} - : A positive integer indicating the total number of RTP packets sent for this SSRC, including retransmissions. <!-- [RFC3550] section 6.4.1 --> ### Common RTP stream statistics <!-- RTCRtpStreamStats --> - {{domxref("RTCRemoteOutboundRtpStreamStats.codecId", "codecId")}} {{optional_inline}} - : A string that uniquely identifies the object that was inspected to produce the {{domxref("RTCCodecStats")}} report associated with this {{Glossary("RTP")}} stream. - {{domxref("RTCRemoteOutboundRtpStreamStats.kind", "kind")}} - : A string indicating whether the {{domxref("MediaStreamTrack")}} associated with the stream is an audio or a video track. - {{domxref("RTCRemoteOutboundRtpStreamStats.ssrc", "ssrc")}} - : A positive integer that identifies the synchronization source (SSRC) of the RTP packets in this stream. - {{domxref("RTCRemoteOutboundRtpStreamStats.transportId", "transportId")}} {{optional_inline}} - : A string that uniquely identifies the object that was inspected to produce the {{domxref("RTCTransportStats")}} report associated with this RTP stream. ### Common instance properties The following properties are common to all WebRTC statistics objects. <!-- RTCStats --> - {{domxref("RTCRemoteOutboundRtpStreamStats.id", "id")}} - : A string that uniquely identifies the object that is being monitored to produce this set of statistics. - {{domxref("RTCRemoteOutboundRtpStreamStats.timestamp", "timestamp")}} - : A {{domxref("DOMHighResTimeStamp")}} object indicating the time at which the sample was taken for this statistics object. - {{domxref("RTCRemoteOutboundRtpStreamStats.type", "type")}} - : A string with the value `"remote-outbound-rtp"`, indicating the type of statistics that the object contains. ## Usage notes The `RTCRemoteOutboundRtpStreamStats` object's {{domxref("RTCRemoteOutboundRtpStreamStats.remoteTimestamp", "remoteTimestamp")}} property provides statistics based on the received data's NTP timestamp taken from an {{Glossary("RTCP")}} Sender Report (SR) block. Be aware that the remote clock may not be synchronized with the local clock (either in current time or speed at which time elapses). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}