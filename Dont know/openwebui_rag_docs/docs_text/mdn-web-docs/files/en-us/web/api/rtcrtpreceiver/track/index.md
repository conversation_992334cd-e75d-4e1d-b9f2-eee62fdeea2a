Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpreceiver > track > index.md --- title: "RTCRtpReceiver: track property" short-title: track slug: Web/API/RTCRtpReceiver/track page-type: web-api-instance-property browser-compat: api.RTCRtpReceiver.track --- {{APIRef("WebRTC API")}} The **`track`** read-only property of the {{domxref("RTCRtpReceiver")}} interface returns the {{domxref("MediaStreamTrack")}} associated with the current {{domxref("RTCRtpReceiver")}} instance. ## Value A {{domxref("MediaStreamTrack")}} instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}