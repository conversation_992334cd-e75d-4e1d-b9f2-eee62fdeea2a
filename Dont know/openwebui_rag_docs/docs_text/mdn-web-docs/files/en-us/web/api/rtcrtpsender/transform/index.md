Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md Path: mdn-web-docs > files > en-us > web > api > rtcrtpsender > transform > index.md --- title: "RTCRtpSender: transform property" short-title: transform slug: Web/API/RTCRtpSender/transform page-type: web-api-instance-property browser-compat: api.RTCRtpSender.transform --- {{APIRef("WebRTC")}} The **`transform`** property of the {{domxref("RTCRtpSender")}} object is used to insert a transform stream ({{domxref("TransformStream")}}) running in a worker thread into the sender pipeline. This allows stream transforms to be applied to encoded video and audio frames after they are output by a codec, and before they are sent. The transform that is to be added is defined using an {{domxref("RTCRtpScriptTransform")}} and its associated {{domxref("Worker")}}. If the transform is set synchronously immediately after creating the `RTCRtpSender` it will receive the first full frame generated by the sender's encoder. ### Value A {{domxref("RTCRtpScriptTransform")}}<!-- or {{domxref("SFrameTransform")}} -->, or `null` if the sender has no associated transform stream. ## Example This example shows how you might stream video from a user's webcam over WebRTC, adding a WebRTC encoded transform to modify the outgoing streams. Note that this is part of a larger example in the guide topic [Using WebRTC Encoded Transforms](/en-US/docs/Web/API/WebRTC_API/Using_Encoded_Transforms). The code assumes that there is an {{domxref("RTCPeerConnection")}} called `peerConnection` that is already connected to a remote peer. It first gets a {{domxref("MediaStreamTrack")}}, using {{domxref("MediaDevices/getUserMedia", "getUserMedia()")}} to get a video {{domxref("MediaStream")}} from a media device, and then the {{domxref("MediaStream.getTracks()")}} method to get the first {{domxref("MediaStreamTrack")}} in the stream. The track is added to the peer connection using {{domxref("RTCPeerConnection/addTrack()", "addTrack()")}}. This returns a new {{domxref("RTCRtpSender")}} that will be used to send it. ```js const mediaStream = await navigator.mediaDevices.getUserMedia({ video: true }); const [track] = mediaStream.getTracks(); const videoSender = peerConnection.addTrack(track, mediaStream); ``` The code above sets up the connection and starts sending the track. To add a transform stream into the pipeline we need to construct an {{domxref("RTCRtpScriptTransform")}} and assign it to the sender's `transform` property. As the transform is constructed immediately after creation of the {{domxref("RTCRtpSender")}}, it will receive the first frame generated by the sender's encoder, before it is sent. ```js const worker = new Worker("worker.js"); videoSender.transform = new RTCRtpScriptTransform(worker, { name: "senderTransform", }); ``` Note that you can add the transform at any time. However by adding it immediately after calling `addTrack()` the transform will get the first encoded frame that is sent. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using WebRTC Encoded Transforms](/en-US/docs/Web/API/WebRTC_API/Using_Encoded_Transforms) - {{domxref("RTCRtpReceiver.transform")}}