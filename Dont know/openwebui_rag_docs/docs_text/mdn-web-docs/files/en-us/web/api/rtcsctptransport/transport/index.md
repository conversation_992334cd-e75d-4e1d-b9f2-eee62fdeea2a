Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md Path: mdn-web-docs > files > en-us > web > api > rtcsctptransport > transport > index.md --- title: "RTCSctpTransport: transport property" short-title: transport slug: Web/API/RTCSctpTransport/transport page-type: web-api-instance-property browser-compat: api.RTCSctpTransport.transport --- {{APIRef("WebRTC")}} The **`transport`** read-only property of the {{DOMxRef("RTCSctpTransport")}} interface returns a {{DOMxRef("RTCDtlsTransport")}} object representing the {{Glossary("DTLS")}} transport used for the transmission and receipt of data packets. ## Value A {{DOMxRef("RTCDtlsTransport")}} object representing the {{Glossary("DTLS")}} transport used for the transmission and receipt of data packets. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{DOMxRef("RTCSctpTransport")}}