Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md

Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md Path: mdn-web-docs > files > en-us > web > api > rtctrackevent > track > index.md --- title: "RTCTrackEvent: track property" short-title: track slug: Web/API/RTCTrackEvent/track page-type: web-api-instance-property browser-compat: api.RTCTrackEvent.track --- {{APIRef("WebRTC")}} The [WebRTC API](/en-US/docs/Web/API/WebRTC_API) interface {{domxref("RTCTrackEvent")}}'s read-only **`track`** property specifies the {{domxref("MediaStreamTrack")}} that has been added to the {{domxref("RTCPeerConnection")}}. ## Value A {{domxref("MediaStreamTrack")}} indicating the track which has been added to the {{domxref("RTCPeerConnection")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}