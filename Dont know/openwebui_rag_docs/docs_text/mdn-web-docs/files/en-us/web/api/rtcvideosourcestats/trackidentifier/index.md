Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > trackidentifier > index.md --- title: "RTCVideoSourceStats: trackIdentifier property" short-title: trackIdentifier slug: Web/API/RTCVideoSourceStats/trackIdentifier page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_media-source.trackIdentifier --- {{APIRef("WebRTC")}} The **`trackIdentifier`** property of the {{domxref("RTCVideoSourceStats")}} dictionary contains the `id` attribute of the associated [`MediaStreamTrack`](/en-US/docs/Web/API/MediaStreamTrack). ## Value A string containing the value of the associated [`MediaStreamTrack.id`](/en-US/docs/Web/API/MediaStreamTrack/id). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}