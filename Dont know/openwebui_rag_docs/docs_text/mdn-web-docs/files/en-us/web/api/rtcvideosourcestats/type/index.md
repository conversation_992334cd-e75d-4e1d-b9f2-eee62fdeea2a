Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md

Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md Path: mdn-web-docs > files > en-us > web > api > rtcvideosourcestats > type > index.md --- title: "RTCVideoSourceStats: type property" short-title: type slug: Web/API/RTCVideoSourceStats/type page-type: web-api-instance-property browser-compat: api.RTCStatsReport.type_media-source.type --- {{APIRef("WebRTC")}} The **`type`** property of the {{domxref("RTCVideoSourceStats")}} dictionary is a string with value `media-source`. The type of `media-source` identifies the type of statistics as either {{domxref("RTCAudioSourceStats")}} or {{domxref("RTCVideoSourceStats")}} when iterating the {{domxref("RTCStatsReport")}} returned by {{domxref("RTCRtpSender.getStats()")}} or {{domxref("RTCPeerConnection.getStats()")}}. The type of stats can further be differentiated using the {{domxref("RTCVideoSourceStats.kind", "kind")}}, which will be `video` for `RTCVideoSourceStats`. ## Value A string with the value `media-source`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}