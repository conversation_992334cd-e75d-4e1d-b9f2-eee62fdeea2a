Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md

Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md Path: mdn-web-docs > files > en-us > web > api > screen > availwidth > index.md --- title: "Screen: availWidth property" short-title: availWidth slug: Web/API/Screen/availWidth page-type: web-api-instance-property browser-compat: api.Screen.availWidth --- {{APIRef("CSSOM View")}} The **`Screen.availWidth`** property returns the amount of horizontal space (in CSS pixels) available to the window. ## Value A number. ## Examples ```js const screenAvailWidth = window.screen.availWidth; console.log(screenAvailWidth); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}