Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md

Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md Path: mdn-web-docs > files > en-us > web > api > screen > mozenabled > index.md --- title: "Screen: mozEnabled property" short-title: mozEnabled slug: Web/API/Screen/mozEnabled page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.Screen.mozEnabled --- {{APIRef("CSSOM")}}{{Deprecated_Header}}{{Non-standard_Header}} This Boolean attribute controls the device's screen. Setting it to `false` will turn off the screen. ## Value A boolean. ## Specifications Not part of specification. ## Browser compatibility {{Compat}}