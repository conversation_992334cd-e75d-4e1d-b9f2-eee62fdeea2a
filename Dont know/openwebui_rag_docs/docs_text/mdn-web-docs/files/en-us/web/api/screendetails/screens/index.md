Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md

Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md Path: mdn-web-docs > files > en-us > web > api > screendetails > screens > index.md --- title: "ScreenDetails: screens property" short-title: screens slug: Web/API/ScreenDetails/screens page-type: web-api-instance-property status: - experimental browser-compat: api.ScreenDetails.screens --- {{APIRef("Window Management API")}}{{SeeCompatTable}}{{SecureContext_Header}} The **`screens`** read-only property of the {{domxref("ScreenDetails")}} interface contains an array of {{domxref("ScreenDetailed")}} objects, each one representing detailed information about one specific screen available to the user's device. ## Value An array of {{domxref("ScreenDetailed")}} objects. > **Note:** `screens` only includes "extended" displays, not those that mirror another display. ## Examples See the main [`ScreenDetails`](/en-US/docs/Web/API/ScreenDetails#examples) page for example usage. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Window Management API](/en-US/docs/Web/API/Window_Management_API)