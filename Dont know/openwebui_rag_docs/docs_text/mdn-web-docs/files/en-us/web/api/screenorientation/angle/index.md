Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md

Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > angle > index.md --- title: "ScreenOrientation: angle property" short-title: angle slug: Web/API/ScreenOrientation/angle page-type: web-api-instance-property browser-compat: api.ScreenOrientation.angle --- {{APIRef("Screen Orientation")}} The **`angle`** read-only property of the {{domxref("ScreenOrientation")}} interface returns the document's current orientation angle. ## Value An unsigned short integer. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}