Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md

Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md Path: mdn-web-docs > files > en-us > web > api > screenorientation > type > index.md --- title: "ScreenOrientation: type property" short-title: type slug: Web/API/ScreenOrientation/type page-type: web-api-instance-property browser-compat: api.ScreenOrientation.type --- {{APIRef("Screen Orientation")}} The **`type`** read-only property of the {{domxref("ScreenOrientation")}} interface returns the document's current orientation type, one of `portrait-primary`, `portrait-secondary`, `landscape-primary`, or `landscape-secondary`. ## Value A {{jsxref("String")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}