Path: mdn-web-docs > files > en-us > web > api > scriptprocessornode > buffersize > index.md

Path: mdn-web-docs > files > en-us > web > api > scriptprocessornode > buffersize > index.md Path: mdn-web-docs > files > en-us > web > api > scriptprocessornode > buffersize > index.md Path: mdn-web-docs > files > en-us > web > api > scriptprocessornode > buffersize > index.md Path: mdn-web-docs > files > en-us > web > api > scriptprocessornode > buffersize > index.md --- title: "ScriptProcessorNode: bufferSize property" short-title: bufferSize slug: Web/API/ScriptProcessorNode/bufferSize page-type: web-api-instance-property status: - deprecated browser-compat: api.ScriptProcessorNode.bufferSize --- {{APIRef("Web Audio API")}}{{Deprecated_Header}} The `bufferSize` property of the {{domxref("ScriptProcessorNode")}} interface returns an integer representing both the input and output buffer size, in sample-frames. Its value can be a power of 2 value in the range `256` `16384`. > [!NOTE] > This feature was replaced by [AudioWorklets](/en-US/docs/Web/API/AudioWorklet) and the {{domxref("AudioWorkletNode")}} interface. ## Value An integer. ## Examples See [`BaseAudioContext.createScriptProcessor()`](/en-US/docs/Web/API/BaseAudioContext/createScriptProcessor#examples) for example code. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using the Web Audio API](/en-US/docs/Web/API/Web_Audio_API/Using_Web_Audio_API)