Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md

Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md Path: mdn-web-docs > files > en-us > web > api > scrolltimeline > source > index.md --- title: "ScrollTimeline: source property" short-title: source slug: Web/API/ScrollTimeline/source page-type: web-api-instance-property status: - experimental browser-compat: api.ScrollTimeline.source --- {{APIRef("Web Animations")}}{{SeeCompatTable}} The **`source`** read-only property of the {{domxref("ScrollTimeline")}} interface returns a reference to the scrollable element (_scroller_) whose scroll position is driving the progress of the timeline and therefore the animation. ## Value An {{domxref("Element")}}. ## Examples See the main {{domxref("ScrollTimeline")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - [CSS scroll-driven animations](/en-US/docs/Web/CSS/CSS_scroll-driven_animations) - {{domxref("ScrollTimeline")}} - {{domxref("AnimationTimeline")}}, {{domxref("ViewTimeline")}}