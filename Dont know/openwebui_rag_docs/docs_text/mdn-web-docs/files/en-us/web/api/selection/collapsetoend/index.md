Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md

Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetoend > index.md --- title: "Selection: collapseToEnd() method" short-title: collapseToEnd() slug: Web/API/Selection/collapseToEnd page-type: web-api-instance-method browser-compat: api.Selection.collapseToEnd --- {{ ApiRef("DOM") }} The **`Selection.collapseToEnd()`** method collapses the selection to the end of the last range in the selection. If the content of the selection is focused and editable, the caret will blink there. ## Syntax ```js-nolint collapseToEnd() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Selection")}}, the interface it belongs to.