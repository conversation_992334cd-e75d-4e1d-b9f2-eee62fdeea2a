Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md

Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md Path: mdn-web-docs > files > en-us > web > api > selection > collapsetostart > index.md --- title: "Selection: collapseToStart() method" short-title: collapseToStart() slug: Web/API/Selection/collapseToStart page-type: web-api-instance-method browser-compat: api.Selection.collapseToStart --- {{ ApiRef("DOM") }} The **`Selection.collapseToStart()`** method collapses the selection to the start of the first range in the selection. If the content of the selection is focused and editable, the caret will blink there. ## Syntax ```js-nolint collapseToStart() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Selection")}}, the interface it belongs to.