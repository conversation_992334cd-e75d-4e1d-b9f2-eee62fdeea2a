Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md

Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md Path: mdn-web-docs > files > en-us > web > api > selection > removerange > index.md --- title: "Selection: removeRange() method" short-title: removeRange() slug: Web/API/Selection/removeRange page-type: web-api-instance-method browser-compat: api.Selection.removeRange --- {{ ApiRef("DOM") }} The **`Selection.removeRange()`** method removes a range from a selection. ## Syntax ```js-nolint removeRange(range) ``` ### Parameters - `range` - : A range object that will be removed from the selection. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js /* Programmatically, more than one range can be selected. * This will remove all ranges except the first. */ const s = window.getSelection(); if (s.rangeCount > 1) { for (let i = 1; i < s.rangeCount; i++) { s.removeRange(s.getRangeAt(i)); } } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Selection")}}, the interface it belongs to.