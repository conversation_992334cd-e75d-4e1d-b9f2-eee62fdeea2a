Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md

Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > activated > index.md --- title: "Sensor: activated property" short-title: activated slug: Web/API/Sensor/activated page-type: web-api-instance-property browser-compat: api.Sensor.activated --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`activated`** read-only property of the {{domxref("Sensor")}} interface returns a boolean value indicating whether the sensor is active. Because {{domxref('Sensor')}} is a base class, `activated` may only be read from one of its derived classes. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}