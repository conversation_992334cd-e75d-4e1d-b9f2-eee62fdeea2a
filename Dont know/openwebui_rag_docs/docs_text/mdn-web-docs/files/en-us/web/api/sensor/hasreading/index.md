Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md

Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > hasreading > index.md --- title: "Sensor: hasReading property" short-title: hasReading slug: Web/API/Sensor/hasReading page-type: web-api-instance-property browser-compat: api.Sensor.hasReading --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`hasReading`** read-only property of the {{domxref("Sensor")}} interface returns a boolean value indicating whether the sensor has a reading. Because {{domxref('Sensor')}} is a base class, `hasReading` may only be read from one of its derived classes. ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}