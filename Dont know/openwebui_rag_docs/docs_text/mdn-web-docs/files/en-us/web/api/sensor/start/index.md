Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md

Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > start > index.md --- title: "Sensor: start() method" short-title: start() slug: Web/API/Sensor/start page-type: web-api-instance-method browser-compat: api.Sensor.start --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`start()`** method of the {{domxref("Sensor")}} interface activates one of the sensors based on `Sensor`. ## Syntax ```js-nolint start() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}