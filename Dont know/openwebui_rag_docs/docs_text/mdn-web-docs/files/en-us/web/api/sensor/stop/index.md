Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md

Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > stop > index.md --- title: "Sensor: stop() method" short-title: stop() slug: Web/API/Sensor/stop page-type: web-api-instance-method browser-compat: api.Sensor.stop --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`stop()`** method of the {{domxref("Sensor")}} interface deactivates the current sensor. ## Syntax ```js-nolint stop() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js // TBD ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}