Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > sensor > timestamp > index.md --- title: "Sensor: timestamp property" short-title: timestamp slug: Web/API/Sensor/timestamp page-type: web-api-instance-property browser-compat: api.Sensor.timestamp --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`timestamp`** read-only property of the {{domxref("Sensor")}} interface returns the timestamp of the latest sensor reading. Because {{domxref('Sensor')}} is a base class, `timestamp` may only be read from one of its derived classes. ## Value A {{domxref("DOMHighResTimeStamp")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}