Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md

Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > error > index.md --- title: "SensorErrorEvent: error property" short-title: error slug: Web/API/SensorErrorEvent/error page-type: web-api-instance-property browser-compat: api.SensorErrorEvent.error --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`error`** read-only property of the {{domxref("SensorErrorEvent")}} interface returns the {{domxref('DOMException')}} object passed in the event's constructor. ## Value A {{domxref('DOMException')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}