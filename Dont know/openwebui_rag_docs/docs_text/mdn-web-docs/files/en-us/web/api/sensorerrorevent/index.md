Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md

Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md Path: mdn-web-docs > files > en-us > web > api > sensorerrorevent > index.md --- title: SensorErrorEvent slug: Web/API/SensorErrorEvent page-type: web-api-interface browser-compat: api.SensorErrorEvent --- {{securecontext_header}}{{APIRef("Sensor API")}} The **`SensorErrorEvent`** interface of the [Sensor APIs](/en-US/docs/Web/API/Sensor_APIs) provides information about errors thrown by a {{domxref('Sensor')}} or derived interface. {{InheritanceDiagram}} ## Constructor - {{domxref("SensorErrorEvent.SensorErrorEvent", "SensorErrorEvent()")}} - : Creates a new `SensorErrorEvent` object. ## Instance properties - {{domxref('SensorErrorEvent.error')}} {{ReadOnlyInline}} - : Returns the {{domxref('DOMException')}} object passed in the event's constructor. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}