<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="1130"
   height="1518"
   viewBox="-10.5 -51 1232.555 1655.7686"
   version="1.1"
   id="svg434"
   sodipodi:docname="sw-lifecycle.svg"
   inkscape:version="1.2.1 (9c6d41e410, 2022-07-14)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns:xhtml="http://www.w3.org/1999/xhtml">
  <defs
     id="defs438" />
  <sodipodi:namedview
     id="namedview436"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     showgrid="false"
     inkscape:zoom="1.1264822"
     inkscape:cx="549.05439"
     inkscape:cy="908.58071"
     inkscape:window-width="3200"
     inkscape:window-height="1771"
     inkscape:window-x="-9"
     inkscape:window-y="-9"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg434"
     showguides="true">
    <sodipodi:guide
       position="262.40557,1039.9399"
       orientation="1,0"
       id="guide1320"
       inkscape:locked="false" />
  </sodipodi:namedview>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g6">
    <text
       x="1005.5"
       y="361.5"
       id="text2">Service worker fetched</text>
    <text
       x="1005.5"
       y="378.5"
       id="text4">and registered</text>
  </g>
  <path
     d="M 961,344 V 224 h 60.46 l 26.14,30.48 V 344 Z m 4.9,-5.69 h 76.8 V 260.2 h -24.51 V 229.72 H 965.9 Z"
     pointer-events="none"
     id="path8" />
  <path
     d="m 978.11,332.5 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path10" />
  <path
     d="m 978.11,332.5 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path12" />
  <path
     d="m 1004.36,311.42 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path14" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch18">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:254px;margin-left:962px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="991"
       y="260"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text16">v1</text>
  </switch>
  <path
     d="m 241,14.4 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 L 338.5,22.4 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 H 253.25 C 246.63,109.96 241.39,104.16 241,96.95 Z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,83.5 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path20" />
  <path
     d="m 371,14.4 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 L 468.5,22.4 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 H 383.25 C 376.63,109.96 371.39,104.16 371,96.95 Z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,83.5 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path22" />
  <path
     d="M 445.35,120.5 421,96.13 l 9.74,-9.7 12.48,12.52 C 449.14,93 455.24,87.03 463.04,80.94 470.95,74.8 479.39,70.5 483.5,70.7 c -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path24" />
  <path
     d="m 501,14.4 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 L 598.5,22.4 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 H 513.25 C 506.63,109.96 501.39,104.16 501,96.95 Z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,83.5 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path26" />
  <path
     d="M 575.35,120.5 551,96.13 l 9.74,-9.7 12.48,12.52 C 579.14,93 585.24,87.03 593.04,80.94 600.95,74.8 609.39,70.5 613.5,70.7 c -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path28" />
  <path
     d="m 371,129 v 5 q 0,5 10,5 h 95 q 10,0 10,5 v 2.5 -5 q 0,-2.5 10,-2.5 h 95 q 10,0 10,-5 v -5"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path30" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch34">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:202px;height:1px;padding-top:164px;margin-left:385px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">Pages loaded in the browser (client)</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="486"
       y="168"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text32">Pages loaded in the browser (client)</text>
  </switch>
  <path
     d="m 241,242.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,312 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path36" />
  <path
     d="m 371,242.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,312 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path38" />
  <path
     d="M 445.35,349 421,324.63 l 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path40" />
  <path
     d="m 501,242.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,312 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path42" />
  <path
     d="M 575.35,349 551,324.63 l 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path44" />
  <path
     d="m 241,359 v 5 q 0,5 10,5 h 160 q 10,0 10,5 v 2.5 -5 q 0,-2.5 10,-2.5 h 160 q 10,0 10,-5 v -5"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path46" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch50">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:202px;height:1px;padding-top:394px;margin-left:325px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">Scope of pages for the service worker</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="426"
       y="398"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text48">Scope of pages for the service worker</text>
  </switch>
  <path
     d="M 598.5,284 H 954.63"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path52" />
  <path
     d="m 959.88,284 -7,3.5 1.75,-3.5 -1.75,-3.5 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path54" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch60">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:1px;height:1px;padding-top:259px;margin-left:782px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0); background-color: rgb(255 255 255);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;background-color:#fff;white-space:nowrap">
            <xhtml:font
               face="Courier New">register(workerURL,{options = scope})</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="782"
       y="263"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text58"><tspan
         style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-family:'Courier New';-inkscape-font-specification:'Courier New'"
         id="tspan56">register(workerURL,{options = scope})</tspan></text>
  </switch>
  <path
     d="m 426.01,409 q 0.05,10 232.52,10 Q 891,419 891,269"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     stroke-dasharray="3, 3"
     pointer-events="none"
     id="path62" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch70">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:49px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">0. Initial situation, no service worker<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="53"
       font-family="Helvetica"
       font-size="14"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text68"><tspan
         sodipodi:role="line"
         id="tspan1292"
         x="96"
         y="53">0. Initial situation:</tspan><tspan
         sodipodi:role="line"
         id="tspan1294"
         x="96"
         y="70.5">no service worker</tspan></text>
  </switch>
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch80">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:284px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:div>
              <xhtml:font
                 style="font-size:19px">1. Registration of the</xhtml:font>
            </xhtml:div>
            <xhtml:div>
              <xhtml:font
                 style="font-size:19px">first version of a service worker<xhtml:br />
</xhtml:font>
            </xhtml:div>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="288"
       font-family="Helvetica"
       font-size="14"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text78"><tspan
         sodipodi:role="line"
         id="tspan1310"
         x="96"
         y="288">1. Registration of the</tspan><tspan
         sodipodi:role="line"
         id="tspan1312"
         x="96"
         y="305.5">first version of a</tspan><tspan
         sodipodi:role="line"
         id="tspan1314"
         x="96"
         y="323">service worker</tspan></text>
  </switch>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g86">
    <text
       x="725.5"
       y="636.5"
       id="text82">Service worker fetched</text>
    <text
       x="725.5"
       y="653.5"
       id="text84">and registered</text>
  </g>
  <path
     d="M 681,619 V 499 h 60.46 l 26.14,30.48 V 619 Z m 4.9,-5.69 h 76.8 V 535.2 H 738.19 V 504.72 H 685.9 Z"
     pointer-events="none"
     id="path88" />
  <path
     d="m 698.11,607.5 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path90" />
  <path
     d="m 698.11,607.5 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path92" />
  <path
     d="m 724.36,586.42 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path94" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch98">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:524px;margin-left:682px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="711"
       y="530"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text96">v1</text>
  </switch>
  <path
     d="m 241,517.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,587 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 371,517.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,587 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path100" />
  <path
     d="M 445.35,624 421,599.63 l 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path102" />
  <path
     d="m 501,517.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,587 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path104" />
  <path
     d="M 325.35,624 301,599.63 l 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path106" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch110">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:559px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">2. Installation<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="563"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text108">2. Installation</text>
  </switch>
  <path
     d="M 901,504.12 773.68,543.66"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path112" />
  <path
     d="m 768.67,545.22 5.64,-5.42 -0.63,3.86 2.71,2.83 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path114" />
  <path
     d="m 901,529 11,-12.57 -9.35,-8.79 9.35,-7.54 -9.35,-11.31 17.05,-19.79 14.3,16.02 -13.2,8.8 7.7,8.79 -11.55,7.54 7.15,6.91 z"
     fill="#ffd966"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="6"
     pointer-events="none"
     id="path116" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch122">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe flex-start;justify-content:unsafe center;width:1px;height:1px;padding-top:536px;margin-left:918px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:nowrap">i<xhtml:font
   face="Courier New">nstall</xhtml:font>


event</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="918"
       y="555"
       font-family="Helvetica"
       font-size="19px"
       text-anchor="middle"
       id="text120"
       style="word-spacing:3px"><tspan
   style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-family:'Courier New';-inkscape-font-specification:'Courier New'"
   id="tspan118">install</tspan> event</text>
  </switch>
  <path
     d="m 937.5,621.5 97.99,-56.89"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path124" />
  <path
     d="m 1040.03,561.98 -4.29,6.54 -0.25,-3.91 -3.27,-2.15 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path126" />
  <ellipse
     cx="922.22998"
     cy="620.53003"
     rx="15.271"
     ry="15.461"
     pointer-events="none"
     id="ellipse128" />
  <path
     d="m 926.11,643.11 -5.82,5.89 v -3.93 c -8.82,-0.33 -16.58,-6 -19.68,-14.36 -3.11,-8.36 -0.96,-17.8 5.46,-23.94 6.42,-6.13 15.84,-7.77 23.92,-4.15 -7.17,-2.98 -15.39,-1.45 -21.05,3.9 -5.66,5.36 -7.73,13.56 -5.29,21 2.44,7.44 8.94,12.76 16.64,13.63 v -3.93 z"
     pointer-events="none"
     id="path130" />
  <ellipse
     cx="922.32001"
     cy="607.42999"
     rx="1.483"
     ry="1.257"
     fill="#ffffff"
     pointer-events="none"
     id="ellipse132" />
  <ellipse
     cx="909.46997"
     cy="621.27002"
     rx="1.546"
     ry="1.472"
     fill="#ffffff"
     pointer-events="none"
     id="ellipse134" />
  <ellipse
     cx="922.67999"
     cy="634.03003"
     rx="1.668"
     ry="1.35"
     fill="#ffffff"
     pointer-events="none"
     id="ellipse136" />
  <ellipse
     cx="935.44"
     cy="621.20001"
     rx="1.454"
     ry="1.35"
     fill="#ffffff"
     pointer-events="none"
     id="ellipse138" />
  <path
     d="m 919.56,622 v -9.32 a 2.822,2.822 0 0 1 2.67,-1.94 c 1.21,0 2.28,0.78 2.67,1.94 v 6.38 l 5.09,5.15 c 0.74,1.14 0.61,2.65 -0.32,3.64 a 2.89,2.89 0 0 1 -3.56,0.54 z"
     fill="#ffffff"
     pointer-events="none"
     id="path140" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch144">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe flex-start;justify-content:unsafe center;width:1px;height:1px;padding-top:656px;margin-left:918px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:nowrap">event.waitUntil()</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="918"
       y="675"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text142">event.waitUntil()</text>
  </switch>
  <path
     d="m 767.6,574.85 125.52,45.96"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path146" />
  <path
     d="m 898.05,622.62 -7.78,0.88 2.85,-2.69 -0.44,-3.89 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path148" />
  <path
     d="m 1041,524 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path150" />
  <path
     d="m 1041,529 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path152" />
  <path
     d="m 1041,534 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path154" />
  <path
     d="m 1041,524 c 0,-13.33 60,-13.33 60,0 v 40 c 0,13.33 -60,13.33 -60,0 z"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path156" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch160">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:202px;height:1px;padding-top:614px;margin-left:970px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">Setting up caches, offline assets, etc.</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="618"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text158">Setting up caches, offline assets</text>
  </switch>
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch164">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:554px;margin-left:1042px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="560"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text162">v1</text>
  </switch>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g170">
    <text
       x="725.5"
       y="849.5"
       id="text166">Service worker installed</text>
    <text
       x="725.5"
       y="866.5"
       id="text168">but not controlling</text>
  </g>
  <path
     d="M 681,829 V 709 h 60.46 l 26.14,30.48 V 829 Z m 4.9,-5.69 h 76.8 V 745.2 H 738.19 V 714.72 H 685.9 Z"
     pointer-events="none"
     id="path172" />
  <path
     d="m 698.11,817.5 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path174" />
  <path
     d="m 698.11,817.5 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path176" />
  <path
     d="m 724.36,796.42 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path178" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch182">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:734px;margin-left:682px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="711"
       y="740"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text180">v1</text>
  </switch>
  <path
     d="m 241,727.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,797 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 371,727.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,797 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 501,727.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,797 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path184" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch192">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:769px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">3. Waiting for clients to be closed<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="773"
       font-family="Helvetica"
       font-size="14"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text190"><tspan
         sodipodi:role="line"
         id="tspan1306"
         x="96"
         y="773">3. Waiting for clients</tspan><tspan
         sodipodi:role="line"
         id="tspan1308"
         x="96"
         y="790.5">to be closed</tspan></text>
  </switch>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g198">
    <text
       x="725.5"
       y="1059.5"
       id="text194">Service worker installed</text>
    <text
       x="725.5"
       y="1076.5"
       id="text196">but not controlling</text>
  </g>
  <path
     d="M 681,1039 V 919 h 60.46 l 26.14,30.48 V 1039 Z m 4.9,-5.69 h 76.8 V 955.2 H 738.19 V 924.72 H 685.9 Z"
     pointer-events="none"
     id="path200" />
  <path
     d="m 698.11,1027.5 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path202" />
  <path
     d="m 698.11,1027.5 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path204" />
  <path
     d="m 724.36,1006.42 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path206" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch210">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:944px;margin-left:682px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="711"
       y="950"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text208">v1</text>
  </switch>
  <path
     d="m 241,937.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,1007 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 371,937.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,1007 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 501,937.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,1007 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path212" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch216">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:979px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">4. Activation<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="983"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text214">4. Activation</text>
  </switch>
  <path
     d="M 901,937.84 773.8,967.47"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path218" />
  <path
     d="m 768.69,968.66 6.02,-5 -0.91,3.81 2.5,3.01 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path220" />
  <path
     d="m 901,964 11,-12.57 -9.35,-8.79 9.35,-7.54 -9.35,-11.31 17.05,-19.79 14.3,16.02 -13.2,8.8 7.7,8.79 -11.55,7.54 7.15,6.91 z"
     fill="#ffd966"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="6"
     pointer-events="none"
     id="path222" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch228">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe flex-start;justify-content:unsafe center;width:1px;height:1px;padding-top:971px;margin-left:918px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:nowrap"><xhtml:font
   face="Courier New">activate</xhtml:font>


event</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="918"
       y="990"
       font-family="Helvetica"
       font-size="19px"
       text-anchor="middle"
       id="text226"
       style="word-spacing:6px"><tspan
   style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-family:'Courier New';-inkscape-font-specification:'Courier New'"
   id="tspan224">activate</tspan> event</text>
  </switch>
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch236">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:202px;height:1px;padding-top:1024px;margin-left:817px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">Finishing setup, cleaning old resources for previous versions</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="918"
       y="1028"
       font-family="Helvetica"
       font-size="14"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text234"><tspan
         sodipodi:role="line"
         id="tspan1322"
         x="918"
         y="1028">Finishing setup, cleaning</tspan><tspan
         sodipodi:role="line"
         id="tspan1324"
         x="918"
         y="1045.5">old resources for previous versions</tspan></text>
  </switch>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g244">
    <text
       x="855.5"
       y="1264.5"
       id="text238">Service worker</text>
    <text
       x="855.5"
       y="1281.5"
       id="text240">controlling documents</text>
    <text
       x="855.5"
       y="1298.5"
       id="text242">in its scope</text>
  </g>
  <path
     d="m 811,1234 v -120 h 60.46 l 26.14,30.48 V 1234 Z m 4.9,-5.69 h 76.8 v -78.11 h -24.51 v -30.48 H 815.9 Z"
     pointer-events="none"
     id="path246" />
  <path
     d="m 828.11,1222.5 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path248" />
  <path
     d="m 828.11,1222.5 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path250" />
  <path
     d="m 854.36,1201.42 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path252" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch256">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1139px;margin-left:812px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="841"
       y="1145"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text254">v1</text>
  </switch>
  <path
     d="m 241,1132.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 314 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 283.98,1202 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z M 371,1132.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 444 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 413.98,1202 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path258" />
  <path
     d="m 501,1132.9 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 H 574 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 L 543.98,1202 Z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path260" />
  <path
     d="m 575.35,1239 -24.35,-24.37 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path262" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch266">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:1174px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">5. Activated<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="1178"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text264">5. Activated</text>
  </switch>
  <path
     d="m 731.58,1175.2 73.05,-0.71"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path268" />
  <path
     d="m 809.88,1174.43 -6.96,3.57 1.71,-3.51 -1.78,-3.49 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path270" />
  <path
     d="m 709.5,1189 11,-12.57 -9.35,-8.79 9.35,-7.54 -9.35,-11.31 17.05,-19.79 14.3,16.02 -13.2,8.8 7.7,8.79 -11.55,7.54 7.15,6.91 z"
     fill="#ffd966"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="6"
     pointer-events="none"
     id="path272" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch282">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe flex-start;justify-content:unsafe center;width:1px;height:1px;padding-top:1196px;margin-left:726px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:nowrap">
            <xhtml:div
               style="font-size:14px">
              <xhtml:font
                 style="font-size:14px">functional events</xhtml:font>
            </xhtml:div>
            <xhtml:div
               style="font-size:14px">
              <xhtml:font
                 style="font-size:14px">(e.g.<xhtml:font
   style="font-size:14px"
   face="Courier New">fetch</xhtml:font>


)<xhtml:br
   style="font-size:14px" />
</xhtml:font>
            </xhtml:div>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="726"
       y="1210"
       font-family="Helvetica"
       font-size="14"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text280"><tspan
         x="726"
         y="1210"
         id="tspan274">functional events</tspan><tspan
         x="726"
         y="1227.5"
         id="tspan278">(e.g. <tspan
   style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-family:'Courier New';-inkscape-font-specification:'Courier New'"
   id="tspan276">fetch</tspan>)</tspan></text>
  </switch>
  <path
     d="m 1041,749 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path284" />
  <path
     d="m 1041,754 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path286" />
  <path
     d="m 1041,759 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path288" />
  <path
     d="m 1041,749 c 0,-13.33 60,-13.33 60,0 v 40 c 0,13.33 -60,13.33 -60,0 z"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path290" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch294">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:816px;margin-left:1042px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:14px">Cache</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="821"
       font-family="Helvetica"
       font-size="19px"
       text-anchor="middle"
       id="text292">Cache</text>
  </switch>
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch298">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:779px;margin-left:1042px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="785"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text296">v1</text>
  </switch>
  <path
     d="m 1041,974 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path300" />
  <path
     d="m 1041,979 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path302" />
  <path
     d="m 1041,984 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path304" />
  <path
     d="m 1041,974 c 0,-13.33 60,-13.33 60,0 v 40 c 0,13.33 -60,13.33 -60,0 z"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path306" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch310">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1004px;margin-left:1042px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="1010"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text308">v1</text>
  </switch>
  <path
     d="m 1041,1154 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path312" />
  <path
     d="m 1041,1159 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path314" />
  <path
     d="m 1041,1164 c 0,10 60,10 60,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path316" />
  <path
     d="m 1041,1154 c 0,-13.33 60,-13.33 60,0 v 40 c 0,13.33 -60,13.33 -60,0 z"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path318" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch322">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1184px;margin-left:1042px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1071"
       y="1190"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text320">v1</text>
  </switch>
  <path
     d="m 598.5,1173.64 109.09,-0.79"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path324" />
  <path
     d="m 712.84,1172.81 -6.98,3.55 1.73,-3.51 -1.78,-3.49 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path326" />
  <path
     d="M 897.6,1165.46 Q 981,1149 1034.78,1160.65"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path328" />
  <path
     d="m 1039.91,1161.76 -7.59,1.94 2.46,-3.05 -0.97,-3.79 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path330" />
  <path
     d="M 903.77,1186.67 Q 991,1209 1041,1184"
     fill="none"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path332" />
  <path
     d="m 898.68,1185.36 7.65,-1.65 -2.56,2.96 0.83,3.82 z"
     stroke="#000000"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path334" />
  <switch
     transform="translate(-0.5,-0.5)"
     id="switch340">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:1px;height:1px;padding-top:1174px;margin-left:981px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0); background-color: rgb(255 255 255);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;background-color:#fff;white-space:nowrap">
            <xhtml:font
               style="font-size:15px"
               face="Helvetica">Using cache</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="981"
       y="1180"
       font-family="Courier New"
       font-size="19"
       text-anchor="middle"
       style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-size:18.6667px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal;text-anchor:middle;fill:#000000"
       id="text338"><tspan
         style="font-style:normal;font-variant:normal;font-weight:400;font-stretch:normal;font-size:13.3333px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal"
         id="tspan336">Using cache</tspan></text>
  </switch>
  <text
     x="824.3877"
     y="1475.693"
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="text342">Previous version</text>
  <path
     d="m 779.88772,1451.193 v -120 h 60.46 l 26.14,30.48 v 89.52 z m 4.9,-5.69 h 76.8 v -78.11 h -24.51 v -30.48 h -52.29 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path344" />
  <path
     d="m 796.99772,1439.693 v -55 h 52.38 v 55 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path346" />
  <path
     d="m 796.99772,1439.693 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path348" />
  <path
     d="m 823.24772,1418.613 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path350" />
  <switch
     transform="translate(-1.6122807,21.692983)"
     id="switch354">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1334px;margin-left:782px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: #B3B3B3;">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#b3b3b3;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="811"
       y="1340"
       fill="#b3b3b3"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text352">v1</text>
  </switch>
  <path
     d="m 239.88772,1370.093 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 h -16.43 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 l -13.78,51.21 z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z m 68.44,-35.33 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 h -16.43 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 l -13.78,51.21 z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     fill="#b3b3b3"
     pointer-events="none"
     id="path356" />
  <path
     d="m 499.88772,1370.093 c 0.27,-7.76 6.14,-13.89 13.27,-13.89 h 64.12 l 20.11,21.89 v 74.55 c -0.31,6.84 -5.01,12.5 -11.23,13.55 h -74.02 c -6.62,-0.54 -11.86,-6.34 -12.25,-13.55 z m 8.17,82.32 c 0.31,2.35 1.75,4.34 3.78,5.23 h 72.79 c 2.49,-0.69 4.33,-3 4.59,-5.78 l 0.1,-68.66 h -16.43 v -18 h -60.03 c -2.34,0.48 -4.21,2.38 -4.8,4.89 z m 25.01,-22.44 -18.38,-13 v -4.89 l 18.38,-13.55 v 7 l -13.37,8.89 13.37,8.33 z m 4.29,9.22 13.78,-51.21 h 5.51 l -13.78,51.21 z m 24.09,-33.77 v -7.22 l 18.28,13.33 v 5 l -18.28,12.89 v -6.89 l 13.38,-8.33 z"
     pointer-events="none"
     id="path358" />
  <path
     d="m 574.23772,1476.193 -24.35,-24.37 9.74,-9.7 12.48,12.52 c 5.92,-5.95 12.02,-11.92 19.82,-18.01 7.91,-6.14 16.35,-10.44 20.46,-10.24 -7.8,6.56 -15.57,14.73 -22.42,23.68 -7.22,9.22 -12.41,18.17 -15.73,26.12 z"
     fill="#2d9c5e"
     pointer-events="none"
     id="path360" />
  <switch
     transform="translate(-1.6122807,21.692983)"
     id="switch364">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:1389px;margin-left:2px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:14px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">
            <xhtml:font
               style="font-size:19px">6. Replacement<xhtml:br />
</xhtml:font>
          </xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="96"
       y="1393"
       font-family="Helvetica"
       font-size="14px"
       text-anchor="middle"
       id="text362">6. Replacement</text>
  </switch>
  <path
     d="m 1069.8877,1341.193 c 0,10 60,10 60,0"
     fill="none"
     stroke="#b3b3b3"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path366" />
  <path
     d="m 1069.8877,1346.193 c 0,10 60,10 60,0"
     fill="none"
     stroke="#b3b3b3"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path368" />
  <path
     d="m 1069.8877,1351.193 c 0,10 60,10 60,0"
     fill="none"
     stroke="#b3b3b3"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path370" />
  <path
     d="m 1069.8877,1341.193 c 0,-13.33 60,-13.33 60,0 v 40 c 0,13.33 -60,13.33 -60,0 z"
     fill="none"
     stroke="#b3b3b3"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path372" />
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g380"
     transform="translate(-1.1122807,22.192983)">
    <text
       x="1111"
       y="1396.5"
       id="text374">Old cache:</text>
    <text
       x="1111"
       y="1413.5"
       id="text378">purged during v2 <tspan
   style="font-style:normal;font-variant:normal;font-weight:700;font-stretch:normal;font-family:Helvetica;-inkscape-font-specification:'Helvetica Bold'"
   id="tspan376">activation</tspan></text>
  </g>
  <switch
     transform="translate(-1.6122807,21.692983)"
     id="switch384">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1349px;margin-left:1072px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: #B3B3B3;">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#b3b3b3;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v1</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="1101"
       y="1355"
       fill="#b3b3b3"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text382">v1</text>
  </switch>
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g394"
     transform="translate(-1.1122807,22.192983)">
    <text
       x="725.5"
       y="1496.5"
       id="text386">New version:</text>
    <text
       x="725.5"
       y="1513.5"
       style="font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:14px;font-family:Helvetica;-inkscape-font-specification:'Helvetica, Normal';font-variant-ligatures:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-east-asian:normal"
       id="text392"><tspan
         sodipodi:role="line"
         id="tspan1316"
         x="725.5"
         y="1513.5">will control pages</tspan><tspan
         sodipodi:role="line"
         id="tspan1318"
         x="725.5"
         y="1531">once installed and activated</tspan></text>
  </g>
  <path
     d="m 679.88772,1481.193 v -120 h 60.46 l 26.14,30.48 v 89.52 z m 4.9,-5.69 h 76.8 v -78.11 h -24.51 v -30.48 h -52.29 z"
     pointer-events="none"
     id="path396" />
  <path
     d="m 696.99772,1469.693 v -55 h 52.38 v 55 z"
     pointer-events="none"
     id="path398" />
  <path
     d="m 696.99772,1469.693 v -2.48 h 52.38 v 2.48 z"
     fill-opacity="0.3"
     pointer-events="none"
     id="path400" />
  <path
     d="m 723.24772,1448.613 c -4.32,0 -7.71,-3.44 -7.71,-7.65 0,-4.54 3.78,-7.71 7.48,-7.71 4.95,0 7.81,4.07 7.81,7.58 0,4.51 -3.57,7.78 -7.58,7.78 z m 0.16,5.99 c 7,0 13.42,-5.9 13.42,-13.85 0,-7.17 -5.85,-13.54 -13.86,-13.54 -6.57,0 -13.42,5.47 -13.42,13.81 0,7.18 5.73,13.58 13.86,13.58 z m -13.13,5.39 -6.03,-6.08 2.45,-2.48 c -1.19,-1.93 -2.1,-4.02 -2.57,-6.21 h -3.47 v -8.55 l 3.47,-0.02 c 0.46,-2.2 1.42,-4.38 2.57,-6.22 l -2.45,-2.44 6.03,-6.06 2.47,2.46 c 2.03,-1.27 4.1,-2.11 6.18,-2.57 v -3.49 h 8.52 v 3.51 c 2.48,0.55 4.5,1.51 6.18,2.57 l 2.47,-2.47 6.04,6.03 -2.45,2.46 c 1.35,2.15 2.12,4.32 2.54,6.22 h 3.47 v 8.59 h -3.47 c -0.57,2.49 -1.49,4.51 -2.57,6.2 l 2.47,2.47 -6.01,6.06 -2.47,-2.47 c -1.76,1.08 -3.69,2.02 -6.17,2.56 l -0.02,3.51 h -8.53 v -3.48 c -2.31,-0.53 -4.37,-1.42 -6.18,-2.59 z"
     fill="#ffffff"
     pointer-events="none"
     id="path402" />
  <switch
     transform="translate(-1.6122807,21.692983)"
     id="switch406">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1364px;margin-left:682px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v2</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="711"
       y="1370"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text404">v2</text>
  </switch>
  <path
     d="m 949.88772,1426.193 c 0,10 59.99998,10 59.99998,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path408" />
  <path
     d="m 949.88772,1431.193 c 0,10 59.99998,10 59.99998,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path410" />
  <path
     d="m 949.88772,1436.193 c 0,10 59.99998,10 59.99998,0"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path412" />
  <path
     d="m 949.88772,1426.193 c 0,-13.33 59.99998,-13.33 59.99998,0 v 40 c 0,13.33 -59.99998,13.33 -59.99998,0 z"
     fill="none"
     stroke="#000000"
     stroke-width="3"
     stroke-miterlimit="10"
     pointer-events="none"
     id="path414" />
  <g
     font-family="Helvetica"
     pointer-events="none"
     text-anchor="middle"
     font-size="14px"
     id="g422"
     transform="translate(-1.1122807,22.192983)">
    <text
       x="980.5"
       y="1481.5"
       id="text416">New cache:</text>
    <text
       x="980.5"
       y="1498.5"
       id="text420">populated during <tspan
   style="font-style:normal;font-variant:normal;font-weight:700;font-stretch:normal;font-family:Helvetica;-inkscape-font-specification:'Helvetica Bold'"
   id="tspan418">installation</tspan></text>
  </g>
  <switch
     transform="translate(-1.6122807,21.692983)"
     id="switch426">
    <foreignObject
       style="overflow:visible;text-align:left"
       pointer-events="none"
       width="100%"
       height="100%"
       requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
      <xhtml:div
         style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:58px;height:1px;padding-top:1434px;margin-left:952px">
        <xhtml:div
           style="box-sizing:border-box;font-size:0;text-align:center"
           data-drawio-colors="color: rgb(0 0 0);">
          <xhtml:div
             style="display:inline-block;font-size:19px;font-family:Courier New;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">v2</xhtml:div>
        </xhtml:div>
      </xhtml:div>
    </foreignObject>
    <text
       x="981"
       y="1440"
       font-family="'Courier New'"
       font-size="19px"
       text-anchor="middle"
       id="text424">v2</text>
  </switch>
</svg>
