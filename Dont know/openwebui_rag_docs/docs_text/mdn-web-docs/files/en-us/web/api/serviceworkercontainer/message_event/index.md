Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkercontainer > message_event > index.md --- title: "ServiceWorkerContainer: message event" short-title: message slug: Web/API/ServiceWorkerContainer/message_event page-type: web-api-event browser-compat: api.ServiceWorkerContainer.message_event --- {{APIRef("Service Workers API")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`message`** event is used in a page controlled by a service worker to receive messages from the service worker. This event is not cancelable and does not bubble. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("message", (event) => { }) onmessage = (event) => { } ``` ## Event type A {{domxref("MessageEvent")}}. Inherits from {{domxref("Event")}}. {{InheritanceDiagram("MessageEvent")}} ## Event properties _This interface also inherits properties from its parent, {{domxref("Event")}}._ - {{domxref("MessageEvent.data")}} {{ReadOnlyInline}} - : The data sent by the message emitter. - {{domxref("MessageEvent.origin")}} {{ReadOnlyInline}} - : A string representing the origin of the message emitter. - {{domxref("MessageEvent.lastEventId")}} {{ReadOnlyInline}} - : A string representing a unique ID for the event. - {{domxref("MessageEvent.source")}} {{ReadOnlyInline}} - : A `MessageEventSource` (which can be a {{glossary("WindowProxy")}}, {{domxref("MessagePort")}}, or {{domxref("ServiceWorker")}} object) representing the message emitter. - {{domxref("MessageEvent.ports")}} {{ReadOnlyInline}} - : An array of {{domxref("MessagePort")}} objects representing the ports associated with the channel the message is being sent through (where appropriate, e.g., in channel messaging or when sending a message to a shared worker). ## Examples In this example the service worker get the client's ID from a [`fetch`](/en-US/docs/Web/API/ServiceWorkerGlobalScope/fetch_event) event and then sends it a message using [`Client.postMessage`](/en-US/docs/Web/API/Client/postMessage): ```js // service-worker.js async function messageClient(clientId) { const client = await clients.get(clientId); client.postMessage("Hi client!"); } addEventListener("fetch", (event) => { messageClient(event.clientId); event.respondWith(() => { // }); }); ``` The client can receive the message by listening to the `message` event: ```js // main.js navigator.serviceWorker.addEventListener("message", (message) => { console.log(message); }); ``` Alternatively, the client can receive the message with the `onmessage` event handler: ```js // main.js navigator.serviceWorker.onmessage = (message) => { console.log(message); }; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers) - [Service workers basic code example](https://github.com/mdn/dom-examples/tree/main/service-worker/simple-service-worker) - [Using web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers)