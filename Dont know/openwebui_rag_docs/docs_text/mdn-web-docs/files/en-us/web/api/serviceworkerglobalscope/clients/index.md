Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > clients > index.md --- title: "ServiceWorkerGlobalScope: clients property" short-title: clients slug: Web/API/ServiceWorkerGlobalScope/clients page-type: web-api-instance-property browser-compat: api.ServiceWorkerGlobalScope.clients --- {{APIRef("Service Workers API")}}{{SecureContext_Header}}{{AvailableInWorkers("service")}} The **`clients`** read-only property of the {{domxref("ServiceWorkerGlobalScope")}} interface returns the [`Clients`](/en-US/docs/Web/API/Clients) object associated with the service worker. ## Value The {{domxref("Clients")}} object associated with the specific worker. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers) - [Service workers basic code example](https://github.com/mdn/dom-examples/tree/main/service-worker/simple-service-worker) - [Using web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers)