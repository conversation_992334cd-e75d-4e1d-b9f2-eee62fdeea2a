Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > cookiestore > index.md --- title: "ServiceWorkerGlobalScope: cookieStore property" short-title: cookieStore slug: Web/API/ServiceWorkerGlobalScope/cookieStore page-type: web-api-instance-property browser-compat: api.ServiceWorkerGlobalScope.cookieStore --- {{APIRef("Cookie Store API")}}{{SecureContext_Header}}{{AvailableInWorkers("service")}} The **`cookieStore`** read-only property of the {{domxref("ServiceWorkerGlobalScope")}} interface returns a reference to the {{domxref("CookieStore")}} object associated with this service worker. ## Value A {{domxref("CookieStore")}} object instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}