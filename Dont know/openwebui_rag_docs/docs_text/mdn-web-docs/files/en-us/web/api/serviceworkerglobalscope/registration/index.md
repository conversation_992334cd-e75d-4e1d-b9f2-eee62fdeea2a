Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > registration > index.md --- title: "ServiceWorkerGlobalScope: registration property" short-title: registration slug: Web/API/ServiceWorkerGlobalScope/registration page-type: web-api-instance-property browser-compat: api.ServiceWorkerGlobalScope.registration --- {{APIRef("Service Workers API")}}{{SecureContext_Header}}{{AvailableInWorkers("service")}} The **`registration`** read-only property of the {{domxref("ServiceWorkerGlobalScope")}} interface returns a reference to the {{domxref("ServiceWorkerRegistration")}} object, which represents the service worker's registration. ## Value A {{domxref("ServiceWorkerRegistration")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers) - [Service workers basic code example](https://github.com/mdn/dom-examples/tree/main/service-worker/simple-service-worker) - [Using web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers)