Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerglobalscope > serviceworker > index.md --- title: "ServiceWorkerGlobalScope: serviceWorker property" short-title: serviceWorker slug: Web/API/ServiceWorkerGlobalScope/serviceWorker page-type: web-api-instance-property browser-compat: api.ServiceWorkerGlobalScope.serviceWorker --- {{APIRef("Service Workers API")}}{{SecureContext_Header}}{{AvailableInWorkers("service")}} The **`serviceWorker`** read-only property of the {{domxref("ServiceWorkerGlobalScope")}} interface returns a reference to the {{domxref("ServiceWorker")}} object, which represents the service worker. ## Value A {{domxref("ServiceWorker")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers)