Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > sync > index.md --- title: "ServiceWorkerRegistration: sync property" short-title: sync slug: Web/API/ServiceWorkerRegistration/sync page-type: web-api-instance-property browser-compat: api.ServiceWorkerRegistration.sync --- {{APIRef("Background Sync")}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`sync`** read-only property of the {{domxref("ServiceWorkerRegistration")}} interface returns a reference to the {{domxref("SyncManager")}} interface, which manages background synchronization processes. ## Value A {{domxref("SyncManager")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}