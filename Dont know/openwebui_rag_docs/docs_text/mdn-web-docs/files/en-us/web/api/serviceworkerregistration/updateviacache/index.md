Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md

Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md Path: mdn-web-docs > files > en-us > web > api > serviceworkerregistration > updateviacache > index.md --- title: "ServiceWorkerRegistration: updateViaCache property" short-title: updateViaCache slug: Web/API/ServiceWorkerRegistration/updateViaCache page-type: web-api-instance-property browser-compat: api.ServiceWorkerRegistration.updateViaCache --- {{APIRef("Service Workers API")}}{{SecureContext_Header}} {{AvailableInWorkers}} The **`updateViaCache`** read-only property of the {{domxref("ServiceWorkerRegistration")}} interface returns the value of the setting used to determine the circumstances in which the browser will consult the HTTP cache when it tries to update the service worker or any scripts that are imported via {{domxref("WorkerGlobalScope.importScripts", "importScripts()")}}. ## Value Returns one of the following values: - `imports`, meaning the HTTP cache is not consulted for updates to the service worker script, but is consulted for scripts imported using {{domxref("WorkerGlobalScope.importScripts", "importScripts()")}}. This is the default value. - `all`, meaning the HTTP cache is consulted for updates to the service worker script and for scripts imported using {{domxref("WorkerGlobalScope.importScripts", "importScripts()")}}. - `none`, meaning the HTTP cache is never consulted. ## Examples The following example shows the use of updateViaCache. ```js if ("serviceWorker" in navigator) { navigator.serviceWorker .register("/service-worker.js", { updateViaCache: "none", }) .then((registration) => { registration.addEventListener("updatefound", () => { // If updatefound is fired, it means that there's // a new service worker being installed. console.log(`Value of updateViaCache: ${registration.updateViaCache}`); }); }) .catch((error) => { console.error(`Service worker registration failed: ${error}`); }); } ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using Service Workers](/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers) - [Service workers basic code example](https://github.com/mdn/dom-examples/tree/main/service-worker/simple-service-worker) - [Using web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers)