Path: mdn-web-docs > files > en-us > web > api > sharedworkerglobalscope > index.md

Path: mdn-web-docs > files > en-us > web > api > sharedworkerglobalscope > index.md Path: mdn-web-docs > files > en-us > web > api > sharedworkerglobalscope > index.md Path: mdn-web-docs > files > en-us > web > api > sharedworkerglobalscope > index.md Path: mdn-web-docs > files > en-us > web > api > sharedworkerglobalscope > index.md --- title: SharedWorkerGlobalScope slug: Web/API/SharedWorkerGlobalScope page-type: web-api-interface browser-compat: api.SharedWorkerGlobalScope --- {{APIRef("Web Workers API")}} The **`SharedWorkerGlobalScope`** object (the {{domxref("SharedWorker")}} global scope) is accessible through the {{domxref("window.self","self")}} keyword. Some additional global functions, namespaces objects, and constructors, not typically associated with the worker global scope, but available on it, are listed in the [JavaScript Reference](/en-US/docs/Web/JavaScript/Reference). See the complete list of [functions available to workers](/en-US/docs/Web/API/Web_Workers_API/Functions_and_classes_available_to_workers). {{InheritanceDiagram}} ## Instance properties _This interface inherits properties from the {{domxref("WorkerGlobalScope")}} interface, and its parent {{domxref("EventTarget")}}._ - {{domxref("SharedWorkerGlobalScope.name")}} {{ReadOnlyInline}} - : The name that the {{domxref("SharedWorker")}} was (optionally) given when it was created using the {{domxref("SharedWorker.SharedWorker", "SharedWorker()")}} constructor. This is mainly useful for debugging purposes. ## Instance methods _This interface inherits methods from the {{domxref("WorkerGlobalScope")}} interface, and its parent {{domxref("EventTarget")}}._ - {{domxref("SharedWorkerGlobalScope.close()")}} - : Discards any tasks queued in the `SharedWorkerGlobalScope`'s event loop, effectively closing this particular scope. ## Events Listen to this event using {{domxref("EventTarget.addEventListener()", "addEventListener()")}} or by assigning an event listener to the `oneventname` property of this interface. - {{domxref("SharedWorkerGlobalScope/connect_event", "connect")}} - : Fired on shared workers when a new client connects. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SharedWorker")}} - {{domxref("WorkerGlobalScope")}} - [Using Web workers](/en-US/docs/Web/API/Web_Workers_API/Using_web_workers) - [Functions available to workers](/en-US/docs/Web/API/Web_Workers_API/Functions_and_classes_available_to_workers)