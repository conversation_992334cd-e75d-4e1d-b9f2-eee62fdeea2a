Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > audiotracks > index.md --- title: "SourceBuffer: audioTracks property" short-title: audioTracks slug: Web/API/SourceBuffer/audioTracks page-type: web-api-instance-property browser-compat: api.SourceBuffer.audioTracks --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`audioTracks`** read-only property of the {{domxref("SourceBuffer")}} interface returns a list of the audio tracks currently contained inside the `SourceBuffer`. ## Value An {{domxref("AudioTrackList")}} object. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBufferList")}}