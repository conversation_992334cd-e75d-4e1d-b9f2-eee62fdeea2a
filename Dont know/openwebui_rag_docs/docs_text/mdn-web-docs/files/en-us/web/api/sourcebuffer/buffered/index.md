Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > buffered > index.md --- title: "SourceBuffer: buffered property" short-title: buffered slug: Web/API/SourceBuffer/buffered page-type: web-api-instance-property browser-compat: api.SourceBuffer.buffered --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`buffered`** read-only property of the {{domxref("SourceBuffer")}} interface returns the time ranges that are currently buffered in the `SourceBuffer` as a normalized {{domxref("TimeRanges")}} object. ## Value A {{domxref("TimeRanges")}} object. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBufferList")}}