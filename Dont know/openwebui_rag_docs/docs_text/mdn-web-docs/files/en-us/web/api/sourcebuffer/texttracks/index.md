Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > texttracks > index.md --- title: "SourceBuffer: textTracks property" short-title: textTracks slug: Web/API/SourceBuffer/textTracks page-type: web-api-instance-property status: - experimental browser-compat: api.SourceBuffer.textTracks --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}}{{SeeCompatTable}} The **`textTracks`** read-only property of the {{domxref("SourceBuffer")}} interface returns a list of the text tracks currently contained inside the `SourceBuffer`. ## Value An {{domxref("TextTrackList")}} object. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBufferList")}}