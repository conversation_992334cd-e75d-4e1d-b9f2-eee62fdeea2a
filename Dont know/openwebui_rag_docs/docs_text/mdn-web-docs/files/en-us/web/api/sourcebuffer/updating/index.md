Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > updating > index.md --- title: "SourceBuffer: updating property" short-title: updating slug: Web/API/SourceBuffer/updating page-type: web-api-instance-property browser-compat: api.SourceBuffer.updating --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`updating`** read-only property of the {{domxref("SourceBuffer")}} interface indicates whether the `SourceBuffer` is currently being updated i.e., whether an {{domxref("SourceBuffer.appendBuffer()")}} or {{domxref("SourceBuffer.remove()")}} operation is currently in progress. ## Value A boolean value. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBufferList")}}