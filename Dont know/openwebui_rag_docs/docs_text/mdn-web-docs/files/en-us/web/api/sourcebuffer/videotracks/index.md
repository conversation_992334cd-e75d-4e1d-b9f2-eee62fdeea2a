Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebuffer > videotracks > index.md --- title: "SourceBuffer: videoTracks property" short-title: videoTracks slug: Web/API/SourceBuffer/videoTracks page-type: web-api-instance-property browser-compat: api.SourceBuffer.videoTracks --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`videoTracks`** read-only property of the {{domxref("SourceBuffer")}} interface returns a list of the video tracks currently contained inside the `SourceBuffer`. ## Value An {{domxref("VideoTrackList")}} object. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBufferList")}}