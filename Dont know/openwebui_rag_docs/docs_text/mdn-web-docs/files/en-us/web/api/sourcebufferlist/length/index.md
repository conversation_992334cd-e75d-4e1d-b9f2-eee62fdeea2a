Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > sourcebufferlist > length > index.md --- title: "SourceBufferList: length property" short-title: length slug: Web/API/SourceBufferList/length page-type: web-api-instance-property browser-compat: api.SourceBufferList.length --- {{APIRef("Media Source Extensions")}}{{AvailableInWorkers("window_and_dedicated")}} The **`length`** read-only property of the {{domxref("SourceBufferList")}} interface returns the number of {{domxref("SourceBuffer")}} objects in the list. ## Value An unsigned long number. ## Examples TBD ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("MediaSource")}} - {{domxref("SourceBuffer")}}