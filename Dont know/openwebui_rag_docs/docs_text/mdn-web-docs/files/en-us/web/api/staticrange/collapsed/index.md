Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md

Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > collapsed > index.md --- title: "StaticRange: collapsed property" short-title: collapsed slug: Web/API/StaticRange/collapsed page-type: web-api-instance-property browser-compat: api.StaticRange.collapsed --- {{APIRef("DOM WHATWG")}} The **`collapsed`** read-only property of the {{domxref("StaticRange")}} interface returns `true` if the range's start position and end position are the same. ## Value A boolean value which is `true` if the range is **collapsed**. A collapsed range is one in which the start and end positions are the same, resulting in a zero-character-long range. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}