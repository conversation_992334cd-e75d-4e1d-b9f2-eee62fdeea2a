Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md

Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endcontainer > index.md --- title: "StaticRange: endContainer property" short-title: endContainer slug: Web/API/StaticRange/endContainer page-type: web-api-instance-property browser-compat: api.StaticRange.endContainer --- {{APIRef("DOM WHATWG")}} The **`endContainer`** property of the {{domxref("StaticRange")}} interface returns the end {{domxref("Node")}} for the range. ## Value The DOM {{domxref("Node")}} which contains the final character of the range. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}