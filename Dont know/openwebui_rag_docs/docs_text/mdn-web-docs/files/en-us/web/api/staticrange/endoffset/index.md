Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md

Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > endoffset > index.md --- title: "StaticRange: endOffset property" short-title: endOffset slug: Web/API/StaticRange/endOffset page-type: web-api-instance-property browser-compat: api.StaticRange.endOffset --- {{APIRef("DOM")}} The **`endOffset`** property of the {{domxref("StaticRange")}} interface returns the offset into the end node of the range's end position. ## Value An integer value indicating the number of characters into the {{domxref("Node")}} indicated by {{domxref("StaticRange.endContainer", "endContainer")}} at which the final character of the range is located. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}