Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md

Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startcontainer > index.md --- title: "StaticRange: startContainer property" short-title: startContainer slug: Web/API/StaticRange/startContainer page-type: web-api-instance-property browser-compat: api.StaticRange.startContainer --- {{APIRef("DOM")}} The read-only **`startContainer`** property of the {{domxref("StaticRange")}} interface returns the start {{domxref("Node")}} for the range. ## Value The DOM {{domxref("Node")}} inside which the start position of the range is found. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}