Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md

Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > staticrange > startoffset > index.md --- title: "StaticRange: startOffset property" short-title: startOffset slug: Web/API/StaticRange/startOffset page-type: web-api-instance-property browser-compat: api.StaticRange.startOffset --- {{APIRef("DOM")}} The read-only **`startOffset`** property of the {{domxref("StaticRange")}} interface returns the offset into the start node of the range's start position. ## Value An integer value indicating the number of characters into the {{domxref("Node")}} indicated by {{domxref("StaticRange.startContainer", "startContainer")}} at which the first character of the range is located. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}