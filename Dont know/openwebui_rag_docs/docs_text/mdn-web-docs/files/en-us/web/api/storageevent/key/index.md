Path: mdn-web-docs > files > en-us > web > api > storageevent > key > index.md

Path: mdn-web-docs > files > en-us > web > api > storageevent > key > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > key > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > key > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > key > index.md --- title: "StorageEvent: key property" short-title: key slug: Web/API/StorageEvent/key page-type: web-api-instance-property browser-compat: api.StorageEvent.key --- {{APIRef("Web Storage API")}} The **`key`** property of the {{domxref("StorageEvent")}} interface returns the key for the storage item that was changed. ## Value A string containing the key for the storage item that was changed. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Web Storage API", "", "", "nocode")}}