Path: mdn-web-docs > files > en-us > web > api > storageevent > newvalue > index.md

Path: mdn-web-docs > files > en-us > web > api > storageevent > newvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > newvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > newvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > newvalue > index.md --- title: "StorageEvent: newValue property" short-title: newValue slug: Web/API/StorageEvent/newValue page-type: web-api-instance-property browser-compat: api.StorageEvent.newValue --- {{APIRef("Web Storage API")}} The **`newValue`** property of the {{domxref("StorageEvent")}} interface returns the new value of the storage item whose value was changed. ## Value A string containing the new value of the storage item. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Web Storage API", "", "", "nocode")}}