Path: mdn-web-docs > files > en-us > web > api > storageevent > oldvalue > index.md

Path: mdn-web-docs > files > en-us > web > api > storageevent > oldvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > oldvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > oldvalue > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > oldvalue > index.md --- title: "StorageEvent: oldValue property" short-title: oldValue slug: Web/API/StorageEvent/oldValue page-type: web-api-instance-property browser-compat: api.StorageEvent.oldValue --- {{APIRef("Web Storage API")}} The **`oldValue`** property of the {{domxref("StorageEvent")}} interface returns the original value of the storage item whose value changed. ## Value A string containing the original value of the storage item. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Web Storage API", "", "", "nocode")}}