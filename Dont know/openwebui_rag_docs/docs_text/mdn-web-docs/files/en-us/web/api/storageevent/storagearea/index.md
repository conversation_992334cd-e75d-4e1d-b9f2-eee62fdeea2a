Path: mdn-web-docs > files > en-us > web > api > storageevent > storagearea > index.md

Path: mdn-web-docs > files > en-us > web > api > storageevent > storagearea > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > storagearea > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > storagearea > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > storagearea > index.md --- title: "StorageEvent: storageArea property" short-title: storageArea slug: Web/API/StorageEvent/storageArea page-type: web-api-instance-property browser-compat: api.StorageEvent.storageArea --- {{APIRef("Web Storage API")}} The **`storageArea`** property of the {{domxref("StorageEvent")}} interface returns the storage object that was affected. ## Value A {{DOMxRef("Storage")}} object that represents the storage object that was affected. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Web Storage API", "", "", "nocode")}}