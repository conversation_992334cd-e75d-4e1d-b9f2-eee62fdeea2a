Path: mdn-web-docs > files > en-us > web > api > storageevent > url > index.md

Path: mdn-web-docs > files > en-us > web > api > storageevent > url > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > url > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > url > index.md Path: mdn-web-docs > files > en-us > web > api > storageevent > url > index.md --- title: "StorageEvent: url property" short-title: url slug: Web/API/StorageEvent/url page-type: web-api-instance-property browser-compat: api.StorageEvent.url --- {{APIRef("Web Storage API")}} The **`url`** property of the {{domxref("StorageEvent")}} interface returns the URL of the document whose storage changed. ## Value A string containing the URL of the document whose storage changed. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("Web Storage API", "", "", "nocode")}}