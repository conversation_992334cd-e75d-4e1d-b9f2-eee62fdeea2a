Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md

Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > title > index.md --- title: "StyleSheet: title property" short-title: title slug: Web/API/StyleSheet/title page-type: web-api-instance-property browser-compat: api.StyleSheet.title --- {{APIRef("CSSOM")}} The **`title`** property of the {{domxref("StyleSheet")}} interface returns the advisory title of the current style sheet. The title is often specified in the {{domxref("StyleSheet/OwnerNode", "ownerNode")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}