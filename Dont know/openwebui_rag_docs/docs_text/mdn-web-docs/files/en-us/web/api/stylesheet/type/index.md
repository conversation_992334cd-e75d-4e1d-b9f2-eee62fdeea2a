Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md

Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md Path: mdn-web-docs > files > en-us > web > api > stylesheet > type > index.md --- title: "StyleSheet: type property" short-title: type slug: Web/API/StyleSheet/type page-type: web-api-instance-property browser-compat: api.StyleSheet.type --- {{APIRef("CSSOM")}} The **`type`** property of the {{domxref("StyleSheet")}} interface specifies the style sheet language for the given style sheet. ## Value A string. ## Examples ```js myStyleSheet.type = "text/css"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}