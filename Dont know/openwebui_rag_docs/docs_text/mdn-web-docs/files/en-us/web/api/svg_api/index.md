Path: mdn-web-docs > files > en-us > web > api > svg_api > index.md

Path: mdn-web-docs > files > en-us > web > api > svg_api > index.md Path: mdn-web-docs > files > en-us > web > api > svg_api > index.md Path: mdn-web-docs > files > en-us > web > api > svg_api > index.md Path: mdn-web-docs > files > en-us > web > api > svg_api > index.md --- title: SVG API slug: Web/API/SVG_API page-type: web-api-overview spec-urls: https://svgwg.org/svg2-draft/ --- {{DefaultAPISidebar("SVG")}} SVG provides elements for circles, rectangles, and simple and complex curves. The elements' attribute values specify how these must be drawn. The **SVG API** is the subset of the **DOM** connecting these SVG elements and their attribute values to scripts or programming languages by representing them in memory. The SVG API thus provides methods that allow programmatic access to the SVG elements and their attribute values. The SVG API is a set of interfaces that have been categorized into the following broad categories: 1. [The element interfaces](#svg_element_interfaces) provide access to the properties of SVG elements and methods to manipulate them. 2. [The static data type](#svg_data_type_interfaces) interfaces provide access to element attribute values and methods to manipulate them. 3. For attributes that can be animated, the [animated data type interfaces](#svg_data_type_interfaces) provide read only access to the current animated value of an attribute. 4. [The Synchronized Multimedia Integration Language (SMIL) interfaces](#smil-related_interfaces) 5. [Others](#other_svg_interfaces) ## Interfaces ### SVG element interfaces - {{DOMxRef("SVGAElement")}} - {{DOMxRef("SVGAnimationElement")}} - {{DOMxRef("SVGAnimateElement")}} - {{DOMxRef("SVGAnimateMotionElement")}} - {{DOMxRef("SVGAnimateTransformElement")}} - {{DOMxRef("SVGCircleElement")}} - {{DOMxRef("SVGClipPathElement")}} - {{DOMxRef("SVGComponentTransferFunctionElement")}} - {{DOMxRef("SVGDefsElement")}} - {{DOMxRef("SVGDescElement")}} - {{DOMxRef("SVGDiscardElement")}} - {{DOMxRef("SVGElement")}} - {{DOMxRef("SVGEllipseElement")}} - {{DOMxRef("SVGFEBlendElement")}} - {{DOMxRef("SVGFEColorMatrixElement")}} - {{DOMxRef("SVGFEComponentTransferElement")}} - {{DOMxRef("SVGFECompositeElement")}} - {{DOMxRef("SVGFEConvolveMatrixElement")}} - {{DOMxRef("SVGFEDiffuseLightingElement")}} - {{DOMxRef("SVGFEDisplacementMapElement")}} - {{DOMxRef("SVGFEDistantLightElement")}} - {{DOMxRef("SVGFEDropShadowElement")}} - {{DOMxRef("SVGFEFloodElement")}} - {{DOMxRef("SVGFEFuncAElement")}} - {{DOMxRef("SVGFEFuncBElement")}} - {{DOMxRef("SVGFEFuncGElement")}} - {{DOMxRef("SVGFEFuncRElement")}} - {{DOMxRef("SVGFEGaussianBlurElement")}} - {{DOMxRef("SVGFEImageElement")}} - {{DOMxRef("SVGFEMergeElement")}} - {{DOMxRef("SVGFEMergeNodeElement")}} - {{DOMxRef("SVGFEMorphologyElement")}} - {{DOMxRef("SVGFEOffsetElement")}} - {{DOMxRef("SVGFEPointLightElement")}} - {{DOMxRef("SVGFESpecularLightingElement")}} - {{DOMxRef("SVGFESpotLightElement")}} - {{DOMxRef("SVGFETileElement")}} - {{DOMxRef("SVGFETurbulenceElement")}} - {{DOMxRef("SVGFilterElement")}} - {{DOMxRef("SVGForeignObjectElement")}} - {{DOMxRef("SVGGElement")}} - {{DOMxRef("SVGGeometryElement")}} - {{DOMxRef("SVGGradientElement")}} - {{DOMxRef("SVGGraphicsElement")}} - {{DOMxRef("SVGImageElement")}} - {{DOMxRef("SVGLinearGradientElement")}} - {{DOMxRef("SVGLineElement")}} - {{DOMxRef("SVGMarkerElement")}} {{Experimental_Inline}} - {{DOMxRef("SVGMaskElement")}} - {{DOMxRef("SVGMetadataElement")}} - {{DOMxRef("SVGMPathElement")}} - {{DOMxRef("SVGPathElement")}} - {{DOMxRef("SVGPatternElement")}} - {{DOMxRef("SVGPolylineElement")}} - {{DOMxRef("SVGPolygonElement")}} - {{DOMxRef("SVGRadialGradientElement")}} - {{DOMxRef("SVGRectElement")}} - {{DOMxRef("SVGScriptElement")}} - {{DOMxRef("SVGSetElement")}} - {{DOMxRef("SVGStopElement")}} - {{DOMxRef("SVGStyleElement")}} - {{DOMxRef("SVGSVGElement")}} - {{DOMxRef("SVGSwitchElement")}} - {{DOMxRef("SVGSymbolElement")}} - {{DOMxRef("SVGTextContentElement")}} - {{DOMxRef("SVGTextElement")}} - {{DOMxRef("SVGTextPathElement")}} - {{DOMxRef("SVGTextPositioningElement")}} - {{DOMxRef("SVGTitleElement")}} - {{DOMxRef("SVGTSpanElement")}} - {{DOMxRef("SVGUseElement")}} - {{DOMxRef("SVGViewElement")}} ### SVG data type interfaces Here are the DOM APIs for data types used in the definitions of SVG properties and attributes. #### Static type - {{DOMxRef("SVGAngle")}} - {{DOMxRef("SVGLength")}} - {{DOMxRef("SVGLengthList")}} - {{DOMxRef("SVGNumber")}} - {{DOMxRef("SVGNumberList")}} - {{DOMxRef("SVGPreserveAspectRatio")}} - {{DOMxRef("SVGStringList")}} - {{DOMxRef("SVGTransform")}} - {{DOMxRef("SVGTransformList")}} #### Animated type - {{DOMxRef("SVGAnimatedAngle")}} - {{DOMxRef("SVGAnimatedBoolean")}} - {{DOMxRef("SVGAnimatedEnumeration")}} - {{DOMxRef("SVGAnimatedInteger")}} - {{DOMxRef("SVGAnimatedLength")}} - {{DOMxRef("SVGAnimatedLengthList")}} - {{DOMxRef("SVGAnimatedNumber")}} - {{DOMxRef("SVGAnimatedNumberList")}} - {{DOMxRef("SVGAnimatedPreserveAspectRatio")}} - {{DOMxRef("SVGAnimatedRect")}} - {{DOMxRef("SVGAnimatedString")}} - {{DOMxRef("SVGAnimatedTransformList")}} ### SMIL-related interfaces - {{DOMxRef("TimeEvent")}} ### Other SVG interfaces - {{DOMxRef("ShadowAnimation")}} - {{DOMxRef("SVGUnitTypes")}} - {{DOMxRef("SVGUseElementShadowRoot")}} ## Specifications {{Specifications}} ## See also - [Getting Started with SVG](/en-US/docs/Web/SVG)