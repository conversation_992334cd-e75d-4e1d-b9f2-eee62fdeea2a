Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatecolorelement > index.md --- title: SVGAnimateColorElement slug: Web/API/SVGAnimateColorElement page-type: web-api-interface status: - deprecated browser-compat: api.SVGAnimateColorElement --- {{APIRef("SVG")}}{{deprecated_header}} The **`SVGAnimateColorElement`** interface corresponds to the `<animateColor>` element. {{InheritanceDiagram}} ## Instance properties _This interface has no properties but inherits properties from its parent, {{domxref("SVGAnimationElement")}}._ ## Instance methods _This interface has no methods but inherits methods from its parent, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}