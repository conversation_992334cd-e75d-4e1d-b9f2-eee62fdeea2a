Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedboolean > index.md --- title: SVGAnimatedBoolean slug: Web/API/SVGAnimatedBoolean page-type: web-api-interface browser-compat: api.SVGAnimatedBoolean --- {{APIRef("SVG")}} The **`SVGAnimatedBoolean`** interface is used for attributes of type boolean which can be animated. ## Instance properties - {{domxref("SVGAnimatedBoolean.baseVal")}} - : A boolean representing the base value of the given attribute before applying any animations. - {{domxref("SVGAnimatedBoolean.animVal")}} {{ReadOnlyInline}} - : A boolean representing the current animated value of the given attribute. If the given attribute is not currently being animated, then it contains the same value as `baseVal`. ## Instance methods _The `SVGAnimatedBoolean` interface does not provide any specific methods._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}