Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > animval > index.md --- title: "SVGAnimatedString: animVal property" short-title: animVal slug: Web/API/SVGAnimatedString/animVal page-type: web-api-instance-property browser-compat: api.SVGAnimatedString.animVal --- {{APIRef("SVG")}} The `animVal` read-only property of the {{domxref("SVGAnimatedString")}} interface contains the same value as the {{domxref("SVGAnimatedString.baseVal")}} property. If the given attribute or property is being animated, it contains the current animated value of the attribute or property. If the given attribute or property is not currently being animated, then it contains the same value as `baseVal`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}