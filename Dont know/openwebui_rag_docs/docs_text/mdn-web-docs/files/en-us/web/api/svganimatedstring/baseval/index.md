Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatedstring > baseval > index.md --- title: "SVGAnimatedString: baseVal property" short-title: baseVal slug: Web/API/SVGAnimatedString/baseVal page-type: web-api-instance-property browser-compat: api.SVGAnimatedString.baseVal --- {{APIRef("SVG")}} Base<PERSON><PERSON> gets or sets the base value of the given attribute before any animations are applied. The base value of the given attribute before applying any animations. Setter throws DOMException. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}