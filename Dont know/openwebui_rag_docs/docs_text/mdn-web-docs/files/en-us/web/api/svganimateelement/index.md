Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimateelement > index.md --- title: SVGAnimateElement slug: Web/API/SVGAnimateElement page-type: web-api-interface browser-compat: api.SVGAnimateElement --- {{APIRef("SVG")}} The **`SVGAnimateElement`** interface corresponds to the {{SVGElement("animate")}} element. {{InheritanceDiagram}} ## Instance properties _This interface has no properties but inherits properties from its parent, {{domxref("SVGAnimationElement")}}._ ## Instance methods _This interface has no methods but inherits methods from its parent, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}