Path: mdn-web-docs > files > en-us > web > api > svganimatemotionelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatemotionelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatemotionelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatemotionelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatemotionelement > index.md --- title: SVGAnimateMotionElement slug: Web/API/SVGAnimateMotionElement page-type: web-api-interface browser-compat: api.SVGAnimateMotionElement --- {{APIRef("SVG")}} The **`SVGAnimateMotionElement`** interface corresponds to the {{SVGElement("animateMotion")}} element. {{InheritanceDiagram}} ## Instance properties _This interface has no properties but inherits properties from its parent, {{domxref("SVGAnimationElement")}}._ ## Instance methods _This interface has no methods but inherits methods from its parent, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}