Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimatetransformelement > index.md --- title: SVGAnimateTransformElement slug: Web/API/SVGAnimateTransformElement page-type: web-api-interface browser-compat: api.SVGAnimateTransformElement --- {{APIRef("SVG")}} The `SVGAnimateTransformElement` interface corresponds to the {{SVGElement("animateTransform")}} element. {{InheritanceDiagram}} ## Instance properties _This interface has no properties but inherits properties from its parent, {{domxref("SVGAnimationElement")}}._ ## Instance methods _This interface has no methods but inherits methods from its parent, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}