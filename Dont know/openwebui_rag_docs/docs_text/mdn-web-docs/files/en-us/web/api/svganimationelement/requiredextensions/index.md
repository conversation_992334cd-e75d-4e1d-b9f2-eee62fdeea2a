Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > requiredextensions > index.md --- title: "SVGAnimationElement: requiredExtensions property" short-title: requiredExtensions slug: Web/API/SVGAnimationElement/requiredExtensions page-type: web-api-instance-property browser-compat: api.SVGAnimationElement.requiredExtensions --- {{APIRef("SVG")}} The **`requiredExtensions`** read-only property of the {{domxref("SVGAnimationElement")}} interface reflects the {{SVGAttr("requiredExtensions")}} attribute of the given element. ## Value An {{domxref("SVGStringList")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}