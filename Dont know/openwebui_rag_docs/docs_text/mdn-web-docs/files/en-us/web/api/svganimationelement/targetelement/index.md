Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svganimationelement > targetelement > index.md --- title: "SVGAnimationElement: targetElement property" short-title: targetElement slug: Web/API/SVGAnimationElement/targetElement page-type: web-api-instance-property browser-compat: api.SVGAnimationElement.targetElement --- {{APIRef("SVG")}} The **`targetElement`** read-only property of the {{domxref("SVGAnimationElement")}} interface refers to the element which is being animated. If no target element is being animated (for example, because the {{SVGAttr("href")}} attribute specifies an unknown element), the value returned is `null`. ## Value An {{domxref("SVGElement")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}