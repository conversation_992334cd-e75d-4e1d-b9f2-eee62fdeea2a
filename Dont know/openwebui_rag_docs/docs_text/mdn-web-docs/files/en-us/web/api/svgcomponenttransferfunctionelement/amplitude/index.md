Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > amplitude > index.md --- title: "SVGComponentTransferFunctionElement: amplitude property" short-title: amplitude slug: Web/API/SVGComponentTransferFunctionElement/amplitude page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.amplitude --- {{APIRef("SVG")}} The **`amplitude`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("amplitude")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}