Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > exponent > index.md --- title: "SVGComponentTransferFunctionElement: exponent property" short-title: exponent slug: Web/API/SVGComponentTransferFunctionElement/exponent page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.exponent --- {{APIRef("SVG")}} The **`exponent`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("exponent")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}