Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > intercept > index.md --- title: "SVGComponentTransferFunctionElement: intercept property" short-title: intercept slug: Web/API/SVGComponentTransferFunctionElement/intercept page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.intercept --- {{APIRef("SVG")}} The **`intercept`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("intercept")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}