Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > offset > index.md --- title: "SVGComponentTransferFunctionElement: offset property" short-title: offset slug: Web/API/SVGComponentTransferFunctionElement/offset page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.offset --- {{APIRef("SVG")}} The **`offset`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("offset")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}