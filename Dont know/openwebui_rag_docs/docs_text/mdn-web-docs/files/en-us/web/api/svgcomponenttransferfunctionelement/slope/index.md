Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > slope > index.md --- title: "SVGComponentTransferFunctionElement: slope property" short-title: slope slug: Web/API/SVGComponentTransferFunctionElement/slope page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.slope --- {{APIRef("SVG")}} The **`slope`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("slope")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}