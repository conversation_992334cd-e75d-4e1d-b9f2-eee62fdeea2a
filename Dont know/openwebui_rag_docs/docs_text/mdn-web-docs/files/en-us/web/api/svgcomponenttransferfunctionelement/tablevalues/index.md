Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > tablevalues > index.md --- title: "SVGComponentTransferFunctionElement: tableValues property" short-title: tableValues slug: Web/API/SVGComponentTransferFunctionElement/tableValues page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.tableValues --- {{APIRef("SVG")}} The **`tableValues`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("tableValues")}} attribute of the given element. ## Value An {{domxref("SVGAnimatedNumberList")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}