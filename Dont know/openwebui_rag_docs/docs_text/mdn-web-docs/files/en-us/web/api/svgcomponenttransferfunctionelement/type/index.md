Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md

Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md Path: mdn-web-docs > files > en-us > web > api > svgcomponenttransferfunctionelement > type > index.md --- title: "SVGComponentTransferFunctionElement: type property" short-title: type slug: Web/API/SVGComponentTransferFunctionElement/type page-type: web-api-instance-property browser-compat: api.SVGComponentTransferFunctionElement.type --- {{APIRef("SVG")}} The **`type`** read-only property of the {{domxref("SVGComponentTransferFunctionElement")}} interface reflects the {{SVGAttr("type")}} attribute of the given element. It takes one of the [`SVG_FECOMPONENTTRANSFER_TYPE_*`](/en-US/docs/Web/API/SVGComponentTransferFunctionElement#static_properties) constants defined on this interface. ## Value An {{domxref("SVGAnimatedEnumeration")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}