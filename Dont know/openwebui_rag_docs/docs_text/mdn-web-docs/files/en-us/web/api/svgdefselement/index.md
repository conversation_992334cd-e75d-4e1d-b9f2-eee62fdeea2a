Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdefselement > index.md --- title: SVGDefsElement slug: Web/API/SVGDefsElement page-type: web-api-interface browser-compat: api.SVGDefsElement --- {{APIRef("SVG")}} The **`SVGDefsElement`** interface corresponds to the {{SVGElement("defs")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent, {{domxref("SVGGraphicsElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent, {{domxref("SVGGraphicsElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}