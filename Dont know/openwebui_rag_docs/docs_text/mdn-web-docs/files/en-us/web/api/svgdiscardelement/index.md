Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgdiscardelement > index.md --- title: SVGDiscardElement slug: Web/API/SVGDiscardElement page-type: web-api-interface status: - experimental browser-compat: api.SVGDiscardElement --- {{APIRef("SVG")}}{{SeeCompatTable}} The **`SVGDiscardElement`** interface is an interface for the {{SVGElement("discard")}} element. Note that it does not provide access to the specific attributes of the {{SVGElement("discard")}} element (`begin` and `href`). {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGAnimationElement")}}._ ## Instance methods _Inherits methods from its parent interface, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("discard")}} SVG element