Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md

Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md Path: mdn-web-docs > files > en-us > web > api > svgelement > autofocus > index.md --- title: "SVGElement: autofocus property" short-title: autofocus slug: Web/API/SVGElement/autofocus page-type: web-api-instance-property browser-compat: api.SVGElement.autofocus --- {{APIRef("SVG")}} The **`autofocus`** property of the {{domxref("SVGElement")}} interface contains a boolean value reflecting the [`autofocus`](/en-US/docs/Web/HTML/Reference/Global_attributes/autofocus) HTML global attribute. It indicates whether the SVG element should be focused when the page loads or when the element becomes shown if the SVG element is inside a {{htmlelement("dialog")}} or a [popover](/en-US/docs/Web/HTML/Reference/Global_attributes/popover). ## Value A boolean value. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGElement.focus")}} - {{domxref("HTMLElement.autofocus")}} - [Popover API](/en-US/docs/Web/API/Popover_API)