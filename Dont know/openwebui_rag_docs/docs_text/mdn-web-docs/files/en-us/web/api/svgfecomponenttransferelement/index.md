Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfecomponenttransferelement > index.md --- title: SVGFEComponentTransferElement slug: Web/API/SVGFEComponentTransferElement page-type: web-api-interface browser-compat: api.SVGFEComponentTransferElement --- {{APIRef("SVG")}} The **`SVGFEComponentTransferElement`** interface corresponds to the {{SVGElement("feComponentTransfer")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEComponentTransferElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEComponentTransferElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFEComponentTransferElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEComponentTransferElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEComponentTransferElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEComponentTransferElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feComponentTransfer")}}