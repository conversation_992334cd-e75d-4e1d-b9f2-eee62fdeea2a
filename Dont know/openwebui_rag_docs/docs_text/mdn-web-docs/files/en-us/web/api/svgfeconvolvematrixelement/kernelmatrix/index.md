Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelmatrix > index.md --- title: "SVGFEConvolveMatrixElement: kernelMatrix property" short-title: kernelMatrix slug: Web/API/SVGFEConvolveMatrixElement/kernelMatrix page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.kernelMatrix --- {{APIRef("SVG")}} The **`kernelMatrix`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("kernelMatrix")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. ## Value An {{domxref("SVGAnimatedNumberList")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedNumberList")}}