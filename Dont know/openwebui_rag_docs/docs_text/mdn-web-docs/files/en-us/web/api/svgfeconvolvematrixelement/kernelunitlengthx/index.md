Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthx > index.md --- title: "SVGFEConvolveMatrixElement: kernelUnitLengthX property" short-title: kernelUnitLengthX slug: Web/API/SVGFEConvolveMatrixElement/kernelUnitLengthX page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.kernelUnitLengthX --- {{APIRef("SVG")}} The **`kernelUnitLengthX`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("kernelUnitLength")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. It specifies the length in user units for each cell of the convolution matrix along the X-axis. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedNumber")}}