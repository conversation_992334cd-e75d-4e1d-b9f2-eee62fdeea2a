Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > kernelunitlengthy > index.md --- title: "SVGFEConvolveMatrixElement: kernelUnitLengthY property" short-title: kernelUnitLengthY slug: Web/API/SVGFEConvolveMatrixElement/kernelUnitLengthY page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.kernelUnitLengthY --- {{APIRef("SVG")}} The **`kernelUnitLengthY`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("kernelUnitLength")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. It specifies the length in user units for each cell of the convolution matrix along the Y-axis. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedNumber")}}