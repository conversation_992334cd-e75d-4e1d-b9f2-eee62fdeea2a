Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > orderx > index.md --- title: "SVGFEConvolveMatrixElement: orderX property" short-title: orderX slug: Web/API/SVGFEConvolveMatrixElement/orderX page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.orderX --- {{APIRef("SVG")}} The **`orderX`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("order")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. It specifies how many cells (or elements) are present in each row of the kernel matrix along the X-axis. ## Value An {{domxref("SVGAnimatedInteger")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedInteger")}}