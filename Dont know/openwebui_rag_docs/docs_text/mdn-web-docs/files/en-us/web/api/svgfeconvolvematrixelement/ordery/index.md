Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > ordery > index.md --- title: "SVGFEConvolveMatrixElement: orderY property" short-title: orderY slug: Web/API/SVGFEConvolveMatrixElement/orderY page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.orderY --- {{APIRef("SVG")}} The **`orderY`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("order")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. It specifies how many cells (or elements) are present in each row of the kernel matrix along the Y-axis. ## Value An {{domxref("SVGAnimatedInteger")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedInteger")}}