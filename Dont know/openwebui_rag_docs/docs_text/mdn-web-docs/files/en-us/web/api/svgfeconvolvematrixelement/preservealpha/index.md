Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > preservealpha > index.md --- title: "SVGFEConvolveMatrixElement: preserveAlpha property" short-title: preserveAlpha slug: Web/API/SVGFEConvolveMatrixElement/preserveAlpha page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.preserveAlpha --- {{APIRef("SVG")}} The **`preserveAlpha`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("preserveAlpha")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. ## Value An {{domxref("SVGAnimatedBoolean")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedBoolean")}}