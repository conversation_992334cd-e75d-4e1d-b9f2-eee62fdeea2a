Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > result > index.md --- title: "SVGFEConvolveMatrixElement: result property" short-title: result slug: Web/API/SVGFEConvolveMatrixElement/result page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.result --- {{APIRef("SVG")}} The **`result`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface describes the assigned name of an SVG filter primitive as a {{domxref("SVGAnimatedString")}}. It reflects the {{SVGElement("feConvolveMatrix")}} element's {{SVGAttr("result")}} attribute. The `<feConvolveMatrix>` filter applies a matrix convolution effect, combining pixels in the input image with neighboring pixels to produce a convolution effect such as blurring, edge detection, sharpening, embossing or beveling. The attribute value is a {{cssxref("custom-ident")}}. If supplied, then graphics that result from processing this filter primitive can be referenced by an {{SVGAttr("in")}} attribute on a subsequent filter primitive within the same {{SVGElement("filter")}} element. If no `result` attribute is defined, the filter's `result.baseVal` and `result.animVal` are empty strings, and the output of the `<feConvolveMatrix>` filter will only be available for re-use as the implicit input into the next filter primitive if that filter primitive provides no value for its `in` attribute. ## Value An {{domxref("SVGAnimatedString")}}. ## Example ```js const feConvolveMatrixElement = document.querySelector("feConvolveMatrix"); const filterName = feConvolveMatrixElement.result; console.log(filterName.baseVal); // the filter's assigned name ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEConvolveMatrixElement.in1")}} - {{cssxref("custom-ident")}} data type - [CSS filter effects](/en-US/docs/Web/CSS/CSS_filter_effects) module - SVG {{SVGElement("filter")}} element, SVG {{SVGAttr("filter")}} attribute in [SVG](/en-US/docs/Web/SVG) - [Applying SVG effects to HTML content](/en-US/docs/Web/SVG/Guides/Applying_SVG_effects_to_HTML_content)