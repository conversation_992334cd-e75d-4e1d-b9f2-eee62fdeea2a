Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targetx > index.md --- title: "SVGFEConvolveMatrixElement: targetX property" short-title: targetX slug: Web/API/SVGFEConvolveMatrixElement/targetX page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.targetX --- {{APIRef("SVG")}} The **`targetX`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("targetX")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. ## Value An {{domxref("SVGAnimatedInteger")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedInteger")}}