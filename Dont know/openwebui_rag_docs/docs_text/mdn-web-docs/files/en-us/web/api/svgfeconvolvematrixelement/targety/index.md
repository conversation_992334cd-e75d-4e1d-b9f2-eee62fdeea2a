Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > targety > index.md --- title: "SVGFEConvolveMatrixElement: targetY property" short-title: targetY slug: Web/API/SVGFEConvolveMatrixElement/targetY page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.targetY --- {{APIRef("SVG")}} The **`targetY`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface reflects the {{SVGAttr("targetY")}} attribute of the given {{SVGElement("feConvolveMatrix")}} element. ## Value An {{domxref("SVGAnimatedInteger")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedInteger")}}