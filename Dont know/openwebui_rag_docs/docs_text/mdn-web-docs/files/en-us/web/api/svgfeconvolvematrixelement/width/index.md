Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeconvolvematrixelement > width > index.md --- title: "SVGFEConvolveMatrixElement: width property" short-title: width slug: Web/API/SVGFEConvolveMatrixElement/width page-type: web-api-instance-property browser-compat: api.SVGFEConvolveMatrixElement.width --- {{APIRef("SVG")}} The **`width`** read-only property of the {{domxref("SVGFEConvolveMatrixElement")}} interface describes the horizontal size of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGElement("feConvolveMatrix")}} element's {{SVGAttr("width")}} filter primitive attribute. The `<feConvolveMatrix>` filter applies a matrix convolution effect, combining pixels in the input image with neighboring pixels to produce a convolution effect such as blurring, edge detection, sharpening, embossing or beveling. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or a [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage) relative to the width of the filter region. The default value is `100%`. The property value is a length in user coordinate system units. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feConvolveMatrix = document.querySelector("feConvolveMatrix"); const horizontalSize = feConvolveMatrix.width; console.log(horizontalSize.baseVal.value); // the `width` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEConvolveMatrixElement.height")}} - [CSS filter effects](/en-US/docs/Web/CSS/CSS_filter_effects) module - SVG {{SVGElement("filter")}} element, SVG {{SVGAttr("filter")}} attribute in [SVG](/en-US/docs/Web/SVG) - [Applying SVG effects to HTML content](/en-US/docs/Web/SVG/Guides/Applying_SVG_effects_to_HTML_content)