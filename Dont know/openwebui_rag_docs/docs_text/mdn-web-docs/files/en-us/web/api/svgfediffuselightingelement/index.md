Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > index.md --- title: SVGFEDiffuseLightingElement slug: Web/API/SVGFEDiffuseLightingElement page-type: web-api-interface browser-compat: api.SVGFEDiffuseLightingElement --- {{APIRef("SVG")}} The **`SVGFEDiffuseLightingElement`** interface corresponds to the {{SVGElement("feDiffuseLighting")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEDiffuseLightingElement.diffuseConstant")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("diffuseConstant")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.kernelUnitLengthX")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the X component of the {{SVGAttr("kernelUnitLength")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.kernelUnitLengthY")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the Y component of the {{SVGAttr("kernelUnitLength")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.surfaceScale")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("surfaceScale")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEDiffuseLightingElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feDiffuseLighting")}}