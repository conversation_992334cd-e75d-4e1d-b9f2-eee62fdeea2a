Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthx > index.md --- title: "SVGFEDiffuseLightingElement: kernelUnitLengthX property" short-title: kernelUnitLengthX slug: Web/API/SVGFEDiffuseLightingElement/kernelUnitLengthX page-type: web-api-instance-property browser-compat: api.SVGFEDiffuseLightingElement.kernelUnitLengthX --- {{APIRef("SVG")}} The **`kernelUnitLengthX`** read-only property of the {{domxref("SVGFEDiffuseLightingElement")}} interface reflects the X component of the {{SVGAttr("kernelUnitLength")}} attribute of the given {{SVGElement("feDiffuseLighting")}} element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedNumber")}}