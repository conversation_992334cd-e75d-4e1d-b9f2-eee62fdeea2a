Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > kernelunitlengthy > index.md --- title: "SVGFEDiffuseLightingElement: kernelUnitLengthY property" short-title: kernelUnitLengthY slug: Web/API/SVGFEDiffuseLightingElement/kernelUnitLengthY page-type: web-api-instance-property browser-compat: api.SVGFEDiffuseLightingElement.kernelUnitLengthY --- {{APIRef("SVG")}} The **`kernelUnitLengthY`** read-only property of the {{domxref("SVGFEDiffuseLightingElement")}} interface reflects the Y component of the {{SVGAttr("kernelUnitLength")}} attribute of the given {{SVGElement("feDiffuseLighting")}} element. ## Value An {{domxref("SVGAnimatedNumber")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedNumber")}}