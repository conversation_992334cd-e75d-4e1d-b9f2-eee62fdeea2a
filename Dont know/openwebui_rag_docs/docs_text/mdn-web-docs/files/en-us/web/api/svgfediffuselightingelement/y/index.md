Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfediffuselightingelement > y > index.md --- title: "SVGFEDiffuseLightingElement: y property" short-title: y slug: Web/API/SVGFEDiffuseLightingElement/y page-type: web-api-instance-property browser-compat: api.SVGFEDiffuseLightingElement.y --- {{APIRef("SVG")}} The **`y`** read-only property of the {{domxref("SVGFEDiffuseLightingElement")}} interface describes the vertical coordinate of the position of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGElement("feDiffuseLighting")}} element's {{SVGAttr("y")}} filter primitive attribute value. The filter lights an image using the alpha channel as a bump map. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage). The `<coordinate>` is a length in the user coordinate system that is the given distance from the origin of the filter along the y-axis. If the `y` attribute is a percent value, the property value is a relative to the height of the filter region in user coordinate system units. The default value is `0`. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feDiffuseLighting = document.querySelector("feDiffuseLighting"); const topPosition = feDiffuseLighting.y; console.log(topPosition.baseVal.value); // the `y` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEDiffuseLightingElement.x")}} - {{domxref("SVGFESpecularLightingElement")}} - {{SVGElement("feSpecularLighting")}} - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("mix-blend-mode")}} property