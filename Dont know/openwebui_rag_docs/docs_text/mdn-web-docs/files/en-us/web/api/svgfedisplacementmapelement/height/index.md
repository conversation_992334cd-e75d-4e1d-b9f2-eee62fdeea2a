Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedisplacementmapelement > height > index.md --- title: "SVGFEDisplacementMapElement: height property" short-title: height slug: Web/API/SVGFEDisplacementMapElement/height page-type: web-api-instance-property browser-compat: api.SVGFEDisplacementMapElement.height --- {{APIRef("SVG")}} The **`height`** read-only property of the {{domxref("SVGFEDisplacementMapElement")}} interface describes the vertical size of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGElement("feDisplacementMap")}} element's {{SVGAttr("height")}} filter primitive attribute. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or a [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage) relative to the height of the filter region. The default value is `100%`. The property value is a length in user coordinate system units. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feDisplacementMap = document.querySelector("feDisplacementMap"); const verticalSize = feDisplacementMap.height; console.log(verticalSize.baseVal.value); // the `height` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEDisplacementMapElement.width")}} - {{domxref("SVGFEImageElement")}} API and {{SVGElement("feImage")}} element - {{domxref("SVGFETurbulenceElement")}} API and {{SVGElement("feTurbulence")}} element - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("mix-blend-mode")}} property