Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedistantlightelement > index.md --- title: SVGFEDistantLightElement slug: Web/API/SVGFEDistantLightElement page-type: web-api-interface browser-compat: api.SVGFEDistantLightElement --- {{APIRef("SVG")}} The **`SVGFEDistantLightElement`** interface corresponds to the {{SVGElement("feDistantLight")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEDistantLightElement.azimuth")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("azimuth")}} attribute of the given element. - {{domxref("SVGFEDistantLightElement.elevation")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("elevation")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feDistantLight")}}