Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > index.md --- title: SVGFEDropShadowElement slug: Web/API/SVGFEDropShadowElement page-type: web-api-interface browser-compat: api.SVGFEDropShadowElement --- {{APIRef("SVG")}} The **`SVGFEDropShadowElement`** interface corresponds to the {{SVGElement("feDropShadow")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEDropShadowElement.dx")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("dx")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.dy")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("dy")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.stdDeviationX")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the (possibly automatically computed) X component of the {{SVGAttr("stdDeviation")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.stdDeviationY")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the (possibly automatically computed) Y component of the {{SVGAttr("stdDeviation")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEDropShadowElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface also inherits methods of its parent, {{domxref("SVGElement")}}._ - {{domxref("SVGFEDropShadowElement.setStdDeviation()")}} - : Sets the values for the `stdDeviation` attribute. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feDropShadow")}}