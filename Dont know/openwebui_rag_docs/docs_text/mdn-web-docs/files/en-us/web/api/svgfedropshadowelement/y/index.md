Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgfedropshadowelement > y > index.md --- title: "SVGFEDropShadowElement: y property" short-title: y slug: Web/API/SVGFEDropShadowElement/y page-type: web-api-instance-property browser-compat: api.SVGFEDropShadowElement.y --- {{APIRef("SVG")}} The **`y`** read-only property of the {{domxref("SVGFEDropShadowElement")}} interface describes the vertical coordinate of the position of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGAttr("y")}} attribute of the {{SVGElement("feDropShadow")}} element, which creates a drop shadow of an input image. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage). The `<coordinate>` is a length in the user coordinate system that is the given distance from the origin of the filter along the y-axis. If the `y` attribute is a percent value, the property value is a relative to the height of the filter region in user coordinate system units. The default value is `0`. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feDropShadow = document.querySelector("feDropShadow"); const topPosition = feDropShadow.y; console.log(topPosition.baseVal.value); // the `y` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEDropShadowElement.x")}} - CSS {{cssxref("filter-function/drop-shadow", "drop-shadow()")}} function - CSS {{cssxref("box-shadow")}} property - CSS {{cssxref("text-shadow")}} property - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("mix-blend-mode")}} property