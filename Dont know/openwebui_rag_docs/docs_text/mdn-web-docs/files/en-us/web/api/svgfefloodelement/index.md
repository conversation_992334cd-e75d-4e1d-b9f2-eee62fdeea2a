Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > index.md --- title: SVGFEFloodElement slug: Web/API/SVGFEFloodElement page-type: web-api-interface browser-compat: api.SVGFEFloodElement --- {{APIRef("SVG")}} The **`SVGFEFloodElement`** interface corresponds to the {{SVGElement("feFlood")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEFloodElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEFloodElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEFloodElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEFloodElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEFloodElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feFlood")}}