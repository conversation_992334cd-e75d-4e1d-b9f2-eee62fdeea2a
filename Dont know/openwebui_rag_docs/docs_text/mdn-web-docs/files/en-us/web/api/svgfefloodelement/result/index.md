Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md Path: mdn-web-docs > files > en-us > web > api > svgfefloodelement > result > index.md --- title: "SVGFEFloodElement: result property" short-title: result slug: Web/API/SVGFEFloodElement/result page-type: web-api-instance-property browser-compat: api.SVGFEFloodElement.result --- {{APIRef("SVG")}} The **`result`** read-only property of the {{domxref("SVGFEFloodElement")}} interface describes the assigned name of an SVG filter primitive as a {{domxref("SVGAnimatedString")}}. It reflects the {{SVGAttr("result")}} attribute of the {{SVGElement("feFlood")}} element, which fills an SVG filter subregion with the color and opacity defined by {{SVGAttr("flood-color")}} and {{SVGAttr("flood-opacity")}}. The attribute value is a {{cssxref("custom-ident")}}. If supplied, then graphics that result from processing this filter primitive can be referenced by an {{SVGAttr("in")}} attribute on a subsequent filter primitive within the same {{SVGElement("filter")}} element. If no `result` attribute is defined, the filter's `result.baseVal` and `result.animVal` are empty strings, and the output of the `<feFlood>` filter will only be available for re-use as the implicit input into the next filter primitive if that filter primitive provides no value for its `in` attribute. ## Value An {{domxref("SVGAnimatedString")}}. ## Example ```js const feFloodElement = document.querySelector("feFlood"); const filterName = feFloodElement.result; console.log(filterName.baseVa); // the filter's assigned name ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{cssxref("custom-ident")}} data type - [SVG filter tutorial](/en-US/docs/Web/SVG/Guides/SVG_filters) - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("mix-blend-mode")}} property