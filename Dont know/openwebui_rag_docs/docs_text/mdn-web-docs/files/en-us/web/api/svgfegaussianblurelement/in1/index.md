Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > in1 > index.md --- title: "SVGFEGaussianBlurElement: in1 property" short-title: in1 slug: Web/API/SVGFEGaussianBlurElement/in1 page-type: web-api-instance-property browser-compat: api.SVGFEGaussianBlurElement.in1 --- {{APIRef("SVG")}} The **`in1`** read-only property of the {{domxref("SVGFEGaussianBlurElement")}} interface reflects the {{SVGAttr("in")}} attribute of the given {{SVGElement("feGaussianBlur")}} element. ## Value An {{domxref("SVGAnimatedString")}} object. ## Examples In this example, two {{SVGElement("feGaussianBlur")}} elements are defined in a filter, each with a different `in` attribute. ```html <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"> <defs> <filter id="gaussian-blur-filter"> <!-- Gaussian blur applied to the SourceGraphic --> <feGaussianBlur in="SourceGraphic" stdDeviation="5" result="blurred-source" /> <!-- Gaussian blur applied to the BackgroundImage --> <feGaussianBlur in="BackgroundImage" stdDeviation="10" result="blurred-background" /> </filter> </defs> <!-- Rectangle with SourceGraphic blur effect --> <rect x="20" y="20" width="100" height="100" style="fill:rebeccapurple;" filter="url(#gaussian-blur-filter)" /> <!-- Circle with BackgroundImage blur effect --> <circle cx="150" cy="100" r="50" style="fill:hotpink;" filter="url(#gaussian-blur-filter)" /> </svg> ``` We can access the `in` attribute: ```js // Get all feGaussianBlur elements const gaussianBlurs = document.querySelectorAll("feGaussianBlur"); // Access the 'in' attribute values console.log(gaussianBlurs[0].in1.baseVal); // Output: "SourceGraphic" console.log(gaussianBlurs[1].in1.baseVal); // Output: "BackgroundImage" ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAnimatedString")}}