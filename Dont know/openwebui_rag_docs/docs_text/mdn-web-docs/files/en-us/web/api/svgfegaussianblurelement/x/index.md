Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgfegaussianblurelement > x > index.md --- title: "SVGFEGaussianBlurElement: x property" short-title: x slug: Web/API/SVGFEGaussianBlurElement/x page-type: web-api-instance-property browser-compat: api.SVGFEGaussianBlurElement.x --- {{APIRef("SVG")}} The **`x`** read-only property of the {{domxref("SVGFEGaussianBlurElement")}} interface describes the horizontal coordinate of the position of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGAttr("x")}} attribute of the {{SVGElement("feGaussianBlur")}} element, which blurs an input image. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage). The `<coordinate>` is a length in the user coordinate system that is the given distance from the origin of the user coordinate system along the x-axis. If the `x` attribute is a percent value, the property value is relative to the width of the filter region in user coordinate system units. The default value is `0`. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feGaussianBlur = document.querySelector("feGaussianBlur"); const leftPosition = feGaussianBlur.x; console.log(leftPosition.baseVal.value); // the `x` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEGaussianBlurElement.y")}} - [SVG filter tutorial](/en-US/docs/Web/SVG/Guides/SVG_filters) - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("filter-function/blur", "blur()")}} function - [CSS filter effects](/en-US/docs/Web/CSS/CSS_filter_effects) module- CSS {{cssxref("mix-blend-mode")}} property