Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeimageelement > height > index.md --- title: "SVGFEImageElement: height property" short-title: height slug: Web/API/SVGFEImageElement/height page-type: web-api-instance-property browser-compat: api.SVGFEImageElement.height --- {{APIRef("SVG")}} The **`height`** read-only property of the {{domxref("SVGFEImageElement")}} interface describes the vertical size of an SVG filter primitive as a {{domxref("SVGAnimatedLength")}}. It reflects the {{SVGAttr("height")}} attribute of the {{SVGElement("feImage")}} element, which fetches image data from an external source and provides the pixel data as output. The attribute is a [`<length>`](/en-US/docs/Web/SVG/Guides/Content_type#length) or a [`<percentage>`](/en-US/docs/Web/SVG/Guides/Content_type#percentage) relative to the height of the filter region. The default value is `100%`. The property value is a length in user coordinate system units. ## Value An {{domxref("SVGAnimatedLength")}}. ## Example ```js const feImage = document.querySelector("feImage"); const verticalSize = feImage.height; console.log(verticalSize.baseVal.value); // the `height` value ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGFEImageElement.width")}} - [SVG tutorial: Filter effects](/en-US/docs/Web/SVG/Tutorials/SVG_from_scratch/Filter_effects) - [SVG Filter primitive attributes](/en-US/docs/Web/SVG/Reference/Attribute#filters_attributes) - CSS {{cssxref("blend-mode")}} data type - CSS {{cssxref("mix-blend-mode")}} property