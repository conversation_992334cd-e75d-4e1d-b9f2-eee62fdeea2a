Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergeelement > index.md --- title: SVGFEMergeElement slug: Web/API/SVGFEMergeElement page-type: web-api-interface browser-compat: api.SVGFEMergeElement --- {{APIRef("SVG")}} The **`SVGFEMergeElement`** interface corresponds to the {{SVGElement("feMerge")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEMergeElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEMergeElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEMergeElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEMergeElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEMergeElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feMerge")}}