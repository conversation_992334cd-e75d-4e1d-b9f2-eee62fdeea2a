Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfemergenodeelement > index.md --- title: SVGFEMergeNodeElement slug: Web/API/SVGFEMergeNodeElement page-type: web-api-interface browser-compat: api.SVGFEMergeNodeElement --- {{APIRef("SVG")}} The **`SVGFEMergeNodeElement`** interface corresponds to the {{SVGElement("feMergeNode")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEMergeNodeElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feMergeNode")}}