Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfeoffsetelement > index.md --- title: SVGFEOffsetElement slug: Web/API/SVGFEOffsetElement page-type: web-api-interface browser-compat: api.SVGFEOffsetElement --- {{APIRef("SVG")}} The **`SVGFEOffsetElement`** interface corresponds to the {{SVGElement("feOffset")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEOffsetElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.dx")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("dx")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.dy")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("dy")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEOffsetElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feOffset")}}