Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfepointlightelement > index.md --- title: SVGFEPointLightElement slug: Web/API/SVGFEPointLightElement page-type: web-api-interface browser-compat: api.SVGFEPointLightElement --- {{APIRef("SVG")}} The **`SVGFEPointLightElement`** interface corresponds to the {{SVGElement("fePointLight")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFEPointLightElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFEPointLightElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. - {{domxref("SVGFEPointLightElement.z")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("z")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("fePointLight")}}