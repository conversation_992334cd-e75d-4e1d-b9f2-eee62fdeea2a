Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespecularlightingelement > index.md --- title: SVGFESpecularLightingElement slug: Web/API/SVGFESpecularLightingElement page-type: web-api-interface browser-compat: api.SVGFESpecularLightingElement --- {{APIRef("SVG")}} The **`SVGFESpecularLightingElement`** interface corresponds to the {{SVGElement("feSpecularLighting")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFESpecularLightingElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.kernelUnitLengthX")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the X component of the {{SVGAttr("kernelUnitLength")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.kernelUnitLengthY")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the Y component of the {{SVGAttr("kernelUnitLength")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.specularConstant")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("specularConstant")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.specularExponent")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("specularExponent")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.surfaceScale")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("surfaceScale")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFESpecularLightingElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feSpecularLighting")}}