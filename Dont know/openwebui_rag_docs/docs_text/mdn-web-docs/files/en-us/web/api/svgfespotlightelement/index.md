Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfespotlightelement > index.md --- title: SVGFESpotLightElement slug: Web/API/SVGFESpotLightElement page-type: web-api-interface browser-compat: api.SVGFESpotLightElement --- {{APIRef("SVG")}} The **`SVGFESpotLightElement`** interface corresponds to the {{SVGElement("feSpotLight")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFESpotLightElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.z")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("z")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.pointsAtX")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("pointsAtX")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.pointsAtY")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("pointsAtY")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.pointsAtZ")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("pointsAtZ")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.specularExponent")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("specularExponent")}} attribute of the given element. - {{domxref("SVGFESpotLightElement.limitingConeAngle")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("limitingConeAngle")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feSpotLight")}}