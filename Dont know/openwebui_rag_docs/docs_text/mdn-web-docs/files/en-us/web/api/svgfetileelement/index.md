Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgfetileelement > index.md --- title: SVGFETileElement slug: Web/API/SVGFETileElement page-type: web-api-interface browser-compat: api.SVGFETileElement --- {{APIRef("SVG")}} The **`SVGFETileElement`** interface corresponds to the {{SVGElement("feTile")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGFETileElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given element. - {{domxref("SVGFETileElement.in1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("in")}} attribute of the given element. - {{domxref("SVGFETileElement.result")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} corresponding to the {{SVGAttr("result")}} attribute of the given element. - {{domxref("SVGFETileElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given element. - {{domxref("SVGFETileElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given element. - {{domxref("SVGFETileElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given element. ## Instance methods _This interface does not provide any specific methods, but implements those of its parent, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("feTile")}}