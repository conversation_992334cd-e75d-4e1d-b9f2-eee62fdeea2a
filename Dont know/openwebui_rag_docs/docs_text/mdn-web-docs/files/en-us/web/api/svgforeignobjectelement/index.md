Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgforeignobjectelement > index.md --- title: SVGForeignObjectElement slug: Web/API/SVGForeignObjectElement page-type: web-api-interface browser-compat: api.SVGForeignObjectElement --- {{APIRef("SVG")}} The **`SVGForeignObjectElement`** interface provides access to the properties of {{SVGElement("foreignObject")}} elements, as well as methods to manipulate them. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGGraphicsElement")}}._ - {{domxref("SVGForeignObjectElement.x")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given {{SVGElement("foreignObject")}} element. - {{domxref("SVGForeignObjectElement.y")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given {{SVGElement("foreignObject")}} element. - {{domxref("SVGForeignObjectElement.width")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given {{SVGElement("foreignObject")}} element. - {{domxref("SVGForeignObjectElement.height")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given {{SVGElement("foreignObject")}} element. ## Instance methods _This interface has no methods but inherits methods from its parent, {{domxref("SVGGraphicsElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("foreignObject")}}