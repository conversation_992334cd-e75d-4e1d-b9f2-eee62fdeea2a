Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md Path: mdn-web-docs > files > en-us > web > api > svggelement > index.md --- title: SVGGElement slug: Web/API/SVGGElement page-type: web-api-interface browser-compat: api.SVGGElement --- {{APIRef("SVG")}} The **`SVGGElement`** interface corresponds to the {{SVGElement("g")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGGraphicsElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGGraphicsElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}