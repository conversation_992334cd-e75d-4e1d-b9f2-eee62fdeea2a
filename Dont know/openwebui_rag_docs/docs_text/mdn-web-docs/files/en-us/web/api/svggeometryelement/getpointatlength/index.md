Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md

Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > getpointatlength > index.md --- title: "SVGGeometryElement: getPointAtLength() method" short-title: getPointAtLength() slug: Web/API/SVGGeometryElement/getPointAtLength page-type: web-api-instance-method browser-compat: api.SVGGeometryElement.getPointAtLength --- {{APIRef("SVG")}} The **`SVGGeometryElement.getPointAtLength()`** method returns the point at a given distance along the path. ## Syntax ```js-nolint getPointAtLength(distance) ``` ### Parameters - `distance` - : A float referring to the distance along the path. ### Return value A {{domxref("DOMPoint")}} indicating the point at a given distance along the path. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}