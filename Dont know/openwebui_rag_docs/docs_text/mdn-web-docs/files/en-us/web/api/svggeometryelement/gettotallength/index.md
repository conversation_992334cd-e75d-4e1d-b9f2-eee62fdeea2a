Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md

Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > gettotallength > index.md --- title: "SVGGeometryElement: getTotalLength() method" short-title: getTotalLength() slug: Web/API/SVGGeometryElement/getTotalLength page-type: web-api-instance-method browser-compat: api.SVGGeometryElement.getTotalLength --- {{APIRef("SVG")}} The **`SVGGeometryElement.getTotalLength()`** method returns the user agent's computed value for the total length of the path in user units. ## Syntax ```js-nolint getTotalLength() ``` ### Parameters None. ### Return value A float indicating the total length of the path in user units. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}