Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md

Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md Path: mdn-web-docs > files > en-us > web > api > svggeometryelement > pathlength > index.md --- title: "SVGGeometryElement: pathLength property" short-title: pathLength slug: Web/API/SVGGeometryElement/pathLength page-type: web-api-instance-property browser-compat: api.SVGGeometryElement.pathLength --- {{APIRef("SVG")}} The **`SVGGeometryElement.pathLength`** property reflects the {{SVGAttr("pathLength")}} attribute and returns the total length of the path, in user units. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}