Path: mdn-web-docs > files > en-us > web > api > svggraphicselement > requiredextensions > index.md

Path: mdn-web-docs > files > en-us > web > api > svggraphicselement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svggraphicselement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svggraphicselement > requiredextensions > index.md Path: mdn-web-docs > files > en-us > web > api > svggraphicselement > requiredextensions > index.md --- title: "SVGGraphicsElement: requiredExtensions property" short-title: requiredExtensions slug: Web/API/SVGGraphicsElement/requiredExtensions page-type: web-api-instance-property browser-compat: api.SVGGraphicsElement.requiredExtensions --- {{APIRef("SVG")}} The **`requiredExtensions`** read-only property of the {{domxref("SVGGraphicsElement")}} interface reflects the {{SVGAttr("requiredExtensions")}} attribute of the given element. ## Value An {{domxref("SVGStringList")}} object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}