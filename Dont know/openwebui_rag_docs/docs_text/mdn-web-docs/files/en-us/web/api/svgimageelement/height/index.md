Path: mdn-web-docs > files > en-us > web > api > svgimageelement > height > index.md

Path: mdn-web-docs > files > en-us > web > api > svgimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > height > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > height > index.md --- title: "SVGImageElement: height property" short-title: height slug: Web/API/SVGImageElement/height page-type: web-api-instance-property browser-compat: api.SVGImageElement.height --- {{APIRef("SVG")}} The **`height`** read-only property of the {{domxref("SVGImageElement")}} interface returns an {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("height")}} attribute of the given {{SVGElement("image")}} element. ## Value An {{domxref("SVGAnimatedLength")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}