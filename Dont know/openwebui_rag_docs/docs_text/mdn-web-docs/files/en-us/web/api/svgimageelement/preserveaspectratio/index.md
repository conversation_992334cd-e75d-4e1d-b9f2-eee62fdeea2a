Path: mdn-web-docs > files > en-us > web > api > svgimageelement > preserveaspectratio > index.md

Path: mdn-web-docs > files > en-us > web > api > svgimageelement > preserveaspectratio > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > preserveaspectratio > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > preserveaspectratio > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > preserveaspectratio > index.md --- title: "SVGImageElement: preserveAspectRatio property" short-title: preserveAspectRatio slug: Web/API/SVGImageElement/preserveAspectRatio page-type: web-api-instance-property browser-compat: api.SVGImageElement.preserveAspectRatio --- {{APIRef("SVG")}} The **`preserveAspectRatio`** read-only property of the {{domxref("SVGImageElement")}} interface returns an {{domxref("SVGAnimatedPreserveAspectRatio")}} corresponding to the {{SVGAttr("preserveAspectRatio")}} attribute of the given {{SVGElement("image")}} element. ## Value An {{domxref("SVGAnimatedPreserveAspectRatio")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}