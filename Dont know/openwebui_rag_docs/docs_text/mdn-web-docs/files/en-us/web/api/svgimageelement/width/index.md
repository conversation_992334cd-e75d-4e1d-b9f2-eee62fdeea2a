Path: mdn-web-docs > files > en-us > web > api > svgimageelement > width > index.md

Path: mdn-web-docs > files > en-us > web > api > svgimageelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > width > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > width > index.md --- title: "SVGImageElement: width property" short-title: width slug: Web/API/SVGImageElement/width page-type: web-api-instance-property browser-compat: api.SVGImageElement.width --- {{APIRef("SVG")}} The **`width`** read-only property of the {{domxref("SVGImageElement")}} interface returns an {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("width")}} attribute of the given {{SVGElement("image")}} element. ## Value An {{domxref("SVGAnimatedLength")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}