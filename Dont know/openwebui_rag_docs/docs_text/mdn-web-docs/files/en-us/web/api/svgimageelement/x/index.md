Path: mdn-web-docs > files > en-us > web > api > svgimageelement > x > index.md

Path: mdn-web-docs > files > en-us > web > api > svgimageelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > x > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > x > index.md --- title: "SVGImageElement: x property" short-title: x slug: Web/API/SVGImageElement/x page-type: web-api-instance-property browser-compat: api.SVGImageElement.x --- {{APIRef("SVG")}} The **`x`** read-only property of the {{domxref("SVGImageElement")}} interface returns an {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x")}} attribute of the given {{SVGElement("image")}} element. ## Value An {{domxref("SVGAnimatedLength")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}