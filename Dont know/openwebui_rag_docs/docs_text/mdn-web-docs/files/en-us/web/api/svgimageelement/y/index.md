Path: mdn-web-docs > files > en-us > web > api > svgimageelement > y > index.md

Path: mdn-web-docs > files > en-us > web > api > svgimageelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > y > index.md Path: mdn-web-docs > files > en-us > web > api > svgimageelement > y > index.md --- title: "SVGImageElement: y property" short-title: y slug: Web/API/SVGImageElement/y page-type: web-api-instance-property browser-compat: api.SVGImageElement.y --- {{APIRef("SVG")}} The **`y`** read-only property of the {{domxref("SVGImageElement")}} interface returns an {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y")}} attribute of the given {{SVGElement("image")}} element. ## Value An {{domxref("SVGAnimatedLength")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}