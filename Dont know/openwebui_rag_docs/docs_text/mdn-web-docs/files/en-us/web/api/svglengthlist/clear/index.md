Path: mdn-web-docs > files > en-us > web > api > svglengthlist > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > svglengthlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > clear > index.md --- title: "SVGLengthList: clear() method" short-title: clear() slug: Web/API/SVGLengthList/clear page-type: web-api-instance-method browser-compat: api.SVGLengthList.clear --- {{APIRef("SVG")}} The **`clear()`** method of the {{domxref("SVGLengthList")}} interface clears all existing items from the list, with the result being an empty list. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ### Exceptions - {{domxref("DOMException")}} `NoModificationAllowedError` - : Thrown when the list is read-only. ## Examples See {{domxref("SVGLengthList")}} for a complete example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}