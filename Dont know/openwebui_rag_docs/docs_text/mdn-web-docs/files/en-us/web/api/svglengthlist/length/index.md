Path: mdn-web-docs > files > en-us > web > api > svglengthlist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > svglengthlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > length > index.md --- title: "SVGLengthList: length property" short-title: length slug: Web/API/SVGLengthList/length page-type: web-api-instance-property browser-compat: api.SVGLengthList.length --- {{APIRef("SVG")}} The **`length`** property of the {{domxref("SVGLengthList")}} interface returns the number of items in the list. It is an alias of {{domxref("SVGLengthList.numberOfItems", "numberOfItems")}} to make SVG lists more [array-like](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array#array-like_objects). ## Value A non-negative integer that represents the number of items in the list. ## Examples See {{domxref("SVGLengthList")}} for a complete example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}