Path: mdn-web-docs > files > en-us > web > api > svglengthlist > numberofitems > index.md

Path: mdn-web-docs > files > en-us > web > api > svglengthlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svglengthlist > numberofitems > index.md --- title: "SVGLengthList: numberOfItems property" short-title: numberOfItems slug: Web/API/SVGLengthList/numberOfItems page-type: web-api-instance-property browser-compat: api.SVGLengthList.numberOfItems --- {{APIRef("SVG")}} The **`numberOfItems`** property of the {{domxref("SVGLengthList")}} interface returns the number of items in the list. {{domxref("SVGLengthList.length", "length")}} is an alias of it. ## Value A non-negative integer that represents the number of items in the list. ## Examples See {{domxref("SVGLengthList")}} for a complete example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}