Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineargradientelement > index.md --- title: SVGLinearGradientElement slug: Web/API/SVGLinearGradientElement page-type: web-api-interface browser-compat: api.SVGLinearGradientElement --- {{APIRef("SVG")}} The **`SVGLinearGradientElement`** interface corresponds to the {{SVGElement("linearGradient")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGGradientElement")}}._ - {{domxref("SVGLinearGradientElement.x1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x1")}} attribute of the given {{SVGElement("linearGradient")}} element. - {{domxref("SVGLinearGradientElement.y1")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y1")}} attribute of the given {{SVGElement("linearGradient")}} element. - {{domxref("SVGLinearGradientElement.x2")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("x2")}} attribute of the given {{SVGElement("linearGradient")}} element. - {{domxref("SVGLinearGradientElement.y2")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("y2")}} attribute of the given {{SVGElement("linearGradient")}} element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGGradientElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}