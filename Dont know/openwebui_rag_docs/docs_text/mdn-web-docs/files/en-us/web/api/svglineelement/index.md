Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md Path: mdn-web-docs > files > en-us > web > api > svglineelement > index.md --- title: SVGLineElement slug: Web/API/SVGLineElement page-type: web-api-interface browser-compat: api.SVGLineElement --- {{APIRef("SVG")}} The **`SVGLineElement`** interface provides access to the properties of {{ SVGElement("line") }} elements, as well as methods to manipulate them. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGGeometryElement")}}._ - {{domxref("SVGLineElement.x1")}} {{ReadOnlyInline}} - : Returns an {{domxref("SVGAnimatedLength")}} that corresponds to attribute {{SVGAttr("x1")}} on the given {{SVGElement("line")}} element. - {{domxref("SVGLineElement.y1")}} {{ReadOnlyInline}} - : Returns an {{domxref("SVGAnimatedLength")}} that corresponds to attribute {{SVGAttr("y1")}} on the given {{SVGElement("line")}} element. - {{domxref("SVGLineElement.x2")}} {{ReadOnlyInline}} - : Returns an {{domxref("SVGAnimatedLength")}} that corresponds to attribute {{SVGAttr("x2")}} on the given {{SVGElement("line")}} element. - {{domxref("SVGLineElement.y2")}} {{ReadOnlyInline}} - : Returns an {{domxref("SVGAnimatedLength")}} that corresponds to attribute {{SVGAttr("y2")}} on the given {{SVGElement("line")}} element. ## Instance methods _Inherits methods from its parent interface, {{domxref("SVGGeometryElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("line")}} SVG Element