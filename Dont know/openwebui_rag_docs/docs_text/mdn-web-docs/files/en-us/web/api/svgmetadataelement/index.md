Path: mdn-web-docs > files > en-us > web > api > svgmetadataelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgmetadataelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmetadataelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmetadataelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmetadataelement > index.md --- title: SVGMetadataElement slug: Web/API/SVGMetadataElement page-type: web-api-interface browser-compat: api.SVGMetadataElement --- {{APIRef("SVG")}} The **`SVGMetadataElement`** interface corresponds to the {{SVGElement("metadata")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}