Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgmpathelement > index.md --- title: SVGMPathElement slug: Web/API/SVGMPathElement page-type: web-api-interface browser-compat: api.SVGMPathElement --- {{APIRef("SVG")}} The **`SVGMPathElement`** interface corresponds to the {{SVGElement("mpath")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGElement")}}._ - {{domxref("SVGMPathElement.href")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedString")}} that corresponds to the {{SVGAttr("href")}} or {{SVGAttr("xlink:href")}} {{deprecated_inline}} attribute of the given {{SVGElement("mpath")}} element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}