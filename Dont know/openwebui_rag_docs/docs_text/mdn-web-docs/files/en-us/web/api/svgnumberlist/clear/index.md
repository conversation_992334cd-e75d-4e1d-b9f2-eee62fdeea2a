Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > clear > index.md --- title: "SVGNumberList: clear() method" short-title: clear() slug: Web/API/SVGNumberList/clear page-type: web-api-instance-method browser-compat: api.SVGNumberList.clear --- {{APIRef("SVG")}} The **`clear()`** method of the {{domxref("SVGNumberList")}} interface clears all existing items from the list, with the result being an empty list. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ### Exceptions - {{domxref("DOMException")}} `NoModificationAllowedError` - : Thrown if the {{domxref("SVGNumberList")}} corresponds to a read-only attribute or when the object itself is read-only. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}