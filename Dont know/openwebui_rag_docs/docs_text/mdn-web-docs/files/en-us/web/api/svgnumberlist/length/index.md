Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > length > index.md --- title: "SVGNumberList: length property" short-title: length slug: Web/API/SVGNumberList/length page-type: web-api-instance-property browser-compat: api.SVGNumberList.length --- {{APIRef("SVG")}} The **`length`** property of the {{domxref("SVGNumberList")}} interface returns the number of items in the list. It is an alias of {{domxref("SVGNumberList.numberOfItems", "numberOfItems")}} to make SVG lists more [array-like](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array#array-like_objects). ## Value A non-negative integer that represents the number of items in the list. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}