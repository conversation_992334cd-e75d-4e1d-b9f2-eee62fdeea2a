Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md

Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgnumberlist > numberofitems > index.md --- title: "SVGNumberList: numberOfItems property" short-title: numberOfItems slug: Web/API/SVGNumberList/numberOfItems page-type: web-api-instance-property browser-compat: api.SVGNumberList.numberOfItems --- {{APIRef("SVG")}} The **`numberOfItems`** property of the {{domxref("SVGNumberList")}} interface returns the number of items in the list. {{domxref("SVGNumberList.length", "length")}} is an alias of it. ## Value A non-negative integer that represents the number of items in the list. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}