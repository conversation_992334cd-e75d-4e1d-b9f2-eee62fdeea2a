Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgradialgradientelement > index.md --- title: SVGRadialGradientElement slug: Web/API/SVGRadialGradientElement page-type: web-api-interface browser-compat: api.SVGRadialGradientElement --- {{APIRef("SVG")}} The **`SVGRadialGradientElement`** interface corresponds to the {{SVGElement("RadialGradient")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent, {{domxref("SVGGradientElement")}}._ - {{domxref("SVGRadialGradientElement.cx")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("cx")}} attribute of the given {{SVGElement("RadialGradient")}} element. - {{domxref("SVGRadialGradientElement.cy")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("cy")}} attribute of the given {{SVGElement("RadialGradient")}} element. - {{domxref("SVGRadialGradientElement.r")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("r")}} attribute of the given {{SVGElement("RadialGradient")}} element. - {{domxref("SVGRadialGradientElement.fr")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("fr")}} attribute. - {{domxref("SVGRadialGradientElement.fx")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("fx")}} attribute of the given {{SVGElement("RadialGradient")}} element. - {{domxref("SVGRadialGradientElement.fy")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedLength")}} corresponding to the {{SVGAttr("fy")}} attribute of the given {{SVGElement("RadialGradient")}} element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGGradientElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}