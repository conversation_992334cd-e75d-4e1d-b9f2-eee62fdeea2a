Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsetelement > index.md --- title: SVGSetElement slug: Web/API/SVGSetElement page-type: web-api-interface browser-compat: api.SVGSetElement --- {{APIRef("SVG")}} The **`SVGSetElement`** interface corresponds to the {{SVGElement("set")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGAnimationElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGAnimationElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}