Path: mdn-web-docs > files > en-us > web > api > svgstopelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgstopelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgstopelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgstopelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgstopelement > index.md --- title: SVGStopElement slug: Web/API/SVGStopElement page-type: web-api-interface browser-compat: api.SVGStopElement --- {{APIRef("SVG")}} The **`SVGStopElement`** interface corresponds to the {{SVGElement("stop")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGElement")}}._ - {{domxref("SVGStopElement.offset")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedNumber")}} corresponding to the {{SVGAttr("offset")}} of the given element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}