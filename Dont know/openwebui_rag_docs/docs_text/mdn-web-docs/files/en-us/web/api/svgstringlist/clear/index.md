Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md

Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > clear > index.md --- title: "SVGStringList: clear() method" short-title: clear() slug: Web/API/SVGStringList/clear page-type: web-api-instance-method browser-compat: api.SVGStringList.clear --- {{APIRef("SVG")}} The **`clear()`** method of the {{domxref("SVGStringList")}} interface clears all existing items from the list, with the result being an empty list. ## Syntax ```js-nolint clear() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ### Exceptions - {{domxref("DOMException")}} `NoModificationAllowedError` - : Thrown if the {{domxref("SVGStringList")}} corresponds to a read-only attribute or when the object itself is read-only. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}