Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md

Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > length > index.md --- title: "SVGStringList: length property" short-title: length slug: Web/API/SVGStringList/length page-type: web-api-instance-property browser-compat: api.SVGStringList.length --- {{APIRef("SVG")}} The **`length`** property of the {{domxref("SVGStringList")}} interface returns the number of items in the list. It is an alias of {{domxref("SVGStringList.numberOfItems", "numberOfItems")}} to make SVG lists more [array-like](/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array#array-like_objects). ## Value A non-negative integer that represents the number of items in the list. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}