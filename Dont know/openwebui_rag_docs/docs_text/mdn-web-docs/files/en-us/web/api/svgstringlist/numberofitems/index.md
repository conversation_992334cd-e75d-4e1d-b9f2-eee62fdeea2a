Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md

Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md Path: mdn-web-docs > files > en-us > web > api > svgstringlist > numberofitems > index.md --- title: "SVGStringList: numberOfItems property" short-title: numberOfItems slug: Web/API/SVGStringList/numberOfItems page-type: web-api-instance-property browser-compat: api.SVGStringList.numberOfItems --- {{APIRef("SVG")}} The **`numberOfItems`** property of the {{domxref("SVGStringList")}} interface returns the number of items in the list. {{domxref("SVGStringList.length", "length")}} is an alias of it. ## Value A non-negative integer that represents the number of items in the list. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}