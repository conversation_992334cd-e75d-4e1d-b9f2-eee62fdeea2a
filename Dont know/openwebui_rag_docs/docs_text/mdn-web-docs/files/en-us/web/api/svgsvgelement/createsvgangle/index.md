Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgangle > index.md --- title: "SVGSVGElement: createSVGAngle() method" short-title: createSVGAngle() slug: Web/API/SVGSVGElement/createSVGAngle page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGAngle --- {{APIRef("SVG")}} The `createSVGAngle()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("SVGAngle")}} object outside of any document trees. ## Syntax ```js-nolint createSVGAngle() ``` ### Parameters None. ### Return value An {{domxref("SVGAngle")}} object, initialized to a value of `0` (unitless). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGAngle")}}