Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvglength > index.md --- title: "SVGSVGElement: createSVGLength() method" short-title: createSVGLength() slug: Web/API/SVGSVGElement/createSVGLength page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGLength --- {{APIRef("SVG")}} The `createSVGLength()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("SVGLength")}} object outside of any document trees. ## Syntax ```js-nolint createSVGLength() ``` ### Parameters None. ### Return value An {{domxref("SVGLength")}} object, initialized to a value of `0` (unitless). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGLength")}}