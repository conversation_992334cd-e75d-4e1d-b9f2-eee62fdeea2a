Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgmatrix > index.md --- title: "SVGSVGElement: createSVGMatrix() method" short-title: createSVGMatrix() slug: Web/API/SVGSVGElement/createSVGMatrix page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGMatrix --- {{APIRef("SVG")}} The `createSVGMatrix()` method of the {{domxref("SVGSVGElement")}} interface creates a {{domxref("DOMMatrix")}} object outside of any document trees. ## Syntax ```js-nolint createSVGMatrix() ``` ### Parameters None. ### Return value A {{domxref("DOMMatrix")}} object, initialized to the identity matrix. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMMatrix", "SVGMatrix")}}