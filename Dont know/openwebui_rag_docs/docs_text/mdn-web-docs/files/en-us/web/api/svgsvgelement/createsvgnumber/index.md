Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgnumber > index.md --- title: "SVGSVGElement: createSVGNumber() method" short-title: createSVGNumber() slug: Web/API/SVGSVGElement/createSVGNumber page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGNumber --- {{APIRef("SVG")}} The `createSVGNumber()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("SVGNumber")}} object outside of any document trees. ## Syntax ```js-nolint createSVGNumber() ``` ### Parameters None. ### Return value An {{domxref("SVGNumber")}} object, initialized to `0`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGNumber")}}