Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgpoint > index.md --- title: "SVGSVGElement: createSVGPoint() method" short-title: createSVGPoint() slug: Web/API/SVGSVGElement/createSVGPoint page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGPoint --- {{APIRef("SVG")}} The `createSVGPoint()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("SVGPoint")}} object outside of any document trees. ## Syntax ```js-nolint createSVGPoint() ``` ### Parameters None. ### Return value An {{domxref("SVGPoint")}} object, initialized to the point `(0,0)` in the user coordinate system. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGPoint")}}