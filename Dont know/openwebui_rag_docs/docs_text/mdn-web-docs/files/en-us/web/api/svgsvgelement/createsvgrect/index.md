Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgrect > index.md --- title: "SVGSVGElement: createSVGRect() method" short-title: createSVGRect() slug: Web/API/SVGSVGElement/createSVGRect page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGRect --- {{APIRef("SVG")}} The `createSVGRect()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("DOMRect")}} object outside of any document trees. ## Syntax ```js-nolint createSVGRect() ``` ### Parameters None. ### Return value A {{domxref("DOMRect")}} object, initialized with `x`, `y`, `width`, and `height` all set to `0`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("DOMRect")}}