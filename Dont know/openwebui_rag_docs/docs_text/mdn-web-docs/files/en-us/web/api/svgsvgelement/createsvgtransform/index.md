Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > createsvgtransform > index.md --- title: "SVGSVGElement: createSVGTransform() method" short-title: createSVGTransform() slug: Web/API/SVGSVGElement/createSVGTransform page-type: web-api-instance-method browser-compat: api.SVGSVGElement.createSVGTransform --- {{APIRef("SVG")}} The `createSVGTransform()` method of the {{domxref("SVGSVGElement")}} interface creates an {{domxref("SVGTransform")}} object outside of any document trees. ## Syntax ```js-nolint createSVGTransform() ``` ### Parameters None. ### Return value An {{domxref("SVGTransform")}} object, initialized to the identity matrix transform (`matrix(1, 0, 0, 1, 0, 0)`). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("SVGTransform")}}