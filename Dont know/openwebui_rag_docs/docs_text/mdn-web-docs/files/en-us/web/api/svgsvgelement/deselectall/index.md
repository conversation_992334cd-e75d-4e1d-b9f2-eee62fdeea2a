Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md Path: mdn-web-docs > files > en-us > web > api > svgsvgelement > deselectall > index.md --- title: "SVGSVGElement: deselectAll() method" short-title: deselectAll() slug: Web/API/SVGSVGElement/deselectAll page-type: web-api-instance-method browser-compat: api.SVGSVGElement.deselectAll --- {{APIRef("SVG")}} The `deselectAll()` method of the {{domxref("SVGSVGElement")}} interface unselects any selected objects, including any selections of text strings and type-in bars. ## Syntax ```js-nolint deselectAll() ``` ### Parameters None. ### Return value None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}