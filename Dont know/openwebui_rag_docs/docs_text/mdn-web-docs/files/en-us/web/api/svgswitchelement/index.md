Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgswitchelement > index.md --- title: SVGSwitchElement slug: Web/API/SVGSwitchElement page-type: web-api-interface browser-compat: api.SVGSwitchElement --- {{APIRef("SVG")}} The **`SVGSwitchElement`** interface corresponds to the {{SVGElement("switch")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGGraphicsElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGGraphicsElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}