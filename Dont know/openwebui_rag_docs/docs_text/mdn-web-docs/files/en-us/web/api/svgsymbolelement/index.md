Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgsymbolelement > index.md --- title: SVGSymbolElement slug: Web/API/SVGSymbolElement page-type: web-api-interface browser-compat: api.SVGSymbolElement --- {{APIRef("SVG")}} The **`SVGSymbolElement`** interface corresponds to the {{SVGElement("symbol")}} element. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent interface, {{domxref("SVGGraphicsElement")}}._ - {{domxref("SVGSymbolElement.viewBox")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedRect")}} corresponding to the {{SVGAttr("viewBox")}} attribute of the given {{SVGElement("symbol")}} element. - {{domxref("SVGSymbolElement.preserveAspectRatio")}} {{ReadOnlyInline}} - : An {{domxref("SVGAnimatedPreserveAspectRatio")}} corresponding to the {{SVGAttr("preserveAspectRatio")}} attribute of the given {{SVGElement("symbol")}} element. ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGGraphicsElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}