Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtextelement > index.md --- title: SVGTextElement slug: Web/API/SVGTextElement page-type: web-api-interface browser-compat: api.SVGTextElement --- {{APIRef("SVG")}} The **`SVGTextElement`** interface corresponds to the {{SVGElement("text")}} elements. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGTextPositioningElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGTextPositioningElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("text")}}