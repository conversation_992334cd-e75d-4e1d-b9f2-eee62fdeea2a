Path: mdn-web-docs > files > en-us > web > api > svgtitleelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgtitleelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtitleelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtitleelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtitleelement > index.md --- title: SVGTitleElement slug: Web/API/SVGTitleElement page-type: web-api-interface browser-compat: api.SVGTitleElement --- {{APIRef("SVG")}} The **`SVGTitleElement`** interface corresponds to the {{SVGElement("title")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties, but inherits properties from its parent interface, {{domxref("SVGElement")}}._ ## Instance methods _This interface doesn't implement any specific methods, but inherits methods from its parent interface, {{domxref("SVGElement")}}._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}