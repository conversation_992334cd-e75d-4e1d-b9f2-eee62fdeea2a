Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md

Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md Path: mdn-web-docs > files > en-us > web > api > svgtspanelement > index.md --- title: SVGTSpanElement slug: Web/API/SVGTSpanElement page-type: web-api-interface browser-compat: api.SVGTSpanElement --- {{APIRef("SVG")}} The **`SVGTSpanElement`** interface represents a {{SVGElement("tspan")}} element. {{InheritanceDiagram}} ## Instance properties _This interface doesn't provide any specific properties, but inherits the properties from its parent, {{domxref("SVGTextPositioningElement")}}._ ## Instance methods _This interface doesn't provide any specific methods._ ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{SVGElement("tspan")}} SVG Element