Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md

Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md Path: mdn-web-docs > files > en-us > web > api > svgunittypes > index.md --- title: SVGUnitTypes slug: Web/API/SVGUnitTypes page-type: web-api-interface browser-compat: api.SVGUnitTypes --- {{APIRef("SVG")}} The **`SVGUnitTypes`** interface defines a commonly used set of constants used for reflecting {{SVGAttr("gradientUnits")}}, {{SVGAttr("patternContentUnits")}} and other similar attributes. {{InheritanceDiagram}} ## Instance properties _This interface doesn't implement any specific properties._ ## Instance methods _This interface doesn't implement any specific methods._ ## Static properties - `SVG_UNIT_TYPE_UNKNOWN` (0) - : The type is not one of predefined types. It is invalid to attempt to define a new value of this type or to attempt to switch an existing value to this type. - `SVG_UNIT_TYPE_USERSPACEONUSE` (1) - : Corresponds to the value `userSpaceOnUse`. - `SVG_UNIT_TYPE_OBJECTBOUNDINGBOX` (2) - : Corresponds to the value `objectBoundingBox`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}