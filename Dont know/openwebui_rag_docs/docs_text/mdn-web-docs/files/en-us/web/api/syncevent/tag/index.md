Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md

Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md Path: mdn-web-docs > files > en-us > web > api > syncevent > tag > index.md --- title: "SyncEvent: tag property" short-title: tag slug: Web/API/SyncEvent/tag page-type: web-api-instance-property browser-compat: api.SyncEvent.tag --- {{APIRef("Background Sync")}}{{AvailableInWorkers("service")}} The **`tag`** read-only property of the {{domxref("SyncEvent")}} interface returns the developer-defined identifier for this `SyncEvent`. This is the value passed in the `tag` parameter of the {{domxref("SyncEvent.SyncEvent","SyncEvent()")}} constructor. ## Value The developer-defined identifier for this `SyncEvent`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}