Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md

Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md Path: mdn-web-docs > files > en-us > web > api > syncmanager > gettags > index.md --- title: "SyncManager: getTags() method" short-title: getTags() slug: Web/API/SyncManager/getTags page-type: web-api-instance-method browser-compat: api.SyncManager.getTags --- {{APIRef("Background Sync")}}{{AvailableInWorkers}} The **`getTags()`** method of the {{domxref("SyncManager")}} interface returns a list of developer-defined identifiers for `SyncManager` registrations. ## Syntax ```js-nolint getTags() ``` ### Parameters None. ### Return value A {{jsxref("Promise")}} that resolves to an array of strings containing developer-defined identifiers for `SyncManager` registrations. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}