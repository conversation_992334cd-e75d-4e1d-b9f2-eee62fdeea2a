Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containerid > index.md

Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containerid > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containerid > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containerid > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containerid > index.md --- title: "TaskAttributionTiming: containerId property" short-title: containerId slug: Web/API/TaskAttributionTiming/containerId page-type: web-api-instance-property status: - experimental browser-compat: api.TaskAttributionTiming.containerId --- {{APIRef("Performance API")}}{{SeeCompatTable}} The **`containerId`** read-only property of the {{domxref("TaskAttributionTiming")}} interface returns the container's `id` attribute. A container is the iframe, embed or object etc. that is being implicated, on the whole, for a long task. ## Value A string containing the container's [`id`](/en-US/docs/Web/HTML/Reference/Global_attributes/id) HTML content attribute. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}