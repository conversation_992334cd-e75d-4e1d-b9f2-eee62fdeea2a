Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containername > index.md

Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containername > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containername > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containername > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containername > index.md --- title: "TaskAttributionTiming: containerName property" short-title: containerName slug: Web/API/TaskAttributionTiming/containerName page-type: web-api-instance-property status: - experimental browser-compat: api.TaskAttributionTiming.containerName --- {{APIRef("Performance API")}}{{SeeCompatTable}} The **`containerName`** read-only property of the {{domxref("TaskAttributionTiming")}} interface returns the container's `name` attribute. A container is the iframe, embed or object etc. that is being implicated, on the whole, for a long task. ## Value A string containing the container's `name` HTML content attribute (e.g., [`<iframe name="myIframe">`](/en-US/docs/Web/HTML/Reference/Elements/iframe#name) or [`<object name="myObject">`](/en-US/docs/Web/HTML/Reference/Elements/object#name)). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}