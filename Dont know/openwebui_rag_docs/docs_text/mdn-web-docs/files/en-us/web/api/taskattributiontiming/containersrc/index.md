Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containersrc > index.md

Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containersrc > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containersrc > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containersrc > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containersrc > index.md --- title: "TaskAttributionTiming: containerSrc property" short-title: containerSrc slug: Web/API/TaskAttributionTiming/containerSrc page-type: web-api-instance-property status: - experimental browser-compat: api.TaskAttributionTiming.containerSrc --- {{APIRef("Performance API")}}{{SeeCompatTable}} The **`containerSrc`** read-only property of the {{domxref("TaskAttributionTiming")}} interface returns the container's `src` attribute. A container is the iframe, embed or object etc. that is being implicated, on the whole, for a long task. ## Value A string containing the container's `src` attribute (e.g., [`<iframe src="url.html">`](/en-US/docs/Web/HTML/Reference/Elements/iframe#src)). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}