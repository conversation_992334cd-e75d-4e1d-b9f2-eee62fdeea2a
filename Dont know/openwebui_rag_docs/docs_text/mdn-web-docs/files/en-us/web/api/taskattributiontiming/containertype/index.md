Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containertype > index.md

Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containertype > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containertype > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containertype > index.md Path: mdn-web-docs > files > en-us > web > api > taskattributiontiming > containertype > index.md --- title: "TaskAttributionTiming: containerType property" short-title: containerType slug: Web/API/TaskAttributionTiming/containerType page-type: web-api-instance-property status: - experimental browser-compat: api.TaskAttributionTiming.containerType --- {{APIRef("Performance API")}}{{SeeCompatTable}} The **`containerType`** read-only property of the {{domxref("TaskAttributionTiming")}} interface returns the type of the container, one of `iframe`, `embed`, or `object`. ## Value A string containing the container's type, one of `iframe`, `embed`, or `object`. If no type can be determined, `window` will be returned. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}