Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md

Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md Path: mdn-web-docs > files > en-us > web > api > text > assignedslot > index.md --- title: "Text: assignedSlot property" short-title: assignedSlot slug: Web/API/Text/assignedSlot page-type: web-api-instance-property browser-compat: api.Text.assignedSlot --- {{APIRef("DOM")}} The read-only **`assignedSlot`** property of the {{domxref("Text")}} interface returns the {{domxref("HTMLSlotElement")}} object associated with the element. ## Value An {{domxref("HTMLSlotElement")}}, or `null` if no {{HTMLElement("slot")}} element is associated with the text node. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}