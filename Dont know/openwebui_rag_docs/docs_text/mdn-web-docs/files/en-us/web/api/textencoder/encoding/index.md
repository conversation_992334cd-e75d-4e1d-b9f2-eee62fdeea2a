Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md

Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > encoding > index.md --- title: "TextEncoder: encoding property" short-title: encoding slug: Web/API/TextEncoder/encoding page-type: web-api-instance-property browser-compat: api.TextEncoder.encoding --- {{APIRef("Encoding API")}}{{AvailableInWorkers}} The **`TextEncoder.encoding`** read-only property returns a string containing the name of the encoding algorithm used by the specific encoder. It can only have the following value `utf-8`. ## Value A string with the value `utf-8`. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{DOMxRef("TextEncoder")}} interface it belongs to.