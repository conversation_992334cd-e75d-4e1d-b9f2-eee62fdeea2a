Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md

Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md Path: mdn-web-docs > files > en-us > web > api > textencoder > textencoder > index.md --- title: "TextEncoder: TextEncoder() constructor" short-title: TextEncoder() slug: Web/API/TextEncoder/TextEncoder page-type: web-api-constructor browser-compat: api.TextEncoder.TextEncoder --- {{APIRef("Encoding API")}}{{AvailableInWorkers}} The **`TextEncoder()`** constructor returns a newly created {{DOMxRef("TextEncoder")}} object that will generate a byte stream with UTF-8 encoding. ## Syntax ```js-nolint new TextEncoder() ``` ### Parameters None. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{DOMxRef("TextEncoder")}} interface it belongs to.