Path: mdn-web-docs > files > en-us > web > api > textevent > data > index.md

Path: mdn-web-docs > files > en-us > web > api > textevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > data > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > data > index.md --- title: "TextEvent: data property" short-title: data slug: Web/API/TextEvent/data page-type: web-api-instance-property status: - deprecated browser-compat: api.TextEvent.data --- {{APIRef("UI Events")}}{{deprecated_header}} The **`data`** read-only property of the {{domxref("TextEvent")}} interface returns the last character added to the input element. ## Value A string representing the event data. For `textInput` events, this is the character that was last entered. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}