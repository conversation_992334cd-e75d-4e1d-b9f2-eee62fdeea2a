Path: mdn-web-docs > files > en-us > web > api > textevent > index.md

Path: mdn-web-docs > files > en-us > web > api > textevent > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > index.md Path: mdn-web-docs > files > en-us > web > api > textevent > index.md --- title: TextEvent slug: Web/API/TextEvent page-type: web-api-interface status: - deprecated browser-compat: api.TextEvent --- {{APIRef("UI Events")}}{{deprecated_header}} The **`TextEvent`** interface is a legacy UI event interface for reporting changes to text UI elements. > **Note:** `TextEvent` events have been superseded by events such as `input`, `beforeinput`, `keypress`, `keyup`, and `keydown`. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from its parent {{domxref("UIEvent")}}, and indirectly from {{domxref("Event")}}._ - {{domxref("TextEvent.data")}} {{ReadOnlyInline}} {{deprecated_inline}} - : Indicates the data associated with the event. ## Instance methods - {{domxref("TextEvent.initTextEvent()")}} {{deprecated_inline}} - : Populates the values of this (new) `TextEvent` with the given parameters. ## Events list The following is a list of all `TextEvent` events: - `textinput` ## Examples ### Listen for text input events You can register a listener for text input events using {{DOMxRef("EventTarget.addEventListener()")}} as follows: ```js element.addEventListener( "textInput", (event) => { // }, false, ); ``` ### Simple logger showing input events This example listens for a number of events that are fired on an input, including `textInput`. The event type and the event data are logged, allowing you to see where `textInput` is emitted relative to the other events such as those generated by key presses. #### HTML ```html <input placeholder="Enter some text" name="name" /> ``` ```html hidden <pre id="log"></pre> ``` ```css hidden #log { height: 140px; overflow: scroll; padding: 0.5rem; border: 1px solid black; } ``` #### JavaScript ```js hidden const logElement = document.querySelector("#log"); function log(text) { logElement.innerText = `${logElement.innerText}${text}\n`; logElement.scrollTop = logElement.scrollHeight; } ``` ```js const input = document.querySelector("input"); input.addEventListener("keypress", updateValue); input.addEventListener("keyup", updateValue); input.addEventListener("keydown", updateValue); input.addEventListener("input", updateValue); input.addEventListener("beforeinput", updateValue); input.addEventListener("textInput", updateValue); function updateValue(e) { log(`${e.type}: ${e.data}`); } ``` #### Result {{EmbedLiveSample("Simple logger showing input events", "100%", "210px" )}} ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}