Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md

Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md Path: mdn-web-docs > files > en-us > web > api > texttrack > index.md --- title: TextTrack slug: Web/API/TextTrack page-type: web-api-interface browser-compat: api.TextTrack --- {{APIRef("WebVTT")}} The **`TextTrack`** interface of the [WebVTT API](/en-US/docs/Web/API/WebVTT_API) represents a text track associated with a media element. An object of this type owns the list of {{domxref("VTTCue")}} objects that will be displayed over the video at various points. `TextTrack` objects can be added to a {{domxref("HTMLVideoElement")}} or {{domxref("HTMLAudioElement")}} element using the {{domxref("HTMLMediaElement.addTextTrack()")}} method, which has the same effect as adding text tracks declaratively using {{htmlelement("track")}} elements inside a {{htmlelement("video")}} or {{htmlelement("audio")}} element. The `TextTrack` objects are stored in a {{domxref("TextTrackList")}}, which can be retrieved using the {{domxref("HTMLMediaElement.textTracks")}} property. {{InheritanceDiagram}} ## Instance properties _This interface also inherits properties from {{domxref("EventTarget")}}._ - {{domxref("TextTrack.activeCues")}} {{ReadOnlyInline}} - : A {{domxref("TextTrackCueList")}} object listing the currently active set of text track cues. Track cues are active if the current playback position of the media is between the cues' start and end times. Thus, for displayed cues such as captions or subtitles, the active cues are currently being displayed. - {{domxref("TextTrack.cues")}} {{ReadOnlyInline}} - : A {{domxref("TextTrackCueList")}} which contains all of the track's cues. - {{domxref("TextTrack.id")}} {{ReadOnlyInline}} - : A string which identifies the track, if it has one. If it doesn't have an ID, then this value is an empty string (`""`). If the `TextTrack` is associated with a {{HTMLElement("track")}} element, then the track's ID matches the element's ID. - {{domxref("TextTrack.inBandMetadataTrackDispatchType")}} {{ReadOnlyInline}} - : Returns a string which indicates the track's in-band metadata track dispatch type. - {{domxref("TextTrack.kind")}} {{ReadOnlyInline}} - : Returns a string indicating what kind of text track the `TextTrack` describes. It must be one of the permitted values. - {{domxref("TextTrack.label")}} {{ReadOnlyInline}} - : A human-readable string which contains the text track's label, if one is present; otherwise, this is an empty string (`""`), in which case a custom label may need to be generated by your code using other attributes of the track, if the track's label needs to be exposed to the user. - {{domxref("TextTrack.language")}} {{ReadOnlyInline}} - : A string which specifies the text language in which the text track's contents is written. The value must adhere to the format specified in {{RFC(5646, "Tags for Identifying Languages (also known as BCP 47)")}}, just like the HTML [`lang`](/en-US/docs/Web/HTML/Reference/Global_attributes/lang) attribute. For example, this can be `"en-US"` for United States English or `"pt-BR"` for Brazilian Portuguese. - {{domxref("TextTrack.mode")}} - : A string specifying the track's current mode, which must be one of the permitted values. Changing this property's value changes the track's current mode to match. The default is `disabled`, unless the {{HTMLElement("track")}} element's [`default`](/en-US/docs/Web/HTML/Reference/Elements/track#default) boolean attribute is set to `true` in which case the default mode is `showing`. - {{domxref("TextTrack.sourceBuffer", "sourceBuffer")}} {{ReadOnlyInline}} - : The {{domxref("SourceBuffer")}} that created the track. Returns null if the track was not created by a {{domxref("SourceBuffer")}} or the {{domxref("SourceBuffer")}} has been removed from the {{domxref("MediaSource.sourceBuffers")}} attribute of its parent media source. ## Instance methods _This interface also inherits methods from {{domxref("EventTarget")}}._ > [!NOTE] > The {{domxref("TextTrackCue")}} interface is an abstract class used as the parent for other cue interfaces such as {{domxref("VTTCue")}}. Therefore, when adding or removing a cue you will be passing in one of the cue types that inherit from `TextTrackCue`. - {{domxref("TextTrack.addCue()")}} - : Adds a cue (specified as a {{domxref("TextTrackCue")}} object) to the track's list of cues. - {{domxref("TextTrack.removeCue()")}} - : Removes a cue (specified as a {{domxref("TextTrackCue")}} object) from the track's list of cues. ## Events - [`cuechange`](/en-US/docs/Web/API/TextTrack/cuechange_event) - : Fired when cues are entered and exited. A given text cue appears when the cue is entered and disappears when the cue is exited. Also available via the `oncuechange` property. ## Example The following example adds a new `TextTrack` to a video, then sets it to display using {{domxref("TextTrack.mode")}}. ```js let video = document.querySelector("video"); let track = video.addTextTrack("captions", "Captions", "en"); track.mode = "showing"; ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [WebVTT](/en-US/docs/Web/API/WebVTT_API) - {{domxref("TextTrackCueList")}} - {{domxref("VTTCue")}} - {{HTMLElement("track")}} - {{domxref("HTMLTrackElement")}}