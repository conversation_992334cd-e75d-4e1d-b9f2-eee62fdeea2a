Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md

Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md Path: mdn-web-docs > files > en-us > web > api > texttrackcue > pauseonexit > index.md --- title: "TextTrackCue: pauseOnExit property" short-title: pauseOnExit slug: Web/API/TextTrackCue/pauseOnExit page-type: web-api-instance-property browser-compat: api.TextTrackCue.pauseOnExit --- {{APIRef("WebVTT")}} The **`pauseOnExit`** property of the {{domxref("TextTrackCue")}} interface returns or sets the flag indicating whether playback of the media should pause when the end of the range to which this cue applies is reached. ## Value A {{jsxref("Boolean")}}, true if the media will pause. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}