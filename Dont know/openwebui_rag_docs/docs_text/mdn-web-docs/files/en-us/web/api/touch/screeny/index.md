Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md

Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md Path: mdn-web-docs > files > en-us > web > api > touch > screeny > index.md --- title: "Touch: screenY property" short-title: screenY slug: Web/API/Touch/screenY page-type: web-api-instance-property browser-compat: api.Touch.screenY --- {{ APIRef("Touch Events") }} Returns the Y coordinate of the touch point relative to the screen, not including any scroll offset. ## Value A number. ## Examples The [Touch.screenX example](/en-US/docs/Web/API/Touch/screenX#examples) includes an example of this property's usage. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}