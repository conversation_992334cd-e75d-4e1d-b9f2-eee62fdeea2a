Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md

Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > ctrlkey > index.md --- title: "TouchEvent: ctrlKey property" short-title: ctrlKey slug: Web/API/TouchEvent/ctrlKey page-type: web-api-instance-property browser-compat: api.TouchEvent.ctrlKey --- {{ APIRef("Touch Events") }} The read-only **`ctrlKey`** property of the {{domxref("TouchEvent")}} interface returns a boolean value indicating whether the <kbd>control</kbd> (Control) key is enabled when the touch event is created. If this key is enabled, the attribute's value is `true`. Otherwise, it is `false`. This property is {{ReadOnlyInline}}. ## Value A boolean value that is `true` if the <kbd>control</kbd> key is enabled for this event; and `false` if the <kbd>control</kbd> is not enabled. ## Examples The [TouchEvent.altKey example](/en-US/docs/Web/API/TouchEvent/altKey#examples) includes an example of this property's usage. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}