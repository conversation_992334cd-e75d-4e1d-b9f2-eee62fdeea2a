Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md

Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md Path: mdn-web-docs > files > en-us > web > api > touchevent > shiftkey > index.md --- title: "TouchEvent: shiftKey property" short-title: shiftKey slug: Web/API/TouchEvent/shiftKey page-type: web-api-instance-property browser-compat: api.TouchEvent.shiftKey --- {{ APIRef("Touch Events") }} The read-only **`shiftKey`** property of the `TouchEvent` interface returns a boolean value indicating whether or not the <kbd>shift</kbd> key is enabled when the touch event is created. If this key is enabled, the attribute's value is `true`. Otherwise, it is `false`. ## Value The boolean value `true` if the <kbd>shift</kbd> key is enabled for this event; and `false` if the <kbd>shift</kbd> key is not enabled. ## Examples The [TouchEvent.altKey example](/en-US/docs/Web/API/TouchEvent/altKey#examples) includes an example of this property's usage. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}