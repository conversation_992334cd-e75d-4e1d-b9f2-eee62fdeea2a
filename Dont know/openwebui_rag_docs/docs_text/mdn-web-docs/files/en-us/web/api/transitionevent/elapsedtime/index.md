Path: mdn-web-docs > files > en-us > web > api > transitionevent > elapsedtime > index.md

Path: mdn-web-docs > files > en-us > web > api > transitionevent > elapsedtime > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > elapsedtime > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > elapsedtime > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > elapsedtime > index.md --- title: "TransitionEvent: elapsedTime property" short-title: elapsedTime slug: Web/API/TransitionEvent/elapsedTime page-type: web-api-instance-property browser-compat: api.TransitionEvent.elapsedTime --- {{ apiref("CSSOM") }} The **`TransitionEvent.elapsedTime`** read-only property is a `float` giving the amount of time the animation has been running, in seconds, when this event fired. This value is not affected by the {{cssxref("transition-delay")}} property. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using CSS transitions](/en-US/docs/Web/CSS/CSS_transitions/Using_CSS_transitions)