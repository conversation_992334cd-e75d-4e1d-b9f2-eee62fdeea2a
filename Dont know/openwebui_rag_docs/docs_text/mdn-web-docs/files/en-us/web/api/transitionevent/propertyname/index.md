Path: mdn-web-docs > files > en-us > web > api > transitionevent > propertyname > index.md

Path: mdn-web-docs > files > en-us > web > api > transitionevent > propertyname > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > propertyname > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > propertyname > index.md Path: mdn-web-docs > files > en-us > web > api > transitionevent > propertyname > index.md --- title: "TransitionEvent: propertyName property" short-title: propertyName slug: Web/API/TransitionEvent/propertyName page-type: web-api-instance-property browser-compat: api.TransitionEvent.propertyName --- {{ apiref("CSSOM") }} The **`propertyName`** read-only property of {{domxref("TransitionEvent")}} objects is a string containing the name of the CSS property associated with the transition. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using CSS transitions](/en-US/docs/Web/CSS/CSS_transitions/Using_CSS_transitions)