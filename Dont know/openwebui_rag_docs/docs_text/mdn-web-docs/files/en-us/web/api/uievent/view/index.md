Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md

Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md Path: mdn-web-docs > files > en-us > web > api > uievent > view > index.md --- title: "UIEvent: view property" short-title: view slug: Web/API/UIEvent/view page-type: web-api-instance-property browser-compat: api.UIEvent.view --- {{APIRef("UI Events")}} The **`UIEvent.view`** read-only property returns the {{glossary("WindowProxy")}} object from which the event was generated. In browsers, this is the {{ domxref("Window") }} object the event happened in. ## Value A reference to an `AbstractView` object. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}