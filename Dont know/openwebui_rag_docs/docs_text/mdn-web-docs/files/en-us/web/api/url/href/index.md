Path: mdn-web-docs > files > en-us > web > api > url > href > index.md

Path: mdn-web-docs > files > en-us > web > api > url > href > index.md Path: mdn-web-docs > files > en-us > web > api > url > href > index.md Path: mdn-web-docs > files > en-us > web > api > url > href > index.md Path: mdn-web-docs > files > en-us > web > api > url > href > index.md Path: mdn-web-docs > files > en-us > web > api > url > href > index.md --- title: "URL: href property" short-title: href slug: Web/API/URL/href page-type: web-api-instance-property browser-compat: api.URL.href --- {{ApiRef("URL API")}} {{AvailableInWorkers}} The **`href`** property of the {{domxref("URL")}} interface is a string containing the whole URL. ## Value A string. ## Examples ```js const url = new URL( "https://developer.mozilla.org/en-US/docs/Web/API/URL/href", ); console.log(url.href); // Logs: 'https://developer.mozilla.org/en-US/docs/Web/API/URL/href' ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("URL")}} interface it belongs to.