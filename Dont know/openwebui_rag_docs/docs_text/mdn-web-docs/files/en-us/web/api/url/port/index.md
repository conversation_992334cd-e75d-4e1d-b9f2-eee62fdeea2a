Path: mdn-web-docs > files > en-us > web > api > url > port > index.md

Path: mdn-web-docs > files > en-us > web > api > url > port > index.md Path: mdn-web-docs > files > en-us > web > api > url > port > index.md Path: mdn-web-docs > files > en-us > web > api > url > port > index.md Path: mdn-web-docs > files > en-us > web > api > url > port > index.md Path: mdn-web-docs > files > en-us > web > api > url > port > index.md --- title: "URL: port property" short-title: port slug: Web/API/URL/port page-type: web-api-instance-property browser-compat: api.URL.port --- {{ApiRef("URL API")}} {{AvailableInWorkers}} The **`port`** property of the {{domxref("URL")}} interface is a string containing the port number of the URL. If the port is the default for the protocol (`80` for `ws:` and `http:`, `443` for `wss:` and `https:`, and `21` for `ftp:`), this property contains an empty string, `""`. This property can be set to change the port of the URL. If the URL has no {{domxref("URL.host", "host")}} or its scheme is `file:`, then setting this property has no effect. It also silently ignores invalid port numbers. ## Value A string. ## Examples ```js // https protocol with non-default port number new URL("https://example.com:5443/svn/Repos/").port; // '5443' // http protocol with non-default port number new URL("http://example.com:8080/svn/Repos/").port; // '8080' // https protocol with default port number new URL("https://example.com:443/svn/Repos/").port; // '' (empty string) // http protocol with default port number new URL("http://example.com:80/svn/Repos/").port; // '' (empty string) // https protocol with no explicit port number new URL("https://example.com/svn/Repos/").port; // '' (empty string) // http protocol with no explicit port number new URL("http://example.com/svn/Repos/").port; // '' (empty string) // ftp protocol with non-default port number new URL("ftp://example.com:221/svn/Repos/").port; // '221' // ftp protocol with default port number new URL("ftp://example.com:21/svn/Repos/").port; // '' (empty string) ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - The {{domxref("URL")}} interface it belongs to.