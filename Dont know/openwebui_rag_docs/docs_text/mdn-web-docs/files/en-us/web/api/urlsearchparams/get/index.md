Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md

Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md Path: mdn-web-docs > files > en-us > web > api > urlsearchparams > get > index.md --- title: "URLSearchParams: get() method" short-title: get() slug: Web/API/URLSearchParams/get page-type: web-api-instance-method browser-compat: api.URLSearchParams.get --- {{ApiRef("URL API")}} {{AvailableInWorkers}} The **`get()`** method of the {{domxref("URLSearchParams")}} interface returns the first value associated to the given search parameter. ## Syntax ```js-nolint get(name) ``` ### Parameters - `name` - : The name of the parameter to return. ### Return value A string if the given search parameter is found; otherwise, **`null`**. ## Examples If the URL of your page is `https://example.com/?name=Jonathan&age=18` you could parse out the 'name' and 'age' parameters using: ```js let params = new URLSearchParams(document.location.search); let name = params.get("name"); // is the string "Jonathan" let age = parseInt(params.get("age"), 10); // is the number 18 ``` Requesting a parameter that isn't present in the query string will return **`null`**: ```js let address = params.get("address"); // null ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}