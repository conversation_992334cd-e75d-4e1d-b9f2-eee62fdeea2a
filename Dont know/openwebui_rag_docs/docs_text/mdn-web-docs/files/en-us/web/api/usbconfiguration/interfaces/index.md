Path: mdn-web-docs > files > en-us > web > api > usbconfiguration > interfaces > index.md

Path: mdn-web-docs > files > en-us > web > api > usbconfiguration > interfaces > index.md Path: mdn-web-docs > files > en-us > web > api > usbconfiguration > interfaces > index.md Path: mdn-web-docs > files > en-us > web > api > usbconfiguration > interfaces > index.md Path: mdn-web-docs > files > en-us > web > api > usbconfiguration > interfaces > index.md --- title: "USBConfiguration: interfaces property" short-title: interfaces slug: Web/API/USBConfiguration/interfaces page-type: web-api-instance-property status: - experimental browser-compat: api.USBConfiguration.interfaces --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`interfaces`** read-only property of the {{domxref("USBConfiguration")}} interface returns an array containing instances of the {{domxref('USBInterface')}} describing each interface supported by this configuration. ## Value An array containing instances of {{domxref('USBInterface')}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}