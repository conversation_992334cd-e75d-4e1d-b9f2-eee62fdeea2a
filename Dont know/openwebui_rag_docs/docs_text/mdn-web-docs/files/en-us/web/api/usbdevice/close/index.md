Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > close > index.md --- title: "USBDevice: close() method" short-title: close() slug: Web/API/USBDevice/close page-type: web-api-instance-method status: - experimental browser-compat: api.USBDevice.close --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`close()`** method of the {{domxref("USBDevice")}} interface returns a {{jsxref("promise")}} that resolves when all open interfaces are released and the device session has ended. ## Syntax ```js-nolint close() ``` ### Parameters None. ### Return value A {{jsxref("promise")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}