Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > configurations > index.md --- title: "USBDevice: configurations property" short-title: configurations slug: Web/API/USBDevice/configurations page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.configurations --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`configurations`** read only property of the {{domxref("USBDevice")}} interface an {{jsxref("array")}} of device-specific interfaces for controlling a paired USB device. ## Value An {{jsxref("array")}} of {{domxref("USBConfiguration")}} objects. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}