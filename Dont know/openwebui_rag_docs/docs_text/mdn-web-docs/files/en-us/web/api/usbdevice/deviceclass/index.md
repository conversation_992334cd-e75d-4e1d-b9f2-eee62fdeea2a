Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceclass > index.md --- title: "USBDevice: deviceClass property" short-title: deviceClass slug: Web/API/USBDevice/deviceClass page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceClass --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceClass`** read only property of the {{domxref("USBDevice")}} interface one of three properties that identify USB devices for the purpose of loading a USB driver that will work with that device. The other two properties are `USBDevice.deviceSubclass` and `USBDevice.deviceProtocol`. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}