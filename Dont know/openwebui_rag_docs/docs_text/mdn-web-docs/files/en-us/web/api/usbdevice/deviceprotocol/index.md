Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceprotocol > index.md --- title: "USBDevice: deviceProtocol property" short-title: deviceProtocol slug: Web/API/USBDevice/deviceProtocol page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceProtocol --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceProtocol`** read only property of the {{domxref("USBDevice")}} interface one of three properties that identify USB devices for the purpose of loading a USB driver that will work with that device. The other two properties are `USBDevice.deviceClass` and `USBDevice.deviceSubclass`. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}