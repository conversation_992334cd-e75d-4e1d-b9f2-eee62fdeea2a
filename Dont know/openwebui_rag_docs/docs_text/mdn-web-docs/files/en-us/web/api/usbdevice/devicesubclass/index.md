Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > devicesubclass > index.md --- title: "USBDevice: deviceSubclass property" short-title: deviceSubclass slug: Web/API/USBDevice/deviceSubclass page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceSubclass --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceSubclass`** read only property of the {{domxref("USBDevice")}} interface one of three properties that identify USB devices for the purpose of loading a USB driver that will work with that device. The other two properties are USBDevice.deviceClass and USBDevice.deviceProtocol. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}