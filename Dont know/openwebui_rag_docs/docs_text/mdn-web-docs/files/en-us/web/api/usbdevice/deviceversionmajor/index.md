Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionmajor > index.md --- title: "USBDevice: deviceVersionMajor property" short-title: deviceVersionMajor slug: Web/API/USBDevice/deviceVersionMajor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceVersionMajor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceVersionMajor`** read only property of the {{domxref("USBDevice")}} interface he major version number of the device in a semantic versioning scheme. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}