Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionminor > index.md --- title: "USBDevice: deviceVersionMinor property" short-title: deviceVersionMinor slug: Web/API/USBDevice/deviceVersionMinor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceVersionMinor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceVersionMinor`** read only property of the {{domxref("USBDevice")}} interface the minor version number of the device in a semantic versioning scheme. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}