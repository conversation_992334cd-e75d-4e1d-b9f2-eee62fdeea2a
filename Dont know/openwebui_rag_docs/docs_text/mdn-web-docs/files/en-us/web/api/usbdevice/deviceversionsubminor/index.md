Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > deviceversionsubminor > index.md --- title: "USBDevice: deviceVersionSubminor property" short-title: deviceVersionSubminor slug: Web/API/USBDevice/deviceVersionSubminor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.deviceVersionSubminor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`deviceVersionSubminor`** read only property of the {{domxref("USBDevice")}} interface the patch version number of the device in a semantic versioning scheme. ## Value A number. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}