Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > manufacturername > index.md --- title: "USBDevice: manufacturerName property" short-title: manufacturerName slug: Web/API/USBDevice/manufacturerName page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.manufacturerName --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`manufacturerName`** read only property of the {{domxref("USBDevice")}} interface the of the organization that manufactured the USB device. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}