Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > open > index.md --- title: "USBDevice: open() method" short-title: open() slug: Web/API/USBDevice/open page-type: web-api-instance-method status: - experimental browser-compat: api.USBDevice.open --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`open()`** method of the {{domxref("USBDevice")}} interface returns a {{jsxref("promise")}} that resolves when a device session has started. ## Syntax ```js-nolint open() ``` ### Parameters None. ### Return value A {{jsxref("promise")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}