Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productid > index.md --- title: "USBDevice: productId property" short-title: productId slug: Web/API/USBDevice/productId page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.productId --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`productId`** read only property of the {{domxref("USBDevice")}} interface the manufacturer-defined code that identifies a USB device. ## Value The manufacturer-defined code that identifies a USB device. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}