Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > productname > index.md --- title: "USBDevice: productName property" short-title: productName slug: Web/API/USBDevice/productName page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.productName --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`productName`** read only property of the {{domxref("USBDevice")}} interface the manufacturer-defined name that identifies a USB device. ## Value The manufacturer-defined name that identifies a USB device. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}