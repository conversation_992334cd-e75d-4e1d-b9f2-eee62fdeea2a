Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > reset > index.md --- title: "USBDevice: reset() method" short-title: reset() slug: Web/API/USBDevice/reset page-type: web-api-instance-method status: - experimental browser-compat: api.USBDevice.reset --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`reset()`** method of the {{domxref("USBDevice")}} interface returns a {{jsxref("promise")}} that resolves when the device is reset and all app operations canceled and their promises rejected. ## Syntax ```js-nolint reset() ``` ### Parameters None. ### Return value A {{jsxref("promise")}}. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}