Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > serialnumber > index.md --- title: "USBDevice: serialNumber property" short-title: serialNumber slug: Web/API/USBDevice/serialNumber page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.serialNumber --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`serialNumber`** read only property of the {{domxref("USBDevice")}} interface is the manufacturer-defined serial number for the specific USB device. ## Value The serial number for the specified USB device ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}