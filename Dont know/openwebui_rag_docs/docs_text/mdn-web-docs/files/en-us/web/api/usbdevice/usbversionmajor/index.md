Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionmajor > index.md --- title: "USBDevice: usbVersionMajor property" short-title: usbVersionMajor slug: Web/API/USBDevice/usbVersionMajor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.usbVersionMajor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`usbVersionMajor`** read only property of the {{domxref("USBDevice")}} interface is one of three properties that declare the USB protocol version supported by the device. The other two properties are USBDevice.usbVersionMinor and USBDevice.usbVersionSubminor. ## Value The last of three properties that declare the USB protocol version supported by the device. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}