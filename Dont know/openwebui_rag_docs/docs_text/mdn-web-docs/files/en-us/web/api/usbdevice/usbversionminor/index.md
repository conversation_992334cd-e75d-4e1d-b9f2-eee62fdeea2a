Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionminor > index.md --- title: "USBDevice: usbVersionMinor property" short-title: usbVersionMinor slug: Web/API/USBDevice/usbVersionMinor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.usbVersionMinor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`usbVersionMinor`** read only property of the {{domxref("USBDevice")}} interface is one of three properties that declare the USB protocol version supported by the device. The other two properties are USBDevice.usbVersionMajor and USBDevice.usbVersionSubminor. ## Value The second of three properties that declare the USB protocol version supported by the device. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}