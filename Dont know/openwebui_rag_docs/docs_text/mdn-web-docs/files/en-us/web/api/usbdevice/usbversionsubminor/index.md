Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > usbversionsubminor > index.md --- title: "USBDevice: usbVersionSubminor property" short-title: usbVersionSubminor slug: Web/API/USBDevice/usbVersionSubminor page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.usbVersionSubminor --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`usbVersionSubminor`** read only property of the {{domxref("USBDevice")}} interface is one of three properties that declare the USB protocol version supported by the device. The other two properties are USBDevice.usbVersionMajor and USBDevice.usbVersionMinor. ## Value The first of three properties that declare the USB protocol version supported by the device. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}