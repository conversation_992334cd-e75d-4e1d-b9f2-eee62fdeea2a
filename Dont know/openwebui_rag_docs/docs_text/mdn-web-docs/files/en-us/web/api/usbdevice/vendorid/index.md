Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md

Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md Path: mdn-web-docs > files > en-us > web > api > usbdevice > vendorid > index.md --- title: "USBDevice: vendorId property" short-title: vendorId slug: Web/API/USBDevice/vendorId page-type: web-api-instance-property status: - experimental browser-compat: api.USBDevice.vendorId --- {{APIRef("WebUSB API")}}{{SeeCompatTable}}{{SecureContext_Header}}{{AvailableInWorkers}} The **`vendorId`** read only property of the {{domxref("USBDevice")}} interface is the official usb.org-assigned vendor ID. ## Value The official usb.org-assigned vendor ID. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}