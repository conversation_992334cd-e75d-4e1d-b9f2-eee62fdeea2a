Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md

Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md Path: mdn-web-docs > files > en-us > web > api > videocolorspace > tojson > index.md --- title: "VideoColorSpace: toJSON() method" short-title: toJSON() slug: Web/API/VideoColorSpace/toJSON page-type: web-api-instance-method browser-compat: api.VideoColorSpace.toJSON --- {{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`toJSON()`** method of the {{domxref("VideoColorSpace")}} interface is a _serializer_ that returns a JSON representation of the `VideoColorSpace` object. ## Syntax ```js-nolint toJSON() ``` ### Parameters None. ### Return value A JSON object. ## Examples In the following example, `colorSpace` is a `VideoColorSpace` object returned from {{domxref("VideoFrame")}}. This object is then printed to the console as JSON. ```js let colorSpace = VideoFrame.colorSpace; console.log(colorSpace.toJSON()); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}