Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md

Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > close > index.md --- title: "VideoEncoder: close() method" short-title: close() slug: Web/API/VideoEncoder/close page-type: web-api-instance-method browser-compat: api.VideoEncoder.close --- {{APIRef("WebCodecs API")}}{{SecureContext_Header}}{{AvailableInWorkers("window_and_dedicated")}} The **`close()`** method of the {{domxref("VideoEncoder")}} interface ends all pending work and releases system resources. ## Syntax ```js-nolint close() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Examples The following example closes the `VideoEncoder`. ```js VideoEncoder.close(); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}