Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md

Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md Path: mdn-web-docs > files > en-us > web > api > videoencoder > dequeue_event > index.md --- title: "VideoEncoder: dequeue event" short-title: dequeue slug: Web/API/VideoEncoder/dequeue_event page-type: web-api-event browser-compat: api.VideoEncoder.dequeue_event --- {{securecontext_header}}{{APIRef("WebCodecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`dequeue`** event of the {{domxref("VideoEncoder")}} interface fires to signal a decrease in {{domxref("VideoEncoder.encodeQueueSize")}}. This eliminates the need for developers to use a {{domxref("Window.setTimeout", "setTimeout()")}} poll to determine when the queue has decreased, and more work should be queued up. ## Syntax Use the event name in methods like {{domxref("EventTarget.addEventListener", "addEventListener()")}}, or set an event handler property. ```js-nolint addEventListener("dequeue", (event) => { }) ondequeue = (event) => { } ``` ## Example ```js videoEncoder.addEventListener("dequeue", (event) => { // Queue up more encoding work }); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}