Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > close > index.md --- title: "VideoFrame: close() method" short-title: close() slug: Web/API/VideoFrame/close page-type: web-api-instance-method browser-compat: api.VideoFrame.close --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`close()`** method of the {{domxref("VideoFrame")}} interface clears all states and releases the reference to the media resource. ## Syntax ```js-nolint close() ``` ### Parameters None. ### Return value Undefined. ## Examples The following example shows the `VideoFrame` object being closed. ```js VideoFrame.close(); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}