Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > codedwidth > index.md --- title: "VideoFrame: codedWidth property" short-title: codedWidth slug: Web/API/VideoFrame/codedWidth page-type: web-api-instance-property browser-compat: api.VideoFrame.codedWidth --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`codedWidth`** property of the {{domxref("VideoFrame")}} interface returns the width of the `VideoFrame` in pixels, potentially including non-visible padding, and prior to considering potential ratio adjustments. ## Value An integer. ## Examples The following example prints the `codedWidth` to the console. ```js console.log(VideoFrame.codedWidth); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}