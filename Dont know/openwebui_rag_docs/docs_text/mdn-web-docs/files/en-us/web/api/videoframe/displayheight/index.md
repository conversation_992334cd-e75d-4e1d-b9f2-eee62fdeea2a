Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displayheight > index.md --- title: "VideoFrame: displayHeight property" short-title: displayHeight slug: Web/API/VideoFrame/displayHeight page-type: web-api-instance-property browser-compat: api.VideoFrame.displayHeight --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`displayHeight`** property of the {{domxref("VideoFrame")}} interface returns the height of the `VideoFrame` after applying aspect ratio adjustments. ## Value An integer. ## Examples The following example prints the `displayHeight` to the console. ```js console.log(VideoFrame.displayHeight); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}