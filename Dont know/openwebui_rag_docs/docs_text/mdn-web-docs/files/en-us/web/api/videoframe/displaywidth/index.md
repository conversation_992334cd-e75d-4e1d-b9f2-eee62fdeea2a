Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > displaywidth > index.md --- title: "VideoFrame: displayWidth property" short-title: displayWidth slug: Web/API/VideoFrame/displayWidth page-type: web-api-instance-property browser-compat: api.VideoFrame.displayWidth --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`displayWidth`** property of the {{domxref("VideoFrame")}} interface returns the width of the `VideoFrame` after applying aspect ratio adjustments. ## Value An integer. ## Examples The following example prints the `displayWidth` to the console. ```js console.log(VideoFrame.displayWidth); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}