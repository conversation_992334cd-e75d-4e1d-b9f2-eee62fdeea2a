Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > duration > index.md --- title: "VideoFrame: duration property" short-title: duration slug: Web/API/VideoFrame/duration page-type: web-api-instance-property browser-compat: api.VideoFrame.duration --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`duration`** property of the {{domxref("VideoFrame")}} interface returns an integer indicating the duration of the video in microseconds. ## Value An integer. ## Examples The following example prints the `duration` to the console. ```js console.log(VideoFrame.duration); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}