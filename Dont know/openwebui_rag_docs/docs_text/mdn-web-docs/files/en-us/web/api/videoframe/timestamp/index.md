Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > timestamp > index.md --- title: "VideoFrame: timestamp property" short-title: timestamp slug: Web/API/VideoFrame/timestamp page-type: web-api-instance-property browser-compat: api.VideoFrame.timestamp --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`timestamp`** property of the {{domxref("VideoFrame")}} interface returns an integer indicating the timestamp of the video in microseconds. ## Value An integer. ## Examples The following example prints the `timestamp` to the console. ```js console.log(VideoFrame.timestamp); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}