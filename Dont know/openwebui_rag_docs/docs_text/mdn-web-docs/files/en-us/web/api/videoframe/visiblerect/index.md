Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md

Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md Path: mdn-web-docs > files > en-us > web > api > videoframe > visiblerect > index.md --- title: "VideoFrame: visibleRect property" short-title: visibleRect slug: Web/API/VideoFrame/visibleRect page-type: web-api-instance-property browser-compat: api.VideoFrame.visibleRect --- {{APIRef("Web Codecs API")}}{{AvailableInWorkers("window_and_dedicated")}} The **`visibleRect`** property of the {{domxref("VideoFrame")}} interface returns a {{domxref("DOMRectReadOnly")}} describing the visible rectangle of pixels for this `VideoFrame`. ## Value A {{domxref("DOMRectReadOnly")}}. ## Examples The following example prints the `visibleRect` to the console. ```js console.log(VideoFrame.visibleRect); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}