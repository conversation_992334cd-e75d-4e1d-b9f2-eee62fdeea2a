Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md

Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > track > index.md --- title: "VideoTrackGenerator: track property" short-title: track slug: Web/API/VideoTrackGenerator/track page-type: web-api-instance-property status: - experimental browser-compat: api.VideoTrackGenerator.track --- {{APIRef("Insertable Streams for MediaStreamTrack API")}}{{SeeCompatTable}} The **`track`** property of the {{domxref("VideoTrackGenerator")}} interface returns a {{domxref("MediaStreamTrack")}}. {{domxref("VideoFrame")}}s written to {{domxref("VideoTrackGenerator.writable")}} will be generated in this track. ## Value A video {{domxref("MediaStreamTrack")}}. ## Examples See the [Insertable Streams for MediaStreamTrack API](/en-US/docs/Web/API/Insertable_Streams_for_MediaStreamTrack_API#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}