Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md

Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md Path: mdn-web-docs > files > en-us > web > api > videotrackgenerator > writable > index.md --- title: "VideoTrackGenerator: writable property" short-title: writable slug: Web/API/VideoTrackGenerator/writable page-type: web-api-instance-property status: - experimental browser-compat: api.VideoTrackGenerator.writable --- {{APIRef("Insertable Streams for MediaStreamTrack API")}}{{SeeCompatTable}} The **`writable`** property of the {{domxref("VideoTrackGenerator")}} interface returns a {{domxref("WritableStream")}}. This allows the writing of {{domxref("VideoFrame")}}s to the {{domxref("VideoTrackGenerator.track")}}. ## Value A {{domxref("WritableStream")}} of {{domxref("VideoFrame")}}s. ## Examples See the [Insertable Streams for MediaStreamTrack API](/en-US/docs/Web/API/Insertable_Streams_for_MediaStreamTrack_API#examples) for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}