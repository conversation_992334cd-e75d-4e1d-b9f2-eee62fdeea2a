Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md

Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md Path: mdn-web-docs > files > en-us > web > api > videotracklist > selectedindex > index.md --- title: "VideoTrackList: selectedIndex property" short-title: selectedIndex slug: Web/API/VideoTrackList/selectedIndex page-type: web-api-instance-property browser-compat: api.VideoTrackList.selectedIndex --- {{APIRef("HTML DOM")}} The read-only **{{domxref("VideoTrackList")}}** property **`selectedIndex`** returns the index of the currently selected track, if any, or `-1` otherwise. ## Value A number indicating the index of the currently selected track, if any, or `-1` otherwise. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}