Path: mdn-web-docs > files > en-us > web > api > viewtimeline > endoffset > index.md

Path: mdn-web-docs > files > en-us > web > api > viewtimeline > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > endoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > endoffset > index.md --- title: "ViewTimeline: endOffset property" short-title: endOffset slug: Web/API/ViewTimeline/endOffset page-type: web-api-instance-property status: - experimental browser-compat: api.ViewTimeline.endOffset --- {{APIRef("Web Animations")}}{{SeeCompatTable}} The **`endOffset`** read-only property of the {{domxref("ViewTimeline")}} interface returns a {{domxref("CSSNumericValue")}} representing the ending (100% progress) scroll position of the timeline as an offset from the start of the overflowing section of content in the scroller. ## Value A {{domxref("CSSNumericValue")}}. ## Examples See the main {{domxref("ScrollTimeline")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - [CSS scroll-driven animations](/en-US/docs/Web/CSS/CSS_scroll-driven_animations) - {{domxref("ViewTimeline")}} - {{domxref("AnimationTimeline")}}, {{domxref("ScrollTimeline")}}