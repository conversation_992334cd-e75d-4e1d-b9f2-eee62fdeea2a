Path: mdn-web-docs > files > en-us > web > api > viewtimeline > startoffset > index.md

Path: mdn-web-docs > files > en-us > web > api > viewtimeline > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > startoffset > index.md Path: mdn-web-docs > files > en-us > web > api > viewtimeline > startoffset > index.md --- title: "ViewTimeline: startOffset property" short-title: startOffset slug: Web/API/ViewTimeline/startOffset page-type: web-api-instance-property status: - experimental browser-compat: api.ViewTimeline.startOffset --- {{APIRef("Web Animations")}}{{SeeCompatTable}} The **`startOffset`** read-only property of the {{domxref("ViewTimeline")}} interface returns a {{domxref("CSSNumericValue")}} representing the starting (0% progress) scroll position of the timeline as an offset from the start of the overflowing section of content in the scroller. ## Value A {{domxref("CSSNumericValue")}}. ## Examples See the main {{domxref("ScrollTimeline")}} page for an example. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Web Animations API](/en-US/docs/Web/API/Web_Animations_API) - [CSS scroll-driven animations](/en-US/docs/Web/CSS/CSS_scroll-driven_animations) - {{domxref("ViewTimeline")}} - {{domxref("AnimationTimeline")}}, {{domxref("ScrollTimeline")}}