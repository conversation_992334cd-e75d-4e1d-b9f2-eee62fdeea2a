Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > height > index.md --- title: "VisualViewport: height property" short-title: height slug: Web/API/VisualViewport/height page-type: web-api-instance-property browser-compat: api.VisualViewport.height --- {{APIRef("Visual Viewport")}} The **`height`** read-only property of the {{domxref("VisualViewport")}} interface returns the height of the visual viewport, in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}