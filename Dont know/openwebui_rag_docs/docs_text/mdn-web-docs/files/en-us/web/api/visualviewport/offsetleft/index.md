Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsetleft > index.md --- title: "VisualViewport: offsetLeft property" short-title: offsetLeft slug: Web/API/VisualViewport/offsetLeft page-type: web-api-instance-property browser-compat: api.VisualViewport.offsetLeft --- {{APIRef("Visual Viewport")}} The **`offsetLeft`** read-only property of the {{domxref("VisualViewport")}} interface returns the offset of the left edge of the visual viewport from the left edge of the layout viewport in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}