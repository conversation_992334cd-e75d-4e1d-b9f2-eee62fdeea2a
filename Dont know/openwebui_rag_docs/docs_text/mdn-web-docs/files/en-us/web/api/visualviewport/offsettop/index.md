Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > offsettop > index.md --- title: "VisualViewport: offsetTop property" short-title: offsetTop slug: Web/API/VisualViewport/offsetTop page-type: web-api-instance-property browser-compat: api.VisualViewport.offsetTop --- {{APIRef("Visual Viewport")}} The **`offsetTop`** read-only property of the {{domxref("VisualViewport")}} interface returns the offset of the top edge of the visual viewport from the top edge of the layout viewport in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}