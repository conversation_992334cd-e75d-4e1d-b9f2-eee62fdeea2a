Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pageleft > index.md --- title: "VisualViewport: pageLeft property" short-title: pageLeft slug: Web/API/VisualViewport/pageLeft page-type: web-api-instance-property browser-compat: api.VisualViewport.pageLeft --- {{APIRef("Visual Viewport")}} The **`pageLeft`** read-only property of the {{domxref("VisualViewport")}} interface returns the x coordinate of the left edge of the visual viewport relative to the initial containing block origin, in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}