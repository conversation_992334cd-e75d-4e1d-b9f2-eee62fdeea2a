Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > pagetop > index.md --- title: "VisualViewport: pageTop property" short-title: pageTop slug: Web/API/VisualViewport/pageTop page-type: web-api-instance-property browser-compat: api.VisualViewport.pageTop --- {{APIRef("Visual Viewport")}} The **`pageTop`** read-only property of the {{domxref("VisualViewport")}} interface returns the y coordinate of the top edge of the visual viewport relative to the initial containing block origin, in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}