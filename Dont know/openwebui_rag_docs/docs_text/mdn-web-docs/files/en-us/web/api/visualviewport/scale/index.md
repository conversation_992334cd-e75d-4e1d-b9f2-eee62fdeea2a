Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > scale > index.md --- title: "VisualViewport: scale property" short-title: scale slug: Web/API/VisualViewport/scale page-type: web-api-instance-property browser-compat: api.VisualViewport.scale --- {{APIRef("Visual Viewport")}} The **`scale`** read-only property of the {{domxref("VisualViewport")}} interface returns the pinch-zoom scaling factor applied to the visual viewport, or `0` if current document is not fully active, or `1` if there is no output device. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}