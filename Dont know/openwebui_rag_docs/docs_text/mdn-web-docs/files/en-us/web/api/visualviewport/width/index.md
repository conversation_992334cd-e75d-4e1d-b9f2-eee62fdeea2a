Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md

Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md Path: mdn-web-docs > files > en-us > web > api > visualviewport > width > index.md --- title: "VisualViewport: width property" short-title: width slug: Web/API/VisualViewport/width page-type: web-api-instance-property browser-compat: api.VisualViewport.width --- {{APIRef("Visual Viewport")}} The **`width`** read-only property of the {{domxref("VisualViewport")}} interface returns the width of the visual viewport, in CSS pixels, or `0` if current document is not fully active. ## Value A double. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}