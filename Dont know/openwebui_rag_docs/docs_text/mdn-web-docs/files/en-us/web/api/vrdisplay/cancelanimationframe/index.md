Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > cancelanimationframe > index.md --- title: "VRDisplay: cancelAnimationFrame() method" short-title: cancelAnimationFrame() slug: Web/API/VRDisplay/cancelAnimationFrame page-type: web-api-instance-method status: - deprecated - non-standard browser-compat: api.VRDisplay.cancelAnimationFrame --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`cancelAnimationFrame()`** method of the {{domxref("VRDisplay")}} interface is a special implementation of {{domxref("Window.cancelAnimationFrame")}} that unregisters callbacks registered with {{domxref("VRDisplay.requestAnimationFrame()")}}. > [!NOTE] > This method was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Syntax ```js-nolint cancelAnimationFrame(handle) ``` ### Parameters - `handle` - : The handle returned by the {{domxref("VRDisplay.requestAnimationFrame()")}} call that you want to unregister. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js canvas.width = window.innerWidth; canvas.height = window.innerHeight; drawScene(); // WebVR: Check to see if WebVR is supported if (navigator.getVRDisplays) { console.log("WebVR 1.1 supported"); // Then get the displays attached to the computer navigator.getVRDisplays().then((displays) => { // If a display is available, use it to present the scene if (displays.length > 0) { vrDisplay = displays[0]; console.log("Display found"); // Starting the presentation when the button is clicked: It can only be called in response to a user gesture btn.addEventListener("click", () => { if (btn.textContent === "Start VR display") { vrDisplay.requestPresent([{ source: canvas }]).then(() => { console.log("Presenting to WebVR display"); // Set the canvas size to the size of the vrDisplay viewport const leftEye = vrDisplay.getEyeParameters("left"); const rightEye = vrDisplay.getEyeParameters("right"); canvas.width = Math.max(leftEye.renderWidth, rightEye.renderWidth) * 2; canvas.height = Math.max( leftEye.renderHeight, rightEye.renderHeight, ); // stop the normal presentation, and start the vr presentation window.cancelAnimationFrame(normalSceneFrame); drawVRScene(); btn.textContent = "Exit VR display"; }); } else { vrDisplay.exitPresent(); console.log("Stopped presenting to WebVR display"); btn.textContent = "Start VR display"; // Stop the VR presentation, and start the normal presentation vrDisplay.cancelAnimationFrame(vrSceneFrame); drawScene(); } }); } }); } else { info.textContent = "WebVR API not supported by this browser."; } function drawVRScene() { // WebVR: Request the next frame of the animation vrSceneFrame = vrDisplay.requestAnimationFrame(drawVRScene); // } ``` > [!NOTE] > You can see this complete code at [raw-webgl-example](https://github.com/mdn/webvr-tests/blob/main/webvr/raw-webgl-example/webgl-demo.js). ## Specifications This method was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)