Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > getframedata > index.md --- title: "VRDisplay: getFrameData() method" short-title: getFrameData() slug: Web/API/VRDisplay/getFrameData page-type: web-api-instance-method status: - deprecated - non-standard browser-compat: api.VRDisplay.getFrameData --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`getFrameData()`** method of the {{domxref("VRDisplay")}} interface accepts a {{domxref("VRFrameData")}} object and populates it with the information required to render the current frame. > [!NOTE] > This method was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). This includes the {{domxref("VRPose")}} and view and projection matrices for the current frame. ## Syntax ```js-nolint getFrameData(frameData) ``` ### Parameters - `frameData` - : The {{domxref("VRFrameData")}} object you want to populate. ### Return value A boolean value a value of `true` is returned if the {{domxref("VRFrameData")}} object was successfully populated, or `false` if it wasn't. ## Examples ```js const frameData = new VRFrameData(); let vrDisplay; navigator.getVRDisplays().then((displays) => { vrDisplay = displays[0]; console.log("Display found"); // Starting the presentation when the button is clicked: It can only be called in response to a user gesture btn.addEventListener("click", () => { vrDisplay.requestPresent([{ source: canvas }]).then(() => { drawVRScene(); }); }); }); // WebVR: Draw the scene for the WebVR display. function drawVRScene() { // WebVR: Request the next frame of the animation vrSceneFrame = vrDisplay.requestAnimationFrame(drawVRScene); // Populate frameData with the data of the next frame to display vrDisplay.getFrameData(frameData); // You can get the position, orientation, etc. of the display from the current frame's pose // curFramePose is a VRPose object const curFramePose = frameData.pose; const curPos = curFramePose.position; const curOrient = curFramePose.orientation; // Clear the canvas before we start drawing on it. gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT); // WebVR: Create the required projection and view matrix locations needed // for passing into the uniformMatrix4fv methods below const projectionMatrixLocation = gl.getUniformLocation( shaderProgram, "projMatrix", ); const viewMatrixLocation = gl.getUniformLocation(shaderProgram, "viewMatrix"); // WebVR: Render the left eye's view to the left half of the canvas gl.viewport(0, 0, canvas.width * 0.5, canvas.height); gl.uniformMatrix4fv( projectionMatrixLocation, false, frameData.leftProjectionMatrix, ); gl.uniformMatrix4fv(viewMatrixLocation, false, frameData.leftViewMatrix); drawGeometry(); // WebVR: Render the right eye's view to the right half of the canvas gl.viewport(canvas.width * 0.5, 0, canvas.width * 0.5, canvas.height); gl.uniformMatrix4fv( projectionMatrixLocation, false, frameData.rightProjectionMatrix, ); gl.uniformMatrix4fv(viewMatrixLocation, false, frameData.rightViewMatrix); drawGeometry(); function drawGeometry() { // draw the view for each eye } // // WebVR: Indicate that we are ready to present the rendered frame to the VR display vrDisplay.submitFrame(); } ``` > [!NOTE] > You can see this complete code at [raw-webgl-example](https://github.com/mdn/webvr-tests/blob/main/webvr/raw-webgl-example/webgl-demo.js). ## Specifications This method was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)