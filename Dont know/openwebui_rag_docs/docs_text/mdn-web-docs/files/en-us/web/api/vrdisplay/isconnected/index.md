Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > isconnected > index.md --- title: "VRDisplay: isConnected property" short-title: isConnected slug: Web/API/VRDisplay/isConnected page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.VRDisplay.isConnected --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`isConnected`** read-only property of the {{domxref("VRDisplay")}} interface returns a boolean value indicating whether the `VRDisplay` is connected to the computer. > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value A boolean value; `true` means the display is connected; `false` means it isn't. ## Examples ```js navigator.getVRDisplays().then((displays) => { // If a display is available, use it to present the scene if (displays.length > 0) { vrDisplay = displays[0]; // Starting the presentation when the button is clicked: It can only be called in response to a user gesture btn.addEventListener("click", () => { // Only request presentation if the display is still connected. if (vrDisplay.isConnected) { vrDisplay.requestPresent([{ source: canvas }]).then(() => { // start rendering the app, etc. }); } else { console.log("Connection to display lost"); } }); } }); ``` ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)