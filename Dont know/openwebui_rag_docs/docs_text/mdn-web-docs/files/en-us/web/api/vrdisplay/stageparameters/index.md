Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplay > stageparameters > index.md --- title: "VRDisplay: stageParameters property" short-title: stageParameters slug: Web/API/VRDisplay/stageParameters page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.VRDisplay.stageParameters --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`stageParameters`** read-only property of the {{domxref("VRDisplay")}} interface returns a {{domxref("VRStageParameters")}} object containing room-scale parameters, if the `VRDisplay` is capable of supporting room-scale experiences. > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value {{domxref("VRStageParameters")}} object containing the `VRDisplay`'s room-scale parameters, or `null` if the `VRDisplay` is not capable of supporting room-scale experiences. ## Examples See [`VRStageParameters`](/en-US/docs/Web/API/VRStageParameters#examples) for example code. ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)