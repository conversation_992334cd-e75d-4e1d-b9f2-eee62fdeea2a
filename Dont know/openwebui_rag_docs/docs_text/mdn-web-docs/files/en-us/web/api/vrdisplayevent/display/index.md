Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > display > index.md --- title: "VRDisplayEvent: display property" short-title: display slug: Web/API/VRDisplayEvent/display page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.VRDisplayEvent.display --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`display`** read-only property of the {{domxref("VRDisplayEvent")}} interface returns the {{domxref("VRDisplay")}} associated with this event. > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value A {{domxref("VRDisplay")}} object. ## Examples ```js window.addEventListener("vrdisplaypresentchange", (e) => { console.log( `Display ${e.display.displayId} presentation has changed. Reason given: ${e.reason}.`, ); }); ``` ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)