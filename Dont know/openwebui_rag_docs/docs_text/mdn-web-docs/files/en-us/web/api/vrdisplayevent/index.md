Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md

Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md Path: mdn-web-docs > files > en-us > web > api > vrdisplayevent > index.md --- title: VRDisplayEvent slug: Web/API/VRDisplayEvent page-type: web-api-interface status: - deprecated - non-standard browser-compat: api.VRDisplayEvent --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`VRDisplayEvent`** interface of the [WebVR API](/en-US/docs/Web/API/WebVR_API) represents the event object of WebVR-related events (see the [list of WebVR window extensions](/en-US/docs/Web/API/WebVR_API#window_events)). > [!NOTE] > This interface was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Constructor - {{domxref("VRDisplayEvent.VRDisplayEvent", "VRDisplayEvent()")}} {{Deprecated_Inline}} {{Non-standard_Inline}} - : Creates a `VRDisplayEvent` object instance. ## Instance properties _`VRDisplayEvent` also inherits properties from its parent object, {{domxref("Event")}}._ - {{domxref("VRDisplayEvent.display")}} {{Deprecated_Inline}} {{ReadOnlyInline}} {{Non-standard_Inline}} - : The {{domxref("VRDisplay")}} associated with this event. - {{domxref("VRDisplayEvent.reason")}} {{Deprecated_Inline}} {{ReadOnlyInline}} {{Non-standard_Inline}} - : A human-readable reason why the event was fired. ## Examples ```js window.addEventListener("vrdisplaypresentchange", (e) => { console.log( `Display ${e.display.displayId} presentation has changed. Reason given: ${e.reason}.`, ); }); ``` ## Specifications This interface was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)