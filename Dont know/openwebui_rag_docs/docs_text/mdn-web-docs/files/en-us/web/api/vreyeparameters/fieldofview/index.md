Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md

Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md Path: mdn-web-docs > files > en-us > web > api > vreyeparameters > fieldofview > index.md --- title: "VREyeParameters: fieldOfView property" short-title: fieldOfView slug: Web/API/VREyeParameters/fieldOfView page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.VREyeParameters.fieldOfView --- {{APIRef("WebVR API")}}{{Deprecated_Header}}{{Non-standard_Header}} The **`fieldOfView`** read-only property of the {{domxref("VREyeParameters")}} interface returns a {{domxref("VRFieldOfView")}} object describing the current field of view for the eye, which can vary as the user adjusts their interpupillary distance (IPD). > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value A {{domxref("VRFieldOfView")}} object. ## Examples See [`VRFieldOfView`](/en-US/docs/Web/API/VRFieldOfView#examples) for example code. ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)