Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md

Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md Path: mdn-web-docs > files > en-us > web > api > vrfieldofview > downdegrees > index.md --- title: "VRFieldOfView: downDegrees property" short-title: downDegrees slug: Web/API/VRFieldOfView/downDegrees page-type: web-api-instance-property status: - deprecated - non-standard browser-compat: api.VRFieldOfView.downDegrees --- {{APIRef("WebVR API")}}{{Deprecated_header}}{{Non-standard_header}} The **`downDegrees`** read-only property of the {{domxref("VRFieldOfView")}} interface returns the number of degrees downwards that the field of view extends in. > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value A double. ## Examples See [`VRFieldOfView`](/en-US/docs/Web/API/VRFieldOfView#examples) for example code. ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## Browser compatibility {{Compat}} ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)