Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md

Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md Path: mdn-web-docs > files > en-us > web > api > vrlayerinit > source > index.md --- title: "VRLayerInit: source property" short-title: source slug: Web/API/VRLayerInit/source page-type: web-api-instance-property status: - deprecated --- {{APIRef("WebVR API")}}{{Deprecated_Header}} The **`source`** property of the {{domxref("VRLayerInit")}} interface (dictionary) defines the canvas whose contents will be presented by the {{domxref("VRDisplay")}}. > [!NOTE] > This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/). It has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). ## Value An {{domxref("HTMLCanvasElement")}} or {{domxref("OffscreenCanvas")}} object. ## Examples See [`VRLayerInit`](/en-US/docs/Web/API/VRLayerInit#examples) for example code. ## Specifications This property was part of the old [WebVR API](https://immersive-web.github.io/webvr/spec/1.1/) that has been superseded by the [WebXR Device API](https://immersive-web.github.io/webxr/). It is no longer on track to becoming a standard. Until all browsers have implemented the new [WebXR APIs](/en-US/docs/Web/API/WebXR_Device_API/Fundamentals), it is recommended to rely on frameworks, like [A-Frame](https://aframe.io/), [Babylon.js](https://www.babylonjs.com/), or [Three.js](https://threejs.org/), or a [polyfill](https://github.com/immersive-web/webxr-polyfill), to develop WebXR applications that will work across all browsers. Read [Meta's Porting from WebVR to WebXR](https://developers.meta.com/horizon/documentation/web/port-vr-xr/) guide for more information. ## See also - [WebVR API](/en-US/docs/Web/API/WebVR_API)