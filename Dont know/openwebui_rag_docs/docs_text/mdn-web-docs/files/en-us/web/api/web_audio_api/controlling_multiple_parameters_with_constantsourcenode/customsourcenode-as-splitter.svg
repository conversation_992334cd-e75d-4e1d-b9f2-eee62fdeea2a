<svg xmlns="http://www.w3.org/2000/svg" viewBox="7 7 580 346" width="580pt" height="346pt"><defs><marker orient="auto" overflow="visible" markerUnits="strokeWidth" id="a" viewBox="-1 -3 7 6" markerWidth="7" markerHeight="6" color="#000"><path d="M4.8 0 0-1.8v3.6z" fill="currentColor" stroke="currentColor"/></marker></defs><g fill="none"><path fill="#867fff" d="M207 99h180v45H207z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M207 99h180v45H207z"/><text transform="translate(212 113)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x="9.388" y="14" textLength="151.225">ConstantSourceNode</tspan></text><path fill="#867fff" d="M9 216h180v45H9z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M9 216h180v45H9z"/><text transform="translate(14 230)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x="51.395" y="14" textLength="67.211">GainNode</tspan></text><path fill="#867fff" d="M405 216h180v45H405z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M405 216h180v45H405z"/><text transform="translate(410 230)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x="51.395" y="14" textLength="67.211">GainNode</tspan></text><path fill="#867fff" d="M207 216h180v45H207z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M207 216h180v45H207z"/><text transform="translate(212 230)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x="17.789" y="14" textLength="134.422">StereoPannerNode</tspan></text><path d="M252 144v27H99v32.1M297 144v59.1m45-59.1v27h153v32.1" marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(55.876 192.447)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x=".197" y="14" textLength="33.605">gain</tspan></text><text transform="translate(258.64 192.854)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x=".398" y="14" textLength="25.204">pan</tspan></text><text transform="translate(504.37 193.347)" fill="#000"><tspan font-family="Courier" font-size="14" font-weight="500" x=".197" y="14" textLength="33.605">gain</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M297 54v32.1"/><path d="M243 9h144l-36 45H207z" fill="#fff"/><path d="M243 9h144l-36 45H207z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(248 22)" fill="#000"><tspan font-family="Arial" font-size="16" font-weight="500" x="17.734" y="15" textLength="52.93">input = </tspan><tspan font-family="Courier" font-size="16" font-style="italic" font-weight="500" x="70.664" y="15" textLength="9.602">N</tspan></text><path d="M243 306h144l-36 45H207z" fill="#fff"/><path d="M243 306h144l-36 45H207z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(248 319)" fill="#000"><tspan font-family="Arial" font-size="16" font-weight="500" x="12.84" y="15" textLength="62.719">output = </tspan><tspan font-family="Courier" font-size="16" font-style="italic" font-weight="500" x="75.559" y="15" textLength="9.602">N</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m296.5 261 .357 32.101"/><path d="M441 306h144l-36 45H405z" fill="#fff"/><path d="M441 306h144l-36 45H405z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(446 319)" fill="#000"><tspan font-family="Arial" font-size="16" font-weight="500" x="12.84" y="15" textLength="62.719">output = </tspan><tspan font-family="Courier" font-size="16" font-style="italic" font-weight="500" x="75.559" y="15" textLength="9.602">N</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M495 261v32.1"/><path d="M45 306h144l-36 45H9z" fill="#fff"/><path d="M45 306h144l-36 45H9z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(50 319)" fill="#000"><tspan font-family="Arial" font-size="16" font-weight="500" x="12.84" y="15" textLength="62.719">output = </tspan><tspan font-family="Courier" font-size="16" font-style="italic" font-weight="500" x="75.559" y="15" textLength="9.602">N</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M99 261v32.1"/></g></svg>