Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md

Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > compressedteximage3d > index.md --- title: "WebGL2RenderingContext: compressedTexImage3D() method" short-title: compressedTexImage3D() slug: Web/API/WebGL2RenderingContext/compressedTexImage3D page-type: web-api-instance-method browser-compat: api.WebGL2RenderingContext.compressedTexImage3D --- {{APIRef("WebGL")}}{{AvailableInWorkers}} The **`compressedTexImage3D()`** method of the {{domxref("WebGL2RenderingContext")}} interface of the [WebGL API](/en-US/docs/Web/API/WebGL_API) specifies a three-dimensional texture image in a compressed format. Compressed image formats must be enabled by [WebGL extensions](/en-US/docs/Web/API/WebGL_API/Using_Extensions) before using these methods. ## Syntax ```js-nolint // read from buffer bound to gl.PIXEL_UNPACK_BUFFER compressedTexImage3D(target, level, internalformat, width, height, depth, border, imageSize, offset) compressedTexImage3D(target, level, internalformat, width, height, depth, border, srcData) compressedTexImage3D(target, level, internalformat, width, height, depth, border, srcData, srcOffset) compressedTexImage3D(target, level, internalformat, width, height, depth, border, srcData, srcOffset, srcLengthOverride) ``` ### Parameters - `target` - : A {{domxref("WebGL_API/Types", "GLenum")}} specifying the binding point (target) of the active texture. Possible values for `compressedTexImage3D`: - `gl.TEXTURE_2D_ARRAY` - `gl.TEXTURE_3D` - `level` - : A {{domxref("WebGL_API/Types", "GLint")}} specifying the level of detail. Level 0 is the base image level and level _n_ is the n-th mipmap reduction level. - `internalformat` - : A {{domxref("WebGL_API/Types", "GLenum")}} specifying the compressed image format. Compressed image formats must be enabled by [WebGL extensions](/en-US/docs/Web/API/WebGL_API/Using_Extensions) before using this method. See [compressed texture formats](/en-US/docs/Web/API/WebGL_API/Compressed_texture_formats) for which are valid for `compressedTexImage3D`. Possible values: - When using the {{domxref("WEBGL_compressed_texture_s3tc")}} extension: - `ext.COMPRESSED_RGB_S3TC_DXT1_EXT` - `ext.COMPRESSED_RGBA_S3TC_DXT1_EXT` - `ext.COMPRESSED_RGBA_S3TC_DXT3_EXT` - `ext.COMPRESSED_RGBA_S3TC_DXT5_EXT` - When using the {{domxref("WEBGL_compressed_texture_s3tc_srgb")}} extension: - `ext.COMPRESSED_SRGB_S3TC_DXT1_EXT` - `ext.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT` - `ext.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT` - `ext.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT` - When using the {{domxref("WEBGL_compressed_texture_etc")}} extension: - `ext.COMPRESSED_R11_EAC` - `ext.COMPRESSED_SIGNED_R11_EAC` - `ext.COMPRESSED_RG11_EAC` - `ext.COMPRESSED_SIGNED_RG11_EAC` - `ext.COMPRESSED_RGB8_ETC2` - `ext.COMPRESSED_RGBA8_ETC2_EAC` - `ext.COMPRESSED_SRGB8_ETC2` - `ext.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC` - `ext.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2` - `ext.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2` - When using the {{domxref("WEBGL_compressed_texture_pvrtc")}} extension: - `ext.COMPRESSED_RGB_PVRTC_4BPPV1_IMG` - `ext.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG` - `ext.COMPRESSED_RGB_PVRTC_2BPPV1_IMG` - `ext.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG` - When using the {{domxref("WEBGL_compressed_texture_etc1")}} extension: - `ext.COMPRESSED_RGB_ETC1_WEBGL` - When using the {{domxref("WEBGL_compressed_texture_astc")}} extension: - `ext.COMPRESSED_RGBA_ASTC_4x4_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR` - `ext.COMPRESSED_RGBA_ASTC_5x4_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR` - `ext.COMPRESSED_RGBA_ASTC_5x5_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR` - `ext.COMPRESSED_RGBA_ASTC_6x5_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR` - `ext.COMPRESSED_RGBA_ASTC_6x6_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR` - `ext.COMPRESSED_RGBA_ASTC_8x5_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR` - `ext.COMPRESSED_RGBA_ASTC_8x6_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR` - `ext.COMPRESSED_RGBA_ASTC_8x8_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR` - `ext.COMPRESSED_RGBA_ASTC_10x5_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR` - `ext.COMPRESSED_RGBA_ASTC_10x6_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR` - `ext.COMPRESSED_RGBA_ASTC_10x10_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR` - `ext.COMPRESSED_RGBA_ASTC_12x10_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR` - `ext.COMPRESSED_RGBA_ASTC_12x12_KHR ext.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR` - When using the {{domxref("EXT_texture_compression_bptc")}} extension: - `ext.COMPRESSED_RGBA_BPTC_UNORM_EXT` - `ext.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT` - `ext.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT` - `ext.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT` - When using the {{domxref("EXT_texture_compression_rgtc")}} extension: - `ext.COMPRESSED_RED_RGTC1_EXT` - `ext.COMPRESSED_SIGNED_RED_RGTC1_EXT` - `ext.COMPRESSED_RED_GREEN_RGTC2_EXT` - `ext.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT` - `width` - : A {{domxref("WebGL_API/Types", "GLsizei")}} specifying the width of the texture. - `height` - : A {{domxref("WebGL_API/Types", "GLsizei")}} specifying the height of the texture. - `depth` - : A {{domxref("WebGL_API/Types", "GLsizei")}} specifying the depth of the texture/the number of textures in a `TEXTURE_2D_ARRAY`. - `border` - : A {{domxref("WebGL_API/Types", "GLint")}} specifying the width of the border. Must be 0. - `imageSize` - : A {{domxref("WebGL_API/Types", "GLsizei")}} specifying the number of bytes to read from the buffer bound to `gl.PIXEL_UNPACK_BUFFER`. - `offset` - : A {{domxref("WebGL_API/Types", "GLintptr")}} specifying the offset in bytes from which to read from the buffer bound to `gl.PIXEL_UNPACK_BUFFER`. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using WebGL extensions](/en-US/docs/Web/API/WebGL_API/Using_Extensions) - {{domxref("WebGL2RenderingContext.compressedTexSubImage3D()")}} - {{domxref("WebGLRenderingContext.compressedTexSubImage2D()")}} - {{domxref("WebGLRenderingContext.compressedTexImage2D()")}} - {{domxref("WEBGL_compressed_texture_s3tc")}} - {{domxref("WEBGL_compressed_texture_s3tc_srgb")}} - {{domxref("WEBGL_compressed_texture_etc")}} - {{domxref("WEBGL_compressed_texture_pvrtc")}} - {{domxref("WEBGL_compressed_texture_etc1")}} - {{domxref("WEBGL_compressed_texture_astc")}} - {{domxref("EXT_texture_compression_bptc")}} - {{domxref("EXT_texture_compression_rgtc")}}