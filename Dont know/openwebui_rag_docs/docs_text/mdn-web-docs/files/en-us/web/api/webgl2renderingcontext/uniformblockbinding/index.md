Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md

Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > uniformblockbinding > index.md --- title: "WebGL2RenderingContext: uniformBlockBinding() method" short-title: uniformBlockBinding() slug: Web/API/WebGL2RenderingContext/uniformBlockBinding page-type: web-api-instance-method browser-compat: api.WebGL2RenderingContext.uniformBlockBinding --- {{APIRef("WebGL")}}{{AvailableInWorkers}} The **`WebGL2RenderingContext.uniformBlockBinding()`** method of the [WebGL 2 API](/en-US/docs/Web/API/WebGL_API) assigns binding points for active uniform blocks. ## Syntax ```js-nolint uniformBlockBinding(program, uniformBlockIndex, uniformBlockBinding) ``` ### Parameters - `program` - : A {{domxref("WebGLProgram")}} containing the active uniform block whose binding to assign. - `uniformBlockIndex` - : A {{domxref("WebGL_API/Types", "GLuint")}} specifying the index of the active uniform block within the program. - `uniformBlockBinding` - : A {{domxref("WebGL_API/Types", "GLuint")}} specifying the binding point to which to bind the uniform block. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js gl.uniformBlockBinding(program, 0, 1); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("WebGL2RenderingContext.getUniformIndices()")}}