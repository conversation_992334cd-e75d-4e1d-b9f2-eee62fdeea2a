Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md

Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md Path: mdn-web-docs > files > en-us > web > api > webgl2renderingcontext > vertexattribdivisor > index.md --- title: "WebGL2RenderingContext: vertexAttribDivisor() method" short-title: vertexAttribDivisor() slug: Web/API/WebGL2RenderingContext/vertexAttribDivisor page-type: web-api-instance-method browser-compat: api.WebGL2RenderingContext.vertexAttribDivisor --- {{APIRef("WebGL")}}{{AvailableInWorkers}} The **`WebGL2RenderingContext.vertexAttribDivisor()`** method of the [WebGL 2 API](/en-US/docs/Web/API/WebGL_API) modifies the rate at which generic vertex attributes advance when rendering multiple instances of primitives with {{domxref("WebGL2RenderingContext.drawArraysInstanced()", "gl.drawArraysInstanced()")}} and {{domxref("WebGL2RenderingContext.drawElementsInstanced()", "gl.drawElementsInstanced()")}}. > [!NOTE] > When using {{domxref("WebGLRenderingContext", "WebGL 1", "", 1)}}, the {{domxref("ANGLE_instanced_arrays")}} extension can provide this method, > too. ## Syntax ```js-nolint vertexAttribDivisor(index, divisor) ``` ### Parameters - `index` - : A {{domxref("WebGL_API/Types", "GLuint")}} specifying the index of the generic vertex attributes. - `divisor` - : A {{domxref("WebGL_API/Types", "GLuint")}} specifying the number of instances that will pass between updates of the generic attribute. ### Return value None ({{jsxref("undefined")}}). ## Examples ```js gl.vertexAttribDivisor(0, 2); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("ANGLE_instanced_arrays.vertexAttribDivisorANGLE()")}}