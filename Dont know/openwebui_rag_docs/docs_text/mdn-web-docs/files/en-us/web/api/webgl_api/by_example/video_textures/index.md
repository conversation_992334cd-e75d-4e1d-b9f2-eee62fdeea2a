Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md

Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md Path: mdn-web-docs > files > en-us > web > api > webgl_api > by_example > video_textures > index.md --- title: Video textures slug: Web/API/WebGL_API/By_example/Video_textures page-type: guide --- {{DefaultAPISidebar("WebGL")}}{{Previous("Web/API/WebGL_API/By_example/Textures_from_code")}} This example demonstrates how to use video files as textures for WebGL surfaces. ## Textures from video {{EmbedGHLiveSample('dom-examples/webgl-examples/tutorial/sample8/index.html', 670, 510) }} The source code of this example is available on [GitHub](https://github.com/mdn/dom-examples/tree/main/webgl-examples/tutorial/sample8). {{Previous("Web/API/WebGL_API/By_example/Textures_from_code")}}