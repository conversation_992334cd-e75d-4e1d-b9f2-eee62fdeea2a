Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md

Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md Path: mdn-web-docs > files > en-us > web > api > webglrenderingcontext > flush > index.md --- title: "WebGLRenderingContext: flush() method" short-title: flush() slug: Web/API/WebGLRenderingContext/flush page-type: web-api-instance-method browser-compat: api.WebGLRenderingContext.flush --- {{APIRef("WebGL")}}{{AvailableInWorkers}} The **`WebGLRenderingContext.flush()`** method of the [WebGL API](/en-US/docs/Web/API/WebGL_API) empties different buffer commands, causing all commands to be executed as quickly as possible. ## Syntax ```js-nolint flush() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - {{domxref("WebGLRenderingContext.finish()")}}