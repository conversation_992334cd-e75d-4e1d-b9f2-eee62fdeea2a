Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md

Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md Path: mdn-web-docs > files > en-us > web > api > webgltransformfeedback > index.md --- title: WebGLTransformFeedback slug: Web/API/WebGLTransformFeedback page-type: web-api-interface browser-compat: api.WebGLTransformFeedback --- {{APIRef("WebGL")}}{{AvailableInWorkers}} The **`WebGLTransformFeedback`** interface is part of the [WebGL 2](/en-US/docs/Web/API/WebGL_API) API and enables transform feedback, which is the process of capturing primitives generated by vertex processing. It allows to preserve the post-transform rendering state of an object and resubmit this data multiple times. {{InheritanceDiagram}} When working with `WebGLTransformFeedback` objects, the following methods of the {{domxref("WebGL2RenderingContext")}} are useful: - {{domxref("WebGL2RenderingContext.createTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.deleteTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.isTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.bindTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.beginTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.endTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.pauseTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.resumeTransformFeedback()")}} - {{domxref("WebGL2RenderingContext.transformFeedbackVaryings()")}} - {{domxref("WebGL2RenderingContext.getTransformFeedbackVarying()")}} ## Examples ### Creating a `WebGLTransformFeedback` object in this example, `gl` must be a {{domxref("WebGL2RenderingContext")}}. `WebGLTransformFeedback` objects are not available in WebGL 1. ```js const transformFeedback = gl.createTransformFeedback(); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}