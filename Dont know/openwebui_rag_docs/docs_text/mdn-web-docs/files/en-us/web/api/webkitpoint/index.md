Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md

Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md Path: mdn-web-docs > files > en-us > web > api > webkitpoint > index.md --- title: Point slug: Web/API/WebKitPoint page-type: web-api-interface status: - deprecated - non-standard browser-compat: api.WebKitPoint --- {{APIRef("CSS3 Transforms")}}{{Deprecated_Header}}{{Non-standard_Header}} **`Point`** is an interface which represents a point in 2-dimensional space. It is non-standard, not broadly compatible, and should not be used. > [!NOTE] > Although it is not directly related to this defunct interface, you are probably looking for {{domxref("DOMPoint")}}. ## Instance properties - `x` {{Deprecated_Inline}} {{Non-standard_Inline}} - : A floating-point value specifying the point's position with respect to the X (horizontal) axis. - `y` {{Deprecated_Inline}} {{Non-standard_Inline}} - : A floating-point value specifying the point's position with respect to the Y (vertical) axis. ## Specifications This class was specified in [the defunct 20 March 2009 Working Draft of CSS 2D Transforms Module Level 3](https://www.w3.org/TR/2009/WD-css3-2d-transforms-20090320/). It is not present in any current specification. ## Browser compatibility {{Compat}} ## See also - {{domxref("Window.webkitConvertPointFromNodeToPage()")}} - {{domxref("Window.webkitConvertPointFromPageToNode()")}}