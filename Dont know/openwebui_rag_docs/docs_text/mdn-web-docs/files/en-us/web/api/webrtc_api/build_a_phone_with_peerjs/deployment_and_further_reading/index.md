Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md

Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md Path: mdn-web-docs > files > en-us > web > api > webrtc_api > build_a_phone_with_peerjs > deployment_and_further_reading > index.md --- title: Deployment and further reading slug: Web/API/WebRTC_API/Build_a_phone_with_peerjs/Deployment_and_further_reading page-type: guide --- {{DefaultAPISidebar("WebRTC")}} {{PreviousMenu("Web/API/WebRTC_API/Build_a_phone_with_peerjs/Connect_peers/End_a_call")}} ## Deployment The easiest place to deploy this app would be [Glitch](https://glitch.com/), since you don't have to fiddle with configuring the port for the peer server. ## Further Reading - [PeerJS](https://peerjs.com/) - [WebRTC](/en-US/docs/Web/API/WebRTC_API) - [PeerJS Server](https://github.com/peers/peerjs-server) - [A similar video tutorial with video](https://www.youtube.com/watch?v=OOrBcpwelPY) - [The code tutorial](https://github.com/SamsungInternet/WebPhone/tree/master/tutorial) {{PreviousMenu("Web/API/WebRTC_API/Build_a_phone_with_peerjs/Connect_peers/End_a_call")}}