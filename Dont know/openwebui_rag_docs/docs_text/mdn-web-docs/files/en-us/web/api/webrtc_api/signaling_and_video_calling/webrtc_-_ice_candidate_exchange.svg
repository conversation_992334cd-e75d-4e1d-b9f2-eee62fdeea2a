<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 918 774" width="1224" height="1032"><defs><marker orient="auto" overflow="visible" markerUnits="strokeWidth" id="a" viewBox="-1 -4 10 8" markerWidth="10" markerHeight="8" color="#000"><path d="M8 0L0-3v6z" fill="currentColor" stroke="currentColor"/></marker></defs><g fill="none"><path fill="#fff" d="M0 0h918v774H0z"/><path fill="#7465dc" fill-opacity=".5" d="M9 81h360v657H9z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M9 81h360v657H9z"/><path fill="#62d6ac" fill-opacity=".5" d="M549 81h360v657H549z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M549 81h360v657H549z"/><path fill="#be844a" fill-opacity=".5" d="M369 81h180v657H369z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M369 81h180v657H369z"/><path fill="#62d6ac" fill-opacity=".5" d="M549 9h360v72H549z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M549 9h360v72H549z"/><text transform="translate(554 31.5)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="bold" x="109.692" y="21" textLength="130.615">Priya (Callee)</tspan></text><path fill="#be844a" fill-opacity=".5" d="M369 9h180v72H369z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M369 9h180v72H369z"/><text transform="translate(374 31.5)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="bold" x="5.161" y="21" textLength="159.678">Signaling Server</tspan></text><path fill="#7465dc" fill-opacity=".5" d="M9 9h360v72H9z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M9 9h360v72H9z"/><text transform="translate(14 31.5)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="bold" x="103.428" y="21" textLength="143.145">Naomi (Caller)</tspan></text><path fill="#fff" fill-opacity=".503" d="M189 81h180v657H189z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M189 81h180v657H189z"/><path fill="#fff" fill-opacity=".503" d="M549 81h180v657H549z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M549 81h180v657H549z"/><path fill="#c9ffff" d="M27 280.68h144v80H27z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M27 280.68h144v80H27z"/><text transform="translate(32 285.68)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="129.526">Receives the candidate and </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="25" textLength="74.072">sends it to Priya</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="73.975" y="25" textLength="1.699">'</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="75.376" y="25" textLength="35.063">s client </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="13.75">thr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="13.55" y="39" textLength="119.78">ough the signaling server </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="53" textLength="21.089">as a </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="21.089" y="53" textLength="54.009">“new-ice-</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="0" y="67" textLength="60.01">candidate”</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="60.01" y="67" textLength="43.701"> message</tspan></text><path fill="#fff5c8" d="M27 261h144v19.68H27z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M27 261h144v19.68H27z"/><text transform="translate(32 265.84)" fill="#000"><tspan font-family="Courier" font-size="8" font-weight="500" x="6.99" y="8" textLength="120.02">handleICECandidateEvent()</tspan></text><path fill="#c9ffff" d="M207 126h144v52H207z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M207 126h144v52H207z"/><text transform="translate(212 131)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="28.721">Gener</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="28.521" y="11" textLength="97.397">ate an ICE candidate </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="25" textLength="4.082">r</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="3.882" y="25" textLength="15.82">epr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="19.502" y="25" textLength="46.123">esented b</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="65.425" y="25" textLength="43.33">y an SDP </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="26.528">string</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1,4" d="M207 178l-100.15 76.967"/><text transform="translate(59 194)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="31.108">Event: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="31.108" y="11" textLength="72.012">icecandidate</tspan></text><path fill="#c9ffff" d="M387 279h144v66H387z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M387 279h144v66H387z"/><text transform="translate(392 284)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="35.313">Receive</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="35.313" y="11" textLength="60.01"> “new-ice-</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="0" y="25" textLength="60.01">candidate”</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="60.01" y="25" textLength="66.724"> message and </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="30.933">forwar</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="30.732" y="39" textLength="52.783">d it to Priya</tspan></text><path fill="#fff5c8" d="M387 261h144v18H387z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M387 261h144v18H387z"/><text transform="translate(392 264)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="500" x="30.994" y="10" textLength="72.012">on.message()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4,4" d="M171 270.84l206.1-.802"/><text transform="translate(197.427 268.103)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x=".446" y="11" textLength="46.089">Message: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="46.535" y="11" textLength="114.019">“new-ice-candidate”</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4,4" d="M531 270h206.1"/><text transform="translate(560 267.56)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x=".196" y="11" textLength="46.089">Message: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="46.285" y="11" textLength="108.018">“new-ice-candidate</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="154.303" y="11" textLength="3.501">”</tspan></text><path fill="#c9ffff" d="M747 279h144v144H747z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M747 279h144v144H747z"/><text transform="translate(752 284)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="1" y="11" textLength="8.379">1.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="11" y="11" textLength="10.391">Cr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="21.19" y="11" textLength="37.207">eate an </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="25" textLength="90.015">RTCIceCandidate</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="100.965" y="25" textLength="33.794"> object </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="39" textLength="77.134">using the SDP pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="87.884" y="39" textLength="6.04">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="93.723" y="39" textLength="39.268">vided in </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="53" textLength="66.484">the candidate.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="1" y="67" textLength="8.379">2.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="11" y="67" textLength="113.848">Deliver the candidate to </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="81" textLength="23.232">Priya</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="34.085" y="81" textLength="1.699">'</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="35.486" y="81" textLength="56.172">s ICE layer b</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="91.458" y="81" textLength="7.637">y </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="95" textLength="58.799">passing it to </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="108" textLength="120.02">RTCPeerConnection.ad</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="120" textLength="90.015">dIceCandidate()</tspan></text><path fill="#fff5c8" d="M747 261h144v18H747z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M747 261h144v18H747z"/><text transform="translate(752 265)" fill="#000"><tspan font-family="Courier" font-size="8" font-weight="500" x="4.59" y="8" textLength="124.82">handleNewIceCandidateMsg()</tspan></text><path fill="#c9ffff" d="M747 586.68h144v80H747z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M747 586.68h144v80H747z"/><text transform="translate(752 591.68)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="129.526">Receives the candidate and </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="25" textLength="83.511">sends it to Naomi'</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="83.213" y="25" textLength="35.063">s client </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="13.75">thr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="13.55" y="39" textLength="119.78">ough the signaling server </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="53" textLength="21.089">as a </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="21.089" y="53" textLength="54.009">“new-ice-</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="0" y="67" textLength="60.01">candidate”</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="60.01" y="67" textLength="43.701"> message</tspan></text><path fill="#fff5c8" d="M747 567h144v19.68H747z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M747 567h144v19.68H747z"/><text transform="translate(752 571.84)" fill="#000"><tspan font-family="Courier" font-size="8" font-weight="500" x="6.99" y="8" textLength="120.02">handleICECandidateEvent()</tspan></text><path fill="#c9ffff" d="M567 441h144v52H567z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M567 441h144v52H567z"/><text transform="translate(572 446)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="28.721">Gener</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="28.521" y="11" textLength="97.397">ate an ICE candidate </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="25" textLength="4.082">r</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="3.882" y="25" textLength="15.82">epr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="19.502" y="25" textLength="46.123">esented b</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="65.425" y="25" textLength="43.33">y an SDP </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="26.528">string</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1,4" d="M711 493l99.833 68.404"/><text transform="translate(761 500)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="31.108">Event: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="31.108" y="11" textLength="72.012">icecandidate</tspan></text><path fill="#c9ffff" d="M387 585h144v66H387z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M387 585h144v66H387z"/><text transform="translate(392 590)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="11" textLength="37.91">Receive </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="37.91" y="11" textLength="54.009">“new-ice-</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="0" y="25" textLength="60.01">candidate”</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="60.01" y="25" textLength="66.724"> message and </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="0" y="39" textLength="30.933">forwar</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="30.732" y="39" textLength="60.522">d it to Naomi</tspan></text><path fill="#fff5c8" d="M387 567h144v18H387z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M387 567h144v18H387z"/><text transform="translate(392 570)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="500" x="30.994" y="10" textLength="72.012">on.message()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4,4" d="M747 576.84l-206.1-.802"/><text transform="translate(559.573 574.103)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x=".446" y="11" textLength="46.089">Message: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="46.535" y="11" textLength="114.019">“new-ice-candidate”</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4,4" d="M387 576H171.9"/><text transform="translate(194 573.56)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x=".446" y="11" textLength="46.089">Message: </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="46.535" y="11" textLength="114.019">“new-ice-candidate”</tspan></text><path fill="#c9ffff" d="M18 585h144v144H18z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M18 585h144v144H18z"/><text transform="translate(23 590)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="500" x="1" y="11" textLength="8.379">1.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="11" y="11" textLength="10.391">Cr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="21.19" y="11" textLength="37.207">eate an </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="25" textLength="90.015">RTCIceCandidate</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="100.965" y="25" textLength="33.794"> object </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="39" textLength="77.134">using the SDP pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="87.884" y="39" textLength="6.04">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="93.723" y="39" textLength="39.268">vided in </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="53" textLength="66.484">the candidate.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="1" y="67" textLength="8.379">2.</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="11" y="67" textLength="113.848">Deliver the candidate to </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="81" textLength="32.671">Naomi'</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="43.323" y="81" textLength="56.172">s ICE layer b</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="99.295" y="81" textLength="7.637">y </tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" x="10.95" y="95" textLength="58.799">passing it to </tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="108" textLength="120.02">RTCPeerConnection.ad</tspan><tspan font-family="Courier" font-size="10" font-weight="500" x="10.95" y="120" textLength="90.015">dIceCandidate()</tspan></text><path fill="#fff5c8" d="M18 567h144v18H18z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M18 567h144v18H18z"/><text transform="translate(23 571)" fill="#000"><tspan font-family="Courier" font-size="8" font-weight="500" x="4.59" y="8" textLength="124.82">handleNewIceCandidateMsg()</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M9 81h180v27H9z"/><text transform="translate(14 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="50.664" y="17" textLength="15.133">W</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="65.477" y="17" textLength="53.859">eb App</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M189 81h180v27H189z"/><text transform="translate(194 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="34.137" y="17" textLength="15.133">W</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="48.949" y="17" textLength="40.805">eb Br</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="89.434" y="17" textLength="9.773">o</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="98.887" y="17" textLength="36.977">wser</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M549 81h180v27H549z"/><text transform="translate(554 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="34.137" y="17" textLength="15.133">W</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="48.949" y="17" textLength="40.805">eb Br</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="89.434" y="17" textLength="9.773">o</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="98.887" y="17" textLength="36.977">wser</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M729 81h180v27H729z"/><text transform="translate(734 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="50.664" y="17" textLength="15.133">W</tspan><tspan font-family="Open Sans" font-size="16" font-weight="bold" x="65.477" y="17" textLength="53.859">eb App</tspan></text><path fill="#da456b" fill-opacity=".503" d="M9 738h900v27H9z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M9 738h900v27H9z"/><text transform="translate(14 740.5)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="500" x="227.586" y="17" textLength="16.164">Pr</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" x="243.43" y="17" textLength="52.211">ocess r</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" x="295.32" y="17" textLength="230.836">epeats until both ICE layers agr</tspan><tspan font-family="Open Sans" font-size="16" font-weight="500" x="525.836" y="17" textLength="136.578">ee on a candidate.</tspan></text></g></svg>
