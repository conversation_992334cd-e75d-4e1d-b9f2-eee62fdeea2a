<svg xmlns="http://www.w3.org/2000/svg" viewBox="17.5 8.5 901 1117" width="901" height="1117"><defs><marker orient="auto" overflow="visible" markerUnits="strokeWidth" id="a" stroke-linejoin="miter" stroke-miterlimit="10" viewBox="-1 -4 10 8" markerWidth="10" markerHeight="8" color="#000"><path d="M8 0L0-3v6z" fill="currentColor" stroke="currentColor"/></marker></defs><g fill="none"><path fill="#887ee3" fill-opacity=".5" d="M18 81.165h360V1125H18z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M18 81.165h360V1125H18z"/><path fill="#71dcba" fill-opacity=".5" d="M558 81.165h360V1125H558z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M558 81.165h360V1125H558z"/><path fill="#cb965c" fill-opacity=".5" d="M378 81.165h180V1125H378z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M378 81.165h180V1125H378z"/><path fill="#71dcba" fill-opacity=".5" d="M558 9h360v72.165H558z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M558 9h360v72.165H558z"/><text transform="translate(563 31.583)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="700" x="109.692" y="21">Priya (Callee)</tspan></text><path fill="#cb965c" fill-opacity=".5" d="M378 9h180v72.165H378z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M378 9h180v72.165H378z"/><text transform="translate(383 31.583)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="700" x="5.161" y="21">Signaling Server</tspan></text><path fill="#887ee3" fill-opacity=".5" d="M18 9h360v72.165H18z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M18 9h360v72.165H18z"/><text transform="translate(23 31.583)" fill="#000"><tspan font-family="Open Sans" font-size="20" font-weight="700" x="103.428" y="21">Naomi (Caller)</tspan></text><path fill="#fff" fill-opacity=".503" d="M558 108h180v1017H558z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M558 108h180v1017H558zM18 81h180v27H18z"/><text transform="translate(23 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="600" x="50.664" y="17">Web App</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M558 81h180v27H558z"/><text transform="translate(563 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="600" x="34.137" y="17">Web Browser</tspan></text><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M738 81h180v27H738z"/><text transform="translate(743 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="600" x="50.664" y="17">Web App</tspan></text><path fill="#d2ffff" d="M36 144h144v180H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 144h144v180H36z"/><text transform="translate(41 149)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="11">1. Create an </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="24">RTCPeerConnection</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="37">2. Call </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="37">getUserMedia()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="37"> to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="51">access the webcam and </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="65">microphone</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="79">3. Promise fulfilled: add the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="93">local stream's tracks by </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="107">calling </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="120">RTCPeerConnection.ad</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="132">dTrack()</tspan></text><path fill="#fff6d2" d="M36 126h144v18H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 126h144v18H36z"/><text transform="translate(41 129)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="400" x="42.996" y="10">invite()</tspan></text><path fill="#fff" fill-opacity=".503" d="M198 108h180v1017H198z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M198 108h180v1017H198zm0-27h180v27H198z"/><text transform="translate(203 86)" fill="#000"><tspan font-family="Open Sans" font-size="16" font-weight="600" x="34.137" y="17">Web Browser</tspan></text><path fill="#d2ffff" d="M36 398.5h144v210H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 398.5h144v210H36z"/><text transform="translate(41 403.5)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="11">1. Create an SDP offer by </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="25">calling </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="38">RTCPeerConnection.cr</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="50">eateOffer()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="63">3. Promise fulfilled: set the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="77">description of Naomi's </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="91">end of the call by calling </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="104">RTCPeerConnection.se</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="116">tLocalDescription()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="129">4. Promise fulfilled: send the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="143">offer through the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="157">signaling server to Priya in </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="171">a message of type </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="184">“video-offer”</tspan></text><path fill="#fff6d2" d="M36 380.5h144v18H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 380.5h144v18H36z"/><text transform="translate(41 385.5)" fill="#000"><tspan font-family="Courier" font-size="7" font-weight="400" x="3.99" y="6">handleNegotiationNeededEvent()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4.0,4.0" d="M180 503.5l206.1.477"/><text transform="rotate(.133 -216390.778 97669.229)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x=".449" y="11">Message: </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11">“video-offer”</tspan></text><path d="M234.773 689.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" fill="#71dcba"/><path d="M234.773 689.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(242.6 663)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.94" y="11">ICE layer starts </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x=".346" y="25">sending candidates </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="27.7" y="39">to Priya</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M108 608.5l119.276 34.352"/><path fill="#d2ffff" d="M756 513h144v496H756z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M756 513h144v496H756z"/><text transform="translate(761 518)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="11">1. Create an </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="24">RTCPeerConnection</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="37">2. Create an </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="50">RTCSessionDescriptio</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="63">n</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="63"> using the received SDP </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="77">offer</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="91">3. Call </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="104">RTCPeerConnection.se</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="117">tRemoteDescription()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="117"> </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="131">to tell WebRTC about </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="145">Naomi's configuration.</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="159">4. Call </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="159">getUserMedia()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="159"> to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="173">access the webcam and </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="187">microphone</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="201">5. Promise fulfilled: add the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="215">local stream's tracks by </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="229">calling </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="242">RTCPeerConnection.ad</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="254">dTrack()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="267">6. Promise fulfilled: call </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="280">RTCPeerConnection.cr</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="293">eateAnswer()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="293"> to create </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="307">an SDP answer to send to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="321">Naomi</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="335">7. Promise fulfilled: </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="349">configure Priya's end of </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="363">the connection by match </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="377">the generated answer by </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="391">calling </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="404">RTCPeerConnection.se</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="416">tLocalDescription()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="429">8. Promise fulfilled: send the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="443">SDP answer through the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="457">signaling server to Naomi </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="471">in a message of type </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="484">“video-answer”</tspan></text><path fill="#fff6d2" d="M756 495h144v18H756z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M756 495h144v18H756z"/><text transform="translate(761 498)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="400" x="3.99" y="10">handleVideoOfferMsg()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4.0,4.0" d="M540 504h206.1"/><text transform="translate(585.5 501.56)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x=".449" y="11">Message: </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11">“video-offer”</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4.0,4.0" d="M756 761l-206.103-4.771"/><text transform="rotate(1.326 -32309.396 25547.84)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x=".449" y="11">Message: </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11">“video-answer”</tspan></text><path d="M594.773 1058.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" fill="#887ee3"/><path d="M594.773 1058.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(602.6 1032)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.94" y="11">ICE layer starts </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x=".346" y="25">sending candidates </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="23.83" y="39">to Naomi</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M756 1009l-37.442 37.038"/><path fill="#d2ffff" d="M36 765h144v188H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 765h144v188H36z"/><text transform="translate(41 770)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="11">1. Create an </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="24">RTCSessionDescriptio</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="37">n</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="37"> using the received SDP </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="51">answer</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="1" y="65">2. Pass the session </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="79">description to </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="92">RTCPeerConnection.se</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" x="10.95" y="105">tRemoteDescription()</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="105"> </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="119">to configure Naomi's </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="133">WebRTC layer to know </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="147">how Priya's end of the </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="10.95" y="161">connection is configured</tspan></text><path fill="#fff6d2" d="M36 747h144v18H36z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M36 747h144v18H36z"/><text transform="translate(41 750)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="400" x=".989" y="10">handleVideoAnswerMsg()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4.0,4.0" d="M396 756H189.9"/><text transform="translate(224 753.56)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x=".199" y="11">Message: </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11">“video-answer</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="11">”</tspan></text><path fill="#d2ffff" d="M396 513h144v66H396z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M396 513h144v66H396z"/><text transform="translate(401 518)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="11">Receive</tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11"> “video-offer” </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="25">message and forward it to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="39">Priya</tspan></text><path fill="#fff6d2" d="M396 495h144v18H396z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M396 495h144v18H396z"/><text transform="translate(401 498)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="400" x="30.994" y="10">on.message()</tspan></text><path fill="#d2ffff" d="M396 765h144v66H396z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M396 765h144v66H396z"/><text transform="translate(401 770)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="11">Receive </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="11">“video-answer”</tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" y="11"> </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="25">message and forward it to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="0" y="39">Naomi</tspan></text><path fill="#fff6d2" d="M396 747h144v18H396z"/><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" d="M396 747h144v18H396z"/><text transform="translate(401 750)" fill="#000"><tspan font-family="Courier" font-size="10" font-weight="400" x="30.994" y="10">on.message()</tspan></text><path marker-end="url(#a)" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1.0,4.0" d="M288 303.202L187.423 391.95"/><text transform="rotate(-41.425 606.24 -55.815)" fill="#000"><tspan font-family="Helvetica Neue" font-size="10" font-weight="400" x=".117" y="10">Event: </tspan> <tspan font-family="Courier" font-size="10" font-weight="400" y="10">negotiationneeded</tspan></text><path d="M234.773 257.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" fill="#71dcba"/><path d="M234.773 257.8c-23.273-5.8-13.992-54.632 23.134-46.3 3.444-16.242 46.617-13.606 46.335 0 27.07-17.402 61.665 17.297 38.46 34.7 27.845 8.436-.35 53.893-23.202 46.3-1.829 12.657-42.68 17.086-46.266 0-23.132 18.247-71.366-9.809-38.46-34.7z" stroke="#000" stroke-linecap="round" stroke-linejoin="round"/><text transform="translate(242.6 231)" fill="#000"><tspan font-family="Open Sans" font-size="10" font-weight="400" x=".517" y="11">Ready to negotiate, </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="2.048" y="25">so ask the caller to </tspan> <tspan font-family="Open Sans" font-size="10" font-weight="400" x="13.503" y="39">start doing so</tspan></text></g></svg>
