Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md

Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > extensions > index.md --- title: "WebSocket: extensions property" short-title: extensions slug: Web/API/WebSocket/extensions page-type: web-api-instance-property browser-compat: api.WebSocket.extensions --- {{APIRef("Web Sockets API")}}{{AvailableInWorkers}} The **`WebSocket.extensions`** read-only property returns the extensions selected by the server. This is currently only the empty string or a list of extensions as negotiated by the connection. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}