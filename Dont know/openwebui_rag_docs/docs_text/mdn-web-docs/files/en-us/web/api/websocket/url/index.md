Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md

Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md Path: mdn-web-docs > files > en-us > web > api > websocket > url > index.md --- title: "WebSocket: url property" short-title: url slug: Web/API/WebSocket/url page-type: web-api-instance-property browser-compat: api.WebSocket.url --- {{APIRef("WebSockets API")}}{{AvailableInWorkers}} The **`WebSocket.url`** read-only property returns the absolute URL of the {{domxref("WebSocket")}} as resolved by the constructor. ## Value A string. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}