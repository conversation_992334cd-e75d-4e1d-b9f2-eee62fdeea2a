Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md

Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > readable > index.md --- title: "WebTransportBidirectionalStream: readable property" short-title: readable slug: Web/API/WebTransportBidirectionalStream/readable page-type: web-api-instance-property browser-compat: api.WebTransportBidirectionalStream.readable --- {{APIRef("WebTransport API")}}{{SecureContext_Header}} {{AvailableInWorkers}} The **`readable`** read-only property of the {{domxref("WebTransportBidirectionalStream")}} interface returns a {{domxref("WebTransportReceiveStream")}} instance that can be used to reliably read incoming data. ## Value A {{domxref("WebTransportReceiveStream")}}. ## Examples See the main {{domxref("WebTransportBidirectionalStream")}} interface page. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using WebTransport](https://developer.chrome.com/docs/capabilities/web-apis/webtransport) - {{domxref("WebSockets API", "WebSockets API", "", "nocode")}} - {{domxref("Streams API", "Streams API", "", "nocode")}} - [WebTransport over HTTP/3](https://datatracker.ietf.org/doc/html/draft-ietf-webtrans-http3/)