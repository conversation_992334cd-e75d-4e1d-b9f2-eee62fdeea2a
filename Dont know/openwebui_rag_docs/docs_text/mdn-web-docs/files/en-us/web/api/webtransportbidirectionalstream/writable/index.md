Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md

Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md Path: mdn-web-docs > files > en-us > web > api > webtransportbidirectionalstream > writable > index.md --- title: "WebTransportBidirectionalStream: writable property" short-title: writable slug: Web/API/WebTransportBidirectionalStream/writable page-type: web-api-instance-property browser-compat: api.WebTransportBidirectionalStream.writable --- {{APIRef("WebTransport API")}}{{SecureContext_Header}} {{AvailableInWorkers}} The **`writable`** read-only property of the {{domxref("WebTransportBidirectionalStream")}} interface returns a {{domxref("WebTransportSendStream")}} instance that can be used to write outgoing data. ## Value A {{domxref("WebTransportSendStream")}}. ## Examples See the main {{domxref("WebTransportBidirectionalStream")}} interface page. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}} ## See also - [Using WebTransport](https://developer.chrome.com/docs/capabilities/web-apis/webtransport) - {{domxref("WebSockets API", "WebSockets API", "", "nocode")}} - {{domxref("Streams API", "Streams API", "", "nocode")}} - [WebTransport over HTTP/3](https://datatracker.ietf.org/doc/html/draft-ietf-webtrans-http3/)