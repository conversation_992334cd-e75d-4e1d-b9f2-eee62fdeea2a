Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md

Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md Path: mdn-web-docs > files > en-us > web > api > window > captureevents > index.md --- title: "Window: captureEvents() method" short-title: captureEvents() slug: Web/API/Window/captureEvents page-type: web-api-instance-method status: - deprecated browser-compat: api.Window.captureEvents --- {{APIRef}} {{Deprecated_Header}} The **`Window.captureEvents()`** method does nothing. > [!NOTE] > This is an method long removed from the specification. It is kept in browsers to prevent code breakage but does nothing. ## Syntax ```js-nolint captureEvents() ``` ### Parameters None. ### Return value None ({{jsxref("undefined")}}). ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}