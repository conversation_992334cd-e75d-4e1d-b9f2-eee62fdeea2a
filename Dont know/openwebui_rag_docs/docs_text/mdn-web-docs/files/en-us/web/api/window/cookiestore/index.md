Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md

Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md Path: mdn-web-docs > files > en-us > web > api > window > cookiestore > index.md --- title: "Window: cookieStore property" short-title: cookieStore slug: Web/API/Window/cookieStore page-type: web-api-instance-property browser-compat: api.Window.cookieStore --- {{securecontext_header}}{{APIRef("Cookie Store API")}} The **`cookieStore`** read-only property of the {{domxref("Window")}} interface returns a reference to the {{domxref("CookieStore")}} object for the current document context. This is an entry point for the [Cookie Store API](/en-US/docs/Web/API/Cookie_Store_API). ## Value A {{domxref("CookieStore")}} object instance. ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}