Path: mdn-web-docs > files > en-us > web > api > window > document > index.md

Path: mdn-web-docs > files > en-us > web > api > window > document > index.md Path: mdn-web-docs > files > en-us > web > api > window > document > index.md Path: mdn-web-docs > files > en-us > web > api > window > document > index.md Path: mdn-web-docs > files > en-us > web > api > window > document > index.md Path: mdn-web-docs > files > en-us > web > api > window > document > index.md --- title: "Window: document property" short-title: document slug: Web/API/Window/document page-type: web-api-instance-property browser-compat: api.Window.document --- {{APIRef}} **`window.document`** returns a reference to the [document](/en-US/docs/Web/API/Document) contained in the window. ## Value A [document](/en-US/docs/Web/API/Document) object. ## Examples ```js console.log(window.document.title); ``` ## Specifications {{Specifications}} ## Browser compatibility {{Compat}}